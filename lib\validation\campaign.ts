import { z } from 'zod';
import { BaseDocumentSchema, ERROR_MESSAGES } from './base';

// Schemas para enums da campanha
export const CampaignStatusSchema = z.enum(['draft', 'active', 'paused', 'completed', 'cancelled']);
export const CampaignPrioritySchema = z.enum(['low', 'medium', 'high', 'urgent']);
export const InfluencerCampaignStatusSchema = z.enum(['invited', 'confirmed', 'declined', 'in_progress', 'completed']);
export const PaymentStatusSchema = z.enum(['pending', 'partial', 'completed', 'cancelled']);

// Schema para deliverables da campanha
export const CampaignDeliverableSchema = z.object({
  id: z.string().min(1, 'ID do deliverable é obrigatório'),
  type: z.enum(['post', 'story', 'reel', 'video', 'article', 'live']),
  platform: z.string().min(1, 'Plataforma é obrigatória'),
  description: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(500, ERROR_MESSAGES.MAX_LENGTH(500)),
  dueDate: z.date(),
  completed: z.boolean(),
  completedAt: z.date().optional(),
  notes: z.string().max(200, ERROR_MESSAGES.MAX_LENGTH(200)).optional()
});

// Schema para influenciador em uma campanha
export const CampaignInfluencerSchema = z.object({
  influencerId: z.string().min(1, 'ID do influenciador é obrigatório'),
  financialId: z.string().optional(),
  negotiatedPrice: z.number()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0))
    .max(1000000, ERROR_MESSAGES.MAX_VALUE(1000000)),
  deliverables: z.array(CampaignDeliverableSchema),
  status: InfluencerCampaignStatusSchema,
  paymentStatus: PaymentStatusSchema,
  invitedAt: z.date(),
  confirmedAt: z.date().optional(),
  completedAt: z.date().optional(),
  notes: z.string().max(500, ERROR_MESSAGES.MAX_LENGTH(500)).optional()
});

// Schema completo para Campaign
export const CampaignSchema = BaseDocumentSchema.extend({
  name: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim(),
  
  description: z.string()
    .max(1000, ERROR_MESSAGES.MAX_LENGTH(1000))
    .optional(),
  
  brandId: z.string().min(1, 'Brand ID é obrigatório'),
  brandName: z.string().optional(), // Cache para performance
  clientName: z.string().max(100, ERROR_MESSAGES.MAX_LENGTH(100)).optional(),
  
  // Datas e orçamento
  startDate: z.date(),
  endDate: z.date(),
  budget: z.number()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0))
    .max(10000000, ERROR_MESSAGES.MAX_VALUE(10000000)),
  currency: z.string().length(3, 'Moeda deve ter 3 caracteres (ex: BRL, USD)'),
  
  // Objetivos e público
  objectives: z.string().max(500, ERROR_MESSAGES.MAX_LENGTH(500)).optional(),
  targetAudience: z.string().max(500, ERROR_MESSAGES.MAX_LENGTH(500)).optional(),
  kpis: z.array(z.string()).optional(),
  
  // Status e metadados
  status: CampaignStatusSchema,
  priority: CampaignPrioritySchema,
  tags: z.array(z.string()).max(20, 'Máximo 20 tags por campanha'),
  notes: z.string().max(1000, ERROR_MESSAGES.MAX_LENGTH(1000)).optional(),
  
  // Progresso
  progress: z.number().min(0).max(100),
  
  // Influenciadores na campanha
  influencers: z.array(CampaignInfluencerSchema),
  
  // Aprovações
  requiresApproval: z.boolean(),
  approvedBy: z.string().optional(),
  approvedAt: z.date().optional()
});

// Schema base para criação de campanha (sem refinements)
const CreateCampaignBaseSchema = z.object({
  name: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim(),
  
  description: z.string()
    .max(1000, ERROR_MESSAGES.MAX_LENGTH(1000))
    .optional(),
  
  brandId: z.string().min(1, 'Brand ID é obrigatório'),
  clientName: z.string().max(100, ERROR_MESSAGES.MAX_LENGTH(100)).optional(),
  
  startDate: z.date(),
  endDate: z.date(),
  budget: z.number()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0))
    .max(10000000, ERROR_MESSAGES.MAX_VALUE(10000000)),
  currency: z.string().length(3, 'Moeda deve ter 3 caracteres').optional(),
  
  objectives: z.string().max(500, ERROR_MESSAGES.MAX_LENGTH(500)).optional(),
  targetAudience: z.string().max(500, ERROR_MESSAGES.MAX_LENGTH(500)).optional(),
  kpis: z.array(z.string()).optional(),
  
  status: CampaignStatusSchema.optional(),
  priority: CampaignPrioritySchema.optional(),
  tags: z.array(z.string()).max(20, 'Máximo 20 tags por campanha').optional(),
  notes: z.string().max(1000, ERROR_MESSAGES.MAX_LENGTH(1000)).optional(),
  
  influencers: z.array(CampaignInfluencerSchema.omit({ invitedAt: true })).optional(),
  requiresApproval: z.boolean().optional()
});

// Schema para criação de nova campanha (com validação de datas)
export const CreateCampaignSchema = CreateCampaignBaseSchema.refine(
  data => data.endDate > data.startDate, 
  {
    message: 'Data de fim deve ser posterior à data de início',
    path: ['endDate']
  }
);

// Schema para atualização de campanha
export const UpdateCampaignSchema = CreateCampaignBaseSchema.partial();

// Schema para filtros de campanha
export const CampaignFiltersSchema = z.object({
  userId: z.string().min(1).optional(),
  brandId: z.string().optional(),
  search: z.string().optional(),
  status: z.array(CampaignStatusSchema).optional(),
  priority: z.array(CampaignPrioritySchema).optional(),
  startDateFrom: z.date().optional(),
  startDateTo: z.date().optional(),
  endDateFrom: z.date().optional(),
  endDateTo: z.date().optional(),
  budgetMin: z.number().min(0).optional(),
  budgetMax: z.number().min(0).optional(),
  tags: z.array(z.string()).optional(),
  hasInfluencers: z.boolean().optional(),
  requiresApproval: z.boolean().optional(),
  createdAtFrom: z.date().optional(),
  createdAtTo: z.date().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
  sortBy: z.enum(['name', 'startDate', 'endDate', 'budget', 'createdAt', 'priority']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// Schema para validação de relacionamento campaign-brand
export const CampaignBrandOwnershipSchema = z.object({
  campaignId: z.string().min(1, 'Campaign ID é obrigatório'),
  brandId: z.string().min(1, 'Brand ID é obrigatório'),
  userId: z.string().min(1, 'User ID é obrigatório')
});

// Tipos TypeScript derivados dos schemas
export type CampaignValidation = z.infer<typeof CampaignSchema>;
export type CreateCampaignInput = z.infer<typeof CreateCampaignSchema>;
export type UpdateCampaignInput = z.infer<typeof UpdateCampaignSchema>;
export type CampaignFiltersInput = z.infer<typeof CampaignFiltersSchema>;
export type CampaignInfluencerValidation = z.infer<typeof CampaignInfluencerSchema>;
export type CampaignDeliverableValidation = z.infer<typeof CampaignDeliverableSchema>;

// Funções de validação customizadas
export function validateCampaignDates(startDate: Date, endDate: Date): boolean {
  return endDate > startDate;
}

export function validateBrandOwnership(
  campaignData: any, 
  brandData: any, 
  userId: string
): boolean {
  if (!campaignData?.brandId || !brandData?.id || !brandData?.userId) {
    return false;
  }
  
  return campaignData.brandId === brandData.id && brandData.userId === userId;
}

export function validateCampaignInfluencerOwnership(
  influencerIds: string[],
  userInfluencers: string[]
): boolean {
  return influencerIds.every(id => userInfluencers.includes(id));
}

export function sanitizeCampaignData(data: CreateCampaignInput): CreateCampaignInput {
  const sanitized = { ...data };
  
  // Normalizar nome
  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }
  
  // Definir valores padrão
  if (!sanitized.currency) {
    sanitized.currency = 'BRL';
  }
  
  if (!sanitized.status) {
    sanitized.status = 'draft';
  }
  
  if (!sanitized.priority) {
    sanitized.priority = 'medium';
  }
  
  if (!sanitized.tags) {
    sanitized.tags = [];
  }
  
  if (!sanitized.influencers) {
    sanitized.influencers = [];
  }
  
  if (sanitized.requiresApproval === undefined) {
    sanitized.requiresApproval = false;
  }
  
  return sanitized;
}

export function calculateCampaignProgress(campaign: any): number {
  if (!campaign.influencers || campaign.influencers.length === 0) {
    return 0;
  }
  
  const totalDeliverables = campaign.influencers.reduce(
    (sum: number, inf: any) => sum + (inf.deliverables?.length || 0), 
    0
  );
  
  if (totalDeliverables === 0) {
    return 0;
  }
  
  const completedDeliverables = campaign.influencers.reduce(
    (sum: number, inf: any) => 
      sum + (inf.deliverables?.filter((d: any) => d.completed).length || 0), 
    0
  );
  
  return Math.round((completedDeliverables / totalDeliverables) * 100);
}

export function calculateCampaignBudgetUtilization(campaign: any): number {
  if (!campaign.influencers || campaign.influencers.length === 0 || !campaign.budget) {
    return 0;
  }
  
  const totalNegotiated = campaign.influencers.reduce(
    (sum: number, inf: any) => sum + (inf.negotiatedPrice || 0),
    0
  );
  
  return Math.round((totalNegotiated / campaign.budget) * 100);
}

export function validateCampaignBudget(
  campaignBudget: number,
  influencerPrices: number[]
): { isValid: boolean; totalNegotiated: number; remaining: number } {
  const totalNegotiated = influencerPrices.reduce((sum, price) => sum + price, 0);
  const remaining = campaignBudget - totalNegotiated;
  
  return {
    isValid: remaining >= 0,
    totalNegotiated,
    remaining
  };
}

export function getCampaignStatusColor(status: string): string {
  const colors = {
    draft: '#6B7280',      // Gray
    active: '#10B981',     // Green
    paused: '#F59E0B',     // Yellow
    completed: '#8B5CF6',  // Purple
    cancelled: '#EF4444'   // Red
  };
  
  return colors[status as keyof typeof colors] || colors.draft;
}

export function getCampaignPriorityColor(priority: string): string {
  const colors = {
    low: '#10B981',      // Green
    medium: '#F59E0B',   // Yellow
    high: '#F97316',     // Orange
    urgent: '#EF4444'    // Red
  };
  
  return colors[priority as keyof typeof colors] || colors.medium;
} 

