'use client';

import { OrganizationSelector } from '@/components/organization-selector';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect } from 'react';

export default function SelectOrganizationPage() {
  const router = useRouter();

  const handleOrganizationSelect = (organizationId: string) => {
    // Redirecionar para dashboard após seleção
    router.push('/dashboard');
  };

  // Por enquanto, redirecionar automaticamente para dashboard
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push('/dashboard');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-4xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Sistema de Organização</CardTitle>
          <CardDescription>
            Redirecionando para o dashboard...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OrganizationSelector 
            onSelect={handleOrganizationSelect}
            className="w-full"
          />
        </CardContent>
      </Card>
    </div>
  );
} 

