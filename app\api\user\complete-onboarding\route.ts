import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import { z } from 'zod';
import { 
  mapUserTypeToClerkRole, 
  CLERK_ROLES,
  logClerkSecurityEvent 
} from '@/lib/clerk-fga-config';

const onboardingSchema = z.object({
  userId: z.string(),
  userType: z.enum(['agency', 'influencer', 'manager', 'brand', 'viewer']),
  organizationName: z.string().min(1, 'Nome da organização é obrigatório'),
  role: z.string().optional()
}).refine((data) => {
  // Validação condicional: agência deve ter organizationName
  if (data.userType === 'agency') {
    return data.organizationName && data.organizationName.trim().length > 0;
  }
  return true; // Para outros tipos, organizationName não é obrigatório
}, {
  message: 'Agências devem fornecer o nome da organização',
  path: ['organizationName']
});

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação básica (sem organização ainda)
    const { userId: authUserId } = await auth();
    
    if (!authUserId) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { userId, userType, organizationName } = onboardingSchema.parse(body);

    // Verificar se o usuário autenticado é o mesmo do corpo da requisição
    if (authUserId !== userId) {
      logClerkSecurityEvent('ONBOARDING_USER_MISMATCH', authUserId, undefined, { 
        requestedUserId: userId 
      });
      return NextResponse.json(
        { error: 'Não autorizado para este usuário' },
        { status: 403 }
      );
    }

    console.log('👤 Iniciando onboarding Clerk:', {
      userId,
      userType,
      organizationName,
      timestamp: new Date().toISOString()
    });
    
    // Mapear userType para role do Clerk
    const clerkRole = mapUserTypeToClerkRole(userType);
    
    // Buscar dados do usuário
    const clerk = await clerkClient();
    const user = await clerk.users.getUser(userId);
    const userEmail = user.primaryEmailAddress?.emailAddress || user.emailAddresses?.[0]?.emailAddress;
    
    console.log('👤 Dados do usuário:', {
      id: user.id,
      email: userEmail,
      firstName: user.firstName,
      lastName: user.lastName
    });

    let organization;
    
    if (userType === 'agency') {
      // 1. Criar organização apenas para agências
      console.log('🏢 Criando organização para agência:', organizationName);
      organization = await clerk.organizations.createOrganization({
        name: organizationName,
        createdBy: userId,
      });

      console.log('✅ Organização criada:', {
        id: organization.id,
        name: organization.name,
        slug: organization.slug
      });
    } else {
      // Para managers e influenciadores, não criar organização
      console.log(`👤 ${userType} configurado sem organização própria`);
      organization = null;
    }

    // 2. Atualizar metadata do usuário com informações de onboarding
    await clerk.users.updateUserMetadata(userId, {
      publicMetadata: {
        userType,
        onboardingCompleted: true,
        onboardingDate: new Date().toISOString(),
      },
      privateMetadata: {
        primaryOrganizationId: organization?.id || null
      }
    });

    console.log('✅ Metadata do usuário atualizada');

    // 3. Definir organização como ativa (se possível via API)
    // Nota: Isso normalmente seria feito no cliente, mas vamos tentar via API

    logClerkSecurityEvent('ONBOARDING_COMPLETED', userId, organization?.id, {
      userType,
      clerkRole,
      organizationName
    });

    return NextResponse.json({
      success: true,
      message: 'Onboarding completado com sucesso',
      data: {
      userId,
      userType,
        clerkRole,
        organization: organization ? {
          id: organization.id,
          name: organization.name,
          slug: organization.slug
        } : null
      }
    });
    
  } catch (error) {
    console.error('❌ Erro no onboarding:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Dados inválidos', details: error.errors },
        { status: 400 }
      );
    }
    
    // Log do erro de segurança
    logClerkSecurityEvent('ONBOARDING_ERROR', 'unknown', undefined, {
      error: error instanceof Error ? error.message : error
    });
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 

