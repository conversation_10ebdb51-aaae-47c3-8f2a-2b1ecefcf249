// SCRIPT PARA LIMPAR CACHE DO APOLLO CLIENT
// Forçar consulta fresca dos dados atualizados

const testFreshDataQuery = `
  query GetFreshInfluencersData($userId: ID!) {
    influencers(userId: $userId, filters: {}, pagination: { limit: 5, offset: 0 }) {
      nodes {
        id
        name
        avatar
        
        # Pricing denormalizado (deve estar atualizado agora)
        pricing {
          hasFinancialData
          priceRange
          avgPrice
          isNegotiable
          lastPriceUpdate
        }
        
        # Current Pricing (deve aparecer agora)
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }
        
        # Orçamentos por plataforma
        budgets {
          instagram {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          tiktok {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          youtube {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          facebook {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          twitch {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          kwai {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          personalizado {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
        }
        
        # Demographics
        currentDemographics {
          id
          platform
          audienceGender {
            male
            female
            other
          }
                  audienceLocations {
          country
            percentage
          }
          audienceCities {
            city
            percentage
          }
          audienceAgeRange {
            range
            percentage
          }
          isActive
          source
        }
        
        createdAt
        updatedAt
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

// Executar query com headers específicos para forçar cache fresh
async function testFreshData() {
  try {
    console.log('🔄 [FRESH] Executando query com dados frescos (sem cache)...');
    
    const response = await fetch('http://localhost:3000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forçar bypass do cache
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      body: JSON.stringify({
        query: testFreshDataQuery,
        variables: {
          userId: "qSrQ5VmzuZQjgCP8Qs07eRE4aul1"
        }
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ [FRESH] Erros GraphQL encontrados:', result.errors);
      return;
    }

    console.log('✅ [FRESH] Query executada com sucesso!');
    console.log('📊 [FRESH] Dados retornados:', {
      totalCount: result.data?.influencers?.totalCount,
      nodesCount: result.data?.influencers?.nodes?.length
    });

    // Analisar cada influencer com foco nos dados atualizados
    result.data?.influencers?.nodes?.forEach((influencer, index) => {
      console.log(`\n🆔 [FRESH] === ${influencer.name} (${influencer.id}) ===`);
      
      // 💰 PRICING DENORMALIZADO (deve estar atualizado)
      console.log(`💰 [FRESH] Pricing Denormalizado:`, {
        hasFinancialData: influencer.pricing?.hasFinancialData,
        priceRange: influencer.pricing?.priceRange,
        avgPrice: influencer.pricing?.avgPrice,
        isNegotiable: influencer.pricing?.isNegotiable,
        lastUpdate: influencer.pricing?.lastPriceUpdate
      });
      
      // 💎 CURRENT PRICING (deve aparecer agora)
      if (influencer.currentPricing) {
        console.log(`💎 [FRESH] Current Pricing ENCONTRADO!`);
        console.log(`💎 [FRESH] ID: ${influencer.currentPricing.id}`);
        console.log(`💎 [FRESH] Ativo: ${influencer.currentPricing.isActive}`);
        
        // Listar serviços disponíveis
        const services = influencer.currentPricing.services;
        if (services) {
          Object.entries(services).forEach(([platform, platformServices]) => {
            if (platformServices && Object.keys(platformServices).length > 0) {
              console.log(`  📱 [FRESH] ${platform}:`);
              Object.entries(platformServices).forEach(([serviceType, serviceData]) => {
                if (serviceData && serviceData.price) {
                  console.log(`    💵 [FRESH] ${serviceType}: ${serviceData.currency} ${serviceData.price}`);
                }
              });
            }
          });
        }
      } else {
        console.log(`❌ [FRESH] Current Pricing ainda não encontrado`);
      }
      
      // 🎯 ORÇAMENTOS
      if (influencer.budgets) {
        console.log(`🎯 [FRESH] Orçamentos:`);
        
        let totalBudgetsForInfluencer = 0;
        Object.entries(influencer.budgets).forEach(([platform, budgets]) => {
          if (budgets && Array.isArray(budgets) && budgets.length > 0) {
            totalBudgetsForInfluencer += budgets.length;
            console.log(`  📱 [FRESH] ${platform}: ${budgets.length} orçamento(s)`);
            budgets.forEach((budget, budgetIndex) => {
              console.log(`    💵 [FRESH] ${budget.serviceType}: ${budget.currency} ${budget.amount} (${budget.status})`);
            });
          }
        });
        
        if (totalBudgetsForInfluencer === 0) {
          console.log(`  ⚠️ [FRESH] Nenhum orçamento encontrado`);
        }
      }
      
      // 📊 DEMOGRAPHICS
      console.log(`📊 [FRESH] Demographics: ${influencer.currentDemographics?.length || 0} plataforma(s)`);
      if (influencer.currentDemographics && influencer.currentDemographics.length > 0) {
        influencer.currentDemographics.forEach(demo => {
          console.log(`  📱 [FRESH] ${demo.platform}: ${demo.source} (${demo.isActive ? 'ativo' : 'inativo'})`);
        });
      }
    });

    // 🎯 RESUMO FINAL
    console.log('\n📋 [FRESH] === RESUMO APÓS CORREÇÕES ===');
    
    const influencersWithPricing = result.data?.influencers?.nodes?.filter(inf => inf.pricing?.hasFinancialData) || [];
    const influencersWithCurrentPricing = result.data?.influencers?.nodes?.filter(inf => inf.currentPricing) || [];
    const influencersWithBudgets = result.data?.influencers?.nodes?.filter(inf => {
      if (!inf.budgets) return false;
      return Object.values(inf.budgets).some(budgets => Array.isArray(budgets) && budgets.length > 0);
    }) || [];
    
    console.log(`💰 [FRESH] Influencers com pricing denormalizado: ${influencersWithPricing.length}`);
    console.log(`💎 [FRESH] Influencers com current pricing: ${influencersWithCurrentPricing.length}`);
    console.log(`🎯 [FRESH] Influencers com orçamentos: ${influencersWithBudgets.length}`);
    
    if (influencersWithPricing.length > 0 && influencersWithCurrentPricing.length > 0 && influencersWithBudgets.length > 0) {
      console.log('🎉 [FRESH] PERFEITO! Todos os sistemas estão funcionando corretamente!');
    } else {
      console.log('⚠️ [FRESH] Ainda há inconsistências nos dados. Pode ser necessário mais troubleshooting.');
    }

  } catch (error) {
    console.error('❌ [FRESH] Erro na execução:', error);
  }
}

// Executar teste
testFreshData(); 
