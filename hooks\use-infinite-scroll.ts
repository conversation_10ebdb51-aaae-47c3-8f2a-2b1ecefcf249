"use client"

import { useEffect, useRef, useCallback } from 'react';

interface UseInfiniteScrollOptions {
  hasMore: boolean;
  isLoading: boolean;
  onLoadMore: () => void;
  threshold?: number; // Distância em pixels do final para disparar carregamento
  root?: Element | null; // Elemento container (null = viewport)
}

export function useInfiniteScroll({
  hasMore,
  isLoading,
  onLoadMore,
  threshold = 200,
  root = null
}: UseInfiniteScrollOptions) {
  const loadingRef = useRef<HTMLDivElement>(null);

  const handleObserver = useCallback((entries: IntersectionObserverEntry[]) => {
    const target = entries[0];
    
    if (target.isIntersecting && hasMore && !isLoading) {
      console.log('🔄 [InfiniteScroll] Carregando mais dados...');
      onLoadMore();
    }
  }, [hasMore, isLoading, onLoadMore]);

  useEffect(() => {
    if (!loadingRef.current) return;

    const observer = new IntersectionObserver(handleObserver, {
      root,
      rootMargin: `${threshold}px`,
      threshold: 0.1
    });

    observer.observe(loadingRef.current);

    return () => {
      observer.disconnect();
    };
  }, [handleObserver, threshold, root]);

  return { loadingRef };
}

export default useInfiniteScroll; 

