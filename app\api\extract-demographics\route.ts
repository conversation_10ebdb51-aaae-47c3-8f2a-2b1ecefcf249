import { NextRequest, NextResponse } from 'next/server'
import { Mistral } from '@mistralai/mistralai'

// Interface para dados demográficos extraídos
interface ExtractedDemographics {
  platform: string
  username: string
  followers: number
  following: number
  posts: number
  engagementRate: number
  avgViews: number
  audienceGender: {
    male: number
    female: number
    other: number
  }
  audienceAgeRange: Array<{
    range: string
    percentage: number
  }>
  audienceLocations: Array<{
    country: string
    percentage: number
  }>
  audienceCities: Array<{
    city: string
    state?: string
    percentage: number
  }>
  additionalMetrics?: {
    storiesViews?: number
    reelsViews?: number
    shortsViews?: number
    longFormViews?: number
    videoViews?: number
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verificar se a API key está configurada
    const apiKey = process.env.MISTRAL_API_KEY
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Chave API do Mistral AI não configurada' },
        { status: 500 }
      )
    }

    // Obter dados da requisição
    const { base64Image } = await request.json()
    
    if (!base64Image) {
      return NextResponse.json(
        { error: 'Imagem em base64 é obrigatória' },
        { status: 400 }
      )
    }

    console.log('🧠 [API] Iniciando extração de dados demográficos com Mistral AI...')

    // Inicializar cliente Mistral
    const client = new Mistral({ apiKey })

    // Prompt reformulado para análise real da imagem
    const prompt = `ANALISE CUIDADOSAMENTE esta imagem de rede social e extraia APENAS os dados que você conseguir VER na captura de tela.

IMPORTANTE: NÃO use dados de exemplo! Extraia apenas o que está REALMENTE visível na imagem.

Retorne no formato JSON com esta estrutura EXATA:

{
  "platform": "[plataforma detectada: instagram/tiktok/youtube/etc]",
  "username": "[username/handle que você conseguir ler na imagem ou deixe vazio se não visível]", 
  "followers": [número de seguidores que você conseguir ler ou 0],
  "following": [número de seguindo que você conseguir ler ou 0],
  "posts": [número de posts que você conseguir ler ou 0],
  "engagementRate": [taxa de engajamento se visível ou 0],
  "avgViews": [média de visualizações se visível ou 0],
  "audienceGender": {
    "male": [porcentagem masculina que você conseguir ler ou 0],
    "female": [porcentagem feminina que você conseguir ler ou 0], 
    "other": [porcentagem outros que você conseguir ler ou 0]
  },
  "audienceAgeRange": [
    {"range": "[faixa etária visível]", "percentage": [porcentagem visível]}
  ],
  "audienceLocations": [
    {"country": "[país visível na imagem]", "percentage": [porcentagem visível]}
  ],
  "audienceCities": [
    {"city": "[cidade visível na imagem]", "percentage": [porcentagem visível]}
  ]
}

REGRAS CRÍTICAS:
- Analise SOMENTE o que está VISÍVEL na imagem fornecida
- Converta K=1000, M=1000000, B=1000000000 se necessário
- Use apenas dados que você conseguir LER na imagem
- Se um campo não estiver visível, use 0 ou array vazio
- Para faixas etárias use EXATAMENTE: "13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"
- Se você vir "17-18" na imagem, distribua entre "13-17" e "18-24" proporcionalmente
- Se você vir outras faixas não padrão, normalize para as faixas corretas
- Para países/cidades use os nomes EXATOS que aparecem na imagem

RETORNE APENAS O JSON, sem explicações.`

    // Fazer a requisição para o Mistral AI
    const chatResponse = await client.chat.complete({
      model: 'pixtral-12b-2409',
      messages: [
        {
          role: "system",
          content: "Você é um especialista em análise de dados de redes sociais. Extraia dados demográficos e métricas de screenshots com precisão máxima. Sempre retorne um JSON válido e estruturado."
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            {
              type: "image_url",
              imageUrl: `data:image/jpeg;base64,${base64Image}`
            }
          ]
        }
      ],
      responseFormat: {
        type: "json_object"
      },
      maxTokens: 2000
    })

    const responseContent = chatResponse.choices[0]?.message?.content
    if (!responseContent) {
      throw new Error('Resposta vazia do Mistral AI')
    }

    console.log('🧠 [API] Resposta recebida do Mistral AI:', responseContent)

    // Parse do JSON retornado (já no formato padronizado)
    const extractedData = JSON.parse(String(responseContent))
    
    console.log('✅ [API] Dados extraídos do Mistral AI:', extractedData)
    
    // Validar se tem a estrutura mínima esperada
    const validatedData = validateAndCleanData(extractedData)
    
    console.log('🔄 [API] Dados validados enviados:', validatedData)
    
    return NextResponse.json(validatedData)

  } catch (error) {
    console.error('❌ [API] Erro na extração:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido na análise'
    
    return NextResponse.json(
      { error: `Erro na extração de dados: ${errorMessage}` },
      { status: 500 }
    )
  }
}

/**
 * Normaliza faixas etárias para o padrão correto
 */
function normalizeAgeRanges(ageRanges: any[]): Array<{ range: string; percentage: number }> {
  const standardRanges = ["13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"];
  const normalized: { [key: string]: number } = {};
  
  // Inicializar com zero
  standardRanges.forEach(range => {
    normalized[range] = 0;
  });
  
  // Processar cada faixa extraída
  ageRanges.forEach((item: any) => {
    if (!item.range || !item.percentage || item.percentage <= 0) return;
    
    const range = item.range.toString().trim();
    const percentage = Number(item.percentage);
    
    // Se for uma faixa padrão, usar diretamente
    if (standardRanges.includes(range)) {
      normalized[range] += percentage;
      return;
    }
    
    // Normalizar faixas não padrão
    switch (range) {
      case "17-18":
        // Dividir igualmente entre 13-17 e 18-24
        normalized["13-17"] += percentage / 2;
        normalized["18-24"] += percentage / 2;
        break;
      case "16-17":
        normalized["13-17"] += percentage;
        break;
      case "18-19":
      case "19-20":
      case "20-21":
      case "21-22":
      case "22-23":
      case "23-24":
        normalized["18-24"] += percentage;
        break;
      case "24-25":
      case "25-26":
      case "26-30":
      case "30-34":
        normalized["25-34"] += percentage;
        break;
      case "35-40":
      case "40-44":
        normalized["35-44"] += percentage;
        break;
      case "45-50":
      case "50-54":
        normalized["45-54"] += percentage;
        break;
      case "55-60":
      case "60-64":
        normalized["55-64"] += percentage;
        break;
      case "65+":
      case "65-70":
      case "70+":
        normalized["65+"] += percentage;
        break;
      default:
        console.log(`⚠️ Faixa etária não reconhecida: ${range}, ignorando...`);
    }
  });
  
  // Converter de volta para array, filtrando valores zero
  return Object.entries(normalized)
    .filter(([_, percentage]) => percentage > 0)
    .map(([range, percentage]) => ({ range, percentage }));
}

/**
 * Valida e limpa os dados extraídos pelo Mistral AI (que já vem no formato padronizado)
 */
function validateAndCleanData(data: any): ExtractedDemographics {
  console.log('🔄 [API] Validando dados padronizados:', data)

  // Garantir valores padrão para campos obrigatórios
  const validatedData: ExtractedDemographics = {
    platform: data.platform || 'instagram',
    username: data.username || '',
    followers: Number(data.followers) || 0,
    following: Number(data.following) || 0,
    posts: Number(data.posts) || 0,
    engagementRate: Number(data.engagementRate) || 0,
    avgViews: Number(data.avgViews) || 0,
    audienceGender: {
      male: Number(data.audienceGender?.male) || 0,
      female: Number(data.audienceGender?.female) || 0,
      other: Number(data.audienceGender?.other) || 0
    },
    audienceAgeRange: Array.isArray(data.audienceAgeRange) 
      ? normalizeAgeRanges(data.audienceAgeRange)
      : [],
    audienceLocations: Array.isArray(data.audienceLocations) ? data.audienceLocations.filter((item: any) => 
      item.country && item.percentage > 0
    ) : [],
    audienceCities: Array.isArray(data.audienceCities) ? data.audienceCities.filter((item: any) => 
      item.city && item.percentage > 0
    ) : []
  }

  console.log('✅ [API] Dados validados:', validatedData)
  
  return validatedData
}

// Funções de normalização removidas - não necessárias com prompt padronizado do Mistral AI 

