import React, { useState, useCallback, forwardRef } from 'react'
import { Input } from '@/components/ui/input'
import { formatNumber } from '@/lib/utils'

export interface FormattedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value?: string | number
  onChange?: (value: string, numericValue: number) => void
  formatType?: 'currency' | 'number' | 'percentage'
  maxValue?: number
  decimalPlaces?: number
}

export const FormattedInput = forwardRef<HTMLInputElement, FormattedInputProps>(
  ({ value, onChange, formatType = 'number', maxValue, decimalPlaces = 2, className, ...props }, ref) => {
    
    // Função para formatar moeda
    const formatCurrency = useCallback((numValue: number): string => {
      return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(numValue)
    }, [])

    const [displayValue, setDisplayValue] = useState(() => {
      if (!value && value !== 0) return ''
      
      const numValue = typeof value === 'string' ? parseFloat(value) || 0 : value
      
      switch (formatType) {
        case 'currency':
          // Para valores externos (como carregamento de dados), usar o valor diretamente
          return formatCurrency(numValue)
        case 'number':
          return formatNumber(numValue)
        case 'percentage':
          return numValue.toString()
        default:
          return numValue.toString()
      }
    })

    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      let numericValue = 0
      let formattedValue = ''

      switch (formatType) {
        case 'currency':
          // Extrair apenas números do input
          const currencyDigits = inputValue.replace(/\D/g, '')
          
          // Se estiver vazio, permitir
          if (currencyDigits === '') {
            formattedValue = ''
            numericValue = 0
          } else {
            // Limitar número de dígitos (máximo 8 dígitos + 2 centavos = 10)
            if (currencyDigits.length <= 10) {
              // Tratar os últimos 2 dígitos como centavos
              const cents = parseInt(currencyDigits) || 0
              numericValue = cents / 100 // Converter centavos para reais
              formattedValue = formatCurrency(numericValue)
            } else {
              return // Não permitir mais de 10 dígitos
            }
          }
          break

        case 'number':
          // Extrair apenas números do input
          const numberDigits = inputValue.replace(/\D/g, '')
          
          if (numberDigits === '') {
            formattedValue = ''
            numericValue = 0
          } else {
            if (numberDigits.length <= 10) {
              numericValue = parseInt(numberDigits) || 0
              formattedValue = formatNumber(numericValue)
            } else {
              return // Não permitir mais de 10 dígitos
            }
          }
          break

        case 'percentage':
          // Para percentuais, permitir decimais
          const percentMatch = inputValue.match(/^(\d*\.?\d*)$/)
          if (percentMatch) {
            const percentValue = parseFloat(inputValue) || 0
            if (percentValue <= 100) {
              formattedValue = inputValue
              numericValue = percentValue
            } else {
              return // Não permitir valores acima de 100
            }
          } else if (inputValue === '') {
            formattedValue = ''
            numericValue = 0
          } else {
            return // Não permitir caracteres inválidos
          }
          break

        default:
          formattedValue = inputValue
          numericValue = parseFloat(inputValue) || 0
      }

      // Verificar valor máximo se especificado
      if (maxValue && numericValue > maxValue) {
        return
      }

      setDisplayValue(formattedValue)
      onChange?.(formattedValue, numericValue)
    }, [formatType, maxValue, onChange])

    // Atualizar display value quando value prop mudar externamente
    React.useEffect(() => {
      if (value !== undefined) {
        if (!value && value !== 0) {
          setDisplayValue('')
          return
        }
        
        const numValue = typeof value === 'string' ? parseFloat(value) || 0 : value
        
        switch (formatType) {
          case 'currency':
            setDisplayValue(formatCurrency(numValue))
            break
          case 'number':
            setDisplayValue(formatNumber(numValue))
            break
          case 'percentage':
            setDisplayValue(numValue.toString())
            break
          default:
            setDisplayValue(numValue.toString())
        }
      }
    }, [value, formatType, formatCurrency])

    return (
      <Input
        {...props}
        ref={ref}
        value={displayValue}
        onChange={handleInputChange}
        className={className}
      />
    )
  }
)

FormattedInput.displayName = 'FormattedInput' 

