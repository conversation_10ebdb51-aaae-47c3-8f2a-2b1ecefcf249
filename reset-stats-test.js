const fetch = require('node-fetch');

async function resetUserStats() {
  try {
    console.log('🔄 Iniciando reset das estatísticas...');
    
    const response = await fetch('http://localhost:3000/api/admin/reset-stats', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: "qSrQ5VmzuZQjgCP8Qs07eRE4aul1",
        action: "reset"
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Reset realizado com sucesso!');
      console.log('📊 Novas estatísticas:', {
        totalInfluencers: result.stats.totalInfluencers,
        totalViews: result.stats.totalViews,
        totalFollowers: result.stats.totalFollowers,
        totalBrands: result.stats.totalBrands
      });
    } else {
      console.error('❌ Erro no reset:', result.error);
    }
  } catch (error) {
    console.error('❌ Erro de conexão:', error.message);
  }
}

resetUserStats(); 
