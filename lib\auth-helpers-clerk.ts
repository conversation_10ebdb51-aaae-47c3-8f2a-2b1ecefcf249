/**
 * 🛡️ HELPERS DE AUTENTICAÇÃO E AUTORIZAÇÃO - CLERK FGA
 * 
 * ⚠️ MIGRAÇÃO: Substituindo WorkOS FGA por Clerk como sistema completo de FGA
 * Sistema baseado em organizações, roles e permissões granulares
 */

import { auth } from '@clerk/nextjs/server';
import { currentUser } from '@clerk/nextjs/server';
import { NextRequest } from 'next/server';
import { 
  CLERK_ROLES, 
  CLERK_PERMISSIONS, 
  ROLE_PERMISSIONS_MAP,
  isValidClerkRole,
  logClerkSecurityEvent,
  type ClerkRole,
  type ClerkPermission 
} from './clerk-fga-config';

// ========================
// TIPOS DE RESPOSTA DE AUTENTICAÇÃO
// ========================
export interface ClerkAuthResult {
  userId: string;
  organizationId?: string;
  role?: ClerkRole;
  permissions: ClerkPermission[];
  hasPermission: (permission: ClerkPermission) => boolean;
  hasRole: (role: ClerkRole) => boolean;
  isAdmin: boolean;
}

export interface ClerkAuthError {
  error: string;
  code: 'UNAUTHORIZED' | 'NO_ORGANIZATION' | 'INVALID_ROLE' | 'MISSING_PERMISSION';
  details?: any;
}

// ========================
// AUTENTICAÇÃO PRINCIPAL
// ========================

/**
 * 🔐 Verificar autenticação e autorização usando Clerk
 * Substitui completamente o sistema WorkOS FGA
 */
export async function requireClerkAuth(req?: NextRequest): Promise<ClerkAuthResult | null> {
  try {
    const { userId, orgId, orgRole, has } = await auth();
    
    if (!userId) {
      logClerkSecurityEvent('AUTH_FAILED', 'unknown', undefined, { reason: 'no_user_id' });
      return null;
    }

    // Verificar se tem organização ativa
    if (!orgId) {
      logClerkSecurityEvent('AUTH_NO_ORG', userId, undefined, { reason: 'no_active_organization' });
      return null;
    }

    // Verificar se role é válido
    const userRole = orgRole as ClerkRole;
    if (!userRole || !isValidClerkRole(userRole)) {
      logClerkSecurityEvent('AUTH_INVALID_ROLE', userId, orgId, { role: userRole });
      return null;
    }

    // Obter permissões do role
    const rolePermissions = ROLE_PERMISSIONS_MAP[userRole] || [];
    
    logClerkSecurityEvent('AUTH_SUCCESS', userId, orgId, { 
      role: userRole, 
      permissionsCount: rolePermissions.length 
    });

    return {
      userId,
      organizationId: orgId,
      role: userRole,
      permissions: rolePermissions as ClerkPermission[],
      hasPermission: (permission: ClerkPermission) => has({ permission }),
      hasRole: (role: ClerkRole) => has({ role }),
      isAdmin: userRole === CLERK_ROLES.ADMIN
    };

  } catch (error) {
    console.error('❌ Erro na autenticação Clerk:', error);
    logClerkSecurityEvent('AUTH_ERROR', 'unknown', undefined, { error: error instanceof Error ? error.message : error });
    return null;
  }
}

/**
 * 🔒 Verificar se usuário tem permissão específica
 */
export async function hasClerkPermission(permission: ClerkPermission): Promise<boolean> {
  try {
    const { has } = await auth();
    return has({ permission });
  } catch (error) {
    console.error('❌ Erro ao verificar permissão:', error);
    return false;
  }
}

/**
 * 🎭 Verificar se usuário tem role específico
 */
export async function hasClerkRole(role: ClerkRole): Promise<boolean> {
  try {
    const { has } = await auth();
    return has({ role });
  } catch (error) {
    console.error('❌ Erro ao verificar role:', error);
    return false;
  }
}

/**
 * 🛡️ Middleware para proteger rotas com permissão específica
 */
export function withClerkPermission(permission: ClerkPermission) {
  return async (req: NextRequest) => {
    const authResult = await requireClerkAuth(req);
    
    if (!authResult) {
      return new Response(
        JSON.stringify({ error: 'Não autenticado' }), 
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!authResult.hasPermission(permission)) {
      logClerkSecurityEvent('PERMISSION_DENIED', authResult.userId, authResult.organizationId, { 
        permission, 
        userRole: authResult.role 
      });
      
      return new Response(
        JSON.stringify({ error: `Sem permissão: ${permission}` }), 
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    return null; // Autorizado, continuar
  };
}

/**
 * 🎯 Middleware para proteger rotas com role específico
 */
export function withClerkRole(role: ClerkRole) {
  return async (req: NextRequest) => {
    const authResult = await requireClerkAuth(req);
    
    if (!authResult) {
      return new Response(
        JSON.stringify({ error: 'Não autenticado' }), 
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!authResult.hasRole(role)) {
      logClerkSecurityEvent('ROLE_DENIED', authResult.userId, authResult.organizationId, { 
        requiredRole: role, 
        userRole: authResult.role 
      });
      
      return new Response(
        JSON.stringify({ error: `Acesso negado. Role necessário: ${role}` }), 
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    return null; // Autorizado, continuar
  };
}

// ========================
// HELPERS PARA COMPONENTES
// ========================

/**
 * 🔐 Hook-like helper para verificar autenticação em Server Components
 */
export async function useClerkAuth(): Promise<ClerkAuthResult | null> {
  return await requireClerkAuth();
}

/**
 * 🛡️ Proteger Server Actions com Clerk
 */
export async function protectServerAction(
  requiredPermission?: ClerkPermission,
  requiredRole?: ClerkRole
): Promise<ClerkAuthResult | null> {
  const authResult = await requireClerkAuth();
  
  if (!authResult) {
    throw new Error('Não autenticado');
  }

  if (requiredPermission && !authResult.hasPermission(requiredPermission)) {
    logClerkSecurityEvent('SERVER_ACTION_DENIED', authResult.userId, authResult.organizationId, { 
      permission: requiredPermission 
    });
    throw new Error(`Sem permissão: ${requiredPermission}`);
  }

  if (requiredRole && !authResult.hasRole(requiredRole)) {
    logClerkSecurityEvent('SERVER_ACTION_DENIED', authResult.userId, authResult.organizationId, { 
      role: requiredRole 
    });
    throw new Error(`Role necessário: ${requiredRole}`);
  }

  return authResult;
}

// ========================
// HELPERS DE ORGANIZAÇÃO
// ========================

/**
 * 📋 Obter dados da organização atual
 */
export async function getCurrentOrganization() {
  try {
    const { orgId, orgSlug } = await auth();
    
    if (!orgId) {
      return null;
    }

    return {
      id: orgId,
      slug: orgSlug || undefined
    };
  } catch (error) {
    console.error('❌ Erro ao obter organização:', error);
    return null;
  }
}

/**
 * 👤 Obter dados completos do usuário autenticado
 */
export async function getCurrentClerkUser() {
  try {
    const user = await currentUser();
    const { orgId, orgRole } = await auth();
    
    if (!user) {
      return null;
    }

    return {
      id: user.id,
      email: user.primaryEmailAddress?.emailAddress || user.emailAddresses?.[0]?.emailAddress,
      firstName: user.firstName,
      lastName: user.lastName,
      imageUrl: user.imageUrl,
      organizationId: orgId || undefined,
      organizationRole: orgRole as ClerkRole || undefined,
      metadata: {
        public: user.publicMetadata,
        private: user.privateMetadata
      }
    };
  } catch (error) {
    console.error('❌ Erro ao obter usuário:', error);
    return null;
  }
}

// ========================
// HELPERS DE CONVENIÊNCIA
// ========================

/**
 * ⚡ Verificação rápida se é admin
 */
export async function isClerkAdmin(): Promise<boolean> {
  return await hasClerkRole(CLERK_ROLES.ADMIN);
}

/**
 * ⚡ Verificação rápida se pode gerenciar usuários
 */
export async function canManageUsers(): Promise<boolean> {
  return await hasClerkPermission(CLERK_PERMISSIONS.USERS_MANAGE);
}

/**
 * ⚡ Verificação rápida se pode ver financeiro
 */
export async function canViewFinancials(): Promise<boolean> {
  return await hasClerkPermission(CLERK_PERMISSIONS.FINANCIALS_VIEW);
}

/**
 * ⚡ Verificação rápida se pode criar campanhas
 */
export async function canCreateCampaigns(): Promise<boolean> {
  return await hasClerkPermission(CLERK_PERMISSIONS.CAMPAIGNS_CREATE);
}

// ========================
// EXPORT DOS TIPOS
// ========================
export type { ClerkRole, ClerkPermission } from './clerk-fga-config'; 

