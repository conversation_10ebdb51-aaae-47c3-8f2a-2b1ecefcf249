'use client'

// 🔥 COMPONENTE LAZY PARA GRÁFICOS PESADOS (Recharts)
// Carregado apenas quando necessário para reduzir bundle inicial

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  ResponsiveContainer, 
  PieChart, 
  Pie, 
  Cell, 
  LineChart, 
  Line, 
  LabelList, 
  AreaChart, 
  Area 
} from "recharts";
import AgeRangeBarChart from "@/components/age-range-bar-chart";
import { LocationBarChart } from "@/components/ui/location-bar-chart";
import { AudienceInterests } from "@/components/ui/audience-interests";

// 🔥 EXPORTAR TODOS OS COMPONENTES DE GRÁFICOS
export {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  LabelList,
  AreaChart,
  Area,
  AgeRangeBarChart,
  LocationBarChart,
  AudienceInterests
};

// 🔥 COMPONENTE WRAPPER PARA LAZY LOADING
export default function ChartsLazy() {
  return null; // Este componente é apenas para exportar os charts
}
