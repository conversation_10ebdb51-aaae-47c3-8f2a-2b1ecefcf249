/**
 * API Route atualizada para influenciadores com nova estrutura organizada
 * Esta versão substitui a route.ts atual com melhor organização dos dados
 */

import { NextResponse } from 'next/server';
import { 
  addStructuredInfluencer, 
  updateStructuredInfluencer,
  convertFormDataToStructured,
  createFinancialData,
  type InfluencerStructured 
} from '@/scripts/recreate-influencer-structure';
import { 
  db,
  influencersCollection,
  financialsCollection,
  getCategoryById 
} from '@/lib/firebase';
import { getFinancialByInfluencerId } from '@/lib/firebase-financials';
import { collection, doc, getDoc, getDocs, deleteDoc, query, where } from 'firebase/firestore';

// ===== FUNÇÕES DE UTILIDADE =====

/**
 * Busca dados financeiros de um influenciador
 */
async function getInfluencerFinancials(influencerId: string) {
  try {
    const q = query(
      collection(db, 'influencer_financials'),
      where('influencerId', '==', influencerId)
    );
    const snapshot = await getDocs(q);
    
    if (!snapshot.empty) {
      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      };
    }
    return null;
  } catch (error) {
    console.error('Erro ao buscar dados financeiros:', error);
    return null;
  }
}

/**
 * Converte dados estruturados para formato de compatibilidade com frontend antigo
 */
function convertToLegacyFormat(influencer: InfluencerStructured, financials?: any): any {
  const legacy: any = {
    id: influencer.id,
    
    // Informações pessoais (formato antigo)
    name: influencer.personalInfo.name,
    age: influencer.personalInfo.age,
    gender: influencer.personalInfo.gender,
    bio: influencer.personalInfo.bio,
    avatarUrl: influencer.personalInfo.avatarUrl,
    backgroundImage: influencer.personalInfo.backgroundImageUrl,
    verified: influencer.personalInfo.verified,
    
    // Localização (formato antigo)
    city: influencer.personalInfo.location.city,
    state: influencer.personalInfo.location.state,
    country: influencer.personalInfo.location.country,
    location: influencer.personalInfo.location.formattedLocation,
    cep: influencer.personalInfo.location.cep,
    
    // Contato (formato antigo)
    email: influencer.contactInfo.email,
    whatsapp: influencer.contactInfo.whatsapp,
    
    // Negócios (formato antigo)
    agencyName: influencer.businessInfo.agency.name,
    agency_name: influencer.businessInfo.agency.name,
    responsible_name: influencer.businessInfo.agency.responsibleName,
    responsible_capturer: influencer.businessInfo.agency.responsibleCapturer,
    mainCategories: influencer.businessInfo.mainCategories,
    category: influencer.businessInfo.mainCategories[0] || '',
    promotes_traders: influencer.businessInfo.promotesTraders,
    content_type: influencer.businessInfo.contentType.join(','),
    
    // Métricas (formato antigo)
    totalFollowers: influencer.metrics.totalFollowers.toString(),
    engagementRate: influencer.metrics.overallEngagementRate,
    rating: influencer.metrics.rating,
    
    // Redes sociais (formato antigo)
    socialNetworks: influencer.socialNetworks,
    
    // Histórico de marcas
    brandHistory: influencer.businessInfo.brandHistory,
    
    // Dados de audiência
    audienceGender: {},
    audienceAgeRanges: {},
    audienceLocations: {},
    
    // Timestamps
    createdAt: influencer.createdAt,
    updatedAt: influencer.updatedAt,
    
    // Tags e notas
    tags: influencer.tags,
    notes: influencer.notes
  };
  
  // Processar redes sociais para formato antigo
  Object.entries(influencer.socialNetworks).forEach(([platform, data]) => {
    if (data) {
      // Campos principais
      legacy[`${platform}_username`] = data.username;
      legacy[platform] = data.followers;
      legacy[`${platform}_followers`] = data.followers;
      legacy[`${platform}_views`] = data.avgViews;
      legacy[`${platform}_avg_views`] = data.avgViews;
      legacy[`${platform}_engagement_rate`] = data.engagementRate;
      
      // Dados de audiência
      legacy.audienceGender[platform] = data.audienceGender;
      legacy.audienceAgeRanges[platform] = data.audienceAgeRanges;
      legacy.audienceLocations[platform] = data.audienceLocations;
      
      // Campos específicos por plataforma
      if (platform === 'instagram') {
        legacy.stories_views = data.storiesViews;
        legacy.story_price = data.storyPrice;
        legacy.reels_price = data.reelPrice;
      } else if (platform === 'tiktok') {
        legacy.tiktok_price = data.price;
      } else if (platform === 'youtube') {
        legacy.video_dedicated_price = data.dedicatedPrice;
        legacy.video_insertion_price = data.insertionPrice;
        legacy.shorts_price = data.shortsPrice;
      }
    }
  });
  
  // Adicionar dados financeiros se disponíveis
  if (financials) {
    legacy.financials = financials;
    legacy.financialData = financials;
  }
  
  return legacy;
}

// ===== ROTAS DA API =====

/**
 * GET - Buscar influenciadores
 */
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  try {
    if (id) {
      // Buscar um influenciador específico
      const docRef = doc(db, 'influencers', id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return NextResponse.json(
          { error: 'Influenciador não encontrado' },
          { status: 404 }
        );
      }
      
      const influencerData = {
        id: docSnap.id,
        ...docSnap.data(),
        createdAt: docSnap.data()?.createdAt?.toDate(),
        updatedAt: docSnap.data()?.updatedAt?.toDate()
      } as InfluencerStructured;
      
      // Buscar dados financeiros
      const financials = await getInfluencerFinancials(id);
      
      // Buscar dados completos das categorias
      if (influencerData.businessInfo?.mainCategories) {
        const categoriesData = await Promise.all(
          influencerData.businessInfo.mainCategories.map(async (categoryId) => {
            try {
              const category = await getCategoryById(categoryId);
              return category || { 
                id: categoryId, 
                name: categoryId,
                slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
              };
            } catch (error) {
              return { 
                id: categoryId, 
                name: categoryId,
                slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
              };
            }
          })
        );
        
        // Adicionar dados das categorias ao resultado
        (influencerData as any).mainCategoriesData = categoriesData;
      }
      
      // Converter para formato de compatibilidade
      const legacyFormat = convertToLegacyFormat(influencerData, financials);
      
      return NextResponse.json(legacyFormat);
      
    } else {
      // Buscar todos os influenciadores
      const snapshot = await getDocs(collection(db, 'influencers'));
      const influencers: InfluencerStructured[] = [];
      
      snapshot.forEach((doc) => {
        if (!doc.data()._example) { // Ignorar documentos de exemplo
          influencers.push({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate(),
            updatedAt: doc.data().updatedAt?.toDate()
          } as InfluencerStructured);
        }
      });
      
      // Processar categorias para todos os influenciadores
      const influencersWithCategories = await Promise.all(
        influencers.map(async (influencer) => {
          if (influencer.businessInfo?.mainCategories) {
            const categoriesData = await Promise.all(
              influencer.businessInfo.mainCategories.map(async (categoryId) => {
                try {
                  const category = await getCategoryById(categoryId);
                  return category || { 
                    id: categoryId, 
                    name: categoryId,
                    slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                  };
                } catch (error) {
                  return { 
                    id: categoryId, 
                    name: categoryId,
                    slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                  };
                }
              })
            );
            
            // Converter para formato de compatibilidade
            const legacyFormat = convertToLegacyFormat(influencer);
            legacyFormat.mainCategoriesData = categoriesData;
            return legacyFormat;
          }
          
          return convertToLegacyFormat(influencer);
        })
      );
      
      return NextResponse.json(influencersWithCategories);
    }
  } catch (error: any) {
    console.error('Erro ao buscar influenciadores:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar dados de influenciadores' },
      { status: 500 }
    );
  }
}

/**
 * POST - Criar novo influenciador
 */
export async function POST(request: Request) {
  try {
    const formData = await request.json();
    
    // Validação básica
    if (!formData.name || !formData.city || !formData.state) {
      return NextResponse.json(
        { error: 'Dados inválidos. Nome, cidade e estado são obrigatórios.' },
        { status: 400 }
      );
    }
    
    console.log('📝 Criando novo influenciador com dados:', {
      name: formData.name,
      city: formData.city,
      state: formData.state,
      email: formData.email,
      instagram: formData.instagram_username
    });
    
    // Usar a função estruturada para criar o influenciador
    const influencerId = await addStructuredInfluencer(formData);
    
    // Buscar o influenciador criado para retornar
    const docRef = doc(db, 'influencers', influencerId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const influencerData = {
        id: docSnap.id,
        ...docSnap.data(),
        createdAt: docSnap.data()?.createdAt?.toDate(),
        updatedAt: docSnap.data()?.updatedAt?.toDate()
      } as InfluencerStructured;
      
      // Buscar dados financeiros
      const financials = await getInfluencerFinancials(influencerId);
      
      // Converter para formato de compatibilidade
      const legacyFormat = convertToLegacyFormat(influencerData, financials);
      
      return NextResponse.json(legacyFormat, { status: 201 });
    }
    
    return NextResponse.json(
      { error: 'Erro ao recuperar influenciador criado' },
      { status: 500 }
    );
    
  } catch (error: any) {
    console.error('❌ Erro ao criar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao adicionar influenciador: ' + error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT - Atualizar influenciador existente
 */
export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador não fornecido' },
        { status: 400 }
      );
    }
    
    const formData = await request.json();
    
    console.log('📝 Atualizando influenciador:', id, {
      name: formData.name,
      email: formData.email,
      instagram: formData.instagram_username
    });
    
    // Usar a função estruturada para atualizar
    await updateStructuredInfluencer(id, formData);
    
    // Buscar o influenciador atualizado
    const docRef = doc(db, 'influencers', id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const influencerData = {
        id: docSnap.id,
        ...docSnap.data(),
        createdAt: docSnap.data()?.createdAt?.toDate(),
        updatedAt: docSnap.data()?.updatedAt?.toDate()
      } as InfluencerStructured;
      
      // Buscar dados financeiros
      const financials = await getInfluencerFinancials(id);
      
      // Converter para formato de compatibilidade
      const legacyFormat = convertToLegacyFormat(influencerData, financials);
      
      return NextResponse.json(legacyFormat);
    }
    
    return NextResponse.json(
      { error: 'Influenciador não encontrado após atualização' },
      { status: 404 }
    );
    
  } catch (error: any) {
    console.error('❌ Erro ao atualizar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar influenciador: ' + error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE - Remover influenciador
 */
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador não fornecido' },
        { status: 400 }
      );
    }
    
    console.log('🗑️ Removendo influenciador:', id);
    
    // Remover influenciador
    await deleteDoc(doc(db, 'influencers', id));
    
    // Remover dados financeiros associados
    const q = query(
      collection(db, 'influencer_financials'),
      where('influencerId', '==', id)
    );
    const snapshot = await getDocs(q);
    
    const deletePromises = snapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);
    
    console.log('✅ Influenciador e dados financeiros removidos:', id);
    
    return NextResponse.json(
      { message: 'Influenciador removido com sucesso' },
      { status: 200 }
    );
    
  } catch (error: any) {
    console.error('❌ Erro ao excluir influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir influenciador: ' + error.message },
      { status: 500 }
    );
  }
}

