import { NextRequest, NextResponse } from 'next/server'
import { getBrandInfluencersByBrandId } from '@/services/brand-influencer-service'
// Firebase Auth removido em favor do Clerk
// import { adminAuth } from '@/lib/firebase-admin'

interface Params {
  id: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<Params> }
): Promise<NextResponse> {
  try {
    const { id: brandId } = await params
    console.log('🔍 [API] Buscando influenciadores da marca:', brandId)

    // Verificar autenticação
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { error: 'Token de autenticação necessário' },
        { status: 401 }
      )
    }

    // DESABILITADO: Firebase Auth removido em favor do Clerk
    // const decodedToken = await adminAuth.verifyIdToken(token)
    // const userId = decodedToken.uid
    const userId = 'temp-user-id' // TODO: Implementar autenticação com Clerk

    console.log('🔍 [API] Usuário autenticado:', userId)

    // Buscar influenciadores associados à marca
    const influencers = await getBrandInfluencersByBrandId(brandId, userId)
    
    console.log('✅ [API] Influenciadores encontrados:', {
      brandId,
      totalInfluencers: influencers.length,
      influencerIds: influencers.map(inf => inf.influencerId)
    })

    return NextResponse.json({
      success: true,
      data: influencers,
      total: influencers.length
    })

  } catch (error) {
    console.error('❌ [API] Erro ao buscar influenciadores da marca:', error)
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor', 
        details: error instanceof Error ? error.message : 'Erro desconhecido',
        code: 'INTERNAL_ERROR' 
      },
      { status: 500 }
    )
  }
} 