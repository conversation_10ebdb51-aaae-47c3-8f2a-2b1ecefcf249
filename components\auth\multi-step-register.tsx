'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';
import { Eye, EyeOff, Mail, User, Lock, ChevronRight, ChevronLeft, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { BlurText } from '@/components/ui/animated-blur-text';
import { toast } from 'sonner';
import { auth } from '@/lib/firebase-client';
import { fetchSignInMethodsForEmail, sendPasswordResetEmail } from 'firebase/auth';

interface MultiStepRegisterProps {
  onBackToLogin: () => void;
  onComplete: () => void;
}

interface FormData {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
}

type StepType = 'email' | 'name' | 'password';

export function MultiStepRegister({ onBackToLogin, onComplete }: MultiStepRegisterProps) {
  const [currentStep, setCurrentStep] = useState<StepType>('email');
  const [formData, setFormData] = useState<FormData>({
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [emailExists, setEmailExists] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const { register } = useFirebaseAuth();

  // Limpar erros de validação quando o usuário digita
  useEffect(() => {
    setValidationErrors({});
  }, [formData]);

  // Validar email
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setValidationErrors({ email: 'Email é obrigatório' });
      return false;
    }
    if (!emailRegex.test(email)) {
      setValidationErrors({ email: 'Email inválido' });
      return false;
    }
    return true;
  };

  // Validar nomes
  const validateNames = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.firstName.trim()) {
      errors.firstName = 'Nome é obrigatório';
    } else if (formData.firstName.trim().length < 2) {
      errors.firstName = 'Nome deve ter pelo menos 2 caracteres';
    }
    
    if (!formData.lastName.trim()) {
      errors.lastName = 'Sobrenome é obrigatório';
    } else if (formData.lastName.trim().length < 2) {
      errors.lastName = 'Sobrenome deve ter pelo menos 2 caracteres';
    }
    
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return false;
    }
    return true;
  };

  // Validar senha
  const validatePassword = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.password) {
      errors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 6) {
      errors.password = 'Senha deve ter pelo menos 6 caracteres';
    }
    
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Confirmação de senha é obrigatória';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Senhas não coincidem';
    }
    
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return false;
    }
    return true;
  };

  // Verificar se email já existe
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const signInMethods = await fetchSignInMethodsForEmail(auth, email);
      return signInMethods.length > 0;
    } catch (error) {
      console.error('Erro ao verificar email:', error);
      return false;
    }
  };

  // Enviar email de redefinição de senha
  const sendPasswordReset = async (email: string) => {
    try {
      setIsLoading(true);
      await sendPasswordResetEmail(auth, email);
      toast.success('Email de redefinição de senha enviado!');
      onBackToLogin();
    } catch (error: any) {
      console.error('Erro ao enviar email de redefinição:', error);
      toast.error('Erro ao enviar email de redefinição');
    } finally {
      setIsLoading(false);
    }
  };

  // Avançar para próxima etapa
  const handleNextStep = async () => {
    setIsLoading(true);
    
    try {
      if (currentStep === 'email') {
        if (!validateEmail(formData.email)) {
          setIsLoading(false);
          return;
        }
        
        const exists = await checkEmailExists(formData.email);
        if (exists) {
          setEmailExists(true);
          setIsLoading(false);
          return;
        }
        
        setEmailExists(false);
        setCurrentStep('name');
      } else if (currentStep === 'name') {
        if (!validateNames()) {
          setIsLoading(false);
          return;
        }
        setCurrentStep('password');
      } else if (currentStep === 'password') {
        if (!validatePassword()) {
          setIsLoading(false);
          return;
        }
        
        // Completar registro
        await handleRegister();
      }
    } catch (error) {
      console.error('Erro na etapa:', error);
      toast.error('Erro ao processar');
    } finally {
      setIsLoading(false);
    }
  };

  // Voltar para etapa anterior
  const handlePreviousStep = () => {
    if (currentStep === 'name') {
      setCurrentStep('email');
    } else if (currentStep === 'password') {
      setCurrentStep('name');
    }
    setEmailExists(false);
  };

  // Registrar usuário
  const handleRegister = async () => {
    try {
      console.log('🚀 Iniciando registro do usuário...');
      const fullName = `${formData.firstName.trim()} ${formData.lastName.trim()}`;
      
      await register(formData.email, formData.password, fullName, 'user');
      
      console.log('✅ Registro concluído com sucesso');
      toast.success('Conta criada com sucesso!');
      onComplete();
    } catch (error: any) {
      console.error('❌ Erro no registro:', error);
      
      let errorMessage = 'Erro ao criar conta';
      
      // Tratar diferentes tipos de erro
      if (error?.code === 'auth/email-already-in-use' || error?.message?.includes('email já está em uso')) {
        errorMessage = 'Este email já está em uso';
        setEmailExists(true);
        setCurrentStep('email'); // Voltar para a etapa de email
      } else if (error?.code === 'auth/weak-password') {
        errorMessage = 'Senha muito fraca (mínimo 6 caracteres)';
      } else if (error?.code === 'auth/invalid-email') {
        errorMessage = 'Email inválido';
      } else if (error?.code === 'auth/operation-not-allowed') {
        errorMessage = 'Registro não permitido';
      } else if (error?.code === 'auth/too-many-requests') {
        errorMessage = 'Muitas tentativas. Tente novamente mais tarde.';
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      console.error('🚨 Erro tratado:', errorMessage);
      toast.error(errorMessage);
      
      // Se for erro de email em uso, não relançar o erro
      if (!error?.message?.includes('email já está em uso')) {
        throw error;
      }
    }
  };

  // Obter título da etapa atual
  const getStepTitle = () => {
    switch (currentStep) {
      case 'email':
        return 'Qual é o seu email?';
      case 'name':
        return 'Como você se chama?';
      case 'password':
        return 'Crie sua senha';
      default:
        return 'Criar Conta';
    }
  };

  // Obter subtítulo da etapa atual
  const getStepSubtitle = () => {
    switch (currentStep) {
      case 'email':
        return 'Vamos começar com o seu endereço de email';
      case 'name':
        return 'Precisamos do seu nome para personalizar sua experiência';
      case 'password':
        return 'Escolha uma senha segura para proteger sua conta';
      default:
        return '';
    }
  };

  // Renderizar etapa atual
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'email':
        return (
          <motion.div
            key="email"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Label htmlFor="email" className="text-black flex items-center gap-2 dark:text-muted-foreground">
                <Mail className="w-4 h-4" />
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                className="bg-white/5 border-white/20 text-white placeholder:text-gray-500 focus:border-blue-400 focus:ring-blue-400/20 backdrop-blur-sm dark:text-muted-foreground"
              />
              {validationErrors.email && (
                <p className="text-red-500 text-sm flex items-center gap-1">
                  <AlertCircle className="w-4 h-4" />
                  {validationErrors.email}
                </p>
              )}
            </div>

            {emailExists && (
              <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800 dark:text-orange-200">
                  Este email já possui uma conta. 
                  <Button
                    variant="link"
                    className="p-0 h-auto text-orange-600 hover:text-orange-800"
                    onClick={onBackToLogin}
                  >
                    Fazer login
                  </Button>
                  {' ou '}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-orange-600 hover:text-orange-800"
                    onClick={() => sendPasswordReset(formData.email)}
                    disabled={isLoading}
                  >
                    esqueceu a senha?
                  </Button>
                </AlertDescription>
              </Alert>
            )}
          </motion.div>
        );

      case 'name':
        return (
          <motion.div
            key="name"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-black flex items-center gap-2 dark:text-muted-foreground">
                  <User className="w-4 h-4" />
                  Nome
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                  placeholder="Seu nome"
                  className="bg-white/5 border-white/20 text-white placeholder:text-gray-500 focus:border-blue-400 focus:ring-blue-400/20 backdrop-blur-sm dark:text-muted-foreground"
                />
                {validationErrors.firstName && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {validationErrors.firstName}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-black flex items-center gap-2 dark:text-muted-foreground">
                  <User className="w-4 h-4" />
                  Sobrenome
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                  placeholder="Seu sobrenome"
                  className="bg-white/5 border-white/20 text-white placeholder:text-gray-500 focus:border-blue-400 focus:ring-blue-400/20 backdrop-blur-sm dark:text-muted-foreground"
                />
                {validationErrors.lastName && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {validationErrors.lastName}
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        );

      case 'password':
        return (
          <motion.div
            key="password"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-black flex items-center gap-2 dark:text-muted-foreground">
                  <Lock className="w-4 h-4" />
                  Senha
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    placeholder="••••••••"
                    className="bg-white/5 border-white/20 text-white placeholder:text-gray-500 focus:border-blue-400 focus:ring-blue-400/20 backdrop-blur-sm pr-10 dark:text-muted-foreground"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                {validationErrors.password && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {validationErrors.password}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-black flex items-center gap-2 dark:text-muted-foreground">
                  <Lock className="w-4 h-4" />
                  Confirmar Senha
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    placeholder="••••••••"
                    className="bg-white/5 border-white/20 text-white placeholder:text-gray-500 focus:border-blue-400 focus:ring-blue-400/20 backdrop-blur-sm pr-10 dark:text-muted-foreground"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
                {validationErrors.confirmPassword && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {validationErrors.confirmPassword}
                  </p>
                )}
              </div>
            </div>

            {/* Indicadores de força da senha */}
            {formData.password && (
              <div className="space-y-2">
                <div className="text-sm text-gray-600 dark:text-gray-400">Força da senha:</div>
                <div className="flex gap-1">
                  {[1, 2, 3, 4].map((level) => (
                    <div
                      key={level}
                      className={`h-2 flex-1 rounded ${
                        formData.password.length >= level * 2
                          ? level <= 2 ? 'bg-red-400' : level === 3 ? 'bg-yellow-400' : 'bg-green-400'
                          : 'bg-gray-200 dark:bg-gray-700'
                      }`}
                    />
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        );
    }
  };

  const getStepNumber = () => {
    switch (currentStep) {
      case 'email': return 1;
      case 'name': return 2;
      case 'password': return 3;
      default: return 1;
    }
  };

  return (
    <div className="relative">
      {/* Gradient blur externo */}
      <div className="absolute -inset-0 rounded-3xl opacity-60 blur-[10rem] bg-gradient-to-r from-[#ff0074] via-blue-500 via-pink-500 to-[#9810fa] animate-pulse -z-20"></div>
      
      {/* Backdrop blur layer */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-2xl rounded-xl -z-10"></div>

      {/* Modal content */}
      <div className="relative bg-black/80 backdrop-blur-xl rounded-xl p-8 shadow-2xl overflow-hidden">
        {/* Efeito de borda animado */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-[#ff0074] via-[#9810fa] via-[#ff0074] to-[#9810fa] p-[1px] animate-blur-border">
          <div className="bg-white/80 backdrop-blur-xl rounded-xl h-full w-full dark:bg-black/80"></div>
        </div>

        {/* Conteúdo */}
        <div className="relative z-10">
          {/* Cabeçalho com título e indicador de progresso */}
          <div className="text-center mb-8">
            <BlurText
              text={getStepTitle()}
              delay={150}
              animateBy="words"
              direction="top"
              className="text-3xl justify-center font-bold text-black dark:text-white"
              animationFrom={{
                filter: "blur(15px)",
                opacity: 0,
                y: -30,
                scale: 0.8
              }}
              animationTo={[
                {
                  filter: "blur(8px)",
                  opacity: 0.3,
                  y: -10,
                  scale: 0.9
                },
                {
                  filter: "blur(3px)",
                  opacity: 0.7,
                  y: 0,
                  scale: 1
                },
                {
                  filter: "blur(0px)",
                  opacity: 1,
                  y: 0,
                  scale: 1
                }
              ]}
              stepDuration={0.4}
            />
            <BlurText
              text={getStepSubtitle()}
              delay={100}
              animateBy="words"
              direction="bottom"
              className="text-black text-center dark:text-white text-sm opacity-80 items-center justify-center"
              animationFrom={{
                filter: "blur(10px)",
                opacity: 0,
                y: 20
              }}
              animationTo={[
                {
                  filter: "blur(5px)",
                  opacity: 0.4,
                  y: 10
                },
                {
                  filter: "blur(0px)",
                  opacity: 0.8,
                  y: 0
                }
              ]}
              stepDuration={0.3}
            />
            
            {/* Progress indicator */}
            <div className="flex justify-center gap-2 mt-6">
              {[1, 2, 3].map((step) => (
                <div
                  key={step}
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all ${
                    step < getStepNumber()
                      ? 'bg-green-500 text-white'
                      : step === getStepNumber()
                      ? 'bg-[#ff0074] text-white'
                      : 'bg-gray-200 text-gray-500 dark:bg-gray-700'
                  }`}
                >
                  {step < getStepNumber() ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    step
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Conteúdo da etapa atual */}
          <div className="space-y-6">
            <AnimatePresence mode="wait">
              {renderCurrentStep()}
            </AnimatePresence>

            {/* Navigation buttons */}
            <div className="flex gap-3">
              {currentStep !== 'email' && (
                <Button
                  variant="outline"
                  onClick={handlePreviousStep}
                  className="flex-1"
                  disabled={isLoading}
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Voltar
                </Button>
              )}
              
              <Button
                onClick={currentStep === 'email' && emailExists ? onBackToLogin : handleNextStep}
                disabled={isLoading}
                className={`${currentStep === 'email' ? 'w-full' : 'flex-1'} bg-gradient-to-r from-[#ff0074] to-[#9810fa] hover:from-[#9810fa] hover:to-[#ff0074] text-white font-medium py-3 transition-all duration-200 transform hover:scale-[1.02] shadow-lg`}
              >
                {isLoading ? (
                  'Processando...'
                ) : currentStep === 'email' && emailExists ? (
                  'Fazer Login'
                ) : currentStep === 'password' ? (
                  'Criar Conta'
                ) : (
                  <>
                    Continuar
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>

            {/* Back to login link */}
            <div className="text-center">
              <p className="text-gray-400 text-sm">
                Já tem uma conta?{' '}
                <button
                  type="button"
                  onClick={onBackToLogin}
                  className="text-[#ff0074] hover:text-[#9810fa] font-medium transition-colors"
                >
                  Fazer login
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 


