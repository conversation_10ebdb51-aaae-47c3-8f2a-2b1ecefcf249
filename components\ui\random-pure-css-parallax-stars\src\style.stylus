random(min, max)
  return floor(math(0, 'random')*(max - min + 1) + min)

body
  width 100vw
  height 100vh
  margin 0
  overflow hidden
  background-image radial-gradient(center, ellipse cover, rgba(39,54,79,1) 0%,rgba(17,17,34,1) 50%,rgba(17,17,34,1) 100%)
  background -moz-radial-gradient(center, ellipse cover, rgba(39,54,79,1) 0%, rgba(17,17,34,1) 50%, rgba(17,17,34,1) 100%)
  background -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,rgba(39,54,79,1)), color-stop(50%,rgba(17,17,34,1)), color-stop(100%,rgba(17,17,34,1)))
  background -webkit-radial-gradient(center, ellipse cover, rgba(39,54,79,1) 0%,rgba(17,17,34,1) 50%,rgba(17,17,34,1) 100%)
  background -o-radial-gradient(center, ellipse cover, rgba(39,54,79,1) 0%,rgba(17,17,34,1) 50%,rgba(17,17,34,1) 100%)
  background -ms-radial-gradient(center, ellipse cover, rgba(39,54,79,1) 0%,rgba(17,17,34,1) 50%,rgba(17,17,34,1) 100%)
  filter 'progid:DXImageTransform.Microsoft.gradient( startColorstr='#27364f', endColorstr='#111122',GradientType=1 )'
  background-position 50% 0%
  background-size 150vmax 150vmax
  background-repeat no-repeat
  background-color #112

.stars
  .container
    position absolute
    animation stars linear infinite
    .star
      animation twinkle linear infinite
      border-radius 100%
      transform translateZ(0)
  for n in (0..100)
    .container:nth-child({n})
      width random(1, 3px)
      height @width
      left (random(0, 200) / 2vw)
      animation-delay (random(1, 100) / 5s / @width - 1000s)
      animation-duration (random(200, 2000) / 5s / @width)
      .star
        width inherit
        height inherit
        animation-delay (random(1, 100) / 10s - 50s)
        animation-duration (random(50, 500) / 10s)
        background rgba(random(200, 255), random(150, 255), random(100, 255), (random(7, 10) / 10))



@keyframes stars
  0%
    transform translateY(110vh) translateZ(0)
  100%
    transform translateY(-10vh) translateZ(0)

@keyframes twinkle
  0%, 80%, 100%
    opacity .7
    box-shadow 0 0 0 white, 0 0 0 white
  95%
    opacity 1
    box-shadow 0 0 2px white, 0 0 4px white
