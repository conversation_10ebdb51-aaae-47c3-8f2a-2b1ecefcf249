/**
 * 🔒 SERVIÇO UNIFICADO DE CATEGORIAS
 * Camada centralizada de lógica de negócio para categorias
 * Compartilhada entre GraphQL e REST APIs
 * 
 * SEGURANÇA: Todas as operações exigem userId e aplicam isolamento de dados
 */

import { db } from '@/lib/firebase-admin';
import { 
  getAllCategories as firebaseGetAllCategories, 
  getCategoryById as firebaseGetCategoryById, 
  addCategory as firebaseAddCategory, 
  updateCategory as firebaseUpdateCategory, 
  deleteCategory as firebaseDeleteCategory 
} from '@/lib/firebase';

// ===== INTERFACES =====

export interface Category {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  color?: string;
  count?: number | null;
  userId: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCategoryInput {
  name: string;
  slug?: string;
  description?: string;
  color?: string;
  isActive?: boolean;
}

export interface UpdateCategoryInput {
  name?: string;
  slug?: string;
  description?: string;
  color?: string;
  isActive?: boolean;
}

export interface CategoryFilters {
  isActive?: boolean;
  includePublic?: boolean;
  search?: string;
}

export interface CategoryServiceOptions {
  includeCount?: boolean;
  orderBy?: 'name' | 'createdAt' | 'updatedAt';
  orderDirection?: 'asc' | 'desc';
  limit?: number;
}

// ===== CLASSE DE SERVIÇO UNIFICADA =====

export class CategoryService {
  
  /**
   * 🔒 BUSCAR CATEGORIAS COM ISOLAMENTO DE SEGURANÇA
   * Retorna categorias públicas + categorias do usuário específico
   */
  static async getCategories(
    userId: string, 
    filters: CategoryFilters = {}, 
    options: CategoryServiceOptions = {}
  ): Promise<Category[]> {
    try {
      console.log(`🔒 [SECURITY] CategoryService.getCategories para userId: ${userId}`);
      
      // 🚨 VALIDAÇÃO DE SEGURANÇA CRÍTICA
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.warn('⚠️ [SECURITY] Tentativa de buscar categorias sem userId válido');
        throw new Error('UserId é obrigatório para operações de categoria');
      }

      const categories: Category[] = [];
      const { includePublic = true, isActive = true, search } = filters;
      const { orderBy = 'name', orderDirection = 'asc', limit } = options;

      // Adicionar categoria "Todos" como primeira opção (apenas para listagens)
      if (!search) {
        categories.push({
          id: 'all',
          name: 'Todos',
          slug: 'all',
          description: 'Todos os influenciadores',
          count: null,
          userId: null,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      // 🔒 BUSCAR CATEGORIAS PÚBLICAS (se includePublic = true)
      if (includePublic) {
        console.log(`🔍 [SECURITY] Buscando categorias públicas`);
        
        let publicQuery = db.collection('categories')
          .where('isActive', '==', isActive)
          .where('userId', 'in', [null, '', 'system']);

        if (orderBy && orderBy !== 'count') {
          publicQuery = publicQuery.orderBy(orderBy, orderDirection);
        }

        const publicSnapshot = await publicQuery.get();
        
        publicSnapshot.docs.forEach(doc => {
          const data = doc.data();
          if (!search || data.name?.toLowerCase().includes(search.toLowerCase())) {
            categories.push({
              id: doc.id,
              name: data.name || 'Categoria sem nome',
              slug: data.slug || this.generateSlug(data.name || ''),
              description: data.description || null,
              color: data.color || null,
              count: data.count || null,
              userId: data.userId || null,
              isActive: data.isActive !== false,
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date()
            });
          }
        });

        console.log(`✅ [SECURITY] ${publicSnapshot.docs.length} categorias públicas processadas`);
      }

      // 🔒 BUSCAR CATEGORIAS DO USUÁRIO ESPECÍFICO
      console.log(`🔍 [SECURITY] Buscando categorias do usuário: ${userId}`);
      
      let userQuery = db.collection('categories')
        .where('isActive', '==', isActive)
        .where('userId', '==', userId);

      if (orderBy && orderBy !== 'count') {
        userQuery = userQuery.orderBy(orderBy, orderDirection);
      }

      const userSnapshot = await userQuery.get();
      
      userSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (!search || data.name?.toLowerCase().includes(search.toLowerCase())) {
          categories.push({
            id: doc.id,
            name: data.name || 'Categoria sem nome',
            slug: data.slug || this.generateSlug(data.name || ''),
            description: data.description || null,
            color: data.color || null,
            count: data.count || null,
            userId: data.userId,
            isActive: data.isActive !== false,
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date()
          });
        }
      });

      console.log(`✅ [SECURITY] ${userSnapshot.docs.length} categorias do usuário processadas`);

      // Aplicar ordenação e limite se necessário
      let finalCategories = categories;
      
      if (orderBy === 'count') {
        finalCategories.sort((a, b) => {
          const aCount = a.count || 0;
          const bCount = b.count || 0;
          return orderDirection === 'asc' ? aCount - bCount : bCount - aCount;
        });
      }

      if (limit && limit > 0) {
        finalCategories = finalCategories.slice(0, limit);
      }

      console.log(`🔒 [SECURITY] Retornando ${finalCategories.length} categorias com isolamento seguro`);
      return finalCategories;

    } catch (error) {
      console.error('❌ [SECURITY] Erro em CategoryService.getCategories:', error);
      throw new Error('Erro ao buscar categorias');
    }
  }

  /**
   * 🔒 BUSCAR CATEGORIA POR ID COM VALIDAÇÃO DE ACESSO
   */
  static async getCategoryById(categoryId: string, userId: string): Promise<Category | null> {
    try {
      console.log(`🔒 [SECURITY] CategoryService.getCategoryById ${categoryId} para userId: ${userId}`);
      
      // 🚨 VALIDAÇÃO DE SEGURANÇA CRÍTICA
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.warn('⚠️ [SECURITY] Tentativa de buscar categoria sem userId válido');
        throw new Error('UserId é obrigatório para buscar categoria');
      }

      if (!categoryId || categoryId === 'undefined' || categoryId === 'null') {
        console.warn('⚠️ [SECURITY] CategoryId inválido fornecido');
        return null;
      }

      // Categoria especial "all"
      if (categoryId === 'all') {
        return {
          id: 'all',
          name: 'Todos',
          slug: 'all',
          description: 'Todos os influenciadores',
          count: null,
          userId: null,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }

      const doc = await db.collection('categories').doc(categoryId).get();
      
      if (!doc.exists) {
        console.log(`📭 [SECURITY] Categoria ${categoryId} não encontrada`);
        return null;
      }

      const data = doc.data();
      if (!data) {
        console.log(`📭 [SECURITY] Categoria ${categoryId} sem dados`);
        return null;
      }

      // 🔒 VALIDAÇÃO DE ACESSO: Verificar se é pública ou pertence ao usuário
      const isPublic = !data.userId || data.userId === '' || data.userId === 'system';
      const isUserCategory = data.userId === userId;

      if (!isPublic && !isUserCategory) {
        console.warn(`⚠️ [SECURITY] Tentativa de acesso a categoria de outro usuário: ${categoryId}`);
        return null;
      }

      console.log(`✅ [SECURITY] Categoria ${categoryId} acessada com sucesso`);
      
      return {
        id: doc.id,
        name: data.name || 'Categoria sem nome',
        slug: data.slug || this.generateSlug(data.name || ''),
        description: data.description || null,
        color: data.color || null,
        count: data.count || null,
        userId: data.userId || null,
        isActive: data.isActive !== false,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      };

    } catch (error) {
      console.error('❌ [SECURITY] Erro em CategoryService.getCategoryById:', error);
      throw new Error('Erro ao buscar categoria');
    }
  }

  /**
   * 🔒 CRIAR CATEGORIA COM ISOLAMENTO DE USUÁRIO
   */
  static async createCategory(userId: string, input: CreateCategoryInput): Promise<Category> {
    try {
      console.log(`🔒 [SECURITY] CategoryService.createCategory para userId: ${userId}`);
      
      // 🚨 VALIDAÇÃO DE SEGURANÇA CRÍTICA
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.warn('⚠️ [SECURITY] Tentativa de criar categoria sem userId válido');
        throw new Error('UserId é obrigatório para criar categoria');
      }

      // Validações de entrada
      if (!input.name || input.name.trim().length === 0) {
        throw new Error('Nome da categoria é obrigatório');
      }

      const slug = input.slug || this.generateSlug(input.name);
      
      // Verificar se slug já existe para este usuário
      const existingCategory = await this.getCategoryBySlug(userId, slug);
      if (existingCategory) {
        throw new Error('Já existe uma categoria com este nome/slug');
      }

      // 🔒 FORÇAR userId do usuário autenticado (nunca confiar no input)
      const categoryData = {
        name: input.name.trim(),
        slug: slug,
        description: input.description?.trim() || null,
        color: input.color || null,
        userId: userId, // 🔒 CRÍTICO: Sempre usar o userId validado
        isActive: input.isActive !== false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await firebaseAddCategory(categoryData);
      
      console.log(`✅ [SECURITY] Categoria criada com sucesso: ${result.id} para usuário: ${userId}`);
      
      return {
        id: result.id,
        ...categoryData,
        count: null
      };

    } catch (error) {
      console.error('❌ [SECURITY] Erro em CategoryService.createCategory:', error);
      throw error;
    }
  }



  /**
   * 🔒 ATUALIZAR CATEGORIA COM VALIDAÇÃO DE PROPRIEDADE
   */
  static async updateCategory(categoryId: string, userId: string, input: UpdateCategoryInput): Promise<Category> {
    try {
      console.log(`🔒 [SECURITY] CategoryService.updateCategory ${categoryId} para userId: ${userId}`);

      // 🚨 VALIDAÇÃO DE SEGURANÇA CRÍTICA
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.warn('⚠️ [SECURITY] Tentativa de atualizar categoria sem userId válido');
        throw new Error('UserId é obrigatório para atualizar categoria');
      }

      if (!categoryId || categoryId === 'undefined' || categoryId === 'null') {
        throw new Error('CategoryId é obrigatório para atualizar categoria');
      }

      // 🔒 VERIFICAR SE A CATEGORIA PERTENCE AO USUÁRIO
      const existingCategory = await this.getCategoryById(categoryId, userId);
      if (!existingCategory) {
        console.warn(`⚠️ [SECURITY] Tentativa de atualizar categoria inexistente ou sem acesso: ${categoryId}`);
        throw new Error('Categoria não encontrada ou sem permissão de acesso');
      }

      // Verificar se é categoria do usuário (não pública)
      if (!existingCategory.userId || existingCategory.userId !== userId) {
        console.warn(`⚠️ [SECURITY] Tentativa de atualizar categoria pública ou de outro usuário: ${categoryId}`);
        throw new Error('Não é possível atualizar categorias públicas ou de outros usuários');
      }

      // Preparar dados de atualização
      const updateData: any = {
        updatedAt: new Date()
      };

      if (input.name !== undefined) {
        if (!input.name || input.name.trim().length === 0) {
          throw new Error('Nome da categoria não pode estar vazio');
        }
        updateData.name = input.name.trim();

        // Gerar novo slug se o nome mudou
        if (input.name.trim() !== existingCategory.name) {
          updateData.slug = input.slug || this.generateSlug(input.name);

          // Verificar se o novo slug já existe
          const slugExists = await this.getCategoryBySlug(userId, updateData.slug);
          if (slugExists && slugExists.id !== categoryId) {
            throw new Error('Já existe uma categoria com este nome/slug');
          }
        }
      }

      if (input.slug !== undefined) {
        updateData.slug = this.generateSlug(input.slug);

        // Verificar se o slug já existe
        const slugExists = await this.getCategoryBySlug(userId, updateData.slug);
        if (slugExists && slugExists.id !== categoryId) {
          throw new Error('Já existe uma categoria com este slug');
        }
      }

      if (input.description !== undefined) {
        updateData.description = input.description?.trim() || null;
      }

      if (input.color !== undefined) {
        updateData.color = input.color || null;
      }

      if (input.isActive !== undefined) {
        updateData.isActive = input.isActive;
      }

      // Atualizar no Firebase
      await firebaseUpdateCategory(categoryId, updateData);

      console.log(`✅ [SECURITY] Categoria ${categoryId} atualizada com sucesso para usuário: ${userId}`);

      // Retornar categoria atualizada
      return {
        ...existingCategory,
        ...updateData,
        id: categoryId,
        userId: userId // Garantir que o userId permanece correto
      };

    } catch (error) {
      console.error('❌ [SECURITY] Erro em CategoryService.updateCategory:', error);
      throw error;
    }
  }

  /**
   * 🔒 DELETAR CATEGORIA COM VALIDAÇÃO DE PROPRIEDADE
   */
  static async deleteCategory(categoryId: string, userId: string): Promise<boolean> {
    try {
      console.log(`🔒 [SECURITY] CategoryService.deleteCategory ${categoryId} para userId: ${userId}`);

      // 🚨 VALIDAÇÃO DE SEGURANÇA CRÍTICA
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.warn('⚠️ [SECURITY] Tentativa de deletar categoria sem userId válido');
        throw new Error('UserId é obrigatório para deletar categoria');
      }

      if (!categoryId || categoryId === 'undefined' || categoryId === 'null') {
        throw new Error('CategoryId é obrigatório para deletar categoria');
      }

      // Não permitir deletar categoria especial "all"
      if (categoryId === 'all') {
        throw new Error('Não é possível deletar a categoria "Todos"');
      }

      // 🔒 VERIFICAR SE A CATEGORIA PERTENCE AO USUÁRIO
      const existingCategory = await this.getCategoryById(categoryId, userId);
      if (!existingCategory) {
        console.warn(`⚠️ [SECURITY] Tentativa de deletar categoria inexistente ou sem acesso: ${categoryId}`);
        throw new Error('Categoria não encontrada ou sem permissão de acesso');
      }

      // Verificar se é categoria do usuário (não pública)
      if (!existingCategory.userId || existingCategory.userId !== userId) {
        console.warn(`⚠️ [SECURITY] Tentativa de deletar categoria pública ou de outro usuário: ${categoryId}`);
        throw new Error('Não é possível deletar categorias públicas ou de outros usuários');
      }

      // Deletar no Firebase
      await firebaseDeleteCategory(categoryId);

      console.log(`✅ [SECURITY] Categoria ${categoryId} deletada com sucesso para usuário: ${userId}`);
      return true;

    } catch (error) {
      console.error('❌ [SECURITY] Erro em CategoryService.deleteCategory:', error);
      throw error;
    }
  }

  /**
   * 🔒 BUSCAR CATEGORIA POR SLUG (PRIVADO)
   */
  private static async getCategoryBySlug(userId: string, slug: string): Promise<Category | null> {
    try {
      const snapshot = await db.collection('categories')
        .where('userId', '==', userId)
        .where('slug', '==', slug)
        .limit(1)
        .get();

      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      const data = doc.data();

      return {
        id: doc.id,
        name: data.name || 'Categoria sem nome',
        slug: data.slug || slug,
        description: data.description || null,
        color: data.color || null,
        count: data.count || null,
        userId: data.userId,
        isActive: data.isActive !== false,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      };

    } catch (error) {
      console.error('❌ [SECURITY] Erro em getCategoryBySlug:', error);
      return null;
    }
  }

  /**
   * 🔒 GERAR SLUG SEGURO
   */
  private static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, '-') // Substitui espaços por hífens
      .replace(/-+/g, '-') // Remove hífens duplicados
      .trim();
  }
}
