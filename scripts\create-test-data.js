const admin = require('firebase-admin');
const path = require('path');

// Inicializar Firebase Admin
const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'deumatch-demo'
});

const db = admin.firestore();

// Dados de teste
const testUserId = 'dev-user-123';

const testData = {
  brands: [
    {
      id: 'brand-test-1',
      name: 'Nike Teste',
      industry: 'Esportes',
      logo: 'https://via.placeholder.com/100x100?text=Nike',
      logoBackgroundColor: '#000000',
      status: 'active',
      userId: testUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'brand-test-2',
      name: 'Coca-Cola Teste',
      industry: 'Bebidas',
      logo: 'https://via.placeholder.com/100x100?text=Coca',
      logoBackgroundColor: '#ff0000',
      status: 'active',
      userId: testUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'brand-test-3',
      name: 'Apple Teste',
      industry: 'Tecnologia',
      logo: 'https://via.placeholder.com/100x100?text=Apple',
      logoBackgroundColor: '#ffffff',
      status: 'active',
      userId: testUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  campaigns: [
    {
      id: 'campaign-test-1',
      title: 'Campanha Nike Verão 2025',
      description: 'Campanha de verão focada em tênis esportivos',
      brandId: 'brand-test-1',
      status: 'active',
      startDate: new Date('2025-01-01'),
      endDate: new Date('2025-03-31'),
      budget: 50000,
      userId: testUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'campaign-test-2',
      title: 'Coca-Cola Refresh',
      description: 'Campanha de refrescância para o verão',
      brandId: 'brand-test-2',
      status: 'draft',
      startDate: new Date('2025-02-01'),
      endDate: new Date('2025-04-30'),
      budget: 75000,
      userId: testUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'campaign-test-3',
      title: 'iPhone 16 Launch',
      description: 'Lançamento do novo iPhone',
      brandId: 'brand-test-3',
      status: 'active',
      startDate: new Date('2025-01-15'),
      endDate: new Date('2025-05-15'),
      budget: 100000,
      userId: testUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  users: [
    {
      id: testUserId,
      email: '<EMAIL>',
      name: 'Usuário de Teste',
      role: 'admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: new Date()
    }
  ]
};

async function createTestData() {
  try {
    console.log('🚀 Iniciando criação de dados de teste...');

    // Criar usuário de teste
    console.log('👤 Criando usuário de teste...');
    for (const user of testData.users) {
      await db.collection('users').doc(user.id).set(user);
      console.log(`✅ Usuário criado: ${user.email}`);
    }

    // Criar marcas de teste
    console.log('🏢 Criando marcas de teste...');
    for (const brand of testData.brands) {
      await db.collection('brands').doc(brand.id).set(brand);
      console.log(`✅ Marca criada: ${brand.name}`);
    }

    // Criar campanhas de teste
    console.log('📊 Criando campanhas de teste...');
    for (const campaign of testData.campaigns) {
      await db.collection('campaigns').doc(campaign.id).set(campaign);
      console.log(`✅ Campanha criada: ${campaign.title}`);
    }

    console.log('\n🎉 Dados de teste criados com sucesso!');
    console.log('\n📋 Resumo:');
    console.log(`👤 Usuários: ${testData.users.length}`);
    console.log(`🏢 Marcas: ${testData.brands.length}`);
    console.log(`📊 Campanhas: ${testData.campaigns.length}`);
    console.log(`🆔 User ID de teste: ${testUserId}`);
    
    console.log('\n🧪 Agora você pode testar em:');
    console.log('• http://localhost:3000/test-fase6');
    console.log('• http://localhost:3000/test-isolamento');
    console.log('• http://localhost:3000/admin/brands');

  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error);
  } finally {
    process.exit(0);
  }
}

// Executar criação de dados
createTestData(); 
