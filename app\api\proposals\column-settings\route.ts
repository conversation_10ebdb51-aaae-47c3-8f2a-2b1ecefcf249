import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    const { proposalId, settings, userId } = await request.json();

    if (!proposalId || !settings || !userId) {
      return NextResponse.json(
        { error: 'proposalId, settings e userId são obrigatórios' },
        { status: 400 }
      );
    }

    console.log('💾 [COLUMN-SETTINGS] Salvando configurações:', {
      userId,
      proposalId,
      hasSettings: !!settings
    });

    // Salvar configurações no Firestore (apenas para o usuário autenticado)
    const settingsRef = db
      .collection('users')
      .doc(userId)
      .collection('proposalColumnSettings')
      .doc(proposalId);

    await settingsRef.set({
      ...settings,
      userId, // Garantir que o userId está correto
      updatedAt: new Date()
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Configurações das colunas salvas com sucesso' 
    });

  } catch (error) {
    console.error('Erro ao salvar configurações das colunas:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const proposalId = searchParams.get('proposalId');
    const userId = searchParams.get('userId');

    if (!proposalId || !userId) {
      return NextResponse.json(
        { error: 'proposalId e userId são obrigatórios' },
        { status: 400 }
      );
    }

    console.log('📊 [COLUMN-SETTINGS] Carregando configurações:', {
      userId,
      proposalId
    });

    // Buscar configurações no Firestore (apenas do usuário autenticado)
    const settingsRef = db
      .collection('users')
      .doc(userId)
      .collection('proposalColumnSettings')
      .doc(proposalId);

    const doc = await settingsRef.get();

    if (!doc.exists) {
      return NextResponse.json({ 
        success: true, 
        settings: null,
        message: 'Configurações não encontradas'
      });
    }

    return NextResponse.json({ 
      success: true, 
      settings: doc.data() 
    });

  } catch (error) {
    console.error('Erro ao carregar configurações das colunas:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 

