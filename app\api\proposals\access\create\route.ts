import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';

/**
 * 🔐 API: Criar entrada de acesso à proposta no sistema híbrido
 * 
 * POST /api/proposals/access/create
 * 
 * Cria entrada na collection proposal_access para controle granular
 * Parte do sistema híbrido: Clerk Organizations + Firebase Access Control
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔒 [PROPOSAL_ACCESS] Iniciando criação de acesso');

    // 📝 Extrair dados do request
    const accessData = await request.json();
    
    const {
      userId,
      organizationId,
      proposalId,
      role,
      grantedBy,
      grantedAt
    } = accessData;

    // ✅ Validações básicas
    if (!userId || !organizationId || !proposalId || !role) {
      console.log('❌ [PROPOSAL_ACCESS] Dados obrigatórios ausentes:', { userId, organizationId, proposalId, role });
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios ausentes: userId, organizationId, proposalId, role'
      }, { status: 400 });
    }

    // 🏗️ Definir permissões baseadas no role
    const rolePermissions = {
      proposal_admin: {
        canView: true,
        canEdit: true,
        canInvite: true,
        canDelete: true,
        canViewBudgets: true,
        canEditBudgets: true,
        canManageMembers: true
      },
      proposal_editor: {
        canView: true,
        canEdit: true,
        canInvite: false,
        canDelete: false,
        canViewBudgets: true,
        canEditBudgets: true,
        canManageMembers: false
      },
      proposal_viewer: {
        canView: true,
        canEdit: false,
        canInvite: false,
        canDelete: false,
        canViewBudgets: false,
        canEditBudgets: false,
        canManageMembers: false
      }
    };

    const permissions = rolePermissions[role as keyof typeof rolePermissions];
    
    if (!permissions) {
      console.log('❌ [PROPOSAL_ACCESS] Role inválido:', role);
      return NextResponse.json({
        success: false,
        error: `Role inválido: ${role}. Roles válidos: proposal_admin, proposal_editor, proposal_viewer`
      }, { status: 400 });
    }

    console.log('🎯 [PROPOSAL_ACCESS] Permissões para role', role, ':', permissions);

    // 📊 Verificar se já existe entrada para este usuário
    const existingAccessQuery = await db.collection('proposal_access')
      .where('userId', '==', userId)
      .where('organizationId', '==', organizationId)
      .get();

    if (!existingAccessQuery.empty) {
      // 🔄 Atualizar entrada existente
      const existingDoc = existingAccessQuery.docs[0];
      const existingData = existingDoc.data();
      
      const updatedProposalAccess = {
        ...existingData.proposalAccess,
        [proposalId]: {
          role: role,
          permissions: permissions,
          grantedBy: grantedBy,
          grantedAt: grantedAt || new Date().toISOString(),
          lastUpdated: new Date().toISOString()
        }
      };

      await existingDoc.ref.update({
        proposalAccess: updatedProposalAccess,
        lastSync: new Date().toISOString()
      });

      console.log('✅ [PROPOSAL_ACCESS] Entrada atualizada para usuário existente:', userId);
    } else {
      // 🆕 Criar nova entrada
      const newAccessData = {
        userId: userId,
        organizationId: organizationId,
        proposalAccess: {
          [proposalId]: {
            role: role,
            permissions: permissions,
            grantedBy: grantedBy,
            grantedAt: grantedAt || new Date().toISOString()
          }
        },
        createdAt: new Date().toISOString(),
        lastSync: new Date().toISOString()
      };

      await db.collection('proposal_access').add(newAccessData);
      
      console.log('✅ [PROPOSAL_ACCESS] Nova entrada criada para usuário:', userId);
    }

    // 📤 Retornar sucesso
    return NextResponse.json({
      success: true,
      message: 'Acesso à proposta criado com sucesso',
      data: {
        userId,
        proposalId,
        role,
        permissions
      }
    });

  } catch (error) {
    console.error('❌ [PROPOSAL_ACCESS] Erro ao criar acesso:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
} 