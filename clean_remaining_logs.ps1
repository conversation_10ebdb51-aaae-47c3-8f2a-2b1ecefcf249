# Script para limpar console.log dos arquivos mais importantes
Write-Host "🧹 Limpando console.log dos arquivos restantes..." -ForegroundColor Yellow

$targetFiles = @(
    "hooks/use-screenshots.ts",
    "hooks/use-influencers-graphql.ts", 
    "components/influencer-form/sections/personal-info-section.tsx",
    "components/ui/backdrop-effect.tsx",
    "components/ui/avatar-crop-modal.tsx"
)

$totalRemoved = 0

foreach ($file in $targetFiles) {
    if (Test-Path $file) {
        Write-Host "📁 Processando: $file" -ForegroundColor Cyan
        
        # Backup
        $backupPath = "$file.backup-" + (Get-Date -Format "yyyyMMdd-HHmmss")
        Copy-Item $file $backupPath
        
        # Ler e limpar
        $content = Get-Content $file -Raw
        
        # Padrões para remoção
        $patterns = @(
            'console\.log\([^)]*\);?',
            'console\.error\([^)]*\);?', 
            'console\.warn\([^)]*\);?',
            'console\.info\([^)]*\);?',
            'console\.debug\([^)]*\);?'
        )
        
        $fileRemovedCount = 0
        foreach ($pattern in $patterns) {
            $matches = [regex]::Matches($content, $pattern, [Text.RegularExpressions.RegexOptions]::Multiline)
            $fileRemovedCount += $matches.Count
            $content = [regex]::Replace($content, $pattern, '', [Text.RegularExpressions.RegexOptions]::Multiline)
        }
        
        # Limpar linhas vazias extras
        $content = [regex]::Replace($content, '\n\s*\n\s*\n', "`n`n", [Text.RegularExpressions.RegexOptions]::Multiline)
        
        # Salvar arquivo limpo
        $content | Set-Content $file -NoNewline
        
        $totalRemoved += $fileRemovedCount
        Write-Host "  ✅ $fileRemovedCount logs removidos" -ForegroundColor Green
        Write-Host "  📁 Backup: $backupPath" -ForegroundColor Blue
    } else {
        Write-Host "  ⚠️ Arquivo não encontrado: $file" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Limpeza concluída!" -ForegroundColor Green
Write-Host "📊 Total de console.log removidos: $totalRemoved" -ForegroundColor Cyan

# Verificação final
Write-Host "`n🔍 Verificação final dos console.log restantes..." -ForegroundColor Yellow
$remaining = Select-String -Path "hooks/*", "components/*", "app/*" -Pattern "console\.(log|error|warn|info|debug)" -AllMatches 2>$null
if ($remaining) {
    $count = ($remaining | Measure-Object).Count
    Write-Host "⚠️ Ainda restam $count console.log no projeto" -ForegroundColor Yellow
} else {
    Write-Host "🎉 Nenhum console.log encontrado! Projeto completamente limpo." -ForegroundColor Green
} 