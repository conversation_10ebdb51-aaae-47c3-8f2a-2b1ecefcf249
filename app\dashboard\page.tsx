import { auth, currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

/**
 * 📊 Dashboard - Rota de Fallback
 * 
 * NOTA: Esta rota agora é principalmente uma rota de fallback.
 * O redirecionamento principal após login acontece via /auth-redirect.
 * 
 * Esta página ainda é necessária para:
 * - Compatibilidade com links diretos para /dashboard
 * - Fallback em casos de erro no /auth-redirect
 * - Redirecionamentos de contextos específicos
 */
export default async function DashboardPage() {
  console.log('🔄 [DASHBOARD] Iniciando verificação de redirecionamento (FALLBACK)...');
  
  const { userId } = await auth();
  console.log('🔍 [DASHBOARD] UserId obtido:', userId);

  if (!userId) {
    console.log('❌ [DASHBOARD] Usuário não autenticado, redirecionando para login');
    redirect('/sign-in');
  }

  const user = await currentUser();
  console.log('👤 [DASHBOARD] Dados do usuário:', {
    id: user?.id,
    email: user?.primaryEmailAddress?.emailAddress,
    onboardingCompleted: user?.privateMetadata?.onboardingCompleted
  });
  
  if (!user) {
    console.log('❌ [DASHBOARD] Dados do usuário não encontrados, redirecionando para login');
    redirect('/sign-in');
  }

  // Verificar se o usuário explicitamente NÃO completou o onboarding
  const onboardingCompleted = user.privateMetadata?.onboardingCompleted;
  
  // Por padrão, assumir que completou onboarding (para usuários existentes)
  // Só redirecionar para onboarding se explicitamente definido como false
  if (onboardingCompleted === false) {
    console.log('📝 [DASHBOARD] Usuário não completou onboarding, redirecionando...');
    redirect('/onboarding');
  }

  // Redirecionar para influencers (mantido para compatibilidade)
  const targetUrl = `/${user.id}/influencers`;
  console.log('✅ [DASHBOARD] Redirecionamento (fallback) para influencers:', targetUrl);
  
  redirect(targetUrl);
} 

