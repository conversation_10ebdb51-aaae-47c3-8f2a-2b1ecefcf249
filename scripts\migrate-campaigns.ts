import { db } from '@/lib/firebase-admin';
import { backupCollection } from './backup-firestore';

/**
 * 🔄 SCRIPT DE MIGRAÇÃO DE CAMPAIGNS
 * FASE 5.2: Migração para adicionar userId às campanhas existentes
 */

// Interface para dados de migração
interface MigrationResult {
  collection: string;
  totalDocuments: number;
  migratedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: Array<{ id: string; error: string }>;
  brandRelationships: {
    found: number;
    notFound: number;
    inherited: number;
  };
  duration: number;
}

/**
 * Migrar coleção de campaigns para incluir userId
 */
export async function migrateCampaigns(options: {
  dryRun?: boolean;
  backupFirst?: boolean;
  batchSize?: number;
  validateBrandRelationships?: boolean;
} = {}): Promise<MigrationResult> {
  console.log('🔄 Iniciando migração de campaigns...');
  
  const config = {
    dryRun: false,
    backupFirst: true,
    batchSize: 100,
    validateBrandRelationships: true,
    ...options
  };
  
  const startTime = Date.now();
  
  const result: MigrationResult = {
    collection: 'campaigns',
    totalDocuments: 0,
    migratedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: [],
    brandRelationships: {
      found: 0,
      notFound: 0,
      inherited: 0
    },
    duration: 0
  };
  
  try {
    // 1. Fazer backup se solicitado
    if (config.backupFirst) {
      console.log('📦 Fazendo backup antes da migração...');
      await backupCollection('campaigns');
      console.log('✅ Backup concluído');
    }
    
    // 2. Obter todos os documentos
    const snapshot = await db.collection('campaigns').get();
    result.totalDocuments = snapshot.size;
    
    console.log(`📊 Encontrados ${result.totalDocuments} documentos de campaigns`);
    
    if (result.totalDocuments === 0) {
      console.log('⚠️ Nenhuma campanha encontrada para migrar');
      return result;
    }
    
    // 3. Primeira passada: mapear brands para otimização
    console.log('🗺️ Mapeando brands existentes...');
    const brandsMap = await createBrandsMap();
    console.log(`📊 ${Object.keys(brandsMap).length} brands mapeadas`);
    
    // 4. Processar em lotes
    const docs = snapshot.docs;
    const batches = [];
    
    for (let i = 0; i < docs.length; i += config.batchSize) {
      batches.push(docs.slice(i, i + config.batchSize));
    }
    
    console.log(`🔄 Processando ${batches.length} lotes de até ${config.batchSize} documentos`);
    
    // 5. Migrar cada lote
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`📝 Processando lote ${batchIndex + 1}/${batches.length}`);
      
      const firebaseBatch = db.batch();
      let batchHasUpdates = false;
      
      for (const doc of batch) {
        try {
          const data = doc.data();
          
          // Pular se já tem userId
          if (data.userId) {
            result.skippedCount++;
            console.log(`⏭️ Pulando ${doc.id} - já possui userId`);
            continue;
          }
          
          // Determinar userId para esta campaign
          const userIdResult = await determineUserIdForCampaign(data, brandsMap, result);
          
          if (!userIdResult.userId) {
            result.errorCount++;
            result.errors.push({
              id: doc.id,
              error: userIdResult.error || 'Não foi possível determinar userId'
            });
            console.error(`❌ Erro: ${doc.id} - ${userIdResult.error}`);
            continue;
          }
          
          // Preparar dados para atualização
          const updateData = {
            userId: userIdResult.userId,
            updatedAt: new Date(),
            migratedAt: new Date(),
            _migrationMetadata: {
              phase: 'fase-5-userId-migration',
              timestamp: new Date().toISOString(),
              source: userIdResult.source,
              originalData: {
                brandId: data.brandId || null,
                brandName: data.brandName || null,
                name: data.name || null
              }
            }
          };
          
          if (config.dryRun) {
            console.log(`🔍 [DRY RUN] ${doc.id} seria atualizado com userId: ${userIdResult.userId} (fonte: ${userIdResult.source})`);
            result.migratedCount++;
          } else {
            firebaseBatch.update(doc.ref, updateData);
            batchHasUpdates = true;
            result.migratedCount++;
            console.log(`✅ ${doc.id} será atualizado com userId: ${userIdResult.userId} (fonte: ${userIdResult.source})`);
          }
          
        } catch (error) {
          result.errorCount++;
          const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
          result.errors.push({ id: doc.id, error: errorMsg });
          console.error(`❌ Erro ao processar ${doc.id}:`, error);
        }
      }
      
      // Executar batch se há atualizações
      if (batchHasUpdates && !config.dryRun) {
        try {
          await firebaseBatch.commit();
          console.log(`✅ Lote ${batchIndex + 1} commitado com sucesso`);
        } catch (error) {
          console.error(`❌ Erro ao commitar lote ${batchIndex + 1}:`, error);
          // Contar erros do lote inteiro
          for (const doc of batch) {
            if (!doc.data().userId) {
              result.errorCount++;
              result.migratedCount--;
              result.errors.push({
                id: doc.id,
                error: `Erro no batch commit: ${error.message}`
              });
            }
          }
        }
      }
    }
    
    result.duration = Date.now() - startTime;
    
    // 6. Relatório final
    console.log('\n📊 RELATÓRIO DE MIGRAÇÃO DE CAMPAIGNS');
    console.log('======================================');
    console.log(`📁 Coleção: ${result.collection}`);
    console.log(`📊 Total de documentos: ${result.totalDocuments}`);
    console.log(`✅ Migrados: ${result.migratedCount}`);
    console.log(`⏭️ Pulados: ${result.skippedCount}`);
    console.log(`❌ Erros: ${result.errorCount}`);
    console.log(`⏱️ Duração: ${formatDuration(result.duration)}`);
    
    console.log('\n🔗 Relacionamentos com Brands:');
    console.log(`✅ Brands encontradas: ${result.brandRelationships.found}`);
    console.log(`❌ Brands não encontradas: ${result.brandRelationships.notFound}`);
    console.log(`📥 UserIds herdados: ${result.brandRelationships.inherited}`);
    
    console.log(`\n🔧 Modo: ${config.dryRun ? 'DRY RUN' : 'EXECUÇÃO REAL'}`);
    
    if (result.errors.length > 0) {
      console.log('\n❌ Erros encontrados:');
      result.errors.slice(0, 10).forEach(error => {
        console.log(`  - ${error.id}: ${error.error}`);
      });
      if (result.errors.length > 10) {
        console.log(`  ... e mais ${result.errors.length - 10} erros`);
      }
    }
    
    if (config.dryRun) {
      console.log('\n🔍 Esta foi uma execução em modo DRY RUN. Nenhuma alteração foi feita.');
      console.log('📝 Execute novamente sem --dry-run para aplicar as mudanças.');
    } else {
      console.log('\n✅ Migração de campaigns concluída!');
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ Erro fatal na migração de campaigns:', error);
    result.duration = Date.now() - startTime;
    throw error;
  }
}

/**
 * Criar mapa de brands para otimização
 */
async function createBrandsMap(): Promise<Record<string, { userId: string; name: string }>> {
  const brandsSnapshot = await db.collection('brands').get();
  const brandsMap: Record<string, { userId: string; name: string }> = {};
  
  brandsSnapshot.docs.forEach(doc => {
    const data = doc.data();
    if (data.userId) {
      brandsMap[doc.id] = {
        userId: data.userId,
        name: data.name || 'Nome não definido'
      };
    }
  });
  
  return brandsMap;
}

/**
 * Determinar userId baseado nos dados da campaign
 */
async function determineUserIdForCampaign(
  campaignData: any,
  brandsMap: Record<string, { userId: string; name: string }>,
  result: MigrationResult
): Promise<{ userId: string | null; source: string; error?: string }> {
  try {
    // Estratégia 1: Através da brand relacionada (preferencial)
    if (campaignData.brandId) {
      console.log(`🔍 Verificando brandId: ${campaignData.brandId}`);
      
      if (brandsMap[campaignData.brandId]) {
        const brand = brandsMap[campaignData.brandId];
        result.brandRelationships.found++;
        result.brandRelationships.inherited++;
        console.log(`✅ UserId herdado da brand "${brand.name}": ${brand.userId}`);
        return {
          userId: brand.userId,
          source: `brand_relationship:${campaignData.brandId}`
        };
      } else {
        // Brand não encontrada ou sem userId - buscar no banco
        const brandDoc = await db.collection('brands').doc(campaignData.brandId).get();
        
        if (brandDoc.exists) {
          const brandData = brandDoc.data();
          if (brandData?.userId) {
            result.brandRelationships.found++;
            result.brandRelationships.inherited++;
            console.log(`✅ UserId encontrado na brand (busca direta): ${brandData.userId}`);
            return {
              userId: brandData.userId,
              source: `brand_direct:${campaignData.brandId}`
            };
          } else {
            result.brandRelationships.notFound++;
            console.log(`⚠️ Brand ${campaignData.brandId} encontrada mas sem userId`);
          }
        } else {
          result.brandRelationships.notFound++;
          console.log(`❌ Brand ${campaignData.brandId} não encontrada`);
        }
      }
    }
    
    // Estratégia 2: Buscar por brandName se brandId não funcionou
    if (campaignData.brandName) {
      console.log(`🔍 Buscando brand por nome: ${campaignData.brandName}`);
      
      const brandSnapshot = await db.collection('brands')
        .where('name', '==', campaignData.brandName)
        .limit(1)
        .get();
      
      if (!brandSnapshot.empty) {
        const brandData = brandSnapshot.docs[0].data();
        if (brandData.userId) {
          result.brandRelationships.found++;
          result.brandRelationships.inherited++;
          console.log(`✅ UserId encontrado por nome da brand: ${brandData.userId}`);
          return {
            userId: brandData.userId,
            source: `brand_name_search:${campaignData.brandName}`
          };
        }
      }
    }
    
    // Estratégia 3: Buscar campaigns similares já migradas
    if (campaignData.name) {
      console.log(`🔍 Buscando campaigns similares para: ${campaignData.name}`);
      
      const similarCampaignsSnapshot = await db.collection('campaigns')
        .where('name', '==', campaignData.name)
        .limit(5)
        .get();
      
      for (const similarDoc of similarCampaignsSnapshot.docs) {
        const similarData = similarDoc.data();
        if (similarData.userId) {
          console.log(`✅ UserId encontrado em campanha similar: ${similarData.userId}`);
          return {
            userId: similarData.userId,
            source: `similar_campaign:${similarDoc.id}`
          };
        }
      }
    }
    
    // Estratégia 4: Fallback para primeiro admin
    console.log('🔍 Buscando usuário admin como fallback...');
    
    const adminSnapshot = await db.collection('users')
      .where('role', '==', 'admin')
      .limit(1)
      .get();
    
    if (!adminSnapshot.empty) {
      const adminUserId = adminSnapshot.docs[0].id;
      console.log(`⚠️ Usando admin como fallback: ${adminUserId}`);
      return {
        userId: adminUserId,
        source: 'admin_fallback'
      };
    }
    
    // Estratégia 5: Qualquer usuário como último recurso
    const anyUserSnapshot = await db.collection('users').limit(1).get();
    if (!anyUserSnapshot.empty) {
      const anyUserId = anyUserSnapshot.docs[0].id;
      console.log(`⚠️ Usando primeiro usuário disponível: ${anyUserId}`);
      return {
        userId: anyUserId,
        source: 'any_user_fallback'
      };
    }
    
    return {
      userId: null,
      source: 'none',
      error: 'Nenhuma estratégia funcionou para determinar userId'
    };
    
  } catch (error) {
    console.error('❌ Erro ao determinar userId:', error);
    return {
      userId: null,
      source: 'error',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Validar integridade dos relacionamentos após migração
 */
export async function validateCampaignBrandRelationships(): Promise<{
  total: number;
  validRelationships: number;
  invalidRelationships: number;
  orphanedCampaigns: number;
  issues: Array<{ campaignId: string; issue: string; brandId?: string }>;
}> {
  console.log('🔍 Validando relacionamentos campaign-brand...');
  
  const campaignsSnapshot = await db.collection('campaigns').get();
  const total = campaignsSnapshot.size;
  let validRelationships = 0;
  let invalidRelationships = 0;
  let orphanedCampaigns = 0;
  const issues: Array<{ campaignId: string; issue: string; brandId?: string }> = [];
  
  for (const campaignDoc of campaignsSnapshot.docs) {
    const campaign = campaignDoc.data();
    
    if (!campaign.userId) {
      issues.push({
        campaignId: campaignDoc.id,
        issue: 'Campaign sem userId'
      });
      continue;
    }
    
    if (!campaign.brandId) {
      orphanedCampaigns++;
      issues.push({
        campaignId: campaignDoc.id,
        issue: 'Campaign sem brandId'
      });
      continue;
    }
    
    // Verificar se brand existe e tem mesmo userId
    const brandDoc = await db.collection('brands').doc(campaign.brandId).get();
    
    if (!brandDoc.exists) {
      invalidRelationships++;
      issues.push({
        campaignId: campaignDoc.id,
        issue: 'Brand referenciada não existe',
        brandId: campaign.brandId
      });
    } else {
      const brand = brandDoc.data();
      if (brand?.userId !== campaign.userId) {
        invalidRelationships++;
        issues.push({
          campaignId: campaignDoc.id,
          issue: `UserIds não coincidem. Campaign: ${campaign.userId}, Brand: ${brand?.userId}`,
          brandId: campaign.brandId
        });
      } else {
        validRelationships++;
      }
    }
  }
  
  const result = {
    total,
    validRelationships,
    invalidRelationships,
    orphanedCampaigns,
    issues
  };
  
  console.log(`📊 Relacionamentos válidos: ${validRelationships}/${total}`);
  console.log(`❌ Relacionamentos inválidos: ${invalidRelationships}`);
  console.log(`🏴‍☠️ Campaigns órfãs: ${orphanedCampaigns}`);
  
  return result;
}

/**
 * Reverter migração de campaigns
 */
export async function revertCampaignsMigration(): Promise<void> {
  console.log('🔄 Revertendo migração de campaigns...');
  
  const snapshot = await db.collection('campaigns').get();
  const batch = db.batch();
  
  let revertCount = 0;
  
  for (const doc of snapshot.docs) {
    const data = doc.data();
    
    if (data.migratedAt) {
      batch.update(doc.ref, {
        userId: null,
        migratedAt: null,
        _migrationMetadata: null
      });
      revertCount++;
    }
  }
  
  if (revertCount > 0) {
    await batch.commit();
    console.log(`✅ Migração revertida para ${revertCount} campaigns`);
  } else {
    console.log('⚠️ Nenhuma campaign com dados de migração encontrada');
  }
}

// Utilitários
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
}

// Função principal para execução via linha de comando
export async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const noBackup = args.includes('--no-backup');
  const validateOnly = args.includes('--validate-only');
  
  try {
    if (validateOnly) {
      console.log('🔍 Executando apenas validação...\n');
      const validation = await validateCampaignBrandRelationships();
      
      if (validation.issues.length === 0) {
        console.log('✅ Todos os relacionamentos estão válidos!');
      } else {
        console.log('⚠️ Problemas encontrados nos relacionamentos');
        validation.issues.slice(0, 10).forEach(issue => {
          console.log(`  - ${issue.campaignId}: ${issue.issue}`);
        });
      }
      
      process.exit(0);
    }
    
    console.log('🚀 Iniciando migração de campaigns...\n');
    
    const result = await migrateCampaigns({
      dryRun,
      backupFirst: !noBackup
    });
    
    // Validar relacionamentos se não for dry run
    if (!dryRun) {
      console.log('\n🔍 Validando relacionamentos...');
      await validateCampaignBrandRelationships();
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Erro na migração:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 

