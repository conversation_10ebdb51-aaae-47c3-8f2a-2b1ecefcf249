'use client';

import React, { useState, useEffect } from 'react';
import { TourProvider, useTour } from '@reactour/tour';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  HelpCircle, 
  BarChart3,
  PieChart,
  TrendingUp,
  Eye,
  Users,
  Target,
  Lightbulb,
  Info
} from 'lucide-react';

// Tour específico para Gênero da Audiência
const genderChartSteps = [
  {
    selector: '[data-tour="gender-chart"]',
    content: 'Este gráfico de pizza mostra a distribuição de gênero da audiência do influenciador por plataforma. As cores representam: Rosa (#ff0074) para Feminino, Roxo (#5600ce) para Masculino, e Cinza para Outros.',
  },
  {
    selector: '[data-tour="gender-chart"] .recharts-pie-sector',
    content: 'Cada fatia representa a porcentagem de audiência daquele gênero. Informação crucial para segmentação de campanhas e escolha de produtos adequados ao público.',
  },
  {
    selector: '[data-tour="gender-chart"] .flex.items-center.space-x-2',
    content: 'A legenda mostra as porcentagens exatas para cada gênero. Use estes dados para decidir se o influenciador alinha com seu público-alvo.',
  },
];

// Tour específico para Faixas Etárias
const ageRangeChartSteps = [
  {
    selector: '[data-tour="age-range-chart"]',
    content: 'Gráfico de barras comparativo mostrando as faixas etárias da audiência por plataforma. Cada cor representa uma rede social diferente.',
  },
  {
    selector: '[data-tour="age-range-chart"] .recharts-bar-rectangles',
    content: 'As barras mostram a concentração de audiência em cada faixa etária. Compare plataformas para encontrar onde seu público-alvo está mais ativo.',
  },
  {
    selector: '[data-tour="age-range-chart"] .recharts-cartesian-axis-tick',
    content: 'As faixas etárias estão organizadas dos mais jovens aos mais velhos. Identifique padrões geracionais para campanhas mais efetivas.',
  },
  {
    selector: '[data-tour="age-range-chart"] .recharts-legend-wrapper',
    content: 'A legenda diferencia cada plataforma por cor. Observe como diferentes redes atraem diferentes grupos etários.',
  },
];

// Tour específico para Taxa de Engajamento
const engagementChartSteps = [
  {
    selector: '[data-tour="engagement-chart"]',
    content: 'Gráfico crucial que mostra a taxa de engajamento por plataforma. Taxas mais altas indicam audiência mais engajada e melhor ROI potencial.',
  },
  {
    selector: '[data-tour="engagement-chart"] .recharts-bar',
    content: 'Cada barra representa a taxa de engajamento (curtidas, comentários, compartilhamentos) daquela plataforma. Valores acima de 3% são considerados excelentes.',
  },
  {
    selector: '[data-tour="engagement-chart"] .recharts-label-list',
    content: 'Os valores no topo das barras mostram a porcentagem exata de engajamento. Compare com benchmarks da indústria para avaliar performance.',
  },
];

// Tour específico para Visualizações por Tipo de Conteúdo
const contentViewsChartSteps = [
  {
    selector: '[data-tour="content-views-chart"]',
    content: 'Gráfico horizontal mostrando visualizações por tipo de conteúdo nos últimos 30 dias. Identifique quais formatos geram mais alcance.',
  },
  {
    selector: '[data-tour="content-views-chart"] .recharts-bar',
    content: 'Cada barra representa um tipo de conteúdo diferente: Stories, Reels, Shorts, etc. Comprimentos maiores indicam mais visualizações.',
  },
  {
    selector: '[data-tour="content-views-chart"] .recharts-label-list',
    content: 'Os números nas barras mostram visualizações totais. Use para negociar preços baseados no alcance real de cada formato.',
  },
];

// Componentes de botões para cada tour
export function GenderChartTourButton() {
  const { setIsOpen } = useTour();
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setIsOpen(true)}
      className="gap-1 text-[#ff0074] hover:bg-[#ff0074]/10 h-6 px-2 text-xs"
    >
      <PieChart className="w-3 h-3" />
      ?
    </Button>
  );
}

export function AgeRangeChartTourButton() {
  const { setIsOpen } = useTour();
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setIsOpen(true)}
      className="gap-1 text-[#5600ce] hover:bg-[#5600ce]/10 h-6 px-2 text-xs"
    >
      <BarChart3 className="w-3 h-3" />
      ?
    </Button>
  );
}

export function EngagementChartTourButton() {
  const { setIsOpen } = useTour();
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setIsOpen(true)}
      className="gap-1 text-[#ff0074] hover:bg-[#ff0074]/10 h-6 px-2 text-xs"
    >
      <TrendingUp className="w-3 h-3" />
      ?
    </Button>
  );
}

export function ContentViewsTourButton() {
  const { setIsOpen } = useTour();
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setIsOpen(true)}
      className="gap-1 text-[#5600ce] hover:bg-[#5600ce]/10 h-6 px-2 text-xs"
    >
      <Eye className="w-3 h-3" />
      ?
    </Button>
  );
}

// Providers para cada tour específico
export function GenderChartTourProvider({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return <>{children}</>;

  return (
    <TourProvider
      steps={genderChartSteps}
      showBadge={true}
      showCloseButton={true}
      showNavigation={true}
      showDots={true}
      styles={{
        popover: (base) => ({
          ...base,
          backgroundColor: 'white',
          color: 'black',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '320px',
          boxShadow: '0 10px 25px rgba(255,0,116,0.2)',
        }),
        badge: (base) => ({
          ...base,
          backgroundColor: '#ff0074',
          color: 'white',
        }),
      }}
      onClickMask={({ setIsOpen }) => setIsOpen(false)}
    >
      {children}
    </TourProvider>
  );
}

export function AgeRangeChartTourProvider({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return <>{children}</>;

  return (
    <TourProvider
      steps={ageRangeChartSteps}
      showBadge={true}
      showCloseButton={true}
      showNavigation={true}
      showDots={true}
      styles={{
        popover: (base) => ({
          ...base,
          backgroundColor: 'white',
          color: 'black',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '320px',
          boxShadow: '0 10px 25px rgba(86,0,206,0.2)',
        }),
        badge: (base) => ({
          ...base,
          backgroundColor: '#5600ce',
          color: 'white',
        }),
      }}
      onClickMask={({ setIsOpen }) => setIsOpen(false)}
    >
      {children}
    </TourProvider>
  );
}

export function EngagementChartTourProvider({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return <>{children}</>;

  return (
    <TourProvider
      steps={engagementChartSteps}
      showBadge={true}
      showCloseButton={true}
      showNavigation={true}
      showDots={true}
      styles={{
        popover: (base) => ({
          ...base,
          backgroundColor: 'white',
          color: 'black',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '320px',
          boxShadow: '0 10px 25px rgba(255,0,116,0.2)',
        }),
        badge: (base) => ({
          ...base,
          backgroundColor: '#ff0074',
          color: 'white',
        }),
      }}
      onClickMask={({ setIsOpen }) => setIsOpen(false)}
    >
      {children}
    </TourProvider>
  );
}

export function ContentViewsTourProvider({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return <>{children}</>;

  return (
    <TourProvider
      steps={contentViewsChartSteps}
      showBadge={true}
      showCloseButton={true}
      showNavigation={true}
      showDots={true}
      styles={{
        popover: (base) => ({
          ...base,
          backgroundColor: 'white',
          color: 'black',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '320px',
          boxShadow: '0 10px 25px rgba(86,0,206,0.2)',
        }),
        badge: (base) => ({
          ...base,
          backgroundColor: '#5600ce',
          color: 'white',
        }),
      }}
      onClickMask={({ setIsOpen }) => setIsOpen(false)}
    >
      {children}
    </TourProvider>
  );
} 