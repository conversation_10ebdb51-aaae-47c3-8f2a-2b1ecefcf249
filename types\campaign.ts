// Modelos de dados para campanhas com influenciadores

import { BaseDocument, CreateData, UpdateData, DocumentStatus } from './base';

// Status possíveis para uma campanha
export type CampaignStatus = 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

// Status possíveis para um influenciador em uma campanha
export type InfluencerCampaignStatus = 'invited' | 'confirmed' | 'declined' | 'in_progress' | 'completed';

// Status possíveis para pagamento
export type PaymentStatus = 'pending' | 'partial' | 'completed' | 'cancelled';

// Prioridade da campanha
export type CampaignPriority = 'low' | 'medium' | 'high' | 'urgent';

// Interface para deliverables da campanha
export interface CampaignDeliverable {
  id: string;
  type: 'post' | 'story' | 'reel' | 'video' | 'article' | 'live';
  platform: string;
  description: string;
  dueDate: Date;
  completed: boolean;
  completedAt?: Date;
  notes?: string;
}

// Interface para influenciador em uma campanha
export interface CampaignInfluencer {
  influencerId: string;
  financialId?: string;
  negotiatedPrice: number;
  deliverables: CampaignDeliverable[];
  status: InfluencerCampaignStatus;
  paymentStatus: PaymentStatus;
  invitedAt: Date;
  confirmedAt?: Date;
  completedAt?: Date;
  notes?: string;
}

// Interface principal para campanhas
export interface Campaign extends BaseDocument {
  name: string;
  description?: string;
  brandId: string;         // Deve pertencer ao mesmo userId
  brandName?: string;      // Cache para performance
  clientName?: string;     // Nome do cliente final (se diferente da marca)
  
  // Datas e orçamento
  startDate: Date;
  endDate: Date;
  budget: number;
  currency: string;
  
  // Objetivos e público
  objectives?: string;
  targetAudience?: string;
  kpis?: string[];
  
  // Status e metadados
  status: CampaignStatus;
  priority: CampaignPriority;
  tags: string[];
  notes?: string;
  
  // Progresso
  progress: number;        // 0-100
  
  // Influenciadores na campanha
  influencers: CampaignInfluencer[];
  
  // Aprovações
  requiresApproval: boolean;
  approvedBy?: string;
  approvedAt?: Date;
}

// Interface para criação de nova campanha (userId será adicionado automaticamente)
export interface CreateCampaignData {
  name: string;
  description?: string;
  brandId: string;         // Validação: deve pertencer ao mesmo userId
  clientName?: string;
  startDate: Date;
  endDate: Date;
  budget: number;
  currency?: string;
  objectives?: string;
  targetAudience?: string;
  kpis?: string[];
  status?: CampaignStatus;
  priority?: CampaignPriority;
  tags?: string[];
  notes?: string;
  influencers?: Omit<CampaignInfluencer, 'invitedAt'>[];
  requiresApproval?: boolean;
}

// Interface para atualização de campanha usando tipo utilitário
export type UpdateCampaignData = UpdateData<Campaign>;

// Interface para campanha com validação de ownership e permissões
export interface CampaignWithPermissions extends Campaign {
  canEdit: boolean;
  canDelete: boolean;
  canInviteInfluencers: boolean;
  canApprove: boolean;
  brandOwnership: boolean;    // Se o usuário é dono da brand associada
}

// Interface para filtros de campanha
export interface CampaignFilters {
  brandId?: string;
  status?: CampaignStatus[];
  priority?: CampaignPriority[];
  startDateFrom?: Date;
  startDateTo?: Date;
  endDateFrom?: Date;
  endDateTo?: Date;
  budgetMin?: number;
  budgetMax?: number;
  tags?: string[];
  search?: string;
}

// Utilitários para validação
export function validateCampaignDates(startDate: Date, endDate: Date): boolean {
  return endDate > startDate;
}

export function validateBrandOwnership(campaign: Campaign, brandUserId: string): boolean {
  return campaign.userId === brandUserId;
}

export function calculateCampaignProgress(campaign: Campaign): number {
  if (campaign.influencers.length === 0) return 0;
  
  const totalDeliverables = campaign.influencers.reduce(
    (sum, inf) => sum + inf.deliverables.length, 0
  );
  
  if (totalDeliverables === 0) return 0;
  
  const completedDeliverables = campaign.influencers.reduce(
    (sum, inf) => sum + inf.deliverables.filter(d => d.completed).length, 0
  );
  
  return Math.round((completedDeliverables / totalDeliverables) * 100);
}


