import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { BrandInfluencerService } from '@/services/brand-influencer-service';
import { CreateBrandInfluencerData } from '@/types/brand-influencer';

/**
 * POST /api/brand-influencers - Criar nova associação marca-influenciador
 */
export async function POST(req: NextRequest) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      try {
        const data: CreateBrandInfluencerData = await req.json();

        console.log('[API_BRAND_INFLUENCERS_POST] Dados recebidos:', { 
          brandId: data.brandId,
          influencerId: data.influencerId,
          userId 
        });

        // Validar dados obrigatórios
        if (!data.brandId || !data.influencerId) {
          return NextResponse.json(
            { error: 'brandId e influencerId são obrigatórios' },
            { status: 400 }
          );
        }

        // Criar associação
        const association = await BrandInfluencerService.createAssociation(data, userId);

        console.log('[API_BRAND_INFLUENCERS_POST] Associação criada:', association.id);

        return NextResponse.json(association, { status: 201 });

      } catch (error: any) {
        console.error('[API_BRAND_INFLUENCERS_POST] Erro:', error);
        
        return NextResponse.json(
          { 
            error: 'Erro interno do servidor',
            message: error.message 
          },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * GET /api/brand-influencers - Buscar associações do usuário
 */
export async function GET(req: NextRequest) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      try {
        const url = new URL(req.url);
        const brandId = url.searchParams.get('brandId');
        const influencerId = url.searchParams.get('influencerId');
        const status = url.searchParams.get('status') as 'active' | 'inactive' | 'paused' | null;
        const priority = url.searchParams.get('priority') as 'high' | 'medium' | 'low' | null;
        const search = url.searchParams.get('search');

        const filters = {
          brandId: brandId || undefined,
          influencerId: influencerId || undefined,
          status: status || undefined,
          priority: priority || undefined,
          search: search || undefined
        };

        const result = await BrandInfluencerService.getAssociations(userId, filters);

        console.log('[API_BRAND_INFLUENCERS_GET] Associações encontradas:', result.total);

        return NextResponse.json(result);

      } catch (error: any) {
        console.error('[API_BRAND_INFLUENCERS_GET] Erro:', error);
        
        return NextResponse.json(
          { 
            error: 'Erro interno do servidor',
            message: error.message 
          },
          { status: 500 }
        );
      }
    }
  )(req);
} 

