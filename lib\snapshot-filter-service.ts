import { 
  InfluencerSnapshot, 
  InfluencerBasicSnapshot, 
  InfluencerCompleteSnapshot,
  SocialPlatformBasicSnapshot,
  SocialPlatformCompleteSnapshot,
  PropostaComSnapshotBasico,
  PropostaComSnapshotCompleto,
  PropostaComSnapshot,
  SnapshotAccessLevel,
  SnapshotFilterConfig
} from '@/types/proposal-snapshot';

export class SnapshotFilterService {
  
  /**
   * 🔒 FILTRAR SNAPSHOT BASEADO NO ROLE DO USUÁRIO E PAPEL COMO COLABORADOR
   */
  static filterSnapshotByRole(
    snapshot: any, 
    userRole: string, 
    config?: Partial<SnapshotFilterConfig & {
      collaboratorRole?: 'owner' | 'editor' | 'viewer' | 'none';
      isCollaborator?: boolean;
    }>
  ): PropostaComSnapshot {
    
    // 🎯 NOVA LÓGICA: Considerar role como colaborador
    const effectiveAccessLevel = this.determineAccessLevel(userRole, config?.collaboratorRole, config?.isCollaborator);
    
    const filterConfig: SnapshotFilterConfig = {
      accessLevel: effectiveAccessLevel,
      userRole,
      includeFinancials: effectiveAccessLevel === SnapshotAccessLevel.COMPLETE,
      includePricing: effectiveAccessLevel === SnapshotAccessLevel.COMPLETE || config?.collaboratorRole === 'editor',
      includeContactInfo: effectiveAccessLevel === SnapshotAccessLevel.COMPLETE,
      includeNegotiationHistory: effectiveAccessLevel === SnapshotAccessLevel.COMPLETE,
      ...config
    };

    console.log('🔒 Filtrando snapshot:', {
      userRole,
      collaboratorRole: config?.collaboratorRole,
      isCollaborator: config?.isCollaborator,
      effectiveAccessLevel,
      config: filterConfig
    });

    if (filterConfig.accessLevel === SnapshotAccessLevel.BASIC) {
      return this.createBasicSnapshot(snapshot, filterConfig);
    } else {
      return this.createCompleteSnapshot(snapshot, filterConfig);
    }
  }

  /**
   * 🎯 DETERMINAR NÍVEL DE ACESSO BASEADO EM ROLE GLOBAL + COLABORADOR
   */
  private static determineAccessLevel(
    userRole: string, 
    collaboratorRole?: 'owner' | 'editor' | 'viewer' | 'none',
    isCollaborator?: boolean
  ): SnapshotAccessLevel {
    
    // 1. Admins/Managers sempre têm acesso completo
    if (['super_admin', 'admin', 'manager'].includes(userRole)) {
      return SnapshotAccessLevel.COMPLETE;
    }
    
    // 2. Proprietários têm acesso completo
    if (collaboratorRole === 'owner') {
      return SnapshotAccessLevel.COMPLETE;
    }
    
    // 3. Editores colaboradores têm acesso intermediário (basic com alguns extras)
    if (isCollaborator && collaboratorRole === 'editor') {
      return SnapshotAccessLevel.BASIC;
    }
    
    // 4. Viewers colaboradores têm acesso básico
    if (isCollaborator && collaboratorRole === 'viewer') {
      return SnapshotAccessLevel.BASIC;
    }
    
    // 5. Usuários sem colaboração = acesso básico muito restrito
    return SnapshotAccessLevel.BASIC;
  }

  /**
   * 🟢 CRIAR SNAPSHOT BÁSICO (para colaboradores e role USER)
   * NOVA VERSÃO: Mais generoso para colaboradores legítimos
   */
  private static createBasicSnapshot(
    originalSnapshot: any, 
    config: SnapshotFilterConfig
  ): PropostaComSnapshotBasico {
    
    const filteredInfluencers: InfluencerBasicSnapshot[] = originalSnapshot.influencersSnapshot?.map(
      (influencer: any) => this.filterInfluencerToBasic(influencer, config)
    ) || [];

    // 🎯 NOVA LÓGICA: Para colaboradores editores, incluir alguns dados de pricing básicos
    const includeBasicPricing = config.includePricing;
    
    const filteredServices = originalSnapshot.services?.map((service: any) => ({
      serviceId: service.serviceId,
      serviceName: service.serviceName,
      description: service.description,
      quantity: service.quantity,
      // Incluir pricing básico para editores
      ...(includeBasicPricing && service.pricing && {
        pricing: {
          hasCustomPrice: Boolean(service.pricing.customPrice),
          originalPrice: service.pricing.originalPrice || 0,
          // Não incluir preço exato, apenas indicação se foi negociado
          isNegotiated: service.pricing.customPrice !== service.pricing.originalPrice
        }
      })
    })) || [];

    return {
      id: originalSnapshot.id,
      nome: originalSnapshot.nome,
      descricao: originalSnapshot.descricao,
      status: originalSnapshot.status,
      priority: originalSnapshot.priority,
      dataEnvio: originalSnapshot.dataEnvio,
      createdAt: originalSnapshot.createdAt,
      updatedAt: originalSnapshot.updatedAt,
      
      influencersSnapshot: filteredInfluencers,
      
      brand: originalSnapshot.brand ? {
        id: originalSnapshot.brand.id,
        name: originalSnapshot.brand.name,
        logo: originalSnapshot.brand.logo,
        category: originalSnapshot.brand.category
      } : undefined,
      
      services: filteredServices,
      
      snapshotMetadata: {
        createdAt: new Date(),
        createdBy: config.userRole,
        version: includeBasicPricing ? '2.0.0-collaborator' : '1.0.0-basic',
        accessLevel: 'basic',
        filteredFields: this.getFilteredFieldsForAccess(config.userRole, includeBasicPricing)
      }
    };
  }

  /**
   * 🔵 CRIAR SNAPSHOT COMPLETO (para admins/managers)
   */
  private static createCompleteSnapshot(
    originalSnapshot: any, 
    config: SnapshotFilterConfig
  ): PropostaComSnapshotCompleto {
    
    const completeInfluencers: InfluencerCompleteSnapshot[] = originalSnapshot.influencersSnapshot?.map(
      (influencer: any) => this.filterInfluencerToComplete(influencer, config)
    ) || [];

    const completeServices = originalSnapshot.services?.map((service: any) => ({
      serviceId: service.serviceId,
      serviceName: service.serviceName,
      description: service.description,
      quantity: service.quantity,
      pricing: service.pricing || {
        originalPrice: service.precoOriginal || 0,
        customPrice: service.precoNegociado,
        discount: service.discount || 0
      },
      totalValue: service.totalValue || 0
    })) || [];

    return {
      id: originalSnapshot.id,
      nome: originalSnapshot.nome,
      descricao: originalSnapshot.descricao,
      status: originalSnapshot.status,
      priority: originalSnapshot.priority,
      dataEnvio: originalSnapshot.dataEnvio,
      createdAt: originalSnapshot.createdAt,
      updatedAt: originalSnapshot.updatedAt,
      
      influencersSnapshot: completeInfluencers,
      
      brand: originalSnapshot.brand,
      
      services: completeServices,
      
      financial: config.includeFinancials ? {
        totalAmount: originalSnapshot.totalAmount || 0,
        currency: originalSnapshot.currency || 'BRL',
        paymentTerms: originalSnapshot.paymentTerms || '',
        invoiceData: originalSnapshot.invoiceData
      } : undefined,
      
      negotiation: config.includeNegotiationHistory ? {
        history: originalSnapshot.negotiationHistory || [],
        currentOffer: originalSnapshot.currentOffer,
        counterOffers: originalSnapshot.counterOffers || []
      } : undefined,
      
      snapshotMetadata: {
        createdAt: new Date(),
        createdBy: config.userRole,
        version: '1.0.0-complete',
        accessLevel: 'complete',
        includesFinancials: config.includeFinancials,
        includesPricing: config.includePricing
      }
    };
  }

  /**
   * 🟢 FILTRAR INFLUENCIADOR PARA VERSÃO BÁSICA
   */
  private static filterInfluencerToBasic(
    influencer: any, 
    config: SnapshotFilterConfig
  ): InfluencerBasicSnapshot {
    
    const basicSocialPlatforms: SocialPlatformBasicSnapshot[] = influencer.redesSociais 
      ? this.convertSocialPlatformsToBasic(influencer.redesSociais)
      : influencer.socialPlatforms?.map((platform: any) => ({
          platform: platform.platform,
          username: platform.username,
          url: platform.url,
          followers: platform.followers,
          engagement: platform.engagement,
          verified: platform.verified,
          isActive: platform.isActive
        })) || [];

    // 🔥 PRESERVAR SUBCOLEÇÕES IMPORTANTES DOS SNAPSHOTS
    const result: any = {
      id: influencer.influencerId || influencer.id,
      name: influencer.nome || influencer.name,
      avatar: influencer.avatar,
      bio: influencer.bio,
      followers: influencer.followers || influencer.redesSociais?.instagram?.seguidores,
      engagement: influencer.engagement || influencer.redesSociais?.instagram?.engajamento,
      tier: influencer.tier,
      category: influencer.categoria || influencer.category,
      location: influencer.localizacao?.cidade || influencer.location,
      verified: influencer.verificado || influencer.verified,
      socialPlatforms: basicSocialPlatforms,
      
      portfolio: influencer.portfolio?.filter((item: any) => !item.pricing), // Remove pricing do portfolio
      
      stats: influencer.stats ? {
        avgLikes: influencer.stats.avgLikes,
        avgComments: influencer.stats.avgComments,
        avgShares: influencer.stats.avgShares,
        postFrequency: influencer.stats.postFrequency
      } : undefined,
      
      capturedAt: influencer.snapshotMetadata?.dataCaptura || new Date(),
      snapshotVersion: '1.0.0-basic',

      // 🔥 PRESERVAR SUBCOLEÇÕES DOS SNAPSHOTS (CRÍTICO PARA INTERFACE)
      ...(influencer.currentPricing && { currentPricing: influencer.currentPricing }),
      ...(influencer.currentDemographics && { currentDemographics: influencer.currentDemographics }),
      ...(influencer.currentBudgets && { currentBudgets: influencer.currentBudgets }),
      
      // 🔥 PRESERVAR DADOS ESTRUTURAIS ORIGINAIS DO SNAPSHOT
      ...(influencer.socialNetworks && { socialNetworks: influencer.socialNetworks }),
      ...(influencer.expertise && { expertise: influencer.expertise }),
      ...(influencer.age && { age: influencer.age }),
      ...(influencer.gender && { gender: influencer.gender }),
      // ❌ CAMPOS ADMINISTRATIVOS EXCLUÍDOS DO SNAPSHOT:
      // - email (removido)
      // - whatsapp (removido)
      // - responsibleName (removido)  
      // - agencyName (removido)
      // - responsibleCapturer (removido)
      // - promotesTraders (removido - será filtrado abaixo)
      ...(influencer.totalFollowers && { totalFollowers: influencer.totalFollowers }),
      ...(influencer.rating && { rating: influencer.rating }),
      ...(influencer.isVerified !== undefined && { isVerified: influencer.isVerified }),
      // ❌ createdAt removido - campo administrativo excluído do snapshot
      
      // 🔥 PRESERVAR CAMPOS ESPECÍFICOS DAS REDES SOCIAIS
      // Instagram
      ...(influencer.instagramUsername && { instagramUsername: influencer.instagramUsername }),
      ...(influencer.instagramFollowers && { instagramFollowers: influencer.instagramFollowers }),
      ...(influencer.instagramEngagementRate && { instagramEngagementRate: influencer.instagramEngagementRate }),
      ...(influencer.instagramAvgViews && { instagramAvgViews: influencer.instagramAvgViews }),
      ...(influencer.instagramStoriesViews && { instagramStoriesViews: influencer.instagramStoriesViews }),
      ...(influencer.instagramReelsViews && { instagramReelsViews: influencer.instagramReelsViews }),
      
      // Facebook
      ...(influencer.facebookUsername && { facebookUsername: influencer.facebookUsername }),
      ...(influencer.facebookFollowers && { facebookFollowers: influencer.facebookFollowers }),
      ...(influencer.facebookEngagementRate && { facebookEngagementRate: influencer.facebookEngagementRate }),
              ...(influencer.facebookAvgViews && { facebookAvgViews: influencer.facebookAvgViews }),
        ...(influencer.facebookViews && { facebookViews: influencer.facebookViews }),
      
      // TikTok
      ...(influencer.tiktokUsername && { tiktokUsername: influencer.tiktokUsername }),
      ...(influencer.tiktokFollowers && { tiktokFollowers: influencer.tiktokFollowers }),
      ...(influencer.tiktokEngagementRate && { tiktokEngagementRate: influencer.tiktokEngagementRate }),
      ...(influencer.tiktokAvgViews && { tiktokAvgViews: influencer.tiktokAvgViews }),
      ...(influencer.tiktokVideoViews && { tiktokVideoViews: influencer.tiktokVideoViews }),
      
      // YouTube
      ...(influencer.youtubeUsername && { youtubeUsername: influencer.youtubeUsername }),
      ...(influencer.youtubeFollowers && { youtubeFollowers: influencer.youtubeFollowers }),
      ...(influencer.youtubeSubscribers && { youtubeSubscribers: influencer.youtubeSubscribers }),
      ...(influencer.youtubeEngagementRate && { youtubeEngagementRate: influencer.youtubeEngagementRate }),
      ...(influencer.youtubeAvgViews && { youtubeAvgViews: influencer.youtubeAvgViews }),
      ...(influencer.youtubeShortsViews && { youtubeShortsViews: influencer.youtubeShortsViews }),
      ...(influencer.youtubeLongFormViews && { youtubeLongFormViews: influencer.youtubeLongFormViews }),
      
      // Twitch
      ...(influencer.twitchUsername && { twitchUsername: influencer.twitchUsername }),
      ...(influencer.twitchFollowers && { twitchFollowers: influencer.twitchFollowers }),
      ...(influencer.twitchEngagementRate && { twitchEngagementRate: influencer.twitchEngagementRate }),
      ...(influencer.twitchViews && { twitchViews: influencer.twitchViews }),
      
      // Kwai
      ...(influencer.kwaiUsername && { kwaiUsername: influencer.kwaiUsername }),
      ...(influencer.kwaiFollowers && { kwaiFollowers: influencer.kwaiFollowers }),
      ...(influencer.kwaiEngagementRate && { kwaiEngagementRate: influencer.kwaiEngagementRate }),
      
      // Campos extras
      ...(influencer.phone && { phone: influencer.phone }),
      ...(influencer.country && { country: influencer.country }),
      ...(influencer.state && { state: influencer.state }),
      ...(influencer.city && { city: influencer.city }),
      ...(influencer.categories && { categories: influencer.categories }),
      ...(influencer.engagementRate && { engagementRate: influencer.engagementRate }),
      ...(influencer.isAvailable !== undefined && { isAvailable: influencer.isAvailable }),
      ...(influencer.status && { status: influencer.status }),
      
      // 🔥 PRESERVAR METADADOS DO SNAPSHOT
      ...(influencer.snapshotMetadata && { snapshotMetadata: influencer.snapshotMetadata })
    };

    return result;
  }

  /**
   * 🔵 FILTRAR INFLUENCIADOR PARA VERSÃO COMPLETA
   */
  private static filterInfluencerToComplete(
    influencer: any, 
    config: SnapshotFilterConfig
  ): InfluencerCompleteSnapshot {
    
    const basicData = this.filterInfluencerToBasic(influencer, config);
    
    const completeSocialPlatforms: SocialPlatformCompleteSnapshot[] = influencer.redesSociais 
      ? this.convertSocialPlatformsToComplete(influencer.redesSociais, influencer.precosSnapshot)
      : influencer.socialPlatforms?.map((platform: any) => ({
          ...platform,
          pricing: platform.pricing || {
            post: 0,
            story: 0,
            reel: 0,
            video: 0
          }
        })) || [];

    const result: any = {
      ...basicData,
      socialPlatforms: completeSocialPlatforms,
      
      financials: config.includeFinancials ? {
        totalEarnings: influencer.financials?.totalEarnings || 0,
        avgProjectValue: influencer.financials?.avgProjectValue || 0,
        paymentTerms: influencer.financials?.paymentTerms || '',
        preferredCurrency: influencer.financials?.preferredCurrency || 'BRL',
        taxInfo: influencer.financials?.taxInfo
      } : undefined,
      
      negotiationHistory: config.includeNegotiationHistory 
        ? influencer.negotiationHistory || []
        : undefined,
      
      contactInfo: config.includeContactInfo ? {
        email: influencer.email || '',
        phone: influencer.phone,
        manager: influencer.manager
      } : undefined,
      
      snapshotVersion: '1.0.0-complete',

      // 🔥 PARA VERSÃO COMPLETA, PRESERVAR TODAS AS SUBCOLEÇÕES E DADOS ORIGINAIS
      ...(influencer.currentPricing && { currentPricing: influencer.currentPricing }),
      ...(influencer.currentDemographics && { currentDemographics: influencer.currentDemographics }),
      ...(influencer.currentBudgets && { currentBudgets: influencer.currentBudgets }),
      
      // 🔥 PRESERVAR TODOS OS DADOS ESTRUTURAIS ORIGINAIS (EXCETO CAMPOS ADMINISTRATIVOS)
      ...Object.keys(influencer).reduce((acc: any, key: string) => {
        // ❌ EXCLUIR campos administrativos e campos já processados explicitamente
        const excludedFields = [
          'id', 'name', 'avatar', 'bio', 'followers', 'engagement', 'tier', 'category', 'location', 'verified', 
          'socialPlatforms', 'portfolio', 'stats', 'capturedAt', 'snapshotVersion',
          // ❌ CAMPOS ADMINISTRATIVOS EXCLUÍDOS DO SNAPSHOT:
          'email', 'whatsapp', 'responsibleName', 'agencyName', 'responsibleCapturer', 'promotesTraders', 'createdAt'
        ];
        
        if (!excludedFields.includes(key)) {
          acc[key] = influencer[key];
        }
        return acc;
      }, {})
    };

    return result;
  }

  /**
   * 🔄 CONVERTER REDES SOCIAIS ANTIGAS PARA FORMATO BÁSICO
   */
  private static convertSocialPlatformsToBasic(redesSociais: any): SocialPlatformBasicSnapshot[] {
    const platforms: SocialPlatformBasicSnapshot[] = [];
    
    if (redesSociais.instagram) {
      platforms.push({
        platform: 'instagram',
        username: redesSociais.instagram.username,
        url: `https://instagram.com/${redesSociais.instagram.username}`,
        followers: redesSociais.instagram.seguidores,
        engagement: redesSociais.instagram.engajamento,
        verified: redesSociais.instagram.verificado,
        isActive: true
      });
    }
    
    if (redesSociais.youtube) {
      platforms.push({
        platform: 'youtube',
        username: redesSociais.youtube.username,
        url: `https://youtube.com/@${redesSociais.youtube.username}`,
        followers: redesSociais.youtube.seguidores,
        engagement: redesSociais.youtube.visualizacoes || 0,
        verified: redesSociais.youtube.verificado,
        isActive: true
      });
    }
    
    if (redesSociais.tiktok) {
      platforms.push({
        platform: 'tiktok',
        username: redesSociais.tiktok.username,
        url: `https://tiktok.com/@${redesSociais.tiktok.username}`,
        followers: redesSociais.tiktok.seguidores,
        engagement: redesSociais.tiktok.curtidas || 0,
        verified: redesSociais.tiktok.verificado,
        isActive: true
      });
    }
    
    return platforms;
  }

  /**
   * 🔄 CONVERTER REDES SOCIAIS ANTIGAS PARA FORMATO COMPLETO (com pricing)
   */
  private static convertSocialPlatformsToComplete(
    redesSociais: any, 
    precosSnapshot?: any
  ): SocialPlatformCompleteSnapshot[] {
    
    const basicPlatforms = this.convertSocialPlatformsToBasic(redesSociais);
    
    return basicPlatforms.map(platform => ({
      ...platform,
      pricing: this.extractPricingForPlatform(platform.platform, precosSnapshot),
      historicalRates: [], // TODO: Implementar histórico
      negotiatedRates: []  // TODO: Implementar taxas negociadas
    }));
  }

  /**
   * 💰 EXTRAIR PRICING ESPECÍFICO DA PLATAFORMA
   */
  private static extractPricingForPlatform(platform: string, precosSnapshot: any): any {
    if (!precosSnapshot) {
      return { post: 0, story: 0, reel: 0, video: 0 };
    }
    
    switch (platform) {
      case 'instagram':
        return {
          post: 0, // TODO: Mapear do precosSnapshot
          story: precosSnapshot.instagramStory?.price || 0,
          reel: precosSnapshot.instagramReel?.price || 0
        };
      case 'youtube':
        return {
          video: precosSnapshot.youtubeDedicated?.price || 0,
          shorts: precosSnapshot.youtubeShorts?.price || 0
        };
      case 'tiktok':
        return {
          video: precosSnapshot.tiktokVideo?.price || 0
        };
      default:
        return { post: 0, story: 0, reel: 0, video: 0 };
    }
  }

  /**
   * 🔍 VERIFICAR SE USUÁRIO TEM ACESSO AOS DADOS COMPLETOS
   */
  static canAccessCompleteData(userRole: string): boolean {
    return ['super_admin', 'admin', 'manager'].includes(userRole);
  }

  /**
   * 📋 LISTAR CAMPOS FILTRADOS PARA UM ROLE (método legado - manter compatibilidade)
   */
  static getFilteredFieldsForRole(userRole: string): string[] {
    return this.getFilteredFieldsForAccess(userRole, false);
  }

  /**
   * 🎯 DETERMINAR CAMPOS FILTRADOS COM BASE NO NÍVEL DE ACESSO
   */
  private static getFilteredFieldsForAccess(userRole: string, includeBasicPricing: boolean): string[] {
    if (userRole === 'user') {
      const baseFiltered = [
        // ❌ CAMPOS ADMINISTRATIVOS (sempre filtrados)
        'email',
        'whatsapp', 
        'responsibleName',
        'agencyName',
        'responsibleCapturer',
        'promotesTraders',
        'createdAt',
        // ❌ CAMPOS FINANCEIROS E SENSÍVEIS
        'financials', 
        'contactInfo',
        'negotiationHistory',
        'totalAmount',
        'paymentTerms',
        'invoiceData',
        'historicalRates',
        'negotiatedRates',
        'taxInfo',
        'managerContact'
      ];
      
      // Se incluir pricing básico (colaborador editor), não filtrar pricing completamente
      if (!includeBasicPricing) {
        baseFiltered.push('pricing');
      }
      
      return baseFiltered;
    }
    return [];
  }
} 

