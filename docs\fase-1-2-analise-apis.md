# 🔗 FASE 1.2: Análise Completa de APIs

## 🎯 Resultado da Análise

### ❌ APIs SEM Isolamento por Usuário (Requer Implementação)

| API | Método | Status | Prioridade | Observações |
|-----|--------|--------|------------|-------------|
| `/api/brands` | GET, POST | ❌ Sem filtro | 🔴 Alta | Chama `getAllBrands()` sem userId |
| `/api/campaigns` | GET, POST, PUT, DELETE | ❌ Sem filtro | 🔴 Alta | Busca todas campanhas sem filtro |
| `/api/influencers` | GET, POST, PUT, DELETE | ❌ Sem filtro | 🔴 Alta | `getAllInfluencers()` sem filtro |
| `/api/marcas/influencers` | GET, POST, PUT, DELETE | ❌ Sem filtro | 🔴 Alta | Relacionamento brands-influencers |
| `/api/notes` | GET, POST, PUT, DELETE | ❌ Sem filtro | 🟡 Média | Filtra apenas por influencerId |
| `/api/tags` | GET, POST, PUT, DELETE | ❌ Sem filtro | 🟡 Baixa | Sistema de etiquetas |
| `/api/filters` | GET, POST, DELETE | ❌ Sem filtro | 🟡 Média | Comentário indica futura implementação |

### ⚠️ APIs Parcialmente Isoladas

| API | Método | Status | Campo Atual | Recomendação |
|-----|--------|--------|-------------|--------------|
| `/api/groups` | GET, POST, PUT, DELETE | ⚠️ Parcial | `createdBy` | Padronizar para `userId` |
| `/api/proposals` | GET, POST | ⚠️ Usa Service | `ProposalService` | Verificar implementação do service |

### ✅ APIs Corretamente Isoladas

| API | Método | Status | Campo Isolamento | Observações |
|-----|--------|--------|------------------|-------------|
| `/api/auth/*` | Vários | ✅ Isolado | Middleware auth | Sistema de autenticação robusto |
| `/api/admin/*` | Vários | ✅ Isolado | Middleware auth | Proteção por roles |

## 📋 Detalhamento por API

### 🔴 Prioridade Alta - Implementação Urgente

#### `/api/brands` 
```typescript
// ❌ ATUAL - Sem filtro
export async function GET() {
  const brands = await getAllBrands(); // Busca todas as brands
  return NextResponse.json({ brands });
}

// ✅ NECESSÁRIO - Com filtro userId
export async function GET(request: NextRequest) {
  const userId = await getUserIdFromAuth(request);
  const brands = await getAllBrands(userId); // Filtrar por userId
  return NextResponse.json({ brands });
}
```

#### `/api/campaigns`
```typescript
// ❌ ATUAL - Sem filtro por usuário
let campaigns = await getAllCampaigns(); // Busca todas

// ✅ NECESSÁRIO - Com filtro userId  
let campaigns = await getAllCampaigns(userId); // Filtrar por userId
```

#### `/api/influencers`
```typescript
// ❌ ATUAL - Sem filtro por usuário
const influencers = await getAllInfluencers(); // Busca todos

// ✅ NECESSÁRIO - Com filtro userId
const influencers = await getAllInfluencers(userId); // Filtrar por userId
```

### 🟡 Prioridade Média - Melhorias Necessárias

#### `/api/groups`
```typescript
// ⚠️ ATUAL - Parcialmente correto
if (createdBy) {
  query = query.where('createdBy', '==', createdBy);
}

// ✅ MELHORADO - Padronização
const userId = await getUserIdFromAuth(request);
query = query.where('userId', '==', userId);
```

#### `/api/filters`
```typescript
// ❌ ATUAL - Comentário sobre futuro
// Para uma implementação com usuários, poderíamos passar o ID do usuário aqui
const filters = await getAllFilters();

// ✅ NECESSÁRIO - Implementar filtro
const userId = await getUserIdFromAuth(request);
const filters = await getAllFilters(userId);
```

## 🛠️ Implementações Necessárias

### 1. Middleware de Autenticação para APIs

```typescript
// lib/api-auth-middleware.ts
export async function getUserIdFromAuth(request: NextRequest): Promise<string> {
  const authData = await verifyAuth(request);
  if (!authData) {
    throw new Error('Usuário não autenticado');
  }
  return authData.user.id;
}

export function withUserAuth(
  handler: (req: NextRequest, userId: string) => Promise<Response>
) {
  return async (req: NextRequest) => {
    try {
      const userId = await getUserIdFromAuth(req);
      return await handler(req, userId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Não autenticado' },
        { status: 401 }
      );
    }
  };
}
```

### 2. Atualização das Funções de Banco

```typescript
// lib/firebase.ts - Atualizações necessárias

// ❌ ATUAL
export async function getAllBrands() {
  const snapshot = await brandsCollection.get();
  // ...
}

// ✅ NOVO  
export async function getAllBrands(userId: string) {
  const snapshot = await brandsCollection.where('userId', '==', userId).get();
  // ...
}

// ❌ ATUAL
export async function getAllCampaigns() {
  const snapshot = await campaignsCollection.get();
  // ...
}

// ✅ NOVO
export async function getAllCampaigns(userId: string) {
  const snapshot = await campaignsCollection.where('userId', '==', userId).get();
  // ...
}
```

### 3. Padrão de Implementação

```typescript
// Padrão para todas as APIs que precisam de isolamento
export const GET = withUserAuth(async (request: NextRequest, userId: string) => {
  try {
    // Buscar dados filtrados por userId
    const data = await getDataByUserId(userId);
    return NextResponse.json({ data });
  } catch (error) {
    return NextResponse.json({ error: 'Erro interno' }, { status: 500 });
  }
});

export const POST = withUserAuth(async (request: NextRequest, userId: string) => {
  try {
    const body = await request.json();
    // Garantir que userId seja adicionado aos dados
    const dataWithUserId = { ...body, userId };
    const result = await createData(dataWithUserId);
    return NextResponse.json({ result });
  } catch (error) {
    return NextResponse.json({ error: 'Erro interno' }, { status: 500 });
  }
});
```

## 📊 Estatísticas da Análise

- **Total de APIs Analisadas**: 12
- **APIs Sem Isolamento**: 7 (58%)
- **APIs Parcialmente Isoladas**: 2 (17%)
- **APIs Corretamente Isoladas**: 3 (25%)

## 🚨 Riscos Identificados

### Alto Risco:
- **Vazamento de dados**: APIs retornam dados de todos os usuários
- **Operações em dados incorretos**: Usuário pode modificar dados de outros
- **Violação de LGPD**: Exposição de dados pessoais

### Médio Risco:
- **Performance**: Queries sem filtro podem ser lentas
- **Experiência do usuário**: Dados desnecessários na interface

## ⚡ Plano de Implementação

### Fase 1: Implementar Middleware
1. Criar `getUserIdFromAuth()`
2. Criar `withUserAuth()` wrapper
3. Testar com uma API simples

### Fase 2: Atualizar APIs Críticas (Ordem de Prioridade)
1. `/api/brands` - Base para outras funcionalidades
2. `/api/influencers` - Dados sensíveis
3. `/api/campaigns` - Operações críticas
4. `/api/marcas/influencers` - Relacionamentos

### Fase 3: Atualizar APIs Auxiliares
1. `/api/groups`
2. `/api/notes`
3. `/api/filters`
4. `/api/tags`

### Fase 4: Testes e Validação
1. Testes de isolamento
2. Testes de performance
3. Validação de segurança

---

✅ **Análise da Fase 1.2 Concluída** 