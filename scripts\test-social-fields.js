const admin = require('firebase-admin');

// Configurar Firebase Admin se ainda não foi configurado
if (!admin.apps.length) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

// Importar o SnapshotFilterService para testar
const path = require('path');
const fs = require('fs');

// Mock do import do TypeScript
const SnapshotFilterService = {
  filterSnapshotByRole: (snapshot, userRole, config) => {
    // Simular o comportamento do filtro
    console.log('🔍 [TEST] Aplicando filtro com config:', { userRole, config });
    
    // Retornar um snapshot mockado para teste
    return {
      influencersSnapshot: snapshot.influencersSnapshot.map(inf => ({
        id: inf.id,
        name: inf.name,
        // Simular preservação dos campos de redes sociais
        instagramFollowers: inf.instagramFollowers,
        facebookFollowers: inf.facebookFollowers,
        tiktokFollowers: inf.tiktokFollowers,
        youtubeFollowers: inf.youtubeFollowers,
        currentDemographics: inf.currentDemographics,
        currentBudgets: inf.currentBudgets
      }))
    };
  }
};

async function testSocialFields() {
  try {
    console.log('🧪 [TEST] Testando campos específicos das redes sociais...');
    
    const db = admin.firestore();
    const proposalId = 'Xd8ntVkssHRlqnvISxiz';
    
    // 1. Buscar dados reais do snapshot
    const proposalSharingsQuery = db.collection('proposal_sharings')
      .where('proposalId', '==', proposalId);
    
    const sharingsSnapshot = await proposalSharingsQuery.get();
    
    if (sharingsSnapshot.empty) {
      console.log('❌ [TEST] Nenhum sharing encontrado');
      return;
    }
    
    const allInfluencers = [];
    
    for (const sharingDoc of sharingsSnapshot.docs) {
      const snapshotsCollection = sharingDoc.ref.collection('snapshots');
      const snapshotsSnapshot = await snapshotsCollection.get();
      
      for (const snapshotDoc of snapshotsSnapshot.docs) {
        const influencerId = snapshotDoc.id;
        const mainData = snapshotDoc.data();
        
        // Buscar subcoleções
        const demographicsCollection = snapshotDoc.ref.collection('demographics');
        const demographicsSnapshot = await demographicsCollection.get();
        
        const budgetsCollection = snapshotDoc.ref.collection('budgets');
        const budgetsSnapshot = await budgetsCollection.get();
        
        const currentDemographics = [];
        demographicsSnapshot.forEach(doc => {
          currentDemographics.push({ id: doc.id, ...doc.data() });
        });
        
        const currentBudgets = [];
        budgetsSnapshot.forEach(doc => {
          currentBudgets.push({ id: doc.id, ...doc.data() });
        });
        
        // Montar influenciador como na API
        const influencerSnapshot = {
          id: influencerId,
          originalInfluencerId: mainData.originalInfluencerId || influencerId,
          ...mainData,
          currentDemographics,
          currentBudgets
        };
        
        allInfluencers.push(influencerSnapshot);
        
        console.log(`\n👤 [TEST] Dados RAW do influenciador:`, {
          id: influencerId,
          name: mainData.name,
          
          // Verificar campos específicos das redes sociais
          hasInstagramFollowers: !!mainData.instagramFollowers,
          instagramFollowers: mainData.instagramFollowers,
          
          hasFacebookFollowers: !!mainData.facebookFollowers,
          facebookFollowers: mainData.facebookFollowers,
          
          hasTiktokFollowers: !!mainData.tiktokFollowers,
          tiktokFollowers: mainData.tiktokFollowers,
          
          hasYoutubeFollowers: !!mainData.youtubeFollowers,
          youtubeFollowers: mainData.youtubeFollowers,
          
          // Verificar campos de engagement
          instagramEngagementRate: mainData.instagramEngagementRate,
          facebookEngagementRate: mainData.facebookEngagementRate,
          
          // Verificar outros campos
          totalFollowers: mainData.totalFollowers,
          engagementRate: mainData.engagementRate
        });
      }
    }
    
    // 2. Simular o processo de filtragem como na API
    console.log(`\n🔍 [TEST] Simulando filtragem como na API real...`);
    
    const proposalSnapshot = {
      id: proposalId,
      nome: 'Snapshot da Proposta',
      influencersSnapshot: allInfluencers,
      services: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Simular diferentes tipos de usuário
    const userTypes = [
      { userRole: 'user', collaboratorRole: 'viewer', isCollaborator: true },
      { userRole: 'user', collaboratorRole: 'editor', isCollaborator: true },
      { userRole: 'admin', collaboratorRole: 'owner', isCollaborator: true }
    ];
    
    userTypes.forEach(({ userRole, collaboratorRole, isCollaborator }) => {
      console.log(`\n🧪 [TEST] Testando filtro para: ${userRole}/${collaboratorRole}`);
      
      const filteredSnapshot = SnapshotFilterService.filterSnapshotByRole(
        proposalSnapshot,
        userRole,
        { collaboratorRole, isCollaborator }
      );
      
      const firstInfluencer = filteredSnapshot.influencersSnapshot[0];
      
      console.log(`📊 [TEST] Campos preservados:`, {
        hasInstagramFollowers: !!firstInfluencer.instagramFollowers,
        hasFacebookFollowers: !!firstInfluencer.facebookFollowers,
        hasTiktokFollowers: !!firstInfluencer.tiktokFollowers,
        hasYoutubeFollowers: !!firstInfluencer.youtubeFollowers,
        hasDemographics: !!firstInfluencer.currentDemographics,
        hasBudgets: !!firstInfluencer.currentBudgets
      });
    });
    
    // 3. RESULTADO FINAL
    console.log(`\n🎯 [TEST RESULTADO FINAL]:`);
    
    if (allInfluencers.length > 0) {
      const firstInfluencer = allInfluencers[0];
      
      const socialFieldsPresent = {
        instagram: {
          followers: !!firstInfluencer.instagramFollowers,
          engagement: !!firstInfluencer.instagramEngagementRate,
          username: !!firstInfluencer.instagramUsername
        },
        facebook: {
          followers: !!firstInfluencer.facebookFollowers,
          engagement: !!firstInfluencer.facebookEngagementRate,
          username: !!firstInfluencer.facebookUsername
        },
        tiktok: {
          followers: !!firstInfluencer.tiktokFollowers,
          engagement: !!firstInfluencer.tiktokEngagementRate,
          username: !!firstInfluencer.tiktokUsername
        },
        youtube: {
          followers: !!firstInfluencer.youtubeFollowers,
          engagement: !!firstInfluencer.youtubeEngagementRate,
          username: !!firstInfluencer.youtubeUsername
        }
      };
      
      console.log('📋 [TEST] Campos das redes sociais disponíveis:', socialFieldsPresent);
      
      const totalSocialFields = Object.values(socialFieldsPresent).reduce((total, platform) => {
        return total + Object.values(platform).filter(Boolean).length;
      }, 0);
      
      console.log(`✅ [TEST] Total de campos de redes sociais encontrados: ${totalSocialFields}`);
      
      if (totalSocialFields > 0) {
        console.log(`🎉 [TEST] SUCESSO! Os campos das redes sociais estão sendo salvos no snapshot`);
      } else {
        console.log(`❌ [TEST] PROBLEMA! Nenhum campo de rede social foi encontrado`);
      }
    }
    
  } catch (error) {
    console.error('❌ [TEST] Erro:', error);
  }
}

testSocialFields(); 
