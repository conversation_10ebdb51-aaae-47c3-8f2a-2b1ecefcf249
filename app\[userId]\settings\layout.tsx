'use client';

import { useState, useEffect } from 'react';
import { useParams, usePathname } from 'next/navigation';
import Link from 'next/link';
import { Protect } from '@clerk/nextjs';
import { useAuth } from '@/hooks/use-auth-v2';
import { useSidebar } from '@/contexts/sidebar-context';
import { isAdmin } from '@/lib/security-config';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Loader } from '@/components/ui/loader';
import { 
  Settings, 
  Building2, 
  User, 
  Bell, 
  Shield, 
  CreditCard,
  Key,
  Palette,
  Mail,
  HelpCircle,
  Menu,
  Tag
} from 'lucide-react';

interface SettingsMenuItem {
  icon: React.ReactNode;
  label: string;
  href: string;
  description: string;
  adminOnly?: boolean;
}

export default function SettingsLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const pathname = usePathname();
  const { currentUser, isLoading, isInitialized } = useAuth();
  const { collapseSidebar } = useSidebar();
  
  const userId = params?.userId as string;
  const isOwnProfile = currentUser?.id === userId;
  
  // Aguardar inicialização antes de verificar acesso
  if (!isInitialized || isLoading) {
    return <Loader isLoading={true} message="" showLogo={true} />;
  }

  // Verificar acesso após inicialização
  if (!isOwnProfile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            Você não tem permissão para acessar estas configurações.
          </p>
        </div>
      </div>
    );
  }

  const settingsMenuItems: SettingsMenuItem[] = [
    /*{
      icon: <User className="h-5 w-5" />,
      label: 'Perfil',
      href: `/${userId}/settings`,
      description: 'Gerenciar informações pessoais'
    },*/
    {
      icon: <Building2 className="h-5 w-5" />,
      label: 'Marcas',
      href: `/${userId}/settings/brands`,
      description: 'Criar e gerenciar suas marcas',
      adminOnly: true
    },
    {
      icon: <Tag className="h-5 w-5" />,
      label: 'Categorias',
      href: `/${userId}/settings/categories`,
      description: 'Organizar influenciadores por categorias',
      adminOnly: true
    }
  ];

  const isActiveRoute = (href: string) => {
    if (!pathname) return false;
    if (href === `/${userId}/settings`) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-[#080210] overflow-hidden">
      {/* Settings Sidebar - Fixo e visível */}
      <div className="w-80 bg-white dark:bg-[#080210] border-r border flex flex-col">
        {/* Header do Sidebar */}
        <div className="p-6 border-b border">
          <div className="flex items-center gap-3 mb-2">
            <Settings className="h-6 w-6 text-[#ff0074]" />
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">Configurações</h1>
          </div>
        </div>

        {/* Menu de Navegação */}
        <nav className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-1">
            {settingsMenuItems.map((item) => (
              item.adminOnly ? (
                <Protect key={item.href} role="org:admin">
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-start gap-3 px-4 py-3 rounded-lg transition-all duration-200 group",
                      isActiveRoute(item.href) 
                        ? "bg-[#ff0074] text-white shadow-md" 
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    )}
                  >
                    <div className={cn(
                      "mt-0.5 transition-colors flex-shrink-0",
                      isActiveRoute(item.href) ? "text-white" : "text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"
                    )}>
                      {item.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={cn(
                        "font-medium text-xs",
                        isActiveRoute(item.href) ? "text-white" : "text-gray-900 dark:text-white"
                      )}>
                        {item.label}
                      </p>
                      <p className={cn(
                        "text-xs transition-colors",
                        isActiveRoute(item.href) 
                          ? "text-white/80" 
                          : "text-gray-500 dark:text-gray-400"
                      )}>
                        {item.description}
                      </p>
                    </div>
                  </Link>
                </Protect>
              ) : (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-start gap-3 px-4 py-3 rounded-lg transition-all duration-200 group",
                    isActiveRoute(item.href) 
                      ? "bg-[#ff0074] text-white shadow-md" 
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  )}
                >
                  <div className={cn(
                    "mt-0.5 transition-colors flex-shrink-0",
                    isActiveRoute(item.href) ? "text-white" : "text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"
                  )}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={cn(
                      "font-medium text-xs",
                      isActiveRoute(item.href) ? "text-white" : "text-gray-900 dark:text-white"
                    )}>
                      {item.label}
                    </p>
                    <p className={cn(
                      "text-xs transition-colors",
                      isActiveRoute(item.href) 
                        ? "text-white/80" 
                        : "text-gray-500 dark:text-gray-400"
                    )}>
                      {item.description}
                    </p>
                  </div>
                </Link>
              )
            ))}
          </div>

          {/* Divider */}
          <div className="my-6 border-t border" />

          {/* Help Section */}
          <div className="space-y-1">
            <Link
              href="/help"
              className="flex items-start gap-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 group transition-all duration-200"
            >
              <HelpCircle className="h-5 w-5 mt-0.5 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300 flex-shrink-0" />
              <div>
                <p className="font-medium text-xs text-gray-900 dark:text-white">Ajuda & Suporte</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Documentação e contato
                </p>
              </div>
            </Link>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 bg-gray-50 dark:bg-[#080210] overflow-auto">
        <div className="h-full">
          {children}
        </div>
      </div>
    </div>
  );
} 