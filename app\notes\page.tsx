"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Note } from '@/types/influencer';
import { 
  Search, 
  Filter, 
  Calendar, 
  MessageSquare, 
  User, 
  Edit, 
  Trash, 
  ChevronDown,
  X,
  Check,
  SlidersHorizontal,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

type NoteWithInfluencer = Note & { influencerName?: string };

export default function NotesPage() {
  const router = useRouter();
  const [allNotes, setAllNotes] = useState<NoteWithInfluencer[]>([]);
  const [filteredNotes, setFilteredNotes] = useState<NoteWithInfluencer[]>([]);
  const [displayedNotes, setDisplayedNotes] = useState<NoteWithInfluencer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [influencerFilter, setInfluencerFilter] = useState<string>('');
  const [influencers, setInfluencers] = useState<{id: string, name: string}[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  
  // Paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const notesPerPage = 12; // Número de notas por página
  
  // Buscar todas as anotações
  useEffect(() => {
    const fetchAllNotes = async () => {
      setIsLoading(true);
      try {
        // Primeiro, buscar todos os influenciadores para ter os nomes
        const influencersResponse = await fetch('/api/influencers');
        if (influencersResponse.ok) {
          const influencersData = await influencersResponse.json();
          const influencersList = influencersData.map((inf: { id: string; name: string }) => ({
            id: inf.id,
            name: inf.name
          }));
          setInfluencers(influencersList);
          
          // Agora buscar todas as anotações
          const notesResponse = await fetch('/api/notes?all=true');
          if (notesResponse.ok) {
            const notesData = await notesResponse.json();
            
            // Adicionar o nome do influenciador a cada anotação
            const notesWithInfluencerName = notesData.map((note: Note) => {
              const influencer = influencersList.find((inf: {id: string; name: string}) => inf.id === note.influencerId);
              return {
                ...note,
                influencerName: influencer?.name || 'Influenciador desconhecido'
              };
            });
            
            setAllNotes(notesWithInfluencerName);
          } else {
            console.error('Erro ao buscar anotações');
          }
        } else {
          console.error('Erro ao buscar influenciadores');
        }
      } catch (error) {
        console.error('Erro ao buscar dados:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAllNotes();
  }, []);
  
  // Função para excluir uma anotação
  const handleDeleteNote = async (noteId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta anotação?')) return;
    
    try {
      const response = await fetch(`/api/notes?id=${noteId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        // Remover a anotação da lista
        setAllNotes(allNotes.filter((note: NoteWithInfluencer) => note.id !== noteId));
      } else {
        console.error('Erro ao excluir anotação');
      }
    } catch (error) {
      console.error('Erro ao excluir anotação:', error);
    }
  };
  
  // Função para formatar data
  const formatDate = (date: Date | string | number | null | undefined) => {
    if (!date) return 'Data desconhecida';
    
    try {
      return format(new Date(date), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return 'Data inválida';
    }
  };
  
  // Função para obter cor baseada no tipo de anotação
  const getNoteTypeColor = (type: string = 'geral') => {
    const typeColors: Record<string, string> = {
      'geral': '#3B82F6',
      'contrato': '#10B981',
      'negociacao': '#F59E0B',
      'problema': '#EF4444',
      'feedback': '#EC4899'
    };
    
    return typeColors[type] || typeColors['geral'];
  };
  
  // Efeito para filtrar as notas e aplicar paginação
  useEffect(() => {
    // Filtrar anotações
    const filtered = allNotes.filter((note: NoteWithInfluencer) => {
      // Filtro de busca
      const searchMatch = searchTerm === '' || 
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (note.influencerName && note.influencerName.toLowerCase().includes(searchTerm.toLowerCase()));
      
      // Filtro de tipo
      const typeMatch = typeFilter === '' || note.type === typeFilter;
      
      // Filtro de influenciador
      const influencerMatch = influencerFilter === '' || note.influencerId === influencerFilter;
      
      // Filtro de data
      let dateMatch = true;
      if (dateFilter === 'hoje') {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const noteDate = new Date(note.createdAt);
        dateMatch = noteDate >= today;
      } else if (dateFilter === 'semana') {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const noteDate = new Date(note.createdAt);
        dateMatch = noteDate >= weekAgo;
      } else if (dateFilter === 'mes') {
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        const noteDate = new Date(note.createdAt);
        dateMatch = noteDate >= monthAgo;
      }
      
      return searchMatch && typeMatch && influencerMatch && dateMatch;
    });
    
    // Atualizar as notas filtradas
    setFilteredNotes(filtered);
    
    // Calcular o número total de páginas
    const total = Math.ceil(filtered.length / notesPerPage);
    setTotalPages(total || 1);
    
    // Garantir que a página atual é válida
    if (currentPage > total && total > 0) {
      setCurrentPage(1);
    }
    
    // Aplicar paginação
    const startIndex = (currentPage - 1) * notesPerPage;
    const endIndex = startIndex + notesPerPage;
    setDisplayedNotes(filtered.slice(startIndex, endIndex));
  }, [allNotes, searchTerm, typeFilter, dateFilter, influencerFilter, currentPage, notesPerPage]);
  
  // Função para mudar de página
  const changePage = (page: number) => {
    setCurrentPage(page);
  };
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Anotações</h1>
        <Link href="/" className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm">
          Voltar para Dashboard
        </Link>
      </div>
      
      <div className="mb-6">
        {/* Barra de busca e filtros */}
        <div className="flex flex-wrap gap-3 mb-4">
          <div className="flex-1 min-w-[300px] relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar anotações..."
              className="pl-10 pr-4 py-2 w-full border rounded-md"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <button
            type="button"
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm flex items-center gap-1"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal size={16} />
            Filtros
            <ChevronDown size={16} className={`transform transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>
        
        {/* Painel de filtros */}
        {showFilters && (
          <div className="p-4 bg-gray-50 rounded-lg mb-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Tipo</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option value="">Todos os tipos</option>
                  <option value="geral">Geral</option>
                  <option value="contrato">Contrato</option>
                  <option value="negociacao">Negociação</option>
                  <option value="problema">Problema</option>
                  <option value="feedback">Feedback</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Período</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                >
                  <option value="">Qualquer data</option>
                  <option value="hoje">Hoje</option>
                  <option value="semana">Última semana</option>
                  <option value="mes">Último mês</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Influenciador</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={influencerFilter}
                  onChange={(e) => setInfluencerFilter(e.target.value)}
                >
                  <option value="">Todos os influenciadores</option>
                  {influencers.map(inf => (
                    <option key={inf.id} value={inf.id}>{inf.name}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <button
                type="button"
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-sm flex items-center gap-1"
                onClick={() => {
                  setTypeFilter('');
                  setDateFilter('');
                  setInfluencerFilter('');
                }}
              >
                <X size={16} />
                Limpar filtros
              </button>
              <button
                type="button"
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm flex items-center gap-1"
                onClick={() => setShowFilters(false)}
              >
                <Check size={16} />
                Aplicar
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* Lista de anotações */}
      <div className="space-y-6">
        {isLoading ? (
          <div className="text-center py-12">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
            <p className="mt-2 text-gray-600">Carregando anotações...</p>
          </div>
        ) : filteredNotes.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-gray-600">Nenhuma anotação encontrada</p>
            {searchTerm || typeFilter || dateFilter || influencerFilter ? (
              <button
                className="mt-4 px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-sm"
                onClick={() => {
                  setSearchTerm('');
                  setTypeFilter('');
                  setDateFilter('');
                  setInfluencerFilter('');
                }}
              >
                Limpar filtros
              </button>
            ) : null}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {displayedNotes.map((note: NoteWithInfluencer) => {
                const typeColor = getNoteTypeColor(note.type);
                
                return (
                  <div 
                    key={note.id}
                    className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div 
                      className="flex justify-between items-center p-3 border-b"
                      style={{ borderLeftWidth: '4px', borderLeftColor: typeColor }}
                    >
                      <div className="flex items-center gap-2">
                        <MessageSquare size={16} style={{ color: typeColor }} />
                        <h4 className="font-medium truncate">{note.title}</h4>
                        <span 
                          className="text-xs px-2 py-0.5 rounded-full"
                          style={{ backgroundColor: `${typeColor}20`, color: typeColor }}
                        >
                          {note.type || 'geral'}
                        </span>
                      </div>
                      
                      <div className="flex gap-1">
                        <Link
                          href={`/notes/edit/${note.id}`}
                          className="p-1 rounded-full hover:bg-gray-100"
                        >
                          <Edit size={16} />
                        </Link>
                        <button
                          type="button"
                          className="p-1 rounded-full hover:bg-gray-100"
                          onClick={() => handleDeleteNote(note.id)}
                        >
                          <Trash size={16} />
                        </button>
                      </div>
                    </div>
                    
                    <div className="p-4 whitespace-pre-wrap text-sm max-h-32 overflow-y-auto">
                      {note.content}
                    </div>
                    
                    <div className="bg-gray-50 p-3 text-xs text-gray-500 flex flex-col gap-1">
                      <div className="flex items-center gap-1">
                        <User size={12} />
                        <Link 
                          href={`/?influencerId=${note.influencerId}`}
                          className="text-blue-600 hover:underline"
                        >
                          {note.influencerName}
                        </Link>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar size={12} />
                        {formatDate(note.createdAt)}
                        {note.updatedAt && note.createdAt && 
                         typeof note.updatedAt.getTime === 'function' && 
                         typeof note.createdAt.getTime === 'function' && 
                         note.updatedAt.getTime() !== note.createdAt.getTime() && (
                          <span className="ml-2">
                            (Editado em {formatDate(note.updatedAt)})
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            
            {/* Controles de paginação */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8 gap-2">
                <button
                  onClick={() => changePage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="p-2 border rounded hover:bg-gray-100 disabled:opacity-50"
                >
                  <ChevronLeft size={16} />
                </button>
                
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => changePage(page)}
                    className={`w-10 h-10 rounded-full ${currentPage === page ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'}`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => changePage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="p-2 border rounded hover:bg-gray-100 disabled:opacity-50"
                >
                  <ChevronRight size={16} />
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}


