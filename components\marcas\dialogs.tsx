import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CheckSquare, Calendar, DollarSign, Users, X, Instagram, Youtube, Music, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Influencer {
  id: string;
  nome: string;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
}

interface Service {
  id: string;
  name: string;
  description: string;
  platform: string;
}

interface ProposalService {
  serviceId: string;
  quantity: number;
  customPrice?: number;
  notes?: string;
}

interface CreateGroupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedInfluencers: string[];
  influencers: Influencer[];
  groupName: string;
  setGroupName: (name: string) => void;
  groupDescription: string;
  setGroupDescription: (description: string) => void;
  onCreateGroup: () => void;
  loading?: boolean;
}

interface CreateProposalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedInfluencers: string[];
  influencers: Influencer[];
  proposalInfluencer: Influencer | null;
  groupName: string;
  setGroupName: (name: string) => void;
  groupDescription: string;
  setGroupDescription: (description: string) => void;
  dataEnvio: string;
  setDataEnvio: (date: string) => void;
  availableServices: Service[];
  proposalServices: ProposalService[];
  selectedCurrency: string;
  setSelectedCurrency: (currency: 'BRL' | 'USD' | 'EUR') => void;
  onCreateProposal: () => void;
  addServiceToProposal: (serviceId: string) => void;
  removeServiceFromProposal: (serviceId: string) => void;
  updateServiceInProposal: (serviceId: string, updates: Partial<ProposalService>) => void;
  loading?: boolean;
}

function formatCurrency(value: number, currency: string = 'BRL'): string {
  if (currency === 'USD') {
    return `$${value.toLocaleString()}`;
  } else if (currency === 'EUR') {
    return `€${value.toLocaleString()}`;
  }
  return `R$ ${value.toLocaleString()}`;
}

function getServiceIcon(platform: string) {
  switch (platform.toLowerCase()) {
    case 'instagram':
      return <Instagram className="h-4 w-4" />;
    case 'youtube':
      return <Youtube className="h-4 w-4" />;
    case 'tiktok':
      return <Music className="h-4 w-4" />;
    default:
      return <Share2 className="h-4 w-4" />;
  }
}

export function CreateGroupDialog({
  open,
  onOpenChange,
  selectedInfluencers,
  influencers,
  groupName,
  setGroupName,
  groupDescription,
  setGroupDescription,
  onCreateGroup,
  loading = false
}: CreateGroupDialogProps) {
  const selectedInfluencerData = influencers.filter(inf => inf && inf.id && selectedInfluencers.includes(inf.id));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Criar Grupo de Influenciadores
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Influenciadores Selecionados */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Influenciadores Selecionados</Label>
              <Badge variant="secondary">
                {selectedInfluencers.length} selecionado{selectedInfluencers.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
              {selectedInfluencerData.map((influencer) => (
                <Card key={influencer.id} className="p-3">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={influencer.avatar} />
                      <AvatarFallback className="text-xs">
                        {influencer.nome.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">{influencer.nome}</p>
                      <p className="text-xs text-muted-foreground truncate">
                        {influencer.cidade}, {influencer.estado}
                      </p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
          
          <Separator />
          
          {/* Informações do Grupo */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="groupName">Nome do Grupo *</Label>
              <Input
                id="groupName"
                placeholder="Ex: Campanha Verão 2024"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="groupDescription">Descrição do Grupo</Label>
              <Textarea
                id="groupDescription"
                placeholder="Descreva o objetivo deste grupo de influenciadores..."
                value={groupDescription}
                onChange={(e) => setGroupDescription(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={onCreateGroup}
            disabled={!groupName.trim() || selectedInfluencers.length === 0 || loading}
          >
            {loading ? 'Criando...' : 'Criar Grupo'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function CreateProposalDialog({
  open,
  onOpenChange,
  selectedInfluencers,
  influencers,
  proposalInfluencer,
  groupName,
  setGroupName,
  groupDescription,
  setGroupDescription,
  dataEnvio,
  setDataEnvio,
  availableServices,
  proposalServices,
  selectedCurrency,
  setSelectedCurrency,
  onCreateProposal,
  addServiceToProposal,
  removeServiceFromProposal,
  updateServiceInProposal,
  loading = false
}: CreateProposalDialogProps) {
  const selectedInfluencerData = influencers.filter(inf => inf && inf.id && selectedInfluencers.includes(inf.id));
  const totalAmount = proposalServices.reduce((total, service) => {
    const serviceData = availableServices.find(s => s.id === service.serviceId);
    if (!serviceData) return total;
    
    const basePrice = proposalInfluencer?.servicos?.[service.serviceId as keyof typeof proposalInfluencer.servicos] || 0;
    const finalPrice = service.customPrice || basePrice;
    return total + (finalPrice * service.quantity);
  }, 0);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Criar Proposta Comercial
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Influenciadores Selecionados */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Influenciadores</Label>
              <Badge variant="secondary">
                {selectedInfluencers.length} selecionado{selectedInfluencers.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-40 overflow-y-auto">
              {selectedInfluencerData.map((influencer) => (
                <Card key={influencer.id} className="p-3">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={influencer.avatar} />
                      <AvatarFallback className="text-xs">
                        {influencer.nome.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-xs truncate">{influencer.nome}</p>
                      <p className="text-xs text-muted-foreground truncate">
                        {influencer.categoria}
                      </p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
          
          <Separator />
          
          {/* Informações da Proposta */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="proposalGroupName">Nome da Proposta *</Label>
                <Input
                  id="proposalGroupName"
                  placeholder="Ex: Campanha Black Friday 2024"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="proposalDescription">Descrição da Campanha</Label>
                <Textarea
                  id="proposalDescription"
                  placeholder="Descreva os objetivos e detalhes da campanha..."
                  value={groupDescription}
                  onChange={(e) => setGroupDescription(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="dataEnvio">Data de Envio *</Label>
                <Input
                  id="dataEnvio"
                  type="date"
                  value={dataEnvio}
                  onChange={(e) => setDataEnvio(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Moeda</Label>
                <Select value={selectedCurrency} onValueChange={setSelectedCurrency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BRL">Real Brasileiro (BRL)</SelectItem>
                    <SelectItem value="USD">Dólar Americano (USD)</SelectItem>
                    <SelectItem value="EUR">Euro (EUR)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <Separator />
          
          {/* Seleção de Serviços */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-lg font-semibold">Serviços Disponíveis</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Selecione os serviços que deseja incluir na proposta
                </p>
              </div>
              <Badge variant="secondary">
                {proposalServices.length} selecionado{proposalServices.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {availableServices.map((service) => {
                const isSelected = proposalServices.some(ps => ps.serviceId === service.id);
                const selectedService = proposalServices.find(ps => ps.serviceId === service.id);
                const basePrice = proposalInfluencer?.servicos?.[service.id as keyof typeof proposalInfluencer.servicos] || 0;
                const finalPrice = selectedService?.customPrice || basePrice;
                const totalPrice = finalPrice * (selectedService?.quantity || 1);
                
                return (
                  <Card key={service.id} className={cn(
                    "transition-all duration-200",
                    isSelected ? "border-primary bg-primary/5" : "hover:border-primary/30"
                  )}>
                    <CardContent className="p-4">
                      <div 
                        className="flex items-center justify-between cursor-pointer"
                        onClick={() => {
                          if (isSelected) {
                            removeServiceFromProposal(service.id);
                          } else {
                            addServiceToProposal(service.id);
                          }
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "flex items-center justify-center w-8 h-8 rounded-lg",
                            isSelected ? "bg-primary text-primary-foreground" : "bg-muted"
                          )}>
                            {getServiceIcon(service.platform)}
                          </div>
                          <div>
                            <h5 className="font-medium text-sm">{service.name}</h5>
                            <p className="text-xs text-muted-foreground">{service.description}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div className="text-sm font-semibold">
                              {formatCurrency(isSelected ? totalPrice : basePrice, selectedCurrency)}
                            </div>
                            {isSelected && selectedService && selectedService.quantity > 1 && (
                              <div className="text-xs text-muted-foreground">
                                {selectedService.quantity}x {formatCurrency(finalPrice, selectedCurrency)}
                              </div>
                            )}
                          </div>
                          
                          <div className={cn(
                            "w-5 h-5 rounded border-2 flex items-center justify-center",
                            isSelected ? "bg-primary border-primary" : "border-muted-foreground/30"
                          )}>
                            {isSelected && <CheckSquare className="w-3 h-3 text-primary-foreground" />}
                          </div>
                        </div>
                      </div>
                      
                      {/* Configurações do Serviço */}
                      {isSelected && selectedService && (
                        <div className="mt-4 pt-4 border-t space-y-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-1">
                              <Label className="text-xs">Quantidade</Label>
                              <Input
                                type="number"
                                min="1"
                                max="10"
                                value={selectedService.quantity}
                                onChange={(e) => updateServiceInProposal(service.id, {
                                  quantity: Math.max(1, parseInt(e.target.value) || 1)
                                })}
                                className="h-8 text-sm"
                              />
                            </div>
                            
                            <div className="space-y-1">
                              <Label className="text-xs">Preço Personalizado</Label>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder={formatCurrency(basePrice, selectedCurrency)}
                                value={selectedService.customPrice || ''}
                                onChange={(e) => updateServiceInProposal(service.id, {
                                  customPrice: e.target.value ? parseFloat(e.target.value) : undefined
                                })}
                                className="h-8 text-sm"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
          
          {/* Resumo da Proposta */}
          {proposalServices.length > 0 && (
            <>
              <Separator />
              <Card className="bg-muted/50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold">Total da Proposta</h4>
                      <p className="text-sm text-muted-foreground">
                        {proposalServices.length} serviço{proposalServices.length !== 1 ? 's' : ''} selecionado{proposalServices.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">
                        {formatCurrency(totalAmount, selectedCurrency)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Para {selectedInfluencers.length} influenciador{selectedInfluencers.length !== 1 ? 'es' : ''}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={onCreateProposal}
            disabled={!groupName.trim() || !dataEnvio || proposalServices.length === 0 || loading}
          >
            {loading ? 'Criando...' : 'Criar Proposta'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

