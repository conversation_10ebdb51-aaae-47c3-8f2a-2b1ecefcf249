const admin = require('firebase-admin');
const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicializar Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function testViewsData() {
  try {
    console.log('🔍 Testando dados de views no Firestore...');
    
    // Dados de teste com views
    const testInfluencerData = {
      personalInfo: {
        name: 'Teste Views Influencer',
        age: 25,
        gender: 'Ma<PERSON><PERSON><PERSON>'
      },
      platforms: {
        instagram: {
          username: '@teste_views',
          followers: 50000,
          engagementRate: 5.2,
          views: {
            storiesViews: 12000,
            reelsViews: 25000
          },
          pricing: {
            story: 500,
            reel: 800
          }
        },
        youtube: {
          username: '@testeviews',
          followers: 80000,
          engagementRate: 4.8,
          views: {
            shortsViews: 45000,
            longFormViews: 120000
          },
          pricing: {
            shorts: 1200,
            dedicated: 2500
          }
        },
        tiktok: {
          username: '@testeviews',
          followers: 75000,
          engagementRate: 8.5,
          views: {
            videoViews: 85000
          },
          pricing: {
            video: 900
          }
        }
      },
      location: {
        country: 'Brasil',
        state: 'SP',
        city: 'São Paulo'
      },
      contact: {
        email: '<EMAIL>',
        whatsapp: '***********'
      },
      business: {
        categories: ['Lifestyle', 'Tech'],
        promotesTraders: false
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'test-user',
      createdBy: 'test-script'
    };
    
    // Salvar no Firestore
    console.log('💾 Salvando dados de teste no Firestore...');
    const docRef = await db.collection('influencers').add(testInfluencerData);
    console.log('✅ Documento criado com ID:', docRef.id);
    
    // Buscar o documento criado
    console.log('🔍 Buscando documento criado...');
    const doc = await db.collection('influencers').doc(docRef.id).get();
    const savedData = doc.data();
    
    console.log('📋 Dados salvos:');
    console.log('- Nome:', savedData.personalInfo?.name);
    console.log('- Platforms:', Object.keys(savedData.platforms || {}));
    
    // Verificar dados de views para cada plataforma
    if (savedData.platforms) {
      console.log('\n🔍 Verificando dados de views:');
      
      Object.keys(savedData.platforms).forEach(platform => {
        const platformData = savedData.platforms[platform];
        console.log(`\n📱 ${platform.toUpperCase()}:`);
        console.log('  - Username:', platformData.username);
        console.log('  - Followers:', platformData.followers);
        console.log('  - Views:', JSON.stringify(platformData.views, null, 4));
        console.log('  - Pricing:', JSON.stringify(platformData.pricing, null, 4));
      });
    }
    
    // Limpar dados de teste
    console.log('\n🧹 Removendo dados de teste...');
    await db.collection('influencers').doc(docRef.id).delete();
    console.log('✅ Dados de teste removidos');
    
    console.log('\n🎉 Teste concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
  
  process.exit(0);
}

testViewsData(); 
