import { NextResponse } from 'next/server';
import { getFinancialByInfluencerId, updateFinancial } from '@/lib/firebase-financials';
import { deleteDocument } from '@/lib/firebase-storage';
import { db } from '@/lib/firebase';

export async function POST(request: Request) {
  try {
    const { documentUrl, influencerId, proposalId, documentId } = await request.json();
    
    if (!documentUrl || !influencerId) {
      return NextResponse.json(
        { error: 'URL do documento e ID do influenciador são obrigatórios' },
        { status: 400 }
      );
    }
    
    console.log(`📄 [DELETE] Excluindo documento: ${documentUrl} do influenciador: ${influencerId}`);
    console.log(`📄 [DELETE] Contexto: proposal=${proposalId || 'independente'}, documentId=${documentId || 'não fornecido'}`);
    
    // 🆕 ESTRUTURA HIERÁRQUICA: Se há proposalId, excluir da subcoleção
    if (proposalId) {
      console.log('📄 [HIERARCHICAL] Excluindo documento da subcoleção hierárquica');

      // Verificar se a proposta existe
      const proposalDoc = await db.collection('proposals').doc(proposalId).get();
      if (!proposalDoc.exists) {
        return NextResponse.json(
          { error: 'Proposta não encontrada' },
          { status: 404 }
        );
      }

      // Se documentId foi fornecido, usar ele diretamente
      if (documentId) {
        try {
          await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .doc(influencerId)
            .collection('documents')
            .doc(documentId)
            .delete();

          console.log('✅ [HIERARCHICAL] Documento excluído da subcoleção com ID:', documentId);
        } catch (error) {
          console.error('❌ [HIERARCHICAL] Erro ao excluir documento por ID:', error);
          return NextResponse.json(
            { error: 'Erro ao excluir documento da proposta' },
            { status: 500 }
          );
        }
      } else {
        // Buscar documento pela URL na subcoleção
        try {
          const documentsSnapshot = await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .doc(influencerId)
            .collection('documents')
            .where('url', '==', documentUrl)
            .get();

          if (documentsSnapshot.empty) {
            return NextResponse.json(
              { error: 'Documento não encontrado na proposta' },
              { status: 404 }
            );
          }

          // Excluir documento encontrado
          const docToDelete = documentsSnapshot.docs[0];
          await docToDelete.ref.delete();

          console.log('✅ [HIERARCHICAL] Documento excluído da subcoleção por URL:', documentUrl);
        } catch (error) {
          console.error('❌ [HIERARCHICAL] Erro ao buscar/excluir documento por URL:', error);
          return NextResponse.json(
            { error: 'Erro ao excluir documento da proposta' },
            { status: 500 }
          );
        }
      }

      // Buscar documentos restantes para retornar
      const remainingDocumentsSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('documents')
        .orderBy('uploadedAt', 'desc')
        .get();

      const remainingDocuments = remainingDocumentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Tentar excluir o arquivo do Firebase Storage
      try {
        if (documentUrl.includes('storage.googleapis.com')) {
          await deleteDocument(documentUrl);
          console.log(`📄 [STORAGE] Arquivo excluído do Firebase Storage: ${documentUrl}`);
        }
      } catch (fileError) {
        console.error('⚠️ [STORAGE] Erro ao excluir arquivo (documento já removido do banco):', fileError);
      }

      return NextResponse.json({
        success: true,
        message: 'Documento excluído da proposta com sucesso',
        structure: 'hierarchical',
        remainingDocuments: remainingDocuments
      });
    }
    
    // 🔄 ESTRUTURA INDEPENDENTE: Excluir da coleção influencer_financials (comportamento atual)
    else {
      console.log('📄 [INDEPENDENT] Excluindo documento da estrutura independente (influencer_financials)');
      
      // Buscar registro financeiro do influenciador
      const financialRecord = await getFinancialByInfluencerId(influencerId);
      
      if (!financialRecord) {
        return NextResponse.json(
          { error: 'Registro financeiro não encontrado para este influenciador' },
          { status: 404 }
        );
      }
      
      // Verificar se o documento existe na lista
      const existingDocuments = financialRecord.additionalData?.documents || [];
      const documentExists = existingDocuments.some((doc: any) => doc.url === documentUrl);
      
      if (!documentExists) {
        return NextResponse.json(
          { error: 'Documento não encontrado na lista' },
          { status: 404 }
        );
      }
      
      // Remover documento da lista
      const updatedDocuments = existingDocuments.filter((doc: any) => doc.url !== documentUrl);

      // Atualizar registro no banco de dados
      await updateFinancial(financialRecord.id, {
        additionalData: {
          ...financialRecord.additionalData,
          documents: updatedDocuments
        }
      });
      
      // Tentar excluir o arquivo do Firebase Storage
      try {
        if (documentUrl.includes('storage.googleapis.com')) {
          await deleteDocument(documentUrl);
          console.log(`📄 [STORAGE] Arquivo excluído do Firebase Storage: ${documentUrl}`);
        } else if (documentUrl.startsWith('/uploads/')) {
          console.log(`📄 [LEGACY] URL local detectada (sistema legado): ${documentUrl} - apenas removido da lista`);
        }
      } catch (fileError) {
        console.error('⚠️ [STORAGE] Erro ao excluir arquivo (documento já removido do banco):', fileError);
      }
      
      return NextResponse.json({
        success: true,
        message: 'Documento excluído com sucesso',
        structure: 'independent',
        remainingDocuments: updatedDocuments
      });
    }
    
  } catch (error) {
    console.error('❌ [DELETE] Erro ao excluir documento:', error);
    return NextResponse.json(
      { 
        error: 'Falha ao excluir documento',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}


