

import React, { useState } from "react"
import { 
  <PERSON>ridIcon, 
  List, 
  Plus, 
  Trash2, 
  <PERSON><PERSON>, 
  MessageSquare, 
  CheckCircle, 
  Send, 
  Settings
} from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover"
import { 
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Protect } from "@clerk/nextjs"
import { useBrandsList } from "@/hooks/use-brands"
import { Brand } from "@/types/brand"
import { useTranslations } from "@/hooks/use-translations"

interface ToolbarProps {
  viewMode: "grid" | "list"
  selectionMode: boolean
  selectedCount: number
  onViewModeChange: (mode: "grid" | "list") => void
  onAddInfluencer: () => void
  onToggleSelectionMode: () => void
  onDeleteSelected: () => void
  onDuplicateSelected: () => void
  verifiedOnly: boolean
  onVerifiedOnlyChange: (value: boolean) => void
  onSendToBrand?: (brandId: string, brandName: string) => void
}

// Componente dos controles de visualização
function ViewControls({ viewMode, onViewModeChange }: {
  viewMode: "grid" | "list"
  onViewModeChange: (mode: "grid" | "list") => void
}) {
  return (
    <div className="flex items-center gap-1 bg-muted/30 rounded-lg p-1">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="sm"
            className={`h-8 w-8 p-0 ${
              viewMode === 'grid' 
                ? 'bg-background shadow-sm text-foreground' 
                : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
            }`}
            onClick={() => onViewModeChange('grid')}
          >
            <GridIcon className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Visualização em grade</p>
        </TooltipContent>
      </Tooltip>
      
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="sm"
            className={`h-8 w-8 p-0 ${
              viewMode === 'list' 
                ? 'bg-background shadow-sm text-foreground' 
                : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
            }`}
            onClick={() => onViewModeChange('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Visualização em lista</p>
        </TooltipContent>
      </Tooltip>
    </div>
  )
}

// Componente dos filtros rápidos
function QuickFilters({ verifiedOnly, onVerifiedOnlyChange }: {
  verifiedOnly: boolean
  onVerifiedOnlyChange: (value: boolean) => void
}) {
  const { t } = useTranslations()
  
  return (
    <div className="flex items-center gap-3">
      <Separator orientation="vertical" className="h-6" />
      
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="sm"
            className={`h-8 px-3 gap-2 ${
              verifiedOnly 
                ? 'bg-[#ff0074] hover:bg-[#cc0050] text-white' 
                : 'bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground'
            }`}
            onClick={() => onVerifiedOnlyChange(!verifiedOnly)}
          >
            <CheckCircle className="h-3 w-3" />
            <span className="text-xs font-medium">{t('common.exclusive') || 'Exclusivos'}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Mostrar apenas influenciadores verificados</p>
        </TooltipContent>
      </Tooltip>
    </div>
  )
}

// Componente das ações principais
function MainActions({ onAddInfluencer, selectionMode, onToggleSelectionMode }: {
  onAddInfluencer: () => void
  selectionMode: boolean
  onToggleSelectionMode: () => void
}) {
  const { t } = useTranslations()
  
  return (
    <div className="flex items-center gap-2">
      <Protect role="org:admin">
        <Link href="/notes" passHref>
          <Button 
            size="sm"
            className="h-8 px-3 gap-2 bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground"
          >
            <MessageSquare className="h-3 w-3" />
            <span className="hidden sm:inline text-xs">{t('common.view_notes') || 'Anotações'}</span>
          </Button>
        </Link>
      </Protect>

      <Protect role="org:admin">
        <Button 
          size="sm"
          className={`h-8 px-3 gap-2 ${
            selectionMode 
              ? 'bg-muted text-foreground' 
              : 'bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground'
          }`}
          onClick={onToggleSelectionMode}
        >
          <Settings className="h-3 w-3" />
          <span className="hidden sm:inline text-xs">
            {selectionMode ? t('common.cancel_selection') || 'Cancelar' : t('common.select_multiple') || 'Selecionar'}
          </span>
        </Button>
      </Protect>

      <Protect role="org:admin">
        <Button 
          size="sm"
          className="h-8 px-3 gap-2 bg-[#5600ce] hover:bg-[#4a00b8] text-white shadow-sm"
          onClick={onAddInfluencer}
        >
          <Plus className="h-3 w-3" />
          <span className="hidden sm:inline text-xs font-medium">
            {t('common.add_influencer') || 'Adicionar'}
          </span>
        </Button>
      </Protect>
    </div>
  )
}

// Componente das ações de seleção
function SelectionActions({ 
  selectedCount, 
  onDeleteSelected, 
  onDuplicateSelected, 
  onSendToBrand 
}: {
  selectedCount: number
  onDeleteSelected: () => void
  onDuplicateSelected: () => void
  onSendToBrand?: (brandId: string, brandName: string) => void
}) {
  const { brands, loading: isLoadingBrands } = useBrandsList()
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)

  const handleSendToBrand = (brand: Brand) => {
    if (onSendToBrand) {
      onSendToBrand(brand.id, brand.name)
      setIsPopoverOpen(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Badge className="bg-[#ff0074]/10 text-[#ff0074] border-[#ff0074]/20 px-2 py-1 text-xs font-medium">
        {selectedCount} selecionado{selectedCount > 1 ? 's' : ''}
      </Badge>

      {onSendToBrand && (
        <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <PopoverTrigger asChild>
            <Button 
              size="sm"
              className="h-8 px-3 gap-2 bg-[#9810fa] hover:bg-[#4a00b8] text-white"
            >
              <Send className="h-3 w-3" />
              <span className="text-xs">Enviar</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <Card className="border-0 shadow-lg">
              <div className="p-4">
                <h4 className="font-semibold text-sm mb-3 text-foreground">
                  Selecionar Marca
                </h4>
                {isLoadingBrands ? (
                  <div className="text-sm text-muted-foreground py-4 text-center">
                    Carregando marcas...
                  </div>
                ) : brands.length === 0 ? (
                  <div className="text-sm text-muted-foreground py-4 text-center">
                    Nenhuma marca encontrada
                  </div>
                ) : (
                  <div className="space-y-1 max-h-60 overflow-y-auto">
                    {brands.map((brand) => (
                      <Button
                        key={brand.id}
                        className="w-full justify-start h-auto p-3 hover:bg-muted/50 bg-transparent text-left"
                        onClick={() => handleSendToBrand(brand)}
                      >
                        <div className="flex items-center gap-3 w-full">
                          {brand.logo && (
                            <img 
                              src={brand.logo} 
                              alt={brand.name}
                              className="w-8 h-8 rounded object-cover"
                            />
                          )}
                          <div className="text-left flex-1">
                            <div className="font-medium text-sm text-foreground">
                              {brand.name}
                            </div>
                            {brand.industry && (
                              <div className="text-xs text-muted-foreground">
                                {brand.industry}
                              </div>
                            )}
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          </PopoverContent>
        </Popover>
      )}
      
      <Protect role="org:admin">
        <Button 
          size="sm"
          className="h-8 px-3 gap-2 bg-destructive/10 hover:bg-destructive/20 text-destructive hover:text-destructive"
          onClick={onDeleteSelected}
        >
          <Trash2 className="h-3 w-3" />
          <span className="text-xs">Excluir</span>
        </Button>
      </Protect>
      
      <Protect role="org:admin">
        <Button 
          size="sm"
          className="h-8 px-3 gap-2 bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground"
          onClick={onDuplicateSelected}
        >
          <Copy className="h-3 w-3" />
          <span className="text-xs">Duplicar</span>
        </Button>
      </Protect>
    </div>
  )
}

export function Toolbar({
  viewMode,
  selectionMode,
  selectedCount,
  onViewModeChange,
  onAddInfluencer,
  onToggleSelectionMode,
  onDeleteSelected,
  onDuplicateSelected,
  verifiedOnly,
  onVerifiedOnlyChange,
  onSendToBrand
}: ToolbarProps) {
  return (
    <Card className="p-3 border-border/50 shadow-sm bg-background/50 backdrop-blur-sm">
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
        {/* Controles à esquerda */}
        <div className="flex items-center gap-4 flex-wrap">
          <ViewControls 
            viewMode={viewMode} 
            onViewModeChange={onViewModeChange} 
          />
          
          <QuickFilters
            verifiedOnly={verifiedOnly}
            onVerifiedOnlyChange={onVerifiedOnlyChange}
          />
        </div>

        {/* Ações à direita */}
        <div className="flex items-center gap-4 w-full lg:w-auto justify-end">
          {selectionMode && selectedCount > 0 ? (
            <SelectionActions
              selectedCount={selectedCount}
              onDeleteSelected={onDeleteSelected}
              onDuplicateSelected={onDuplicateSelected}
              onSendToBrand={onSendToBrand}
            />
          ) : (
            <MainActions
              onAddInfluencer={onAddInfluencer}
              selectionMode={selectionMode}
              onToggleSelectionMode={onToggleSelectionMode}
            />
          )}
        </div>
      </div>
    </Card>
  )
}



