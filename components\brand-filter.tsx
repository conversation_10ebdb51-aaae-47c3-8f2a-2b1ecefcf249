"use client"

import { useState } from "react"
import { Check } from "lucide-react"
import { useBrandsList } from "@/hooks/use-brands"
import { useTranslations } from "@/hooks/use-translations"

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"

interface Brand {
  id: string
  name: string
  logo?: string
  logoBackgroundColor?: string
}

interface BrandFilterProps {
  onFilterChange: (selectedBrands: string[]) => void
}

export function BrandFilter({ onFilterChange }: BrandFilterProps) {
  const { t } = useTranslations();
  // 🚀 FASE 6: Usando novo hook com isolamento automático
  const { brands, loading, error } = useBrandsList()
  
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  // Log de erro se houver (exceto erros de autenticação)
  if (error && !error.includes('não autenticado') && !error.includes('Usuário não autenticado')) {
    console.error('Erro ao carregar marcas para filtro:', error)
  }

  const toggleBrand = (brandId: string) => {
    const newSelection = selectedBrands.includes(brandId)
      ? selectedBrands.filter(id => id !== brandId)
      : [...selectedBrands, brandId]
    
    setSelectedBrands(newSelection)
    onFilterChange(newSelection)
  }

  const filteredBrands = brands.filter(brand => 
    brand.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const selectedBrandsDetails = brands.filter(brand => 
    selectedBrands.includes(brand.id)
  )

  return (
    <div className="flex flex-wrap gap-2 items-center">
      <Popover>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className="border-dashed dark:border border"
          >
                         {t('common.filter_by_brand')}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-2 border-b dark:border-white/10 border-gray-200">
            <Input
              placeholder="Buscar marca..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="dark:bg-black/20"
            />
          </div>
          <ScrollArea className="h-72">
            <div className="p-2">
              {loading ? (
                <div className="text-center py-4 text-sm text-gray-500">
                  Carregando marcas...
                </div>
              ) : filteredBrands.length > 0 ? (
                <div className="grid grid-cols-1 gap-2">
                  {filteredBrands.map((brand) => (
                    <div
                      key={brand.id}
                      className={`flex items-center gap-3 p-3 rounded-md cursor-pointer transition-colors
                        ${selectedBrands.includes(brand.id)
                          ? 'bg-gray-100 dark:bg-white/10'
                          : 'hover:bg-gray-50 dark:hover:bg-white/5'
                        }`}
                      onClick={() => toggleBrand(brand.id)}
                    >
                      <div 
                        className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full"
                        style={{
                          backgroundColor: brand.logoBackgroundColor || '#1f2937'
                        }}
                      >
                        {brand.logo ? (
                          <img
                            src={brand.logo}
                            alt={brand.name}
                            className="w-full h-full object-contain p-2"
                          />
                        ) : (
                          <span className="text-base font-bold text-white">
                            {brand.name.substring(0, 1)}
                          </span>
                        )}
                      </div>
                      <span className="flex-grow text-sm font-medium">{brand.name}</span>
                      {selectedBrands.includes(brand.id) && (
                        <Check className="h-5 w-5" />
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-sm text-gray-500">
                  Nenhuma marca encontrada
                </div>
              )}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>

      {/* Mostrar marcas selecionadas */}
      {selectedBrandsDetails.map((brand) => (
        <Badge
          key={brand.id}
          variant="secondary"
          className="gap-1 px-3 py-1"
          onClick={() => toggleBrand(brand.id)}
        >
          {brand.name}
          <button
            className="ml-1 hover:bg-white/20 rounded-full"
            onClick={(e) => {
              e.stopPropagation()
              toggleBrand(brand.id)
            }}
          >
            ×
          </button>
        </Badge>
      ))}
    </div>
  )
}

