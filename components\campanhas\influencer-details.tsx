import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { X, MapPin, Calendar, Users, Heart, Eye, MessageCircle, Share2, DollarSign, Instagram, Youtube, Music } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Influencer {
  id: string;
  nome: string;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
}

interface BrandInfluencer {
  id: string;
  brandId: string;
  brandName: string;
  influencerId: string;
  influencerName: string;
  influencerData: Influencer;
  status: 'enviado' | 'visualizado' | 'interessado' | 'rejeitado' | 'proposta_enviada';
  sentAt: Date;
  viewedAt?: Date;
  lastInteractionAt?: Date;
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

interface InfluencerDetailsProps {
  influencer: Influencer | null;
  brandInfluencer?: BrandInfluencer | null;
  showDetailsPanel: boolean;
  activeTab: 'all' | 'sent' | 'proposals';
  selectedCurrency: string;
  onClose: () => void;
  onSendToBrand?: (influencer: Influencer) => void;
  onCreateProposal?: (influencer: Influencer) => void;
}

function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

function formatCurrency(value: number, currency: string): string {
  if (currency === 'USD') {
    return `$${value.toLocaleString()}`;
  } else if (currency === 'EUR') {
    return `€${value.toLocaleString()}`;
  }
  return `R$ ${value.toLocaleString()}`;
}

function getServiceIcon(service: string) {
  switch (service) {
    case 'postFeed':
      return <Instagram className="h-4 w-4" />;
    case 'stories':
      return <Instagram className="h-4 w-4" />;
    case 'reels':
      return <Instagram className="h-4 w-4" />;
    case 'videoYoutube':
      return <Youtube className="h-4 w-4" />;
    case 'videoTiktok':
      return <Music className="h-4 w-4" />;
    default:
      return <Share2 className="h-4 w-4" />;
  }
}

function getServiceName(service: string): string {
  switch (service) {
    case 'postFeed':
      return 'Post Feed';
    case 'stories':
      return 'Stories';
    case 'reels':
      return 'Reels';
    case 'videoYoutube':
      return 'Vídeo YouTube';
    case 'videoTiktok':
      return 'Vídeo TikTok';
    default:
      return service;
  }
}

function renderStatusBadge(status: string) {
  const statusConfig = {
    enviado: { label: 'Enviado', variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
    visualizado: { label: 'Visualizado', variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800' },
    interessado: { label: 'Interessado', variant: 'default' as const, color: 'bg-green-100 text-green-800' },
    rejeitado: { label: 'Rejeitado', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
    proposta_enviada: { label: 'Proposta Enviada', variant: 'secondary' as const, color: 'bg-purple-100 text-purple-800' }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.enviado;
  
  return (
    <Badge variant={config.variant} className={config.color}>
      {config.label}
    </Badge>
  );
}

export function InfluencerDetails({
  influencer,
  brandInfluencer,
  showDetailsPanel,
  activeTab,
  selectedCurrency,
  onClose,
  onSendToBrand,
  onCreateProposal
}: InfluencerDetailsProps) {
  if (!showDetailsPanel || !influencer) {
    return null;
  }

  const availableServices = Object.entries(influencer.servicos || {})
    .filter(([_, price]) => price > 0)
    .map(([service, price]) => ({ service, price }));

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-background border-l shadow-lg z-50 overflow-y-auto">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Detalhes do Influenciador</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Profile Section */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center">
              <div className="relative mb-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={influencer.avatar} alt={influencer.nome} />
                  <AvatarFallback className="text-lg">
                    {influencer.nome.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                {influencer.verified && (
                  <div className="absolute -bottom-1 -right-1 bg-blue-500 rounded-full p-1">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
              
              <h3 className="text-xl font-semibold mb-2">{influencer.nome}</h3>
              
              <div className="flex items-center text-muted-foreground mb-2">
                <MapPin className="h-4 w-4 mr-1" />
                <span className="text-sm">
                  {influencer.cidade}, {influencer.estado} - {influencer.pais}
                </span>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {influencer.idade} anos
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  {influencer.genero}
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2 justify-center">
                <Badge variant="outline">{influencer.categoria}</Badge>
                {influencer.divulgaTrader && (
                  <Badge variant="secondary">Trader</Badge>
                )}
                {brandInfluencer && renderStatusBadge(brandInfluencer.status)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Social Media Stats */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-base">Redes Sociais</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {influencer.redesSociais?.instagram && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <Instagram className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">@{influencer.redesSociais.instagram.username}</p>
                    <p className="text-sm text-muted-foreground">Instagram</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatNumber(influencer.redesSociais.instagram.seguidores)}</p>
                  <p className="text-sm text-muted-foreground">{influencer.redesSociais.instagram.engajamento}% eng.</p>
                </div>
              </div>
            )}
            
            {influencer.redesSociais?.youtube && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                    <Youtube className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">@{influencer.redesSociais.youtube.username}</p>
                    <p className="text-sm text-muted-foreground">YouTube</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatNumber(influencer.redesSociais.youtube.seguidores)}</p>
                  <p className="text-sm text-muted-foreground">{formatNumber(influencer.redesSociais.youtube.visualizacoes)} views</p>
                </div>
              </div>
            )}
            
            {influencer.redesSociais?.tiktok && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-black dark:bg-white rounded-lg flex items-center justify-center">
                    <Music className="h-4 w-4 text-white dark:text-black" />
                  </div>
                  <div>
                    <p className="font-medium">@{influencer.redesSociais.tiktok.username}</p>
                    <p className="text-sm text-muted-foreground">TikTok</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatNumber(influencer.redesSociais.tiktok.seguidores)}</p>
                  <p className="text-sm text-muted-foreground">{formatNumber(influencer.redesSociais.tiktok.curtidas)} likes</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Services and Pricing */}
        {availableServices.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-base">Serviços e Preços</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {availableServices.map(({ service, price }) => (
                <div key={service} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getServiceIcon(service)}
                    <span className="font-medium">{getServiceName(service)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span className="font-semibold">{formatCurrency(price, selectedCurrency)}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Contact Information - ✅ COMPONENTE DINÂMICO */}
        {influencer.whatsapp && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-base">Contato</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{influencer.whatsapp}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Status Information for Sent Tab */}
        {activeTab === 'sent' && brandInfluencer && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-base">Status do Envio</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {renderStatusBadge(brandInfluencer.status)}
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Enviado em:</span>
                <span className="text-sm font-medium">
                  {new Date(brandInfluencer.sentAt).toLocaleDateString('pt-BR')}
                </span>
              </div>
              
              {brandInfluencer.viewedAt && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Visualizado em:</span>
                  <span className="text-sm font-medium">
                    {new Date(brandInfluencer.viewedAt).toLocaleDateString('pt-BR')}
                  </span>
                </div>
              )}
              
              {brandInfluencer.notes && (
                <div>
                  <span className="text-sm text-muted-foreground">Observações:</span>
                  <p className="text-sm mt-1 p-2 bg-muted/50 rounded">{brandInfluencer.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          {activeTab === 'all' && onSendToBrand && (
            <Button 
              className="w-full" 
              onClick={() => onSendToBrand(influencer)}
            >
              Enviar para Marca
            </Button>
          )}
          
          {onCreateProposal && (
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => onCreateProposal(influencer)}
            >
              Criar Proposta
            </Button>
          )}
          
          <Button variant="ghost" className="w-full">
            Ver Perfil Completo
          </Button>
        </div>
      </div>
    </div>
  );
}

