import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { Influencer } from '../types/influencer';
import { InfluencerFinancial } from '../types/influencer-financial';
import { Campaign } from '../types/campaign';
import { Category } from '../types/category';
import { Brand } from '../types/brand';

// Definindo a interface Filter aqui para evitar problemas de importação
interface Filter {
  id?: string;
  name: string;
  location?: string;
  minFollowers?: number;
  maxFollowers?: number;
  minRating?: number;
  verifiedOnly?: boolean;
  availableOnly?: boolean;
  platforms?: {
    instagram?: boolean;
    tiktok?: boolean;
    youtube?: boolean;
    twitter?: boolean;
  };
  userId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicialize o Firebase apenas se não estiver já inicializado
const firebaseApp = getApps().length === 0 
  ? initializeApp({
      credential: cert(serviceAccount)
    })
  : getApps()[0];

// Exporte o Firestore para uso em toda a aplicação
export const db = getFirestore(firebaseApp);

// Coleções para cada tipo de dados
export const influencersCollection = db.collection('influencers');
export const financialsCollection = db.collection('influencer_financials');
export const campaignsCollection = db.collection('campaigns');
export const categoriesCollection = db.collection('categories');
export const brandsCollection = db.collection('brands');
export const filtersCollection = db.collection('filters');

// FUNÇÕES PARA INFLUENCIADORES (DADOS BÁSICOS)
