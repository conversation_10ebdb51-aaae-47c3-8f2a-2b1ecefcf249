// HOOK PARA DADOS FINANCEIROS COM CACHE E DENORMALIZAÇÃO
// Facilita o acesso aos dados financeiros otimizados nos componentes React

import { useState, useEffect, useCallback, useMemo } from 'react';
import { InfluencerFinancial } from '../types/influencer-financial';
import { Influencer } from '../types/influencer';
import { FinancialCacheService } from '../lib/firebase-financial-cache';
import { FinancialDenormalizationService } from '../lib/financial-denormalization-service';

// Interface para o estado do hook
interface FinancialDataState {
  data: InfluencerFinancial | null;
  isLoading: boolean;
  error: string | null;
  hasData: boolean;
}

// Interface para estatísticas de cache
interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
  hitRate: number;
}

// Interface para filtros de busca por preço
interface PriceFilters {
  priceRange?: 'low' | 'medium' | 'high' | 'premium';
  minPrice?: number;
  maxPrice?: number;
  platform?: 'instagram' | 'tiktok' | 'youtube';
}

/**
 * Hook principal para dados financeiros de um influenciador específico
 */
export function useFinancialData(influencerId: string | null) {
  const [state, setState] = useState<FinancialDataState>({
    data: null,
    isLoading: false,
    error: null,
    hasData: false
  });

  // Buscar dados financeiros
  const fetchFinancialData = useCallback(async () => {
    if (!influencerId) {
      setState({
        data: null,
        isLoading: false,
        error: null,
        hasData: false
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const data = await FinancialCacheService.getFinancialData(influencerId);
      
      setState({
        data,
        isLoading: false,
        error: null,
        hasData: data !== null
      });
    } catch (error) {
      setState({
        data: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao carregar dados financeiros',
        hasData: false
      });
    }
  }, [influencerId]);

  // Carregar dados quando o influencerId mudar
  useEffect(() => {
    fetchFinancialData();
  }, [fetchFinancialData]);

  // Função para invalidar cache e recarregar
  const refresh = useCallback(() => {
    if (influencerId) {
      FinancialCacheService.invalidateInfluencer(influencerId);
      fetchFinancialData();
    }
  }, [influencerId, fetchFinancialData]);

  // Função para sincronizar dados denormalizados
  const syncDenormalizedData = useCallback(async () => {
    if (influencerId && state.data) {
      try {
        await FinancialDenormalizationService.syncFinancialData(influencerId, state.data);
        return true;
      } catch (error) {
        console.error('Erro ao sincronizar dados denormalizados:', error);
        return false;
      }
    }
    return false;
  }, [influencerId, state.data]);

  return {
    ...state,
    refresh,
    syncDenormalizedData
  };
}

/**
 * Hook para dados financeiros de múltiplos influenciadores
 */
export function useMultipleFinancialData(influencerIds: string[]) {
  const [state, setState] = useState<{
    data: Map<string, InfluencerFinancial | null>;
    isLoading: boolean;
    error: string | null;
  }>({
    data: new Map(),
    isLoading: false,
    error: null
  });

  // Buscar dados para múltiplos influenciadores
  const fetchMultipleData = useCallback(async () => {
    if (influencerIds.length === 0) {
      setState({
        data: new Map(),
        isLoading: false,
        error: null
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const data = await FinancialCacheService.getMultipleFinancialData(influencerIds);
      
      setState({
        data,
        isLoading: false,
        error: null
      });
    } catch (error) {
      setState({
        data: new Map(),
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao carregar dados financeiros'
      });
    }
  }, [influencerIds]);

  // Pré-carregar dados
  const preloadData = useCallback(async () => {
    if (influencerIds.length > 0) {
      try {
        await FinancialCacheService.preloadFinancialData(influencerIds);
        await fetchMultipleData();
      } catch (error) {
        console.error('Erro ao pré-carregar dados:', error);
      }
    }
  }, [influencerIds, fetchMultipleData]);

  // Carregar dados quando os IDs mudarem
  useEffect(() => {
    fetchMultipleData();
  }, [fetchMultipleData]);

  return {
    ...state,
    refresh: fetchMultipleData,
    preload: preloadData
  };
}

/**
 * Hook para busca de influenciadores por critérios financeiros
 */
export function useInfluencersByPrice(
  userId: string,
  filters: PriceFilters,
  limit: number = 20
) {
  const [state, setState] = useState<{
    influencers: Influencer[];
    isLoading: boolean;
    error: string | null;
  }>({
    influencers: [],
    isLoading: false,
    error: null
  });

  // Buscar influenciadores por filtros
  const searchInfluencers = useCallback(async () => {
    if (!userId) return;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      let influencers: Influencer[] = [];

      if (filters.priceRange) {
        influencers = await FinancialDenormalizationService.getInfluencersByPriceRange(
          filters.priceRange,
          userId,
          limit
        );
      } else if (filters.minPrice !== undefined && filters.maxPrice !== undefined) {
        influencers = await FinancialDenormalizationService.getInfluencersByPriceValues(
          filters.minPrice,
          filters.maxPrice,
          userId,
          limit
        );
      } else if (filters.platform) {
        influencers = await FinancialDenormalizationService.getInfluencersByMainPlatform(
          filters.platform,
          userId,
          limit
        );
      }

      setState({
        influencers,
        isLoading: false,
        error: null
      });
    } catch (error) {
      setState({
        influencers: [],
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao buscar influenciadores'
      });
    }
  }, [userId, filters, limit]);

  // Buscar quando os filtros mudarem
  useEffect(() => {
    searchInfluencers();
  }, [searchInfluencers]);

  return {
    ...state,
    refresh: searchInfluencers
  };
}

/**
 * Hook para estatísticas financeiras
 */
export function useFinancialStats(userId: string) {
  const [stats, setStats] = useState<{
    totalWithPricing: number;
    byPriceRange: Record<string, number>;
    byPlatform: Record<string, number>;
    avgPrice: number;
    minPrice: number;
    maxPrice: number;
  } | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carregar estatísticas
  const fetchStats = useCallback(async () => {
    if (!userId) return;

    setIsLoading(true);
    setError(null);

    try {
      const statsData = await FinancialDenormalizationService.getPricingStats(userId);
      setStats(statsData);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erro ao carregar estatísticas');
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Carregar estatísticas quando o userId mudar
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Calcular percentuais por faixa de preço
  const priceRangePercentages = useMemo(() => {
    if (!stats || stats.totalWithPricing === 0) return null;

    return {
      low: (stats.byPriceRange.low / stats.totalWithPricing) * 100,
      medium: (stats.byPriceRange.medium / stats.totalWithPricing) * 100,
      high: (stats.byPriceRange.high / stats.totalWithPricing) * 100,
      premium: (stats.byPriceRange.premium / stats.totalWithPricing) * 100
    };
  }, [stats]);

  // Calcular percentuais por plataforma
  const platformPercentages = useMemo(() => {
    if (!stats || stats.totalWithPricing === 0) return null;

    return {
      instagram: (stats.byPlatform.instagram / stats.totalWithPricing) * 100,
      tiktok: (stats.byPlatform.tiktok / stats.totalWithPricing) * 100,
      youtube: (stats.byPlatform.youtube / stats.totalWithPricing) * 100
    };
  }, [stats]);

  return {
    stats,
    priceRangePercentages,
    platformPercentages,
    isLoading,
    error,
    refresh: fetchStats
  };
}

/**
 * Hook para gerenciar o cache financeiro
 */
export function useFinancialCache() {
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);

  // Atualizar estatísticas do cache
  const updateCacheStats = useCallback(() => {
    const stats = FinancialCacheService.getCacheStats();
    setCacheStats(stats);
  }, []);

  // Limpar cache
  const clearCache = useCallback(() => {
    FinancialCacheService.clearCache();
    updateCacheStats();
  }, [updateCacheStats]);

  // ⚡ OTIMIZAÇÃO: Atualizar estatísticas apenas uma vez no carregamento
  useEffect(() => {
    updateCacheStats();
    
    // 🚫 POLLING REMOVIDO: Não atualizar automaticamente - só quando necessário
    // const interval = setInterval(updateCacheStats, 30000);
    // return () => clearInterval(interval);
  }, [updateCacheStats]);

  return {
    cacheStats,
    clearCache,
    updateStats: updateCacheStats
  };
}

export default {
  useFinancialData,
  useMultipleFinancialData,
  useInfluencersByPrice,
  useFinancialStats,
  useFinancialCache
}; 

