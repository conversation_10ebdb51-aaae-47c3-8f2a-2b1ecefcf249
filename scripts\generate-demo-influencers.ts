import { db } from '../lib/firebase-admin';
import { FieldValue } from 'firebase-admin/firestore';

/**
 * Script para gerar influenciadores de demonstração com dados completos
 * Uso: 
 * - Gerar: npx ts-node scripts/generate-demo-influencers.ts --generate --userId=USER_ID
 * - Limpar: npx ts-node scripts/generate-demo-influencers.ts --cleanup --userId=USER_ID
 */

interface DemoInfluencer {
  // Dados básicos
  name: string;
  email: string;
  phone: string;
  whatsapp: string;
  
  // Localização
  country: string;
  state: string;
  city: string;
  
  // Pessoais
  age: number;
  gender: string;
  bio: string;
  avatar: string;
  
  // Profissionais
  category: string;
  categories: string[];
  totalFollowers: number;
  engagementRate: number;
  isVerified: boolean;
  isAvailable: boolean;
  status: string;
  
  // Redes sociais
  instagramUsername: string;
  instagramFollowers: number;
  instagramEngagementRate: number;
  instagramAvgViews: number;
  instagramStoriesViews: number;
  instagramReelsViews: number;
  
  tiktokUsername?: string;
  tiktokFollowers?: number;
  tiktokEngagementRate?: number;
  tiktokAvgViews?: number;
  
  youtubeUsername?: string;
  youtubeFollowers?: number;
  youtubeEngagementRate?: number;
  youtubeAvgViews?: number;
  
  // Meta
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  isDemoData: boolean; // Flag para identificar dados demo
}

interface DemoPricing {
  influencerId: string;
  services: {
    instagram: {
      story: { price: number; currency: string };
      reel: { price: number; currency: string };
      post?: { price: number; currency: string };
    };
    tiktok?: {
      video: { price: number; currency: string };
    };
    youtube?: {
      insertion: { price: number; currency: string };
      dedicated: { price: number; currency: string };
      shorts: { price: number; currency: string };
    };
  };
  isActive: boolean;
  validFrom: Date;
  validUntil: Date;
  notes: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  isDemoData: boolean;
}

interface DemoDemographics {
  influencerId: string;
  platform: string;
  audienceGender: {
    male: number;
    female: number;
    other: number;
  };
  audienceLocations: Array<{
    location: string;
    percentage: number;
  }>;
  audienceCities: Array<{
    city: string;
    percentage: number;
  }>;
  audienceAgeRange: Array<{
    range: string;
    percentage: number;
  }>;
  captureDate: Date;
  isActive: boolean;
  source: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  isDemoData: boolean;
}

// Dados brasileiros realistas
const NOMES_MASCULINOS = [
  'João Silva', 'Pedro Santos', 'Lucas Oliveira', 'Gabriel Costa', 'Rafael Lima',
  'Bruno Souza', 'Mateus Almeida', 'Felipe Rodrigues', 'André Ferreira', 'Diego Martins',
  'Thiago Pereira', 'Leonardo Barbosa', 'Gustavo Ribeiro', 'Eduardo Carvalho', 'Marcelo Dias',
  'Ricardo Nascimento', 'Fernando Campos', 'Roberto Araújo', 'Carlos Monteiro', 'Alexandre Cardoso'
];

const NOMES_FEMININOS = [
  'Maria Silva', 'Ana Santos', 'Juliana Oliveira', 'Fernanda Costa', 'Camila Lima',
  'Bruna Souza', 'Jessica Almeida', 'Amanda Rodrigues', 'Carolina Ferreira', 'Larissa Martins',
  'Bianca Pereira', 'Gabriela Barbosa', 'Isabela Ribeiro', 'Natalia Carvalho', 'Priscila Dias',
  'Vanessa Nascimento', 'Patricia Campos', 'Sandra Araújo', 'Renata Monteiro', 'Luciana Cardoso'
];

const ESTADOS_BRASILEIROS = [
  { estado: 'São Paulo', cidades: ['São Paulo', 'Campinas', 'Santos', 'Ribeirão Preto'] },
  { estado: 'Rio de Janeiro', cidades: ['Rio de Janeiro', 'Niterói', 'Petrópolis', 'Nova Iguaçu'] },
  { estado: 'Minas Gerais', cidades: ['Belo Horizonte', 'Uberlândia', 'Juiz de Fora', 'Betim'] },
  { estado: 'Bahia', cidades: ['Salvador', 'Feira de Santana', 'Vitória da Conquista', 'Camaçari'] },
  { estado: 'Paraná', cidades: ['Curitiba', 'Londrina', 'Maringá', 'Ponta Grossa'] },
  { estado: 'Rio Grande do Sul', cidades: ['Porto Alegre', 'Caxias do Sul', 'Pelotas', 'Canoas'] },
];

const CATEGORIAS = [
  'Lifestyle', 'Moda', 'Beleza', 'Fitness', 'Culinária', 'Viagem', 'Tecnologia',
  'Gaming', 'Música', 'Arte', 'Negócios', 'Educação', 'Comédia', 'Saúde', 'Pets'
];

const BIOS_TEMPLATES = [
  '🌟 Criador de conteúdo apaixonado por {categoria} | 📍 {cidade} | ✨ Transformando vidas através do digital',
  '💖 Influenciadora de {categoria} | 🎯 Conectando marcas e pessoas | 📱 Conteúdo autêntico desde 2020',
  '🚀 Expert em {categoria} | 💼 Parcerias: <EMAIL> | 🎪 Criando momentos únicos',
  '✨ {categoria} enthusiast | 🌎 Viajante do mundo | 💬 Sempre pronto para novas aventuras',
  '🎨 Criativo digital especializado em {categoria} | 🏆 +{followers}k seguidores | 💫 #ConteúdoAutêntico'
];

/**
 * Gera dados aleatórios realistas
 */
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomFloat(min: number, max: number, decimals: number = 2): number {
  return Number((Math.random() * (max - min) + min).toFixed(decimals));
}

function generateInstagramUsername(name: string): string {
  const cleanName = name.toLowerCase()
    .replace(/\s+/g, '')
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');
  
  const suffixes = ['oficial', 'real', 'br', '2024', 'insta', 'ig'];
  const useSuffix = Math.random() > 0.6;
  
  return `@${cleanName}${useSuffix ? getRandomElement(suffixes) : ''}`;
}

function generateEmail(name: string): string {
  const cleanName = name.toLowerCase()
    .replace(/\s+/g, '.')
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');
  
  const domains = ['gmail.com', 'hotmail.com', 'yahoo.com.br', 'outlook.com'];
  return `${cleanName}@${getRandomElement(domains)}`;
}

function generatePhoneNumber(): string {
  const ddd = getRandomNumber(11, 99);
  const numero = getRandomNumber(90000, 99999);
  const final = getRandomNumber(1000, 9999);
  return `(${ddd}) 9${numero}-${final}`;
}

/**
 * Gera um influenciador demo completo
 */
function generateDemoInfluencer(userId: string, index: number): DemoInfluencer {
  const isWoman = Math.random() > 0.5;
  const name = isWoman 
    ? getRandomElement(NOMES_FEMININOS) 
    : getRandomElement(NOMES_MASCULINOS);
  
  const localizacao = getRandomElement(ESTADOS_BRASILEIROS);
  const cidade = getRandomElement(localizacao.cidades);
  
  const categoria = getRandomElement(CATEGORIAS);
  const followersBase = getRandomNumber(1000, 500000);
  
  // Determinar quais plataformas tem
  const hasInstagram = true; // Todos têm Instagram
  const hasTiktok = Math.random() > 0.3; // 70% tem TikTok
  const hasYoutube = Math.random() > 0.5; // 50% tem YouTube
  
  const bio = getRandomElement(BIOS_TEMPLATES)
    .replace('{categoria}', categoria.toLowerCase())
    .replace('{cidade}', cidade)
    .replace('{followers}', Math.floor(followersBase / 1000).toString());

  const influencer: DemoInfluencer = {
    // Básicos
    name,
    email: generateEmail(name),
    phone: generatePhoneNumber(),
    whatsapp: generatePhoneNumber(),
    
    // Localização
    country: 'Brasil',
    state: localizacao.estado,
    city: cidade,
    
    // Pessoais
    age: getRandomNumber(18, 45),
    gender: isWoman ? 'feminino' : 'masculino',
    bio,
    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=${isWoman ? 'ff69b4' : '0084ff'}&color=fff&size=400`,
    
    // Profissionais
    category: categoria,
    categories: [categoria, getRandomElement(CATEGORIAS.filter(c => c !== categoria))],
    totalFollowers: followersBase,
    engagementRate: getRandomFloat(0.5, 8.5),
    isVerified: Math.random() > 0.7, // 30% verificados
    isAvailable: Math.random() > 0.2, // 80% disponíveis
    status: 'active',
    
    // Instagram (todos têm)
    instagramUsername: generateInstagramUsername(name),
    instagramFollowers: followersBase,
    instagramEngagementRate: getRandomFloat(1.0, 6.0),
    instagramAvgViews: Math.floor(followersBase * getRandomFloat(0.1, 0.4)),
    instagramStoriesViews: Math.floor(followersBase * getRandomFloat(0.2, 0.6)),
    instagramReelsViews: Math.floor(followersBase * getRandomFloat(0.3, 0.8)),
    
    // TikTok (opcional)
    ...(hasTiktok && {
      tiktokUsername: `@${name.toLowerCase().replace(/\s+/g, '')}_tk`,
      tiktokFollowers: Math.floor(followersBase * getRandomFloat(0.3, 1.8)),
      tiktokEngagementRate: getRandomFloat(2.0, 12.0),
      tiktokAvgViews: Math.floor(followersBase * getRandomFloat(0.5, 2.0)),
    }),
    
    // YouTube (opcional)
    ...(hasYoutube && {
      youtubeUsername: `${name} Official`,
      youtubeFollowers: Math.floor(followersBase * getRandomFloat(0.1, 0.8)),
      youtubeEngagementRate: getRandomFloat(0.5, 4.0),
      youtubeAvgViews: Math.floor(followersBase * getRandomFloat(0.05, 0.3)),
    }),
    
    // Meta
    userId,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDemoData: true
  };

  return influencer;
}

/**
 * Gera pricing para um influenciador
 */
function generateDemoPricing(influencer: DemoInfluencer): DemoPricing {
  const basePrice = Math.floor(influencer.instagramFollowers / 1000) * getRandomFloat(0.8, 2.5);
  
  const pricing: DemoPricing = {
    influencerId: '', // Será preenchido depois
    services: {
      instagram: {
        story: { price: Math.floor(basePrice * 0.3), currency: 'BRL' },
        reel: { price: Math.floor(basePrice * 0.8), currency: 'BRL' },
        post: { price: Math.floor(basePrice * 0.6), currency: 'BRL' }
      }
    },
    isActive: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 ano
    notes: 'Pricing gerado automaticamente para demonstração',
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: influencer.userId,
    isDemoData: true
  };

  // TikTok pricing (se tem TikTok)
  if (influencer.tiktokFollowers) {
    const tiktokBasePrice = Math.floor(influencer.tiktokFollowers / 1000) * getRandomFloat(1.2, 3.0);
    pricing.services.tiktok = {
      video: { price: Math.floor(tiktokBasePrice), currency: 'BRL' }
    };
  }

  // YouTube pricing (se tem YouTube)
  if (influencer.youtubeFollowers) {
    const youtubeBasePrice = Math.floor(influencer.youtubeFollowers / 1000) * getRandomFloat(2.0, 5.0);
    pricing.services.youtube = {
      insertion: { price: Math.floor(youtubeBasePrice * 0.5), currency: 'BRL' },
      dedicated: { price: Math.floor(youtubeBasePrice * 1.5), currency: 'BRL' },
      shorts: { price: Math.floor(youtubeBasePrice * 0.3), currency: 'BRL' }
    };
  }

  return pricing;
}

/**
 * Gera demographics para um influenciador
 */
function generateDemoDemographics(influencer: DemoInfluencer): DemoDemographics[] {
  const demographics: DemoDemographics[] = [];
  
  // Demographics para Instagram
  demographics.push({
    influencerId: '', // Será preenchido depois
    platform: 'instagram',
    audienceGender: {
      male: influencer.gender === 'masculino' ? getRandomFloat(35, 60) : getRandomFloat(20, 45),
      female: influencer.gender === 'feminino' ? getRandomFloat(55, 80) : getRandomFloat(40, 65),
      other: getRandomFloat(0, 5)
    },
    audienceLocations: [
      { location: 'Brasil', percentage: getRandomFloat(60, 85) },
      { location: 'Portugal', percentage: getRandomFloat(5, 15) },
      { location: 'Estados Unidos', percentage: getRandomFloat(2, 8) },
      { location: 'Argentina', percentage: getRandomFloat(1, 5) }
    ],
    audienceCities: [
      { city: 'São Paulo', percentage: getRandomFloat(15, 25) },
      { city: 'Rio de Janeiro', percentage: getRandomFloat(10, 20) },
      { city: 'Belo Horizonte', percentage: getRandomFloat(5, 12) },
      { city: influencer.city, percentage: getRandomFloat(8, 18) }
    ],
    audienceAgeRange: [
      { range: '13-17', percentage: getRandomFloat(5, 15) },
      { range: '18-24', percentage: getRandomFloat(25, 40) },
      { range: '25-34', percentage: getRandomFloat(30, 45) },
      { range: '35-44', percentage: getRandomFloat(10, 25) },
      { range: '45+', percentage: getRandomFloat(2, 10) }
    ],
    captureDate: new Date(),
    isActive: true,
    source: 'demo_generator',
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: influencer.userId,
    isDemoData: true
  });

  return demographics;
}

/**
 * Gera todos os influenciadores demo
 */
async function generateInfluencers(userId: string, count: number = 200) {
  console.log(`🚀 Gerando ${count} influenciadores demo para usuário ${userId}...`);
  
  let batch = db.batch();
  let batchCount = 0;
  let totalCreated = 0;
  
  for (let i = 0; i < count; i++) {
    try {
      // Gerar influenciador
      const influencer = generateDemoInfluencer(userId, i);
      const influencerRef = db.collection('influencers').doc();
      batch.set(influencerRef, influencer);
      batchCount++;
      
      // Gerar pricing
      const pricing = generateDemoPricing(influencer);
      pricing.influencerId = influencerRef.id;
      const pricingRef = db.collection('influencer_pricing').doc();
      batch.set(pricingRef, pricing);
      batchCount++;
      
      // Gerar demographics
      const demographics = generateDemoDemographics(influencer);
      for (const demo of demographics) {
        demo.influencerId = influencerRef.id;
        const demoRef = db.collection('audience_demographics').doc();
        batch.set(demoRef, demo);
        batchCount++;
      }
      
      // Commit batch quando chegar ao limite
      if (batchCount >= 450) { // 500 é o limite, deixando margem
        await batch.commit();
        totalCreated += Math.floor(batchCount / 3); // Aproximadamente
        console.log(`✅ Batch commitado - ${totalCreated} influenciadores criados`);
        
        // Criar novo batch
        batch = db.batch();
        batchCount = 0;
      }
      
    } catch (error) {
      console.error(`❌ Erro ao gerar influenciador ${i}:`, error);
    }
  }
  
  // Commit batch final
  if (batchCount > 0) {
    await batch.commit();
    totalCreated += Math.floor(batchCount / 3);
  }
  
  console.log(`🎉 Geração completa! ${totalCreated} influenciadores criados com dados completos.`);
  
  // Atualizar contadores de estatísticas
  try {
    console.log('📊 Atualizando contadores de estatísticas...');
    const { statsCache } = await import('../lib/stats-cache-service');
    statsCache.invalidateCache(userId);
    console.log('✅ Cache de estatísticas invalidado');
  } catch (error) {
    console.error('⚠️ Erro ao invalidar cache:', error);
  }
}

/**
 * Limpa todos os dados demo de um usuário
 */
async function cleanupDemoData(userId: string) {
  console.log(`🧹 Limpando dados demo para usuário ${userId}...`);
  
  try {
    // Buscar todos os influenciadores demo
    const influencersQuery = await db.collection('influencers')
      .where('userId', '==', userId)
      .where('isDemoData', '==', true)
      .get();
    
    console.log(`📋 Encontrados ${influencersQuery.size} influenciadores demo`);
    
    const influencerIds = influencersQuery.docs.map(doc => doc.id);
    
    // Limpar em batches
    const batchSize = 100;
    let deleted = 0;
    
    for (let i = 0; i < influencerIds.length; i += batchSize) {
      const batch = db.batch();
      const currentBatch = influencerIds.slice(i, i + batchSize);
      
      // Deletar influenciadores
      for (const id of currentBatch) {
        batch.delete(db.collection('influencers').doc(id));
      }
      
      await batch.commit();
      deleted += currentBatch.length;
      console.log(`🗑️ ${deleted}/${influencerIds.length} influenciadores deletados`);
    }

    // Deletar pricing relacionado - independente do batch de influenciadores
    const pricingQuery = await db.collection('influencer_pricing')
      .where('userId', '==', userId)
      .where('isDemoData', '==', true)
      .get();
    
    console.log(`📊 Encontrados ${pricingQuery.size} registros de pricing para limpar`);
    
    let deletedPricing = 0;
    for (let i = 0; i < pricingQuery.docs.length; i += batchSize) {
      const batch = db.batch();
      const currentBatch = pricingQuery.docs.slice(i, i + batchSize);
      
      for (const doc of currentBatch) {
        batch.delete(doc.ref);
      }
      
      await batch.commit();
      deletedPricing += currentBatch.length;
      console.log(`🗑️ ${deletedPricing}/${pricingQuery.size} registros de pricing deletados`);
    }
    
    // Deletar demographics relacionado - independente do batch de influenciadores
    const demoQuery = await db.collection('audience_demographics')
      .where('userId', '==', userId)
      .where('isDemoData', '==', true)
      .get();
    
    console.log(`📊 Encontrados ${demoQuery.size} registros de demographics para limpar`);
    
    let deletedDemo = 0;
    for (let i = 0; i < demoQuery.docs.length; i += batchSize) {
      const batch = db.batch();
      const currentBatch = demoQuery.docs.slice(i, i + batchSize);
      
      for (const doc of currentBatch) {
        batch.delete(doc.ref);
      }
      
      await batch.commit();
      deletedDemo += currentBatch.length;
      console.log(`🗑️ ${deletedDemo}/${demoQuery.size} registros de demographics deletados`);
    }
    
    console.log('🎉 Limpeza completa!');
    
    // Invalidar cache
    try {
      const { statsCache } = await import('../lib/stats-cache-service');
      statsCache.invalidateCache(userId);
      console.log('✅ Cache de estatísticas invalidado');
    } catch (error) {
      console.error('⚠️ Erro ao invalidar cache:', error);
    }
    
  } catch (error) {
    console.error('❌ Erro durante limpeza:', error);
    throw error;
  }
}

/**
 * Função principal
 */
async function main() {
  const args = process.argv.slice(2);
  
  let userId = '';
  let shouldGenerate = false;
  let shouldCleanup = false;
  let count = 200;
  
  // Parsear argumentos
  for (const arg of args) {
    if (arg.startsWith('--userId=')) {
      userId = arg.split('=')[1];
    } else if (arg === '--generate') {
      shouldGenerate = true;
    } else if (arg === '--cleanup') {
      shouldCleanup = true;
    } else if (arg.startsWith('--count=')) {
      count = parseInt(arg.split('=')[1]) || 200;
    }
  }
  
  if (!userId) {
    console.error('❌ Parâmetro --userId é obrigatório');
    console.log('📋 Uso:');
    console.log('  Gerar: npx ts-node scripts/generate-demo-influencers.ts --generate --userId=USER_ID [--count=200]');
    console.log('  Limpar: npx ts-node scripts/generate-demo-influencers.ts --cleanup --userId=USER_ID');
    process.exit(1);
  }
  
  if (shouldGenerate && shouldCleanup) {
    console.error('❌ Use apenas --generate OU --cleanup, não ambos');
    process.exit(1);
  }
  
  if (!shouldGenerate && !shouldCleanup) {
    console.error('❌ Especifique --generate ou --cleanup');
    process.exit(1);
  }
  
  console.log('🎯 Configuração:');
  console.log(`  👤 Usuário: ${userId}`);
  console.log(`  🔧 Ação: ${shouldGenerate ? 'Gerar' : 'Limpar'}`);
  if (shouldGenerate) {
    console.log(`  📊 Quantidade: ${count}`);
  }
  console.log();
  
  try {
    if (shouldGenerate) {
      await generateInfluencers(userId, count);
    } else if (shouldCleanup) {
      await cleanupDemoData(userId);
    }
  } catch (error) {
    console.error('❌ Erro durante execução:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main()
    .then(() => {
      console.log('✅ Script finalizado com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script falhou:', error);
      process.exit(1);
    });
} 

