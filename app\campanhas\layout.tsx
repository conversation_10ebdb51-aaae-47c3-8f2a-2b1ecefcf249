'use client';

import React, { useState, useEffect, Suspense } from 'react';

// Força renderização dinâmica para evitar problemas de SSG
export const dynamic = 'force-dynamic';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';
import { SideMenu } from '@/components/ui/sidemenu';
import { toast } from '@/components/ui/use-toast';
import { Loader2, AlertTriangle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

// ✅ CORREÇÃO: Importar hook de autenticação para isolamento
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';

interface Group {
  id: string;
  name: string;
  description: string;
  influencers: string[];
  userId: string; // ✅ CAMPO OBRIGATÓRIO PARA ISOLAMENTO
}

function CampanhasLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  
  // ✅ CORREÇÃO: Hook de autenticação para isolamento
  const { currentUser, firebaseUser, isLoading: authLoading } = useFirebaseAuth();
  
  // ⚠️ CORREÇÃO: Usar currentUser.id como brandId se não especificado na URL
  const urlBrandId = searchParams.get('brandId') || searchParams.get('brand');
  const brandId = urlBrandId || currentUser?.id;
  const brandName = searchParams.get('brandName') || currentUser?.name || 'Minha Marca';
  
  const [activeSection, setActiveSection] = useState('influencers');
  // ✅ CORREÇÃO: Grupos agora são gerenciados localmente ou via outro método
  const [createdGroups, setCreatedGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ✅ VERIFICAÇÃO DE AUTENTICAÇÃO E AUTORIZAÇÃO
  useEffect(() => {
    // Se está carregando autenticação, aguardar
    if (authLoading) {
      return;
    }

    // Se não há usuário autenticado, redirecionar para login
    if (!currentUser || !firebaseUser) {
      toast({
        title: "Acesso Negado",
        description: "Você precisa estar logado para acessar campanhas.",
        variant: "destructive",
      });
      router.push('/login');
      return;
    }

    // ✅ VALIDAÇÃO DE ACESSO À MARCA
    if (urlBrandId && urlBrandId !== currentUser.id) {
      // Verificar se usuário tem permissão para acessar esta marca
      // Em implementação futura, verificar relacionamento user-brand
      console.warn('⚠️ Usuário tentando acessar marca diferente:', {
        userId: currentUser.id,
        requestedBrandId: urlBrandId
      });
      
      // Por enquanto, permitir apenas acesso à própria marca
      setError(`Acesso negado à marca ${urlBrandId}. Você só pode acessar sua própria marca.`);
      return;
    }

    setLoading(false);
  }, [currentUser, firebaseUser, authLoading, urlBrandId, router]);

  // Determinar seção ativa baseada na URL
  useEffect(() => {
    if (pathname.includes('/grupos')) {
      setActiveSection('groups');
    } else if (pathname.includes('/propostas')) {
      setActiveSection('proposals');
    } else if (pathname.includes('/campanhas')) {
      setActiveSection('campaigns');
    } else if (pathname.includes('/dashboard')) {
      setActiveSection('influencers');
    } else {
      setActiveSection('influencers');
    }
  }, [pathname]);

  // ✅ FUNÇÃO PARA NAVEGAR COM ISOLAMENTO
  const handleSectionChange = (section: string) => {
    if (!currentUser) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado.",
        variant: "destructive",
      });
      return;
    }

    const params = new URLSearchParams();
    
    // ✅ SEMPRE USAR O ID DO USUÁRIO ATUAL COMO BRAND ID
    params.set('brandId', currentUser.id);
    params.set('brandName', currentUser.name || 'Minha Marca');
    
    let targetUrl = '';
    
    switch (section) {
      case 'influencers':
        targetUrl = '/campanhas/dashboard';
        break;
      case 'groups':
        targetUrl = '/campanhas/grupos';
        break;
      case 'proposals':
        targetUrl = '/propostas';
        break;
      case 'campaigns':
        targetUrl = '/campanhas/campanhas';
        break;
      case 'overview':
        targetUrl = '/campanhas/overview';
        break;
      case 'brands':
        targetUrl = '/campanhas/marcas';
        break;
      case 'tags':
        targetUrl = '/campanhas/tags';
        break;
      case 'notes':
        targetUrl = '/campanhas/notes';
        break;
      case 'settings':
        targetUrl = '/campanhas/configuracoes';
        break;
      default:
        targetUrl = '/campanhas/dashboard';
    }
    
    targetUrl += '?' + params.toString();
    router.push(targetUrl);
  };

  // ⏳ LOADING DE AUTENTICAÇÃO
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen dark:bg-[#080210] bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
          <p className="text-muted-foreground">
            {authLoading ? 'Verificando autenticação...' : 'Carregando campanhas...'}
          </p>
        </div>
      </div>
    );
  }

  // ❌ ERRO DE ACESSO
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-6">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center gap-4 text-center">
              <AlertTriangle className="h-12 w-12 text-red-500" />
              <h2 className="text-xl font-semibold">Acesso Negado</h2>
              <p className="text-muted-foreground">{error}</p>
              <Button 
                onClick={() => router.push('/dashboard')}
                variant="outline"
              >
                Voltar ao Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // ❌ USUÁRIO NÃO AUTENTICADO
  if (!currentUser || !firebaseUser) {
    return null; // Redirecionamento já foi feito no useEffect
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Side Menu */}
      <SideMenu 
        className="flex-shrink-0"
        onSectionChange={handleSectionChange}
        activeSection={activeSection}
        createdGroups={createdGroups}
      />
      
      {/* Main Content */}
      <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-hidden">
        {children}
      </div>
    </div>
  );
}

export default function CampanhasLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
          <p className="text-muted-foreground">Carregando layout...</p>
        </div>
      </div>
    }>
      <CampanhasLayoutContent>
        {children}
      </CampanhasLayoutContent>
    </Suspense>
  );
} 


