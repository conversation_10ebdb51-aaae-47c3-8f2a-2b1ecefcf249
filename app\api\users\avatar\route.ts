import { NextRequest, NextResponse } from 'next/server';
import { uploadUserAvatar } from '@/lib/firebase-storage';
import { updateUserAvatar } from '@/lib/firebase-admin';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Obter dados do formulário
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const userId = formData.get('userId') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'Arquivo é obrigatório' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'ID do usuário é obrigatório' },
        { status: 400 }
      );
    }

    // Validar tipo de arquivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Tipo de arquivo não suportado. Use apenas JPEG, PNG ou WebP.' },
        { status: 400 }
      );
    }

    // Validar tamanho do arquivo (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Arquivo muito grande. Máximo 5MB.' },
        { status: 400 }
      );
    }

    console.log(`Fazendo upload do avatar do usuário ${userId}`);

    // Fazer upload do avatar para o Firebase Storage
    const avatarUrl = await uploadUserAvatar(file, userId);

    console.log(`Upload do avatar concluído. URL: ${avatarUrl}`);

    // Atualizar o documento do usuário no Firestore com a nova URL do avatar
    await updateUserAvatar(userId, avatarUrl);

    console.log(`Avatar do usuário ${userId} salvo no Firestore`);

    return NextResponse.json({ 
      success: true, 
      avatarUrl,
      message: 'Avatar atualizado com sucesso!'
    });

  } catch (error) {
    console.error('Erro no upload do avatar:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 

