'use client';

import { useAuth as useClerk<PERSON><PERSON>, useUser, useOrganizationList, useOrganization } from '@clerk/nextjs';
import { useState, useEffect, useMemo, useCallback } from 'react';

export interface UserOrganization {
  id: string;
  name: string;
  role: string;
  status: string;
  type?: string;
  description?: string;
}

export interface AuthWithOrganizations {
  user: any;
  currentOrganization: UserOrganization | null;
  organizations: UserOrganization[];
  organizationId: string | null;
  isLoading: boolean;
  hasMultipleOrganizations: boolean;
  switchOrganization: (organizationId: string) => void;
  refreshOrganizations: () => Promise<void>;
}

export function useAuthWithOrganizations(): AuthWithOrganizations {
  const { user } = useUser();
  const { isLoaded } = useClerkAuth();
  const { organization: currentOrganization } = useOrganization();
  const { 
    isLoaded: isOrganizationListLoaded, 
    setActive, 
    userMemberships 
  } = useOrganizationList({
    userMemberships: {
      infinite: true,
    },
  });
  
  const [isLoading, setIsLoading] = useState(true);

  // Atualizar loading baseado nos estados do Clerk
  useEffect(() => {
    setIsLoading(!isLoaded || !isOrganizationListLoaded);
  }, [isLoaded, isOrganizationListLoaded]);

  // ✅ CORREÇÃO: Memoizar organizações para evitar re-criação
  const organizations: UserOrganization[] = useMemo(() => {
    return userMemberships?.data?.map(membership => ({
      id: membership.organization.id,
      name: membership.organization.name,
      role: membership.role,
      status: 'active',
      type: 'organization',
      description: membership.organization.name
    })) || [];
  }, [userMemberships?.data]);

  // ✅ CORREÇÃO: Memoizar organização atual para evitar re-criação
  const currentOrg: UserOrganization | null = useMemo(() => {
    if (!currentOrganization) return null;

    return {
      id: currentOrganization.id,
      name: currentOrganization.name,
      role: userMemberships?.data?.find(m => m.organization.id === currentOrganization.id)?.role || 'member',
      status: 'active',
      type: 'organization',
      description: currentOrganization.name
    };
  }, [currentOrganization, userMemberships?.data]);

  // ✅ CORREÇÃO: Memoizar função para evitar re-criação
  const switchOrganization = useCallback(async (organizationId: string) => {
    if (!setActive) return;

    try {
      await setActive({ organization: organizationId });
      // Recarregar a página para atualizar contexto
      window.location.reload();
    } catch (error) {
      console.error('❌ Erro ao trocar organização:', error);
    }
  }, [setActive]);

  // ✅ CORREÇÃO: Memoizar função para evitar re-criação
  const refreshOrganizations = useCallback(async () => {
    // O Clerk atualiza automaticamente, então não precisamos fazer nada
    return Promise.resolve();
  }, []);

  return {
    user,
    currentOrganization: currentOrg,
    organizations,
    organizationId: currentOrg?.id || null,
    isLoading,
    hasMultipleOrganizations: organizations.length > 1,
    switchOrganization,
    refreshOrganizations
  };
}

// Hook legacy para compatibilidade - removido para evitar conflitos
// Use o hook principal useAuthWithOrganizations ou importe de use-firebase-auth.ts 

