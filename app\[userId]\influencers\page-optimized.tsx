import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import dynamic from 'next/dynamic'
import { Protect } from '@clerk/nextjs'

// 🔥 COMPONENTES SERVER (SEM 'use client')
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

// 🔥 LAZY LOADING ESTRATÉGICO - Componentes pesados carregados sob demanda
const InfluencerGridClient = dynamic(() => import('./components/influencer-grid-client'), {
  ssr: false,
  loading: () => <InfluencerGridSkeleton />
})

const InfluencerPanelClient = dynamic(() => import('./components/influencer-panel-client'), {
  ssr: false,
  loading: () => <InfluencerPanelSkeleton />
})

const FiltersClient = dynamic(() => import('./components/filters-client'), {
  ssr: false,
  loading: () => <FiltersSkeleton />
})

// 🔥 COMPONENTES DE SKELETON PARA LOADING
function InfluencerGridSkeleton() {
  return (
    <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto min-h-0">
      <div className="p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-muted rounded-full" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-1/2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

function InfluencerPanelSkeleton() {
  return (
    <div className="w-96 bg-background border-l border-border overflow-auto">
      <div className="p-4 space-y-4">
        <div className="animate-pulse">
          <div className="h-6 bg-muted rounded w-3/4 mb-4" />
          <div className="space-y-3">
            <div className="h-4 bg-muted rounded" />
            <div className="h-4 bg-muted rounded w-5/6" />
            <div className="h-4 bg-muted rounded w-4/6" />
          </div>
        </div>
      </div>
    </div>
  )
}

function FiltersSkeleton() {
  return (
    <div className="w-80 bg-background border-r border-border overflow-auto">
      <div className="p-4 space-y-4">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded w-1/2" />
          <div className="space-y-2">
            <div className="h-10 bg-muted rounded" />
            <div className="h-10 bg-muted rounded" />
            <div className="h-10 bg-muted rounded" />
          </div>
        </div>
      </div>
    </div>
  )
}

// 🔥 METADATA OTIMIZADA
export const metadata: Metadata = {
  title: 'Influenciadores - Deu Match',
  description: 'Encontre e gerencie influenciadores para suas campanhas de marketing',
  keywords: ['influenciadores', 'marketing', 'campanhas', 'creators'],
}

// 🔥 COMPONENTE PRINCIPAL - SERVER COMPONENT
export default function InfluencersPageOptimized({
  params,
  searchParams
}: {
  params: { userId: string }
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const { userId } = params

  return (
    <Protect
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Card className="w-96">
            <CardHeader>
              <CardTitle>Acesso Restrito</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Você precisa estar logado para acessar esta página.</p>
            </CardContent>
          </Card>
        </div>
      }
    >
      <div className="flex h-screen bg-background">
        {/* 🔥 SIDEBAR DE FILTROS - Lazy loaded */}
        <Suspense fallback={<FiltersSkeleton />}>
          <FiltersClient />
        </Suspense>

        {/* 🔥 ÁREA PRINCIPAL */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* 🔥 HEADER ESTÁTICO - Server Component */}
          <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-14 items-center px-4">
              <div className="flex items-center space-x-4">
                <h1 className="text-lg font-semibold">Influenciadores</h1>
                <Separator orientation="vertical" className="h-6" />
                <Badge variant="secondary" className="text-xs">
                  {userId}
                </Badge>
              </div>
            </div>
          </header>

          {/* 🔥 CONTEÚDO PRINCIPAL */}
          <div className="flex-1 flex min-h-0">
            {/* 🔥 GRID DE INFLUENCIADORES - Lazy loaded */}
            <Suspense fallback={<InfluencerGridSkeleton />}>
              <InfluencerGridClient userId={userId} />
            </Suspense>

            {/* 🔥 PAINEL LATERAL - Lazy loaded */}
            <Suspense fallback={<InfluencerPanelSkeleton />}>
              <InfluencerPanelClient userId={userId} />
            </Suspense>
          </div>
        </div>
      </div>
    </Protect>
  )
}
