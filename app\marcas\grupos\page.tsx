'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

// Força renderização dinâmica para evitar problemas de SSG
export const dynamic = 'force-dynamic';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, CheckSquare, FileText, X, Instagram, Plus, Eye, Send, BarChart3 } from 'lucide-react';
import { CreateGroupDialog } from '@/components/campanhas/dialogs';
import { toast } from '@/components/ui/use-toast';

interface Influencer {
  id: string;
  nome: string;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
}

interface BrandInfluencer {
  id: string;
  brandId: string;
  brandName: string;
  influencerId: string;
  influencerName: string;
  influencerData: Influencer;
  status: 'enviado' | 'visualizado' | 'interessado' | 'rejeitado' | 'proposta_enviada';
  sentAt: Date;
  viewedAt?: Date;
  lastInteractionAt?: Date;
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

function GruposPageContent() {
  const searchParams = useSearchParams();
  const brandId = searchParams.get('brandId') || searchParams.get('brand');
  const brandName = searchParams.get('brandName');

  const [createdGroups, setCreatedGroups] = useState<Array<{id: string, name: string, description: string, influencers: string[]}>>([]);
  const [brandInfluencers, setBrandInfluencers] = useState<BrandInfluencer[]>([]);
  const [influencers, setInfluencers] = useState<Influencer[]>([]);
  const [showGroupDialog, setShowGroupDialog] = useState(false);
  const [groupInfluencers, setGroupInfluencers] = useState<string[]>([]);
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupLoading, setGroupLoading] = useState(false);
  const [loading, setLoading] = useState(true);

  // Função para carregar grupos
  const loadGroups = async () => {
    try {
      const response = await fetch(`/api/groups?brandId=${brandId}&createdBy=current-user`);
      
      if (!response.ok) {
        console.warn(`Erro ao carregar grupos: ${response.status} ${response.statusText}`);
        return;
      }
      
      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        console.warn('Erro ao fazer parse da resposta dos grupos:', parseError);
        return;
      }
      
      if (result && result.success && Array.isArray(result.data)) {
        setCreatedGroups(result.data.map((group: any) => ({
          id: group.id || '',
          name: group.name || 'Grupo sem nome',
          description: group.description || '',
          influencers: Array.isArray(group.influencerIds) ? group.influencerIds : []
        })));
      } else {
        console.warn('Resposta inválida da API de grupos:', result);
      }
    } catch (error) {
      console.warn('Erro na requisição de grupos:', error);
    }
  };

  // Função para carregar influenciadores da marca
  const loadBrandInfluencers = async () => {
    if (!brandId) return;

    try {
              const response = await fetch(`/api/campanhas/influencers?brandId=${brandId}`);
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Erro ao fazer parse da resposta de erro:', parseError);
          errorData = { error: `Erro HTTP ${response.status}: ${response.statusText}` };
        }
        throw new Error(errorData.error || `Erro ao carregar influenciadores (${response.status})`);
      }

      const result = await response.json();
      
      const data = result.data || [];
      setBrandInfluencers(data);
      
      const influencerData = data.map((bi: BrandInfluencer) => bi.influencerData);
      setInfluencers(influencerData);
    } catch (error) {
      console.error('Erro ao carregar influenciadores da marca:', error);
    }
  };

  // Função para criar grupo
  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      toast({
        title: "Erro",
        description: "Nome do grupo é obrigatório",
        variant: "destructive"
      });
      return;
    }

    setGroupLoading(true);
    try {
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: groupName,
          description: groupDescription,
          influencerIds: groupInfluencers,
          brandId: brandId,
          createdBy: 'current-user'
        }),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Erro ao fazer parse da resposta de erro:', parseError);
          errorData = { error: `Erro HTTP ${response.status}: ${response.statusText}` };
        }
        throw new Error(errorData.error || `Erro ao criar grupo (${response.status})`);
      }

      const result = await response.json();
      
      const newGroup = {
        id: result.groupId,
        name: groupName,
        description: groupDescription,
        influencers: groupInfluencers
      };
      
      setCreatedGroups(prev => [...prev, newGroup]);
      
      setGroupName('');
      setGroupDescription('');
      setGroupInfluencers([]);
      setShowGroupDialog(false);
      
      toast({
        title: "Sucesso",
        description: result.message || `Grupo "${groupName}" criado com ${groupInfluencers.length} influenciador(es)`,
      });
    } catch (error) {
      console.error('Erro ao criar grupo:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao criar grupo",
        variant: "destructive"
      });
    } finally {
      setGroupLoading(false);
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    const loadData = async () => {
      try {
        if (brandId) {
          await loadBrandInfluencers();
          await loadGroups();
        }
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar dados da marca.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [brandId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="h-full p-6 space-y-6 overflow-y-auto">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header de Estatísticas dos Grupos */}
        <div className="bg-background/50 rounded-lg border p-6">
          <div className="flex items-center justify-between gap-6">
            {/* Estatística: Perfis */}
            <div className="text-center">
              <div className="text-3xl font-bold text-foreground">
                {createdGroups.reduce((total, group) => total + group.influencers.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground font-medium flex items-center justify-center gap-1">
                <Users className="h-4 w-4" />
                Perfis
              </div>
            </div>
            
            {/* Estatística: Aceitos */}
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {brandInfluencers.filter(bi => bi.status === 'interessado').length}
              </div>
              <div className="text-sm text-muted-foreground font-medium flex items-center justify-center gap-1">
                <CheckSquare className="h-4 w-4 text-green-600" />
                Aceitos
              </div>
            </div>
            
            {/* Estatística: Em reserva */}
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600">
                {brandInfluencers.filter(bi => bi.status === 'visualizado').length}
              </div>
              <div className="text-sm text-muted-foreground font-medium flex items-center justify-center gap-1">
                <FileText className="h-4 w-4 text-yellow-600" />
                Em reserva
              </div>
            </div>
            
            {/* Estatística: Descartados */}
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">
                {brandInfluencers.filter(bi => bi.status === 'rejeitado').length}
              </div>
              <div className="text-sm text-muted-foreground font-medium flex items-center justify-center gap-1">
                <X className="h-4 w-4 text-red-600" />
                Descartados
              </div>
            </div>
            
            {/* Estatística: Instagram */}
            <div className="text-center">
              <div className="text-3xl font-bold text-[#ff0074]">
                {influencers.filter(inf => inf && inf.redesSociais?.instagram).length}
              </div>
              <div className="text-sm text-muted-foreground font-medium flex items-center justify-center gap-1">
                <Instagram className="h-4 w-4 text-[#ff0074]" />
                Instagram
              </div>
            </div>
          </div>
        </div>

        {/* Lista de Grupos */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Grupos de Influenciadores</h1>
              <p className="text-muted-foreground">
                Organize e gerencie seus grupos de influenciadores para campanhas
              </p>
            </div>
            <Button
              variant="default"
              className="bg-[#ff0074] hover:bg-[#ff0074]/90"
              onClick={() => setShowGroupDialog(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Criar Nova Proposta
            </Button>
          </div>

          {createdGroups.length === 0 ? (
            <Card className="p-12">
              <div className="text-center">
                <Users className="h-16 w-16 mx-auto text-muted-foreground mb-6" />
                <h2 className="text-2xl font-semibold mb-4">Nenhum grupo criado</h2>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Crie grupos para organizar seus influenciadores e facilitar o gerenciamento de campanhas. 
                  Você pode agrupar por categoria, localização ou tipo de conteúdo.
                </p>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setShowGroupDialog(true)}
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Criar Primeira Proposta
                </Button>
              </div>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {createdGroups.map((group) => (
                <Card key={group.id} className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-lg">{group.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {group.influencers.length} influenciador(es)
                      </p>
                    </div>
                    <Badge variant="secondary" className="bg-[#ff0074]/10 text-[#ff0074]">
                      {group.influencers.length}
                    </Badge>
                  </div>
                  
                  {group.description && (
                    <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                      {group.description}
                    </p>
                  )}
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      Ver Detalhes
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1 hover:bg-[#9810fa]/10 hover:text-[#9810fa] hover:border-[#9810fa]/50"
                    >
                      <Send className="h-4 w-4 mr-1" />
                      Criar Proposta
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Dialog para Criar Grupo */}
      <CreateGroupDialog
        open={showGroupDialog}
        onOpenChange={setShowGroupDialog}
        selectedInfluencers={groupInfluencers}
        influencers={influencers}
        groupName={groupName}
        setGroupName={setGroupName}
        groupDescription={groupDescription}
        setGroupDescription={setGroupDescription}
        onCreateGroup={handleCreateGroup}
        loading={groupLoading}
      />
    </div>
  );
}

export default function GruposPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
          <p className="text-muted-foreground">Carregando grupos...</p>
        </div>
      </div>
    }>
      <GruposPageContent />
    </Suspense>
  );
} 


