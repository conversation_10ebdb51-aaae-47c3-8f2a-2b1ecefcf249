'use client';

import { useTranslations } from '@/hooks/use-translations';
import { useLanguage } from '@/contexts/language-context';

export function TranslationTest() {
  const { t, isLoading, locale, messages } = useTranslations();
  const { availableLocales, setLocale } = useLanguage();

  return (
    <div className="p-4 border rounded-lg space-y-4 bg-muted/50">
      <h3 className="text-lg font-semibold">🌐 Teste de Traduções</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p><strong>Idioma atual:</strong> {locale}</p>
          <p><strong>Carregando:</strong> {isLoading ? 'Sim' : 'Não'}</p>
          <p><strong>Mensagens carregadas:</strong> {Object.keys(messages).length}</p>
        </div>
        
        <div>
          <p><strong>Idiomas disponíveis:</strong></p>
          <div className="flex gap-2">
            {availableLocales.map(lang => (
              <button
                key={lang}
                onClick={() => setLocale(lang)}
                className={`px-2 py-1 text-xs rounded ${
                  locale === lang 
                    ? 'bg-[#ff0074] text-white' 
                    : 'bg-background border'
                }`}
              >
                {lang.toUpperCase()}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">🔥 Teste Headers da Tabela (Problema Reportado):</h4>
        <div className="grid grid-cols-2 gap-2 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded">
          <div>
            <strong>table_headers.name:</strong> "{t('influencers.table_headers.name')}"
          </div>
          <div>
            <strong>table_headers.category:</strong> "{t('influencers.table_headers.category')}"
          </div>
          <div>
            <strong>table_headers.location:</strong> "{t('influencers.table_headers.location')}"
          </div>
          <div>
            <strong>table_headers.instagram:</strong> "{t('influencers.table_headers.instagram')}"
          </div>
          <div>
            <strong>table_headers.youtube:</strong> "{t('influencers.table_headers.youtube')}"
          </div>
          <div>
            <strong>table_headers.tiktok:</strong> "{t('influencers.table_headers.tiktok')}"
          </div>
          <div>
            <strong>table_headers.value:</strong> "{t('influencers.table_headers.value')}"
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">Teste de Traduções Gerais:</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <strong>common.loading:</strong> "{t('common.loading')}"
          </div>
          <div>
            <strong>navigation.influencers:</strong> "{t('navigation.influencers')}"
          </div>
          <div>
            <strong>influencers.title:</strong> "{t('influencers.title')}"
          </div>
          <div>
            <strong>common.save:</strong> "{t('common.save')}"
          </div>
          <div>
            <strong>sidebar.portuguese:</strong> "{t('sidebar.portuguese')}"
          </div>
          <div>
            <strong>teste.nao.existe:</strong> "{t('teste.nao.existe')}"
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">Verificação das Mensagens table_headers:</h4>
        <div className="text-xs bg-background p-2 rounded">
          <p><strong>influencers existe:</strong> {messages.influencers ? 'Sim' : 'Não'}</p>
          <p><strong>table_headers existe:</strong> {messages.influencers?.table_headers ? 'Sim' : 'Não'}</p>
          {messages.influencers?.table_headers && (
            <div className="mt-2">
              <strong>Conteúdo table_headers:</strong>
              <pre className="text-xs mt-1">
                {JSON.stringify(messages.influencers.table_headers, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">Estrutura das Mensagens:</h4>
        <details className="text-xs">
          <summary className="cursor-pointer">Ver estrutura completa</summary>
          <pre className="bg-background p-2 rounded overflow-auto max-h-40">
            {JSON.stringify(messages, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
} 