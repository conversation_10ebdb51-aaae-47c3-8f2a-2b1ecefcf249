import React from 'react';
import { useLanguage, type Locale } from '@/contexts/language-context';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe, Check } from 'lucide-react';

const languageLabels: Record<Locale, { label: string; flag: string; nativeName: string }> = {
  pt: { label: 'Português', flag: '🇧🇷', nativeName: 'Português (Brasil)' },
  en: { label: 'English', flag: '🇺🇸', nativeName: 'English (US)' },
  es: { label: 'Español', flag: '🇪🇸', nativeName: 'Español (España)' },
};

interface LanguageSelectorProps {
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
}

export function LanguageSelector({ variant = 'default', className = '' }: LanguageSelectorProps) {
  const { locale, setLocale, availableLocales } = useLanguage();
  const currentLanguage = languageLabels[locale];

  const handleLanguageChange = (newLocale: Locale) => {
    setLocale(newLocale);
  };

  if (variant === 'icon-only') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className={`h-8 w-8 p-0 ${className}`}
            title={`Idioma atual: ${currentLanguage.nativeName}`}
          >
            <Globe className="h-4 w-4" />
            <span className="sr-only">Selecionar idioma</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {availableLocales.map((lang) => {
            const language = languageLabels[lang];
            return (
              <DropdownMenuItem
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <span className="text-lg">{language.flag}</span>
                <span className="flex-1">{language.nativeName}</span>
                {locale === lang && <Check className="h-4 w-4 text-[#ff0074]" />}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={`h-8 gap-1 ${className}`}
          >
            <span className="text-sm">{currentLanguage.flag}</span>
            <span className="text-xs font-medium uppercase">{locale}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {availableLocales.map((lang) => {
            const language = languageLabels[lang];
            return (
              <DropdownMenuItem
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <span className="text-lg">{language.flag}</span>
                <span className="flex-1">{language.nativeName}</span>
                {locale === lang && <Check className="h-4 w-4 text-[#ff0074]" />}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={`gap-2 ${className}`}
        >
          <Globe className="h-4 w-4" />
          <span className="text-sm">{currentLanguage.flag}</span>
          <span className="text-sm font-medium">{currentLanguage.label}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {availableLocales.map((lang) => {
          const language = languageLabels[lang];
          return (
            <DropdownMenuItem
              key={lang}
              onClick={() => handleLanguageChange(lang)}
              className="flex items-center gap-3 cursor-pointer py-2"
            >
              <span className="text-lg">{language.flag}</span>
              <div className="flex-1">
                <div className="font-medium text-sm">{language.label}</div>
                <div className="text-xs text-muted-foreground">{language.nativeName}</div>
              </div>
              {locale === lang && <Check className="h-4 w-4 text-[#ff0074]" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 