import { BrandService } from '@/services/brand-service';
import { CampaignService } from '@/services/campaign-service';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';

/**
 * 🔐 UTILITÁRIOS DE VALIDAÇÃO DE OWNERSHIP
 * FASE 4.4: Validações de ownership para relacionamentos entre entidades
 */

/**
 * Validar se uma brand pertence ao usuário especificado
 */
export async function validateBrandOwnership(brandId: string, userId: string): Promise<void> {
  try {
    const brand = await BrandService.getBrandById(brandId);
    
    if (!brand) {
      throw new IsolationError(
        ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
        'Marca não encontrada',
        userId,
        brandId
      );
    }
    
    if (!IsolationUtils.validateOwnership(brand, userId)) {
      // Log de tentativa de acesso não autorizado
      IsolationUtils.logIsolationEvent(
        'read',
        'brands',
        brandId,
        userId,
        {
          violation: 'ownership_denied',
          resourceOwnerId: (brand as any).userId,
          attemptedAction: 'validate_brand_ownership'
        }
      );

      throw new IsolationError(
        ISOLATION_ERRORS.OWNERSHIP_DENIED,
        'Usuário não tem permissão para acessar esta marca',
        userId,
        brandId
      );
    }

    // Log de validação bem-sucedida
    IsolationUtils.logIsolationEvent(
      'read',
      'brands',
      brandId,
      userId,
      { validation: 'ownership_confirmed' }
    );

  } catch (error) {
    if (error instanceof IsolationError) {
      throw error;
    }
    
    console.error('[OWNERSHIP_VALIDATION_BRAND]', { brandId, userId, error });
    throw new Error(`Erro na validação de ownership da marca: ${error.message}`);
  }
}

/**
 * Validar se uma campanha pertence ao usuário especificado
 */
export async function validateCampaignOwnership(campaignId: string, userId: string): Promise<void> {
  try {
    const campaign = await CampaignService.getCampaign(campaignId);
    
    if (!campaign) {
      throw new IsolationError(
        ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
        'Campanha não encontrada',
        userId,
        campaignId
      );
    }
    
    if (!IsolationUtils.validateOwnership(campaign as any, userId)) {
      // Log de tentativa de acesso não autorizado
      IsolationUtils.logIsolationEvent(
        'read',
        'campaigns',
        campaignId,
        userId,
        {
          violation: 'ownership_denied',
          resourceOwnerId: (campaign as any).userId,
          attemptedAction: 'validate_campaign_ownership'
        }
      );

      throw new IsolationError(
        ISOLATION_ERRORS.OWNERSHIP_DENIED,
        'Usuário não tem permissão para acessar esta campanha',
        userId,
        campaignId
      );
    }

    // Log de validação bem-sucedida
    IsolationUtils.logIsolationEvent(
      'read',
      'campaigns',
      campaignId,
      userId,
      { validation: 'ownership_confirmed' }
    );

  } catch (error) {
    if (error instanceof IsolationError) {
      throw error;
    }
    
    console.error('[OWNERSHIP_VALIDATION_CAMPAIGN]', { campaignId, userId, error });
    throw new Error(`Erro na validação de ownership da campanha: ${error.message}`);
  }
}

/**
 * Validar se uma campanha e sua brand associada pertencem ao usuário
 */
export async function validateCampaignAndBrandOwnership(
  campaignId: string, 
  userId: string
): Promise<{ campaign: any; brand: any }> {
  try {
    // Validar campanha
    const campaign = await CampaignService.getCampaign(campaignId);
    
    if (!campaign) {
      throw new IsolationError(
        ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
        'Campanha não encontrada',
        userId,
        campaignId
      );
    }
    
    if (!IsolationUtils.validateOwnership(campaign as any, userId)) {
      throw new IsolationError(
        ISOLATION_ERRORS.OWNERSHIP_DENIED,
        'Usuário não tem permissão para acessar esta campanha',
        userId,
        campaignId
      );
    }

    // Validar brand associada
    if (!campaign.brandId) {
      throw new IsolationError(
        ISOLATION_ERRORS.RELATIONSHIP_INVALID,
        'Campanha não possui brand associada',
        userId,
        campaignId
      );
    }

    await validateBrandOwnership(campaign.brandId, userId);
    const brand = await BrandService.getBrandById(campaign.brandId);

    return { campaign, brand };

  } catch (error) {
    if (error instanceof IsolationError) {
      throw error;
    }
    
    console.error('[OWNERSHIP_VALIDATION_CAMPAIGN_BRAND]', { campaignId, userId, error });
    throw new Error(`Erro na validação de ownership da campanha e marca: ${error.message}`);
  }
}

/**
 * Validar ownership em lote para múltiplas brands
 */
export async function validateBrandsOwnership(
  brandIds: string[], 
  userId: string
): Promise<{ validBrands: any[]; invalidIds: string[] }> {
  try {
    const validBrands: any[] = [];
    const invalidIds: string[] = [];

    for (const brandId of brandIds) {
      try {
        await validateBrandOwnership(brandId, userId);
        const brand = await BrandService.getBrandById(brandId);
        if (brand) {
          validBrands.push(brand);
        }
      } catch (error) {
        invalidIds.push(brandId);
        console.warn(`[BATCH_BRAND_VALIDATION] Brand ${brandId} inválida para usuário ${userId}`);
      }
    }

    if (invalidIds.length > 0) {
      IsolationUtils.logIsolationEvent(
        'read',
        'brands',
        'batch_validation',
        userId,
        {
          totalRequested: brandIds.length,
          validCount: validBrands.length,
          invalidIds,
          violation: 'batch_ownership_partial_failure'
        }
      );
    }

    return { validBrands, invalidIds };

  } catch (error) {
    console.error('[OWNERSHIP_VALIDATION_BRANDS_BATCH]', { brandIds, userId, error });
    throw new Error(`Erro na validação em lote de brands: ${error.message}`);
  }
}

/**
 * Validar ownership em lote para múltiplas campanhas
 */
export async function validateCampaignsOwnership(
  campaignIds: string[], 
  userId: string
): Promise<{ validCampaigns: any[]; invalidIds: string[] }> {
  try {
    const validCampaigns: any[] = [];
    const invalidIds: string[] = [];

    for (const campaignId of campaignIds) {
      try {
        await validateCampaignOwnership(campaignId, userId);
        const campaign = await CampaignService.getCampaign(campaignId);
        if (campaign) {
          validCampaigns.push(campaign);
        }
      } catch (error) {
        invalidIds.push(campaignId);
        console.warn(`[BATCH_CAMPAIGN_VALIDATION] Campaign ${campaignId} inválida para usuário ${userId}`);
      }
    }

    if (invalidIds.length > 0) {
      IsolationUtils.logIsolationEvent(
        'read',
        'campaigns',
        'batch_validation',
        userId,
        {
          totalRequested: campaignIds.length,
          validCount: validCampaigns.length,
          invalidIds,
          violation: 'batch_ownership_partial_failure'
        }
      );
    }

    return { validCampaigns, invalidIds };

  } catch (error) {
    console.error('[OWNERSHIP_VALIDATION_CAMPAIGNS_BATCH]', { campaignIds, userId, error });
    throw new Error(`Erro na validação em lote de campanhas: ${error.message}`);
  }
}

/**
 * Validar se um usuário pode criar uma campanha para uma brand específica
 */
export async function validateCampaignCreationPermission(
  brandId: string, 
  userId: string
): Promise<any> {
  try {
    // Validar ownership da brand
    await validateBrandOwnership(brandId, userId);
    
    const brand = await BrandService.getBrandById(brandId);
    
    if (!brand) {
      throw new IsolationError(
        ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
        'Marca não encontrada para criação de campanha',
        userId,
        brandId
      );
    }

    // Verificar se brand está em status que permite criação de campanhas
    if ((brand as any).status === 'archived' || (brand as any).status === 'inactive') {
      throw new IsolationError(
        ISOLATION_ERRORS.VALIDATION_ERROR,
        'Não é possível criar campanhas para marcas inativas ou arquivadas',
        userId,
        brandId,
        { brandStatus: (brand as any).status }
      );
    }

    // Log de validação de permissão
    IsolationUtils.logIsolationEvent(
      'read',
      'brands',
      brandId,
      userId,
      { 
        validation: 'campaign_creation_permission_granted',
        brandName: brand.name 
      }
    );

    return brand;

  } catch (error) {
    if (error instanceof IsolationError) {
      throw error;
    }
    
    console.error('[OWNERSHIP_VALIDATION_CAMPAIGN_CREATION]', { brandId, userId, error });
    throw new Error(`Erro na validação de permissão para criação de campanha: ${error.message}`);
  }
}

/**
 * Middleware de validação genérico para ownership
 */
export function createOwnershipValidator<T>(
  getResourceFunction: (id: string) => Promise<T | null>,
  resourceType: string
) {
  return async (resourceId: string, userId: string): Promise<T> => {
    try {
      const resource = await getResourceFunction(resourceId);
      
      if (!resource) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          `${resourceType} não encontrado`,
          userId,
          resourceId
        );
      }
      
      if (!IsolationUtils.validateOwnership(resource as any, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          `Usuário não tem permissão para acessar este ${resourceType.toLowerCase()}`,
          userId,
          resourceId
        );
      }
      
      return resource;
    } catch (error) {
      if (error instanceof IsolationError) {
        throw error;
      }
      
      console.error(`[OWNERSHIP_VALIDATOR_${resourceType.toUpperCase()}]`, { resourceId, userId, error });
      throw new Error(`Erro na validação de ownership do ${resourceType.toLowerCase()}: ${error.message}`);
    }
  };
}

/**
 * Validadores específicos usando o factory pattern
 */
export const validateInfluencerOwnership = createOwnershipValidator(
  async (id: string) => {
    // TODO: Implementar quando InfluencerService estiver disponível
    // return await InfluencerService.getInfluencerById(id);
    return null;
  },
  'Influencer'
);

export const validateGroupOwnership = createOwnershipValidator(
  async (id: string) => {
    // TODO: Implementar quando GroupService estiver disponível
    // return await GroupService.getGroupById(id);
    return null;
  },
  'Group'
);

/**
 * Utilitário para validar relacionamentos complexos
 */
export async function validateComplexRelationship(
  validations: Array<{
    resourceId: string;
    resourceType: 'brand' | 'campaign' | 'influencer' | 'group';
    userId: string;
  }>
): Promise<{ valid: boolean; results: any[]; errors: string[] }> {
  const results: any[] = [];
  const errors: string[] = [];

  for (const validation of validations) {
    try {
      let result = null;
      
      switch (validation.resourceType) {
        case 'brand':
          await validateBrandOwnership(validation.resourceId, validation.userId);
          result = await BrandService.getBrandById(validation.resourceId);
          break;
        case 'campaign':
          await validateCampaignOwnership(validation.resourceId, validation.userId);
          result = await CampaignService.getCampaign(validation.resourceId);
          break;
        case 'influencer':
          // TODO: Implementar quando InfluencerService estiver disponível
          break;
        case 'group':
          // TODO: Implementar quando GroupService estiver disponível
          break;
      }
      
      if (result) {
        results.push(result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      errors.push(`${validation.resourceType}:${validation.resourceId} - ${errorMessage}`);
    }
  }

  return {
    valid: errors.length === 0,
    results,
    errors
  };
} 

