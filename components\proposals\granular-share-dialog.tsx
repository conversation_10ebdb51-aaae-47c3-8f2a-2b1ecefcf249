'use client'

import { useState } from 'react'
import { useUser, useOrganization } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Copy, Share2, Users, Lock, Eye, DollarSign, Phone, Mail } from 'lucide-react'
import { toast } from 'sonner'

interface ProposalData {
  id: string
  nome: string
  totalAmount: number
  influencerCount: number
  budgetCount: number
}

interface GranularShareDialogProps {
  proposal: ProposalData
  onClose: () => void
}

interface SharePermissions {
  // Nível da proposta
  canViewProposal: boolean
  canViewDescription: boolean
  canComment: boolean
  
  // Subcoleções
  canViewInfluencers: boolean
  canViewBudgets: boolean
  canViewFinancials: boolean
  
  // Dados do influencer
  canViewContactInfo: boolean
  canViewSocialStats: boolean
  
  // Metadados
  expiresIn: string // "7days", "30days", "never"
}

export function GranularShareDialog({ proposal, onClose }: GranularShareDialogProps) {
  const { user } = useUser()
  const { organization } = useOrganization()
  
  const [email, setEmail] = useState('')
  const [permissions, setPermissions] = useState<SharePermissions>({
    canViewProposal: true,
    canViewDescription: true,
    canComment: false,
    canViewInfluencers: true,
    canViewBudgets: false,
    canViewFinancials: false,
    canViewContactInfo: false,
    canViewSocialStats: true,
    expiresIn: '30days'
  })
  const [isSharing, setIsSharing] = useState(false)
  const [shareLink, setShareLink] = useState('')

  const handleShare = async () => {
    if (!email || !organization) {
      toast.error('Email e organização são obrigatórios')
      return
    }

    setIsSharing(true)

    try {
      // 1. Criar convite granular no Firebase
      const granularResponse = await fetch('/api/proposals/granular-share', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          proposalId: proposal.id,
          inviteeEmail: email,
          permissions: permissions,
          organizationId: organization.id,
          invitedBy: user?.id
        })
      })

      const granularData = await granularResponse.json()

      // 2. Criar convite organizacional no Clerk
      const clerkResponse = await fetch('/api/organizations/invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: email,
          organizationId: organization.id,
          role: 'basic_member'
        })
      })

      const clerkData = await clerkResponse.json()

      // 3. Criar link combinado
      const combinedLink = `${window.location.origin}/accept-granular-invite/${granularData.token}?org_invite=${clerkData.inviteId}`
      
      setShareLink(combinedLink)
      
      toast.success('Convite criado com sucesso!')

    } catch (error) {
      console.error('Erro ao compartilhar:', error)
      toast.error('Erro ao criar convite')
    } finally {
      setIsSharing(false)
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareLink)
    toast.success('Link copiado!')
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5 text-[#ff0074]" />
            Compartilhar Proposta: {proposal.nome}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Integração Clerk + Firebase */}
          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
              <Users className="w-4 h-4" />
              Sistema Híbrido: Clerk + Firebase
            </h3>
            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <p>• <strong>Clerk:</strong> {organization?.name} (organização)</p>
              <p>• <strong>Firebase:</strong> Controle granular da proposta</p>
              <p>• <strong>Resultado:</strong> Acesso específico sem navegação fragmentada</p>
            </div>
          </div>

          {/* Email do convidado */}
          <div className="space-y-2">
            <Label htmlFor="email">Email do Convidado</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <Separator />

          {/* Permissões da Proposta */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Eye className="w-4 h-4 text-[#5600ce]" />
              Acesso à Proposta Principal
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="viewProposal"
                  checked={permissions.canViewProposal}
                  onCheckedChange={(checked) => 
                    setPermissions(prev => ({ ...prev, canViewProposal: checked as boolean }))
                  }
                />
                <Label htmlFor="viewProposal" className="text-sm">
                  Ver dados básicos da proposta
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="comment"
                  checked={permissions.canComment}
                  onCheckedChange={(checked) => 
                    setPermissions(prev => ({ ...prev, canComment: checked as boolean }))
                  }
                />
                <Label htmlFor="comment" className="text-sm">
                  Comentar na proposta
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Permissões de Subcoleções */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Lock className="w-4 h-4 text-[#ff0074]" />
              Subcoleções (Estrutura Real Firebase)
            </h3>
            
            <div className="space-y-3">
              {/* Influencers */}
              <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded">
                <p className="text-sm font-mono mb-2 text-gray-600 dark:text-gray-400">
                  /proposals/{proposal.id}/influencers/*
                </p>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="viewInfluencers"
                      checked={permissions.canViewInfluencers}
                      onCheckedChange={(checked) => 
                        setPermissions(prev => ({ ...prev, canViewInfluencers: checked as boolean }))
                      }
                    />
                    <Label htmlFor="viewInfluencers" className="text-sm">
                      Ver lista de influenciadores
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="viewSocialStats"
                      checked={permissions.canViewSocialStats}
                      onCheckedChange={(checked) => 
                        setPermissions(prev => ({ ...prev, canViewSocialStats: checked as boolean }))
                      }
                    />
                    <Label htmlFor="viewSocialStats" className="text-sm">
                      Ver estatísticas sociais
                    </Label>
                  </div>
                </div>
              </div>

              {/* Budgets */}
              <div className="bg-red-50 dark:bg-red-950 p-3 rounded">
                <p className="text-sm font-mono mb-2 text-red-600 dark:text-red-400">
                  /proposals/.../influencers/.../budgets/*
                </p>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="viewBudgets"
                      checked={permissions.canViewBudgets}
                      onCheckedChange={(checked) => 
                        setPermissions(prev => ({ ...prev, canViewBudgets: checked as boolean }))
                      }
                    />
                    <Label htmlFor="viewBudgets" className="text-sm">
                      Ver orçamentos
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="viewFinancials"
                      checked={permissions.canViewFinancials}
                      onCheckedChange={(checked) => 
                        setPermissions(prev => ({ ...prev, canViewFinancials: checked as boolean }))
                      }
                    />
                    <Label htmlFor="viewFinancials" className="text-sm flex items-center gap-1">
                      <DollarSign className="w-3 h-3" />
                      Ver valores (R$ {proposal.totalAmount.toLocaleString()})
                    </Label>
                  </div>
                </div>
              </div>

              {/* Dados de Contato */}
              <div className="bg-yellow-50 dark:bg-yellow-950 p-3 rounded">
                <p className="text-sm font-mono mb-2 text-yellow-600 dark:text-yellow-400">
                  /influencers/* (dados sensíveis)
                </p>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="viewContactInfo"
                    checked={permissions.canViewContactInfo}
                    onCheckedChange={(checked) => 
                      setPermissions(prev => ({ ...prev, canViewContactInfo: checked as boolean }))
                    }
                  />
                  <Label htmlFor="viewContactInfo" className="text-sm flex items-center gap-1">
                    <Phone className="w-3 h-3" />
                    <Mail className="w-3 h-3" />
                    Ver contatos (email, whatsapp)
                  </Label>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Expiração */}
          <div className="space-y-2">
            <Label>Expiração do Acesso</Label>
            <select
              value={permissions.expiresIn}
              onChange={(e) => setPermissions(prev => ({ ...prev, expiresIn: e.target.value }))}
              className="w-full p-2 border rounded"
            >
              <option value="7days">7 dias</option>
              <option value="30days">30 dias</option>
              <option value="90days">90 dias</option>
              <option value="never">Sem expiração</option>
            </select>
          </div>

          {/* Preview do que será visível */}
          <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
              O que {email || 'o convidado'} verá:
            </h4>
            <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <p>✅ Proposta: {proposal.nome}</p>
              {permissions.canViewInfluencers && <p>✅ {proposal.influencerCount} influenciador(es)</p>}
              {permissions.canViewBudgets ? (
                <p>✅ {proposal.budgetCount} orçamento(s)</p>
              ) : (
                <p>❌ Orçamentos ocultos</p>
              )}
              {permissions.canViewFinancials ? (
                <p>✅ Valores: R$ {proposal.totalAmount.toLocaleString()}</p>
              ) : (
                <p>❌ Valores financeiros ocultos</p>
              )}
              {permissions.canViewContactInfo ? (
                <p>✅ Contatos dos influenciadores</p>
              ) : (
                <p>❌ Contatos ocultos</p>
              )}
            </div>
          </div>

          {/* Link gerado */}
          {shareLink && (
            <div className="space-y-2">
              <Label>Link de Convite (Clerk + Firebase)</Label>
              <div className="flex gap-2">
                <Input value={shareLink} readOnly className="text-xs" />
                <Button size="sm" onClick={copyToClipboard}>
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button 
              onClick={handleShare}
              disabled={isSharing || !email}
              className="bg-[#ff0074] hover:bg-[#ff0074]/90"
            >
              {isSharing ? 'Criando Convite...' : 'Compartilhar'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 