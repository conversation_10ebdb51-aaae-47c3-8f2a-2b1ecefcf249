import { clientDb as db } from '@/lib/firebase-client';
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  DocumentSnapshot
} from 'firebase/firestore';
import { Campaign } from '@/types/campaign';

const COLLECTION_NAME = 'campaigns';

export class CampaignService {
  /**
   * Busca todas as campanhas
   */
  static async getCampaigns(
    filters?: {
      status?: string;
      brandId?: string;
      categoryId?: string;
      minBudget?: number;
      maxBudget?: number;
      startDate?: string;
      endDate?: string;
    },
    limitCount = 20,
    lastDoc?: string
  ): Promise<Campaign[]> {
    try {
      let q = query(collection(db, COLLECTION_NAME));

      // Aplicar filtros
      if (filters) {
        if (filters.status) {
          q = query(q, where('status', '==', filters.status));
        }
        if (filters.brandId) {
          q = query(q, where('brandId', '==', filters.brandId));
        }
        if (filters.categoryId) {
          q = query(q, where('categoryId', '==', filters.categoryId));
        }
        if (filters.minBudget) {
          q = query(q, where('budget', '>=', filters.minBudget));
        }
        if (filters.maxBudget) {
          q = query(q, where('budget', '<=', filters.maxBudget));
        }
        if (filters.startDate) {
          q = query(q, where('startDate', '>=', filters.startDate));
        }
        if (filters.endDate) {
          q = query(q, where('endDate', '<=', filters.endDate));
        }
      }

      // Ordenação e paginação
      q = query(q, orderBy('createdAt', 'desc'), limit(limitCount));

      if (lastDoc) {
        const lastDocRef = doc(db, COLLECTION_NAME, lastDoc);
        const lastDocSnap = await getDoc(lastDocRef);
        if (lastDocSnap.exists()) {
          q = query(q, startAfter(lastDocSnap));
        }
      }

      const querySnapshot = await getDocs(q);
      const campaigns: Campaign[] = [];

      querySnapshot.forEach((doc) => {
        campaigns.push({
          id: doc.id,
          ...doc.data()
        } as Campaign);
      });

      return campaigns;
    } catch (error) {
      console.error('Erro ao buscar campanhas:', error);
      throw new Error('Falha ao buscar campanhas');
    }
  }

  /**
   * Busca uma campanha por ID
   */
  static async getCampaign(id: string): Promise<Campaign | null> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as Campaign;
      }

      return null;
    } catch (error) {
      console.error('Erro ao buscar campanha:', error);
      throw new Error('Falha ao buscar campanha');
    }
  }

  /**
   * Cria uma nova campanha
   */
  static async createCampaign(campaignData: Omit<Campaign, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = new Date().toISOString();
      const docRef = await addDoc(collection(db, COLLECTION_NAME), {
        ...campaignData,
        createdAt: now,
        updatedAt: now
      });

      return docRef.id;
    } catch (error) {
      console.error('Erro ao criar campanha:', error);
      throw new Error('Falha ao criar campanha');
    }
  }

  /**
   * Atualiza uma campanha
   */
  static async updateCampaign(id: string, updates: Partial<Campaign>): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao atualizar campanha:', error);
      throw new Error('Falha ao atualizar campanha');
    }
  }

  /**
   * Deleta uma campanha
   */
  static async deleteCampaign(id: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Erro ao deletar campanha:', error);
      throw new Error('Falha ao deletar campanha');
    }
  }

  /**
   * Busca campanhas por marca
   */
  static async getCampaignsByBrand(brandId: string): Promise<Campaign[]> {
    try {
      const q = query(
        collection(db, COLLECTION_NAME),
        where('brandId', '==', brandId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const campaigns: Campaign[] = [];

      querySnapshot.forEach((doc) => {
        campaigns.push({
          id: doc.id,
          ...doc.data()
        } as Campaign);
      });

      return campaigns;
    } catch (error) {
      console.error('Erro ao buscar campanhas da marca:', error);
      throw new Error('Falha ao buscar campanhas da marca');
    }
  }

  /**
   * Busca campanhas ativas
   */
  static async getActiveCampaigns(): Promise<Campaign[]> {
    try {
      const q = query(
        collection(db, COLLECTION_NAME),
        where('status', 'in', ['active', 'recruiting', 'em-analise'])
      );

      const querySnapshot = await getDocs(q);
      const campaigns: Campaign[] = [];

      querySnapshot.forEach((doc) => {
        campaigns.push({
          id: doc.id,
          ...doc.data()
        } as Campaign);
      });

      // Ordenar no lado do cliente para evitar necessidade de índice composto
      campaigns.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt || 0);
        const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt || 0);
        return dateB.getTime() - dateA.getTime();
      });

      return campaigns;
    } catch (error) {
      console.error('Erro ao buscar campanhas ativas:', error);
      throw new Error('Falha ao buscar campanhas ativas');
    }
  }

  /**
   * Atualiza o status de uma campanha
   */
  static async updateCampaignStatus(id: string, status: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await updateDoc(docRef, {
        status,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao atualizar status da campanha:', error);
      throw new Error('Falha ao atualizar status da campanha');
    }
  }

  /**
   * Adiciona um influenciador a uma campanha
   */
  static async addInfluencerToCampaign(
    campaignId: string, 
    influencerId: string, 
    influencerData: any
  ): Promise<void> {
    try {
      const campaign = await this.getCampaign(campaignId);
      if (!campaign) {
        throw new Error('Campanha não encontrada');
      }

      const updatedInfluencers = [...(campaign.influencers || []), {
        influencerId,
        ...influencerData,
        addedAt: new Date().toISOString()
      }];

      await this.updateCampaign(campaignId, {
        influencers: updatedInfluencers
      });
    } catch (error) {
      console.error('Erro ao adicionar influenciador à campanha:', error);
      throw new Error('Falha ao adicionar influenciador à campanha');
    }
  }

  /**
   * Busca campanhas onde um influenciador específico está incluído
   */
  static async getCampaignsByInfluencer(influencerId: string): Promise<Campaign[]> {
    try {
      // Buscar todas as campanhas ativas usando o método existente
      const allCampaigns = await this.getActiveCampaigns();
      
      // Filtrar campanhas que incluem o influenciador
      const influencerCampaigns = allCampaigns.filter(campaign => 
        campaign.influencers && 
        campaign.influencers.some((inf: any) => 
          inf.influencerId === influencerId || inf.id === influencerId
        )
      );
      
      return influencerCampaigns;
    } catch (error) {
      console.error('Erro ao buscar campanhas do influenciador:', error);
      throw new Error('Falha ao buscar campanhas do influenciador');
    }
  }

  /**
   * Remove um influenciador de uma campanha
   */
  static async removeInfluencerFromCampaign(
    campaignId: string, 
    influencerId: string
  ): Promise<void> {
    try {
      const campaign = await this.getCampaign(campaignId);
      if (!campaign) {
        throw new Error('Campanha não encontrada');
      }

      const updatedInfluencers = (campaign.influencers || []).filter(
        (inf: any) => inf.influencerId !== influencerId
      );

      await this.updateCampaign(campaignId, {
        influencers: updatedInfluencers
      });
    } catch (error) {
      console.error('Erro ao remover influenciador da campanha:', error);
      throw new Error('Falha ao remover influenciador da campanha');
    }
  }
}

