{"data": {"influencersByIds": {"influencers": [{"id": "12kIhCdlyaf5oWfxq0dh", "name": "JJAAAA", "avatar": "", "totalFollowers": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": "Lifestyle", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "age": 23, "gender": "female", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-18T19:26:58.219Z", "updatedAt": "2025-07-18T19:26:58.219Z", "__typename": "Influencer"}, {"id": "3kxm1UutKCnitRvfPqTq", "name": "<PERSON>", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752728373279_1752728378748.jpg", "totalFollowers": 33529638, "engagementRate": 22, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": "Lifestyle", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "age": 23, "gender": "female", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "instagramUsername": "filiperet", "instagramFollowers": 132000, "instagramEngagementRate": 22, "instagramAvgViews": null, "tiktokUsername": "@rafaelchef", "tiktokFollowers": 64305, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "facebookUsername": "rafaelchocolate", "facebookFollowers": 33333333, "facebookEngagementRate": 22, "facebookAvgViews": null, "twitchUsername": "fress", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "hhhh", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "CARLOS", "agencyName": "NONSTOP", "responsibleCapturer": "HELLEN", "mainNetwork": "instagram", "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-17T05:00:13.675Z", "updatedAt": "2025-07-18T20:09:10.583Z", "__typename": "Influencer"}, {"id": "7PnAxZLXq0ih5IcLz3nF", "name": "Gigante Glorioso", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752171603008_1752171604310.png", "totalFollowers": 2068888, "engagementRate": 48.67, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": "Lifestyle", "country": "Brasil", "state": "CE", "city": "Jardim", "age": 42, "gender": "male", "email": "<EMAIL>", "phone": "+55888888888", "whatsapp": "+55888888888", "instagramUsername": "marina<PERSON><PERSON>va_", "instagramFollowers": 34333, "instagramEngagementRate": 45, "instagramAvgViews": 3, "tiktokUsername": "@marinasilva", "tiktokFollowers": 2000000, "tiktokEngagementRate": 45, "tiktokAvgViews": 0, "youtubeUsername": "fgggg", "youtubeFollowers": 34555, "youtubeEngagementRate": 56, "youtubeAvgViews": 5, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": false, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "", "mainNetwork": "youtube", "currentPricing": null, "currentDemographics": [{"id": "OAR22JyfxIHlFC3NuXD4", "platform": "youtube", "audienceGender": {"male": 88.5, "female": 11.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 13.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "13-17", "percentage": 0.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 5.6, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 14.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 22.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 15.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 20.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 20.8, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "Tq3mwH0unS8vKHUbRjFz", "platform": "tiktok", "audienceGender": {"male": 85.5, "female": 14.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "hH8ZUAcXr2QoDCIA1j9V", "platform": "instagram", "audienceGender": {"male": 85.5, "female": 14.5, "other": 2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 17.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo", "percentage": 2, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.6, "__typename": "AudienceCity"}, {"city": "<PERSON>", "percentage": 1.4, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1.3, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-10T18:20:07.798Z", "updatedAt": "2025-07-18T19:02:36.627Z", "__typename": "Influencer"}, {"id": "Dvr9bWx5ffTAESPRh0Xd", "name": "ttttYYYY", "avatar": "", "totalFollowers": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": "Esportes", "country": "Brasil", "state": "", "city": "<PERSON><PERSON><PERSON>", "age": 22, "gender": "male", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-18T21:44:09.074Z", "updatedAt": "2025-07-18T21:48:30.897Z", "__typename": "Influencer"}, {"id": "e834SnQ1qPWBlFobQJ4O", "name": "Mariana", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752865929078_1752865929992.jpg", "totalFollowers": 26713478, "engagementRate": 39.33, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": "Lifestyle", "country": "Brasil", "state": "CE", "city": "Jardim", "age": 18, "gender": "female", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "instagramUsername": "filiperet", "instagramFollowers": 132000, "instagramEngagementRate": 21, "instagramAvgViews": null, "tiktokUsername": "@marinasilva", "tiktokFollowers": 22222222, "tiktokEngagementRate": 23, "tiktokAvgViews": null, "youtubeUsername": "fgggg", "youtubeFollowers": 1234567, "youtubeEngagementRate": 23, "youtubeAvgViews": null, "facebookUsername": "rafaelchocolate", "facebookFollowers": 1234567, "facebookEngagementRate": 53, "facebookAvgViews": null, "twitchUsername": "fress", "twitchFollowers": 655555, "twitchEngagementRate": 63, "twitchViews": 55555, "kwaiUsername": "hhhh", "kwaiFollowers": 1234567, "kwaiEngagementRate": 53, "kwaiViews": 55555, "promotesTraders": true, "responsibleName": "prashoop.com", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-18T19:12:11.102Z", "updatedAt": "2025-07-18T19:12:11.102Z", "__typename": "Influencer"}, {"id": "j2ElhOopXa5vCAZ1CczW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/j2ElhOopXa5vCAZ1CczW_1753126469281.jpg", "totalFollowers": 6833235, "engagementRate": 12.52, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": null, "country": "Brasil", "state": "CE", "city": "Jardim", "age": 23, "gender": "not_specified", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "youtubeUsername": "john", "youtubeFollowers": 2222222, "youtubeEngagementRate": 22, "youtubeAvgViews": null, "facebookUsername": "casalrevelidade", "facebookFollowers": 1688223, "facebookEngagementRate": 3.05, "facebookAvgViews": null, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "instagram", "currentPricing": null, "currentDemographics": [{"id": "WVQ7WAixxstJGteosPdY", "platform": "facebook", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON>, BA, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São <PERSON>lo, RJ, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São Paulo, SP, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "itRrTYxn1CZtDi4VtHJc", "platform": "youtube", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "18-24", "percentage": 47.8, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 36.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.9, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 7.7, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.9, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.2, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "ZyyGT98vzQT2C2kYOyR1", "platform": "tiktok", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "oaZSaz8kFVNnT5TXvpgb", "platform": "instagram", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Paulo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São <PERSON>lo, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Ribeirão <PERSON>, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Florianópolis, SC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Campo Grande, MS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, MA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Macapá, AP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Palmas, TO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T19:21:11.969Z", "updatedAt": "2025-07-21T19:34:42.145Z", "__typename": "Influencer"}, {"id": "lxrMDbxTQGCpYvRrkCPZ", "name": "<PERSON>", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752352966267_1752352965497.jpg", "totalFollowers": 2364554, "engagementRate": 34.5, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "category": null, "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "age": 24, "gender": "male", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "instagramUsername": "aquiehparmera", "instagramFollowers": 135444, "instagramEngagementRate": 34, "instagramAvgViews": null, "tiktokUsername": "@rafaelchef", "tiktokFollowers": 657000, "tiktokEngagementRate": 24, "tiktokAvgViews": null, "youtubeUsername": "fgggg", "youtubeFollowers": 34555, "youtubeEngagementRate": 23, "youtubeAvgViews": null, "facebookUsername": "aquiehparmera", "facebookFollowers": 132000, "facebookEngagementRate": 25, "facebookAvgViews": null, "twitchUsername": "Público", "twitchFollowers": 655555, "twitchEngagementRate": 56, "twitchViews": 777777, "kwaiUsername": "hhhh", "kwaiFollowers": 750000, "kwaiEngagementRate": 45, "kwaiViews": 777777, "promotesTraders": true, "responsibleName": "aaa", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "Luana555", "mainNetwork": "twitch", "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-12T20:42:46.499Z", "updatedAt": "2025-07-18T21:04:44.541Z", "__typename": "Influencer"}], "foundIds": ["e834SnQ1qPWBlFobQJ4O", "j2ElhOopXa5vCAZ1CczW", "Dvr9bWx5ffTAESPRh0Xd", "12kIhCdlyaf5oWfxq0dh", "3kxm1UutKCnitRvfPqTq", "lxrMDbxTQGCpYvRrkCPZ", "7PnAxZLXq0ih5IcLz3nF"], "notFoundIds": [], "totalFound": 7, "totalRequested": 7, "processingTimeMs": 973, "hasPartialFailure": false, "errors": [], "__typename": "InfluencersByIdsResult"}}}