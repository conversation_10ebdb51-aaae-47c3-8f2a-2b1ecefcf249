const fetch = require('node-fetch');

async function testUpdateInfluencerWithViews() {
  const influencerId = '8Df9R9mZnIBLwvQQVrDQ'; // ID do influenciador existente
  
  // Dados simulando o que vem do formulário com campos de views
  const testData = {
    influencerData: {
      name: "Filipe Ret Teste Views",
      email: "<EMAIL>",
      phone: "+5588997028711",
      whatsapp: "+5588997028711",
      country: "Brasil",
      state: "CE",
      city: "Jardim",
      location: "Jardim/CE",
      age: 32,
      gender: "male",
      bio: "Testando campos de views",
      avatar: "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1750694315317_1750694320119.jpg",
      socialNetworks: {
        instagram: {
          username: "filiperet_teste",
          followers: 250000,
          avgViews: 80000,
          engagementRate: 3.5,
          // ✨ Campos de views específicos do Instagram
          storiesViews: 15000,
          reelsViews: 35000,
          audienceGender: {
            male: 40,
            female: 58,
            other: 2
          },
          audienceLocations: [],
          audienceCities: [],
          audienceAgeRange: []
        },
        youtube: {
          username: "filiperet_yt",
          followers: 50000,
          avgViews: 25000,
          engagementRate: 4.2,
          // ✨ Campos de views específicos do YouTube
          shortsViews: 45000,
          longFormViews: 125000,
          audienceGender: {
            male: 65,
            female: 33,
            other: 2
          },
          audienceLocations: [],
          audienceCities: [],
          audienceAgeRange: []
        },
        tiktok: {
          username: "filiperet_tt",
          followers: 180000,
          avgViews: 95000,
          engagementRate: 8.1,
          // ✨ Campos de views específicos do TikTok
          videoViews: 95000,
          audienceGender: {
            male: 45,
            female: 52,
            other: 3
          },
          audienceLocations: [],
          audienceCities: [],
          audienceAgeRange: []
        }
      },
      categories: ["Lifestyle", "Tech"],
      mainCategories: ["Lifestyle", "Tech"],
      totalFollowers: 480000,
      engagementRate: 5.3,
      isVerified: true,
      isAvailable: true,
      status: "active",
      promotesTraders: false,
      id: influencerId
    },
    financialData: {
      influencerId: influencerId,
      responsibleName: "Teste Views Manager",
      agencyName: "Views Agency",
      email: "<EMAIL>",
      whatsapp: "+5588997028711",
      instagramStoriesViews: 15000, // Sincronizado com socialNetworks
      prices: {
        instagramStory: {
          name: "Stories Instagram",
          price: 800
        },
        instagramReel: {
          name: "Reels Instagram",
          price: 1200
        },
        tiktokVideo: {
          name: "Vídeo TikTok",
          price: 1000
        },
        youtubeInsertion: {
          name: "Inserção YouTube",
          price: 1500
        },
        youtubeDedicated: {
          name: "Vídeo Dedicado YouTube",
          price: 2500
        },
        youtubeShorts: {
          name: "YouTube Shorts",
          price: 900
        }
      },
      brandHistory: {
        instagram: ["Nike", "Adidas"],
        tiktok: ["Samsung"],
        youtube: ["Apple", "Microsoft"]
      },
      additionalData: {
        contentType: ["Lifestyle", "Tech"],
        promotesTraders: false,
        responsibleRecruiter: "Views Recruiter",
        socialMediaScreenshots: [],
        notes: "Influenciador teste com campos de views implementados",
        documents: []
      }
    }
  };

  try {
    console.log('🚀 Iniciando teste de atualização com campos de views...');
    console.log(`📝 Atualizando influenciador ID: ${influencerId}`);
    
    // Fazer a requisição PUT para a API
    const response = await fetch(`http://localhost:3000/api/influencers?id=${influencerId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorData}`);
    }

    const result = await response.json();
    console.log('✅ Atualização realizada com sucesso!');
    console.log('📋 Resultado:', JSON.stringify(result, null, 2));
    
    // Verificar se os campos platforms foram adicionados
    if (result.platforms) {
      console.log('\n🎉 Campos platforms encontrados!');
      
      Object.keys(result.platforms).forEach(platform => {
        console.log(`\n📱 ${platform.toUpperCase()}:`);
        console.log(`  - Username: ${result.platforms[platform].username}`);
        console.log(`  - Followers: ${result.platforms[platform].followers}`);
        
        if (result.platforms[platform].views) {
          console.log(`  - Views: ${JSON.stringify(result.platforms[platform].views, null, 4)}`);
        } else {
          console.log('  - Views: ❌ Não encontrado');
        }
      });
    } else {
      console.log('❌ Campos platforms não encontrados no resultado');
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
}

testUpdateInfluencerWithViews(); 
