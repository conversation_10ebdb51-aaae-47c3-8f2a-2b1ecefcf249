'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth-v2';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, Users, UserCheck, Sparkles } from 'lucide-react';

interface UserType {
  id: 'agency' | 'influencer' | 'manager';
  title: string;
  description: string;
  icon: React.ReactNode;
  role: string;
  gradient: string;
}

const userTypes: UserType[] = [
  {
    id: 'agency',
    title: 'Agência',
    description: 'Gerencio campanhas e contrato influenciadores para meus clientes',
    icon: <Building2 className="h-10 w-10" />,
    role: 'admin',
    gradient: 'from-[#ff0074] to-[#9810fa]'
  },
  {
    id: 'influencer',
    title: 'Influenciador',
    description: 'Crio conteúdo e recebo propostas de campanhas',
    icon: <Users className="h-10 w-10" />,
    role: 'member',
    gradient: 'from-[#ff0074] to-[#9810fa]'
  },
  {
    id: 'manager',
    title: 'Manager',
    description: 'Coordeno campanhas e faço a ponte entre agências e marcas',
    icon: <UserCheck className="h-10 w-10" />,
    role: 'manager',
    gradient: 'from-[#ff0074] to-[#9810fa]'
  }
];

interface OnboardingModalProps {
  isOpen: boolean;
  onClose?: () => void;
}

export function OnboardingModal({ isOpen, onClose }: OnboardingModalProps) {
  const { currentUser: user } = useAuth();
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleComplete = async () => {
    if (!selectedType || !user) return;
    
    setIsLoading(true);
    
    try {
      const userTypeConfig = userTypes.find(type => type.id === selectedType);
      if (!userTypeConfig) return;

      console.log('👤 Configurando usuário:', {
        userId: user.id,
        email: user.primaryEmailAddress?.emailAddress || user.emailAddresses?.[0]?.emailAddress,
        type: selectedType,
        role: userTypeConfig.role
      });

      const response = await fetch('/api/user/complete-onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          userType: selectedType,
          role: userTypeConfig.role
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Onboarding concluído:', result);
        
        // Marcar como concluído
        localStorage.setItem(`onboarding_completed_${user.id}`, 'true');
        
        // Fechar modal e redirecionar
        onClose?.();
        router.push('/dashboard');
        router.refresh(); // Força refresh para atualizar permissões
      } else {
        console.error('❌ Erro ao completar onboarding');
        alert('Erro ao configurar sua conta. Tente novamente.');
      }
    } catch (error) {
      console.error('❌ Erro no onboarding:', error);
      alert('Erro ao configurar sua conta. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const selectedTypeConfig = userTypes.find(t => t.id === selectedType);

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0 border-none bg-gradient-to-br from-background to-muted/20">
        <div className="p-8 ">
          <DialogHeader className="text-center mb-8">
            
            <DialogTitle className="text-3xl font-bold bg-gradient-to-r from-[#ff0074] to-[#9810fa] bg-clip-text text-transparent text-center">
              Bem-vindo a Deu Match!
            </DialogTitle>
            
            <DialogDescription className="text-lg text-center text-muted-foreground mt-4 max-w-2xl mx-auto">
              Olá, <span className="font-semibold text-foreground">{user?.email}</span>!
              <br />
              Para personalizar sua experiência, escolha seu perfil:
            </DialogDescription>
          </DialogHeader>

          {/* Cards de seleção */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {userTypes.map((type) => (
              <Card 
                key={type.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 border-2 ${
                  selectedType === type.id 
                    ? 'border-[#ff0074] bg-gradient-to-br from-[#ff0074]/5 to-[#9810fa]/5 shadow-lg' 
                    : 'border-border hover:border-[#ff0074]/50'
                }`}
                onClick={() => setSelectedType(type.id)}
              >
                <CardHeader className="text-center pb-4">
                  <div className={`mx-auto mb-6 p-6 rounded-2xl transition-all duration-300 ${
                    selectedType === type.id 
                      ? `bg-gradient-to-r ${type.gradient} text-white shadow-lg` 
                      : 'bg-muted/50 text-muted-foreground hover:bg-muted'
                  }`}>
                    {type.icon}
                  </div>
                  <CardTitle className="text-xl font-bold">{type.title}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription className="text-center leading-relaxed text-sm">
                    {type.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {/* Botão de ação */}
          <div className="text-center space-y-4">
            <Button
              onClick={handleComplete}
              disabled={!selectedType || isLoading}
              size="lg"
              className="bg-gradient-to-r from-[#ff0074] to-[#9810fa] hover:from-[#ff0074]/90 hover:to-[#9810fa]/90 text-white px-12 py-6 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Configurando...
                </div>
              ) : (
                'Começar Agora'
              )}
            </Button>
            
           
          </div>

         
        </div>
      </DialogContent>
    </Dialog>
  );
} 


