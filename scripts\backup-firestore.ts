import { db } from '@/lib/firebase-admin';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 📦 SCRIPT DE BACKUP DO FIRESTORE
 * FASE 5.1: Backup completo e seguro das coleções
 */

// Interface para dados de backup
interface BackupData {
  collection: string;
  timestamp: string;
  count: number;
  documents: Array<{
    id: string;
    data: any;
  }>;
  metadata: {
    backupVersion: string;
    nodeVersion: string;
    firbaseAdminVersion: string;
  };
}

// Configurações de backup
const BACKUP_CONFIG = {
  backupDir: './backups',
  maxRetries: 3,
  batchSize: 500,
  timeout: 30000
};

/**
 * Fazer backup de uma coleção específica
 */
export async function backupCollection(
  collectionName: string,
  options: {
    includeBatches?: boolean;
    includeSubcollections?: boolean;
    filter?: (doc: any) => boolean;
  } = {}
): Promise<string> {
  console.log(`📦 Fazendo backup da coleção: ${collectionName}`);
  
  try {
    // Criar diretório de backup se não existir
    ensureBackupDirectory();
    
    // Obter todos os documentos da coleção
    const snapshot = await db.collection(collectionName).get();
    const allDocs = snapshot.docs;
    
    console.log(`📊 Encontrados ${allDocs.length} documentos em ${collectionName}`);
    
    // Aplicar filtro se fornecido
    const docsToBackup = options.filter 
      ? allDocs.filter(doc => options.filter!(doc.data()))
      : allDocs;
    
    // Processar documentos em lotes para performance
    const documents: Array<{ id: string; data: any }> = [];
    
    for (let i = 0; i < docsToBackup.length; i += BACKUP_CONFIG.batchSize) {
      const batch = docsToBackup.slice(i, i + BACKUP_CONFIG.batchSize);
      console.log(`🔄 Processando lote ${Math.floor(i / BACKUP_CONFIG.batchSize) + 1}/${Math.ceil(docsToBackup.length / BACKUP_CONFIG.batchSize)}`);
      
      for (const doc of batch) {
        let docData = doc.data();
        
        // Converter Timestamps para ISO strings
        docData = convertFirestoreData(docData);
        
        // Incluir subcoleções se solicitado
        if (options.includeSubcollections) {
          docData._subcollections = await getSubcollections(doc.ref);
        }
        
        documents.push({
          id: doc.id,
          data: docData
        });
      }
    }
    
    // Preparar dados de backup
    const backupData: BackupData = {
      collection: collectionName,
      timestamp: new Date().toISOString(),
      count: documents.length,
      documents,
      metadata: {
        backupVersion: '1.0.0',
        nodeVersion: process.version,
        firbaseAdminVersion: require('firebase-admin/package.json').version
      }
    };
    
    // Gerar nome do arquivo
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `backup-${collectionName}-${timestamp}.json`;
    const filepath = path.join(BACKUP_CONFIG.backupDir, filename);
    
    // Salvar backup
    fs.writeFileSync(filepath, JSON.stringify(backupData, null, 2));
    
    // Validar backup salvo
    const backupSize = fs.statSync(filepath).size;
    console.log(`✅ Backup salvo: ${filename}`);
    console.log(`📊 Documentos: ${documents.length} | Tamanho: ${formatBytes(backupSize)}`);
    
    // Criar checksum para integridade
    const checksum = await createChecksum(filepath);
    const checksumFile = filepath.replace('.json', '.checksum');
    fs.writeFileSync(checksumFile, checksum);
    
    return filename;
    
  } catch (error) {
    console.error(`❌ Erro no backup da coleção ${collectionName}:`, error);
    throw new Error(`Falha no backup da coleção ${collectionName}: ${error.message}`);
  }
}

/**
 * Fazer backup de múltiplas coleções
 */
export async function backupMultipleCollections(
  collections: string[],
  options: {
    parallel?: boolean;
    continueOnError?: boolean;
  } = {}
): Promise<{ successful: string[]; failed: Array<{ collection: string; error: string }> }> {
  console.log(`📦 Iniciando backup de ${collections.length} coleções`);
  
  const successful: string[] = [];
  const failed: Array<{ collection: string; error: string }> = [];
  
  if (options.parallel) {
    // Backup paralelo
    const promises = collections.map(async (collection) => {
      try {
        const filename = await backupCollection(collection);
        return { collection, filename, success: true };
      } catch (error) {
        return { collection, error: error.message, success: false };
      }
    });
    
    const results = await Promise.all(promises);
    
    results.forEach(result => {
      if (result.success) {
        successful.push((result as any).filename);
      } else {
        failed.push({ collection: result.collection, error: (result as any).error });
      }
    });
    
  } else {
    // Backup sequencial
    for (const collection of collections) {
      try {
        const filename = await backupCollection(collection);
        successful.push(filename);
        console.log(`✅ Backup concluído: ${collection}`);
      } catch (error) {
        const errorMsg = error.message;
        failed.push({ collection, error: errorMsg });
        console.error(`❌ Falha no backup: ${collection} - ${errorMsg}`);
        
        if (!options.continueOnError) {
          throw error;
        }
      }
    }
  }
  
  console.log(`📊 Resultado do backup:`);
  console.log(`✅ Sucessos: ${successful.length}`);
  console.log(`❌ Falhas: ${failed.length}`);
  
  return { successful, failed };
}

/**
 * Backup completo do sistema
 */
export async function backupFullSystem(): Promise<string> {
  console.log('🚀 Iniciando backup completo do sistema');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupSessionDir = path.join(BACKUP_CONFIG.backupDir, `full-backup-${timestamp}`);
  
  // Criar diretório para esta sessão de backup
  fs.mkdirSync(backupSessionDir, { recursive: true });
  
  // Coleções principais do sistema
  const collections = [
    'users',
    'brands', 
    'campaigns',
    'influencers',
    'groups',
    'notes',
    'tags',
    'categories',
    'proposals',
    'filters',
    'brand_influencers',
    'influencer_financials'
  ];
  
  const backupManifest = {
    timestamp: new Date().toISOString(),
    collections: [] as any[],
    summary: {
      totalCollections: collections.length,
      totalDocuments: 0,
      totalSize: 0,
      duration: 0
    }
  };
  
  const startTime = Date.now();
  
  try {
    for (const collection of collections) {
      console.log(`📂 Processando coleção: ${collection}`);
      
      try {
        // Backup individual com dados detalhados
        const snapshot = await db.collection(collection).get();
        const docCount = snapshot.size;
        
        if (docCount > 0) {
          const filename = await backupCollection(collection, { includeSubcollections: true });
          const filepath = path.join(BACKUP_CONFIG.backupDir, filename);
          const fileSize = fs.statSync(filepath).size;
          
          // Mover arquivo para diretório da sessão
          const newPath = path.join(backupSessionDir, filename);
          fs.renameSync(filepath, newPath);
          
          // Mover checksum também
          const checksumOld = filepath.replace('.json', '.checksum');
          const checksumNew = newPath.replace('.json', '.checksum');
          if (fs.existsSync(checksumOld)) {
            fs.renameSync(checksumOld, checksumNew);
          }
          
          backupManifest.collections.push({
            name: collection,
            filename,
            documentCount: docCount,
            size: fileSize,
            status: 'success'
          });
          
          backupManifest.summary.totalDocuments += docCount;
          backupManifest.summary.totalSize += fileSize;
          
        } else {
          console.log(`⚠️ Coleção ${collection} está vazia, pulando backup`);
          backupManifest.collections.push({
            name: collection,
            filename: null,
            documentCount: 0,
            size: 0,
            status: 'empty'
          });
        }
        
      } catch (error) {
        console.error(`❌ Erro no backup da coleção ${collection}:`, error);
        backupManifest.collections.push({
          name: collection,
          filename: null,
          documentCount: 0,
          size: 0,
          status: 'error',
          error: error.message
        });
      }
    }
    
    backupManifest.summary.duration = Date.now() - startTime;
    
    // Salvar manifesto
    const manifestPath = path.join(backupSessionDir, 'backup-manifest.json');
    fs.writeFileSync(manifestPath, JSON.stringify(backupManifest, null, 2));
    
    // Criar arquivo README para o backup
    const readmePath = path.join(backupSessionDir, 'README.md');
    const readmeContent = generateBackupReadme(backupManifest);
    fs.writeFileSync(readmePath, readmeContent);
    
    console.log(`✅ Backup completo concluído em: ${backupSessionDir}`);
    console.log(`📊 ${backupManifest.summary.totalDocuments} documentos | ${formatBytes(backupManifest.summary.totalSize)} | ${formatDuration(backupManifest.summary.duration)}`);
    
    return backupSessionDir;
    
  } catch (error) {
    console.error('❌ Erro no backup completo:', error);
    throw error;
  }
}

/**
 * Utilitários auxiliares
 */

function ensureBackupDirectory(): void {
  if (!fs.existsSync(BACKUP_CONFIG.backupDir)) {
    fs.mkdirSync(BACKUP_CONFIG.backupDir, { recursive: true });
    console.log(`📁 Diretório de backup criado: ${BACKUP_CONFIG.backupDir}`);
  }
}

function convertFirestoreData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }
  
  if (typeof data._seconds === 'number' && typeof data._nanoseconds === 'number') {
    // Firestore Timestamp
    return new Date(data._seconds * 1000 + data._nanoseconds / 1000000).toISOString();
  }
  
  if (data.toDate && typeof data.toDate === 'function') {
    // Firestore Timestamp com método toDate
    return data.toDate().toISOString();
  }
  
  if (Array.isArray(data)) {
    return data.map(convertFirestoreData);
  }
  
  if (typeof data === 'object') {
    const converted: any = {};
    for (const [key, value] of Object.entries(data)) {
      converted[key] = convertFirestoreData(value);
    }
    return converted;
  }
  
  return data;
}

async function getSubcollections(docRef: any): Promise<any> {
  try {
    const subcollections = await docRef.listCollections();
    const subcollectionData: any = {};
    
    for (const subcol of subcollections) {
      const snapshot = await subcol.get();
      subcollectionData[subcol.id] = snapshot.docs.map(doc => ({
        id: doc.id,
        data: convertFirestoreData(doc.data())
      }));
    }
    
    return subcollectionData;
  } catch (error) {
    console.warn('⚠️ Erro ao obter subcoleções:', error);
    return {};
  }
}

async function createChecksum(filepath: string): Promise<string> {
  const crypto = require('crypto');
  const content = fs.readFileSync(filepath);
  return crypto.createHash('sha256').update(content).digest('hex');
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

function generateBackupReadme(manifest: any): string {
  return `# Backup do Sistema - ${manifest.timestamp}

## Resumo
- **Data/Hora**: ${new Date(manifest.timestamp).toLocaleString('pt-BR')}
- **Total de Coleções**: ${manifest.summary.totalCollections}
- **Total de Documentos**: ${manifest.summary.totalDocuments}
- **Tamanho Total**: ${formatBytes(manifest.summary.totalSize)}
- **Duração**: ${formatDuration(manifest.summary.duration)}

## Coleções

${manifest.collections.map(col => `
### ${col.name}
- **Status**: ${col.status}
- **Documentos**: ${col.documentCount}
- **Arquivo**: ${col.filename || 'N/A'}
- **Tamanho**: ${col.size ? formatBytes(col.size) : 'N/A'}
${col.error ? `- **Erro**: ${col.error}` : ''}
`).join('\n')}

## Instruções de Restauração

Para restaurar este backup, use o script \`restore-backup.ts\`:

\`\`\`bash
npm run restore-backup -- --backup-dir="${path.basename(manifest.timestamp)}"
\`\`\`

## Verificação de Integridade

Cada arquivo de backup possui um arquivo .checksum correspondente para verificação de integridade.
`;
}

// Função principal para execução via linha de comando
export async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'collection':
        const collectionName = args[1];
        if (!collectionName) {
          throw new Error('Nome da coleção é obrigatório. Uso: npm run backup collection <nome>');
        }
        await backupCollection(collectionName);
        break;
        
      case 'multiple':
        const collections = args.slice(1);
        if (collections.length === 0) {
          throw new Error('Pelo menos uma coleção é obrigatória. Uso: npm run backup multiple <col1> <col2> ...');
        }
        await backupMultipleCollections(collections);
        break;
        
      case 'full':
      default:
        await backupFullSystem();
        break;
    }
    
    console.log('✅ Backup concluído com sucesso');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Erro no backup:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 

