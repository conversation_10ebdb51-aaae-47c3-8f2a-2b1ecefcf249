import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';

// 🔒 FUNÇÃO AUXILIAR: Validar e buscar usuário com tratamento de erro
async function validateAndGetUser(clerk: any, userId: string, context: string = '') {
  // Validar formato do ID
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    throw new Error(`ID de usuário inválido${context ? ` (${context})` : ''}: "${userId}"`);
  }

  // Validar formato padrão do Clerk (user_xxxxx)
  if (!userId.startsWith('user_') || userId.length < 10) {
    throw new Error(`Formato de ID Clerk inválido${context ? ` (${context})` : ''}: "${userId}"`);
  }

  try {
    const user = await clerk.users.getUser(userId);
    return user;
  } catch (error: any) {
    // Tratamento específico para erro 404 do Clerk
    if (error.status === 404 || error.clerkError) {
      console.error(`🚫 [PROPOSAL_ACCESS] Usuário não encontrado${context ? ` (${context})` : ''}:`, {
        userId,
        clerkTraceId: error.clerkTraceId,
        errorType: 'USER_NOT_FOUND'
      });
      throw new Error(`Usuário não encontrado no sistema${context ? ` (${context})` : ''}: ${userId}`);
    }
    
    // Re-throw outros erros
    console.error(`❌ [PROPOSAL_ACCESS] Erro do Clerk${context ? ` (${context})` : ''}:`, error);
    throw error;
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const { id: proposalId } = await params;
    const { targetUserId, role } = await request.json();

    console.log(`🔄 [PROPOSAL_ACCESS] Adicionando acesso:`, {
      proposalId,
      targetUserId,
      role,
      requestedBy: userId
    });

    // 🔒 VALIDAÇÃO: Role válido
    if (!['a', 'e', 'v'].includes(role)) {
      console.warn(`⚠️ [PROPOSAL_ACCESS] Role inválido:`, { role, validRoles: ['a', 'e', 'v'] });
      return NextResponse.json(
        { error: 'Role inválido. Use: a (admin), e (editor), v (visualizador)' },
        { status: 400 }
      );
    }

    // 🔒 VALIDAÇÃO: Verificar usuário atual
    const clerk = await clerkClient();
    const currentUser = await validateAndGetUser(clerk, userId, 'usuário atual');
    const currentUserProposalAccess = (currentUser.unsafeMetadata as any)?.p?.[proposalId];
    
    if (currentUserProposalAccess !== 'a') {
      console.warn(`🚫 [PROPOSAL_ACCESS] Acesso negado:`, {
        userId,
        proposalId,
        currentRole: currentUserProposalAccess,
        requiredRole: 'a'
      });
      return NextResponse.json(
        { error: 'Apenas admins podem gerenciar acesso à proposta' },
        { status: 403 }
      );
    }

    // 🔒 VALIDAÇÃO: Buscar usuário alvo com tratamento robusto
    const targetUser = await validateAndGetUser(clerk, targetUserId, 'usuário alvo');
    
    // Atualizar metadata do usuário alvo
    const currentMetadata = (targetUser.unsafeMetadata as any) || {};
    const proposalMetadata = (currentMetadata.p as any) || {};
    
    // Adicionar/atualizar acesso à proposta
    proposalMetadata[proposalId] = role;
    
    await clerk.users.updateUserMetadata(targetUserId, {
      unsafeMetadata: {
        ...currentMetadata,
        p: proposalMetadata
      }
    });

    console.log(`✅ [PROPOSAL_ACCESS] Usuário ${targetUserId} adicionado à proposta ${proposalId} com role ${role}`);

    return NextResponse.json({
      success: true,
      message: 'Acesso adicionado com sucesso',
      data: {
        userId: targetUserId,
        proposalId,
        role
      }
    });

  } catch (error: any) {
    console.error('❌ [PROPOSAL_ACCESS] Erro ao adicionar acesso:', {
      error: error.message,
      clerkTraceId: error.clerkTraceId,
      stack: error.stack
    });
    
    // Retornar mensagens específicas baseadas no tipo de erro
    if (error.message.includes('não encontrado') || error.message.includes('inválido')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const { id: proposalId } = await params;
    const { targetUserId } = await request.json();

    console.log(`🔄 [PROPOSAL_ACCESS] Removendo acesso:`, {
      proposalId,
      targetUserId,
      requestedBy: userId
    });

    // 🔒 VALIDAÇÃO: Verificar usuário atual
    const clerk = await clerkClient();
    const currentUser = await validateAndGetUser(clerk, userId, 'usuário atual');
    const currentUserProposalAccess = (currentUser.unsafeMetadata as any)?.p?.[proposalId];
    
    if (currentUserProposalAccess !== 'a') {
      console.warn(`🚫 [PROPOSAL_ACCESS] Acesso negado para remoção:`, {
        userId,
        proposalId,
        currentRole: currentUserProposalAccess
      });
      return NextResponse.json(
        { error: 'Apenas admins podem gerenciar acesso à proposta' },
        { status: 403 }
      );
    }

    // 🔒 VALIDAÇÃO: Buscar usuário alvo
    const targetUser = await validateAndGetUser(clerk, targetUserId, 'usuário alvo');
    
    // Atualizar metadata do usuário alvo (remover acesso)
    const currentMetadata = (targetUser.unsafeMetadata as any) || {};
    const proposalMetadata = { ...((currentMetadata.p as any) || {}) };
    
    // Remover acesso à proposta
    delete proposalMetadata[proposalId];
    
    await clerk.users.updateUserMetadata(targetUserId, {
      unsafeMetadata: {
        ...currentMetadata,
        p: proposalMetadata
      }
    });

    console.log(`✅ [PROPOSAL_ACCESS] Usuário ${targetUserId} removido da proposta ${proposalId}`);

    return NextResponse.json({
      success: true,
      message: 'Acesso removido com sucesso',
      data: {
        userId: targetUserId,
        proposalId
      }
    });

  } catch (error: any) {
    console.error('❌ [PROPOSAL_ACCESS] Erro ao remover acesso:', {
      error: error.message,
      clerkTraceId: error.clerkTraceId,
      stack: error.stack
    });
    
    if (error.message.includes('não encontrado') || error.message.includes('inválido')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const { id: proposalId } = await params;
    const { targetUserId, newRole } = await request.json();

    console.log(`🔄 [PROPOSAL_ACCESS] Atualizando role:`, {
      proposalId,
      targetUserId,
      newRole,
      requestedBy: userId
    });

    // 🔒 VALIDAÇÃO: Role válido
    if (!['a', 'e', 'v'].includes(newRole)) {
      console.warn(`⚠️ [PROPOSAL_ACCESS] Role inválido para atualização:`, { newRole });
      return NextResponse.json(
        { error: 'Role inválido. Use: a (admin), e (editor), v (visualizador)' },
        { status: 400 }
      );
    }

    // 🔒 VALIDAÇÃO: Verificar usuário atual
    const clerk = await clerkClient();
    const currentUser = await validateAndGetUser(clerk, userId, 'usuário atual');
    const currentUserProposalAccess = (currentUser.unsafeMetadata as any)?.p?.[proposalId];
    
    if (currentUserProposalAccess !== 'a') {
      console.warn(`🚫 [PROPOSAL_ACCESS] Acesso negado para atualização:`, {
        userId,
        proposalId,
        currentRole: currentUserProposalAccess
      });
      return NextResponse.json(
        { error: 'Apenas admins podem gerenciar acesso à proposta' },
        { status: 403 }
      );
    }

    // 🔒 VALIDAÇÃO: Buscar usuário alvo
    const targetUser = await validateAndGetUser(clerk, targetUserId, 'usuário alvo');
    
    // Atualizar metadata do usuário alvo
    const currentMetadata = (targetUser.unsafeMetadata as any) || {};
    const proposalMetadata = (currentMetadata.p as any) || {};
    
    // Atualizar role na proposta
    proposalMetadata[proposalId] = newRole;
    
    await clerk.users.updateUserMetadata(targetUserId, {
      unsafeMetadata: {
        ...currentMetadata,
        p: proposalMetadata
      }
    });

    console.log(`✅ [PROPOSAL_ACCESS] Role do usuário ${targetUserId} na proposta ${proposalId} atualizado para ${newRole}`);

    return NextResponse.json({
      success: true,
      message: 'Role atualizado com sucesso',
      data: {
        userId: targetUserId,
        proposalId,
        role: newRole
      }
    });

  } catch (error: any) {
    console.error('❌ [PROPOSAL_ACCESS] Erro ao atualizar role:', {
      error: error.message,
      clerkTraceId: error.clerkTraceId,
      stack: error.stack
    });
    
    if (error.message.includes('não encontrado') || error.message.includes('inválido')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 