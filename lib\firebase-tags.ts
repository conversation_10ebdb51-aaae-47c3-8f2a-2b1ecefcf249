// Funções para gerenciar etiquetas (tags)

import { db } from './firebase';
import { Tag } from '../types/influencer';
import { firestore } from 'firebase-admin';

// Referência à coleção de etiquetas
export const tagsCollection = db.collection('tags');

// Buscar todas as etiquetas
export async function getAllTags(): Promise<Tag[]> {
  try {
    const snapshot = await tagsCollection.orderBy('name').get();
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        color: data.color,
        createdAt: data.createdAt?.toDate() || new Date()
      } as Tag;
    });
  } catch (error) {
    console.error('Erro ao buscar etiquetas:', error);
    throw error;
  }
}

// Buscar uma etiqueta específica por ID
export async function getTagById(id: string): Promise<Tag | null> {
  try {
    const doc = await tagsCollection.doc(id).get();
    if (!doc.exists) {
      return null;
    }
    const data = doc.data();
    if (!data) {
      return null;
    }
    return {
      id: doc.id,
      name: data.name,
      color: data.color,
      createdAt: data.createdAt?.toDate() || new Date()
    } as Tag;
  } catch (error) {
    console.error(`Erro ao buscar etiqueta com ID ${id}:`, error);
    throw error;
  }
}

// Adicionar uma nova etiqueta
export async function addTag(data: Omit<Tag, 'id'>): Promise<string> {
  try {
    const tagData = {
      name: data.name,
      color: data.color,
      createdAt: firestore.Timestamp.now()
    };
    
    const docRef = await tagsCollection.add(tagData);
    return docRef.id;
  } catch (error) {
    console.error('Erro ao adicionar etiqueta:', error);
    throw error;
  }
}

// Atualizar uma etiqueta
export async function updateTag(id: string, data: Partial<Omit<Tag, 'id' | 'createdAt'>>): Promise<boolean> {
  try {
    const updateData: Record<string, any> = {};
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.color !== undefined) updateData.color = data.color;
    
    await tagsCollection.doc(id).update(updateData);
    return true;
  } catch (error) {
    console.error(`Erro ao atualizar etiqueta com ID ${id}:`, error);
    throw error;
  }
}

// Excluir uma etiqueta
export async function deleteTag(id: string): Promise<boolean> {
  try {
    await tagsCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir etiqueta com ID ${id}:`, error);
    throw error;
  }
}

// Adicionar etiqueta a um influenciador
export async function addTagToInfluencer(influencerId: string, tagId: string): Promise<boolean> {
  try {
    const influencerRef = db.collection('influencers').doc(influencerId);
    const influencerDoc = await influencerRef.get();
    
    if (!influencerDoc.exists) {
      throw new Error(`Influenciador com ID ${influencerId} não encontrado`);
    }
    
    const tagDoc = await tagsCollection.doc(tagId).get();
    if (!tagDoc.exists) {
      throw new Error(`Etiqueta com ID ${tagId} não encontrada`);
    }
    
    // Buscar tags atuais do influenciador
    const influencerData = influencerDoc.data();
    const currentTags = influencerData?.tags || [];
    
    // Verificar se a tag já está associada
    if (currentTags.some((tag: any) => tag.id === tagId)) {
      return true; // Tag já está associada
    }
    
    // Adicionar a nova tag
    const tagData = tagDoc.data();
    const newTag = {
      id: tagId,
      name: tagData?.name,
      color: tagData?.color
    };
    
    await influencerRef.update({
      tags: firestore.FieldValue.arrayUnion(newTag)
    });
    
    return true;
  } catch (error) {
    console.error(`Erro ao adicionar etiqueta ${tagId} ao influenciador ${influencerId}:`, error);
    throw error;
  }
}

// Remover etiqueta de um influenciador
export async function removeTagFromInfluencer(influencerId: string, tagId: string): Promise<boolean> {
  try {
    const influencerRef = db.collection('influencers').doc(influencerId);
    const influencerDoc = await influencerRef.get();
    
    if (!influencerDoc.exists) {
      throw new Error(`Influenciador com ID ${influencerId} não encontrado`);
    }
    
    // Buscar tags atuais do influenciador
    const influencerData = influencerDoc.data();
    const currentTags = influencerData?.tags || [];
    
    // Filtrar a tag a ser removida
    const updatedTags = currentTags.filter((tag: any) => tag.id !== tagId);
    
    // Atualizar o documento do influenciador
    await influencerRef.update({ tags: updatedTags });
    
    return true;
  } catch (error) {
    console.error(`Erro ao remover etiqueta ${tagId} do influenciador ${influencerId}:`, error);
    throw error;
  }
}


