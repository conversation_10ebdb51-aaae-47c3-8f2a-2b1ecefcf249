{"buildCommand": "npm run build", "devCommand": "npm run dev", "outputDirectory": ".next", "framework": "nextjs", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' blob: https://vercel.live https://www.googletagmanager.com https://www.google-analytics.com https://*.clerk.accounts.dev https://clerk-telemetry.com https://challenges.cloudflare.com https://js.workos.com https://api.workos.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://challenges.cloudflare.com; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://firebase.googleapis.com https://*.firebase.googleapis.com https://*.firebaseio.com wss://*.firebaseio.com https://firebaseinstallations.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://firestore.googleapis.com https://*.firestore.googleapis.com wss://firestore.googleapis.com wss://*.firestore.googleapis.com https://www.google-analytics.com https://analytics.google.com https://viacep.com.br https://*.clerk.accounts.dev https://clerk-telemetry.com https://js.workos.com https://api.workos.com; frame-src 'self' https://*.clerk.accounts.dev https://challenges.cloudflare.com https://workos.com;"}]}], "redirects": [{"source": "/admin/(.*)", "destination": "/dashboard", "permanent": false, "has": [{"type": "cookie", "key": "brand-session", "value": "(?<token>.*)"}]}], "rewrites": [{"source": "/api/admin/(.*)", "destination": "/api/admin/$1"}], "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}