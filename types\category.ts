// Modelo de dados para categorias

export interface Category {
  id: string;             // ID único
  name: string;           // Nome da categoria
  slug: string;           // Versão formatada para URLs
  parentId?: string;      // Para criar hierarquia (opcional)
  description?: string;   // Descrição da categoria
  icon?: string;          // URL ou nome do ícone
  color?: string;         // Código de cor para UI
  order?: number;         // Para ordenação personalizada
  createdAt: Date;        // Data de criação
  updatedAt: Date;        // Data de última atualização
}


