import { useState, useEffect } from 'react'

interface Screenshot {
  id: string
  influencerId: string
  url: string
}

export function useScreenshots(influencerId: string | undefined, platform?: string) {
  const [screenshots, setScreenshots] = useState<Screenshot[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchScreenshots = async () => {
    if (!influencerId) {
      setScreenshots([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      console.log('📸 [useScreenshots] Buscando screenshots para:', { influencerId, platform })

      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
                    query GetInfluencerScreenshots($influencerId: ID!, $platform: String) {
          influencerScreenshots(influencerId: $influencerId, platform: $platform) {
            id
            influencerId
            url
          }
        }
          `,
          variables: {
            influencerId,
            platform
          }
        })
      })

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`)
      }

      const result = await response.json()

      if (result.errors) {
        console.error('📸 [useScreenshots] Erros GraphQL:', result.errors)
        throw new Error(result.errors[0]?.message || 'Erro GraphQL desconhecido')
      }

      const screenshotsData = result.data?.influencerScreenshots || []

      console.log('📸 [useScreenshots] Screenshots carregados:', screenshotsData.length)
      setScreenshots(screenshotsData)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      console.error('📸 [useScreenshots] Erro ao carregar screenshots:', errorMessage)
      setError(errorMessage)
      setScreenshots([])
    } finally {
      setLoading(false)
    }
  }

  // Buscar screenshots quando influencerId ou platform mudarem
  useEffect(() => {
    fetchScreenshots()
  }, [influencerId, platform])

  // Função para recarregar screenshots manualmente
  const refetch = () => {
    fetchScreenshots()
  }

  // Função para adicionar screenshot localmente (otimistic update)
  const addScreenshot = (screenshot: Screenshot) => {
    setScreenshots(prev => [...prev, screenshot])
  }

  // Função para remover screenshot localmente
  const removeScreenshot = (screenshotId: string) => {
    setScreenshots(prev => prev.filter(s => s.id !== screenshotId))
  }

  return {
    screenshots,
    loading,
    error,
    refetch,
    addScreenshot,
    removeScreenshot
  }
} 

