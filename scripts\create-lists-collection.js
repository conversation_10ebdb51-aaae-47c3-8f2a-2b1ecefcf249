const admin = require('firebase-admin');

// Inicializar Firebase Admin (assumindo que já está configurado)
if (!admin.apps.length) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: "https://deumatch-demo-default-rtdb.firebaseio.com"
  });
}

const db = admin.firestore();

// Estrutura base de uma lista
const createListStructure = (userId, userDisplayName) => ({
  // Dados básicos da lista
  nome: '',
  descricao: '',
  tipoLista: 'estática', // 'estática' | 'dinâmica'
  tipoObjeto: 'influenciadores', // 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo'
  
  // Propriedades do criador e isolamento
  criadoPor: userId,
  criadoPorNome: userDisplayName,
  
  // Dados temporais
  dataCriacao: admin.firestore.FieldValue.serverTimestamp(),
  ultimaAtualizacao: admin.firestore.FieldValue.serverTimestamp(),
  
  // Metadados
  tamanho: 0, // Número de itens na lista
  tags: [], // Array de strings para categorização
  
  // Configurações de privacidade e compartilhamento
  isPublica: false,
  compartilhadaCom: [], // Array de userIds que têm acesso
  permissoes: {
    visualizar: [], // userIds que podem visualizar
    editar: [], // userIds que podem editar
    gerenciar: [] // userIds que podem gerenciar compartilhamento
  },
  
  // Critérios para listas dinâmicas
  criterios: [], // Array de objetos { campo, operador, valor }
  
  // Configurações de atualização automática (para listas dinâmicas)
  configuracaoAtualizacao: {
    frequencia: 'manual', // 'manual' | 'diaria' | 'semanal' | 'mensal'
    ultimaExecucao: null,
    proximaExecucao: null,
    ativa: false
  },
  
  // Estatísticas e métricas
  estatisticas: {
    totalVisualizacoes: 0,
    ultimaVisualizacao: null,
    totalCompartilhamentos: 0,
    totalExportacoes: 0
  },
  
  // Status da lista
  status: 'ativa', // 'ativa' | 'arquivada' | 'deletada'
  
  // Configurações de exportação
  configuracaoExportacao: {
    formatosPadrao: ['csv', 'xlsx'],
    incluirMetadados: true,
    incluirEstatisticas: false
  }
});

// Estrutura para itens dentro das listas
const createListItemStructure = () => ({
  listaId: '', // Referência à lista pai
  itemId: '', // ID do item original (influencer, marca, etc.)
  tipoItem: 'influenciador', // Tipo do item para facilitar queries
  
  // Dados temporais
  dataAdicao: admin.firestore.FieldValue.serverTimestamp(),
  adicionadoPor: '', // userId de quem adicionou
  
  // Metadados de posição (para ordenação manual)
  posicao: 0,
  
  // Dados específicos do item (snapshot para performance)
  itemData: {
    nome: '',
    foto: '',
    // Outros campos relevantes dependendo do tipo
  },
  
  // Status do item na lista
  status: 'ativo', // 'ativo' | 'removido'
  
  // Notas específicas para este item nesta lista
  notas: '',
  
  // Tags específicas para este item nesta lista
  tagsPersonalizadas: []
});

// Função para criar documentos de exemplo
async function createSampleLists() {
  console.log('🔄 Criando estrutura de listas no Firestore...');
  
  try {
    // Buscar usuários existentes para criar listas de exemplo
    const usersSnapshot = await db.collection('users').limit(2).get();
    
    if (usersSnapshot.empty) {
      console.log('⚠️ Nenhum usuário encontrado. Criando com usuário padrão...');
      
      // Criar lista com usuário padrão
      const sampleList = {
        ...createListStructure('dev-user-123', 'Usuário de Desenvolvimento'),
        nome: 'Lista de Exemplo - Influenciadores Fashion',
        descricao: 'Lista de exemplo para demonstrar a funcionalidade do sistema',
        tipoObjeto: 'influenciadores',
        tags: ['fashion', 'exemplo', 'demo'],
        criterios: [
          {
            campo: 'seguidores',
            operador: '>',
            valor: '50000'
          },
          {
            campo: 'nicho',
            operador: '=',
            valor: 'fashion'
          }
        ]
      };
      
      const docRef = await db.collection('lists').add(sampleList);
      console.log(`✅ Lista de exemplo criada com ID: ${docRef.id}`);
      
    } else {
      // Criar listas para usuários existentes
      for (const userDoc of usersSnapshot.docs) {
        const userData = userDoc.data();
        const userId = userDoc.id;
        const userDisplayName = userData.name || userData.displayName || 'Usuário';
        
        // Lista de Influenciadores
        const influencerList = {
          ...createListStructure(userId, userDisplayName),
          nome: `Top Influenciadores Fashion - ${userDisplayName}`,
          descricao: 'Lista dos principais influenciadores de moda e lifestyle',
          tipoObjeto: 'influenciadores',
          tipoLista: 'dinâmica',
          tags: ['fashion', 'lifestyle', 'top-tier'],
          criterios: [
            {
              campo: 'seguidores',
              operador: '>',
              valor: '50000'
            },
            {
              campo: 'nicho',
              operador: '=',
              valor: 'fashion'
            },
            {
              campo: 'engajamento',
              operador: '>',
              valor: '3'
            }
          ]
        };
        
        // Lista de Marcas
        const brandList = {
          ...createListStructure(userId, userDisplayName),
          nome: `Marcas Tecnologia - ${userDisplayName}`,
          descricao: 'Lista manual de marcas do setor tecnológico para campanhas',
          tipoObjeto: 'marcas',
          tipoLista: 'estática',
          tags: ['tech', 'b2b', 'inovação']
        };
        
        // Criar as listas no Firestore
        const influencerDocRef = await db.collection('lists').add(influencerList);
        const brandDocRef = await db.collection('lists').add(brandList);
        
        console.log(`✅ Listas criadas para usuário ${userDisplayName}:`);
        console.log(`   - Influenciadores: ${influencerDocRef.id}`);
        console.log(`   - Marcas: ${brandDocRef.id}`);
      }
    }
    
    // Criar índices compostos necessários
    console.log('\n📝 Criando índices recomendados...');
    console.log('Execute os seguintes comandos no Firebase Console ou CLI:');
    console.log('');
    console.log('firebase firestore:indexes');
    console.log('');
    console.log('Índices necessários:');
    console.log('1. Coleção: lists');
    console.log('   Campos: criadoPor (Ascending), status (Ascending), dataCriacao (Descending)');
    console.log('');
    console.log('2. Coleção: lists');
    console.log('   Campos: criadoPor (Ascending), tipoObjeto (Ascending), ultimaAtualizacao (Descending)');
    console.log('');
    console.log('3. Coleção: list_items');
    console.log('   Campos: listaId (Ascending), status (Ascending), posicao (Ascending)');
    
    console.log('\n✅ Estrutura de listas criada com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao criar estrutura de listas:', error);
    throw error;
  }
}

// Função para criar subcoleção de itens da lista
async function setupListItemsStructure() {
  console.log('\n🔄 Configurando estrutura de itens das listas...');
  
  // A subcoleção list_items será criada automaticamente quando itens forem adicionados
  // Mas vamos documentar a estrutura
  
  const listItemExample = createListItemStructure();
  console.log('📋 Estrutura de item da lista:', JSON.stringify(listItemExample, null, 2));
  
  console.log('✅ Estrutura de itens das listas documentada!');
}

// Executar criação se chamado diretamente
if (require.main === module) {
  createSampleLists()
    .then(() => setupListItemsStructure())
    .then(() => {
      console.log('\n🎉 Configuração da estrutura de listas concluída!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Erro durante configuração:', error);
      process.exit(1);
    });
}

module.exports = {
  createListStructure,
  createListItemStructure,
  createSampleLists,
  setupListItemsStructure
}; 
