import {
  Lista,
  ItemLista,
  CriarListaData,
  AtualizarListaData,
  AdicionarItemListaData,
  FiltrosLista,
  ResultadoBuscaListas,
  OperacaoLoteListaData,
  ResultadoOperacaoLote
} from '@/types/list';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';
import { db, FieldValue } from '@/lib/firebase-admin';

/**
 * 🔄 LIST SERVICE V2 - REFATORADO PARA SUBCOLEÇÕES
 * Nova implementação usando subcoleções para melhor organização e performance
 * 
 * Estrutura:
 * /lists/{listId}
 *   ├── (campos da lista: nome, descrição, etc.)
 *   └── /items/{itemId}
 *       ├── itemId: string
 *       ├── tipoItem: string
 *       ├── dadosItem: object
 *       └── (outros campos do item)
 */
export class ListServiceV2 {
  private static COLLECTION_NAME = 'lists';
  private static ITEMS_SUBCOLLECTION = 'items';

  /**
   * 🔍 Buscar lista por ID com validação de acesso
   */
  static async buscarListaPorId(listaId: string, userId: string): Promise<Lista | null> {
    try {
      console.log('[LIST_SERVICE_V2_GET_BY_ID] Buscando lista:', { listaId, userId });

      const listaRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      const listaDoc = await listaRef.get();

      if (!listaDoc.exists) {
        console.log('[LIST_SERVICE_V2_GET_BY_ID] Lista não encontrada');
        return null;
      }

      const listaData = listaDoc.data() as Lista;
      listaData.id = listaDoc.id;

      // Verificar acesso
      if (!this.podeVisualizarLista(listaData, userId)) {
        console.log('[LIST_SERVICE_V2_GET_BY_ID] Usuário sem permissão para visualizar lista');
        return null;
      }

      // Atualizar contagem de itens em tempo real
      const itemsCount = await this.contarItensLista(listaId);
      listaData.tamanho = itemsCount;

      console.log('[LIST_SERVICE_V2_GET_BY_ID] Lista encontrada:', {
        id: listaData.id,
        nome: listaData.nome,
        tamanho: listaData.tamanho
      });

      return listaData;
    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_GET_BY_ID] Erro:', { listaId, userId, error });
      throw new Error(`Erro ao buscar lista: ${error.message}`);
    }
  }

  /**
   * 📊 Contar itens ativos de uma lista usando subcoleção
   */
  static async contarItensLista(listaId: string): Promise<number> {
    try {
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      const snapshot = await itemsRef
        .where('status', '==', 'ativo')
        .get();

      return snapshot.size;
    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_COUNT_ITEMS] Erro:', { listaId, error });
      return 0;
    }
  }

  /**
   * 📝 Buscar itens de uma lista usando subcoleção
   */
  static async buscarItensLista(
    listaId: string, 
    userId: string,
    limite: number = 50,
    offset: number = 0
  ): Promise<{ itens: ItemLista[]; total: number }> {
    try {
      // Verificar acesso à lista
      const lista = await this.buscarListaPorId(listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          listaId
        );
      }

      console.log('[LIST_SERVICE_V2_GET_ITEMS] Executando query na subcoleção:', {
        listaId,
        limite,
        offset
      });

      // Query na subcoleção de itens
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      // Buscar itens ativos ordenados por posição
      const query = itemsRef
        .where('status', '==', 'ativo')
        .orderBy('posicao', 'asc')
        .limit(limite + offset);

      const snapshot = await query.get();

      console.log('[LIST_SERVICE_V2_GET_ITEMS] Resultado da query:', {
        empty: snapshot.empty,
        size: snapshot.size
      });

      const todosItens = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          listaId, // Adicionar listaId para compatibilidade
          ...data
        };
      }) as ItemLista[];

      // Aplicar offset manualmente
      const itens = todosItens.slice(offset, offset + limite);

      // Contar total de itens ativos
      const total = await this.contarItensLista(listaId);

      console.log('[LIST_SERVICE_V2_GET_ITEMS] Resultado final:', {
        itensRetornados: itens.length,
        totalItens: total,
        offset,
        limite
      });

      return { itens, total };

    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_GET_ITEMS] Erro:', { listaId, userId, error });
      throw new Error(`Erro ao buscar itens da lista: ${error.message}`);
    }
  }

  /**
   * ➕ Adicionar item à lista usando subcoleção
   */
  static async adicionarItemLista(data: AdicionarItemListaData, userId: string): Promise<ItemLista> {
    try {
      // Verificar se a lista existe e se o usuário pode editá-la
      const lista = await this.buscarListaPorId(data.listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          data.listaId
        );
      }

      if (!this.podeEditarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para adicionar itens nesta lista',
          userId,
          data.listaId
        );
      }

      // Verificar se o item já está na lista
      const itemExistente = await this.verificarItemNaLista(data.listaId, data.itemId);
      if (itemExistente) {
        throw new Error('Item já está presente na lista');
      }

      // Calcular próxima posição
      const proximaPosicao = await this.calcularProximaPosicao(data.listaId);

      // Preparar dados do item
      const dadosItem = IsolationUtils.prepareCreateData(
        {
          itemId: data.itemId,
          tipoItem: data.tipoItem,
          dataAdicao: new Date(),
          adicionadoPor: userId,
          posicao: proximaPosicao,
          itemData: {
            itemId: data.itemId,
            tipoItem: data.tipoItem
          },
          status: 'ativo' as const,
          notas: data.notas || '',
          tagsPersonalizadas: data.tagsPersonalizadas || []
        },
        userId,
        userId
      );

      // Filtrar valores undefined
      const dadosLimpos = this.filterUndefinedValues(dadosItem);

      console.log('[LIST_SERVICE_V2_ADD_ITEM] Salvando item na subcoleção:', {
        listaId: data.listaId,
        itemId: data.itemId,
        tipoItem: data.tipoItem,
        posicao: proximaPosicao
      });

      // Salvar na subcoleção
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(data.listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      const docRef = await itemsRef.add(dadosLimpos);

      console.log('[LIST_SERVICE_V2_ADD_ITEM] Item salvo com sucesso:', {
        docId: docRef.id,
        subcollection: `${this.COLLECTION_NAME}/${data.listaId}/${this.ITEMS_SUBCOLLECTION}`
      });

      // Atualizar tamanho da lista
      await this.incrementarTamanhoLista(data.listaId);

      // Log da adição
      IsolationUtils.logIsolationEvent(
        'create',
        'list_items_v2',
        docRef.id,
        userId,
        { 
          listaId: data.listaId,
          itemId: data.itemId,
          tipoItem: data.tipoItem,
          subcollection: true
        }
      );

      return {
        id: docRef.id,
        listaId: data.listaId, // Adicionar para compatibilidade
        ...dadosItem
      } as unknown as ItemLista;

    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_ADD_ITEM] Erro:', { data, userId, error });
      throw new Error(`Erro ao adicionar item à lista: ${error.message}`);
    }
  }

  /**
   * 🗑️ Remover item da lista (soft delete)
   */
  static async removerItemLista(listaId: string, itemId: string, userId: string): Promise<void> {
    try {
      // Verificar acesso à lista
      const lista = await this.buscarListaPorId(listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          listaId
        );
      }

      if (!this.podeEditarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para remover itens desta lista',
          userId,
          listaId
        );
      }

      // Buscar item na subcoleção
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      const itemQuery = await itemsRef
        .where('itemId', '==', itemId)
        .where('status', '==', 'ativo')
        .limit(1)
        .get();

      if (itemQuery.empty) {
        throw new Error('Item não encontrado na lista');
      }

      const itemDoc = itemQuery.docs[0];

      // Soft delete - marcar como removido
      await itemDoc.ref.update({
        status: 'removido',
        removidoEm: new Date(),
        removidoPor: userId,
        ultimaAtualizacao: new Date()
      });

      // Decrementar tamanho da lista
      await this.decrementarTamanhoLista(listaId);

      console.log('[LIST_SERVICE_V2_REMOVE_ITEM] Item removido com sucesso:', {
        listaId,
        itemId,
        docId: itemDoc.id
      });

      // Log da remoção
      IsolationUtils.logIsolationEvent(
        'update',
        'list_items_v2',
        itemDoc.id,
        userId,
        { 
          listaId,
          itemId,
          action: 'soft_delete',
          subcollection: true
        }
      );

    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_REMOVE_ITEM] Erro:', { listaId, itemId, userId, error });
      throw new Error(`Erro ao remover item da lista: ${error.message}`);
    }
  }

  /**
   * 🗑️ Deletar lista permanentemente com todos os itens
   */
  static async deletarLista(listaId: string, userId: string): Promise<void> {
    try {
      // Verificar ownership
      const lista = await this.buscarListaPorId(listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          listaId
        );
      }

      if (!this.podeGerenciarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para deletar esta lista',
          userId,
          listaId
        );
      }

      console.log('[LIST_SERVICE_V2_DELETE] Iniciando deleção da lista e subcoleção:', listaId);

      // Usar batch para operação atômica
      const batch = db.batch();

      // 1. Deletar todos os itens da subcoleção
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      const itemsSnapshot = await itemsRef.get();

      console.log('[LIST_SERVICE_V2_DELETE] Itens a serem deletados:', itemsSnapshot.size);

      itemsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // 2. Deletar o documento da lista
      const listaRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      batch.delete(listaRef);

      // Executar batch
      await batch.commit();

      console.log('[LIST_SERVICE_V2_DELETE] Lista e itens deletados com sucesso:', {
        listaId,
        itensRemovidos: itemsSnapshot.size
      });

      // Log da deleção
      IsolationUtils.logIsolationEvent(
        'delete',
        'lists_v2',
        listaId,
        userId,
        {
          nome: lista.nome,
          totalItens: lista.tamanho,
          totalItensRemovidos: itemsSnapshot.size,
          tipoDeletacao: 'permanente_com_subcoleção'
        }
      );

    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_DELETE] Erro:', { listaId, userId, error });
      throw new Error(`Erro ao deletar lista: ${error.message}`);
    }
  }

  // ===== MÉTODOS AUXILIARES =====

  /**
   * ✅ Verificar se item já existe na lista
   */
  private static async verificarItemNaLista(listaId: string, itemId: string): Promise<boolean> {
    try {
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      const query = await itemsRef
        .where('itemId', '==', itemId)
        .where('status', '==', 'ativo')
        .limit(1)
        .get();

      return !query.empty;
    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_CHECK_ITEM] Erro:', { listaId, itemId, error });
      return false;
    }
  }

  /**
   * 🔢 Calcular próxima posição para item
   */
  private static async calcularProximaPosicao(listaId: string): Promise<number> {
    try {
      const itemsRef = db.collection(this.COLLECTION_NAME)
        .doc(listaId)
        .collection(this.ITEMS_SUBCOLLECTION);

      const query = await itemsRef
        .where('status', '==', 'ativo')
        .orderBy('posicao', 'desc')
        .limit(1)
        .get();

      if (query.empty) {
        return 1;
      }

      const ultimoItem = query.docs[0].data();
      return (ultimoItem.posicao || 0) + 1;
    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_CALC_POSITION] Erro:', { listaId, error });
      return 1;
    }
  }

  /**
   * ➕ Incrementar tamanho da lista
   */
  private static async incrementarTamanhoLista(listaId: string): Promise<void> {
    try {
      const listaRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      await listaRef.update({
        tamanho: FieldValue.increment(1),
        ultimaAtualizacao: new Date()
      });
    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_INCREMENT_SIZE] Erro:', { listaId, error });
    }
  }

  /**
   * ➖ Decrementar tamanho da lista
   */
  private static async decrementarTamanhoLista(listaId: string): Promise<void> {
    try {
      const listaRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      await listaRef.update({
        tamanho: FieldValue.increment(-1),
        ultimaAtualizacao: new Date()
      });
    } catch (error: any) {
      console.error('[LIST_SERVICE_V2_DECREMENT_SIZE] Erro:', { listaId, error });
    }
  }

  // ===== MÉTODOS DE PERMISSÃO =====

  private static podeVisualizarLista(lista: Lista, userId: string): boolean {
    return lista.criadoPor === userId ||
           lista.permissoes.visualizar.includes(userId) ||
           lista.isPublica;
  }

  private static podeEditarLista(lista: Lista, userId: string): boolean {
    return lista.criadoPor === userId ||
           lista.permissoes.editar.includes(userId);
  }

  private static podeGerenciarLista(lista: Lista, userId: string): boolean {
    return lista.criadoPor === userId ||
           lista.permissoes.gerenciar.includes(userId);
  }

  /**
   * 🧹 Filtrar valores undefined para Firestore
   */
  private static filterUndefinedValues(obj: any): any {
    const filtered: any = {};

    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
          filtered[key] = this.filterUndefinedValues(value);
        } else {
          filtered[key] = value;
        }
      }
    }

    return filtered;
  }
}
