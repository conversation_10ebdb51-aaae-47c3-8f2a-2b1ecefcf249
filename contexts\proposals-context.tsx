"use client";

import { createContext, useContext, useCallback, useEffect, useState, ReactNode } from 'react';
import { useUser } from '@clerk/nextjs';

interface Proposal {
  id: string;
  nome: string;
  descricao?: string;
  criadoPor: string;
  status: string;
  priority?: string;
  brandId?: string;
  influencers?: string[];
  services?: any[];
  totalAmount?: number;
  dataEnvio?: string;
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  icon?: string;
}

interface ProposalsContextType {
  proposals: Proposal[];
  loading: boolean;
  error: string | null;
  refreshProposals: () => Promise<void>;
  lastRefresh: number;
}

const ProposalsContext = createContext<ProposalsContextType | undefined>(undefined);

// ⚡ OTIMIZAÇÃO: Cache global para evitar múltiplas chamadas simultâneas
let globalProposalsCache: {
  data: Proposal[];
  timestamp: number;
  loading: boolean;
} = {
  data: [],
  timestamp: 0,
  loading: false
};

const CACHE_TTL = 30000; // 30 segundos

export function ProposalsProvider({ children }: { children: ReactNode }) {
  const { user } = useUser();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState(0);

  // ⚡ OTIMIZAÇÃO: Função centralizada para buscar propostas
  const fetchProposals = useCallback(async (forceRefresh = false) => {
    if (!user?.id) {
      setProposals([]);
      setLoading(false);
      setError(null);
      return;
    }

    const now = Date.now();
    
    // ⚡ VERIFICAR CACHE: Usar cache se não forçar refresh e estiver dentro do TTL
    if (!forceRefresh && 
        globalProposalsCache.data.length > 0 && 
        (now - globalProposalsCache.timestamp) < CACHE_TTL) {
      console.log('🚀 [PROPOSALS_CONTEXT] Usando cache existente');
      setProposals(globalProposalsCache.data);
      setLoading(false);
      setError(null);
      setLastRefresh(globalProposalsCache.timestamp);
      return;
    }

    // ⚡ VERIFICAR SE JÁ ESTÁ CARREGANDO: Evitar múltiplas chamadas simultâneas
    if (globalProposalsCache.loading && !forceRefresh) {
      console.log('🚀 [PROPOSALS_CONTEXT] Carregamento já em andamento, aguardando...');
      // Aguardar carregamento em andamento
      const checkInterval = setInterval(() => {
        if (!globalProposalsCache.loading) {
          clearInterval(checkInterval);
          setProposals(globalProposalsCache.data);
          setLoading(false);
          setError(null);
          setLastRefresh(globalProposalsCache.timestamp);
        }
      }, 100);
      return;
    }

    try {
      console.log('🚀 [PROPOSALS_CONTEXT] Iniciando carregamento de propostas para usuário:', user.id);
      
      // Marcar como carregando globalmente
      globalProposalsCache.loading = true;
      setLoading(true);
      setError(null);

      const response = await fetch('/api/proposals', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': user.id,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const apiResponse = await response.json();
      
      if (apiResponse.success && apiResponse.data) {
        // Mapear dados da API para o formato esperado
        const mappedProposals: Proposal[] = apiResponse.data.map((proposal: any) => ({
          id: proposal.id,
          nome: proposal.nome || 'Proposta sem nome',
          descricao: proposal.descricao || '',
          criadoPor: proposal.criadoPor || '',
          status: proposal.status || 'draft',
          priority: proposal.priority || 'medium',
          brandId: proposal.brandId || '',
          influencers: proposal.influencers || [],
          services: proposal.services || [],
          totalAmount: proposal.totalAmount || 0,
          dataEnvio: proposal.dataEnvio || '',
          createdAt: proposal.createdAt ? new Date(proposal.createdAt) : new Date(),
          updatedAt: proposal.updatedAt ? new Date(proposal.updatedAt) : new Date(),
          sentAt: proposal.sentAt ? new Date(proposal.sentAt) : undefined,
          icon: proposal.icon || 'FileText'
        }));

        // Atualizar cache global
        globalProposalsCache = {
          data: mappedProposals,
          timestamp: now,
          loading: false
        };

        setProposals(mappedProposals);
        setLastRefresh(now);
        
        console.log(`✅ [PROPOSALS_CONTEXT] ${mappedProposals.length} propostas carregadas com sucesso`);
      } else {
        console.warn('⚠️ [PROPOSALS_CONTEXT] API retornou sem dados:', apiResponse);
        globalProposalsCache = {
          data: [],
          timestamp: now,
          loading: false
        };
        setProposals([]);
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      console.error('❌ [PROPOSALS_CONTEXT] Erro ao carregar propostas:', err);
      
      globalProposalsCache.loading = false;
      setError(errorMessage);
      setProposals([]);
    } finally {
      globalProposalsCache.loading = false;
      setLoading(false);
    }
  }, [user?.id]);

  // ⚡ FUNÇÃO PÚBLICA: Refresh manual das propostas
  const refreshProposals = useCallback(async () => {
    await fetchProposals(true);
  }, [fetchProposals]);

  // ⚡ CARREGAMENTO INICIAL: Quando o usuário está disponível
  useEffect(() => {
    if (user?.id) {
      fetchProposals(false);
    }
  }, [user?.id, fetchProposals]);

  // ⚡ AUTO-REFRESH: A cada 5 minutos
  useEffect(() => {
    if (!user?.id) return;

    const interval = setInterval(() => {
      fetchProposals(false); // Refresh automático respeitando cache
    }, 5 * 60 * 1000); // 5 minutos

    return () => clearInterval(interval);
  }, [user?.id, fetchProposals]);

  const value: ProposalsContextType = {
    proposals,
    loading,
    error,
    refreshProposals,
    lastRefresh
  };

  return (
    <ProposalsContext.Provider value={value}>
      {children}
    </ProposalsContext.Provider>
  );
}

// ⚡ HOOK CUSTOMIZADO: Para usar o contexto
export function useProposals() {
  const context = useContext(ProposalsContext);
  if (context === undefined) {
    throw new Error('useProposals deve ser usado dentro de um ProposalsProvider');
  }
  return context;
} 