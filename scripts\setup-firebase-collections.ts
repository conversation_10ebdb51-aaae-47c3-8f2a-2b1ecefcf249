/**
 * Script para configurar as coleções iniciais do Firebase
 * Execute este script para criar a estrutura básica das coleções
 */

import { 
  createBasicCollections,
  addStructuredInfluencer,
  type InfluencerStructured 
} from './recreate-influencer-structure';
import { db } from '../lib/firebase';
import { collection, addDoc, doc, setDoc } from 'firebase/firestore';

// ===== DADOS DE EXEMPLO =====

/**
 * Propostas de exemplo para o sistema
 */
const exampleProposals = [
  {
    campaignId: 'campaign_example_1',
    influencerId: 'influencer_example_1',
    brandId: 'brand_example_1',
    title: 'Campanha de Verão - Moda Praia',
    description: 'Proposta para divulgação da nova coleção de moda praia da marca, focando no público jovem e estilo de vida saudável.',
    status: 'sent',
    totalAmount: 2500,
    currency: 'BRL',
    deliverables: [
      {
        id: 'del_1',
        type: 'post',
        platform: 'instagram',
        description: 'Post no feed com look completo',
        quantity: 2,
        price: 800,
        requirements: 'Usar hashtags da marca, marcar localização na praia'
      },
      {
        id: 'del_2',
        type: 'story',
        platform: 'instagram',
        description: 'Stories mostrando o processo de escolha do look',
        quantity: 5,
        price: 150,
        requirements: 'Mostrar etiquetas dos produtos'
      },
      {
        id: 'del_3',
        type: 'reel',
        platform: 'instagram',
        description: 'Reel com transição de looks',
        quantity: 1,
        price: 1200,
        requirements: 'Música trending, duração mínima 15s'
      }
    ],
    terms: {
      paymentTerms: 'Pagamento em 30 dias após entrega',
      deliveryDeadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
      revisionRounds: 2,
      usageRights: 'Uso comercial por 12 meses',
      cancellationPolicy: 'Cancelamento com 48h de antecedência'
    },
    expectedMetrics: {
      minReach: 50000,
      minEngagement: 2000,
      targetAudience: 'Mulheres 18-35 anos interessadas em moda'
    },
    maxNegotiationRounds: 3,
    currentNegotiationRound: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'brand_example_1',
    lastModifiedBy: 'brand_example_1'
  },
  {
    campaignId: 'campaign_example_2',
    influencerId: 'influencer_example_2',
    brandId: 'brand_example_2',
    title: 'Review de Produtos de Beleza',
    description: 'Proposta para review honesto dos novos produtos da linha de skincare, com foco na rotina noturna.',
    status: 'negotiating',
    totalAmount: 1800,
    originalAmount: 2000,
    currency: 'BRL',
    deliverables: [
      {
        id: 'del_4',
        type: 'review',
        platform: 'youtube',
        description: 'Vídeo review completo dos produtos',
        quantity: 1,
        price: 1200,
        requirements: 'Mostrar antes e depois, mencionar ingredientes'
      },
      {
        id: 'del_5',
        type: 'post',
        platform: 'instagram',
        description: 'Post com resultados após 7 dias de uso',
        quantity: 1,
        price: 600,
        requirements: 'Fotos com boa iluminação, close no rosto'
      }
    ],
    terms: {
      paymentTerms: 'Pagamento em 15 dias após entrega',
      deliveryDeadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
      revisionRounds: 1,
      usageRights: 'Uso comercial por 6 meses',
      cancellationPolicy: 'Cancelamento com 72h de antecedência'
    },
    expectedMetrics: {
      minReach: 30000,
      minEngagement: 1500,
      targetAudience: 'Mulheres 25-45 anos interessadas em skincare'
    },
    maxNegotiationRounds: 3,
    currentNegotiationRound: 1,
    negotiationHistory: [
      {
        id: 'neg_1',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        actor: 'brand',
        action: 'sent',
        message: 'Proposta inicial enviada'
      },
      {
        id: 'neg_2',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        actor: 'influencer',
        action: 'counter_offer',
        message: 'Gostaria de negociar o valor e prazo',
        previousAmount: 2000,
        newAmount: 1800
      }
    ],
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    createdBy: 'brand_example_2',
    lastModifiedBy: 'influencer_example_2'
  },
  {
    campaignId: 'campaign_example_3',
    influencerId: 'influencer_example_3',
    brandId: 'brand_example_1',
    title: 'Unboxing e Tutorial - Produtos Tech',
    description: 'Proposta para unboxing e tutorial de uso dos novos gadgets da marca, com foco no público tech.',
    status: 'accepted',
    totalAmount: 3200,
    currency: 'BRL',
    deliverables: [
      {
        id: 'del_6',
        type: 'unboxing',
        platform: 'youtube',
        description: 'Vídeo de unboxing detalhado',
        quantity: 1,
        price: 1500,
        requirements: 'Mostrar todos os acessórios, primeiras impressões'
      },
      {
        id: 'del_7',
        type: 'tutorial',
        platform: 'youtube',
        description: 'Tutorial de configuração e uso',
        quantity: 1,
        price: 1200,
        requirements: 'Passo a passo detalhado, dicas de uso'
      },
      {
        id: 'del_8',
        type: 'reel',
        platform: 'instagram',
        description: 'Reel com highlights dos produtos',
        quantity: 2,
        price: 250,
        requirements: 'Focar nos diferenciais, música dinâmica'
      }
    ],
    terms: {
      paymentTerms: 'Pagamento em 45 dias após entrega',
      deliveryDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      revisionRounds: 3,
      usageRights: 'Uso comercial por 24 meses',
      cancellationPolicy: 'Cancelamento com 7 dias de antecedência'
    },
    expectedMetrics: {
      minReach: 80000,
      minEngagement: 4000,
      targetAudience: 'Homens e mulheres 20-40 anos interessados em tecnologia'
    },
    maxNegotiationRounds: 3,
    currentNegotiationRound: 0,
    acceptedAt: new Date(),
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    createdBy: 'brand_example_1',
    lastModifiedBy: 'brand_example_1'
  }
];

/**
 * Categorias básicas para o sistema
 */
const basicCategories = [
  {
    id: 'lifestyle',
    name: 'Lifestyle',
    slug: 'lifestyle',
    description: 'Conteúdo sobre estilo de vida, rotina e bem-estar',
    color: '#FF6B6B',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'fashion',
    name: 'Moda',
    slug: 'fashion',
    description: 'Conteúdo sobre moda, tendências e estilo',
    color: '#4ECDC4',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'beauty',
    name: 'Beleza',
    slug: 'beauty',
    description: 'Conteúdo sobre beleza, skincare e makeup',
    color: '#45B7D1',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'fitness',
    name: 'Fitness',
    slug: 'fitness',
    description: 'Conteúdo sobre exercícios, saúde e bem-estar físico',
    color: '#96CEB4',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'food',
    name: 'Gastronomia',
    slug: 'food',
    description: 'Conteúdo sobre culinária, receitas e gastronomia',
    color: '#FFEAA7',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'travel',
    name: 'Viagem',
    slug: 'travel',
    description: 'Conteúdo sobre viagens, destinos e turismo',
    color: '#DDA0DD',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'tech',
    name: 'Tecnologia',
    slug: 'tech',
    description: 'Conteúdo sobre tecnologia, gadgets e inovação',
    color: '#74B9FF',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'entertainment',
    name: 'Entretenimento',
    slug: 'entertainment',
    description: 'Conteúdo sobre entretenimento, humor e diversão',
    color: '#FD79A8',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Marcas básicas para o sistema
 */
const basicBrands = [
  {
    id: 'nike',
    name: 'Nike',
    slug: 'nike',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/nike-vector-logo.png',
    website: 'https://www.nike.com.br',
    category: 'fitness',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'adidas',
    name: 'Adidas',
    slug: 'adidas',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/adidas-vector-logo.png',
    website: 'https://www.adidas.com.br',
    category: 'fitness',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'coca-cola',
    name: 'Coca-Cola',
    slug: 'coca-cola',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/coca-cola-vector-logo.png',
    website: 'https://www.cocacola.com.br',
    category: 'food',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'samsung',
    name: 'Samsung',
    slug: 'samsung',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/samsung-vector-logo.png',
    website: 'https://www.samsung.com.br',
    category: 'tech',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'loreal',
    name: "L'Oréal",
    slug: 'loreal',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/loreal-vector-logo.png',
    website: 'https://www.loreal.com.br',
    category: 'beauty',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Filtros básicos para o sistema
 */
const basicFilters = [
  {
    id: 'followers-range',
    name: 'Faixa de Seguidores',
    type: 'range',
    options: [
      { value: '1k-10k', label: '1K - 10K', min: 1000, max: 10000 },
      { value: '10k-50k', label: '10K - 50K', min: 10000, max: 50000 },
      { value: '50k-100k', label: '50K - 100K', min: 50000, max: 100000 },
      { value: '100k-500k', label: '100K - 500K', min: 100000, max: 500000 },
      { value: '500k-1m', label: '500K - 1M', min: 500000, max: 1000000 },
      { value: '1m+', label: '1M+', min: 1000000, max: null }
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'engagement-rate',
    name: 'Taxa de Engajamento',
    type: 'range',
    options: [
      { value: '0-1', label: '0% - 1%', min: 0, max: 1 },
      { value: '1-3', label: '1% - 3%', min: 1, max: 3 },
      { value: '3-5', label: '3% - 5%', min: 3, max: 5 },
      { value: '5-10', label: '5% - 10%', min: 5, max: 10 },
      { value: '10+', label: '10%+', min: 10, max: null }
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'location',
    name: 'Localização',
    type: 'select',
    options: [
      { value: 'sp', label: 'São Paulo' },
      { value: 'rj', label: 'Rio de Janeiro' },
      { value: 'mg', label: 'Minas Gerais' },
      { value: 'pr', label: 'Paraná' },
      { value: 'rs', label: 'Rio Grande do Sul' },
      { value: 'sc', label: 'Santa Catarina' },
      { value: 'ba', label: 'Bahia' },
      { value: 'pe', label: 'Pernambuco' },
      { value: 'ce', label: 'Ceará' },
      { value: 'go', label: 'Goiás' }
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'gender',
    name: 'Gênero',
    type: 'select',
    options: [
      { value: 'male', label: 'Masculino' },
      { value: 'female', label: 'Feminino' },
      { value: 'other', label: 'Outro' }
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'age-range',
    name: 'Faixa Etária',
    type: 'range',
    options: [
      { value: '13-17', label: '13-17 anos', min: 13, max: 17 },
      { value: '18-24', label: '18-24 anos', min: 18, max: 24 },
      { value: '25-34', label: '25-34 anos', min: 25, max: 34 },
      { value: '35-44', label: '35-44 anos', min: 35, max: 44 },
      { value: '45-54', label: '45-54 anos', min: 45, max: 54 },
      { value: '55+', label: '55+ anos', min: 55, max: null }
    ],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Exemplo de influenciador para testar a estrutura
 */
const exampleInfluencer = {
  name: 'Maria Silva',
  age: 28,
  gender: 'female',
  bio: 'Influenciadora de lifestyle e moda. Apaixonada por compartilhar dicas de estilo e bem-estar.',
  city: 'São Paulo',
  state: 'SP',
  country: 'Brasil',
  cep: '01310-100',
  email: '<EMAIL>',
  whatsapp: '+5511999999999',
  
  // Redes sociais
  instagram_username: '@mariasilva',
  instagram_followers: 150000,
  instagram_avg_views: 25000,
  instagram_engagement_rate: 4.2,
  instagram_story_price: 800,
  instagram_reel_price: 1500,
  instagram_stories_views: 45000,
  
  tiktok_username: '@mariasilva_',
  tiktok_followers: 80000,
  tiktok_avg_views: 120000,
  tiktok_engagement_rate: 6.8,
  tiktok_price: 1200,
  
  youtube_username: 'Maria Silva',
  youtube_followers: 45000,
  youtube_avg_views: 15000,
  youtube_engagement_rate: 3.5,
  youtube_dedicated_price: 2000,
  youtube_insertion_price: 800,
  youtube_shorts_price: 600,
  
  // Negócios
  agency_name: 'Agência Digital',
  responsible_name: 'João Santos',
  responsible_capturer: 'Ana Costa',
  mainCategories: ['lifestyle', 'fashion'],
  promotes_traders: false,
  content_type: 'Fotos,Vídeos,Stories',
  
  // Métricas
  rating: 4.5,
  
  // Audiência (exemplo para Instagram)
  instagram_audience_gender_male: 25,
  instagram_audience_gender_female: 70,
  instagram_audience_gender_other: 5,
  
  instagram_audience_age_13_17: 15,
  instagram_audience_age_18_24: 35,
  instagram_audience_age_25_34: 30,
  instagram_audience_age_35_44: 15,
  instagram_audience_age_45_54: 5,
  
  instagram_audience_location_sp: 40,
  instagram_audience_location_rj: 25,
  instagram_audience_location_mg: 15,
  instagram_audience_location_other: 20,
  
  // Histórico de marcas
  brand_history: {
    instagram: ['nike', 'loreal'],
    tiktok: ['samsung'],
    youtube: ['coca-cola']
  },
  
  // Tags e notas
  tags: ['lifestyle', 'moda', 'beleza', 'sp'],
  notes: [
    {
      id: 'note1',
      content: 'Influenciadora muito engajada, ótima para campanhas de lifestyle.',
      author: 'João Santos',
      createdAt: new Date(),
      isPrivate: false
    }
  ]
};

// ===== FUNÇÕES DE CONFIGURAÇÃO =====

/**
 * Cria as categorias básicas
 */
async function createCategories() {
  console.log('📂 Criando categorias básicas...');
  
  try {
    for (const category of basicCategories) {
      await setDoc(doc(db, 'categories', category.id), category);
      console.log(`✅ Categoria criada: ${category.name}`);
    }
    console.log('✅ Todas as categorias foram criadas!');
  } catch (error) {
    console.error('❌ Erro ao criar categorias:', error);
    throw error;
  }
}

/**
 * Cria as marcas básicas
 */
async function createBrands() {
  console.log('🏢 Criando marcas básicas...');
  
  try {
    for (const brand of basicBrands) {
      await setDoc(doc(db, 'brands', brand.id), brand);
      console.log(`✅ Marca criada: ${brand.name}`);
    }
    console.log('✅ Todas as marcas foram criadas!');
  } catch (error) {
    console.error('❌ Erro ao criar marcas:', error);
    throw error;
  }
}

/**
 * Cria os filtros básicos
 */
async function createFilters() {
  console.log('🔍 Criando filtros básicos...');
  
  try {
    for (const filter of basicFilters) {
      await setDoc(doc(db, 'filters', filter.id), filter);
      console.log(`✅ Filtro criado: ${filter.name}`);
    }
    console.log('✅ Todos os filtros foram criados!');
  } catch (error) {
    console.error('❌ Erro ao criar filtros:', error);
    throw error;
  }
}

/**
 * Cria um influenciador de exemplo
 */
async function createExampleInfluencer() {
  console.log('👤 Criando influenciador de exemplo...');
  
  try {
    const influencerId = await addStructuredInfluencer(exampleInfluencer);
    console.log(`✅ Influenciador de exemplo criado com ID: ${influencerId}`);
    return influencerId;
  } catch (error) {
    console.error('❌ Erro ao criar influenciador de exemplo:', error);
    throw error;
  }
}

/**
 * Cria propostas de exemplo
 */
async function createProposals() {
  console.log('📋 Criando propostas de exemplo...');
  
  try {
    const proposalsCollection = collection(db, 'proposals');
    
    for (const proposal of exampleProposals) {
      const docRef = await addDoc(proposalsCollection, proposal);
      console.log(`✅ Proposta criada: ${proposal.title} (ID: ${docRef.id})`);
    }
    
    console.log(`✅ ${exampleProposals.length} propostas de exemplo criadas!`);
  } catch (error) {
    console.error('❌ Erro ao criar propostas:', error);
    throw error;
  }
}

/**
 * Cria documento de configuração do sistema
 */
async function createSystemConfig() {
  console.log('⚙️ Criando configuração do sistema...');
  
  const config = {
    version: '2.0.0',
    structureVersion: 'structured-v1',
    createdAt: new Date(),
    updatedAt: new Date(),
    features: {
      structuredData: true,
      financialSeparation: true,
      audienceAnalytics: true,
      brandHistory: true,
      multiPlatform: true
    },
    collections: {
      influencers: 'influencers',
      financials: 'influencer_financials',
      categories: 'categories',
      brands: 'brands',
      filters: 'filters',
      campaigns: 'campaigns',
      proposals: 'proposals'
    }
  };
  
  try {
    await setDoc(doc(db, 'system', 'config'), config);
    console.log('✅ Configuração do sistema criada!');
  } catch (error) {
    console.error('❌ Erro ao criar configuração:', error);
    throw error;
  }
}

// ===== FUNÇÃO PRINCIPAL =====

/**
 * Executa a configuração completa do Firebase
 */
export async function setupFirebaseCollections() {
  console.log('🚀 Iniciando configuração das coleções do Firebase...');
  console.log('=' .repeat(60));
  
  try {
    // 1. Criar coleções básicas
    console.log('\n1️⃣ Criando coleções básicas...');
    await createBasicCollections();
    
    // 2. Criar categorias
    console.log('\n2️⃣ Configurando categorias...');
    await createCategories();
    
    // 3. Criar marcas
    console.log('\n3️⃣ Configurando marcas...');
    await createBrands();
    
    // 4. Criar filtros
    console.log('\n4️⃣ Configurando filtros...');
    await createFilters();
    
    // 5. Criar propostas de exemplo
    console.log('\n5️⃣ Criando propostas...');
    await createProposals();
    
    // 6. Criar configuração do sistema
    console.log('\n6️⃣ Configurando sistema...');
    await createSystemConfig();
    
    // 7. Criar influenciador de exemplo
    console.log('\n7️⃣ Criando dados de exemplo...');
    const exampleId = await createExampleInfluencer();
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 Configuração concluída com sucesso!');
    console.log('\n📊 Resumo:');
    console.log(`   • ${basicCategories.length} categorias criadas`);
    console.log(`   • ${basicBrands.length} marcas criadas`);
    console.log(`   • ${basicFilters.length} filtros criados`);
    console.log(`   • ${exampleProposals.length} propostas de exemplo criadas`);
    console.log(`   • 1 influenciador de exemplo (ID: ${exampleId})`);
    console.log(`   • Configuração do sistema criada`);
    console.log('\n✅ O sistema está pronto para uso!');
    
    return {
      success: true,
      exampleInfluencerId: exampleId,
      categoriesCount: basicCategories.length,
      brandsCount: basicBrands.length,
      filtersCount: basicFilters.length,
      proposalsCount: exampleProposals.length
    };
    
  } catch (error) {
    console.error('\n❌ Erro durante a configuração:', error);
    throw error;
  }
}

/**
 * Função para executar apenas a criação de um influenciador de teste
 */
export async function createTestInfluencer(data?: any) {
  console.log('👤 Criando influenciador de teste...');
  
  const testData = data || exampleInfluencer;
  
  try {
    const influencerId = await addStructuredInfluencer(testData);
    console.log(`✅ Influenciador de teste criado com ID: ${influencerId}`);
    return influencerId;
  } catch (error) {
    console.error('❌ Erro ao criar influenciador de teste:', error);
    throw error;
  }
}

// Exportar dados para uso em outros scripts
export {
  basicCategories,
  basicBrands,
  basicFilters,
  exampleInfluencer,
  exampleProposals
};

/**
 * Função específica para criar apenas propostas de exemplo
 */
export async function createExampleProposals() {
  console.log('📋 Criando apenas propostas de exemplo...');
  
  try {
    await createProposals();
    console.log('✅ Propostas de exemplo criadas com sucesso!');
    return {
      success: true,
      proposalsCount: exampleProposals.length
    };
  } catch (error) {
    console.error('❌ Erro ao criar propostas de exemplo:', error);
    throw error;
  }
}

