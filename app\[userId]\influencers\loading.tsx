/**
 * ⚡ Ultra-Fast Loading UI para página de Influenciadores
 *
 * OTIMIZADO PARA PERFORMANCE:
 * - Zero JavaScript no cliente (Server Component)
 * - Tailwind CSS puro para animações
 * - HTML mínimo para fastest First Paint
 * - Skeleton ultra-leve que espelha a estrutura final
 *
 * MÉTRICAS ALVO:
 * - First Paint: <100ms
 * - LCP: <200ms
 * - Bundle size: 0KB (Server Component)
 *
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/loading
 */

// 🔥 ZERO IMPORTS: Componente Server puro para máxima performance
export default function Loading() {
  return (
    <div className="flex h-screen bg-background">
      {/* 🎨 CSS GLOBAL: Animações definidas no globals.css */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
          .skeleton-shimmer {
            background: linear-gradient(90deg,
              hsl(var(--muted)) 25%,
              hsl(var(--muted-foreground) / 0.1) 50%,
              hsl(var(--muted)) 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
          }
          @media (prefers-color-scheme: dark) {
            .skeleton-shimmer {
              background: linear-gradient(90deg,
                #1a1625 25%,
                #2a2438 50%,
                #1a1625 75%
              );
              background-size: 200% 100%;
            }
          }
        `
      }} />

      {/* 🎯 Sidebar Ultra-Leve */}
      <div className="w-80 border-r border-border bg-card/50 p-4 space-y-4">
        {/* Header */}
        <div className="space-y-2">
          <div className="skeleton-shimmer h-6 w-32 rounded-md" />
          <div className="skeleton-shimmer h-4 w-48 rounded-md" />
        </div>

        <div className="h-px bg-border" />

        {/* Filtros Essenciais */}
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="skeleton-shimmer h-4 w-16 rounded-md" />
            <div className="skeleton-shimmer h-10 w-full rounded-md" />
          </div>

          <div className="space-y-2">
            <div className="skeleton-shimmer h-4 w-20 rounded-md" />
            <div className="skeleton-shimmer h-10 w-full rounded-md" />
          </div>

          <div className="space-y-2">
            <div className="skeleton-shimmer h-4 w-24 rounded-md" />
            <div className="grid grid-cols-2 gap-2">
              <div className="skeleton-shimmer h-10 w-full rounded-md" />
              <div className="skeleton-shimmer h-10 w-full rounded-md" />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="skeleton-shimmer h-4 w-4 rounded" />
              <div className="skeleton-shimmer h-4 w-24 rounded-md" />
            </div>
            <div className="flex items-center space-x-2">
              <div className="skeleton-shimmer h-4 w-4 rounded" />
              <div className="skeleton-shimmer h-4 w-20 rounded-md" />
            </div>
          </div>
        </div>
      </div>

      {/* 🎯 Área Principal Ultra-Leve */}
      <div className="flex-1 flex">
        {/* Grid de Influenciadores */}
        <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto">
          <div className="p-4 space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="skeleton-shimmer h-6 w-40 rounded-md" />
                <div className="skeleton-shimmer h-4 w-64 rounded-md" />
              </div>
              <div className="flex items-center space-x-2">
                <div className="skeleton-shimmer h-9 w-24 rounded-md" />
                <div className="skeleton-shimmer h-9 w-9 rounded-md" />
              </div>
            </div>

            <div className="h-px bg-border" />

            {/* Grid Otimizado - 8 cards estáticos para performance */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* Card 1 */}
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-3/4 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-1/2 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-16 rounded-md" />
                  </div>
                </div>
                <div className="flex gap-1 mt-3">
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                </div>
              </div>

              {/* Card 2 */}
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-2/3 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-1/3 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-20 rounded-md" />
                  </div>
                </div>
                <div className="flex gap-1 mt-3">
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                </div>
              </div>

              {/* Card 3 */}
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-4/5 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-2/5 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-14 rounded-md" />
                  </div>
                </div>
                <div className="flex gap-1 mt-3">
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                </div>
              </div>

              {/* Card 4 */}
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-3/5 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-2/3 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-18 rounded-md" />
                  </div>
                </div>
                <div className="flex gap-1 mt-3">
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                  <div className="skeleton-shimmer h-5 w-8 rounded-full" />
                </div>
              </div>

              {/* Cards 5-8 (versões menores para economizar DOM) */}
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-5/6 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-1/4 rounded-md" />
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-2/3 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-1/2 rounded-md" />
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-4/5 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-1/3 rounded-md" />
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="skeleton-shimmer w-12 h-12 rounded-full flex-shrink-0" />
                  <div className="flex-1 space-y-2">
                    <div className="skeleton-shimmer h-4 w-3/4 rounded-md" />
                    <div className="skeleton-shimmer h-3 w-2/5 rounded-md" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 🎯 Painel Lateral Minimalista */}
        <div className="w-96 border-l border-border bg-card/50 p-4 space-y-4">
          <div className="flex items-center space-x-3">
            <div className="skeleton-shimmer w-16 h-16 rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="skeleton-shimmer h-5 w-32 rounded-md" />
              <div className="skeleton-shimmer h-4 w-24 rounded-md" />
            </div>
          </div>

          <div className="h-px bg-border" />

          <div className="flex space-x-1 bg-muted/50 p-1 rounded-lg">
            <div className="skeleton-shimmer h-8 w-20 rounded-md" />
            <div className="skeleton-shimmer h-8 w-20 rounded-md" />
            <div className="skeleton-shimmer h-8 w-20 rounded-md" />
            <div className="skeleton-shimmer h-8 w-20 rounded-md" />
          </div>

          <div className="space-y-4">
            <div className="bg-card border border-border rounded-lg p-4 space-y-3">
              <div className="skeleton-shimmer h-5 w-24 rounded-md" />
              <div className="space-y-2">
                <div className="flex justify-between">
                  <div className="skeleton-shimmer h-4 w-20 rounded-md" />
                  <div className="skeleton-shimmer h-4 w-16 rounded-md" />
                </div>
                <div className="flex justify-between">
                  <div className="skeleton-shimmer h-4 w-24 rounded-md" />
                  <div className="skeleton-shimmer h-4 w-12 rounded-md" />
                </div>
              </div>
            </div>

            <div className="bg-card border border-border rounded-lg p-4">
              <div className="skeleton-shimmer h-5 w-28 rounded-md mb-4" />
              <div className="skeleton-shimmer h-48 w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
  