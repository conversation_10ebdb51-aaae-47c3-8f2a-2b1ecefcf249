'use client';

import { useAuth as useClerkAuth, useUser, useClerk, useOrganization } from '@clerk/nextjs';
import { ROLE_PERMISSIONS_MAP, CLERK_ROLES } from '@/lib/clerk-fga-config';

// ✅ FUNÇÃO AUXILIAR: Extrair role do usuário
function getUserRole(user: any): string | null {
  // Tentar obter role do organizationMemberships (mais confiável)
  if (user.organizationMemberships && user.organizationMemberships.length > 0) {
    // Pegar o role da primeira organização (assumindo que o usuário está ativo em uma)
    const membership = user.organizationMemberships[0];
    return membership.role;
  }

  // Fallback: tentar obter do publicMetadata
  if (user.publicMetadata?.role) {
    return user.publicMetadata.role;
  }

  // Fallback: tentar obter do unsafeMetadata (desenvolvimento)
  if (user.unsafeMetadata?.role) {
    return user.unsafeMetadata.role;
  }

  // Fallback final: assumir role baseado no tipo de usuário
  if (user.emailAddresses?.[0]?.emailAddress?.includes('admin')) {
    return CLERK_ROLES.ADMIN;
  }

  // Default: member
  return CLERK_ROLES.MEMBER;
}

// Re-exportar o hook do Clerk Auth para manter compatibilidade
export function useAuth() {
  const { userId, sessionId, getToken, isLoaded, isSignedIn } = useClerkAuth();
  const { user } = useUser();
  const clerk = useClerk();
  const { membership } = useOrganization();
  
  // Extrair role do publicMetadata (não privateMetadata que não está disponível no frontend)
  const userType = user?.publicMetadata?.userType as string;
  
  // Mapear userType para roles do sistema de segurança
  const mapUserTypeToRole = (type: string | undefined): string => {
    switch (type) {
      case 'agency': return 'admin';
      case 'manager': return 'manager';
      case 'influencer': return 'member';
      case 'brand': return 'member';
      case 'viewer': return 'viewer';
      default: return 'anonymous'; // Forçar onboarding se não tem userType
    }
  };
  
  const userRole = mapUserTypeToRole(userType);
  
  // Criar currentUser com estrutura compatível
  const currentUser = user ? {
    ...user,
    id: user.id,
    email: user.primaryEmailAddress?.emailAddress || user.emailAddresses?.[0]?.emailAddress || '',
    role: userRole,
    name: user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`.trim()
      : user.firstName || user.lastName || user.username || ''
  } : null;
  
  return {
    currentUser,
    firebaseUser: user, // Mantém compatibilidade com código existente
    isLoading: !isLoaded,
    isInitialized: isLoaded,
    userId,
    sessionId,
    hasPermission: (permission: string) => {
      // ✅ IMPLEMENTAÇÃO REAL: Verificar permissões baseadas no role do usuário
      if (!user) return false;

      // Obter role do membership atual da organização (mais confiável)
      const userRole = membership?.role || getUserRole(user);

      if (!userRole) return false;

      console.log('🔍 [AUTH] Verificando permissão:', { permission, userRole, user: user.id });

      // Verificar se o role tem a permissão solicitada
      const rolePermissions = ROLE_PERMISSIONS_MAP[userRole as keyof typeof ROLE_PERMISSIONS_MAP] || [];
      const hasPermission = rolePermissions.includes(permission as any);

      console.log('✅ [AUTH] Resultado da verificação:', { hasPermission, rolePermissions });

      return hasPermission;
    },
    logout: async () => {
      await clerk.signOut();
    },
    getToken: async () => {
      return await getToken();
    }
  };
}

// Hook para verificar permissões
export function usePermission(permission: string) {
  const { hasPermission } = useAuth();
  return hasPermission(permission);
}

// Hook para verificar roles
export function useRole(role: string) {
  const { currentUser } = useAuth();
  
  if (!currentUser) return false;
  
  // O currentUser.role já está mapeado corretamente no useAuth
  return currentUser.role === role;
}

// Hook para filtro de marca
export function useBrandFilter() {
  const { currentUser } = useAuth();
  
  // Temporariamente, todos os usuários são admin
  return null; // Admin vê tudo
}

// Hook para proteção de rotas
export function useRouteProtection() {
  const { currentUser, hasPermission } = useAuth();
  
  const requirePermission = (permission: string) => {
    return hasPermission(permission);
  };
  
  const requireRole = (role: string) => {
    return role === 'admin'; // Temporariamente todos são admin
  };
  
  return {
    requirePermission,
    requireRole,
    isAuthenticated: !!currentUser
  };
}

