# Project Structure

## Directory Organization

```
├── app/                    # Next.js App Router pages
│   ├── [locale]/          # Internationalized routes (pt, en, es)
│   ├── api/               # API routes and GraphQL endpoint
│   ├── dashboard/         # Main dashboard pages
│   ├── influencers/       # Influencer management pages
│   ├── campaigns/         # Campaign management
│   ├── proposals/         # Proposal and budget management
│   └── admin/             # Admin-only pages
├── components/            # Reusable React components
│   ├── ui/               # Base UI components (shadcn/ui)
│   ├── auth/             # Authentication components
│   ├── forms/            # Form components
│   └── [feature]/        # Feature-specific components
├── lib/                  # Utility libraries and configurations
│   ├── firebase/         # Firebase client/admin setup
│   ├── graphql/          # GraphQL schemas and resolvers
│   ├── utils/            # Utility functions
│   └── validation/       # Zod schemas for validation
├── types/                # TypeScript type definitions
├── hooks/                # Custom React hooks
├── contexts/             # React Context providers
├── services/             # Business logic and API services
├── scripts/              # Database migration and setup scripts
└── public/               # Static assets
```

## Naming Conventions

### Files & Folders
- **kebab-case** for file names: `influencer-form.tsx`
- **PascalCase** for React components: `InfluencerForm`
- **camelCase** for functions and variables: `getUserData`
- **SCREAMING_SNAKE_CASE** for constants: `MAX_FILE_SIZE`

### Components
- Component files use `.tsx` extension
- Export component as default: `export default InfluencerForm`
- Props interface named `[ComponentName]Props`
- Use `React.forwardRef` for components that need ref forwarding

### Types
- Interface names use PascalCase: `Influencer`, `Campaign`
- Type files organized by domain: `types/influencer.ts`
- Use `interface` for object shapes, `type` for unions/computed types

## Code Organization Patterns

### Component Structure
```typescript
// Imports (external first, then internal)
import React from 'react'
import { Button } from '@/components/ui/button'

// Types
interface ComponentProps {
  // props definition
}

// Component
export default function Component({ ...props }: ComponentProps) {
  // hooks
  // handlers
  // render
}
```

### API Routes
- Located in `app/api/` following App Router conventions
- Use TypeScript for request/response typing
- Implement proper error handling and status codes
- Include authentication checks where needed

### Database Collections
- **influencers** - Main influencer profiles
- **brands** - Brand/company profiles  
- **campaigns** - Marketing campaigns
- **proposals** - Collaboration proposals
- **budgets** - Pricing and service details
- **categories** - Content categories
- **users** - User profiles and preferences

### Authentication Flow
1. Clerk handles user authentication
2. Middleware manages route protection and localization
3. Firebase rules provide database-level security
4. GraphQL resolvers include user context validation

## Import Conventions
- Use absolute imports with `@/` prefix
- Group imports: external libraries, then internal modules
- Avoid deep relative imports (`../../../`)
- Import types separately when needed: `import type { User } from '@/types'`