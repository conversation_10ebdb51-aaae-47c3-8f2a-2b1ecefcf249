"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { User, MapPin, Layers, DollarSign, Check, Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PersonalInfoSection } from '@/components/influencer-form/sections/personal-info-section'
import { LocationContactSection } from '@/components/influencer-form/sections/location-contact-section'
import { CategoriesBusinessSection } from '@/components/influencer-form/sections/categories-business-section'
import { SocialPlatformsSection } from '@/components/influencer-form/sections/social-platforms-section'
import { BrandsAssociationSection } from '@/components/influencer-form/sections/brands-association-section'
import { MetricsPreviewSection } from '@/components/influencer-form/sections/metrics-preview-section'
import { PricingPartnershipsSection } from '@/components/influencer-form/sections/pricing-partnerships-section'
import { InfluencerFormData } from '@/types/influencer-form'
import { useInfluencerForm } from '@/hooks/use-influencer-form'
import { useInfluencerMutations, useBrandInfluencerMutations } from '@/hooks/use-graphql-influencers'
import { useAuth } from '@/hooks/use-auth-v2'
import { useApolloClient, gql } from '@apollo/client'
import { cn } from '@/lib/utils'
import { FormProvider } from 'react-hook-form'
import { X, Smartphone, Tag, BarChart3, Save, Building2 } from 'lucide-react'
import { ScrollArea } from '@/components/ui/scroll-area'

interface AddInfluencerFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit?: (data: any) => Promise<void>
  initialData?: any
  mode?: 'create' | 'edit'
}

export function AddInfluencerForm({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode
}: AddInfluencerFormProps) {
  const { toast } = useToast()
  const { currentUser } = useAuth()
  const apolloClient = useApolloClient()
  const [activeTab, setActiveTab] = useState('personal')
  
  // Hook do GraphQL para mutations
  const { 
    createInfluencer: createInfluencerMutation, 
    updateInfluencer: updateInfluencerMutation,
    createInfluencerPricing,
    createAudienceDemographic
  } = useInfluencerMutations()

  // Hook para mutations de associações marca-influenciador via GraphQL
  const { createBrandInfluencerAssociation } = useBrandInfluencerMutations()
  
  // Determinar modo real (edit vs create)
  const actualMode = mode || (initialData ? 'edit' : 'create')
  
  // Função customizada para GraphQL
  const handleGraphQLSubmit = async (formData: InfluencerFormData) => {
    try {
      console.log('🔥 [FORM SUBMIT] === INICIO ===')
      console.log('🔥 [FORM SUBMIT] FormData recebido:', JSON.stringify(formData, null, 2))
      console.log('🔥 [FORM SUBMIT] mainPlatform no formData:', formData.mainPlatform)
      console.log('🔥 [FORM SUBMIT] Plataformas ativas:', formData.platforms ? Object.keys(formData.platforms) : 'nenhuma')
      
      // 💰 [DEBUG] Verificar especificamente os dados de pricing
      console.log('💰 [GraphQL] Verificando dados de pricing no formulário:')
      if (formData.platforms) {
        Object.keys(formData.platforms).forEach(platform => {
          const platformData = (formData.platforms as any)[platform]
          if (platformData?.pricing) {
            console.log(`💰 [GraphQL] ${platform} pricing:`, platformData.pricing)
          } else {
            console.log(`💰 [GraphQL] ${platform} sem dados de pricing`)
          }
        })
      } else {
        console.log('💰 [GraphQL] Nenhuma plataforma encontrada')
      }
      
      // 🔥 CORREÇÃO: Usar convertToCompleteStructure para mapear TODOS os campos
      const { influencerData, financialData } = convertToCompleteStructure(formData)
      
      console.log('📊 [GraphQL] Dados do influenciador convertidos:', influencerData)
      console.log('💰 [GraphQL] Dados financeiros convertidos:', financialData)
      console.log('💰 [GraphQL] Preços extraídos nos dados financeiros:', financialData.prices)
      
      // Mapear para o formato GraphQL CreateInfluencerInput (APENAS campos aceitos pelo schema)
      const graphqlInput: any = {
        name: influencerData.name || "Nome não informado",
        email: influencerData.email || "",
        phone: influencerData.phone || "",
        whatsapp: influencerData.whatsapp || "",
        country: influencerData.country || "Brasil",
        state: influencerData.state || "",
        city: influencerData.city || "",
        location: influencerData.location || "",
        age: influencerData.age || undefined,
        gender: influencerData.gender || "not_specified",
        bio: influencerData.bio || "",
        avatar: influencerData.avatar || "",
        category: influencerData.category || "Lifestyle",
        categories: influencerData.categories || ["Lifestyle"],
        totalFollowers: influencerData.totalFollowers || 0,
        engagementRate: influencerData.engagementRate || 0,
        isVerified: influencerData.isVerified || false,
        isAvailable: influencerData.isAvailable || true,
        status: influencerData.status || "active",
        promotesTraders: influencerData.promotesTraders || false,
        responsibleName: influencerData.responsibleName || "",
        agencyName: influencerData.agencyName || "",
        mainNetwork: influencerData.mainNetwork || "",
        
        // Campos diretos das plataformas
        instagramUsername: influencerData.instagramUsername,
        instagramFollowers: influencerData.instagramFollowers,
        instagramEngagementRate: influencerData.instagramEngagementRate,
        instagramAvgViews: influencerData.instagramAvgViews,
        instagramStoriesViews: influencerData.instagramStoriesViews,
        instagramReelsViews: influencerData.instagramReelsViews,
        
        tiktokUsername: influencerData.tiktokUsername,
        tiktokFollowers: influencerData.tiktokFollowers,
        tiktokEngagementRate: influencerData.tiktokEngagementRate,
        tiktokAvgViews: influencerData.tiktokAvgViews,
        tiktokVideoViews: influencerData.tiktokVideoViews,
        
        youtubeUsername: influencerData.youtubeUsername,
        youtubeFollowers: influencerData.youtubeFollowers,
        youtubeSubscribers: influencerData.youtubeSubscribers,
        youtubeEngagementRate: influencerData.youtubeEngagementRate,
        youtubeAvgViews: influencerData.youtubeAvgViews,
        youtubeShortsViews: influencerData.youtubeShortsViews,
        youtubeLongFormViews: influencerData.youtubeLongFormViews,
        
        facebookUsername: influencerData.facebookUsername,
        facebookFollowers: influencerData.facebookFollowers,
        facebookEngagementRate: influencerData.facebookEngagementRate,
        facebookAvgViews: influencerData.facebookAvgViews,
        
        twitchUsername: influencerData.twitchUsername,
        twitchFollowers: influencerData.twitchFollowers,
        twitchEngagementRate: influencerData.twitchEngagementRate,
        twitchAvgViews: influencerData.twitchAvgViews,
        
        kwaiUsername: influencerData.kwaiUsername,
        kwaiFollowers: influencerData.kwaiFollowers,
        kwaiEngagementRate: influencerData.kwaiEngagementRate,
        kwaiAvgViews: influencerData.kwaiAvgViews,
        
        // 💰 Dados extraídos mas não enviados via GraphQL (processados separadamente)
        // _pricingData: financialData.prices,
        // _demographicsData: formData.platforms
      }
      
      console.log('📡 [GraphQL] Input preparado com TODOS os campos:', graphqlInput)
      
      let result: any
      
      if (actualMode === 'create') {
        // 💰 [DEBUG] Verificar se está enviando os dados completos incluindo pricing
        console.log('💰 [GraphQL] Criando influenciador com dados completos incluindo preços')
        console.log('💰 [GraphQL] financialData completo sendo enviado:', financialData)
        
        // Criar novo influenciador
        result = await createInfluencerMutation(graphqlInput)
        console.log('✅ [GraphQL] Influenciador criado:', result)
        
        // 💰 Criar pricing separadamente se houver dados válidos
        if (financialData.prices && Object.keys(financialData.prices).length > 0) {
          console.log('💰 [GraphQL] Verificando pricing para criação:', financialData.prices)
          
          try {
            // Converter pricing para formato do Firebase (apenas preços válidos > 0)
            const pricingServices: any = {}
            
            // Função para validar preço
            const isValidPrice = (price: any) => {
              return price && Number(price) > 0
            }
            
            if (financialData.prices.instagramStory && isValidPrice(financialData.prices.instagramStory.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.story = { price: financialData.prices.instagramStory.price, currency: 'BRL' }
              console.log('💰 [GraphQL] Instagram Story pricing válido:', financialData.prices.instagramStory.price)
            }
            if (financialData.prices.instagramReel && isValidPrice(financialData.prices.instagramReel.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.reel = { price: financialData.prices.instagramReel.price, currency: 'BRL' }
              console.log('💰 [GraphQL] Instagram Reel pricing válido:', financialData.prices.instagramReel.price)
            }
            if (financialData.prices.tiktokVideo && isValidPrice(financialData.prices.tiktokVideo.price)) {
              pricingServices.tiktok = pricingServices.tiktok || {}
              pricingServices.tiktok.video = { price: financialData.prices.tiktokVideo.price, currency: 'BRL' }
              console.log('💰 [GraphQL] TikTok Video pricing válido:', financialData.prices.tiktokVideo.price)
            }
            if (financialData.prices.youtubeInsertion && isValidPrice(financialData.prices.youtubeInsertion.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.insertion = { price: financialData.prices.youtubeInsertion.price, currency: 'BRL' }
              console.log('💰 [GraphQL] YouTube Insertion pricing válido:', financialData.prices.youtubeInsertion.price)
            }
            if (financialData.prices.youtubeDedicated && isValidPrice(financialData.prices.youtubeDedicated.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.dedicated = { price: financialData.prices.youtubeDedicated.price, currency: 'BRL' }
              console.log('💰 [GraphQL] YouTube Dedicated pricing válido:', financialData.prices.youtubeDedicated.price)
            }
            if (financialData.prices.youtubeShorts && isValidPrice(financialData.prices.youtubeShorts.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.shorts = { price: financialData.prices.youtubeShorts.price, currency: 'BRL' }
              console.log('💰 [GraphQL] YouTube Shorts pricing válido:', financialData.prices.youtubeShorts.price)
            }
            
            // Só criar pricing se houver pelo menos um preço válido
            if (Object.keys(pricingServices).length > 0) {
              console.log('💰 [GraphQL] Criando pricing com serviços válidos:', pricingServices)
              
              // Criar pricing via mutation
              const pricingInput = {
                influencerId: result.id,
                services: pricingServices,
                validFrom: new Date().toISOString(),
                notes: 'Preços criados automaticamente pelo formulário'
              }
              
              const pricingResult = await createInfluencerPricing(pricingInput)
              console.log('💰 [GraphQL] Pricing criado com sucesso via mutation:', pricingResult)
            } else {
              console.log('💰 [GraphQL] Nenhum pricing válido encontrado - pulando criação')
            }
            
          } catch (pricingError) {
            console.error('❌ [GraphQL] Erro ao criar pricing:', pricingError)
            // Não falhar a criação do influenciador se pricing falhar
          }
        } else {
          console.log('💰 [GraphQL] Nenhum pricing para criar')
        }
        
        // 📊 Criar demographics separadamente se houver dados válidos
        if (formData.platforms) {
          console.log('📊 [GraphQL] Criando demographics separadamente')
          
          try {
            const platforms = formData.platforms as any
            
            // Função para validar se os dados de audiência são válidos (qualquer campo preenchido)
            const isValidAudienceData = (platformData: any) => {
              if (!platformData) return false
              
              // Verificar se há dados de gênero
              if (platformData.audienceGender) {
                const total = (platformData.audienceGender.male || 0) + (platformData.audienceGender.female || 0) + (platformData.audienceGender.other || 0)
                if (total > 0) return true
              }
              
              // Verificar se há localizações
              if (platformData.audienceLocations && Array.isArray(platformData.audienceLocations) && platformData.audienceLocations.length > 0) {
                const hasValidLocation = platformData.audienceLocations.some((item: any) => 
                  item.location && item.location.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidLocation) return true
              }
              
              // Verificar se há cidades
              if (platformData.audienceCities && Array.isArray(platformData.audienceCities) && platformData.audienceCities.length > 0) {
                const hasValidCity = platformData.audienceCities.some((item: any) => 
                  item.location && item.location.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidCity) return true
              }
              
              // Verificar se há faixas etárias
              if (platformData.audienceAgeRange && Array.isArray(platformData.audienceAgeRange) && platformData.audienceAgeRange.length > 0) {
                const hasValidAgeRange = platformData.audienceAgeRange.some((item: any) => 
                  item.range && item.range.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidAgeRange) return true
              }
              
              return false
            }
            
            // Instagram demographics - só criar se há dados válidos
            if (platforms.instagram && isValidAudienceData(platforms.instagram)) {
              console.log('📊 [GraphQL] Criando demographics do Instagram com dados válidos:', platforms.instagram)
              
              const instagramDemographicInput = {
                influencerId: result.id,
                platform: 'instagram',
                audienceGender: platforms.instagram.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.instagram.audienceLocations || [],
                audienceCities: platforms.instagram.audienceCities || [],
                audienceAgeRange: platforms.instagram.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const instagramDemographicResult = await createAudienceDemographic(instagramDemographicInput)
              console.log('📊 [GraphQL] Instagram demographics criado:', instagramDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Instagram demographics - dados inválidos ou zerados')
            }
            
            // TikTok demographics - só criar se há dados válidos
            if (platforms.tiktok && isValidAudienceData(platforms.tiktok)) {
              console.log('📊 [GraphQL] Criando demographics do TikTok com dados válidos:', platforms.tiktok)
              
              const tiktokDemographicInput = {
                influencerId: result.id,
                platform: 'tiktok',
                audienceGender: platforms.tiktok.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.tiktok.audienceLocations || [],
                audienceCities: platforms.tiktok.audienceCities || [],
                audienceAgeRange: platforms.tiktok.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const tiktokDemographicResult = await createAudienceDemographic(tiktokDemographicInput)
              console.log('📊 [GraphQL] TikTok demographics criado:', tiktokDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando TikTok demographics - dados inválidos ou zerados')
            }
            
            // YouTube demographics - só criar se há dados válidos
            if (platforms.youtube && isValidAudienceData(platforms.youtube)) {
              console.log('📊 [GraphQL] Criando demographics do YouTube com dados válidos:', platforms.youtube)
              
              const youtubeDemographicInput = {
                influencerId: result.id,
                platform: 'youtube',
                audienceGender: platforms.youtube.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.youtube.audienceLocations || [],
                audienceCities: platforms.youtube.audienceCities || [],
                audienceAgeRange: platforms.youtube.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const youtubeDemographicResult = await createAudienceDemographic(youtubeDemographicInput)
              console.log('📊 [GraphQL] YouTube demographics criado:', youtubeDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando YouTube demographics - dados inválidos ou zerados')
            }
            
            // Facebook demographics - só criar se há dados válidos
            if (platforms.facebook && isValidAudienceData(platforms.facebook)) {
              console.log('📊 [GraphQL] Criando demographics do Facebook com dados válidos:', platforms.facebook)
              
              const facebookDemographicInput = {
                influencerId: result.id,
                platform: 'facebook',
                audienceGender: platforms.facebook.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.facebook.audienceLocations || [],
                audienceCities: platforms.facebook.audienceCities || [],
                audienceAgeRange: platforms.facebook.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const facebookDemographicResult = await createAudienceDemographic(facebookDemographicInput)
              console.log('📊 [GraphQL] Facebook demographics criado:', facebookDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Facebook demographics - dados inválidos ou zerados')
            }
            
            // Twitch demographics - só criar se há dados válidos
            if (platforms.twitch && isValidAudienceData(platforms.twitch)) {
              console.log('📊 [GraphQL] Criando demographics do Twitch com dados válidos:', platforms.twitch)
              
              const twitchDemographicInput = {
                influencerId: result.id,
                platform: 'twitch',
                audienceGender: platforms.twitch.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.twitch.audienceLocations || [],
                audienceCities: platforms.twitch.audienceCities || [],
                audienceAgeRange: platforms.twitch.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const twitchDemographicResult = await createAudienceDemographic(twitchDemographicInput)
              console.log('📊 [GraphQL] Twitch demographics criado:', twitchDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Twitch demographics - dados inválidos ou zerados')
            }
            
            // Kwai demographics - só criar se há dados válidos
            if (platforms.kwai && isValidAudienceData(platforms.kwai)) {
              console.log('📊 [GraphQL] Criando demographics do Kwai com dados válidos:', platforms.kwai)
              
              const kwaiDemographicInput = {
                influencerId: result.id,
                platform: 'kwai',
                audienceGender: platforms.kwai.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.kwai.audienceLocations || [],
                audienceCities: platforms.kwai.audienceCities || [],
                audienceAgeRange: platforms.kwai.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const kwaiDemographicResult = await createAudienceDemographic(kwaiDemographicInput)
              console.log('📊 [GraphQL] Kwai demographics criado:', kwaiDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Kwai demographics - dados inválidos ou zerados')
            }
            
          } catch (demoError) {
            console.error('❌ [GraphQL] Erro ao criar demographics:', demoError)
            // Não falhar a criação do influenciador se demographics falhar
          }
        }
        
        // 🤝 Criar associações marca-influenciador via GraphQL se houver marcas selecionadas
        if (formData.brands && formData.brands.length > 0) {
          console.log('🤝 [BRAND_ASSOCIATION_GRAPHQL] Criando associações via GraphQL:', formData.brands)
          
          try {
            // Verificar se o usuário está autenticado
            if (!currentUser) {
              console.error('❌ [BRAND_ASSOCIATION_GRAPHQL] Usuário não autenticado')
              throw new Error('Usuário não autenticado')
            }

            console.log('✅ [BRAND_ASSOCIATION_GRAPHQL] Processando associações via GraphQL')

            // Primeiro, verificar quais associações já existem
            console.log('🔍 [BRAND_ASSOCIATION_GRAPHQL] Verificando associações existentes...')
            const { data: existingAssociationsData } = await apolloClient.query({
              query: gql`
                query GetBrandInfluencerAssociations($userId: ID!, $influencerId: ID) {
                  brandInfluencerAssociations(userId: $userId, influencerId: $influencerId) {
                    id
                    brandId
                    influencerId
                    status
                  }
                }
              `,
              variables: { 
                userId: currentUser.id,
                influencerId: result.id
              },
              fetchPolicy: 'network-only' // Garantir dados frescos
            })

            const existingBrandIds = (existingAssociationsData.brandInfluencerAssociations || []).map((assoc: any) => assoc.brandId)
            console.log('🔍 [BRAND_ASSOCIATION_GRAPHQL] Associações existentes encontradas:', existingBrandIds)

            // Filtrar apenas marcas que ainda não têm associação
            const brandsToAssociate = formData.brands.filter((brandId: string) => !existingBrandIds.includes(brandId))
            console.log('🤝 [BRAND_ASSOCIATION_GRAPHQL] Marcas a serem associadas:', brandsToAssociate)

            if (brandsToAssociate.length === 0) {
              console.log('ℹ️ [BRAND_ASSOCIATION_GRAPHQL] Todas as marcas já estão associadas')
              toast({
                title: "Marcas já associadas",
                description: "Todas as marcas selecionadas já estão associadas a este influenciador.",
                duration: 5000
              })
              return
            }

            // Preparar dados para criar as associações via GraphQL
            const associationPromises = brandsToAssociate.map(async (brandId: string) => {
              const associationInput = {
                userId: currentUser.id,
                brandId: brandId,
                influencerId: result.id,
                status: 'active',
                tags: []
              }
              
              console.log('🤝 [BRAND_ASSOCIATION_GRAPHQL] Criando associação via GraphQL:', associationInput)
              
              try {
                // Usar Apollo Client para criar a associação
                const { data } = await apolloClient.mutate({
                  mutation: gql`
                    mutation CreateBrandInfluencerAssociation($input: CreateBrandInfluencerAssociationInput!) {
                      createBrandInfluencerAssociation(input: $input) {
                        id
                        brandId
                        brandName
                        influencerId
                        influencerName
                        status
                      }
                    }
                  `,
                  variables: { input: associationInput }
                })
                
                console.log('✅ [BRAND_ASSOCIATION_GRAPHQL] Associação criada via GraphQL:', data.createBrandInfluencerAssociation)
                return data.createBrandInfluencerAssociation
              } catch (mutationError: any) {
                console.error('❌ [BRAND_ASSOCIATION_GRAPHQL] Erro na mutation para brandId:', brandId, mutationError)
                
                // Verificar se é erro de duplicação ou se a função já tratou a duplicação
                if (mutationError.message && (
                  mutationError.message.includes('já existe') ||
                  mutationError.message.includes('already exists') ||
                  mutationError.message.includes('duplicate')
                )) {
                  console.log('ℹ️ [BRAND_ASSOCIATION_GRAPHQL] Associação duplicada detectada - continuando')
                  return { __isDuplicate: true, brandId } // Marcar como duplicata
                }
                
                throw mutationError // Re-lançar outros erros
              }
            })
            
            const associationResults = await Promise.allSettled(associationPromises)
            
            const successfulAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && 
              result.value !== null && 
              result.value !== undefined &&
              !result.value.__isDuplicate &&
              !result.value.__isExisting
            ).length
            
            const failedAssociations = associationResults.filter(result => 
              result.status === 'rejected'
            ).length
            
            const duplicateAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && (
                result.value === null ||
                result.value === undefined ||
                result.value?.__isDuplicate ||
                result.value?.__isExisting
              )
            ).length
            
            console.log('🤝 [BRAND_ASSOCIATION_GRAPHQL] Resultados:', { 
              successful: successfulAssociations, 
              failed: failedAssociations,
              duplicates: duplicateAssociations
            })
            
            if (successfulAssociations > 0) {
              toast({
                title: "Associações criadas",
                description: `${successfulAssociations} associação(ões) com marcas criadas com sucesso.`,
                duration: 5000
              })
            }
            
            if (duplicateAssociations > 0) {
              toast({
                title: "Associações já existentes",
                description: `${duplicateAssociations} associação(ões) já existiam e foram ignoradas.`,
                duration: 5000
              })
            }
            
            if (failedAssociations > 0) {
              toast({
                title: "Algumas associações falharam",
                description: `${failedAssociations} associação(ões) não puderam ser criadas devido a erros.`,
                variant: "destructive",
                duration: 7000
              })
            }
            
          } catch (error) {
            console.error('❌ [BRAND_ASSOCIATION_GRAPHQL] Erro ao criar associações via GraphQL:', error)
            toast({
              title: "Erro nas associações",
              description: "Houve um problema ao verificar ou criar as associações com marcas.",
              variant: "destructive",
              duration: 7000
            })
          }
        }
        
        // Fechar o formulário após criar
        onOpenChange(false)
        
      } else if (actualMode === 'edit' && initialData?.id) {
        // 🔥 CORREÇÃO: Atualizar com TODOS os campos disponíveis no UpdateInfluencerInput
        const updateInput: any = {
          // Campos básicos
          name: graphqlInput.name,
          email: graphqlInput.email,
          phone: graphqlInput.phone,
          whatsapp: graphqlInput.whatsapp,
          
          // Localização
          country: graphqlInput.country,
          state: graphqlInput.state,     
          city: graphqlInput.city,       
          location: graphqlInput.location,
          
          // Dados demográficos
          age: graphqlInput.age,         
          gender: graphqlInput.gender,   
          bio: graphqlInput.bio,
          
          // Visual
          avatar: graphqlInput.avatar,
          
          // Categorização
          category: graphqlInput.category,
          categories: graphqlInput.categories,
          
          // Métricas
          totalFollowers: graphqlInput.totalFollowers,
          engagementRate: graphqlInput.engagementRate,
          
          // Status e verificação
          isVerified: graphqlInput.isVerified,
          isAvailable: graphqlInput.isAvailable,
          status: graphqlInput.status,
          
          // Dados profissionais
          promotesTraders: graphqlInput.promotesTraders,
          responsibleName: graphqlInput.responsibleName,
          agencyName: graphqlInput.agencyName,
          
          // Rede social principal
          mainNetwork: graphqlInput.mainNetwork,
          mainPlatform: graphqlInput.mainPlatform,
          
          // Campos diretos das plataformas (nova estrutura)
          instagramUsername: graphqlInput.instagramUsername,
          instagramFollowers: graphqlInput.instagramFollowers,
          instagramEngagementRate: graphqlInput.instagramEngagementRate,
          instagramAvgViews: graphqlInput.instagramAvgViews,
          instagramStoriesViews: graphqlInput.instagramStoriesViews,
          instagramReelsViews: graphqlInput.instagramReelsViews,
          
          tiktokUsername: graphqlInput.tiktokUsername,
          tiktokFollowers: graphqlInput.tiktokFollowers,
          tiktokEngagementRate: graphqlInput.tiktokEngagementRate,
          tiktokAvgViews: graphqlInput.tiktokAvgViews,
          tiktokVideoViews: graphqlInput.tiktokVideoViews,
          
          youtubeUsername: graphqlInput.youtubeUsername,
          youtubeFollowers: graphqlInput.youtubeFollowers,
          youtubeSubscribers: graphqlInput.youtubeSubscribers,
          youtubeEngagementRate: graphqlInput.youtubeEngagementRate,
          youtubeAvgViews: graphqlInput.youtubeAvgViews,
          youtubeShortsViews: graphqlInput.youtubeShortsViews,
          youtubeLongFormViews: graphqlInput.youtubeLongFormViews,
          
          facebookUsername: graphqlInput.facebookUsername,
          facebookFollowers: graphqlInput.facebookFollowers,
          facebookEngagementRate: graphqlInput.facebookEngagementRate,
          facebookAvgViews: graphqlInput.facebookAvgViews,
          
          twitchUsername: graphqlInput.twitchUsername,
          twitchFollowers: graphqlInput.twitchFollowers,
          twitchEngagementRate: graphqlInput.twitchEngagementRate,
          twitchAvgViews: graphqlInput.twitchAvgViews,
          
          kwaiUsername: graphqlInput.kwaiUsername,
          kwaiFollowers: graphqlInput.kwaiFollowers,
          kwaiEngagementRate: graphqlInput.kwaiEngagementRate,
          kwaiAvgViews: graphqlInput.kwaiAvgViews
        }
        
        console.log('📝 [GraphQL] Input de atualização com TODOS os campos:', updateInput)
        console.log('💰 [GraphQL] Dados financeiros para atualização:', financialData)
        result = await updateInfluencerMutation(String(initialData.id), updateInput)
        console.log('✅ [GraphQL] Influenciador atualizado:', result)
        
        // 💰 ATUALIZAR/CRIAR PRICING SEPARADAMENTE DURANTE EDIÇÃO (APENAS COM DADOS VÁLIDOS)
        if (financialData.prices && Object.keys(financialData.prices).length > 0) {
          console.log('💰 [GraphQL] Verificando pricing para atualização durante edição:', financialData.prices)
          
          try {
            // Converter pricing para formato do Firebase (apenas preços válidos > 0)
            const pricingServices: any = {}
            
            // Função para validar preço
            const isValidPrice = (price: any) => {
              return price && Number(price) > 0
            }
            
            if (financialData.prices.instagramStory && isValidPrice(financialData.prices.instagramStory.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.story = { price: financialData.prices.instagramStory.price, currency: 'BRL' }
              console.log('💰 [GraphQL] Instagram Story pricing válido:', financialData.prices.instagramStory.price)
            }
            if (financialData.prices.instagramReel && isValidPrice(financialData.prices.instagramReel.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.reel = { price: financialData.prices.instagramReel.price, currency: 'BRL' }
              console.log('💰 [GraphQL] Instagram Reel pricing válido:', financialData.prices.instagramReel.price)
            }
            if (financialData.prices.tiktokVideo && isValidPrice(financialData.prices.tiktokVideo.price)) {
              pricingServices.tiktok = pricingServices.tiktok || {}
              pricingServices.tiktok.video = { price: financialData.prices.tiktokVideo.price, currency: 'BRL' }
              console.log('💰 [GraphQL] TikTok Video pricing válido:', financialData.prices.tiktokVideo.price)
            }
            if (financialData.prices.youtubeInsertion && isValidPrice(financialData.prices.youtubeInsertion.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.insertion = { price: financialData.prices.youtubeInsertion.price, currency: 'BRL' }
              console.log('💰 [GraphQL] YouTube Insertion pricing válido:', financialData.prices.youtubeInsertion.price)
            }
            if (financialData.prices.youtubeDedicated && isValidPrice(financialData.prices.youtubeDedicated.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.dedicated = { price: financialData.prices.youtubeDedicated.price, currency: 'BRL' }
              console.log('💰 [GraphQL] YouTube Dedicated pricing válido:', financialData.prices.youtubeDedicated.price)
            }
            if (financialData.prices.youtubeShorts && isValidPrice(financialData.prices.youtubeShorts.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.shorts = { price: financialData.prices.youtubeShorts.price, currency: 'BRL' }
              console.log('💰 [GraphQL] YouTube Shorts pricing válido:', financialData.prices.youtubeShorts.price)
            }
            
            // Só criar pricing se houver pelo menos um preço válido
            if (Object.keys(pricingServices).length > 0) {
              console.log('💰 [GraphQL] Criando pricing com serviços válidos:', pricingServices)
              
              // Criar novo pricing (desativa o anterior automaticamente)
              const pricingInput = {
                influencerId: initialData.id,
                services: pricingServices,
                validFrom: new Date().toISOString(),
                notes: 'Preços atualizados via formulário de edição'
              }
              
              const pricingResult = await createInfluencerPricing(pricingInput)
              console.log('💰 [GraphQL] Pricing atualizado com sucesso via mutation:', pricingResult)
            } else {
              console.log('💰 [GraphQL] Nenhum pricing válido encontrado - pulando atualização')
            }
            
          } catch (pricingError) {
            console.error('❌ [GraphQL] Erro ao atualizar pricing:', pricingError)
            // Não falhar a atualização do influenciador se pricing falhar
          }
        } else {
          console.log('💰 [GraphQL] Nenhum pricing para atualizar')
        }
        
        // 📊 ATUALIZAR/CRIAR DEMOGRAPHICS SEPARADAMENTE DURANTE EDIÇÃO (APENAS COM DADOS VÁLIDOS)
        if (formData.platforms) {
          console.log('📊 [GraphQL] Atualizando demographics separadamente durante edição')
          
          try {
            const platforms = formData.platforms as any
            
            // Função para validar se os dados de audiência são válidos (qualquer campo preenchido)
            const isValidAudienceData = (platformData: any) => {
              if (!platformData) return false
              
              // Verificar se há dados de gênero
              if (platformData.audienceGender) {
                const total = (platformData.audienceGender.male || 0) + (platformData.audienceGender.female || 0) + (platformData.audienceGender.other || 0)
                if (total > 0) return true
              }
              
              // Verificar se há localizações
              if (platformData.audienceLocations && Array.isArray(platformData.audienceLocations) && platformData.audienceLocations.length > 0) {
                const hasValidLocation = platformData.audienceLocations.some((item: any) => 
                  item.location && item.location.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidLocation) return true
              }
              
              // Verificar se há cidades
              if (platformData.audienceCities && Array.isArray(platformData.audienceCities) && platformData.audienceCities.length > 0) {
                const hasValidCity = platformData.audienceCities.some((item: any) => 
                  item.city && item.city.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidCity) return true
              }
              
              // Verificar se há faixas etárias
              if (platformData.audienceAgeRange && Array.isArray(platformData.audienceAgeRange) && platformData.audienceAgeRange.length > 0) {
                const hasValidAgeRange = platformData.audienceAgeRange.some((item: any) => 
                  item.range && item.range.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidAgeRange) return true
              }
              
              return false
            }
            
            // Instagram demographics - só criar se há dados válidos
            if (platforms.instagram && isValidAudienceData(platforms.instagram)) {
              console.log('📊 [GraphQL] Atualizando demographics do Instagram com dados válidos:', platforms.instagram)
              console.log('🎯 [DEBUG] platforms.instagram.audienceAgeRange ANTES de enviar:', platforms.instagram.audienceAgeRange)
              console.log('🎯 [DEBUG] Tipo de audienceAgeRange:', typeof platforms.instagram.audienceAgeRange)
              console.log('🎯 [DEBUG] É array?', Array.isArray(platforms.instagram.audienceAgeRange))
              
              const instagramDemographicInput = {
                influencerId: initialData.id,
                platform: 'instagram',
                audienceGender: platforms.instagram.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.instagram.audienceLocations || [],
                audienceCities: platforms.instagram.audienceCities || [],
                audienceAgeRange: platforms.instagram.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              console.log('🎯 [DEBUG] instagramDemographicInput COMPLETO:', JSON.stringify(instagramDemographicInput, null, 2))
              console.log('🎯 [DEBUG] instagramDemographicInput.audienceAgeRange:', instagramDemographicInput.audienceAgeRange)
              
              const instagramDemographicResult = await createAudienceDemographic(instagramDemographicInput)
              console.log('📊 [GraphQL] Instagram demographics atualizado:', instagramDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Instagram demographics - dados inválidos ou zerados')
            }
            
            // TikTok demographics - só criar se há dados válidos
            if (platforms.tiktok && isValidAudienceData(platforms.tiktok)) {
              console.log('📊 [GraphQL] Atualizando demographics do TikTok com dados válidos:', platforms.tiktok)
              
              const tiktokDemographicInput = {
                influencerId: initialData.id,
                platform: 'tiktok',
                audienceGender: platforms.tiktok.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.tiktok.audienceLocations || [],
                audienceCities: platforms.tiktok.audienceCities || [],
                audienceAgeRange: platforms.tiktok.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const tiktokDemographicResult = await createAudienceDemographic(tiktokDemographicInput)
              console.log('📊 [GraphQL] TikTok demographics atualizado:', tiktokDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando TikTok demographics - dados inválidos ou zerados')
            }
            
            // YouTube demographics - só criar se há dados válidos
            if (platforms.youtube && isValidAudienceData(platforms.youtube)) {
              console.log('📊 [GraphQL] Atualizando demographics do YouTube com dados válidos:', platforms.youtube)
              
              const youtubeDemographicInput = {
                influencerId: initialData.id,
                platform: 'youtube',
                audienceGender: platforms.youtube.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.youtube.audienceLocations || [],
                audienceCities: platforms.youtube.audienceCities || [],
                audienceAgeRange: platforms.youtube.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const youtubeDemographicResult = await createAudienceDemographic(youtubeDemographicInput)
              console.log('📊 [GraphQL] YouTube demographics atualizado:', youtubeDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando YouTube demographics - dados inválidos ou zerados')
            }
            
            // Facebook demographics - só criar se há dados válidos
            if (platforms.facebook && isValidAudienceData(platforms.facebook)) {
              console.log('📊 [GraphQL] Atualizando demographics do Facebook com dados válidos:', platforms.facebook)
              
              const facebookDemographicInput = {
                influencerId: initialData.id,
                platform: 'facebook',
                audienceGender: platforms.facebook.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.facebook.audienceLocations || [],
                audienceCities: platforms.facebook.audienceCities || [],
                audienceAgeRange: platforms.facebook.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const facebookDemographicResult = await createAudienceDemographic(facebookDemographicInput)
              console.log('📊 [GraphQL] Facebook demographics atualizado:', facebookDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Facebook demographics - dados inválidos ou zerados')
            }
            
            // Twitch demographics - só criar se há dados válidos
            if (platforms.twitch && isValidAudienceData(platforms.twitch)) {
              console.log('📊 [GraphQL] Atualizando demographics do Twitch com dados válidos:', platforms.twitch)
              
              const twitchDemographicInput = {
                influencerId: initialData.id,
                platform: 'twitch',
                audienceGender: platforms.twitch.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.twitch.audienceLocations || [],
                audienceCities: platforms.twitch.audienceCities || [],
                audienceAgeRange: platforms.twitch.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const twitchDemographicResult = await createAudienceDemographic(twitchDemographicInput)
              console.log('📊 [GraphQL] Twitch demographics atualizado:', twitchDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Twitch demographics - dados inválidos ou zerados')
            }
            
            // Kwai demographics - só criar se há dados válidos
            if (platforms.kwai && isValidAudienceData(platforms.kwai)) {
              console.log('📊 [GraphQL] Atualizando demographics do Kwai com dados válidos:', platforms.kwai)
              
              const kwaiDemographicInput = {
                influencerId: initialData.id,
                platform: 'kwai',
                audienceGender: platforms.kwai.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.kwai.audienceLocations || [],
                audienceCities: platforms.kwai.audienceCities || [],
                audienceAgeRange: platforms.kwai.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const kwaiDemographicResult = await createAudienceDemographic(kwaiDemographicInput)
              console.log('📊 [GraphQL] Kwai demographics atualizado:', kwaiDemographicResult)
            } else {
              console.log('📊 [GraphQL] Pulando Kwai demographics - dados inválidos ou zerados')
            }
            
          } catch (demoError) {
            console.error('❌ [GraphQL] Erro ao atualizar demographics:', demoError)
            // Não falhar a atualização do influenciador se demographics falhar
          }
        }
        
        // 🤝 Criar associações marca-influenciador se houver marcas selecionadas (MODO EDIÇÃO)
        if (formData.brands && formData.brands.length > 0) {
          console.log('🤝 [BRAND_ASSOCIATION_EDIT] Criando associações durante edição:', formData.brands)
          
          try {
            // Verificar se o usuário está autenticado
            if (!currentUser) {
              console.error('❌ [BRAND_ASSOCIATION_EDIT] Usuário não autenticado')
              throw new Error('Usuário não autenticado')
            }

            console.log('✅ [BRAND_ASSOCIATION_EDIT] Usando GraphQL para criar associações')

            // Preparar dados para criar as associações via GraphQL
            const associationPromises = formData.brands.map(async (brandId) => {
              const associationInput = {
                userId: currentUser.id,
                brandId: brandId,
                influencerId: initialData.id,
                status: 'active',
                tags: [],
                notes: ''
              }
              
              console.log('🤝 [BRAND_ASSOCIATION_EDIT] Criando associação via GraphQL:', associationInput)
              
              try {
                const associationResult = await createBrandInfluencerAssociation(associationInput)
                console.log('✅ [BRAND_ASSOCIATION_EDIT] Associação criada via GraphQL:', associationResult)
                return associationResult
              } catch (mutationError: any) {
                console.error('❌ [BRAND_ASSOCIATION_EDIT] Erro na mutation GraphQL para brandId:', brandId, mutationError)
                
                // Verificar se é erro de duplicação
                if (mutationError.message && (
                  mutationError.message.includes('já existe') ||
                  mutationError.message.includes('already exists') ||
                  mutationError.message.includes('duplicate')
                )) {
                  console.log('ℹ️ [BRAND_ASSOCIATION_EDIT] Associação duplicada detectada - continuando')
                  return { __isDuplicate: true, brandId } // Marcar como duplicata
                }
                
                throw mutationError // Re-lançar outros erros
              }
            })
            
            const associationResults = await Promise.allSettled(associationPromises)
            
            const successfulAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && 
              result.value !== null && 
              result.value !== undefined &&
              !result.value.__isDuplicate &&
              !result.value.__isExisting
            ).length
            
            const failedAssociations = associationResults.filter(result => 
              result.status === 'rejected'
            ).length
            
            const duplicateAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && (
                result.value === null ||
                result.value === undefined ||
                result.value?.__isDuplicate ||
                result.value?.__isExisting
              )
            ).length
            
            console.log('🤝 [BRAND_ASSOCIATION_EDIT] Resultados:', { 
              successful: successfulAssociations, 
              failed: failedAssociations,
              duplicates: duplicateAssociations,
              details: associationResults.map(result => ({
                status: result.status,
                reason: result.status === 'rejected' ? result.reason : 'success'
              }))
            })
            
            if (successfulAssociations > 0) {
              toast({
                title: "Associações criadas",
                description: `${successfulAssociations} associação(ões) com marcas criadas com sucesso.`,
                duration: 5000
              })
            }
            
            if (duplicateAssociations > 0) {
              toast({
                title: "Associações já existentes",
                description: `${duplicateAssociations} associação(ões) já existiam e foram ignoradas.`,
                duration: 5000
              })
            }
            
            if (failedAssociations > 0) {
              const failedDetails = associationResults
                .filter(result => result.status === 'rejected')
                .map(result => (result as any).reason?.message || 'Erro desconhecido')
                .join(', ')
              
              console.error('❌ [BRAND_ASSOCIATION_EDIT] Detalhes dos erros:', failedDetails)
              
              toast({
                title: "Algumas associações falharam",
                description: `${failedAssociations} associação(ões) não puderam ser criadas. Detalhes: ${failedDetails}`,
                variant: "destructive",
                duration: 10000
              })
            }
            
          } catch (error) {
            console.error('❌ [BRAND_ASSOCIATION_EDIT] Erro ao criar associações via GraphQL:', error)
            toast({
              title: "Erro nas associações",
              description: "Houve um problema ao associar as marcas via GraphQL. Tente novamente.",
              variant: "destructive",
              duration: 7000
            })
          }
        } else {
          console.log('🤝 [BRAND_ASSOCIATION_EDIT] Nenhuma marca selecionada para associar')
        }
        
        // Fechar o formulário após salvar
        onOpenChange(false)
      }
      
      return result
      
    } catch (error) {
      console.error('❌ [GraphQL] Erro no submit:', error)
      throw error
    }
  }
  
  // Os dados iniciais são passados diretamente, sem carregamento de marcas associadas
  // O campo 'brands' permanece vazio para novas seleções
  // As marcas já associadas são gerenciadas pelo BrandsAssociationSection via hooks GraphQL
  const processedData = useMemo(() => {
    console.log('[INFLUENCER_FORM] Usando dados iniciais diretos (sem carregar marcas associadas no campo brands)');
    return initialData;
  }, [initialData]);

  // Usar dados processados diretamente

  // Hook do formulário com submit customizado
  const {
    form,
    handleSubmit,
    resetForm,
    isSubmitting,
    errors,
    isValid,
    calculateMetrics,
    activePlatforms,
    addPlatform,
    removePlatform,
    availablePlatforms
  } = useInfluencerForm({
    initialData: processedData,
    onSubmit: handleGraphQLSubmit
  })

  // 🔄 Converter dados do formulário para estrutura completa (influencers + financial)
  const convertToCompleteStructure = (data: InfluencerFormData): { influencerData: any, financialData: any } => {
    console.log('🔄 Convertendo dados para estrutura completa:', data)
    console.log('📋 Tipo de data.platforms:', typeof data.platforms)
    console.log('📋 data.platforms é array?', Array.isArray(data.platforms))
    console.log('📋 Keys de data.platforms:', data.platforms ? Object.keys(data.platforms) : 'undefined')
    console.log('🎯 [DEBUG MAINPLATFORM] data.mainPlatform:', data.mainPlatform)
    console.log('🎯 [DEBUG MAINPLATFORM] tipo:', typeof data.mainPlatform)
    
    let totalFollowers = 0
    let totalEngagement = 0
    let platformCount = 0
    const socialNetworks: any = {}
    
    // Verificar se há dados de plataformas
    if (data.platforms) {
      console.log('📱 Plataformas encontradas:', Object.keys(data.platforms))
      const platforms = data.platforms as any
      
      // Instagram
      if (platforms.instagram) {
        console.log('📸 Processando Instagram:', platforms.instagram)
        console.log('👁️ Dados de views do Instagram:', platforms.instagram.views)
        const instagram = platforms.instagram
        totalFollowers += instagram.followers || 0
        if (instagram.engagementRate) {
          totalEngagement += instagram.engagementRate
          platformCount++
        }
        
        socialNetworks.instagram = {
          username: instagram.username || '',
          followers: instagram.followers || 0,
          avgViews: instagram.avgViews || 0,
          engagementRate: instagram.engagementRate || 0,
          views: instagram.views || {},
          storiesViews: instagram.views?.storiesViews || 0,
          reelsViews: instagram.views?.reelsViews || 0
          // ✅ LIMPEZA: Demographics e pricing são gerenciados separadamente
        }
        console.log('✅ Instagram processado para socialNetworks:', socialNetworks.instagram)
      } else {
        console.log('❌ Instagram não encontrado nos dados')
      }
      
      // TikTok
      if (platforms.tiktok) {
        console.log('🎵 Processando TikTok:', platforms.tiktok)
        console.log('👁️ Dados de views do TikTok:', platforms.tiktok.views)
        const tiktok = platforms.tiktok
        totalFollowers += tiktok.followers || 0
        if (tiktok.engagementRate) {
          totalEngagement += tiktok.engagementRate
          platformCount++
        }
        
        socialNetworks.tiktok = {
          username: tiktok.username || '',
          followers: tiktok.followers || 0,
          avgViews: tiktok.avgViews || 0,
          engagementRate: tiktok.engagementRate || 0,
          views: tiktok.views || {},
          videoViews: tiktok.views?.videoViews || 0
          // ✅ LIMPEZA: Demographics e pricing são gerenciados separadamente
        }
      } else {
        console.log('❌ TikTok não encontrado nos dados')
      }
      
      // YouTube
      if (platforms.youtube) {
        console.log('📺 Processando YouTube:', platforms.youtube)
        console.log('👁️ Dados de views do YouTube:', platforms.youtube.views)
        const youtube = platforms.youtube
        totalFollowers += youtube.followers || 0
        if (youtube.engagementRate) {
          totalEngagement += youtube.engagementRate
          platformCount++
        }
        
        socialNetworks.youtube = {
          username: youtube.username || '',
          followers: youtube.followers || 0,
          subscribers: youtube.followers || 0,
          avgViews: youtube.avgViews || 0,
          engagementRate: youtube.engagementRate || 0,
          views: youtube.views || {},
          shortsViews: youtube.views?.shortsViews || 0,
          longFormViews: youtube.views?.longFormViews || 0
          // ✅ LIMPEZA: Demographics e pricing são gerenciados separadamente
        }
      } else {
        console.log('❌ YouTube não encontrado nos dados')
      }
      
      // Facebook
      if (platforms.facebook) {
        console.log('📘 Processando Facebook:', platforms.facebook)
        socialNetworks.facebook = {
          username: platforms.facebook.username || '',
          followers: platforms.facebook.followers || 0,
          avgViews: platforms.facebook.avgViews || 0,
          engagementRate: platforms.facebook.engagementRate || 0
          // 🔥 PRICING REMOVIDO: Agora gerenciado separadamente
        }
        totalFollowers += platforms.facebook.followers || 0
        if (platforms.facebook.engagementRate) {
          totalEngagement += platforms.facebook.engagementRate
          platformCount++
        }
      } else {
        console.log('❌ Facebook não encontrado nos dados')
      }
      
      // Twitch
      if (platforms.twitch) {
        console.log('💜 Processando Twitch:', platforms.twitch)
        socialNetworks.twitch = {
          username: platforms.twitch.username || '',
          followers: platforms.twitch.followers || 0,
          avgViews: platforms.twitch.avgViews || 0,
          engagementRate: platforms.twitch.engagementRate || 0
          // ✅ LIMPEZA: Pricing é gerenciado separadamente
        }
        totalFollowers += platforms.twitch.followers || 0
        if (platforms.twitch.engagementRate) {
          totalEngagement += platforms.twitch.engagementRate
          platformCount++
        }
      } else {
        console.log('❌ Twitch não encontrado nos dados')
      }
      
      // Kwai
      if (platforms.kwai) {
        console.log('🟠 Processando Kwai:', platforms.kwai)
        socialNetworks.kwai = {
          username: platforms.kwai.username || '',
          followers: platforms.kwai.followers || 0,
          avgViews: platforms.kwai.avgViews || 0,
          engagementRate: platforms.kwai.engagementRate || 0
          // ✅ LIMPEZA: Pricing é gerenciado separadamente
        }
        totalFollowers += platforms.kwai.followers || 0
        if (platforms.kwai.engagementRate) {
          totalEngagement += platforms.kwai.engagementRate
          platformCount++
        }
      } else {
        console.log('❌ Kwai não encontrado nos dados')
      }
      
      console.log('📊 Redes sociais processadas:', socialNetworks)
      console.log('📊 Total de seguidores:', totalFollowers)
      console.log('📊 Plataformas com engajamento:', platformCount)
      console.log('📊 Object.keys(socialNetworks):', Object.keys(socialNetworks))
      console.log('📊 Verificação se socialNetworks está vazio:', Object.keys(socialNetworks).length === 0)
      console.log('👁️ Dados de visualizações incluídos:', {
        instagram: socialNetworks.instagram?.views,
        tiktok: socialNetworks.tiktok?.views,
        youtube: socialNetworks.youtube?.views
      })
    } else {
      console.log('❌ Nenhuma plataforma encontrada em data.platforms')
    }
    
    const overallEngagementRate = platformCount > 0 ? totalEngagement / platformCount : 0
    
    // Mapear gênero para o formato esperado
    const genderMap: { [key: string]: 'male' | 'female' | 'other' | 'not_specified' } = {
      'Masculino': 'male',
      'Feminino': 'female'
    }
    
    // Dados do influenciador (coleção influencers)
    const influencerData = {
      // Dados pessoais
      name: data.personalInfo?.name || '',
      email: data.contact?.email || '',
      phone: data.contact?.whatsapp || '',
      whatsapp: data.contact?.whatsapp || '',
      
      // Localização
      country: data.location?.country || 'Brasil',
      state: data.location?.state || '',
      city: data.location?.city || '',
      location: data.location?.city && data.location?.state 
        ? `${data.location.city}/${data.location.state}` 
        : '',
      
      // Dados demográficos
      age: data.personalInfo?.age || undefined,
      gender: genderMap[data.personalInfo?.gender || ''] || 'not_specified',
      bio: data.personalInfo?.bio || '',
      
      // Visuais
      avatar: data.personalInfo?.avatar || '',
      backgroundImage: '',
      gradient: `linear-gradient(135deg, #ff0074 0%, #5600ce 100%)`,
      
      // Categorização
      category: data.business?.categories?.[0] || 'Lifestyle',
      categories: data.business?.categories || [],
      mainCategories: data.business?.categories || [],
      
      // Métricas e engajamento
      totalFollowers: totalFollowers,
      engagementRate: Math.round(overallEngagementRate * 100) / 100,
      rating: 0,
      
      // Status e verificação
      isVerified: data.personalInfo?.verified || false,
      isAvailable: true,
      status: 'active',
      
      // Distribuição de gênero da audiência (média entre plataformas)
      audienceGender: calculateAverageAudienceGender(data.platforms),
      
      // Redes sociais (campos diretos)
      instagramUsername: socialNetworks.instagram?.username || '',
      instagramFollowers: socialNetworks.instagram?.followers || 0,
      instagramEngagementRate: socialNetworks.instagram?.engagementRate || 0,
      instagramAvgViews: socialNetworks.instagram?.avgViews || 0,
      instagramStoriesViews: socialNetworks.instagram?.storiesViews || 0,
      instagramReelsViews: socialNetworks.instagram?.reelsViews || 0,
      
      tiktokUsername: socialNetworks.tiktok?.username || '',
      tiktokFollowers: socialNetworks.tiktok?.followers || 0,
      tiktokEngagementRate: socialNetworks.tiktok?.engagementRate || 0,
      tiktokAvgViews: socialNetworks.tiktok?.avgViews || 0,
      tiktokVideoViews: socialNetworks.tiktok?.videoViews || 0,
      
      youtubeUsername: socialNetworks.youtube?.username || '',
      youtubeFollowers: socialNetworks.youtube?.followers || 0,
      youtubeSubscribers: socialNetworks.youtube?.subscribers || 0,
      youtubeEngagementRate: socialNetworks.youtube?.engagementRate || 0,
      youtubeAvgViews: socialNetworks.youtube?.avgViews || 0,
      youtubeShortsViews: socialNetworks.youtube?.shortsViews || 0,
      youtubeLongFormViews: socialNetworks.youtube?.longFormViews || 0,
      
      facebookUsername: socialNetworks.facebook?.username || '',
      facebookFollowers: socialNetworks.facebook?.followers || 0,
      facebookEngagementRate: socialNetworks.facebook?.engagementRate || 0,
      facebookAvgViews: socialNetworks.facebook?.avgViews || 0,
      
      twitchUsername: socialNetworks.twitch?.username || '',
      twitchFollowers: socialNetworks.twitch?.followers || 0,
      twitchEngagementRate: socialNetworks.twitch?.engagementRate || 0,
      twitchAvgViews: socialNetworks.twitch?.avgViews || 0,
      
      kwaiUsername: socialNetworks.kwai?.username || '',
      kwaiFollowers: socialNetworks.kwai?.followers || 0,
      kwaiEngagementRate: socialNetworks.kwai?.engagementRate || 0,
      kwaiAvgViews: socialNetworks.kwai?.avgViews || 0,
      
      // Conteúdo e especialização
      contentTypes: data.business?.categories || [],
      contentLanguages: ['pt-BR'],
      specialties: data.business?.categories || [],
      
      // Configurações profissionais
      promotesTraders: data.business?.promotesTraders || false,
      responsibleName: data.business?.responsibleName || '',
      agencyName: data.business?.agencyName || '',
      
      // Plataforma principal
      mainPlatform: data.mainPlatform || '',
      mainNetwork: data.mainPlatform || '',
      
      // Tags e notas (vazios inicialmente)
      tags: [],
      notes: [],
      
      // Metadados (serão preenchidos pelo backend)
      userId: '',
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    // Dados financeiros (coleção influencer_financials)
    const financialData = {
      influencerId: '', // Será preenchido após criar o influenciador
      responsibleName: data.business?.responsibleName || '',
      agencyName: data.business?.agencyName || '',
      email: data.contact?.email || '',
      whatsapp: data.contact?.whatsapp || '',
      
      // Visualizações em stories (pegar do Instagram se disponível)
      instagramStoriesViews: (data.platforms as any)?.instagram?.metrics?.storiesViews || 0,
      
      // Preços
      prices: extractPrices(data.platforms),
      
      // Histórico de marcas
      brandHistory: extractBrandHistory(data.platforms),
      
      // Dados adicionais
      additionalData: {
        contentType: data.business?.categories || [],
        promotesTraders: data.business?.promotesTraders || false,
        responsibleRecruiter: data.business?.responsibleCapturer || '',
        socialMediaScreenshots: extractScreenshots(data.platforms),
        notes: '',
        documents: []
      },
      
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    console.log('🎯 DADOS FINAIS - influencerData.totalFollowers:', influencerData.totalFollowers)
    console.log('🎯 DADOS FINAIS - influencerData.engagementRate:', influencerData.engagementRate)
    console.log('🎯 DADOS FINAIS - influencerData.mainPlatform:', influencerData.mainPlatform)
    console.log('🎯 DADOS FINAIS - influencerData.mainNetwork:', influencerData.mainNetwork)
    
    return { influencerData, financialData }
  }
  
  // Função auxiliar para calcular média de gênero da audiência
  const calculateAverageAudienceGender = (platforms: any) => {
    let totalMale = 0
    let totalFemale = 0
    let totalOther = 0
    let platformCount = 0
    
    if (platforms) {
      Object.values(platforms).forEach((platform: any) => {
        if (platform.audienceGender) {
          totalMale += platform.audienceGender.male || 0
          totalFemale += platform.audienceGender.female || 0
          totalOther += platform.audienceGender.other || 0
          platformCount++
        }
      })
    }
    
    if (platformCount === 0) {
      return { male: 33, female: 33, other: 34 }
    }
    
    return {
      male: Math.round(totalMale / platformCount),
      female: Math.round(totalFemale / platformCount),
      other: Math.round(totalOther / platformCount)
    }
  }
  
  // Função auxiliar para extrair preços
  const extractPrices = (platforms: any) => {
    console.log('💰 [DEBUG] extractPrices - Entrada:', platforms)
    const prices: any = {}
    
    if (platforms?.instagram?.pricing) {
      console.log('💰 [DEBUG] Instagram pricing encontrado:', platforms.instagram.pricing)
      if (platforms.instagram.pricing.story) {
        prices.instagramStory = {
          name: 'Stories Instagram',
          price: platforms.instagram.pricing.story
        }
        console.log('💰 [DEBUG] Story price adicionado:', prices.instagramStory)
      }
      if (platforms.instagram.pricing.reel) {
        prices.instagramReel = {
          name: 'Reels Instagram',
          price: platforms.instagram.pricing.reel
        }
        console.log('💰 [DEBUG] Reel price adicionado:', prices.instagramReel)
      }
      // Ignorar campo 'post' se existir nos dados
      if ('post' in platforms.instagram.pricing) {
        console.log('💰 [DEBUG] Campo post ignorado no Instagram')
      }
    } else {
      console.log('💰 [DEBUG] Instagram pricing não encontrado')
    }
    
    if (platforms?.tiktok?.pricing?.video) {
      prices.tiktokVideo = {
        name: 'Vídeo TikTok',
        price: platforms.tiktok.pricing.video
      }
      console.log('💰 [DEBUG] TikTok video price adicionado:', prices.tiktokVideo)
    } else {
      console.log('💰 [DEBUG] TikTok pricing não encontrado')
    }
    
    if (platforms?.youtube?.pricing) {
      console.log('💰 [DEBUG] YouTube pricing encontrado:', platforms.youtube.pricing)
      if (platforms.youtube.pricing.insertion) {
        prices.youtubeInsertion = {
          name: 'Inserção YouTube',
          price: platforms.youtube.pricing.insertion
        }
        console.log('💰 [DEBUG] YouTube insertion price adicionado:', prices.youtubeInsertion)
      }
      if (platforms.youtube.pricing.dedicated) {
        prices.youtubeDedicated = {
          name: 'Vídeo Dedicado YouTube',
          price: platforms.youtube.pricing.dedicated
        }
        console.log('💰 [DEBUG] YouTube dedicated price adicionado:', prices.youtubeDedicated)
      }
      if (platforms.youtube.pricing.shorts) {
        prices.youtubeShorts = {
          name: 'YouTube Shorts',
          price: platforms.youtube.pricing.shorts
        }
        console.log('💰 [DEBUG] YouTube shorts price adicionado:', prices.youtubeShorts)
      }
    } else {
      console.log('💰 [DEBUG] YouTube pricing não encontrado')
    }
    
    console.log('💰 [DEBUG] extractPrices - Resultado final:', prices)
    return prices
  }
  
  // Função auxiliar para extrair histórico de marcas
  const extractBrandHistory = (platforms: any) => {
    const history: any = {
      instagram: [],
      tiktok: [],
      youtube: []
    }
    
    if (platforms?.instagram?.brandHistory) {
      history.instagram = platforms.instagram.brandHistory
    }
    
    if (platforms?.tiktok?.brandHistory) {
      history.tiktok = platforms.tiktok.brandHistory
    }
    
    if (platforms?.youtube?.brandHistory) {
      history.youtube = platforms.youtube.brandHistory
    }
    
    return history
  }
  
  // Função auxiliar para extrair screenshots
  const extractScreenshots = (platforms: any) => {
    const screenshots: string[] = []
    
    if (platforms) {
      Object.values(platforms).forEach((platform: any) => {
        if (platform.screenshots && Array.isArray(platform.screenshots)) {
          screenshots.push(...platform.screenshots)
        }
      })
    }
    
    return screenshots
  }

  // 📊 Calcular progresso do formulário (baseado em campos preenchidos)
  const calculateProgress = () => {
    const values = form.watch()
    let completed = 0
    let total = 6

    // Seção pessoal - qualquer campo preenchido conta
    if (values.personalInfo?.name || values.personalInfo?.age || values.personalInfo?.gender) completed++
    
    // Seção localização - qualquer campo preenchido conta
    if (values.location?.city || values.location?.state || values.location?.cep) completed++
    
    // Seção contato - qualquer campo preenchido conta
    if (values.contact?.email || values.contact?.whatsapp) completed++
    
    // Seção plataformas - verificar se há plataformas com dados
    const platforms = values.platforms || {}
    const hasActivePlatforms = Object.values(platforms).some((platform: any) => 
      platform && platform.followers > 0
    )
    if (hasActivePlatforms) completed++
    
    // Seção categorias/negócios - qualquer campo preenchido conta
    if (values.business?.categories?.length || values.business?.agencyName || values.business?.responsibleName) completed++
    
    // Seção preços - qualquer preço definido conta
    const hasPricing = Object.values(platforms).some((platform: any) => {
      return platform?.pricing && Object.keys(platform.pricing).length > 0
    })
    if (hasPricing) completed++

    return Math.round((completed / total) * 100)
  }

  const progress = calculateProgress()

  // 📋 Configuração das abas
  const tabs = [
    {
      id: 'personal',
      label: 'Pessoal',
      icon: User,
      component: PersonalInfoSection,
      description: 'Informações básicas do influenciador'
    },
    {
      id: 'location',
      label: 'Localização',
      icon: MapPin,
      component: LocationContactSection,
      description: 'Localização e contato'
    },
    {
      id: 'platforms',
      label: 'Redes Sociais',
      icon: Layers,
      component: SocialPlatformsSection,
      description: 'Plataformas e métricas'
    },
    {
      id: 'categories',
      label: 'Categorias',
      icon: Layers,
      component: CategoriesBusinessSection,
      description: 'Categorias e informações de negócio'
    },
    {
      id: 'brands',
      label: 'Marcas',
      icon: Building2,
      component: BrandsAssociationSection,
      description: 'Associação com marcas'
    },
    {
      id: 'pricing',
      label: 'Preços',
      icon: DollarSign,
              component: PricingPartnershipsSection,
      description: 'Preços e parcerias'
    },
    {
      id: 'metrics',
      label: 'Resumo',
      icon: Layers,
      component: MetricsPreviewSection,
      description: 'Métricas e visualização final'
    }
  ]

  const currentTab = tabs.find(tab => tab.id === activeTab)
  const isPanelOpen = open

  return (
    <>
      {/* Painel lateral estilo Nova Marca */}
      <div className={cn(
        "fixed inset-y-0 right-0 w-[45rem] bg-muted border-l border-border dark:bg-[#080210] transform transition-transform duration-300 ease-in-out z-50 shadow-xl flex flex-col",
        isPanelOpen ? "translate-x-0" : "translate-x-full"
      )}>
        <FormProvider {...form}>
          {/* Header do Painel */}
          <div className="flex items-center bg-[#5600ce] text-white justify-between p-6 border-b border-border flex-shrink-0">
          <div>
              <h2 className="text-lg font-semibold">
                {actualMode === 'create' ? 'Novo Influenciador' : 'Editar Influenciador'}
              </h2>
            
            </div>
            <Button variant="ghost" size="sm" onClick={() => onOpenChange?.(false)}>
              <X className="h-4 w-4" />
                    </Button>
                  </div>

          {/* Progress */}
          <div className="px-6 py-3 border-b border-border bg-muted/20 flex-shrink-0">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Progresso do formulário</span>
              <span className="font-medium">{progress}% completo</span>
                    </div>
            <Progress value={progress} className="h-2 mt-2" />
                  </div>

          {/* Navigation Tabs */}
          <div className="px-6 py-4 border-b border-border flex-shrink-0">
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab, index) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                const hasError = Object.keys(errors).some(key => key.startsWith(tab.id))
                
                // Verificar se a aba tem dados obrigatórios preenchidos
                const values = form.watch()
                let hasRequiredData = false
                let showRequiredIndicator = false
                
                if (tab.id === 'personal') {
                  hasRequiredData = !!(values.personalInfo?.name && values.personalInfo.name.trim())
                  showRequiredIndicator = true // Mostrar indicador apenas para dados obrigatórios
                } else if (tab.id === 'platforms') {
                  const platforms = values.platforms || {}
                  hasRequiredData = Object.values(platforms).some((platform: any) => 
                    platform && (platform.followers > 0 || platform.username)
                  )
                  showRequiredIndicator = false // Redes sociais são opcionais agora
                }
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                      className={cn(
                      "flex items-center gap-2 px-3 py-2 rounded-md text-xs font-medium transition-colors",
                      isActive 
                        ? "bg-[#ff0074] text-white" 
                        : "dark:bg-[#181129] dark:text-[#ddcaff] bg-black/5 text-muted-foreground dark:hover:bg-[#181129]/80",
                      hasError && !isActive && "bg-red-50 text-red-600 border border-red-200"
                    )}
                  >
                    <Icon className="h-3 w-3" />
                    {tab.label}
                    {hasError ? (
                      <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                    ) : hasRequiredData ? (
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    ) : showRequiredIndicator ? (
                      <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                    ) : null}
                  </button>
                )
              })}
                      </div>
                  </div>
          
          {/* Validation Errors Summary */}
          {Object.keys(errors).length > 0 && (
            <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex-shrink-0">
                      <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm font-medium text-red-700">
                  {Object.keys(errors).length} campo(s) com erro
                      </span>
                  </div>
                </div>
              )}

          {/* Formulário - Área scrollável */}
          <div className="flex-1 min-h-0">
            <div className="h-full overflow-y-auto p-6 pb-24">
              {currentTab && (
                <currentTab.component />
              )}
                      </div>
                  </div>

          {/* Botões de Ação - Fixos no Final */}
          <div className="p-6 bg-background border-t border-border flex-shrink-0">


            <div className="flex gap-3">
                    <Button
                onClick={() => {
                  console.log('🖱️ [DEBUG] Botão "Criar Influenciador" clicado!');
                  console.log('🖱️ [DEBUG] isSubmitting:', isSubmitting);
                  console.log('🖱️ [DEBUG] form values:', form.getValues());
                  handleSubmit();
                }}
                disabled={isSubmitting}
                className="flex-1 bg-[#ff0074] text-white hover:bg-[#e6006a]"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {actualMode === 'create' ? 'Criando...' : 'Salvando...'}
                  </div>
                ) : (
                  actualMode === 'create' ? 'Criar Influenciador' : 'Salvar Alterações'
                      )}
                    </Button>
              
              <Button variant="outline" onClick={() => onOpenChange?.(false)}>
                Cancelar
                    </Button>
                  </div>
                    </div>
        </FormProvider>
                  </div>
                  
      {/* Overlay */}
      {isPanelOpen && (
        <div 
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={() => onOpenChange?.(false)}
        />
      )}
    </>
  )
}


