import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth, useRouteProtection } from './use-auth-v2';
import { Campaign, CreateCampaignData, UpdateCampaignData } from '@/types/campaign';

/**
 * 📊 HOOK DE CAMPAIGNS COM ISOLAMENTO AUTOMÁTICO
 * FASE 6.1: Hook atualizado para trabalhar com isolamento por usuário
 */

interface UseCampaignsResult {
  campaigns: Campaign[];
  loading: boolean;
  error: string | null;
  createCampaign: (data: CreateCampaignData) => Promise<Campaign>;
  updateCampaign: (id: string, data: UpdateCampaignData) => Promise<Campaign>;
  deleteCampaign: (id: string) => Promise<void>;
  refreshCampaigns: () => Promise<void>;
  getCampaignById: (id: string) => Campaign | undefined;
  getCampaignsByBrand: (brandId: string) => Campaign[];
}

interface UseCampaignsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableCache?: boolean;
  brandFilter?: string;
  onError?: (error: string) => void;
  onSuccess?: (message: string) => void;
}

/**
 * Hook principal para gerenciamento de campaigns
 */
export function useCampaigns(options: UseCampaignsOptions = {}): UseCampaignsResult {
  const { currentUser, firebaseUser } = useAuth();
  const { isAuthenticated } = useRouteProtection();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 segundos
    enableCache = true,
    brandFilter,
    onError,
    onSuccess
  } = options;

  /**
   * Buscar todas as campanhas do usuário
   */
  const refreshCampaigns = useCallback(async () => {
    if (!isAuthenticated || !firebaseUser) {
      setCampaigns([]);
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log('[USE_CAMPAIGNS] Carregando campanhas do usuário:', firebaseUser.uid);
      
      // Construir query params se houver filtro de marca
      const queryParams = new URLSearchParams();
      if (brandFilter) {
        queryParams.append('brandId', brandFilter);
      }
      
      const url = `/api/campaigns${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Resposta de API inválida');
      }
      
      const campaignsData = result.data || [];
      setCampaigns(campaignsData);
      
      console.log(`[USE_CAMPAIGNS] ${campaignsData.length} campanhas carregadas`);
      
      if (enableCache) {
        // Cache local para performance
        const cacheKey = `campaigns_${firebaseUser.uid}${brandFilter ? `_${brandFilter}` : ''}`;
        localStorage.setItem(cacheKey, JSON.stringify({
          data: campaignsData,
          timestamp: Date.now()
        }));
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido ao carregar campanhas';
      console.error('[USE_CAMPAIGNS] Erro:', err);
      setError(errorMessage);
      onError?.(errorMessage);
      
      // Tentar carregar do cache em caso de erro
      if (enableCache && firebaseUser) {
        try {
          const cacheKey = `campaigns_${firebaseUser.uid}${brandFilter ? `_${brandFilter}` : ''}`;
          const cached = localStorage.getItem(cacheKey);
          if (cached) {
            const { data, timestamp } = JSON.parse(cached);
            if (Date.now() - timestamp < 300000) { // 5 minutos
              setCampaigns(data);
              console.log('[USE_CAMPAIGNS] Dados carregados do cache');
            }
          }
        } catch (cacheError) {
          console.warn('[USE_CAMPAIGNS] Erro ao carregar cache:', cacheError);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, firebaseUser, enableCache, brandFilter, onError]);

  /**
   * Criar nova campanha
   */
  const createCampaign = useCallback(async (data: CreateCampaignData): Promise<Campaign> => {
    if (!isAuthenticated || !firebaseUser) {
      throw new Error('Usuário não autenticado');
    }

    try {
      console.log('[USE_CAMPAIGNS] Criando campanha:', data.name);
      
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Falha ao criar campanha');
      }
      
      const newCampaign = result.data;
      setCampaigns(prev => [newCampaign, ...prev]);
      
      console.log('[USE_CAMPAIGNS] Campanha criada:', newCampaign.id);
      onSuccess?.('Campanha criada com sucesso!');
      
      // Atualizar cache
      if (enableCache && firebaseUser) {
        setCampaigns(currentCampaigns => {
          const updatedCampaigns = [newCampaign, ...currentCampaigns];
          const cacheKey = `campaigns_${firebaseUser.uid}${brandFilter ? `_${brandFilter}` : ''}`;
          localStorage.setItem(cacheKey, JSON.stringify({
            data: updatedCampaigns,
            timestamp: Date.now()
          }));
          return currentCampaigns;
        });
      }
      
      return newCampaign;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar campanha';
      console.error('[USE_CAMPAIGNS] Erro ao criar:', err);
      onError?.(errorMessage);
      throw new Error(errorMessage);
    }
  }, [isAuthenticated, firebaseUser, enableCache, brandFilter, onSuccess, onError]);

  /**
   * Atualizar campanha existente
   */
  const updateCampaign = useCallback(async (id: string, data: UpdateCampaignData): Promise<Campaign> => {
    if (!isAuthenticated || !firebaseUser) {
      throw new Error('Usuário não autenticado');
    }

    try {
      console.log('[USE_CAMPAIGNS] Atualizando campanha:', id);
      
      const response = await fetch(`/api/campaigns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Falha ao atualizar campanha');
      }
      
      const updatedCampaign = result.data;
      setCampaigns(prev => prev.map(campaign => 
        campaign.id === id ? updatedCampaign : campaign
      ));
      
      console.log('[USE_CAMPAIGNS] Campanha atualizada:', id);
      onSuccess?.('Campanha atualizada com sucesso!');
      
      // Atualizar cache
      if (enableCache && firebaseUser) {
        setCampaigns(currentCampaigns => {
          const updatedCampaigns = currentCampaigns.map(campaign => 
            campaign.id === id ? updatedCampaign : campaign
          );
          const cacheKey = `campaigns_${firebaseUser.uid}${brandFilter ? `_${brandFilter}` : ''}`;
          localStorage.setItem(cacheKey, JSON.stringify({
            data: updatedCampaigns,
            timestamp: Date.now()
          }));
          return currentCampaigns;
        });
      }
      
      return updatedCampaign;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar campanha';
      console.error('[USE_CAMPAIGNS] Erro ao atualizar:', err);
      onError?.(errorMessage);
      throw new Error(errorMessage);
    }
  }, [isAuthenticated, firebaseUser, enableCache, brandFilter, onSuccess, onError]);

  /**
   * Deletar campanha
   */
  const deleteCampaign = useCallback(async (id: string): Promise<void> => {
    if (!isAuthenticated || !firebaseUser) {
      throw new Error('Usuário não autenticado');
    }

    try {
      console.log('[USE_CAMPAIGNS] Deletando campanha:', id);
      
      const response = await fetch(`/api/campaigns/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Falha ao deletar campanha');
      }
      
      setCampaigns(prev => prev.filter(campaign => campaign.id !== id));
      
      console.log('[USE_CAMPAIGNS] Campanha deletada:', id);
      onSuccess?.('Campanha deletada com sucesso!');
      
      // Atualizar cache
      if (enableCache && firebaseUser) {
        setCampaigns(currentCampaigns => {
          const updatedCampaigns = currentCampaigns.filter(campaign => campaign.id !== id);
          const cacheKey = `campaigns_${firebaseUser.uid}${brandFilter ? `_${brandFilter}` : ''}`;
          localStorage.setItem(cacheKey, JSON.stringify({
            data: updatedCampaigns,
            timestamp: Date.now()
          }));
          return currentCampaigns;
        });
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao deletar campanha';
      console.error('[USE_CAMPAIGNS] Erro ao deletar:', err);
      onError?.(errorMessage);
      throw new Error(errorMessage);
    }
  }, [isAuthenticated, firebaseUser, enableCache, brandFilter, onSuccess, onError]);

  /**
   * Buscar campanha por ID
   */
  const getCampaignById = useCallback((id: string): Campaign | undefined => {
    return campaigns.find(campaign => campaign.id === id);
  }, [campaigns]);

  /**
   * Buscar campanhas por marca
   */
  const getCampaignsByBrand = useCallback((brandId: string): Campaign[] => {
    return campaigns.filter(campaign => campaign.brandId === brandId);
  }, [campaigns]);

  // Ref para manter a função refreshCampaigns estável
  const refreshCampaignsRef = useRef(refreshCampaigns);
  refreshCampaignsRef.current = refreshCampaigns;

  // Efeito principal - carregar dados iniciais
  useEffect(() => {
    if (isAuthenticated && firebaseUser) {
      refreshCampaignsRef.current();
    }
  }, [isAuthenticated, firebaseUser?.uid, brandFilter]);

  // Auto-refresh periódico
  useEffect(() => {
    if (!autoRefresh || !isAuthenticated || !firebaseUser) return;

    const interval = setInterval(() => {
      refreshCampaignsRef.current();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isAuthenticated, firebaseUser?.uid]);

  // Limpeza ao fazer logout
  useEffect(() => {
    if (!isAuthenticated) {
      setCampaigns([]);
      setError(null);
      setLoading(false);
      
      // Limpar cache do usuário anterior
      if (enableCache) {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('campaigns_')) {
            localStorage.removeItem(key);
          }
        });
      }
    }
  }, [isAuthenticated, enableCache]);

  return {
    campaigns,
    loading,
    error,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    refreshCampaigns,
    getCampaignById,
    getCampaignsByBrand
  };
}

/**
 * Hook simplificado para apenas listar campanhas
 */
export function useCampaignsList(brandId?: string): Pick<UseCampaignsResult, 'campaigns' | 'loading' | 'error'> {
  const { campaigns, loading, error } = useCampaigns({ 
    autoRefresh: false,
    enableCache: true,
    brandFilter: brandId
  });
  
  return { campaigns, loading, error };
}

/**
 * Hook para uma campanha específica
 */
export function useCampaign(campaignId: string): {
  campaign: Campaign | null | undefined;
  loading: boolean;
  error: string | null;
  updateCampaign: (data: UpdateCampaignData) => Promise<Campaign>;
  deleteCampaign: () => Promise<void>;
} {
  const { getCampaignById, updateCampaign: updateCampaignById, deleteCampaign: deleteCampaignById, loading, error } = useCampaigns();
  
  const campaign = campaignId ? getCampaignById(campaignId) : null;
  
  const updateCampaign = useCallback(async (data: UpdateCampaignData) => {
    if (!campaignId) throw new Error('ID da campanha é obrigatório');
    return updateCampaignById(campaignId, data);
  }, [campaignId, updateCampaignById]);
  
  const deleteCampaign = useCallback(async () => {
    if (!campaignId) throw new Error('ID da campanha é obrigatório');
    return deleteCampaignById(campaignId);
  }, [campaignId, deleteCampaignById]);
  
  return {
    campaign,
    loading,
    error,
    updateCampaign,
    deleteCampaign
  };
}

/**
 * Hook para campanhas de uma marca específica
 */
export function useBrandCampaigns(brandId: string): {
  campaigns: Campaign[];
  loading: boolean;
  error: string | null;
  createCampaign: (data: CreateCampaignData) => Promise<Campaign>;
} {
  const { campaigns, loading, error, createCampaign } = useCampaigns({
    brandFilter: brandId,
    enableCache: true
  });
  
  return {
    campaigns,
    loading,
    error,
    createCampaign
  };
}

