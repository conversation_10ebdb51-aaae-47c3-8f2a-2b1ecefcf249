'use client'

import { useQuery, useMutation, useApolloClient } from '@apollo/client'
import { useFirebaseAuth } from '@/hooks/use-clerk-auth'
import { 
  GET_BRANDS_QUERY, 
  CREATE_BRAND_MUTATION, 
  UPDATE_BRAND_MUTATION, 
  DELETE_BRAND_MUTATION 
} from '@/lib/graphql/mutations'

export interface BrandServicePrice {
  name?: string
  price?: number
}

export interface BrandPrices {
  instagramStory?: BrandServicePrice
  instagramReel?: BrandServicePrice
  tiktokVideo?: BrandServicePrice
  youtubeInsertion?: BrandServicePrice
  youtubeDedicated?: BrandServicePrice
  youtubeShorts?: BrandServicePrice
}

export interface Brand {
  id: string
  name: string
  prices?: BrandPrices
  influencerId?: string
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateBrandInput {
  name: string
  prices?: BrandPrices
  influencerId?: string
  userId: string
}

export interface UpdateBrandInput {
  name?: string
  prices?: BrandPrices
}

export function useBrandsGraphQL(influencerId?: string) {
  const { currentUser } = useFirebaseAuth()
  const client = useApolloClient()

  // 🔥 OTIMIZAÇÃO: Query com cache inteligente
  const { 
    data: brandsData, 
    loading: brandsLoading, 
    error: brandsError,
    refetch: refetchBrands 
  } = useQuery(GET_BRANDS_QUERY, {
    variables: { 
      userId: currentUser?.id || '',
      influencerId: influencerId || null
    },
    skip: !currentUser?.id,
    errorPolicy: 'all',
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache (era cache-and-network)
    notifyOnNetworkStatusChange: false // 🔥 OTIMIZAÇÃO: Reduzir re-renders
  })

  // Mutation para criar brand
  const [createBrandMutation, { loading: createLoading }] = useMutation(CREATE_BRAND_MUTATION, {
    update(cache, { data }) {
      if (data?.createBrand) {
        // Atualizar cache adicionando nova brand
        const existingBrands = cache.readQuery<{ brands: Brand[] }>({
          query: GET_BRANDS_QUERY,
          variables: { 
            userId: currentUser?.id || '',
            influencerId: influencerId || null
          }
        })

        if (existingBrands?.brands) {
          cache.writeQuery({
            query: GET_BRANDS_QUERY,
            variables: { 
              userId: currentUser?.id || '',
              influencerId: influencerId || null
            },
            data: {
              brands: [data.createBrand, ...existingBrands.brands]
            }
          })
        }
      }
    },
    onError: (error) => {
      console.error('Erro ao criar marca:', error)
    }
  })

  // Mutation para atualizar brand
  const [updateBrandMutation, { loading: updateLoading }] = useMutation(UPDATE_BRAND_MUTATION, {
    onError: (error) => {
      console.error('Erro ao atualizar marca:', error)
    }
  })

  // Mutation para deletar brand
  const [deleteBrandMutation, { loading: deleteLoading }] = useMutation(DELETE_BRAND_MUTATION, {
    update(cache, { data }, { variables }) {
      if (data?.deleteBrand && variables?.id) {
        // Remover brand do cache
        const existingBrands = cache.readQuery<{ brands: Brand[] }>({
          query: GET_BRANDS_QUERY,
          variables: { 
            userId: currentUser?.id || '',
            influencerId: influencerId || null
          }
        })

        if (existingBrands?.brands) {
          cache.writeQuery({
            query: GET_BRANDS_QUERY,
            variables: { 
              userId: currentUser?.id || '',
              influencerId: influencerId || null
            },
            data: {
              brands: existingBrands.brands.filter((brand: Brand) => brand.id !== variables.id)
            }
          })
        }
      }
    },
    onError: (error) => {
      console.error('Erro ao deletar marca:', error)
    }
  })

  // Funções auxiliares
  const createBrand = async (input: CreateBrandInput) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await createBrandMutation({
        variables: {
          input: {
            ...input,
            userId: currentUser.id
          }
        }
      })
      return result.data?.createBrand
    } catch (error) {
      console.error('Erro ao criar marca:', error)
      throw error
    }
  }

  const updateBrand = async (id: string, input: UpdateBrandInput) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await updateBrandMutation({
        variables: { id, input }
      })
      return result.data?.updateBrand
    } catch (error) {
      console.error('Erro ao atualizar marca:', error)
      throw error
    }
  }

  const deleteBrand = async (id: string) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await deleteBrandMutation({
        variables: { 
          id
        }
      })
      return result.data?.deleteBrand
    } catch (error) {
      console.error('Erro ao deletar marca:', error)
      throw error
    }
  }

  const clearBrandsCache = () => {
    client.cache.evict({
      fieldName: 'brands'
    })
    client.cache.gc()
  }

  return {
    // Dados
    brands: brandsData?.brands || [],
    
    // Estados de loading
    brandsLoading,
    createLoading,
    updateLoading,
    deleteLoading,
    
    // Erros
    brandsError,
    
    // Funções
    createBrand,
    updateBrand,
    deleteBrand,
    refetchBrands,
    clearBrandsCache,
    
    // Estados computados
    isLoading: brandsLoading || createLoading || updateLoading || deleteLoading,
    hasError: !!brandsError
  }
} 

