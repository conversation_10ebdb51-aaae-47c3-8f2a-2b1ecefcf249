'use client';

import { SignIn } from '@clerk/nextjs';
import { BackdropEffect } from '@/components/ui/backdrop-effect';
import { useParams } from 'next/navigation';

// Traduções específicas para a página de login
const signInLocalization = {
  signIn: {
    start: {
      title: 'Bem-vindo de volta!',
      subtitle: 'Entre na sua conta para continuar',
      actionText: 'Quer acesso antecipado?',
      actionLink: 'Entrar na lista de espera',
      actionText__join_waitlist: 'Quer acesso antecipado?',
      actionLink__join_waitlist: 'Entrar na lista de espera',
      footerActionText: 'Não tem uma conta?',
      footerActionLink: 'C<PERSON><PERSON> conta',
    },
    password: {
      title: 'Digite sua senha',
      subtitle: 'Digite a senha associada à sua conta',
      actionLink: 'Esqueceu sua senha?',
    },
    alternativeMethods: {
      title: 'Use outro método',
      subtitle: 'Enfrentando problemas? Você pode usar qualquer um destes métodos para entrar.',
      actionText: 'Não tem nenhum destes?',
      actionLink: 'Usar outro método',
    },
    forgotPassword: {
      title: 'Esqueceu sua senha?',
      subtitle: 'Não se preocupe, vamos te ajudar a recuperar o acesso à sua conta',
      subtitle_email: 'Digite o código enviado para seu email',
      subtitle_phone: 'Digite o código enviado para seu telefone',
      formTitle: 'Redefinir senha',
      formSubtitle: 'Digite o código de verificação',
      resendButton: 'Reenviar código',
    },
    resetPassword: {
      title: 'Criar nova senha',
      subtitle: 'Sua nova senha deve ser diferente da senha anterior',
      formTitle: 'Definir nova senha',
      successMessage: 'Senha alterada com sucesso!',
    },
    emailCode: {
      title: 'Verificar email',
      subtitle: 'Digite o código enviado para seu email',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de 6 dígitos enviado para {{identifier}}',
      resendButton: 'Reenviar código',
    },
    phoneCode: {
      title: 'Verificar telefone',
      subtitle: 'Digite o código enviado para seu telefone',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de 6 dígitos enviado para {{identifier}}',
      resendButton: 'Reenviar código',
    },
    phoneCodeMfa: {
      title: 'Verificação em duas etapas',
      subtitle: 'Para continuar, digite o código de verificação enviado para seu telefone',
      formTitle: 'Código SMS',
      formSubtitle: 'Digite o código enviado para {{identifier}}',
      resendButton: 'Reenviar código',
    },
    totpMfa: {
      title: 'Verificação em duas etapas',
      subtitle: 'Para continuar, digite o código de verificação gerado pelo seu aplicativo autenticador',
      formTitle: 'Código do aplicativo',
      formSubtitle: 'Digite o código de 6 dígitos do seu aplicativo autenticador',
    },
    backupCodeMfa: {
      title: 'Usar código de backup',
      subtitle: 'Digite um dos códigos de backup que você salvou quando configurou a autenticação de duas etapas',
      formTitle: 'Código de backup',
      formSubtitle: 'Digite um código de backup',
    },
    accountSwitcher: {
      title: 'Escolher conta',
      subtitle: 'Selecione a conta com a qual deseja continuar.',
      action__addAccount: 'Adicionar conta',
      action__signOutAll: 'Sair de todas as contas',
    },
  },
  userButton: {
    action__manageAccount: 'Gerenciar conta',
    action__signOut: 'Sair',
    action__signOutAll: 'Sair de todas as contas',
    action__addAccount: 'Adicionar conta',
  },
  // Labels e placeholders de formulário
  formFieldLabel__emailAddress: 'Email',
  formFieldLabel__emailAddress_username: 'Email ou nome de usuário',
  formFieldLabel__phoneNumber: 'Telefone',
  formFieldLabel__username: 'Nome de usuário',
  formFieldLabel__password: 'Senha',
  formFieldLabel__currentPassword: 'Senha atual',
  formFieldLabel__newPassword: 'Nova senha',
  formFieldLabel__confirmPassword: 'Confirmar nova senha',
  
  formFieldInputPlaceholder__emailAddress: 'Digite seu email',
  formFieldInputPlaceholder__emailAddress_username: 'Digite seu email ou nome de usuário',
  formFieldInputPlaceholder__phoneNumber: 'Digite seu telefone',
  formFieldInputPlaceholder__username: 'Digite seu nome de usuário',
  formFieldInputPlaceholder__password: 'Digite sua senha',
  formFieldInputPlaceholder__currentPassword: 'Digite sua senha atual',
  formFieldInputPlaceholder__newPassword: 'Digite sua nova senha',
  formFieldInputPlaceholder__confirmPassword: 'Confirme sua nova senha',
  
  // Botões
  formButtonPrimary: 'Entrar',
  formButtonPrimary__verify: 'Verificar',
  formButtonPrimary__continue: 'Continuar',
  formButtonPrimary__finish: 'Finalizar',
  formButtonPrimary__signIn: 'Entrar',
  formButtonPrimary__resetPassword: 'Redefinir senha',
  
  // Outros textos
  dividerText: 'ou',
  footerActionLink__useAnotherMethod: 'Usar outro método',
  footerActionLink__signUp: 'Não tem uma conta? Criar conta',
  
  // Mensagens de validação e erro
  formFieldError__notMatchingPasswords: 'As senhas não coincidem',
  formFieldError__matchingPasswords: 'As senhas coincidem',
  formFieldAction__forgotPassword: 'Esqueceu a senha?',
  
  // Loading
  loading: 'Carregando...',
  
  // Mensagens de validação de senha
  formFieldSuccessText__password: 'Sua senha atende a todos os requisitos necessários.',
  formFieldHintText__password: 'Sua senha deve ter pelo menos 8 caracteres.',
  formFieldValidationText__password_meets_requirements: 'Sua senha atende a todos os requisitos necessários.',
  
  // Badges
  badge__primary: 'Principal',
  badge__thisDevice: 'Este dispositivo',
  badge__unverified: 'Não verificado',
  badge__you: 'Você',
  
  // Mensagens de erro comuns
  unstable__errors: {
    identification_deletion_failed: 'Não é possível excluir sua última identificação.',
    phone_number_exists: 'Este número de telefone já está sendo usado por outra conta.',
    email_address_exists: 'Este endereço de email já está sendo usado por outra conta.',
    captcha_invalid: 'Verificação de segurança inválida. Tente novamente.',
    form_param_format_invalid__phone_number: 'O número de telefone deve estar em um formato internacional válido.',
    form_param_format_invalid__email_address: 'Digite um endereço de email válido.',
    form_password_pwned: 'Esta senha foi encontrada em uma violação de dados e não pode ser usada.',
    form_username_invalid_length: 'O nome de usuário deve ter entre {{min_length}} e {{max_length}} caracteres.',
    form_password_length_too_short: 'A senha deve ter pelo menos {{min_length}} caracteres.',
    password_not_strong_enough: 'A senha não é forte o suficiente.',
    form_password_validation_failed: 'Senha incorreta.',
    form_identifier_not_found: 'Não foi possível encontrar uma conta com essas credenciais.',
    not_allowed_access: 'Acesso negado.',
    session_exists: 'Você já está conectado.',
    too_many_requests: 'Muitas tentativas. Tente novamente mais tarde.',
    incorrect_password: 'Senha incorreta. Tente novamente.',
    invalid_credentials: 'Credenciais inválidas. Verifique suas informações e tente novamente.',
    user_locked: 'Sua conta foi temporariamente bloqueada. Tente novamente mais tarde.',
  },

  // Traduções diretas para strings específicas
  'Want early access?': 'Quer acesso antecipado?',
  'Join waitlist': 'Entrar na lista de espera',
  "Don't have an account?": 'Não tem uma conta?',
  'Sign up': 'Criar conta',
};

export default function Page() {
  const params = useParams();
  
  // Detectar o idioma atual da URL ou usar o padrão
  let currentLocale = 'pt';
  
  // Se estivermos em uma rota com idioma, extrair da URL
  if (typeof window !== 'undefined') {
    const pathSegments = window.location.pathname.split('/');
    const possibleLocale = pathSegments[1];
    if (['pt', 'en', 'es'].includes(possibleLocale)) {
      currentLocale = possibleLocale;
    }
  }

  // Construir as URLs com base no idioma atual
  const signInPath = `/${currentLocale}/sign-in`;
  const signUpUrl = `/${currentLocale}/sign-up`;
  const redirectUrl = `/${currentLocale}/auth-redirect`;

  console.log('🔍 [SIGN-IN] Configuração:', {
    currentLocale,
    signInPath,
    signUpUrl,
    redirectUrl,
    pathname: typeof window !== 'undefined' ? window.location.pathname : 'SSR'
  });

  return (
    <div className="min-h-screen relative flex items-center justify-center">
      {/* Backdrop de estrelas animadas */}
      <BackdropEffect />
    
      {/* Overlay para o modo claro */}
      <div className="absolute inset-0" />
      
      {/* Container principal */}
      <div className="relative z-10 w-full max-w-md">
        <SignIn 
          path={signInPath}
          routing="path"
          signUpUrl={signUpUrl}
          fallbackRedirectUrl={redirectUrl}
          afterSignInUrl={redirectUrl}
          forceRedirectUrl={redirectUrl}
          appearance={{
            elements: {
              rootBox: "mx-auto",
              card: "bg-white",
            },
            variables: {
              colorPrimary: '#ff0074',
              colorSuccess: '#10b981',
              colorWarning: '#f59e0b',
              colorDanger: '#ef4444',
              colorNeutral: '#6b7280',
              fontFamily: 'inherit',
              borderRadius: '0.5rem'
            }
          }}
        />
      </div>
    </div>
  );
} 