import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { IsolationUtils } from '@/lib/utils/isolation';
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  updateDoc,
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';
import { clientDb as db } from '@/lib/firebase-client';

/**
 * 🔒 API DE GRUPOS COM ISOLAMENTO COMPLETO
 * Garantindo que cada usuário só acesse seus próprios grupos
 */

interface GroupData {
  id: string;
  name: string;
  description: string;
  influencerIds: string[];
  brandId: string;
  userId: string; // ✅ CAMPO OBRIGATÓRIO PARA ISOLAMENTO
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

// ✅ GET - Listar grupos com isolamento por usuário
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    console.log('🔍 [GROUPS_API] Buscando grupos para usuário:', userId);
    
    const { searchParams } = new URL(req.url);
    const brandId = searchParams.get('brandId');
    
    // ✅ VALIDAÇÃO: brandId deve ser igual ao userId (isolamento)
    if (brandId && brandId !== userId) {
      console.warn('⚠️ [GROUPS_API] Tentativa de acesso a marca diferente:', {
        userId,
        requestedBrandId: brandId
      });
      
      return NextResponse.json(
        { 
          error: 'Acesso negado. Você só pode acessar seus próprios grupos.',
          code: 'OWNERSHIP_DENIED'
        },
        { status: 403 }
      );
    }

    // ✅ QUERY COM ISOLAMENTO OBRIGATÓRIO
    const groupsQuery = query(
      collection(db, 'groups'),
      where('userId', '==', userId), // ✅ FILTRO OBRIGATÓRIO
      orderBy('createdAt', 'desc')
    );

    const snapshot = await getDocs(groupsQuery);
    const groups: GroupData[] = [];

    snapshot.forEach((doc) => {
      const data = doc.data();
      
      // ✅ VALIDAÇÃO ADICIONAL: Confirmar ownership
      if (data.userId !== userId) {
        console.warn('⚠️ [GROUPS_API] Documento com userId incorreto encontrado:', doc.id);
        return; // Pular documento
      }

      groups.push({
        id: doc.id,
        name: data.name || '',
        description: data.description || '',
        influencerIds: Array.isArray(data.influencerIds) ? data.influencerIds : [],
        brandId: data.brandId || userId,
        userId: data.userId,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        createdBy: data.createdBy || userId
      });
    });

    console.log(`✅ [GROUPS_API] ${groups.length} grupos carregados para usuário ${userId}`);

    // ✅ LOG DE AUDITORIA
    IsolationUtils.logIsolationEvent(
      'read',
      'groups',
      'list',
      userId,
      { count: groups.length, brandId }
    );

    return NextResponse.json({
      success: true,
      data: groups,
      count: groups.length,
      userId // ✅ CONFIRMAR USUÁRIO NA RESPOSTA
    });

  } catch (error) {
    console.error('❌ [GROUPS_API] Erro ao buscar grupos:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
});

// ✅ POST - Criar grupo com isolamento
export const POST = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    console.log('➕ [GROUPS_API] Criando grupo para usuário:', userId);
    
    const body = await req.json();
    
    // ✅ VALIDAÇÃO DOS DADOS
    if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Nome do grupo é obrigatório' },
        { status: 400 }
      );
    }

    // ✅ VALIDAÇÃO: brandId deve ser igual ao userId
    const brandId = body.brandId || userId;
    if (brandId !== userId) {
      console.warn('⚠️ [GROUPS_API] Tentativa de criar grupo para marca diferente:', {
        userId,
        requestedBrandId: brandId
      });
      
      return NextResponse.json(
        { 
          error: 'Você só pode criar grupos para sua própria marca.',
          code: 'OWNERSHIP_DENIED'
        },
        { status: 403 }
      );
    }

    // ✅ PREPARAR DADOS COM ISOLAMENTO
    const groupData = IsolationUtils.prepareCreateData(
      {
        name: body.name.trim(),
        description: body.description?.trim() || '',
        influencerIds: Array.isArray(body.influencerIds) ? body.influencerIds : [],
        brandId: userId // ✅ FORÇAR brandId = userId
      },
      userId,
      userId // createdBy = userId
    );

    // ✅ CRIAR NO FIRESTORE
    const docRef = await addDoc(collection(db, 'groups'), {
      ...groupData,
      createdAt: Timestamp.fromDate(groupData.createdAt),
      updatedAt: Timestamp.fromDate(groupData.updatedAt)
    });

    console.log(`✅ [GROUPS_API] Grupo criado com ID: ${docRef.id}`);

    // ✅ LOG DE AUDITORIA
    IsolationUtils.logIsolationEvent(
      'create',
      'groups',
      docRef.id,
      userId,
      { 
        name: groupData.name,
        influencerCount: groupData.influencerIds.length 
      }
    );

    return NextResponse.json({
      success: true,
      message: `Grupo "${groupData.name}" criado com sucesso`,
      groupId: docRef.id,
      data: {
        id: docRef.id,
        ...groupData
      }
    }, { status: 201 });

  } catch (error) {
    console.error('❌ [GROUPS_API] Erro ao criar grupo:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
});

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { groupId, name, description, influencerIds, isActive } = body;

    if (!groupId) {
      return NextResponse.json(
        { error: 'ID do grupo é obrigatório' },
        { status: 400 }
      );
    }

    const updateData: any = {
      updatedAt: Timestamp.fromDate(new Date())
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (influencerIds !== undefined) updateData.influencerIds = influencerIds;
    if (isActive !== undefined) updateData.isActive = isActive;

         await updateDoc(doc(db, 'groups', groupId), updateData);

    return NextResponse.json({
      success: true,
      message: 'Grupo atualizado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao atualizar grupo:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const groupId = url.searchParams.get('groupId');

    if (!groupId) {
      return NextResponse.json(
        { error: 'ID do grupo é obrigatório' },
        { status: 400 }
      );
    }

    // Soft delete - marcar como inativo
         await updateDoc(doc(db, 'groups', groupId), {
       isActive: false,
       updatedAt: Timestamp.fromDate(new Date())
     });

    return NextResponse.json({
      success: true,
      message: 'Grupo excluído com sucesso'
    });

  } catch (error) {
    console.error('Erro ao excluir grupo:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 

