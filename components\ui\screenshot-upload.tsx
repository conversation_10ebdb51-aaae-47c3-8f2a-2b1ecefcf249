import React, { useState, useCallback, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { Upload, Trash2, ImagePlus, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'

// Declaração de tipos para Clerk
declare global {
  interface Window {
    Clerk?: {
      session?: {
        getToken: () => Promise<string>;
      };
    };
  }
}

interface Screenshot {
  id: string
  url: string
  influencerId: string
}

interface PendingFile {
  file: File
  preview: string
  id: string
}

interface ScreenshotUploadProps {
  platform: string
  screenshots: Screenshot[]
  onScreenshotsUpdate: (screenshots: Screenshot[]) => void
  onPendingFilesChange: (files: PendingFile[]) => void
  pendingFiles: PendingFile[]
  maxFiles?: number
  acceptedTypes?: string[]
  maxFileSize?: number
  onAddFilesWithPreview?: (files: File[]) => PendingFile[]
  onClearPlatformFiles?: () => void
}

export function ScreenshotUpload({
  platform,
  screenshots = [],
  onScreenshotsUpdate,
  onPendingFilesChange,
  pendingFiles = [],
  maxFiles, // Removido o valor padrão - agora é opcional
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  maxFileSize = 5,
  onAddFilesWithPreview,
  onClearPlatformFiles
}: ScreenshotUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [viewingImage, setViewingImage] = useState<string | null>(null)
  const { toast } = useToast()

  // Nota: URLs de preview agora são gerenciadas externamente para persistir entre navegação de abas

  // Validar arquivo
  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `Tipo de arquivo não suportado`
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return `Arquivo muito grande. Máximo: ${maxFileSize}MB`
    }
    
    return null
  }

  // Adicionar arquivos à lista pendente
  const handleFileSelection = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const totalFiles = screenshots.length + pendingFiles.length + fileArray.length

    // Verificar limite apenas se maxFiles estiver definido
    if (maxFiles && totalFiles > maxFiles) {
      toast({
        title: "Limite excedido",
        description: `Máximo de ${maxFiles} screenshots`,
        variant: "destructive"
      })
      return
    }

    // Validar arquivos
    const validFiles: File[] = []
    fileArray.forEach((file) => {
      const error = validateFile(file)
      if (error) {
        toast({
          title: "Erro no arquivo",
          description: error,
          variant: "destructive"
        })
        return
      }
      validFiles.push(file)
    })

    if (validFiles.length > 0) {
      // Usar função externa se disponível, senão criar localmente
      if (onAddFilesWithPreview) {
        const newPendingFiles = onAddFilesWithPreview(validFiles)
        toast({
          title: "Arquivos selecionados",
          description: `${newPendingFiles.length} arquivo(s) adicionado(s)`
        })
      } else {
        // Fallback para compatibilidade
        const newPendingFiles: PendingFile[] = validFiles.map(file => ({
          file,
          preview: URL.createObjectURL(file),
          id: `${platform}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }))

        const allPendingFiles = [...pendingFiles, ...newPendingFiles]
        onPendingFilesChange(allPendingFiles)

        toast({
          title: "Arquivos selecionados",
          description: `${newPendingFiles.length} arquivo(s) adicionado(s)`
        })
      }
    }
  }, [screenshots, pendingFiles, platform, maxFiles, toast, onPendingFilesChange, onAddFilesWithPreview])

  // Remover arquivo pendente
  const handleRemovePendingFile = useCallback((fileId: string) => {
    const updatedFiles = pendingFiles.filter(f => f.id !== fileId)
    onPendingFilesChange(updatedFiles)
    // Nota: URL de preview será revogada automaticamente pelo gerenciador externo
  }, [pendingFiles, onPendingFilesChange])

  // Remover screenshot já salvo
  const handleRemoveScreenshot = useCallback(async (screenshot: Screenshot) => {
    try {
      console.log('🗑️ Iniciando deleção de screenshot:', {
        id: screenshot.id,
        url: screenshot.url,
        influencerId: screenshot.influencerId,
        platform: platform
      })

      // Verificar se temos os dados necessários
      if (!screenshot.url) {
        throw new Error('URL do screenshot não encontrada')
      }

      if (!screenshot.influencerId) {
        throw new Error('ID do influenciador não encontrado')
      }

      if (!platform) {
        throw new Error('Plataforma não especificada')
      }

      // Obter token de autenticação do Clerk
      let token = null;
      if (typeof window !== 'undefined' && window.Clerk?.session) {
        try {
          token = await window.Clerk.session.getToken();
        } catch (error) {
          console.warn('Erro ao obter token do Clerk:', error);
        }
      }

      // Chamar mutation GraphQL para deletar screenshot
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        body: JSON.stringify({
          query: `
            mutation DeleteScreenshot($id: ID!, $influencerId: ID!, $platform: String!) {
              deleteScreenshot(id: $id, influencerId: $influencerId, platform: $platform)
            }
          `,
          variables: {
            id: screenshot.url, // Usar URL como ID para identificar o screenshot
            influencerId: screenshot.influencerId || screenshot.id, // Fallback para screenshot.id se influencerId não existir
            platform: platform
          }
        })
      })

      if (!response.ok) {
        throw new Error('Erro ao deletar screenshot')
      }

      const result = await response.json()
      console.log('🗑️ Resposta da API GraphQL:', result)

      if (result.errors) {
        console.error('🗑️ Erros GraphQL:', result.errors)
        throw new Error(result.errors[0]?.message || 'Erro ao deletar screenshot')
      }

      // Verificar se a deleção foi bem-sucedida
      const deleteResult = result.data?.deleteScreenshot
      console.log('🗑️ Resultado da deleção:', deleteResult)

      if (deleteResult === true) {
        // Remover da lista local apenas se a deleção no servidor foi bem-sucedida
        const updatedScreenshots = screenshots.filter(s => s.id !== screenshot.id)
        onScreenshotsUpdate(updatedScreenshots)

        toast({
          title: "Screenshot deletado",
          description: "Screenshot removido com sucesso"
        })
      } else {
        // Se retornou false ou null, significa que houve falha
        throw new Error('Falha ao deletar screenshot - screenshot não encontrado ou erro no servidor')
      }
    } catch (error) {
      console.error('❌ Erro ao deletar screenshot:', error)
      toast({
        title: "Erro ao deletar",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive"
      })
    }
  }, [screenshots, onScreenshotsUpdate, platform, toast])

  // Handlers de drag and drop
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelection(e.dataTransfer.files)
    }
  }, [handleFileSelection])

  // Handler do input file
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelection(e.target.files)
    }
  }, [handleFileSelection])

  const totalFiles = screenshots.length + pendingFiles.length

  // Modal Component usando Portal
  const ImageModal = () => {
    if (!viewingImage) return null

    return createPortal(
      <div 
        className="fixed inset-0 bg-black/95 z-[9999] flex items-center justify-center p-4"
        onClick={() => setViewingImage(null)}
        style={{ zIndex: 999999 }}
      >
        <div className="relative max-w-[95vw] max-h-[95vh] w-full h-full flex items-center justify-center">
          <img
            src={viewingImage}
            alt="Visualização"
            className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          />
          <Button
            size="sm"
            variant="secondary"
            className="absolute top-6 right-6 h-12 w-12 p-0 bg-white/90 hover:bg-white shadow-xl text-black text-xl"
            onClick={() => setViewingImage(null)}
          >
            ✕
          </Button>
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
            <p className="text-white text-sm bg-black/70 px-4 py-2 rounded-full backdrop-blur-sm">
              Clique fora da imagem para fechar
            </p>
          </div>
        </div>
      </div>,
      document.body
    )
  }

  return (
    <>
      <div className="space-y-4">
        {/* Área de upload minimalista */}
        <Card 
          className={`relative cursor-pointer transition-colors ${
            dragActive 
              ? 'border-[#ff0074] bg-[#ff0074]/5' 
              : 'border-dashed border-muted-foreground/25 hover:border-[#ff0074]/50'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleInputChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
          />
          
          <div className="p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-[#ff0074]/10 to-[#9810fa]/10 rounded-lg">
                <ImagePlus className="h-4 w-4 text-[#ff0074]" />
              </div>
              <div>
                <p className="text-sm font-medium">Adicionar Screenshots</p>
                <p className="text-xs text-muted-foreground">
                  {totalFiles} {totalFiles === 1 ? 'arquivo' : 'arquivos'}
                </p>
              </div>
            </div>
            
            <Button type="button" size="sm" variant="outline">
              <Upload className="h-3 w-3 mr-1" />
              Selecionar
            </Button>
          </div>
        </Card>

        {/* Lista de imagens simples */}
        {(screenshots.length > 0 || pendingFiles.length > 0) && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {/* Screenshots salvos */}
            {screenshots.map((screenshot) => (
              <div key={screenshot.id} className="relative group">
                <div 
                  className="aspect-square relative overflow-hidden rounded-lg border border-border cursor-pointer"
                  onClick={() => setViewingImage(screenshot.url)}
                >
                  <img
                    src={screenshot.url}
                    alt={`Screenshot ${screenshot.id}`}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <Eye className="h-6 w-6 text-white" />
                  </div>
                </div>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleRemoveScreenshot(screenshot)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}

            {/* Arquivos pendentes */}
            {pendingFiles.map((file) => (
              <div key={file.id} className="relative group">
                <div 
                  className="aspect-square relative overflow-hidden rounded-lg border border-border cursor-pointer"
                  onClick={() => setViewingImage(file.preview)}
                >
                  <img
                    src={file.preview}
                    alt={`Preview ${file.id}`}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <Eye className="h-6 w-6 text-white" />
                  </div>
                  <Badge 
                    variant="secondary" 
                    className="absolute bottom-2 left-2 text-xs bg-yellow-500/90 text-yellow-900"
                  >
                    Pendente
                  </Badge>
                </div>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleRemovePendingFile(file.id)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      <ImageModal />
    </>
  )
} 


