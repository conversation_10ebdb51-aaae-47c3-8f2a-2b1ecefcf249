import { NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { join } from 'path';

// Função para tratar exclusão de arquivos
export async function POST(request: Request) {
  try {
    const { filePath } = await request.json();
    
    if (!filePath) {
      return NextResponse.json(
        { error: 'Caminho do arquivo não fornecido' },
        { status: 400 }
      );
    }
    
    // Remover a parte inicial da URL para obter o caminho relativo
    // Por exemplo: /uploads/documentos/123/arquivo.pdf -> uploads/documentos/123/arquivo.pdf
    const relativePath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
    
    // Caminho completo para o arquivo no sistema de arquivos
    const fullPath = join(process.cwd(), 'public', relativePath);
    
    // Excluir o arquivo
    await unlink(fullPath);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Erro ao excluir arquivo:', error);
    return NextResponse.json(
      { error: 'Falha ao excluir arquivo' },
      { status: 500 }
    );
  }
}


