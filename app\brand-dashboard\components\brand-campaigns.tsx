"use client"

import { useState } from "react"
import { Brand, Influencer } from "@/types/brand-dashboard"
import { Card } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye, MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { InfluencerDetailsDialog } from "./influencer-details-dialog"
import { AddInfluencerDialog } from "./add-influencer-dialog"

interface BrandCampaignsProps {
  brand: Brand
}

const statusMap = {
  approved: { label: "Aprovado", color: "bg-green-500" },
  rejected: { label: "Reprovado", color: "bg-red-500" },
  analyzing: { label: "Em analise", color: "bg-yellow-500" },
  proposal_rejected: { label: "Proposta Recusada", color: "bg-gray-500" },
}

export default function BrandCampaigns({ brand }: BrandCampaignsProps) {
  const [selectedInfluencer, setSelectedInfluencer] = useState<Influencer | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [addInfluencerOpen, setAddInfluencerOpen] = useState(false)

  const handleAddInfluencer = (data: any) => {
    console.log("Novo influenciador:", data)
    // Aqui você implementaria a lógica para adicionar o influenciador
    setAddInfluencerOpen(false)
  }

  return (
    <>
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-2xl font-bold">{brand.name}</h3>
            <Button onClick={() => setAddInfluencerOpen(true)}>
              Nova Campanha
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Influenciador</TableHead>
                <TableHead>Redes Sociais</TableHead>
                <TableHead>Serviços</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Data da Proposta</TableHead>
                <TableHead>Mês da Campanha</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {brand.campaigns.flatMap((campaign) =>
                campaign.influencers.map((influencer) => (
                  <TableRow key={influencer.id}>
                    <TableCell>{influencer.name}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {influencer.socialNetworks.map((social) => (
                          <a
                            key={social.network}
                            href={social.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`text-sm ${
                              social.isPrimary ? "font-bold" : ""
                            }`}
                          >
                            @{social.username}
                          </a>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {influencer.services.map((service, index) => (
                          <div key={index} className="text-sm">
                            {service.type} - R${service.price}
                          </div>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={`${
                          statusMap[influencer.status].color
                        } text-white`}
                      >
                        {statusMap[influencer.status].label}
                      </Badge>
                    </TableCell>
                    <TableCell>{influencer.proposalDate}</TableCell>
                    <TableCell>{influencer.campaignMonth}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedInfluencer(influencer)
                            setDetailsOpen(true)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Ações</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>Editar</DropdownMenuItem>
                            <DropdownMenuItem>Ver Métricas</DropdownMenuItem>
                            <DropdownMenuItem>Histórico</DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600">
                              Remover
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>

      <InfluencerDetailsDialog
        influencer={selectedInfluencer}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
      />

      <AddInfluencerDialog
        open={addInfluencerOpen}
        onOpenChange={setAddInfluencerOpen}
        onSubmit={handleAddInfluencer}
      />
    </>
  )
}

