/**
 * 🔒 CONFIGURAÇÕES DE SEGURANÇA PARA FIREBASE
 * Este arquivo contém as configurações necessárias para resolver problemas de CSP com Firebase
 */

// ✅ URLs necessárias para Firebase Firestore
export const FIREBASE_CSP_DOMAINS = [
  // URLs básicas do Firebase
  'https://firebase.googleapis.com',
  'https://*.firebase.googleapis.com',
  'https://*.firebaseio.com',
  'wss://*.firebaseio.com',
  
  // URLs específicas do Firestore
  'https://firestore.googleapis.com',
  'https://*.firestore.googleapis.com',
  'wss://firestore.googleapis.com',
  'wss://*.firestore.googleapis.com',
  
  // URLs de autenticação
  'https://identitytoolkit.googleapis.com',
  'https://securetoken.googleapis.com',
  'https://firebaseinstallations.googleapis.com',
  
  // URLs de analytics (se usado)
  'https://www.google-analytics.com',
  'https://analytics.google.com',
  
  // URLs de fontes
  'https://fonts.googleapis.com',
  'https://fonts.gstatic.com'
];

// ✅ Content Security Policy completa para Firebase
export const FIREBASE_CSP = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-eval'",
    "'unsafe-inline'",
    "https://vercel.live",
    "https://www.googletagmanager.com",
    "https://www.google-analytics.com"
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com"
  ],
  'img-src': [
    "'self'",
    "data:",
    "https:",
    "blob:"
  ],
  'font-src': [
    "'self'",
    "data:",
    "https://fonts.gstatic.com"
  ],
  'connect-src': [
    "'self'",
    ...FIREBASE_CSP_DOMAINS
  ],
  'frame-src': ["'none'"]
};

// ✅ Função para gerar string de CSP
export function generateCSPString(): string {
  return Object.entries(FIREBASE_CSP)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ') + ';';
}

// ✅ Configurações específicas para desenvolvimento
export const DEV_FIREBASE_CONFIG = {
  emulators: {
    auth: { host: 'localhost', port: 9099 },
    firestore: { host: 'localhost', port: 8080 },
    storage: { host: 'localhost', port: 9199 }
  },
  additionalCSPSources: [
    'http://localhost:*',
    'ws://localhost:*'
  ]
};

// ✅ Função para verificar se Firebase está configurado corretamente
export function validateFirebaseConfig() {
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Variáveis de ambiente Firebase ausentes:', missing);
    return false;
  }
  
  console.log('✅ Configuração Firebase validada');
  return true;
}

// ✅ Headers de segurança recomendados para Firebase
export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Content-Security-Policy': generateCSPString()
};

// ✅ Função para aplicar headers de segurança
export function applySecurityHeaders(response: Response): Response {
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
} 

