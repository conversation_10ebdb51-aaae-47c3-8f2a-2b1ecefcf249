import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation, withCreateIsolation } from '@/lib/middleware/user-isolation';
import { clientDb as db } from '@/lib/firebase-client';
import { collection, doc, addDoc, getDocs, deleteDoc, getDoc, query, where, orderBy, Timestamp, updateDoc } from 'firebase/firestore';
import { getCategoryById } from '@/lib/firebase';
import { getFinancialByInfluencerId } from '@/lib/firebase-financials';

// Interface para influenciadores enviados para campanhas
interface BrandInfluencer {
  id?: string;
  userId: string; // ✅ Campo obrigatório para isolamento
  brandId: string;
  brandName: string;
  influencerId: string;
  status: 'enviado' | 'visualizado' | 'interessado' | 'rejeitado' | 'proposta_enviada';
  sentAt: Date;
  viewedAt?: Date;
  lastInteractionAt?: Date;
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

/**
 * GET - Buscar influenciadores enviados para uma marca específica
 * Query params: brandId (obrigatório)
 * FASE 4.2: Aplicação do middleware de isolamento por usuário
 */
export const GET = withUserIsolation(async (request: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(request.url);
    const brandId = searchParams.get('brandId');
    
    if (!brandId) {
      return NextResponse.json(
        { error: 'brandId é obrigatório' },
        { status: 400 }
      );
    }

    // ✅ CORREÇÃO: Buscar influenciadores com isolamento por userId
    const brandInfluencersRef = collection(db, 'brand_influencers');
    const q = query(
      brandInfluencersRef,
      where('userId', '==', userId), // ✅ Filtro obrigatório para isolamento
      where('brandId', '==', brandId),
      orderBy('sentAt', 'desc')
    );
    
    const snapshot = await getDocs(q);
    const brandInfluencers: any[] = [];
    
    // Para cada relacionamento, buscar os dados completos do influenciador
    for (const docSnapshot of snapshot.docs) {
      const data = docSnapshot.data();
      
      // Buscar dados completos do influenciador
      const influencerRef = doc(db, 'influencers', data.influencerId);
      const influencerDoc = await getDoc(influencerRef);
      
      if (influencerDoc.exists()) {
        const rawInfluencerData = influencerDoc.data();
        
        // Buscar dados das categorias principais
        let mainCategoriesData = [];
        if (rawInfluencerData.mainCategories && Array.isArray(rawInfluencerData.mainCategories)) {
          for (const categoryId of rawInfluencerData.mainCategories) {
            try {
              const categoryData = await getCategoryById(categoryId);
              if (categoryData) {
                mainCategoriesData.push({
                  id: categoryData.id,
                  name: categoryData.name,
                  slug: categoryData.slug,
                  color: categoryData.color
                });
              }
            } catch (error) {
              console.warn(`Erro ao buscar categoria ${categoryId}:`, error);
            }
          }
        }
        
        // Buscar dados financeiros do influenciador
        let financialData = null;
        try {
          financialData = await getFinancialByInfluencerId(data.influencerId);
        } catch (error) {
          console.warn(`Erro ao buscar dados financeiros para influenciador ${data.influencerId}:`, error);
        }
        
        // Mapear dados do Firebase para a estrutura esperada pelo dashboard
        const influencerData = {
          id: influencerDoc.id,
          nome: rawInfluencerData.name || rawInfluencerData.nome || 'Nome não encontrado',
          verified: rawInfluencerData.verified || false,
          pais: rawInfluencerData.country || rawInfluencerData.pais || 'Brasil',
          cidade: rawInfluencerData.city || rawInfluencerData.cidade || '',
          estado: rawInfluencerData.state || rawInfluencerData.estado || '',
          idade: rawInfluencerData.age || rawInfluencerData.idade || 0,
          categoria: rawInfluencerData.mainCategories && rawInfluencerData.mainCategories.length > 0 
            ? (Array.isArray(rawInfluencerData.mainCategories) 
                ? rawInfluencerData.mainCategories[0] 
                : rawInfluencerData.mainCategories)
            : rawInfluencerData.category || rawInfluencerData.categoria || '',
          mainCategories: rawInfluencerData.mainCategories || [],
          mainCategoriesData: mainCategoriesData,
          divulgaTrader: rawInfluencerData.promotes_traders || rawInfluencerData.divulgaTrader || false,
          genero: rawInfluencerData.gender || rawInfluencerData.genero || 'Outro',
          whatsapp: rawInfluencerData.whatsapp || '',
          redesSociais: {
            instagram: rawInfluencerData.socialNetworks?.instagram?.username ? {
              username: rawInfluencerData.socialNetworks.instagram.username,
              seguidores: rawInfluencerData.socialNetworks.instagram.followers || 0,
              engajamento: rawInfluencerData.socialNetworks.instagram.avgViews || 0
            } : undefined,
            youtube: rawInfluencerData.socialNetworks?.youtube?.username ? {
              username: rawInfluencerData.socialNetworks.youtube.username,
              seguidores: rawInfluencerData.socialNetworks.youtube.followers || 0,
              visualizacoes: rawInfluencerData.socialNetworks.youtube.avgViews || 0
            } : undefined,
            tiktok: rawInfluencerData.socialNetworks?.tiktok?.username ? {
              username: rawInfluencerData.socialNetworks.tiktok.username,
              seguidores: rawInfluencerData.socialNetworks.tiktok.followers || 0,
              curtidas: rawInfluencerData.socialNetworks.tiktok.avgViews || 0
            } : undefined
          },
          servicos: {
            postFeed: rawInfluencerData.instagram_reel_price || 0,
            stories: rawInfluencerData.instagram_story_price || 0,
            reels: rawInfluencerData.instagram_reel_price || 0,
            videoYoutube: rawInfluencerData.youtube_dedicated_price || 0,
            videoTiktok: rawInfluencerData.tiktok_price || 0
          },
          avatar: rawInfluencerData.avatar || '',
          // Campos de visualizações específicas do Firebase
          stories_views: rawInfluencerData.stories_views || rawInfluencerData.instagram_stories_views || 0,
          instagram_reels_views: rawInfluencerData.instagram_reels_views || rawInfluencerData.reels_views || 0,
          youtube_long_video_views: rawInfluencerData.youtube_long_video_views || rawInfluencerData.youtube_dedicated_views || 0,
          youtube_shorts_views: rawInfluencerData.youtube_shorts_views || rawInfluencerData.youtube_short_views || 0,
          tiktok_views: rawInfluencerData.tiktok_views || rawInfluencerData.tiktok_video_views || 0,
          // Dados de audiência - gênero e faixas etárias
          audienceGender: rawInfluencerData.audienceGender || {
            instagram: { male: 0, female: 0, other: 0 },
            youtube: { male: 0, female: 0, other: 0 },
            tiktok: { male: 0, female: 0, other: 0 },
            facebook: { male: 0, female: 0, other: 0 },
            twitch: { male: 0, female: 0, other: 0 },
            kwai: { male: 0, female: 0, other: 0 }
          },
          audienceAgeRanges: rawInfluencerData.audienceAgeRanges || {
            instagram: [],
            youtube: [],
            tiktok: [],
            facebook: [],
            twitch: [],
            kwai: []
          },
          // Dados financeiros adicionais
          dadosFinanceiros: financialData ? {
            responsavel: financialData.responsibleName || '',
            agencia: financialData.agencyName || '',
            emailFinanceiro: financialData.email || '',
            whatsappFinanceiro: financialData.whatsapp || '',
            visualizacoesStories: financialData.instagramStoriesViews || 0,
            precos: financialData.prices || null
          } : null
        };
        
        brandInfluencers.push({
          id: docSnapshot.id,
          brandId: data.brandId,
          brandName: data.brandName,
          influencerId: data.influencerId,
          influencerName: influencerData.nome,
          influencerData,
          status: data.status,
          sentAt: data.sentAt?.toDate() || new Date(),
          viewedAt: data.viewedAt?.toDate(),
          lastInteractionAt: data.lastInteractionAt?.toDate(),
          notes: data.notes,
          createdBy: data.createdBy,
          updatedAt: data.updatedAt?.toDate() || new Date()
        });
      } else {
        console.warn(`Influenciador ${data.influencerId} não encontrado`);
        // Ainda assim incluir o relacionamento, mas sem dados do influenciador
        brandInfluencers.push({
          id: docSnapshot.id,
          brandId: data.brandId,
          brandName: data.brandName,
          influencerId: data.influencerId,
          influencerName: 'Influenciador não encontrado',
          influencerData: null,
          status: data.status,
          sentAt: data.sentAt?.toDate() || new Date(),
          viewedAt: data.viewedAt?.toDate(),
          lastInteractionAt: data.lastInteractionAt?.toDate(),
          notes: data.notes,
          createdBy: data.createdBy,
          updatedAt: data.updatedAt?.toDate() || new Date()
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: brandInfluencers,
      count: brandInfluencers.length
    });
    
  } catch (error) {
    console.error('Erro ao buscar influenciadores da marca:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

/**
 * POST - Enviar influenciadores para uma marca
 * Body: { brandId, brandName, influencers: [{ id, data }], createdBy }
 * FASE 4.2: Aplicação do middleware de isolamento por usuário
 */
export const POST = withCreateIsolation(async (request: NextRequest, userId: string, context, data) => {
  try {
    // ✅ Dados já processados pelo middleware incluindo userId
    const { brandId, brandName, influencerIds, createdBy } = data;
    
    if (!brandId || !brandName || !influencerIds || !Array.isArray(influencerIds) || !createdBy) {
      return NextResponse.json(
        { error: 'Campos obrigatórios: brandId, brandName, influencerIds (array), createdBy' },
        { status: 400 }
      );
    }

    const brandInfluencersRef = collection(db, 'brand_influencers');
    const influencersRef = collection(db, 'influencers');
    const results = [];
    
    // Processar cada influenciador
    for (const influencerId of influencerIds) {
      if (!influencerId) {
        continue; // Pular IDs inválidos
      }
      
      // Verificar se o influenciador existe antes de criar o relacionamento
      const influencerDoc = await getDoc(doc(influencersRef, influencerId));
      
      if (!influencerDoc.exists()) {
        console.warn(`Influenciador ${influencerId} não encontrado`);
        results.push({
          influencerId,
          status: 'not_found',
          error: 'Influenciador não encontrado'
        });
        continue;
      }
      
      // Verificar se já existe esse relacionamento
      const existingQuery = query(
        brandInfluencersRef,
        where('brandId', '==', brandId),
        where('influencerId', '==', influencerId)
      );
      
      const existingSnapshot = await getDocs(existingQuery);
      
      if (existingSnapshot.empty) {
        // ✅ Criar novo relacionamento com isolamento por userId
        const brandInfluencerData: Omit<BrandInfluencer, 'id'> = {
          userId, // ✅ Incluir userId para isolamento
          brandId,
          brandName,
          influencerId,
          status: 'enviado',
          sentAt: new Date(),
          createdBy,
          updatedAt: new Date()
        };
        
        const docRef = await addDoc(brandInfluencersRef, {
          ...brandInfluencerData,
          sentAt: Timestamp.fromDate(brandInfluencerData.sentAt),
          updatedAt: Timestamp.fromDate(brandInfluencerData.updatedAt)
        });
        
        results.push({
          influencerId,
          documentId: docRef.id,
          status: 'created'
        });
      } else {
        // Relacionamento já existe - atualizar timestamp de última interação
        const existingDocRef = doc(db, 'brand_influencers', existingSnapshot.docs[0].id);
        await updateDoc(existingDocRef, {
          lastInteractionAt: Timestamp.fromDate(new Date()),
          updatedAt: Timestamp.fromDate(new Date())
        });
        
        results.push({
          influencerId,
          documentId: existingSnapshot.docs[0].id,
          status: 'updated_existing'
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `${results.filter(r => r.status === 'created').length} influenciadores enviados para ${brandName}`,
      data: results
    });
    
  } catch (error) {
    console.error('Erro ao enviar influenciadores para marca:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

/**
 * PUT - Atualizar status de um influenciador enviado para marca
 * Body: { documentId, status, notes?, viewedAt?, lastInteractionAt? }
 * FASE 4.2: Aplicação do middleware de isolamento por usuário
 */
export const PUT = withUserIsolation(async (request: NextRequest, userId: string) => {
  try {
    const body = await request.json();
    const { documentId, brandId, influencerId, status, notes, viewedAt, lastInteractionAt } = body;
    
    if (!status) {
      return NextResponse.json(
        { error: 'Campo obrigatório: status' },
        { status: 400 }
      );
    }

    const validStatuses = ['enviado', 'visualizado', 'interessado', 'rejeitado', 'proposta_enviada'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Status inválido. Valores aceitos: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    let docRef;
    
    // Se documentId foi fornecido, usar diretamente
    if (documentId) {
      docRef = doc(db, 'brand_influencers', documentId);
    } 
    // Caso contrário, buscar pelo brandId e influencerId
    else if (brandId && influencerId) {
      const brandInfluencersRef = collection(db, 'brand_influencers');
      const q = query(
        brandInfluencersRef,
        where('brandId', '==', brandId),
        where('influencerId', '==', influencerId)
      );
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return NextResponse.json(
          { error: 'Relacionamento brand-influencer não encontrado' },
          { status: 404 }
        );
      }
      
      // Usar o primeiro documento encontrado
      docRef = snapshot.docs[0].ref;
    } else {
      return NextResponse.json(
        { error: 'Forneça documentId OU (brandId E influencerId)' },
        { status: 400 }
      );
    }
    
    const updateData: any = {
      status,
      updatedAt: Timestamp.fromDate(new Date())
    };
    
    if (notes !== undefined) updateData.notes = notes;
    if (viewedAt) updateData.viewedAt = Timestamp.fromDate(new Date(viewedAt));
    if (lastInteractionAt) updateData.lastInteractionAt = Timestamp.fromDate(new Date(lastInteractionAt));
    
    await updateDoc(docRef, updateData);

    return NextResponse.json({
      success: true,
      message: 'Status atualizado com sucesso'
    });
    
  } catch (error) {
    console.error('Erro ao atualizar status:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

/**
 * DELETE - Remover influenciador da lista de uma marca
 * Query params: documentId (obrigatório)
 * FASE 4.2: Aplicação do middleware de isolamento por usuário
 */
export const DELETE = withUserIsolation(async (request: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(request.url);
    const documentId = searchParams.get('documentId');
    
    if (!documentId) {
      return NextResponse.json(
        { error: 'documentId é obrigatório' },
        { status: 400 }
      );
    }

    const docRef = doc(db, 'brand_influencers', documentId);
    await deleteDoc(docRef);

    return NextResponse.json({
      success: true,
      message: 'Influenciador removido da marca com sucesso'
    });
    
  } catch (error) {
    console.error('Erro ao remover influenciador da marca:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

