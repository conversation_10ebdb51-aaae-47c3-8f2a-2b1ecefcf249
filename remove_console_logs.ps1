# Script para remover console.log statements
$filePath = "app\[userId]\influencers\page.tsx"

# Ler conteúdo do arquivo linha por linha
$lines = Get-Content $filePath

# Filtrar linhas que não contêm console statements
$filteredLines = $lines | Where-Object { 
    $_ -notmatch "^\s*console\.(log|error|warn|info|debug)\(" -and
    $_ -notmatch "console\.(log|error|warn|info|debug)\([^;]*\);"
}

# Salvar arquivo
$filteredLines | Set-Content $filePath

Write-Host "Console logs removidos de $filePath" 