import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { ProposalService } from '@/services/proposal-service';
import { SnapshotFilterService } from '@/lib/snapshot-filter-service';
import { Proposal, ProposalFilters } from '@/types/proposal';
import { cookies } from 'next/headers';
import { db } from '@/lib/firebase-admin';

// ⚡ OTIMIZAÇÃO: Cache em memória para propostas
const proposalCache = new Map<string, { data: any, timestamp: number }>();
const CACHE_TTL = 30000; // 30 segundos
const USER_CACHE = new Map<string, { data: any, timestamp: number }>();
const USER_CACHE_TTL = 60000; // 1 minuto

// ⚡ OTIMIZAÇÃO: Função de cache com TTL
function getCachedData(key: string, cache: Map<string, { data: any, timestamp: number }>, ttl: number) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }
  return null;
}

function setCachedData(key: string, data: any, cache: Map<string, { data: any, timestamp: number }>) {
  cache.set(key, { data, timestamp: Date.now() });
  
  // ⚡ LIMPEZA: Remover entradas antigas do cache
  if (cache.size > 100) {
    const entries = Array.from(cache.entries());
    entries.slice(0, 50).forEach(([k]) => cache.delete(k));
  }
}

// Função otimizada para extrair dados do usuário do token JWT
async function getUserFromToken(token: string): Promise<{ userId: string; role: string } | null> {
  const cacheKey = `token_${token.slice(-10)}`;
  const cached = getCachedData(cacheKey, USER_CACHE, USER_CACHE_TTL);
  if (cached) return cached;
  
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
    const result = {
      userId: payload.userId,
      role: payload.role
    };
    
    setCachedData(cacheKey, result, USER_CACHE);
    return result;
  } catch {
    return null;
  }
}

// Função server-side para buscar proposta por ID usando Firebase Admin
async function getProposalByIdServerSide(proposalId: string): Promise<any | null> {
  try {
    console.log(`🔍 [SERVER] Buscando proposta por ID (Firebase Admin): ${proposalId}`);
    
    if (!proposalId || proposalId.trim() === '') {
      console.warn(`⚠️ [SERVER] ID inválido fornecido: "${proposalId}"`);
      return null;
    }

    const proposalRef = db.collection('proposals').doc(proposalId.trim());
    const proposalDoc = await proposalRef.get();

    if (!proposalDoc.exists) {
      console.warn(`❌ [SERVER] Proposta não encontrada: ${proposalId}`);
      return null;
    }

    const data = proposalDoc.data();
    console.log(`✅ [SERVER] Proposta encontrada: ${proposalId} (${data?.nome || 'sem nome'})`);
    
    return {
      id: proposalDoc.id,
      ...data,
      createdAt: data?.createdAt?.toDate() || new Date(),
      updatedAt: data?.updatedAt?.toDate() || new Date(),
      sentAt: data?.sentAt?.toDate(),
    };
  } catch (error) {
    console.error(`❌ [SERVER] Erro ao buscar proposta ${proposalId}:`, {
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined,
      proposalId
    });
    return null;
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { searchParams } = new URL(request.url);
    
    // ⚡ OTIMIZAÇÃO: Verificar cache primeiro
    const cacheKey = `${request.url}_${request.headers.get('X-User-ID') || ''}`;
    const cachedResponse = getCachedData(cacheKey, proposalCache, CACHE_TTL);
    if (cachedResponse) {
      return NextResponse.json({
        ...cachedResponse,
        cached: true,
        processingTime: Date.now() - startTime
      });
    }
    
    // 🔒 Verificação de autenticação otimizada
    const [cookieStore, xUserId] = await Promise.all([
      cookies(),
      Promise.resolve(request.headers.get('X-User-ID'))
    ]);
    
    const token = cookieStore.get('brand-session')?.value;
    
    let userId: string | undefined;
    let userRole: string = 'user';
    let userProposalAccess: Record<string, string> = {};
    
    // 🆕 PRIORIDADE 1: Tentar Clerk Auth com otimização
    try {
      const { userId: clerkUserId } = await auth();
      if (clerkUserId) {
        userId = clerkUserId;
        userRole = 'admin';
        
        // ⚡ OTIMIZAÇÃO: Buscar metadata de forma paralela
        const userCacheKey = `clerk_user_${clerkUserId}`;
        let user = getCachedData(userCacheKey, USER_CACHE, USER_CACHE_TTL);
        
        if (!user) {
        const clerk = await clerkClient();
          user = await clerk.users.getUser(clerkUserId);
          setCachedData(userCacheKey, user, USER_CACHE);
        }
        
        userProposalAccess = (user.unsafeMetadata as any)?.p || {};
      }
    } catch (clerkError) {
      // Silenciar logs para melhor performance
    }
    
    // Fallback para WorkOS/legacy se Clerk não funcionou
    if (!userId) {
      if (xUserId) {
        // 🔄 MIGRAÇÃO: Usar X-User-ID para WorkOS (temporário)
        userId = xUserId;
        userRole = 'admin'; // Assumir admin temporariamente para WorkOS users
        console.log('🔒 [AUTH] Usando WorkOS X-User-ID:', userId);
      } else if (token) {
        // 🔄 MIGRAÇÃO: Fallback para cookie legacy
        const user = await getUserFromToken(token);
        if (!user) {
          return NextResponse.json({
            success: false,
            error: 'Invalid token'
          }, { status: 401 });
        }
        userId = user.userId;
        userRole = user.role;
        console.log('🔒 [AUTH] Usando cookie legacy:', userId);
             } else {
         return NextResponse.json({
           success: false,
           error: 'Authentication required'
         }, { status: 401 });
       }
     }
     
     // Verificação final do userId
     if (!userId) {
       return NextResponse.json({
         success: false,
         error: 'Authentication required'
       }, { status: 401 });
     }
     
     // Parse filters from query parameters
    const brandId = searchParams.get('brandId');
    const status = searchParams.get('status')?.split(',');
    const priority = searchParams.get('priority')?.split(',');
    const grupo = searchParams.get('grupo');
    
    const filters = {
      brandId,
      status,
      priority,
      grupo
    };

    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100;
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0;

    // ⚡ OTIMIZAÇÃO: Busca paralela de propostas
    const [proposals] = await Promise.all([
      ProposalService.getAllUserProposalsSimple(userId, limit, offset)
    ]);
    
    // ⚡ OTIMIZAÇÃO: Busca paralela de propostas adicionais do Clerk
    if (Object.keys(userProposalAccess).length > 0) {
      const additionalProposalIds = Object.keys(userProposalAccess).filter(id => id && id.trim() !== '');
      
      // 🚀 BUSCA PARALELA: Buscar todas as propostas adicionais em paralelo
      const additionalProposalsPromises = additionalProposalIds.map(async (proposalId) => {
        try {
          const proposal = await getProposalByIdServerSide(proposalId.trim());
          return proposal ? { proposal, id: proposalId } : null;
        } catch {
          return null;
          }
      });
      
      const additionalResults = await Promise.allSettled(additionalProposalsPromises);
      
      // Processar resultados e evitar duplicatas
      additionalResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value?.proposal) {
          const { proposal, id } = result.value;
          const exists = proposals.find(p => p.id === id);
            if (!exists) {
            proposals.push(proposal);
          }
        }
      });
            }

    // ⚡ OTIMIZAÇÃO: Preparar resposta baseada no role do usuário
    let responseData;
    const processingTime = Date.now() - startTime;
    
    if (userRole === 'user') {
      // 🔒 Dados filtrados para usuários básicos
      const filteredProposals = proposals.map(proposal => ({
        id: proposal.id,
        nome: proposal.nome,
        descricao: proposal.descricao,
        status: proposal.status,
        priority: proposal.priority,
        dataEnvio: proposal.dataEnvio,
        createdAt: proposal.createdAt,
        updatedAt: proposal.updatedAt,
        influencers: proposal.influencers?.map(inf => ({
          id: inf.id,
          name: inf.name,
          avatar: inf.avatar,
          tier: inf.tier,
          followers: inf.followers,
          engagement: inf.engagement
        })) || [],
        influencerCount: proposal.influencers?.length || 0,
        serviceCount: proposal.services?.length || 0
      }));
      
      responseData = {
        success: true,
        data: filteredProposals,
        metadata: {
          total: filteredProposals.length,
          accessLevel: 'basic',
          userRole: userRole,
          filteredFields: SnapshotFilterService.getFilteredFieldsForRole(userRole),
          clerkProposalAccess: userProposalAccess,
          hasClerkAccess: Object.keys(userProposalAccess).length > 0,
          processingTime,
          cached: false
        }
      };
    } else {
      // 🔓 Dados completos para admins/managers
      responseData = {
      success: true,
      data: proposals,
      metadata: {
        total: proposals.length,
        accessLevel: 'complete',
        userRole: userRole,
        filteredFields: [],
          clerkProposalAccess: userProposalAccess,
          hasClerkAccess: Object.keys(userProposalAccess).length > 0,
          processingTime,
          cached: false
        }
      };
    }

    // ⚡ CACHE: Armazenar resposta no cache
    setCachedData(cacheKey, responseData, proposalCache);
    
    // 🚀 HEADERS DE PERFORMANCE: Adicionar headers de otimização
    const response = NextResponse.json(responseData);
    response.headers.set('Cache-Control', 'public, max-age=30, s-maxage=30');
    response.headers.set('X-Processing-Time', processingTime.toString());
    response.headers.set('X-Cache-Status', 'MISS');
    
    return response;
  } catch (error) {
    console.error('❌ [API] Erro ao buscar propostas:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch proposals'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // ✅ Validar apenas campos obrigatórios básicos
    // Influenciadores serão adicionados via subcoleção separadamente
    if (!body.brandId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required field: brandId'
        },
        { status: 400 }
      );
    }

    if (!body.nome || body.nome.trim() === '') {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required field: nome (proposal name)'
        },
        { status: 400 }
      );
    }

    // ✅ Criar dados da proposta com influencers vazio (será preenchido via subcoleção)
    const proposalData = {
      nome: body.nome.trim(),
      descricao: body.descricao || '',
      criadoPor: body.criadoPor || 'Sistema',
      influencers: [], // ✅ Array vazio - influenciadores serão adicionados via subcoleção
      brandId: body.brandId,
      services: body.services || [],
      totalAmount: body.totalAmount || 0,
      dataEnvio: body.dataEnvio || new Date().toISOString().split('T')[0],
      grupo: body.grupo || '',
      priority: body.priority || 'medium'
    };

    console.log('🚀 [API] Criando proposta:', {
      nome: proposalData.nome,
      brandId: proposalData.brandId,
      criadoPor: proposalData.criadoPor
    });

    const proposalId = await ProposalService.createProposal(proposalData);
    
    console.log('✅ [API] Proposta criada com sucesso:', proposalId);
    
    // ✅ Retornar formato esperado pelo frontend
    return NextResponse.json({
      success: true,
      id: proposalId, // ✅ ID diretamente no objeto raiz
      message: 'Proposta criada com sucesso'
    });
  } catch (error) {
    console.error('❌ [API] Erro ao criar proposta:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create proposal',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

