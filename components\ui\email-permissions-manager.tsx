'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useToast } from '@/hooks/use-toast';
import { Users, Plus, Mail, X } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth-v2';

interface ProposalCollaborator {
  userId: string;
  userEmail: string;
  userName: string;
  role: 'editor' | 'viewer';
  addedBy: string;
  addedAt: any;
  status: 'active' | 'pending' | 'revoked';
}

interface EmailPermissionsManagerProps {
  proposalId: string;
  proposalName?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EmailPermissionsManager({ 
  proposalId, 
  proposalName = 'proposta',
  open,
  onOpenChange
}: EmailPermissionsManagerProps) {
  const { firebaseUser } = useAuth();
  const { toast } = useToast();
  
  const [collaborators, setCollaborators] = useState<ProposalCollaborator[]>([]);
  const [loading, setLoading] = useState(false);
  const [addingCollaborator, setAddingCollaborator] = useState(false);
  
  // Formulário para adicionar colaborador
  const [newEmail, setNewEmail] = useState('');
  const [newName, setNewName] = useState('');
  const [newRole, setNewRole] = useState<'editor' | 'viewer'>('viewer');

  // Carregar colaboradores da proposta
  const loadCollaborators = async () => {
    if (!proposalId || !firebaseUser) return;
    
    try {
      setLoading(true);
      const token = await firebaseUser.getIdToken();
      
      const response = await fetch(`/api/proposals/${proposalId}/collaborators`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCollaborators(data.data?.collaborators || []);
      } else {
        throw new Error('Erro ao carregar colaboradores');
      }
    } catch (error) {
      console.error('Erro ao carregar colaboradores:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os colaboradores",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Adicionar novo colaborador
  const handleAddCollaborator = async () => {
    if (!newEmail || !newName || !firebaseUser) return;

    try {
      setAddingCollaborator(true);
      const token = await firebaseUser.getIdToken();

      const response = await fetch(`/api/proposals/${proposalId}/collaborators`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userEmail: newEmail,
          userName: newName,
          role: newRole
        })
      });

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: "Colaborador adicionado com sucesso",
        });
        
        // Limpar formulário
        setNewEmail('');
        setNewName('');
        setNewRole('viewer');
        
        // Recarregar lista
        loadCollaborators();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao adicionar colaborador');
      }
    } catch (error) {
      console.error('Erro ao adicionar colaborador:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao adicionar colaborador",
        variant: "destructive",
      });
    } finally {
      setAddingCollaborator(false);
    }
  };

  // Carregar colaboradores quando o sheet abrir
  useEffect(() => {
    if (open) {
      loadCollaborators();
    }
  }, [open, proposalId, firebaseUser]);

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'editor': return 'default';
      case 'viewer': return 'secondary';
      default: return 'outline';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'editor': return 'Editor';
      case 'viewer': return 'Visualizador';
      default: return role;
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[400px] sm:w-[600px] max-w-[90vw] overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-[#ff0074]" />
            Gerenciar Colaboradores
          </SheetTitle>
          <SheetDescription>
            Gerencie as permissões de acesso para {proposalName}
          </SheetDescription>
        </SheetHeader>
        
        <div className="mt-6 space-y-6">
          {/* Formulário para adicionar colaborador */}
                      <div className="p-4 border rounded-lg space-y-4 bg-gray-50 dark:bg-[#080210]">
            <div className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              <h4 className="font-medium">Adicionar Colaborador</h4>
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="name">Nome *</Label>
                <Input
                  id="name"
                  placeholder="Nome do usuário"
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="role">Nível de Permissão</Label>
                <Select value={newRole} onValueChange={(value: 'editor' | 'viewer') => setNewRole(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="viewer">
                      <div className="flex flex-col items-start">
                        <span className="font-medium">Visualizador</span>
                        <span className="text-xs text-muted-foreground">Pode visualizar a proposta</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="editor">
                      <div className="flex flex-col items-start">
                        <span className="font-medium">Editor</span>
                        <span className="text-xs text-muted-foreground">Pode editar e fazer contrapropostas</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button 
              onClick={handleAddCollaborator}
              disabled={!newEmail || !newName || addingCollaborator}
              className="w-full bg-gradient-to-r from-[#ff0074] to-[#9810fa] hover:from-[#ff0074]/90 hover:to-[#9810fa]/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              {addingCollaborator ? 'Adicionando...' : 'Adicionar Colaborador'}
            </Button>
          </div>

          {/* Lista de colaboradores */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <h4 className="font-medium">Colaboradores Atuais</h4>
              {collaborators.length > 0 && (
                <Badge variant="secondary" className="ml-auto">
                  {collaborators.length} colaborador{collaborators.length !== 1 ? 'es' : ''}
                </Badge>
              )}
            </div>
            
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff0074] mx-auto"></div>
                <p className="mt-2 text-sm text-muted-foreground">Carregando colaboradores...</p>
              </div>
            ) : collaborators.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Nenhum colaborador</p>
                <p className="text-sm">Adicione colaboradores para compartilhar esta proposta</p>
              </div>
            ) : (
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Colaborador</TableHead>
                      <TableHead>Permissão</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Adicionado em</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {collaborators.map((collaborator, index) => (
                      <TableRow key={`${collaborator.userEmail}-${index}`}>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium">{collaborator.userName}</p>
                            <p className="text-sm text-muted-foreground">{collaborator.userEmail}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRoleBadgeVariant(collaborator.role)}>
                            {getRoleLabel(collaborator.role)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={collaborator.status === 'active' ? 'default' : 'secondary'}>
                            {collaborator.status === 'active' ? 'Ativo' : 'Pendente'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {collaborator.addedAt?.toDate ? 
                            collaborator.addedAt.toDate().toLocaleDateString('pt-BR') : 
                            'N/A'
                          }
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>

          {/* Footer com botão de fechar */}
          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              <X className="h-4 w-4 mr-2" />
              Fechar
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
} 


