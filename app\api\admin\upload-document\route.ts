import { NextResponse } from 'next/server';
import { randomUUID } from 'crypto';
import { getFinancialByInfluencerId, updateFinancial } from '@/lib/firebase-financials';
import { uploadDocument } from '@/lib/firebase-storage';
import { db } from '@/lib/firebase';

// Função para tratar upload de documentos de influenciadores
export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const influencerId = formData.get('influencerId') as string;
    // 🆕 NOVO: Receber proposalId para determinar se deve salvar na estrutura hierárquica
    const proposalId = formData.get('proposalId') as string;
    
    if (!file) {
      return NextResponse.json(
        { error: 'Nenhum arquivo enviado' },
        { status: 400 }
      );
    }
    
    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador não fornecido' },
        { status: 400 }
      );
    }
    
    // Validar tipo de arquivo (documentos permitidos)
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Tipo de arquivo não permitido. Use PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, JPEG ou PNG.' },
        { status: 400 }
      );
    }
    
    // Validar tamanho do arquivo (máximo 40MB)
    const maxSize = 40 * 1024 * 1024; // 40MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Arquivo muito grande. Máximo permitido: 40MB' },
        { status: 400 }
      );
    }

    console.log(`📄 [UPLOAD] Fazendo upload do documento ${file.name} para Firebase Storage`);
    console.log(`📄 [UPLOAD] Contexto: influencer=${influencerId}, proposal=${proposalId || 'independente'}`);
    
    // Fazer upload do documento para Firebase Storage
    const publicUrl = await uploadDocument(file, influencerId);

    console.log(`📄 [UPLOAD] Upload concluído. URL: ${publicUrl}`);
    
    // Criar objeto do documento
    const newDocument = {
      id: randomUUID(),
      name: file.name,
      url: publicUrl,
      type: file.name.split('.').pop() || '',
      size: file.size,
      uploadedAt: new Date(),
      uploadedBy: 'user', // TODO: Pegar do contexto de autenticação
      influencerId: influencerId,
      proposalId: proposalId || null
    };
    
    // 🆕 ESTRUTURA HIERÁRQUICA: Se há proposalId, salvar na subcoleção
    if (proposalId) {
      console.log('📄 [HIERARCHICAL] Salvando documento na subcoleção hierárquica:', {
        proposalId,
        influencerId,
        documentName: file.name
      });

      // Verificar se a proposta existe e se o usuário tem acesso
      const proposalDoc = await db.collection('proposals').doc(proposalId).get();
      if (!proposalDoc.exists) {
        return NextResponse.json(
          { error: 'Proposta não encontrada' },
          { status: 404 }
        );
      }

      // Salvar na estrutura: proposals/{proposalId}/influencers/{influencerId}/documents/{documentId}
      const documentRef = db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('documents')
        .doc(newDocument.id);

      await documentRef.set(newDocument);

      console.log('✅ [HIERARCHICAL] Documento salvo na subcoleção hierárquica com sucesso');

      return NextResponse.json({
        success: true,
        url: publicUrl,
        document: newDocument,
        structure: 'hierarchical',
        message: 'Documento enviado e salvo na proposta com sucesso'
      });
    } 
    
    // 🔄 ESTRUTURA INDEPENDENTE: Salvar na coleção influencer_financials (comportamento atual)
    else {
      console.log('📄 [INDEPENDENT] Salvando documento na estrutura independente (influencer_financials)');
      
      // Buscar registro financeiro do influenciador
      let financialRecord = await getFinancialByInfluencerId(influencerId);
      
      if (financialRecord) {
        // Atualizar registro existente
        console.log('📄 [INDEPENDENT] Atualizando registro financeiro existente');
        
        // Obter documentos existentes ou inicializar array vazio
        const existingDocuments = financialRecord.additionalData?.documents || [];
        
        // Adicionar novo documento à lista
        const updatedDocuments = [...existingDocuments, newDocument];
        
        // Atualizar registro no banco de dados
        await updateFinancial(financialRecord.id, {
          additionalData: {
            ...financialRecord.additionalData,
            documents: updatedDocuments
          }
        });
        
        return NextResponse.json({
          success: true,
          url: publicUrl,
          document: newDocument,
          structure: 'independent',
          message: 'Documento enviado e registro atualizado com sucesso'
        });
      } else {
        // Criar novo registro financeiro
        console.log('📄 [INDEPENDENT] Criando novo registro financeiro para o influenciador');
        
        // Importar função para adicionar registro financeiro
        const { addFinancial } = await import('@/lib/firebase-financials');
        
        // Criar novo registro com o documento
        const newFinancialId = await addFinancial({
          influencerId,
          additionalData: {
            documents: [newDocument]
          }
        });
        
        return NextResponse.json({
          success: true,
          url: publicUrl,
          document: newDocument,
          structure: 'independent',
          financialId: newFinancialId,
          message: 'Documento enviado e novo registro financeiro criado'
        });
      }
    }
  } catch (error) {
    console.error('❌ [UPLOAD] Erro no upload de documento:', error);
    return NextResponse.json(
      { 
        error: 'Falha ao fazer upload do documento',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}


