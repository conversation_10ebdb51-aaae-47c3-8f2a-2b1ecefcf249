import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export interface AuthenticatedUser {
  uid: string;
  email: string | undefined;
  email_verified: boolean;
  clerkUserId?: string;
}

// ✅ FUNÇÃO ATUALIZADA: Verificar autenticação do Clerk
export async function verifyClerkToken(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    console.log('🔒 [AUTH] Verificando autenticação Clerk...');
    
    // Usar a função auth do Clerk para verificar a sessão
    const { userId, sessionClaims } = auth();
    
    if (!userId) {
      console.warn('🔒 [AUTH] Usuário não autenticado no Clerk');
      return null;
    }

    // Extrair email do sessionClaims se disponível
    const email = sessionClaims?.email as string | undefined;
    const emailVerified = sessionClaims?.email_verified as boolean | undefined;

    console.log('✅ [AUTH] Autenticação Clerk verificada com sucesso:', {
      userId,
      email
    });

    return {
      uid: userId, // Usar ID do Clerk como UID
      clerkUserId: userId,
      email: email,
      email_verified: emailVerified || false
    };
  } catch (error) {
    console.error('❌ [AUTH] Erro ao verificar autenticação Clerk:', error);
    return null;
  }
}

// 🔄 MANTER COMPATIBILIDADE: Alias para a função antiga
export async function verifyFirebaseToken(request: NextRequest): Promise<AuthenticatedUser | null> {
  console.log('🔄 [AUTH] Redirecionando verifyFirebaseToken para verifyClerkToken');
  return verifyClerkToken(request);
}

export function createAuthMiddleware() {
  // ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
  return async (request: NextRequest): Promise<{ user: AuthenticatedUser | null; error?: string }> => {
    console.warn('createAuthMiddleware está desabilitado - use Clerk Auth');
    return {
      user: null,
      error: 'Middleware Firebase desabilitado - use Clerk Auth'
    };
  };
} 

