'use client';

import { useState, useEffect } from 'react';
import { useOrganizationList, useOrganization, useUser, OrganizationProfile } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ChevronDown, Check, Plus, Building2, Loader2, Crown, Users, Edit3, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function WorkspaceSwitcher() {
  const router = useRouter();
  const { user } = useUser();
  const { organization: currentOrganization } = useOrganization();
  const { 
    isLoaded, 
    setActive, 
    userMemberships,
    createOrganization 
  } = useOrganizationList({
    userMemberships: {
      infinite: true,
    },
  });
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newOrganizationName, setNewOrganizationName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [showOrganizationProfile, setShowOrganizationProfile] = useState(false);


  // Verificar se há organizações nos dados do Clerk
  const hasOrganizations = userMemberships?.data && userMemberships.data.length > 0;
  const organizations = userMemberships?.data || [];

  // Se não há organização ativa mas há organizações, selecionar a primeira
  useEffect(() => {
    if (isLoaded && !currentOrganization && hasOrganizations && setActive) {
      const firstOrg = organizations[0];
      if (firstOrg) {
        setActive({ organization: firstOrg.organization.id });
      }
    }
  }, [isLoaded, currentOrganization, hasOrganizations, organizations, setActive]);

  // Verificar se o usuário tem permissão de admin
  const isUserAdmin = () => {
    if (!user) return false;
    
    // Verificar se é admin da organização atual
    if (currentOrganization && userMemberships?.data) {
      const currentMembership = userMemberships.data.find(
        membership => membership.organization.id === currentOrganization.id
      );
      return currentMembership?.role === 'org:admin';
    }
    
    // Verificar se é admin de pelo menos uma organização
    if (userMemberships?.data) {
      return userMemberships.data.some(membership => membership.role === 'org:admin');
    }
    
    return false;
  };

  // Fechar OrganizationProfile quando pressionar Escape
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showOrganizationProfile) {
        setShowOrganizationProfile(false);
      }
    };

    if (showOrganizationProfile) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showOrganizationProfile]);

  // Se o usuário não é admin, não renderizar o componente (APÓS todos os hooks)
  if (!isUserAdmin()) {
    return null;
  }

  // Debug removido para produção

  const handleCreateOrganization = async () => {
    if (!newOrganizationName.trim() || !createOrganization) return;

    setIsCreating(true);
    try {
      const organization = await createOrganization({
        name: newOrganizationName
      });
      
      setIsCreateDialogOpen(false);
      setNewOrganizationName('');
      
      // Redirecionar para a nova organização
      if (organization && setActive) {
        await setActive({ organization: organization.id });
        router.refresh();
      }
    } catch (error) {
      // Erro tratado pelo UI
    } finally {
      setIsCreating(false);
    }
  };

  const handleSwitchOrganization = async (organizationId: string) => {
    if (!setActive) return;
    
    try {
      await setActive({ organization: organizationId });
      router.refresh(); // Atualizar a página para refletir a mudança
    } catch (error) {
      // Erro tratado pelo UI
    }
  };

  const handleEditOrganization = async (organizationId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Evitar que o clique abra o switcher
    
    // Definir a organização como ativa antes de abrir o profile
    if (setActive && organizationId !== currentOrganization?.id) {
      await setActive({ organization: organizationId });
    }
    
    setShowOrganizationProfile(true);
  };



  // Função removida conforme solicitado: não é mais necessário retornar ícone de role

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'org:admin': return 'Agência';
      case 'org:member': return 'Membro';
      default: return 'Membro';
    }
  };

  if (!isLoaded) {
    return (
      <div className="flex items-center gap-2 px-3 py-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Carregando organizações...</span>
      </div>
    );
  }

  // Se há organizações mas não há organização atual, mostrar seletor básico
  if (hasOrganizations && !currentOrganization) {
    return (
      <div className="px-3 py-2">
        <Button
          variant="outline"
          onClick={() => {
            const firstOrg = organizations[0];
            if (firstOrg && setActive) {
              setActive({ organization: firstOrg.organization.id });
            }
          }}
          className="w-full justify-start text-sm"
        >
          <Building2 className="h-4 w-4 mr-2" />
          Selecionar Organização
        </Button>
      </div>
    );
  }

  // Se não há organizações, mostrar apenas botão de criar
  if (!hasOrganizations) {
    return (
      <Button
        variant="ghost"
        onClick={() => setIsCreateDialogOpen(true)}
        className="w-full justify-start px-3 py-2 h-auto"
      >
        <Plus className="h-4 w-4 mr-2" />
        <span className="text-sm">Criar Organização</span>
      </Button>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between px-3 py-2 h-auto"
          >
            <div className="flex items-center gap-2 overflow-hidden">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-[#ff0074]/10 to-[#5600ce]/10 border border-[#ff0074]/20 overflow-hidden">
                {currentOrganization?.imageUrl ? (
                  <img 
                    src={currentOrganization.imageUrl} 
                    alt={currentOrganization.name}
                    className="h-full w-full object-cover rounded-lg"
                  />
                ) : (
                  <Building2 className="h-4 w-4 text-[#ff0074]" />
                )}
              </div>
              <div className="text-left overflow-hidden">
                <p className="text-sm font-medium truncate">
                  {currentOrganization?.name || 'Selecionar Organização'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {currentOrganization && organizations.find(org => org.organization.id === currentOrganization.id)?.role === 'org:admin' ? 'Agência' : 'Membro'}
                </p>
              </div>
            </div>
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent className="w-[280px]" align="start">
          <DropdownMenuLabel>Teamsrganizations.length})</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {organizations.map((membership) => (
            <DropdownMenuItem
              key={membership.organization.id}
              onClick={() => handleSwitchOrganization(membership.organization.id)}
              className="cursor-pointer group"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <div className="flex h-6 w-6 items-center justify-center rounded bg-muted overflow-hidden">
                    {membership.organization.imageUrl ? (
                      <img 
                        src={membership.organization.imageUrl} 
                        alt={membership.organization.name}
                        className="h-full w-full object-cover rounded"
                      />
                    ) : (
                      <Building2 className="h-3 w-3" />
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium">{membership.organization.name}</p>
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-muted-foreground">
                        {getRoleLabel(membership.role)}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {/* Ícone de edição - visível apenas no hover */}
                  {membership.role === 'org:admin' && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-muted"
                      onClick={(e) => handleEditOrganization(membership.organization.id, e)}
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  )}
                  
                  {/* Check mark para organização ativa */}
                  {currentOrganization && membership.organization.id === currentOrganization.id && (
                    <Check className="h-4 w-4 text-[#ff0074]" />
                  )}
                </div>
              </div>
            </DropdownMenuItem>
          ))}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem
            onClick={() => setIsCreateDialogOpen(true)}
            className="cursor-pointer text-[#ff0074]"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nova Organização
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

            {/* OrganizationProfile - renderizado diretamente quando solicitado */}
      {showOrganizationProfile && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div 
            className="absolute inset-0" 
            onClick={() => setShowOrganizationProfile(false)}
          />
          <div className="relative max-w-4xl w-full max-h-[90vh] overflow-auto">
            {/* Botão X para fechar */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 z-10 h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm border hover:bg-background"
              onClick={() => setShowOrganizationProfile(false)}
            >
              <X className="h-4 w-4" />
            </Button>
            
            <OrganizationProfile 
              routing="hash"
              afterLeaveOrganizationUrl="/"
              appearance={{
                elements: {
                  rootBox: "w-full flex justify-center",
                  card: "shadow-2xl border border-border bg-background rounded-lg max-w-3xl",
                }
              }}
            />
          </div>
        </div>
      )}

      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Criar nova organização</DialogTitle>
            <DialogDescription>
              Crie uma nova organização para gerenciar seus projetos e equipe.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="organization-name">Nome da organização</Label>
              <Input
                id="organization-name"
                value={newOrganizationName}
                onChange={(e) => setNewOrganizationName(e.target.value)}
                placeholder="Ex: Minha Agência Digital"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isCreating) {
                    handleCreateOrganization();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateDialogOpen(false);
                setNewOrganizationName('');
              }}
              disabled={isCreating}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleCreateOrganization}
              disabled={!newOrganizationName.trim() || isCreating}
              className="bg-gradient-to-r from-[#ff0074] to-[#5600ce] hover:from-[#ff0074]/90 hover:to-[#5600ce]/90"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Criando...
                </>
              ) : (
                'Criar organização'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 

