'use client'

import { useQuery, useMutation, useApolloClient } from '@apollo/client'
import { useFirebaseAuth } from '@/hooks/use-clerk-auth'
import { 
  GET_NOTES_QUERY,
  GET_ALL_NOTES_QUERY, 
  CREATE_NOTE_MUTATION, 
  UPDATE_NOTE_MUTATION, 
  DELETE_NOTE_MUTATION 
} from '@/lib/graphql/mutations'

export interface Note {
  id: string
  title: string
  content: string
  type: string
  influencerId: string
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateNoteInput {
  title: string
  content: string
  type: string
  influencerId: string
  userId: string
}

export interface UpdateNoteInput {
  title?: string
  content?: string
  type?: string
}

export function useNotesGraphQL(influencerId?: string, allNotes: boolean = false) {
  const { currentUser } = useFirebaseAuth()
  const client = useApolloClient()

  // Determinar qual query usar
  const query = allNotes ? GET_ALL_NOTES_QUERY : GET_NOTES_QUERY
  const variables = allNotes 
    ? { userId: currentUser?.id || '' }
    : { influencerId: influencerId || '', userId: currentUser?.id || '' }

  // 🔥 OTIMIZAÇÃO: Query com cache inteligente e debounce
  const { 
    data: notesData, 
    loading: notesLoading, 
    error: notesError,
    refetch: refetchNotes 
  } = useQuery(query, {
    variables,
    skip: !currentUser?.id || (!allNotes && !influencerId),
    errorPolicy: 'all',
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache (era cache-and-network)
    notifyOnNetworkStatusChange: false // 🔥 OTIMIZAÇÃO: Reduzir re-renders
  })

  // Mutation para criar note
  const [createNoteMutation, { loading: createLoading }] = useMutation(CREATE_NOTE_MUTATION, {
    update(cache, { data }) {
      if (data?.createNote) {
        // Atualizar cache adicionando nova note
        const existingNotes = cache.readQuery<{ notes?: Note[], allNotes?: Note[] }>({
          query,
          variables
        })

        const notesField = allNotes ? 'allNotes' : 'notes'
        const existingList = allNotes ? existingNotes?.allNotes : existingNotes?.notes

        if (existingList) {
          cache.writeQuery({
            query,
            variables,
            data: {
              [notesField]: [data.createNote, ...existingList]
            }
          })
        }

        // Se estamos vendo notas de um influenciador específico, 
        // também atualizar a query de todas as notas se ela existir no cache
        if (!allNotes && influencerId) {
          const allNotesQuery = cache.readQuery<{ allNotes?: Note[] }>({
            query: GET_ALL_NOTES_QUERY,
            variables: { userId: currentUser?.id || '' }
          })

          if (allNotesQuery?.allNotes) {
            cache.writeQuery({
              query: GET_ALL_NOTES_QUERY,
              variables: { userId: currentUser?.id || '' },
              data: {
                allNotes: [data.createNote, ...allNotesQuery.allNotes]
              }
            })
          }
        }
      }
    },
    onError: (error) => {
      console.error('Erro ao criar nota:', error)
    }
  })

  // Mutation para atualizar note
  const [updateNoteMutation, { loading: updateLoading }] = useMutation(UPDATE_NOTE_MUTATION, {
    onError: (error) => {
      console.error('Erro ao atualizar nota:', error)
    }
  })

  // Mutation para deletar note
  const [deleteNoteMutation, { loading: deleteLoading }] = useMutation(DELETE_NOTE_MUTATION, {
    update(cache, { data }, { variables }) {
      if (data?.deleteNote && variables?.id) {
        // Remover note do cache atual
        const existingNotes = cache.readQuery<{ notes?: Note[], allNotes?: Note[] }>({
          query,
          variables: variables.id ? undefined : variables
        })

        const notesField = allNotes ? 'allNotes' : 'notes'
        const existingList = allNotes ? existingNotes?.allNotes : existingNotes?.notes

        if (existingList) {
          cache.writeQuery({
            query,
            variables: variables.id ? undefined : variables,
            data: {
              [notesField]: existingList.filter((note: Note) => note.id !== variables.id)
            }
          })
        }

        // Também remover de todas as queries relacionadas
        if (!allNotes) {
          // Remover da query de todas as notas
          const allNotesQuery = cache.readQuery<{ allNotes?: Note[] }>({
            query: GET_ALL_NOTES_QUERY,
            variables: { userId: currentUser?.id || '' }
          })

          if (allNotesQuery?.allNotes) {
            cache.writeQuery({
              query: GET_ALL_NOTES_QUERY,
              variables: { userId: currentUser?.id || '' },
              data: {
                allNotes: allNotesQuery.allNotes.filter((note: Note) => note.id !== variables.id)
              }
            })
          }
        }
      }
    },
    onError: (error) => {
      console.error('Erro ao deletar nota:', error)
    }
  })

  // Funções auxiliares
  const createNote = async (input: CreateNoteInput) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await createNoteMutation({
        variables: {
          input: {
            ...input,
            userId: currentUser.id
          }
        }
      })
      return result.data?.createNote
    } catch (error) {
      console.error('Erro ao criar nota:', error)
      throw error
    }
  }

  const updateNote = async (id: string, input: UpdateNoteInput) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await updateNoteMutation({
        variables: { id, input }
      })
      return result.data?.updateNote
    } catch (error) {
      console.error('Erro ao atualizar nota:', error)
      throw error
    }
  }

  const deleteNote = async (id: string) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await deleteNoteMutation({
        variables: { 
          id, 
          userId: currentUser.id 
        }
      })
      return result.data?.deleteNote
    } catch (error) {
      console.error('Erro ao deletar nota:', error)
      throw error
    }
  }

  const clearNotesCache = () => {
    client.cache.evict({
      fieldName: 'notes'
    })
    client.cache.evict({
      fieldName: 'allNotes'
    })
    client.cache.gc()
  }

  // Determinar qual lista usar baseado no tipo de query
  const notes = allNotes 
    ? notesData?.allNotes || []
    : notesData?.notes || []

  return {
    // Dados
    notes,
    
    // Estados de loading
    notesLoading,
    createLoading,
    updateLoading,
    deleteLoading,
    
    // Erros
    notesError,
    
    // Funções
    createNote,
    updateNote,
    deleteNote,
    refetchNotes,
    clearNotesCache,
    
    // Estados computados
    isLoading: notesLoading || createLoading || updateLoading || deleteLoading,
    hasError: !!notesError
  }
} 

