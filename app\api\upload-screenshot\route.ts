import { NextRequest, NextResponse } from 'next/server'
import { db, FieldValue } from '@/lib/firebase-admin'
import { uploadScreenshot } from '@/lib/firebase-storage'

export async function POST(request: NextRequest) {
  try {
    console.log('📸 [API] Iniciando upload direto de screenshot')
    
    // Extrair dados do FormData
    const formData = await request.formData()
    const file = formData.get('file') as File
    const influencerId = formData.get('influencerId') as string
    const platform = formData.get('platform') as string

    console.log('📸 [API] Dados recebidos:', { 
      influencerId, 
      platform, 
      filename: file?.name,
      size: file?.size,
      type: file?.type
    })

    if (!influencerId || !platform || !file) {
      return NextResponse.json({ 
        error: 'Parâmetros obrigatórios: file, influencerId, platform' 
      }, { status: 400 })
    }

    console.log('📸 [API] Fazendo upload direto usando uploadScreenshot()...')

    // Usar a função já existente para upload direto
    const publicUrl = await uploadScreenshot(file, influencerId, platform)

    console.log('📸 [API] Upload concluído. URL:', publicUrl)

    // Atualizar subcoleção com nova estrutura
    // /influencers/ID/screenshots/platform -> { urls: [...] }
    const screenshotDocRef = db
      .collection('influencers')
      .doc(influencerId)
      .collection('screenshots')
      .doc(platform)

    // Adicionar URL ao array de URLs da plataforma
    await screenshotDocRef.set({
      urls: FieldValue.arrayUnion(publicUrl),
      lastUpdated: new Date(),
      platform
    }, { merge: true })

    console.log('📸 [API] Firestore atualizado para plataforma:', platform)

    return NextResponse.json({
      success: true,
      url: publicUrl,
      filename: file.name,
      platform
    })

  } catch (error) {
    console.error('❌ [API] Erro no upload de screenshot:', error)
    return NextResponse.json({ 
      error: `Erro interno do servidor: ${error instanceof Error ? error.message : 'Erro desconhecido'}` 
    }, { status: 500 })
  }
} 

