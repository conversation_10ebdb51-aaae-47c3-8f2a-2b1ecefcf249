import { NextRequest, NextResponse } from 'next/server';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';

/**
 * 🔒 MIDDLEWARE DE ISOLAMENTO POR USUÁRIO
 * FASE 4.1: Middleware atualizado com isolamento automático
 */

// Interface para dados de autenticação
interface AuthData {
  user: {
    id: string;
    email?: string;
    role?: string;
    permissions?: string[];
  };
  token?: string;
}

// Interface para contexto do usuário
interface UserContext {
  userId: string;
  userEmail?: string;
  userRole?: string;
  requestId: string;
  timestamp: Date;
}

/**
 * Middleware principal para isolamento por usuário
 * Garante que todas as operações sejam filtradas por userId
 */
export function withUserIsolation<T = any>(
  handler: (req: NextRequest, userId: string, context: UserContext) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Verificar autenticação
      const authData = await verifyAuth(req);
      
      if (!authData) {
        return NextResponse.json(
          { 
            error: 'Usuário não autenticado',
            code: ISOLATION_ERRORS.INVALID_USER_ID 
          },
          { status: 401 }
        );
      }

      // Criar contexto do usuário
      const context: UserContext = {
        userId: authData.user.id,
        userEmail: authData.user.email,
        userRole: authData.user.role,
        requestId: generateRequestId(),
        timestamp: new Date()
      };

      // Log da requisição para auditoria (apenas em desenvolvimento)
      if (process.env.NODE_ENV === 'development') {
        IsolationUtils.logIsolationEvent(
          'read',
          'api_request',
          req.url,
          context.userId,
          {
            method: req.method,
            userAgent: req.headers.get('user-agent'),
            ip: getClientIp(req),
            requestId: context.requestId
          }
        );
      }

      // Executar handler com userId garantido
      return await handler(req, authData.user.id, context);

    } catch (error) {
      // Tratar erros de isolamento
      if (error instanceof IsolationError) {
        return NextResponse.json(
          { 
            error: error.message,
            code: error.code 
          },
          { status: 403 }
        );
      }

      // Log de erro
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      const errorStack = error instanceof Error ? error.stack : undefined;
      
      console.error('[USER_ISOLATION_MIDDLEWARE]', {
        error: errorMessage,
        stack: errorStack,
        url: req.url,
        method: req.method
      });

      return NextResponse.json(
        { 
          error: 'Erro interno do servidor',
          code: 'INTERNAL_ERROR'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware para operações que precisam de validação de ownership
 * Usado para operações em recursos específicos (GET/PUT/DELETE by ID)
 */
export function withOwnershipValidation<T extends { id: string }>(
  handler: (req: NextRequest, userId: string, context: UserContext) => Promise<NextResponse>,
  getResourceFunction: (id: string) => Promise<T | null>,
  resourceIdParam: string = 'id'
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: UserContext) => {
    try {
      // Extrair ID do recurso da URL
      const url = new URL(req.url);
      const resourceId = url.pathname.split('/').pop() || url.searchParams.get(resourceIdParam);
      
      if (!resourceId) {
        return NextResponse.json(
          { error: 'ID do recurso é obrigatório' },
          { status: 400 }
        );
      }

      // Validar ownership do recurso
      const resource = await getResourceFunction(resourceId);
      
      if (!resource) {
        return NextResponse.json(
          { error: 'Recurso não encontrado' },
          { status: 404 }
        );
      }

      // Verificar se usuário tem acesso ao recurso
      if (!IsolationUtils.validateOwnership(resource as any, userId)) {
        // Log de tentativa de acesso não autorizado
        IsolationUtils.logIsolationEvent(
          'read',
          'unauthorized_access',
          resourceId,
          userId,
          {
            requestId: context.requestId,
            resourceOwnerId: (resource as any).userId,
            violation: 'ownership_denied'
          }
        );

        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para acessar este recurso',
          userId,
          resourceId
        );
      }

      return await handler(req, userId, context);

    } catch (error) {
      if (error instanceof IsolationError) {
        throw error;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      throw new Error(`Erro na validação de ownership: ${errorMessage}`);
    }
  });
}

/**
 * Middleware para validação de relacionamentos entre entidades
 * Usado quando uma operação envolve múltiplas entidades (ex: campaign + brand)
 */
export function withRelationshipValidation(
  handler: (req: NextRequest, userId: string, context: UserContext) => Promise<NextResponse>,
  validationRules: RelationshipValidationRule[]
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: UserContext) => {
    try {
      // Executar validações de relacionamento
      for (const rule of validationRules) {
        await executeValidationRule(rule, req, userId);
      }

      return await handler(req, userId, context);

    } catch (error) {
      if (error instanceof IsolationError) {
        throw error;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      throw new Error(`Erro na validação de relacionamento: ${errorMessage}`);
    }
  });
}

/**
 * Middleware para operações em lote (batch operations)
 * Garante que todas as entidades no lote pertencem ao usuário
 */
export function withBatchValidation<T extends { id: string }>(
  handler: (req: NextRequest, userId: string, context: UserContext) => Promise<NextResponse>,
  getResourcesFunction: (ids: string[]) => Promise<T[]>,
  idsSourceKey: string = 'ids'
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: UserContext) => {
    try {
      const body = await req.json();
      const ids = body[idsSourceKey];

      if (!Array.isArray(ids) || ids.length === 0) {
        return NextResponse.json(
          { error: `${idsSourceKey} deve ser um array não vazio` },
          { status: 400 }
        );
      }

      // Buscar todos os recursos
      const resources = await getResourcesFunction(ids);
      
      // Validar ownership em lote
      const validation = IsolationUtils.validateBatchOwnership(resources as any, userId);
      
      if (!validation.isValid) {
        throw new IsolationError(
          ISOLATION_ERRORS.BATCH_VALIDATION_FAILED,
          `Usuário não tem permissão para os recursos: ${validation.invalidIds.join(', ')}`,
          userId,
          undefined,
          { invalidIds: validation.invalidIds }
        );
      }

      return await handler(req, userId, context);

    } catch (error) {
      if (error instanceof IsolationError) {
        throw error;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      throw new Error(`Erro na validação em lote: ${errorMessage}`);
    }
  });
}

// Types para validação de relacionamentos
interface RelationshipValidationRule {
  field: string;              // Campo no body/params que contém o ID
  getResource: (id: string) => Promise<any>;
  errorMessage: string;
}

async function executeValidationRule(
  rule: RelationshipValidationRule, 
  req: NextRequest, 
  userId: string
): Promise<void> {
  const body = await req.json().catch(() => ({}));
  const url = new URL(req.url);
  const fieldValue = body[rule.field] || url.searchParams.get(rule.field);

  if (!fieldValue) {
    return; // Campo opcional
  }

  const resource = await rule.getResource(fieldValue);
  
  if (!resource) {
    throw new IsolationError(
      ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
      `${rule.errorMessage}: recurso não encontrado`,
      userId,
      fieldValue
    );
  }

  if (!IsolationUtils.validateOwnership(resource, userId)) {
    throw new IsolationError(
      ISOLATION_ERRORS.RELATIONSHIP_INVALID,
      rule.errorMessage,
      userId,
      fieldValue
    );
  }
}

/**
 * Verificar autenticação do usuário
 * ✅ IMPLEMENTAÇÃO APENAS COM CLERK (removido Firebase Auth)
 */
async function verifyAuth(req: NextRequest): Promise<AuthData | null> {
  try {
    // Usar apenas Clerk server auth - sem fallbacks ou headers customizados
    const { auth } = await import('@clerk/nextjs/server');
    const { userId } = await auth();
    
    if (userId) {
      return {
        user: {
          id: userId,
          email: '',
          role: 'user'
        }
      };
    }
    
    // Usuário não autenticado
    return null;
    
  } catch (error) {
    console.error('[USER_ISOLATION] Erro na verificação Clerk:', error);
    return null;
  }
}

/**
 * Utilitários auxiliares
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function getClientIp(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIp) {
    return realIp;
  }
  
  return 'unknown';
}

/**
 * Middleware específico para operações de criação
 * Automaticamente adiciona userId aos dados
 */
export function withCreateIsolation(
  handler: (req: NextRequest, userId: string, context: UserContext, data: any) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: UserContext) => {
    try {
      const body = await req.json();
      
      // Adicionar userId automaticamente aos dados
      const dataWithUserId = IsolationUtils.addUserId(body, userId);
      
      return await handler(req, userId, context, dataWithUserId);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      throw new Error(`Erro na criação com isolamento: ${errorMessage}`);
    }
  });
}

/**
 * Middleware para rate limiting por usuário
 */
export function withRateLimit(
  maxRequestsPerMinute: number = 100
) {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (
    handler: (req: NextRequest, userId: string, context: UserContext) => Promise<NextResponse>
  ) => {
    return withUserIsolation(async (req: NextRequest, userId: string, context: UserContext) => {
      const now = Date.now();
      const windowMs = 60000; // 1 minuto
      
      const userRequestData = userRequests.get(userId);
      
      if (!userRequestData || now > userRequestData.resetTime) {
        userRequests.set(userId, { count: 1, resetTime: now + windowMs });
      } else {
        if (userRequestData.count >= maxRequestsPerMinute) {
          return NextResponse.json(
            { 
              error: 'Rate limit excedido. Tente novamente em breve.',
              code: 'RATE_LIMIT_EXCEEDED'
            },
            { status: 429 }
          );
        }
        userRequestData.count++;
      }

      return await handler(req, userId, context);
    });
  };
} 

