import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { ProposalService } from '@/services/proposal-service';
import { db } from '@/lib/firebase-admin';

interface Params {
  id: string;
}

export const POST = withUserIsolation(async (
  request: NextRequest,
  userId: string
) => {
  try {
    // Extrair proposalId da URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const proposalId = pathParts[pathParts.indexOf('proposals') + 1];
    
    const body = await request.json();
    const { influencerIds } = body;

    console.log('🚀 [API] Adicionando influenciadores à proposta:', {
      proposalId,
      influencerIds,
      userId,
      totalInfluencers: influencerIds?.length
    });

    // Validações básicas
    if (!proposalId) {
      return NextResponse.json(
        { error: 'ID da proposta é obrigatório' },
        { status: 400 }
      );
    }

    if (!influencerIds || !Array.isArray(influencerIds) || influencerIds.length === 0) {
      return NextResponse.json(
        { error: 'Lista de influenciadores é obrigatória' },
        { status: 400 }
      );
    }

    // Adicionar influenciadores à proposta
    await ProposalService.addInfluencersToProposal(
      proposalId,
      influencerIds,
      userId
    );

    console.log('✅ [API] Influenciadores adicionados com sucesso à proposta:', proposalId);

    return NextResponse.json({
      success: true,
      message: `${influencerIds.length} influenciador(es) adicionado(s) à proposta`,
      proposalId,
      influencersAdded: influencerIds.length
    });

  } catch (error) {
    console.error('❌ [API] Erro ao adicionar influenciadores à proposta:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
});

export const GET = withUserIsolation(async (
  request: NextRequest,
  userId: string
) => {
  try {
    // Extrair proposalId da URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const proposalId = pathParts[pathParts.indexOf('proposals') + 1];
    
    console.log('🔍 [PROPOSAL INFLUENCERS] Buscando influencers da proposta:', {
      proposalId,
      userId
    });
    
    // 🔒 VERIFICAÇÃO COMPLETA DE ACESSO (ownership + Clerk metadata)
    let hasAccess = false;
    
    // 1. Verificar ownership via ProposalService (proprietário direto)
    const isOwner = await ProposalService.canUserAccessProposal(proposalId, userId);
    
    if (isOwner) {
      hasAccess = true;
      console.log('✅ [ACCESS] Usuário é proprietário da proposta');
    } else {
      // 2. Verificar acesso via Clerk metadata (membro convidado)
      try {
        const { clerkClient } = await import('@clerk/nextjs/server');
        const clerk = await clerkClient();
        const user = await clerk.users.getUser(userId);
        
        const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];
        
        if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
          hasAccess = true;
          console.log('✅ [ACCESS] Usuário tem acesso via Clerk metadata:', proposalAccess);
        } else {
          console.log('❌ [ACCESS] Usuário não tem acesso via Clerk metadata');
        }
        
      } catch (clerkError) {
        console.warn('⚠️ [ACCESS] Erro ao verificar Clerk metadata:', clerkError);
      }
    }
      
      if (!hasAccess) {
        return NextResponse.json(
        { error: 'Access denied - Você não tem permissão para acessar esta proposta' },
          { status: 403 }
        );
    }
    
    // 🔥 IMPLEMENTAÇÃO CORRETA: Buscar a subcoleção "influencers" da proposta
    const influencersSnapshot = await db
      .collection('proposals')
      .doc(proposalId)
      .collection('influencers')
      .get();
    
    // Extrair os IDs dos influencers
    const influencerIds = influencersSnapshot.docs.map(doc => doc.id);
    
    console.log('✅ [PROPOSAL INFLUENCERS] IDs encontrados na subcoleção:', {
      proposalId,
      influencerIds,
      count: influencerIds.length
    });
    
    return NextResponse.json({
      success: true,
      influencerIds,
      count: influencerIds.length
    });
    
  } catch (error) {
    console.error('❌ [PROPOSAL INFLUENCERS] Erro ao buscar influencers da proposta:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro interno do servidor',
        influencerIds: []
      },
      { status: 500 }
    );
  }
}); 