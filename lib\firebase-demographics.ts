// SERVIÇO DE DEMOGRAPHICS PARA INFLUENCIADORES
// Gerenciamento completo de dados demográficos com subcoleções

import { db } from './firebase';

// Interface para dados demográficos de audiência
export interface AudienceDemographic {
  id?: string;
  influencerId: string;
  userId: string;
  platform: 'instagram' | 'tiktok' | 'youtube' | 'facebook' | 'twitch' | 'kwai';
  
  // Demografia da audiência
  audienceGender: {
    male: number;
    female: number;
    other: number;
  };
  
  audienceLocations: Array<{ country: string; percentage: number }>; // Países/Regiões com percentuais
  audienceCities: Array<{ city: string; percentage: number }>;      // Cidades principais com percentuais
  
  // Faixas etárias (percentuais)
  audienceAgeRange: {
    [range: string]: number; // ex: "18-24": 30, "25-34": 50
  };
  
  // Metadados da captura
  captureDate: Date;
  isActive: boolean;
  source: 'manual' | 'api' | 'import' | 'form';
  
  // Auditoria
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
}

// Função para obter referência da subcoleção demographics
const getDemographicsCollection = (influencerId: string) => 
  db.collection('influencers').doc(influencerId).collection('demographics');

// ===== FUNÇÕES DE BUSCA =====

// Buscar demografia ativa de um influenciador
export async function getCurrentDemographics(influencerId: string): Promise<AudienceDemographic[]> {
  try {
    console.log(`🔍 [Demographics] Buscando demografia ativa para influenciador: ${influencerId}`);
    
    const demographicsCollection = getDemographicsCollection(influencerId);
    const snapshot = await demographicsCollection
      .where('isActive', '==', true)
      .get();

    if (snapshot.empty) {
      console.log(`📭 [Demographics] Nenhuma demografia ativa encontrada para: ${influencerId}`);
      return [];
    }

    const demographics = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        influencerId: influencerId, // Garantir que o ID está correto
        userId: data.userId,
        platform: data.platform,
        audienceGender: data.audienceGender || { male: 0, female: 0, other: 0 },
        audienceLocations: data.audienceLocations || [],
        audienceCities: data.audienceCities || [],
        audienceAgeRange: data.audienceAgeRange || {},
        captureDate: data.captureDate?.toDate() || new Date(),
        isActive: data.isActive,
        source: data.source || 'manual',
        createdAt: data.createdAt?.toDate() || new Date(),
        createdBy: data.createdBy,
        updatedAt: data.updatedAt?.toDate() || new Date(),
        updatedBy: data.updatedBy
      } as AudienceDemographic;
    });

    // Ordenar por captureDate (mais recente primeiro) no código
    demographics.sort((a, b) => b.captureDate.getTime() - a.captureDate.getTime());

    
    return demographics;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao buscar demografia ativa:`, error);
    throw error;
  }
}

// Buscar histórico completo de demografia de um influenciador
export async function getDemographicsHistory(influencerId: string): Promise<AudienceDemographic[]> {
  try {
    console.log(`🔍 [Demographics] Buscando histórico de demografia para: ${influencerId}`);
    
    const demographicsCollection = getDemographicsCollection(influencerId);
    const snapshot = await demographicsCollection.get();

    if (snapshot.empty) {
      console.log(`📭 [Demographics] Nenhum histórico de demografia encontrado para: ${influencerId}`);
      return [];
    }

    const history = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        influencerId: influencerId, // Garantir que o ID está correto
        userId: data.userId,
        platform: data.platform,
        audienceGender: data.audienceGender || { male: 0, female: 0, other: 0 },
        audienceLocations: data.audienceLocations || [],
        audienceCities: data.audienceCities || [],
        audienceAgeRange: data.audienceAgeRange || {},
        captureDate: data.captureDate?.toDate() || new Date(),
        isActive: data.isActive,
        source: data.source || 'manual',
        createdAt: data.createdAt?.toDate() || new Date(),
        createdBy: data.createdBy,
        updatedAt: data.updatedAt?.toDate() || new Date(),
        updatedBy: data.updatedBy
      } as AudienceDemographic;
    });

    // Ordenar por captureDate (mais recente primeiro) no código
    history.sort((a, b) => b.captureDate.getTime() - a.captureDate.getTime());

   
    return history;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao buscar histórico de demografia:`, error);
    throw error;
  }
}

// Buscar demografia por ID
export async function getDemographicById(influencerId: string, demographicId: string): Promise<AudienceDemographic | null> {
  try {
    console.log(`🔍 [Demographics] Buscando demografia por ID: ${demographicId} do influenciador: ${influencerId}`);
    
    const demographicsCollection = getDemographicsCollection(influencerId);
    const doc = await demographicsCollection.doc(demographicId).get();
    
    if (!doc.exists) {
      console.log(`📭 [Demographics] Demografia não encontrada com ID: ${demographicId}`);
      return null;
    }

    const data = doc.data();
    if (!data) {
      return null;
    }
    
    const demographic: AudienceDemographic = {
      id: doc.id,
      influencerId: influencerId,
      userId: data.userId,
      platform: data.platform,
      audienceGender: data.audienceGender || { male: 0, female: 0, other: 0 },
      audienceLocations: data.audienceLocations || [],
      audienceCities: data.audienceCities || [],
      audienceAgeRange: data.audienceAgeRange || {},
      captureDate: data.captureDate?.toDate() || new Date(),
      isActive: data.isActive,
      source: data.source || 'manual',
      createdAt: data.createdAt?.toDate() || new Date(),
      createdBy: data.createdBy,
      updatedAt: data.updatedAt?.toDate() || new Date(),
      updatedBy: data.updatedBy
    };

    
    return demographic;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao buscar demografia por ID:`, error);
    throw error;
  }
}

// ===== FUNÇÕES DE CRIAÇÃO E ATUALIZAÇÃO =====

// Criar nova demografia
export async function createDemographic(
  demographicData: Omit<AudienceDemographic, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string
): Promise<string> {
  try {
    console.log(`🆕 [Demographics] Criando nova demografia para influenciador: ${demographicData.influencerId}`);
    console.log(`🎯 [Demographics] audienceAgeRange recebido:`, demographicData.audienceAgeRange);
    console.log(`🎯 [Demographics] Tipo de audienceAgeRange:`, typeof demographicData.audienceAgeRange);
    console.log(`🎯 [Demographics] É array?`, Array.isArray(demographicData.audienceAgeRange));
    
    // Desativar demografias anteriores para a mesma plataforma
    await deactivateCurrentDemographics(demographicData.influencerId, demographicData.platform);
    
    const now = new Date();
    const dataToSave = {
      userId: demographicData.userId,
      platform: demographicData.platform,
      audienceGender: demographicData.audienceGender,
      audienceLocations: demographicData.audienceLocations,
      audienceCities: demographicData.audienceCities,
      audienceAgeRange: demographicData.audienceAgeRange,
      captureDate: demographicData.captureDate || now,
      isActive: true,
      source: demographicData.source || 'form',
      createdAt: now,
      createdBy: userId,
      updatedAt: now,
      updatedBy: userId
    };
    
    

    const demographicsCollection = getDemographicsCollection(demographicData.influencerId);
    const docRef = await demographicsCollection.add(dataToSave);
    
   
    // Verificar se foi salvo corretamente
    const savedDoc = await docRef.get();
    const savedData = savedDoc.data();
    console.log(`🔍 [Demographics] Dados salvos no Firebase:`, savedData);
    console.log(`🔍 [Demographics] audienceAgeRange salvo:`, savedData?.audienceAgeRange);
    
    return docRef.id;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao criar demografia:`, error);
    throw error;
  }
}

// Atualizar demografia existente
export async function updateDemographic(
  influencerId: string,
  demographicId: string,
  updateData: Partial<Omit<AudienceDemographic, 'id' | 'influencerId' | 'userId' | 'createdAt' | 'createdBy'>>,
  userId: string
): Promise<boolean> {
  try {
    console.log(`✏️ [Demographics] Atualizando demografia: ${demographicId} do influenciador: ${influencerId}`);
    console.log(`📊 [Demographics] Dados de atualização:`, JSON.stringify(updateData, null, 2));
    
    const dataToUpdate = {
      ...updateData,
      updatedAt: new Date(),
      updatedBy: userId
    };

    const demographicsCollection = getDemographicsCollection(influencerId);
    await demographicsCollection.doc(demographicId).update(dataToUpdate);
   
    return true;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao atualizar demografia:`, error);
    throw error;
  }
}

// Deletar demografia
export async function deleteDemographic(influencerId: string, demographicId: string): Promise<boolean> {
  try {
    console.log(`🗑️ [Demographics] Deletando demografia: ${demographicId} do influenciador: ${influencerId}`);
    
    const demographicsCollection = getDemographicsCollection(influencerId);
    await demographicsCollection.doc(demographicId).delete();
    
    return true;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao deletar demografia:`, error);
    throw error;
  }
}

// ===== FUNÇÕES AUXILIARES =====

// Desativar demografias atuais para uma plataforma específica
async function deactivateCurrentDemographics(influencerId: string, platform: string): Promise<void> {
  try {
    console.log(`🔄 [Demographics] Desativando demografias atuais para: ${influencerId} - ${platform}`);
    
    const demographicsCollection = getDemographicsCollection(influencerId);
    const snapshot = await demographicsCollection
      .where('platform', '==', platform)
      .where('isActive', '==', true)
      .get();

    if (!snapshot.empty) {
      const batch = db.batch();
      
      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          isActive: false,
          updatedAt: new Date()
        });
      });
      
      await batch.commit();
      
    }
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao desativar demografias atuais:`, error);
    throw error;
  }
}

// Verificar se influenciador tem demographics ativas
export async function hasActiveDemographics(influencerId: string): Promise<boolean> {
  try {
    const current = await getCurrentDemographics(influencerId);
    return current.length > 0;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao verificar demografias ativas:`, error);
    return false;
  }
}

// Buscar todos os influenciadores com demographics (para relatórios)
export async function getInfluencersWithDemographics(userId: string): Promise<string[]> {
  try {
    console.log(`📊 [Demographics] Buscando influenciadores com demographics para usuário: ${userId}`);
    
    // Com subcoleções, precisamos buscar na coleção principal de influencers primeiro
    const influencersSnapshot = await db.collection('influencers')
      .where('userId', '==', userId)
      .get();

    const influencerIds: string[] = [];
    
    // Para cada influenciador, verificar se tem demographics ativas
    for (const influencerDoc of influencersSnapshot.docs) {
      const demographicsSnapshot = await getDemographicsCollection(influencerDoc.id)
        .where('isActive', '==', true)
        .limit(1)
        .get();
      
      if (!demographicsSnapshot.empty) {
        influencerIds.push(influencerDoc.id);
      }
    }
    
   
    return influencerIds;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao buscar influenciadores com demographics:`, error);
    throw error;
  }
}

// Calcular média demográfica consolidada de um influenciador
export async function getConsolidatedDemographics(influencerId: string): Promise<{
  audienceGender: { male: number; female: number; other: number };
  topLocations: Array<{ country: string; percentage: number }>;
  topCities: Array<{ city: string; percentage: number }>;
  dominantAgeRange: string;
} | null> {
  try {
    console.log(`📊 [Demographics] Calculando demografia consolidada para: ${influencerId}`);
    
    const demographics = await getCurrentDemographics(influencerId);
    
    if (demographics.length === 0) {
      return null;
    }
    
    // Calcular médias ponderadas
    let totalMale = 0, totalFemale = 0, totalOther = 0;
    const locationCounts: { [key: string]: number } = {};
    const cityCounts: { [key: string]: number } = {};
    const ageRangeCounts: { [key: string]: number } = {};
    
    demographics.forEach(demo => {
      // Gênero (média simples)
      totalMale += demo.audienceGender.male;
      totalFemale += demo.audienceGender.female;
      totalOther += demo.audienceGender.other;
      
      // Localizações (contagem com percentuais)
      demo.audienceLocations.forEach(locationData => {
        const locationKey = locationData.country;
        locationCounts[locationKey] = (locationCounts[locationKey] || 0) + locationData.percentage;
      });
      
      // Cidades (contagem com percentuais)
      demo.audienceCities.forEach(cityData => {
        const cityKey = cityData.city;
        cityCounts[cityKey] = (cityCounts[cityKey] || 0) + cityData.percentage;
      });
      
      // Faixas etárias (soma percentuais)
      Object.entries(demo.audienceAgeRange).forEach(([range, percentage]) => {
        ageRangeCounts[range] = (ageRangeCounts[range] || 0) + percentage;
      });
    });
    
    const platformCount = demographics.length;
    
    // Top 5 localizações e cidades com percentuais
    const topLocations = Object.entries(locationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([country, percentage]) => ({ country, percentage }));
      
    const topCities = Object.entries(cityCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([city, percentage]) => ({ city, percentage }));
    
    // Faixa etária dominante
    const dominantAgeRange = Object.entries(ageRangeCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown';
    
    const consolidated = {
      audienceGender: {
        male: Math.round(totalMale / platformCount),
        female: Math.round(totalFemale / platformCount),
        other: Math.round(totalOther / platformCount)
      },
      topLocations,
      topCities,
      dominantAgeRange
    };
    
   
    return consolidated;
  } catch (error) {
    console.error(`❌ [Demographics] Erro ao calcular demografia consolidada:`, error);
    throw error;
  }
} 

