import { BaseDocument } from '@/types/base';

/**
 * Classe utilitária para operações de isolamento multi-tenancy
 */
export class IsolationUtils {
  /**
   * Validar se documento pertence ao usuário
   */
  static validateOwnership(document: BaseDocument, userId: string): boolean {
    if (!document || !document.userId || !userId) {
      return false;
    }
    return document.userId === userId;
  }

  /**
   * Filtrar array de documentos por usuário
   */
  static filterByUser<T extends BaseDocument>(documents: T[], userId: string): T[] {
    if (!userId || !Array.isArray(documents)) {
      return [];
    }
    return documents.filter(doc => doc.userId === userId);
  }

  /**
   * Adicionar userId automaticamente nos dados de criação
   */
  static addUserId<T>(data: T, userId: string): T & { userId: string; createdAt: Date; updatedAt: Date } {
    const now = new Date();
    return {
      ...data,
      userId,
      createdAt: now,
      updatedAt: now
    };
  }

  /**
   * Validar relacionamento entre entidades do mesmo usuário
   */
  static validateRelationship(
    parentDocument: BaseDocument,
    childDocument: BaseDocument
  ): boolean {
    if (!parentDocument?.userId || !childDocument?.userId) {
      return false;
    }
    return parentDocument.userId === childDocument.userId;
  }

  /**
   * Preparar dados para criação com userId e metadados
   */
  static prepareCreateData<T>(
    data: T,
    userId: string,
    createdBy?: string
  ): T & { 
    userId: string; 
    createdBy: string; 
    updatedBy: string; 
    createdAt: Date; 
    updatedAt: Date 
  } {
    const now = new Date();
    const userRef = createdBy || userId;
    
    return {
      ...data,
      userId,
      createdBy: userRef,
      updatedBy: userRef,
      createdAt: now,
      updatedAt: now
    };
  }

  /**
   * Preparar dados para atualização
   */
  static prepareUpdateData<T>(
    data: T,
    updatedBy: string
  ): T & { updatedAt: Date; updatedBy: string } {
    return {
      ...data,
      updatedAt: new Date(),
      updatedBy
    };
  }

  /**
   * Validar se todos os documentos pertencem ao mesmo usuário
   */
  static validateBatchOwnership<T extends BaseDocument>(
    documents: T[], 
    userId: string
  ): { isValid: boolean; invalidIds: string[] } {
    const invalidIds: string[] = [];
    
    documents.forEach(doc => {
      if (!this.validateOwnership(doc, userId)) {
        invalidIds.push(doc.id);
      }
    });
    
    return {
      isValid: invalidIds.length === 0,
      invalidIds
    };
  }

  /**
   * Verificar se usuário pode acessar documento compartilhado
   */
  static canAccessSharedDocument(
    document: any, 
    userId: string, 
    permission: 'read' | 'write' | 'admin' = 'read'
  ): boolean {
    // Se é o proprietário, tem acesso total
    if (document.userId === userId) {
      return true;
    }
    
    // Se não é compartilhado, não tem acesso
    if (!document.isShared || !document.sharedWith) {
      return false;
    }
    
    // Verificar se está na lista de compartilhamento
    if (!document.sharedWith.includes(userId)) {
      return false;
    }
    
    // Verificar permissão específica
    if (permission === 'read') {
      return true;
    }
    
    if (permission === 'write') {
      return ['write', 'admin'].includes(document.sharePermissions);
    }
    
    if (permission === 'admin') {
      return document.sharePermissions === 'admin';
    }
    
    return false;
  }

  /**
   * Remover dados sensíveis baseado no acesso do usuário
   */
  static sanitizeDataForUser<T extends BaseDocument>(
    data: T, 
    userId: string,
    sensitiveFields: (keyof T)[] = []
  ): Partial<T> {
    const sanitized = { ...data } as any;
    
    // Se não é o proprietário, remover campos sensíveis
    if (data.userId !== userId) {
      sensitiveFields.forEach(field => {
        delete sanitized[field];
      });
      
      // Remover metadados de criação/atualização por padrão se existirem
      if ('createdBy' in sanitized) delete sanitized.createdBy;
      if ('updatedBy' in sanitized) delete sanitized.updatedBy;
    }
    
    return sanitized;
  }

  /**
   * Criar filtro de query para isolamento
   */
  static createUserFilter(userId: string): { userId: string } {
    return { userId };
  }

  /**
   * Validar IDs de array pertencem ao usuário
   */
  static async validateReferenceIds<T extends BaseDocument>(
    ids: string[],
    userId: string,
    fetchFunction: (id: string) => Promise<T | null>
  ): Promise<{ isValid: boolean; invalidIds: string[]; notFound: string[] }> {
    const invalidIds: string[] = [];
    const notFound: string[] = [];
    
    for (const id of ids) {
      try {
        const document = await fetchFunction(id);
        if (!document) {
          notFound.push(id);
        } else if (!this.validateOwnership(document, userId)) {
          invalidIds.push(id);
        }
      } catch (error) {
        notFound.push(id);
      }
    }
    
    return {
      isValid: invalidIds.length === 0 && notFound.length === 0,
      invalidIds,
      notFound
    };
  }

  /**
   * Calcular permissões do usuário para documento
   */
  static calculateUserPermissions(
    document: any,
    userId: string
  ): {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
    canShare: boolean;
    isOwner: boolean;
  } {
    const isOwner = document.userId === userId;
    
    if (isOwner) {
      return {
        canRead: true,
        canWrite: true,
        canDelete: true,
        canShare: true,
        isOwner: true
      };
    }
    
    // Para documentos compartilhados
    const canAccess = this.canAccessSharedDocument(document, userId);
    const canWrite = this.canAccessSharedDocument(document, userId, 'write');
    const canAdmin = this.canAccessSharedDocument(document, userId, 'admin');
    
    return {
      canRead: canAccess,
      canWrite: canWrite,
      canDelete: canAdmin,
      canShare: canAdmin,
      isOwner: false
    };
  }

  /**
   * Aplicar paginação com isolamento
   */
  static applyPagination<T>(
    data: T[],
    limit?: number,
    offset?: number
  ): {
    data: T[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  } {
    const total = data.length;
    const actualLimit = limit || 50;
    const actualOffset = offset || 0;
    
    const paginatedData = data.slice(actualOffset, actualOffset + actualLimit);
    
    return {
      data: paginatedData,
      pagination: {
        total,
        limit: actualLimit,
        offset: actualOffset,
        hasNext: actualOffset + actualLimit < total,
        hasPrev: actualOffset > 0
      }
    };
  }

  /**
   * Logs de auditoria para operações sensíveis
   * ⚠️ DESABILITADO: Logs excessivos removidos 
   */
  static logIsolationEvent(
    operation: 'create' | 'read' | 'update' | 'delete' | 'share',
    documentType: string,
    documentId: string,
    userId: string,
    metadata?: Record<string, any>
  ): void {
    // ✅ LOGS DESABILITADOS - reduzindo poluição nos logs
    return;
    
    /*
    const logEntry = {
      timestamp: new Date().toISOString(),
      operation,
      documentType,
      documentId,
      userId,
      metadata: metadata || {}
    };
    
    // Em produção, enviar para serviço de auditoria
    // console.log('[ISOLATION_AUDIT]', JSON.stringify(logEntry)); // DESABILITADO
    */
  }

  /**
   * Criar contexto de isolamento para operações batch
   */
  static createIsolationContext(
    userId: string,
    allowedOperations: string[] = ['read', 'write', 'delete']
  ): {
    userId: string;
    allowedOperations: string[];
    validateOperation: (operation: string) => boolean;
    createUserFilter: () => { userId: string };
    addUserIdToData: <T>(data: T) => T & { userId: string };
  } {
    return {
      userId,
      allowedOperations,
      
      validateOperation: (operation: string) => {
        return allowedOperations.includes(operation);
      },
      
      createUserFilter: () => this.createUserFilter(userId),
      
      addUserIdToData: <T>(data: T) => this.addUserId(data, userId)
    };
  }
}

/**
 * Decorator para métodos que precisam de validação de ownership
 */
export function RequireOwnership(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;
  
  descriptor.value = function(documentId: string, userId: string, ...args: any[]) {
    // Implementação do decorator seria específica para cada caso
    // Esta é uma estrutura exemplo
    return method.apply(this, [documentId, userId, ...args]);
  };
}

/**
 * Constantes para tipos de erro de isolamento
 */
export const ISOLATION_ERRORS = {
  OWNERSHIP_DENIED: 'OWNERSHIP_DENIED',
  INVALID_USER_ID: 'INVALID_USER_ID',
  RELATIONSHIP_INVALID: 'RELATIONSHIP_INVALID',
  DOCUMENT_NOT_FOUND: 'DOCUMENT_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  BATCH_VALIDATION_FAILED: 'BATCH_VALIDATION_FAILED'
} as const;

/**
 * Classe de erro específica para violações de isolamento
 */
export class IsolationError extends Error {
  public readonly code: string;
  public readonly userId?: string;
  public readonly documentId?: string;
  public readonly metadata?: Record<string, any>;

  constructor(
    code: string,
    message: string,
    userId?: string,
    documentId?: string,
    metadata?: Record<string, any>
  ) {
    super(message);
    this.name = 'IsolationError';
    this.code = code;
    this.userId = userId;
    this.documentId = documentId;
    this.metadata = metadata;
  }
} 

