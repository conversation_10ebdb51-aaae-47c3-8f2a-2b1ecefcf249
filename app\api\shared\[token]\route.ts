import { NextRequest, NextResponse } from 'next/server';
import { ListSharingService } from '@/lib/list-sharing-service';
import { ListService } from '@/services/list-service';
import { ProposalService } from '@/services/proposal-service';
// Firebase Auth removido em favor do Clerk
// import { adminAuth } from '@/lib/firebase-admin';

// GET - Acessar lista compartilhada por token
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params;
    
    // 🔐 Verificar se há usuário autenticado
    let currentUserId: string | null = null;
    try {
      const authHeader = request.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const idToken = authHeader.slice(7);
        // DESABILITADO: Firebase Auth removido em favor do Clerk
    // const decodedToken = await adminAuth.verifyIdToken(idToken);
        currentUserId = decodedToken.uid;
        console.log('👤 Usuário autenticado detectado:', currentUserId);
      }
    } catch (authError) {
      console.log('👤 Nenhum usuário autenticado ou token inválido');
    }
    
    // Buscar dados do compartilhamento
    const sharing = await ProposalService.getProposalSharingByToken(token);
    
    if (!sharing) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se está ativo
    if (!sharing.isActive) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento foi desativado' },
        { status: 403 }
      );
    }

    // Verificar se não expirou
    const now = new Date();
    if (sharing.expiresAt && sharing.expiresAt < now) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento expirou' },
        { status: 403 }
      );
    }

    // Verificar configurações de privacidade
    if (!sharing.isPublic) {
      return NextResponse.json(
        { success: false, error: 'Este link requer autenticação' },
        { status: 401 }
      );
    }

    // Verificar senha se necessário
    if (sharing.requiresPassword) {
      const { password } = await request.json().catch(() => ({}));
      if (!password || password !== sharing.password) {
        return NextResponse.json(
          { success: false, error: 'Senha obrigatória', requiresPassword: true },
          { status: 401 }
        );
      }
    }

    // Buscar a proposta original
    const originalProposal = await ProposalService.getProposalById(sharing.proposalId);
    
    if (!originalProposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    // ✨ NOVO: SE HÁ USUÁRIO LOGADO E ELE NÃO É O PROPRIETÁRIO, ADICIONAR COMO COLABORADOR
    let userProposalId = sharing.proposalId; // Agora sempre usa a proposta original
    let userCollaboratorRole: 'owner' | 'editor' | 'viewer' | 'none' = 'none';
    let isUserCollaborator = false;
    
    if (currentUserId && currentUserId !== originalProposal.criadoPor && currentUserId !== originalProposal.brandId) {
      console.log('🤝 [AUTO-COLLAB] Adicionando usuário como colaborador automático:', currentUserId);
      
      try {
        // Verificar se já é colaborador
        const hasAccess = await ProposalService.canUserAccessProposal(sharing.proposalId, currentUserId);
        
        if (!hasAccess) {
          console.log('➕ [AUTO-COLLAB] Usuário não é colaborador, adicionando automaticamente...');
          
          // Buscar dados do usuário (simplificado - usar ID como email temporário)
          // TODO: Em produção, buscar dados reais do usuário no Firebase Auth
          const userData = {
            userId: currentUserId,
            userEmail: `user-${currentUserId}@temp.email`, // Placeholder
            userName: `Usuário ${currentUserId.slice(-6)}`, // Placeholder
            role: 'viewer' as const,
            addedBy: originalProposal.criadoPor // Quem criou a proposta é quem "adicionou" o colaborador via compartilhamento
          };
          
          await ProposalService.addCollaboratorToProposal(sharing.proposalId, userData);
          
          userCollaboratorRole = 'viewer';
          isUserCollaborator = true;
          console.log('✅ [AUTO-COLLAB] Usuário adicionado como colaborador viewer automaticamente');
        } else {
          console.log('✅ [AUTO-COLLAB] Usuário já tem acesso à proposta');
          
          // Buscar o papel específico do colaborador
          const proposal = await ProposalService.getProposalById(sharing.proposalId);
          if (proposal && 'collaborators' in proposal && Array.isArray((proposal as any).collaborators)) {
            const collaborator = (proposal as any).collaborators.find(
              (collab: any) => collab.userId === currentUserId && collab.status === 'active'
            );
            if (collaborator) {
              userCollaboratorRole = collaborator.role;
              isUserCollaborator = true;
              console.log(`🎯 [AUTO-COLLAB] Usuário é colaborador ${userCollaboratorRole}`);
            }
          }
        }
        
      } catch (autoCollabError) {
        console.error('❌ [AUTO-COLLAB] Erro ao adicionar colaborador automático:', autoCollabError);
        // Continua normalmente mesmo se não conseguir adicionar como colaborador
      }
    } else if (currentUserId && (currentUserId === originalProposal.criadoPor || currentUserId === originalProposal.brandId)) {
      // Usuário é proprietário
      userCollaboratorRole = 'owner';
      isUserCollaborator = true;
      console.log('👑 [AUTO-COLLAB] Usuário é proprietário da proposta');
    }

    // 🎯 Buscar a proposta (sempre a original, pois agora usamos colaboradores)
    const proposal = originalProposal;

    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Erro ao acessar proposta do usuário' },
        { status: 500 }
      );
    }

    // Carregar influenciadores da subcoleção (nova estrutura)
    let proposalInfluencers = [];
    try {
      proposalInfluencers = await ProposalService.getProposalInfluencers(userProposalId);
      console.log(`📋 Carregados ${proposalInfluencers.length} influenciadores da subcoleção`);
    } catch (error) {
      console.error('❌ Erro ao carregar influenciadores da subcoleção:', error);
    }

    // Se não há influenciadores na subcoleção, tentar migrar da estrutura antiga
    const hasOldStructure = proposal.influencers?.length > 0 || (proposal as any).influencerIds?.length > 0;
    if (proposalInfluencers.length === 0 && hasOldStructure) {
      console.log('📦 Tentando migrar da estrutura antiga para subcoleção...');
      try {
        await ProposalService.migrateProposalToSubcollection(
          userProposalId,
          proposal.criadoPor || 'system'
        );
        // Recarregar após migração
        proposalInfluencers = await ProposalService.getProposalInfluencers(userProposalId);
        console.log(`✅ Migração concluída. ${proposalInfluencers.length} influenciadores carregados`);
      } catch (migrateError) {
        console.error('❌ Erro durante migração:', migrateError);
      }
    }

    // Preparar dados da proposta para incluir influenciadores da subcoleção
    const proposalData = {
      ...proposal,
      // Adicionar ID da proposta do usuário (pode ser diferente da original)
      userProposalId,
      // Manter a estrutura influencerIds para compatibilidade, mas agora com dados da subcoleção
      influencerIds: proposalInfluencers.map(pi => pi.influencerId),
      // Adicionar dados completos dos influenciadores da subcoleção
      subcollectionInfluencers: proposalInfluencers,
      // Adicionar informação sobre qual estrutura está sendo usada
      _usingSubcollection: proposalInfluencers.length > 0,
      // Adicionar informações sobre compartilhamento
      isCollaboratorAccess: currentUserId && currentUserId !== originalProposal.criadoPor,
      originalProposalId: sharing.proposalId,
      sharedFrom: originalProposal.criadoPor,
      // ✨ NOVO: Informações sobre papel como colaborador
      userCollaboratorRole,
      isUserCollaborator,
      // Metadados de acesso para filtros
      accessMetadata: {
        userId: currentUserId,
        userRole: 'user', // Assumindo que usuarios acessando via link são 'user'
        collaboratorRole: userCollaboratorRole,
        isCollaborator: isUserCollaborator
      }
    };

    // Atualizar contador de acesso
    await ProposalService.updateSharingAccess(token);

    // Retornar dados da proposta e configurações de compartilhamento
    return NextResponse.json({
      success: true,
      data: {
        proposal: proposalData,
        sharing: {
          allowDownload: sharing.allowDownload,
          accessCount: sharing.accessCount + 1,
          createdAt: sharing.createdAt?.toISOString(),
          expiresAt: sharing.expiresAt?.toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Erro ao acessar proposta compartilhada:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Registrar visualização adicional ou interação
export async function POST(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const { token } = await params;
    const { acao } = await request.json();
    
    console.log('📊 API: Registrando interação:', acao);

    // Registrar diferentes tipos de interação
    const dadosVisualizacao = {
      ip: request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    };

    await ListSharingService.registrarVisualizacao(token, dadosVisualizacao);

    return NextResponse.json({
      success: true,
      message: 'Interação registrada'
    });

  } catch (error) {
    console.error('❌ API: Erro ao registrar interação:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 