// RESOLVERS GRAPHQL COM INTEGRAÇÃO FIREBASE E CACHE
// Implementação completa dos resolvers para o sistema de CRM

import { 
  getAllInfluencers, 
  getInfluencerById, 
  addInfluencer, 
  updateInfluencer, 
  deleteInfluencer 
} from '../firebase';
import { 
  getFinancialByInfluencerId, 
  addFinancial, 
  updateFinancial, 
  deleteFinancial
} from '../firebase-financials';
import { db } from '../firebase-admin';
import { FinancialCacheService } from '../firebase-financial-cache';
import { FinancialDenormalizationService } from '../financial-denormalization-service';
import { GraphQLScalarType, Kind } from 'graphql';
import { PubSub } from 'graphql-subscriptions';
import DataLoader from 'dataloader';
// Firebase Admin SDK imports are handled via the db import above

// PubSub para subscriptions
const pubsub = new PubSub();

// Custom scalar para Date
const DateScalar = new GraphQLScalarType({
  name: 'Date',
  description: 'Date custom scalar type',
  serialize(value: Date) {
    return value.toISOString();
  },
  parseValue(value: string) {
    return new Date(value);
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      return new Date(ast.value);
    }
    return null;
  },
});

// Custom scalar para JSON
const JSONScalar = new GraphQLScalarType({
  name: 'JSON',
  description: 'JSON custom scalar type',
  serialize: (value: any) => value,
  parseValue: (value: any) => value,
  parseLiteral: (ast: any) => {
    if (ast.kind === Kind.STRING) {
      try {
        return JSON.parse(ast.value);
      } catch {
        return null;
      }
    }
    return null;
  },
});

// ===== DATALOADERS PARA BATCH OPERATIONS =====

const createInfluencerLoader = () => new DataLoader(async (influencerIds: string[]) => {
  console.log(`🔄 DataLoader: Carregando ${influencerIds.length} influenciadores`);
  
  try {
    const influencers = await Promise.all(
      influencerIds.map(id => getInfluencerById(id))
    );
    return influencers;
  } catch (error) {
    console.error('Erro no DataLoader de influenciadores:', error);
    return influencerIds.map(() => null);
  }
});

const createFinancialLoader = () => new DataLoader(async (influencerIds: string[]) => {
  console.log(`💰 DataLoader: Carregando dados financeiros para ${influencerIds.length} influenciadores`);
  
  try {
    const financialData = await FinancialCacheService.getMultipleFinancialData(influencerIds);
    return influencerIds.map(id => financialData.get(id));
  } catch (error) {
    console.error('Erro no DataLoader de financeiros:', error);
    return influencerIds.map(() => null);
  }
});

const createCampaignLoader = () => new DataLoader(async (influencerIds: string[]) => {
  console.log(`📋 DataLoader: Carregando campanhas para ${influencerIds.length} influenciadores`);
  
  try {
    // Implementar busca de campanhas por influenciador
    const campaigns = await Promise.all(
      influencerIds.map(async (id) => {
        // Placeholder - implementar busca real de campanhas
        return [];
      })
    );
    return campaigns;
  } catch (error) {
    console.error('Erro no DataLoader de campanhas:', error);
    return influencerIds.map(() => []);
  }
});

const createBrandLoader = () => new DataLoader(async (influencerIds: string[]) => {
  console.log(`🏢 DataLoader: Carregando marcas para ${influencerIds.length} influenciadores`);
  
  try {
    // Implementar busca de marcas por influenciador
    const brands = await Promise.all(
      influencerIds.map(async (id) => {
        // Placeholder - implementar busca real de marcas
        return [];
      })
    );
    return brands;
  } catch (error) {
    console.error('Erro no DataLoader de marcas:', error);
    return influencerIds.map(() => []);
  }
});

// ===== RESOLVERS =====

export const resolvers = {
  // Custom scalars
  Date: DateScalar,
  JSON: JSONScalar,

  // ===== QUERY RESOLVERS =====
  Query: {
    // Influenciador individual
    async influencer(parent: any, { id }: { id: string }, context: any) {
      console.log(`🔍 Query: Buscando influenciador ${id}`);
      
      try {
        return await context.loaders.influencer.load(id);
      } catch (error) {
        console.error(`Erro ao buscar influenciador ${id}:`, error);
        throw new Error('Erro ao buscar influenciador');
      }
    },

    // Lista de influenciadores com filtros
    async influencers(parent: any, args: any, context: any) {
      const { filters = {}, pagination = {}, userId } = args;
      
      console.log(`🔍 Query: Buscando influenciadores para usuário ${userId}`, filters);
      
      try {
        // Implementar busca com filtros
        const allInfluencers = await getAllInfluencers();
        
        // Filtrar por userId
        let filteredInfluencers = allInfluencers.filter(inf => inf.userId === userId);
        
        // Garantir valores padrão para campos obrigatórios
        filteredInfluencers = filteredInfluencers.map(inf => ({
          ...inf,
          isVerified: inf.isVerified ?? false,
          isAvailable: inf.isAvailable ?? true,
          status: inf.status || 'active',
          // 🔧 COMPATIBILIDADE mainNetwork/mainPlatform
          mainNetwork: inf.mainNetwork || inf.mainPlatform,
          mainPlatform: inf.mainPlatform || inf.mainNetwork
        }));
        
        // Aplicar filtros
        if (filters.search) {
          const search = filters.search.toLowerCase();
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.name.toLowerCase().includes(search) ||
            inf.bio?.toLowerCase().includes(search)
          );
        }
        
        if (filters.category) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.category === filters.category
          );
        }
        
        if (filters.isAvailable !== undefined) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.isAvailable === filters.isAvailable
          );
        }
        
        if (filters.followersMin) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.totalFollowers >= filters.followersMin
          );
        }
        
        if (filters.followersMax) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.totalFollowers <= filters.followersMax
          );
        }
        
        if (filters.priceRange) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.pricing?.priceRange === filters.priceRange.toLowerCase()
          );
        }
        
        // Paginação
        const { limit = 20, offset = 0, orderBy = 'createdAt', orderDirection = 'DESC' } = pagination;
        
        // Ordenação
        filteredInfluencers.sort((a, b) => {
          const aValue = a[orderBy as keyof typeof a];
          const bValue = b[orderBy as keyof typeof b];
          
          if (orderDirection === 'DESC') {
            return bValue > aValue ? 1 : -1;
          }
          return aValue > bValue ? 1 : -1;
        });
        
        const totalCount = filteredInfluencers.length;
        const nodes = filteredInfluencers.slice(offset, offset + limit);
        
        return {
          nodes,
          totalCount,
          hasNextPage: offset + limit < totalCount,
          hasPreviousPage: offset > 0
        };
        
      } catch (error) {
        console.error('Erro ao buscar influenciadores:', error);
        throw new Error('Erro ao buscar influenciadores');
      }
    },

    // ✨ NOVA QUERY OTIMIZADA: Buscar múltiplos influenciadores por IDs
    async influencersByIds(parent: any, { ids, userId, proposalId }: { ids: string[], userId: string, proposalId?: string }, context: any) {
      const startTime = Date.now();
      console.log(`🚀 [GraphQL] influencersByIds: Iniciando busca otimizada`, {
        totalIds: ids.length,
        userId,
        timestamp: new Date().toISOString()
      });

      // 🛡️ VALIDAÇÕES DE ENTRADA
      const MAX_IDS_PER_QUERY = 50; // Limite para prevenir abuse
      const MIN_ID_LENGTH = 20; // IDs do Firebase têm pelo menos 20 chars
      
      const result = {
        influencers: [] as any[],
        foundIds: [] as string[],
        notFoundIds: [] as string[],
        invalidIds: [] as string[],
        totalRequested: ids.length,
        totalFound: 0,
        totalNotFound: 0,
        totalInvalid: 0,
        processingTimeMs: 0,
        hasPartialFailure: false,
        errors: [] as string[]
      };

      try {
        // ✅ Validação 1: Limite de quantidade
        if (ids.length === 0) {
          result.errors.push('Lista de IDs está vazia');
          result.processingTimeMs = Date.now() - startTime;
          return result;
        }

        if (ids.length > MAX_IDS_PER_QUERY) {
          result.errors.push(`Máximo de ${MAX_IDS_PER_QUERY} IDs por consulta. Recebido: ${ids.length}`);
          result.processingTimeMs = Date.now() - startTime;
          return result;
        }

        // ✅ Validação 2: IDs válidos
        const validIds = [];
        const invalidIds = [];

        for (const id of ids) {
          if (!id || typeof id !== 'string' || id.trim().length < MIN_ID_LENGTH) {
            invalidIds.push(id);
            result.errors.push(`ID inválido: ${id}`);
          } else {
            validIds.push(id.trim());
          }
        }

        result.invalidIds = invalidIds;
        result.totalInvalid = invalidIds.length;

        if (validIds.length === 0) {
          result.errors.push('Nenhum ID válido encontrado na lista');
          result.processingTimeMs = Date.now() - startTime;
          return result;
        }

        console.log(`✅ [GraphQL] Validação concluída: ${validIds.length} IDs válidos, ${invalidIds.length} inválidos`);

        // 🔍 BUSCA OTIMIZADA NO FIRESTORE
        // Usar getAll() que é mais eficiente que múltiplas consultas individuais
        const influencerRefs = validIds.map(id => db.collection('influencers').doc(id));
        
        console.log(`📞 [GraphQL] Executando getAll() para ${influencerRefs.length} referências...`);
        
        // ⏱️ TIMEOUT PROTECTION - Máximo 10 segundos
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Timeout: Consulta excedeu 10 segundos')), 10000);
        });

        const firebasePromise = db.getAll(...influencerRefs);
        const docs = await Promise.race([firebasePromise, timeoutPromise]) as any[];

        console.log(`📦 [GraphQL] Firestore retornou ${docs.length} documentos`);

        // 🔍 PROCESSAMENTO DOS RESULTADOS
        const foundInfluencers = [];
        const foundIds = [];
        const notFoundIds = [];

        for (let i = 0; i < docs.length; i++) {
          const doc = docs[i];
          const requestedId = validIds[i];

          if (!doc.exists) {
            notFoundIds.push(requestedId);
            console.log(`❌ [GraphQL] ID não encontrado: ${requestedId}`);
            continue;
          }

          const data = doc.data();
          
          // ✅ Validação 3: Ownership (influenciador pertence ao usuário ou acesso via proposta)
          let hasAccess = data.userId === userId;
          
          // 🔓 Se não tem ownership direto, verificar acesso via proposta (Clerk metadata)
          if (!hasAccess && proposalId) {
            try {
              // 1. Verificar ownership via ProposalService
              const { ProposalService } = await import('@/services/proposal-service');
              const isOwner = await ProposalService.canUserAccessProposal(proposalId, userId);
              
              if (isOwner) {
                hasAccess = true;
                console.log(`✅ [GraphQL] Acesso como proprietário da proposta ${proposalId} para influencer ${requestedId}`);
              } else {
                // 2. Verificar acesso via Clerk metadata (membro convidado)
                try {
                  const { clerkClient } = await import('@clerk/nextjs/server');
                  const clerk = await clerkClient();
                  const user = await clerk.users.getUser(userId);
                  
                  const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];
                  
                  if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
                    hasAccess = true;
                    console.log(`✅ [GraphQL] Acesso via Clerk metadata (${proposalAccess}) para proposta ${proposalId} influencer ${requestedId}`);
                  } else {
                    console.log(`❌ [GraphQL] Sem acesso via Clerk metadata para proposta ${proposalId} influencer ${requestedId}`);
                  }
                  
                } catch (clerkError) {
                  console.warn(`⚠️ [GraphQL] Erro ao verificar Clerk metadata:`, clerkError);
                }
              }
            } catch (proposalError) {
              console.error(`❌ [GraphQL] Erro ao verificar acesso via proposta:`, proposalError);
            }
          }
          
          if (!hasAccess) {
            notFoundIds.push(requestedId);
            const reason = proposalId 
              ? `ID ${requestedId} não acessível pelo usuário ${userId} (nem ownership nem acesso via proposta ${proposalId})`
              : `ID ${requestedId} não pertence ao usuário ${userId}`;
            result.errors.push(reason);
            console.log(`🚫 [GraphQL] Acesso negado para ${requestedId}:`, reason);
            continue;
          }

          // ✅ Processamento dos dados com fallbacks
          try {
            const influencer = {
              id: doc.id,
              ...data,
              // 🔧 VALORES PADRÃO ROBUSTOS
              rating: data.rating ?? 4.0,
              totalFollowers: data.totalFollowers ?? 0,
              engagementRate: data.engagementRate ?? 0,
              categories: data.categories ?? [],
              campaigns: data.campaigns ?? [],
              brands: data.brands ?? [],
              tags: data.tags ?? [],
              notes: data.notes ?? [],
              mainCategoriesData: data.mainCategoriesData ?? [],
              isVerified: data.isVerified ?? false,
              isAvailable: data.isAvailable ?? true,
              status: data.status ?? 'active',
              // 🔧 COMPATIBILIDADE mainNetwork/mainPlatform
              mainNetwork: data.mainNetwork || data.mainPlatform,
              mainPlatform: data.mainPlatform || data.mainNetwork,
              // 📊 PRICING DATA (será resolvido pelos nested resolvers)
              pricing: null, // Será preenchido pelos nested resolvers
              // 📊 DEMOGRAPHICS E BUDGETS (serão resolvidos pelos nested resolvers)
              currentDemographics: null, // Será preenchido pelo nested resolver
              budgets: null, // Será preenchido pelo nested resolver
              // 📅 DATAS SEGURAS
              createdAt: data.createdAt?.toDate?.() ?? new Date(),
              updatedAt: data.updatedAt?.toDate?.() ?? new Date()
            };

            foundInfluencers.push(influencer);
            foundIds.push(requestedId);
            console.log(`✅ [GraphQL] Processado com sucesso: ${data.name} (${requestedId})`);

          } catch (docError) {
            notFoundIds.push(requestedId);
            result.errors.push(`Erro ao processar ID ${requestedId}: ${docError.message}`);
            console.error(`❌ [GraphQL] Erro ao processar documento ${requestedId}:`, docError);
          }
        }

        // 📊 RESULTADO FINAL
        result.influencers = foundInfluencers;
        result.foundIds = foundIds;
        result.notFoundIds = notFoundIds;
        result.totalFound = foundIds.length;
        result.totalNotFound = notFoundIds.length;
        result.hasPartialFailure = notFoundIds.length > 0 || invalidIds.length > 0;
        result.processingTimeMs = Date.now() - startTime;

        console.log(`🎯 [GraphQL] influencersByIds concluído:`, {
          totalRequested: result.totalRequested,
          totalFound: result.totalFound,
          totalNotFound: result.totalNotFound,
          totalInvalid: result.totalInvalid,
          processingTimeMs: result.processingTimeMs,
          hasPartialFailure: result.hasPartialFailure
        });

        return result;

      } catch (error) {
        // 🚨 ERROR RECOVERY
        result.processingTimeMs = Date.now() - startTime;
        result.hasPartialFailure = true;
        
        if (error.message?.includes('Timeout')) {
          result.errors.push('Consulta expirou - tente novamente com menos IDs');
          console.error(`⏱️ [GraphQL] Timeout na consulta de múltiplos IDs:`, error);
        } else {
          result.errors.push(`Erro interno: ${error.message}`);
          console.error(`❌ [GraphQL] Erro crítico em influencersByIds:`, error);
        }

        // ✅ FALLBACK STRATEGY: Retornar resultado parcial mesmo com erro
        return result;
      }
    },

    // 🎯 NOVA QUERY: Buscar IDs de influencers de uma proposta
    async proposalInfluencers(parent: any, { proposalId, userId }: { proposalId: string, userId: string }, context: any) {
      const startTime = Date.now();
      console.log(`🎯 [GraphQL] proposalInfluencers iniciada:`, { proposalId, userId });

      // Garantir que sempre retornamos um objeto válido
      const result = {
        influencerIds: [] as string[],
        totalCount: 0,
        success: false,
        proposalId: proposalId || '',
        errors: [] as string[]
      };

      try {
        // Validar parâmetros obrigatórios
        if (!proposalId || !userId) {
          result.errors.push('proposalId e userId são obrigatórios');
          console.log('❌ [GraphQL] Parâmetros inválidos:', { proposalId, userId });
          return result;
        }

        // Verificar acesso à proposta (ownership direto + Clerk metadata)
        let hasAccess = false;
        
        // 1. Verificar ownership via ProposalService
        try {
          const { ProposalService } = await import('@/services/proposal-service');
          const isOwner = await ProposalService.canUserAccessProposal(proposalId, userId);
          
          if (isOwner) {
            hasAccess = true;
            console.log('✅ [GraphQL] Usuário é proprietário da proposta');
          }
        } catch (serviceError) {
          console.warn('⚠️ [GraphQL] Erro ao verificar ProposalService:', serviceError);
        }
        
        if (!hasAccess) {
          // 2. Verificar acesso via Clerk metadata (membro convidado)
          try {
            const { clerkClient } = await import('@clerk/nextjs/server');
            const clerk = await clerkClient();
            const user = await clerk.users.getUser(userId);
            
            const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];
            
            if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
              hasAccess = true;
              console.log(`✅ [GraphQL] Usuário tem acesso via Clerk metadata: ${proposalAccess}`);
            } else {
              console.log('❌ [GraphQL] Sem acesso via Clerk metadata');
            }
            
          } catch (clerkError) {
            console.warn('⚠️ [GraphQL] Erro ao verificar Clerk metadata:', clerkError);
          }
        }

        if (!hasAccess) {
          result.errors.push('Acesso negado à proposta');
          console.log('❌ [GraphQL] Acesso negado à proposta');
          return result;
        }

        // Buscar influencers da subcoleção da proposta
        try {
          const { db } = await import('@/lib/firebase-admin');
          const influencersSnapshot = await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .get();

          const influencerIds = influencersSnapshot.docs.map(doc => doc.id);
          
          result.influencerIds = influencerIds;
          result.totalCount = influencerIds.length;
          result.success = true;

          console.log(`✅ [GraphQL] proposalInfluencers concluída:`, {
            proposalId,
            totalCount: result.totalCount,
            processingTimeMs: Date.now() - startTime
          });

        } catch (firebaseError) {
          result.errors.push(`Erro ao acessar Firebase: ${firebaseError.message}`);
          console.error('❌ [GraphQL] Erro Firebase em proposalInfluencers:', firebaseError);
        }

        return result;

      } catch (error: any) {
        result.errors.push(`Erro interno: ${error?.message || 'Erro desconhecido'}`);
        console.error('❌ [GraphQL] Erro geral em proposalInfluencers:', error);
        return result;
      }
    },

    // Buscar por faixa de preço
    async influencersByPriceRange(parent: any, args: any, context: any) {
      const { priceRange, userId, limit = 20 } = args;
      
      console.log(`💰 Query: Buscando influenciadores por faixa de preço ${priceRange}`);
      
      try {
        return await FinancialDenormalizationService.getInfluencersByPriceRange(
          priceRange.toLowerCase(),
          userId,
          limit
        );
      } catch (error) {
        console.error('Erro ao buscar por faixa de preço:', error);
        throw new Error('Erro ao buscar influenciadores por preço');
      }
    },

    // Buscar por valores específicos
    async influencersByPriceValues(parent: any, args: any, context: any) {
      const { minPrice, maxPrice, userId, limit = 20 } = args;
      
      console.log(`💰 Query: Buscando influenciadores entre R$ ${minPrice} e R$ ${maxPrice}`);
      
      try {
        return await FinancialDenormalizationService.getInfluencersByPriceValues(
          minPrice,
          maxPrice,
          userId,
          limit
        );
      } catch (error) {
        console.error('Erro ao buscar por valores de preço:', error);
        throw new Error('Erro ao buscar influenciadores por valores');
      }
    },

    // Buscar por plataforma principal
    async influencersByPlatform(parent: any, args: any, context: any) {
      const { platform, userId, limit = 20 } = args;
      
      console.log(`📱 Query: Buscando influenciadores por plataforma ${platform}`);
      
      try {
        return await FinancialDenormalizationService.getInfluencersByMainPlatform(
          platform.toLowerCase(),
          userId,
          limit
        );
      } catch (error) {
        console.error('Erro ao buscar por plataforma:', error);
        throw new Error('Erro ao buscar influenciadores por plataforma');
      }
    },

    // Dados financeiros individual
    async influencerFinancial(parent: any, { influencerId }: { influencerId: string }, context: any) {
      console.log(`💰 Query: Buscando dados financeiros para ${influencerId}`);
      
      try {
        return await context.loaders.financial.load(influencerId);
      } catch (error) {
        console.error(`Erro ao buscar dados financeiros:`, error);
        throw new Error('Erro ao buscar dados financeiros');
      }
    },

    // Estatísticas financeiras
    async financialStats(parent: any, { userId }: { userId: string }, context: any) {
      console.log(`📊 Query: Buscando estatísticas financeiras para usuário ${userId}`);
      
      try {
        return await FinancialDenormalizationService.getPricingStats(userId);
      } catch (error) {
        console.error('Erro ao buscar estatísticas:', error);
        throw new Error('Erro ao buscar estatísticas financeiras');
      }
    },

    // Estatísticas do cache
    async cacheStats(parent: any, args: any, context: any) {
      console.log(`📊 Query: Buscando estatísticas do cache`);
      
      try {
        return FinancialCacheService.getCacheStats();
      } catch (error) {
        console.error('Erro ao buscar stats do cache:', error);
        throw new Error('Erro ao buscar estatísticas do cache');
      }
    },

    // Dashboard aggregated data
    async dashboardData(parent: any, { userId }: { userId: string }, context: any) {
      console.log(`📊 Query: Carregando dados do dashboard para usuário ${userId}`);
      
      try {
        const [
          allInfluencers,
          financialStats
        ] = await Promise.all([
          getAllInfluencers(),
          FinancialDenormalizationService.getPricingStats(userId)
        ]);

        const userInfluencers = allInfluencers.filter(inf => inf.userId === userId);
        const totalInfluencers = userInfluencers.length;
        const totalWithFinancialData = userInfluencers.filter(inf => inf.pricing?.hasFinancialData).length;

        // Recent influencers (últimos 10)
        const recentInfluencers = userInfluencers
          .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
          .slice(0, 10);

        // Top performers por engajamento
        const topPerformers = userInfluencers
          .filter(inf => inf.engagementRate > 0)
          .sort((a, b) => b.engagementRate - a.engagementRate)
          .slice(0, 10);

        return {
          totalInfluencers,
          totalWithFinancialData,
          totalCampaigns: 0, // Placeholder
          totalBrands: 0, // Placeholder
          financialStats,
          recentInfluencers,
          topPerformers
        };
      } catch (error) {
        console.error('Erro ao carregar dashboard:', error);
        throw new Error('Erro ao carregar dados do dashboard');
      }
    },

    // Buscar screenshots de um influenciador
    async influencerScreenshots(parent: any, { influencerId, platform }: { influencerId: string, platform?: string }, context: any) {
      console.log(`📸 Query: Buscando screenshots do influenciador ${influencerId}`, platform ? `para ${platform}` : '');
      
      try {
        const screenshotsCollection = db
          .collection('influencers')
          .doc(influencerId)
          .collection('screenshots');

        let screenshots: any[] = [];

        if (platform) {
          // Buscar apenas uma plataforma específica
          const platformDoc = await screenshotsCollection.doc(platform).get();
          
          if (platformDoc.exists) {
            const data = platformDoc.data();
            const urls = data?.urls || [];
            
            // Converter cada URL em um objeto Screenshot
            urls.forEach((url: string, index: number) => {
              screenshots.push({
                id: `${influencerId}_${platform}_${index}`,
                influencerId: influencerId,
                platform: platform,
                url: url,
                filename: `screenshot_${index}.jpg`,
                size: 0,
                contentType: 'image/jpeg',
                uploadedAt: data.lastUpdated?.toDate?.() || new Date(),
                uploadedBy: 'system'
              });
            });
          }
        } else {
          // Buscar todas as plataformas
          const snapshot = await screenshotsCollection.get();
          
          snapshot.docs.forEach(doc => {
            const data = doc.data();
            const urls = data?.urls || [];
            const platformName = doc.id; // O ID do documento é o nome da plataforma
            
            // Converter cada URL em um objeto Screenshot
            urls.forEach((url: string, index: number) => {
              screenshots.push({
                id: `${influencerId}_${platformName}_${index}`,
                influencerId: influencerId,
                platform: platformName,
                url: url,
                filename: `screenshot_${index}.jpg`,
                size: 0,
                contentType: 'image/jpeg',
                uploadedAt: data.lastUpdated?.toDate?.() || new Date(),
                uploadedBy: 'system'
              });
            });
          });
        }

        console.log(`📸 Query: Encontrados ${screenshots.length} screenshots`);
        return screenshots;
      } catch (error) {
        console.error('Erro ao buscar screenshots:', error);
        // Retornar array vazio em caso de erro ao invés de throw
        return [];
      }
    },

    // Marcas do usuário
    async brands(parent: any, { userId }: { userId: string }, context: any) {
      console.log(`🏢 Query: Buscando marcas para usuário ${userId}`);
      
      try {
        const db = getFirestore();
        const brandsRef = collection(db, 'brands');
        const q = query(
          brandsRef,
          where('userId', '==', userId)
        );
        
        const snapshot = await getDocs(q);
        
        const brands = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));
        
        console.log(`🏢 Query: Encontradas ${brands.length} marcas para usuário ${userId}`);
        
        return brands;
      } catch (error) {
        console.error('Erro ao buscar marcas:', error);
        throw new Error('Erro ao buscar marcas');
      }
    },

    // Categorias do usuário
    async categories(parent: any, { userId }: { userId?: string }, context: any) {
      console.log(`🏷️ Query: Buscando categorias para usuário ${userId}`);
      
      try {
        const categoriesRef = db.collection('categories');
        
        // Se userId for fornecido, buscar categorias específicas do usuário
        // Caso contrário, buscar categorias globais + do usuário (se autenticado)
        let query;
        if (userId) {
          query = categoriesRef
            .where('userId', '==', userId)
            .where('isActive', '==', true)
            .orderBy('name', 'asc');
        } else {
          // Buscar categorias globais (sem userId ou userId null)
          query = categoriesRef
            .where('isActive', '==', true)
            .orderBy('name', 'asc');
        }
        
        const snapshot = await query.get();
        
        const categories = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));
        
        console.log(`🏷️ Query: Encontradas ${categories.length} categorias`);
        
        return categories;
      } catch (error) {
        console.error('Erro ao buscar categorias:', error);
        // Retornar array vazio em caso de erro ao invés de throw
        return [];
      }
    },

    // Categorias específicas do usuário
    async userCategories(parent: any, { userId }: { userId: string }, context: any) {
      console.log(`🏷️ Query: Buscando categorias específicas do usuário ${userId}`);
      
      if (!userId) {
        console.warn('❌ userCategories: userId é obrigatório');
        return [];
      }
      
      try {
        const categoriesRef = db.collection('categories');
        const query = categoriesRef
          .where('userId', '==', userId)
          .where('isActive', '==', true)
          .orderBy('name', 'asc');
        
        const snapshot = await query.get();
        
        const categories = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));
        
        console.log(`🏷️ Query: Encontradas ${categories.length} categorias do usuário ${userId}`);
        
        return categories;
      } catch (error) {
        console.error('Erro ao buscar categorias do usuário:', error);
        // Retornar array vazio em caso de erro ao invés de throw
        return [];
      }
    },

    // Categoria individual
    async category(parent: any, { id, userId }: { id: string, userId: string }, context: any) {
      console.log(`🏷️ Query: Buscando categoria ${id} para usuário ${userId}`);
      
      if (!id || !userId) {
        console.warn('❌ category: id e userId são obrigatórios');
        return null;
      }
      
      try {
        const categoryRef = db.collection('categories').doc(id);
        const categorySnap = await categoryRef.get();
        
        if (!categorySnap.exists) {
          console.log(`❌ Categoria ${id} não encontrada`);
          return null;
        }
        
        const categoryData = categorySnap.data();
        
        // Verificar se a categoria pertence ao usuário ou é global
        if (categoryData.userId && categoryData.userId !== userId) {
          console.log(`❌ Categoria ${id} não pertence ao usuário ${userId}`);
          return null;
        }
        
        const category = {
          id: categorySnap.id,
          ...categoryData,
          createdAt: categoryData.createdAt?.toDate?.() || new Date(),
          updatedAt: categoryData.updatedAt?.toDate?.() || new Date()
        };
        
        console.log(`✅ Categoria encontrada: ${category.name}`);
        
        return category;
      } catch (error) {
        console.error('Erro ao buscar categoria:', error);
        return null;
      }
    },Ref,
            where('isActive', '==', true),
            orderBy('name', 'asc')
          );
        }
        
        const snapshot = await getDocs(q);
        
        const categories = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));
        
        console.log(`🏷️ Query: Encontradas ${categories.length} categorias`);
        
        return categories;
      } catch (error) {
        console.error('Erro ao buscar categorias:', error);
        // Retornar array vazio em caso de erro ao invés de throw
        return [];
      }
    },

    // Categorias específicas do usuário
    async userCategories(parent: any, { userId }: { userId: string }, context: any) {
      console.log(`🏷️ Query: Buscando categorias específicas do usuário ${userId}`);
      
      if (!userId) {
        console.warn('❌ userCategories: userId é obrigatório');
        return [];
      }
      
      try {
        const db = getFirestore();
        const categoriesRef = collection(db, 'categories');
        const q = query(
          categoriesRef,
          where('userId', '==', userId),
          where('isActive', '==', true),
          orderBy('name', 'asc')
        );
        
        const snapshot = await getDocs(q);
        
        const categories = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));
        
        console.log(`🏷️ Query: Encontradas ${categories.length} categorias do usuário ${userId}`);
        
        return categories;
      } catch (error) {
        console.error('Erro ao buscar categorias do usuário:', error);
        // Retornar array vazio em caso de erro ao invés de throw
        return [];
      }
    },

    // Categoria individual
    async category(parent: any, { id, userId }: { id: string, userId: string }, context: any) {
      console.log(`🏷️ Query: Buscando categoria ${id} para usuário ${userId}`);
      
      if (!id || !userId) {
        console.warn('❌ category: id e userId são obrigatórios');
        return null;
      }
      
      try {
        const db = getFirestore();
        const categoryRef = doc(db, 'categories', id);
        const categorySnap = await getDoc(categoryRef);
        
        if (!categorySnap.exists()) {
          console.log(`❌ Categoria ${id} não encontrada`);
          return null;
        }
        
        const categoryData = categorySnap.data();
        
        // Verificar se a categoria pertence ao usuário ou é global
        if (categoryData.userId && categoryData.userId !== userId) {
          console.log(`❌ Categoria ${id} não pertence ao usuário ${userId}`);
          return null;
        }
        
        const category = {
          id: categorySnap.id,
          ...categoryData,
          createdAt: categoryData.createdAt?.toDate?.() || new Date(),
          updatedAt: categoryData.updatedAt?.toDate?.() || new Date()
        };
        
        console.log(`✅ Categoria encontrada: ${category.name}`);
        
        return category;
      } catch (error) {
        console.error('Erro ao buscar categoria:', error);
        return null;
      }
    },
    
    // Associações Marca-Influenciador
    async brandInfluencerAssociations(parent: any, args: any, context: any) {
      const { userId, influencerId, brandId, status } = args;
      
      console.log(`🤝 Query: Buscando associações marca-influenciador`, { userId, influencerId, brandId, status });
      
      // Validação básica - userId é obrigatório
      if (!userId) {
        console.error('❌ brandInfluencerAssociations: userId é obrigatório');
        return []; // Retornar array vazio em vez de erro
      }
      
      try {
        const db = getFirestore();
        const associationsRef = collection(db, 'brandInfluencerAssociations');
        let q = query(associationsRef, where('userId', '==', userId));
        
        if (influencerId) {
          q = query(q, where('influencerId', '==', influencerId));
        }
        
        if (brandId) {
          q = query(q, where('brandId', '==', brandId));
        }
        
        if (status) {
          q = query(q, where('status', '==', status));
        }
        
        console.log(`🤝 Query: Executando query no Firestore...`);
        const snapshot = await getDocs(q);
        
        const associations = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            userId: data.userId || userId,
            brandId: data.brandId || '',
            brandName: data.brandName || 'Nome não informado',
            brandLogo: data.brandLogo || null,
            influencerId: data.influencerId || '',
            influencerName: data.influencerName || 'Nome não informado',
            influencerAvatar: data.influencerAvatar || null,
            status: data.status || 'active',
            tags: data.tags || [],
            notes: data.notes || null,
            createdAt: data.createdAt?.toDate?.() ?? new Date(),
            updatedAt: data.updatedAt?.toDate?.() ?? new Date(),
            createdBy: data.createdBy || 'system',
            updatedBy: data.updatedBy || 'system'
          };
        });
        
        console.log(`✅ Encontradas ${associations.length} associações para userId: ${userId}`);
        return associations;
        
      } catch (error) {
        console.error('❌ Erro ao buscar associações marca-influenciador:', error);
        console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'Sem stack trace');
        // Em caso de erro, retornar array vazio em vez de lançar erro
        // para evitar quebrar a query
        console.log('⚠️ Retornando array vazio devido ao erro');
        return [];
      }
    },
    
    async brandInfluencerAssociation(parent: any, { id }: { id: string }, context: any) {
      console.log(`🤝 Query: Buscando associação ${id}`);
      
      try {
        const db = getFirestore();
        const associationRef = doc(db, 'brandInfluencerAssociations', id);
        const associationSnap = await getDoc(associationRef);
        
        if (!associationSnap.exists()) {
          return null;
        }
        
        const data = associationSnap.data();
        return {
          id: associationSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate?.() ?? new Date(),
          updatedAt: data.updatedAt?.toDate?.() ?? new Date(),
        };
        
      } catch (error) {
        console.error(`Erro ao buscar associação ${id}:`, error);
        throw new Error('Erro ao buscar associação');
      }
    },

    // Orçamentos do usuário
    async budgets(parent: any, args: any, context: any) {
      const { userId, influencerId, brandId, status, serviceType } = args;
      
      console.log(`💰 Query: Buscando orçamentos para:`, { userId, influencerId, brandId, status, serviceType });
      
      try {
        const db = getFirestore();
        const budgetsRef = collection(db, 'budgets');
        let q = query(
          budgetsRef,
          where('userId', '==', userId),
          orderBy('createdAt', 'desc')
        );
        
        const snapshot = await getDocs(q);
        
        let budgets = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt),
            updatedAt: data.updatedAt?.toDate?.() || new Date(data.updatedAt),
            expiresAt: data.expiresAt?.toDate?.() || (data.expiresAt ? new Date(data.expiresAt) : null),
            hasCounterProposal: data.hasCounterProposal || false
          };
        });
        
        // Aplicar filtros
        if (influencerId) {
          budgets = budgets.filter(budget => budget.influencerId === influencerId);
        }
        
        if (brandId) {
          budgets = budgets.filter(budget => budget.brandId === brandId);
        }
        
        if (status) {
          budgets = budgets.filter(budget => budget.status === status);
        }
        
        if (serviceType) {
          budgets = budgets.filter(budget => budget.serviceType === serviceType);
        }
        
        console.log(`💰 Query: Encontrados ${budgets.length} orçamentos filtrados`);
        
        return budgets;
      } catch (error) {
        console.error('Erro ao buscar orçamentos:', error);
        throw new Error('Erro ao buscar orçamentos');
      }
    },

    // Orçamento individual
    async budget(parent: any, { id }: { id: string }, context: any) {
      console.log(`💰 Query: Buscando orçamento ${id}`);
      
      try {
        const db = getFirestore();
        const budgetRef = doc(db, 'budgets', id);
        const budgetSnap = await getDoc(budgetRef);
        
        if (!budgetSnap.exists()) {
          return null;
        }
        
        const data = budgetSnap.data();
        return {
          id: budgetSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt),
          updatedAt: data.updatedAt?.toDate?.() || new Date(data.updatedAt),
          expiresAt: data.expiresAt?.toDate?.() || (data.expiresAt ? new Date(data.expiresAt) : null),
          hasCounterProposal: data.hasCounterProposal || false
        };
      } catch (error) {
        console.error('Erro ao buscar orçamento:', error);
        throw new Error('Erro ao buscar orçamento');
      }
    },

    // Estatísticas de orçamentos
    async budgetStats(parent: any, { userId, brandId }: { userId: string; brandId?: string }, context: any) {
      console.log(`📊 Query: Buscando estatísticas de orçamentos para usuário ${userId}`, brandId ? `e marca ${brandId}` : '');
      
      try {
        const db = getFirestore();
        const budgetsRef = collection(db, 'budgets');
        let q = query(budgetsRef, where('userId', '==', userId));
        
        if (brandId) {
          q = query(budgetsRef, where('userId', '==', userId), where('brandId', '==', brandId));
        }
        
        const snapshot = await getDocs(q);
        const budgets = snapshot.docs.map(doc => doc.data());
        
        // Calcular estatísticas
        const totalBudgets = budgets.length;
        const totalValue = budgets.reduce((sum, budget) => sum + (budget.amount || 0), 0);
        const averageValue = totalBudgets > 0 ? totalValue / totalBudgets : 0;
        
        // Agrupar por status
        const byStatus = budgets.reduce((acc, budget) => {
          const status = budget.status || 'draft';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {} as any);
        
        // Agrupar por tipo de serviço
        const byServiceType = budgets.reduce((acc, budget) => {
          const serviceType = budget.serviceType || 'personalizado';
          acc[serviceType] = (acc[serviceType] || 0) + 1;
          return acc;
        }, {} as any);
        
        // Taxa de conversão (aprovados / total)
        const approvedCount = byStatus['approved'] || 0;
        const conversionRate = totalBudgets > 0 ? approvedCount / totalBudgets : 0;
        
        return {
          totalBudgets,
          totalValue,
          averageValue,
          byStatus: Object.entries(byStatus).map(([status, count]) => ({ status, count })),
          byServiceType: Object.entries(byServiceType).map(([serviceType, count]) => ({ serviceType, count })),
          byBrand: [], // Implementar se necessário
          conversionRate
        };
      } catch (error) {
        console.error('Erro ao buscar estatísticas de orçamentos:', error);
        throw new Error('Erro ao buscar estatísticas de orçamentos');
      }
    }
  },

  // ===== NESTED RESOLVERS =====
  Influencer: {
    // Resolver para dados financeiros
    async financial(parent: any, args: any, context: any) {
      if (!parent.id) return null;
      
      console.log(`💰 Nested: Carregando dados financeiros para influenciador ${parent.id}`);
      
      try {
        return await context.loaders.financial.load(parent.id);
      } catch (error) {
        console.error('Erro ao carregar dados financeiros nested:', error);
        return null;
      }
    },

    // Resolver para campanhas
    async campaigns(parent: any, args: any, context: any) {
      if (!parent.id) return [];
      
      console.log(`📋 Nested: Carregando campanhas para influenciador ${parent.id}`);
      
      try {
        return await context.loaders.campaigns.load(parent.id);
      } catch (error) {
        console.error('Erro ao carregar campanhas nested:', error);
        return [];
      }
    },

    // Resolver para marcas
    async brands(parent: any, args: any, context: any) {
      if (!parent.id) return [];
      
      console.log(`🏢 Nested: Carregando marcas para influenciador ${parent.id}`);
      
      try {
        return await context.loaders.brands.load(parent.id);
      } catch (error) {
        console.error('Erro ao carregar marcas nested:', error);
        return [];
      }
    },

    // Resolver para tags - placeholder
    async tags(parent: any, args: any, context: any) {
      return []; // Implementar busca de tags
    },

    // Resolver para notes - placeholder
    async notes(parent: any, args: any, context: any) {
      return []; // Implementar busca de notes
    },

    // Resolver para categorias principais
    async mainCategoriesData(parent: any, args: any, context: any) {
      // Implementar busca de categorias por IDs
      return [];
    },

    // Resolver para demographics atuais
    async currentDemographics(parent: any, args: any, context: any) {
      const influencerId = parent.id;

      console.log(`📊 [DEBUG] Nested Resolver: Iniciando busca de demographics para influencer ${influencerId}`);

      try {
        // Buscar demographics na subcoleção: influencers/{influencerId}/demographics
        const demographicsRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('demographics');

        const demographicsSnapshot = await demographicsRef
          .where('isActive', '==', true)
          .orderBy('createdAt', 'desc')
          .get();

        console.log(`📊 [DEBUG] Demographics encontrados: ${demographicsSnapshot.docs.length}`);

        const demographics = demographicsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
            captureDate: data.captureDate?.toDate() || new Date()
          };
        });

        console.log(`✅ [DEBUG] Demographics processados para ${influencerId}:`, demographics.length);

        return demographics;

      } catch (error) {
        console.error(`❌ [DEBUG] Erro ao buscar demographics para ${influencerId}:`, error);
        return [];
      }
    },

    // Resolver para orçamentos organizados por plataforma
    async budgets(parent: any, args: any, context: any) {
      const influencerId = parent.id;
      
      console.log(`💰 [DEBUG] Nested Resolver: Iniciando busca de orçamentos para influencer ${influencerId}`);
      console.log(`💰 [DEBUG] Parent data:`, { id: parent.id, name: parent.name });
      
      try {
        // Buscar orçamentos na nova estrutura: influencers/{influencerId}/budgets/{platform}
        const budgetsRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets');
        
        console.log(`💰 [DEBUG] Referência criada: influencers/${influencerId}/budgets`);
        
        const budgetsSnapshot = await budgetsRef.get();
        
        console.log(`💰 [DEBUG] Snapshot obtido. Documentos encontrados: ${budgetsSnapshot.docs.length}`);
        
        // Organizar orçamentos por plataforma
        const budgetsByPlatform = {
          instagram: [],
          tiktok: [],
          youtube: [],
          facebook: [],
          twitch: [],
          kwai: [],
          personalizado: []
        };
        
        budgetsSnapshot.docs.forEach((doc, index) => {
          const platform = doc.id; // Nome do documento é a plataforma
          const budgetData = doc.data();
          
          console.log(`💰 [DEBUG] Processando documento ${index + 1}: platform=${platform}`, budgetData);
          
          const budget = {
            id: doc.id,
            ...budgetData,
            createdAt: budgetData.createdAt?.toDate() || new Date(),
            updatedAt: budgetData.updatedAt?.toDate() || new Date()
          };
          
          // Adicionar ao array da plataforma correspondente
          if (budgetsByPlatform.hasOwnProperty(platform)) {
            budgetsByPlatform[platform].push(budget);
            console.log(`💰 [DEBUG] Orçamento adicionado à plataforma ${platform}`);
          } else {
            // Se a plataforma não estiver mapeada, adicionar em personalizado
            budgetsByPlatform.personalizado.push(budget);
            console.log(`💰 [DEBUG] Orçamento adicionado à plataforma personalizado (original: ${platform})`);
          }
        });
        
        console.log(`✅ [DEBUG] Orçamentos organizados para ${influencerId}:`, {
          instagram: budgetsByPlatform.instagram.length,
          tiktok: budgetsByPlatform.tiktok.length,
          youtube: budgetsByPlatform.youtube.length,
          facebook: budgetsByPlatform.facebook.length,
          twitch: budgetsByPlatform.twitch.length,
          kwai: budgetsByPlatform.kwai.length,
          personalizado: budgetsByPlatform.personalizado.length
        });
        
        console.log(`💰 [DEBUG] Retornando resultado final:`, budgetsByPlatform);
        
        return budgetsByPlatform;
        
      } catch (error) {
        console.error(`❌ [DEBUG] Erro detalhado ao buscar orçamentos para ${influencerId}:`, error);
        console.error(`❌ [DEBUG] Stack trace:`, error.stack);
        
        // Retornar estrutura vazia em caso de erro
        return {
          instagram: [],
          tiktok: [],
          youtube: [],
          facebook: [],
          twitch: [],
          kwai: [],
          personalizado: []
        };
      }
    }
  },

  // ===== MUTATION RESOLVERS =====
  Mutation: {
    // Criar influenciador
    async createInfluencer(parent: any, { input }: any, context: any) {
      console.log('🆕 Mutation: Criando novo influenciador', input);
      
      try {
        const influencer = await addInfluencer(input);
        
        // Publish para subscription
        pubsub.publish('INFLUENCER_CREATED', {
          influencerCreated: influencer,
          userId: context.user?.id
        });
        
        return influencer;
      } catch (error) {
        console.error('Erro ao criar influenciador:', error);
        throw new Error('Erro ao criar influenciador');
      }
    },

    // Atualizar influenciador
    async updateInfluencer(parent: any, { id, input }: any, context: any) {
      console.log(`✏️ Mutation: Atualizando influenciador ${id}`, input);
      
      try {
        const influencer = await updateInfluencer(id, input);
        
        // Invalidar cache
        context.loaders.influencer.clear(id);
        
        // Publish para subscription
        pubsub.publish('INFLUENCER_UPDATED', {
          influencerUpdated: influencer,
          userId: context.user?.id
        });
        
        return influencer;
      } catch (error) {
        console.error('Erro ao atualizar influenciador:', error);
        throw new Error('Erro ao atualizar influenciador');
      }
    },

    // Deletar influenciador
    async deleteInfluencer(parent: any, { id }: { id: string }, context: any) {
      console.log(`🗑️ Mutation: Deletando influenciador ${id}`);
      
      try {
        await deleteInfluencer(id);
        
        // Limpar caches
        context.loaders.influencer.clear(id);
        context.loaders.financial.clear(id);
        FinancialCacheService.invalidateInfluencer(id);
        
        // Publish para subscription
        pubsub.publish('INFLUENCER_DELETED', {
          influencerDeleted: id,
          userId: context.user?.id
        });
        
        return true;
      } catch (error) {
        console.error('Erro ao deletar influenciador:', error);
        throw new Error('Erro ao deletar influenciador');
      }
    },

    // Criar dados financeiros
    async createInfluencerFinancial(parent: any, { input }: any, context: any) {
      console.log('💰 Mutation: Criando dados financeiros', input);
      
      try {
        const financial = await addFinancial(input);
        
        // Sincronizar dados denormalizados
        await FinancialDenormalizationService.syncFinancialData(input.influencerId, financial);
        
        // Invalidar caches
        context.loaders.financial.clear(input.influencerId);
        context.loaders.influencer.clear(input.influencerId);
        
        return financial;
      } catch (error) {
        console.error('Erro ao criar dados financeiros:', error);
        throw new Error('Erro ao criar dados financeiros');
      }
    },

    // Atualizar dados financeiros
    async updateInfluencerFinancial(parent: any, { id, input }: any, context: any) {
      console.log(`💰 Mutation: Atualizando dados financeiros ${id}`, input);
      
      try {
        const financial = await updateFinancial(id, input);
        
        // Buscar ID do influenciador
        const existingFinancial = await getFinancialByInfluencerId(financial.influencerId);
        if (existingFinancial) {
          // Sincronizar dados denormalizados
          await FinancialDenormalizationService.syncFinancialData(existingFinancial.influencerId, financial);
          
          // Invalidar caches
          context.loaders.financial.clear(existingFinancial.influencerId);
          context.loaders.influencer.clear(existingFinancial.influencerId);
        }
        
        return financial;
      } catch (error) {
        console.error('Erro ao atualizar dados financeiros:', error);
        throw new Error('Erro ao atualizar dados financeiros');
      }
    },

    // Sincronizar dados financeiros denormalizados
    async syncInfluencerFinancialData(parent: any, { influencerId }: { influencerId: string }, context: any) {
      console.log(`🔄 Mutation: Sincronizando dados financeiros para ${influencerId}`);
      
      try {
        const success = await FinancialDenormalizationService.updateInfluencerPricing(influencerId);
        
        if (success) {
          // Invalidar caches
          context.loaders.financial.clear(influencerId);
          context.loaders.influencer.clear(influencerId);
        }
        
        return success;
      } catch (error) {
        console.error('Erro ao sincronizar dados financeiros:', error);
        throw new Error('Erro ao sincronizar dados financeiros');
      }
    },

    // Sincronização em lote
    async bulkSyncFinancialData(parent: any, { influencerIds }: { influencerIds: string[] }, context: any) {
      console.log(`🔄 Mutation: Sincronização em lote para ${influencerIds.length} influenciadores`);
      
      try {
        const result = await FinancialDenormalizationService.updateMultipleInfluencersPricing(influencerIds);
        
        // Limpar caches para todos
        influencerIds.forEach(id => {
          context.loaders.financial.clear(id);
          context.loaders.influencer.clear(id);
        });
        
        return {
          successful: result.success,
          failed: result.failed,
          total: influencerIds.length
        };
      } catch (error) {
        console.error('Erro na sincronização em lote:', error);
        throw new Error('Erro na sincronização em lote');
      }
    },

    // Criar associação marca-influenciador
    async createBrandInfluencerAssociation(parent: any, { input }: any, context: any) {
      console.log('🤝 Mutation: Criando associação marca-influenciador', input);
      
      try {
        const db = getFirestore();
        const associationsRef = collection(db, 'brandInfluencerAssociations');
        
        // Buscar dados da marca e do influenciador
        const brandRef = doc(db, 'brands', input.brandId);
        const influencerRef = doc(db, 'influencers', input.influencerId);
        
        const [brandSnap, influencerSnap] = await Promise.all([
          getDoc(brandRef),
          getDoc(influencerRef)
        ]);
        
        if (!brandSnap.exists()) {
          throw new Error('Marca não encontrada');
        }
        
        if (!influencerSnap.exists()) {
          throw new Error('Influenciador não encontrado');
        }
        
        const brandData = brandSnap.data();
        const influencerData = influencerSnap.data();
        
        const associationData = {
          userId: context.user?.id || brandData.userId, // Usar userId do context ou da marca
          brandId: input.brandId,
          brandName: brandData.name,
          brandLogo: brandData.logo || null,
          influencerId: input.influencerId,
          influencerName: influencerData.name,
          influencerAvatar: influencerData.avatar || null,
          status: input.status || 'active',
          tags: input.tags || [],
          notes: input.notes || null,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: context.user?.id || 'system',
          updatedBy: context.user?.id || 'system'
        };
        
        const docRef = await associationsRef.add(associationData);
        
        const result = {
          id: docRef.id,
          ...associationData
        };
        
        console.log(`✅ Associação criada com sucesso: ${docRef.id}`);
        return result;
        
      } catch (error) {
        console.error('Erro ao criar associação:', error);
        throw new Error('Erro ao criar associação marca-influenciador');
      }
    },
    
    // Deletar associação marca-influenciador
    async deleteBrandInfluencerAssociation(parent: any, { id }: { id: string }, context: any) {
      console.log(`🗑️ Mutation: Deletando associação ${id}`);
      
      try {
        const db = getFirestore();
        const associationRef = doc(db, 'brandInfluencerAssociations', id);
        
        // Verificar se a associação existe
        const associationSnap = await getDoc(associationRef);
        if (!associationSnap.exists()) {
          throw new Error('Associação não encontrada');
        }
        
        // Deletar a associação
        await associationRef.delete();
        
        console.log(`✅ Associação ${id} deletada com sucesso`);
        return true;
        
      } catch (error) {
        console.error(`Erro ao deletar associação ${id}:`, error);
        throw new Error('Erro ao deletar associação');
      }
    },

    // Upload de screenshot
    async uploadScreenshot(parent: any, { input }: any, context: any) {
      const timestamp = Date.now();
      
      console.log('📸 [GraphQL] Mutation uploadScreenshot iniciada');
      console.log('📸 [GraphQL] Input recebido:', JSON.stringify({
        influencerId: input?.influencerId,
        platform: input?.platform,
        filename: input?.filename,
        hasFileData: !!input?.fileData
      }));

      try {
        // Validação de entrada obrigatória
        if (!input) {
          throw new Error('Input não fornecido para upload de screenshot');
        }

        if (!input.influencerId || !input.platform || !input.filename) {
          throw new Error(`Dados obrigatórios em falta: influencerId(${!!input.influencerId}), platform(${!!input.platform}), filename(${!!input.filename})`);
        }

        console.log('✅ [GraphQL] Validação inicial passou');

        // Operação no Firestore
        const platformDocRef = db
          .collection('influencers')
          .doc(input.influencerId)
          .collection('screenshots')
          .doc(input.platform);

        console.log('📸 [GraphQL] Referência Firestore criada:', `influencers/${input.influencerId}/screenshots/${input.platform}`);

        // Buscar documento existente
        const platformDoc = await platformDocRef.get();
        
        let currentUrls: string[] = [];
        if (platformDoc.exists) {
          const data = platformDoc.data();
          currentUrls = data?.urls || [];
          console.log('📸 [GraphQL] URLs existentes encontradas:', currentUrls.length);
        } else {
          console.log('📸 [GraphQL] Criando novo documento para plataforma');
        }

        // Gerar URL mock (para desenvolvimento) - manter nome original do arquivo
        const mockUrl = `https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/${input.influencerId}/screenshots/${input.platform}/${input.filename}`;
        const updatedUrls = [...currentUrls, mockUrl];

        // Atualizar Firestore
        await platformDocRef.set({
          platform: input.platform,
          urls: updatedUrls,
          lastUpdated: new Date(),
          totalScreenshots: updatedUrls.length
        }, { merge: true });

        console.log('✅ [GraphQL] Firestore atualizado com sucesso. Total screenshots:', updatedUrls.length);

        // Criar resultado válido com todos os campos obrigatórios
        const result = {
          id: `${input.influencerId}_${input.platform}_${timestamp}`,
          influencerId: input.influencerId,
          platform: input.platform,
          url: mockUrl,
          filename: input.filename,
          size: input.size || 0,
          contentType: input.contentType || 'image/jpeg',
          uploadedAt: new Date(),
          uploadedBy: context?.user?.id || 'system'
        };

        console.log('📤 [GraphQL] Upload concluído com sucesso:', result.id);
        return result;

      } catch (error) {
        console.error('❌ [GraphQL] Erro na mutation uploadScreenshot:', error);
        throw new Error(`Erro no upload de screenshot: ${error.message}`);
      }
    },

    // Upload em lote de screenshots - OTIMIZADO
    async uploadScreenshotsBatch(parent: any, { input }: any, context: any) {
      console.log('📸 [GraphQL] Mutation uploadScreenshotsBatch iniciada');
      console.log('📸 [GraphQL] Input recebido:', {
        influencerId: input?.influencerId,
        totalScreenshots: input?.screenshots?.length || 0
      });

      try {
        // Validação de entrada obrigatória
        if (!input || !input.influencerId || !input.screenshots || input.screenshots.length === 0) {
          return {
            success: false,
            totalUploaded: 0,
            results: [],
            errors: [{
              platform: 'unknown',
              filename: 'unknown',
              error: 'Input inválido: influencerId e screenshots são obrigatórios'
            }]
          };
        }

        console.log('✅ [GraphQL] Validação inicial passou');

        const results: any[] = [];
        const errors: any[] = [];
        let uploadedCount = 0;
        const timestamp = Date.now();

        // Processar uploads sequencialmente por enquanto para debug
        for (let i = 0; i < input.screenshots.length; i++) {
          const screenshot = input.screenshots[i];
          
          try {
            console.log(`📸 [GraphQL] Processando screenshot ${i + 1}/${input.screenshots.length}: ${screenshot.platform}/${screenshot.filename}`);

            // Validação individual do screenshot
            if (!screenshot.platform || !screenshot.filename || !screenshot.fileData) {
              errors.push({
                platform: screenshot.platform || 'unknown',
                filename: screenshot.filename || 'unknown',
                error: 'Dados obrigatórios em falta: platform, filename, fileData'
              });
              continue;
            }

            // Gerar URL mock para desenvolvimento rápido
            const mockUrl = `https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/${input.influencerId}/screenshots/${screenshot.platform}/${screenshot.filename}`;

            // Atualizar Firestore
            const platformDocRef = db
              .collection('influencers')
              .doc(input.influencerId)
              .collection('screenshots')
              .doc(screenshot.platform);

            const platformDoc = await platformDocRef.get();
            let currentUrls: string[] = [];
            
            if (platformDoc.exists) {
              const data = platformDoc.data();
              currentUrls = data?.urls || [];
            }

            const updatedUrls = [...currentUrls, mockUrl];

            await platformDocRef.set({
              platform: screenshot.platform,
              urls: updatedUrls,
              lastUpdated: new Date(),
              totalScreenshots: updatedUrls.length
            }, { merge: true });

            // Criar resultado de sucesso
            const result = {
              id: `${input.influencerId}_${screenshot.platform}_${timestamp}_${i}`,
              influencerId: input.influencerId,
              platform: screenshot.platform,
              url: mockUrl,
              filename: screenshot.filename,
              size: screenshot.size || 0,
              contentType: screenshot.contentType || 'image/png',
              uploadedAt: new Date(),
              uploadedBy: context?.user?.id || 'system'
            };

            results.push(result);
            uploadedCount++;

            console.log(`✅ [GraphQL] Screenshot ${i + 1} processado com sucesso`);

          } catch (error) {
            console.error(`❌ [GraphQL] Erro no screenshot ${i + 1}:`, error);
            errors.push({
              platform: screenshot.platform || 'unknown',
              filename: screenshot.filename || 'unknown',
              error: error instanceof Error ? error.message : 'Erro desconhecido'
            });
          }
        }

        console.log(`📤 [GraphQL] Upload em lote concluído: ${uploadedCount}/${input.screenshots.length} sucessos`);

        return {
          success: uploadedCount > 0,
          totalUploaded: uploadedCount,
          results,
          errors
        };

      } catch (error) {
        console.error('❌ [GraphQL] Erro na mutation uploadScreenshotsBatch:', error);
        
        return {
          success: false,
          totalUploaded: 0,
          results: [],
          errors: [{
            platform: 'unknown',
            filename: 'unknown',
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          }]
        };
      }
    },

    // Deletar screenshot
    async deleteScreenshot(parent: any, { id, influencerId, platform }: { id: string, influencerId: string, platform: string }, context: any) {
      console.log(`📸 Mutation: Deletando screenshot ${id} do influenciador ${influencerId} plataforma ${platform}`);
      
      try {
        // Referenciar o documento da plataforma na subcoleção screenshots
        const platformDocRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('screenshots')
          .doc(platform);

        // Buscar documento da plataforma
        const platformDoc = await platformDocRef.get();
        
        if (!platformDoc.exists) {
          console.log('❌ Documento da plataforma não encontrado');
          return false;
        }

        const data = platformDoc.data();
        const currentUrls = data?.urls || [];

        // Buscar a URL no array (o ID é a própria URL do screenshot)
        const urlToDelete = id;
        const urlIndex = currentUrls.findIndex(url => url === urlToDelete);

        if (urlIndex === -1) {
          console.log('❌ Screenshot não encontrado na lista:', urlToDelete);
          return false;
        }

        console.log(`✅ Screenshot encontrado no índice ${urlIndex}:`, urlToDelete);

        // Remover URL do array
        const updatedUrls = currentUrls.filter(url => url !== urlToDelete);

        // Atualizar documento da plataforma
        if (updatedUrls.length === 0) {
          // Se não há mais URLs, deletar o documento inteiro
          await platformDocRef.delete();
          console.log('✅ Documento da plataforma deletado (sem screenshots restantes)');
        } else {
          // Atualizar com as URLs restantes
          await platformDocRef.update({
            urls: updatedUrls,
            lastUpdated: new Date(),
            totalScreenshots: updatedUrls.length
          });
          console.log('✅ Screenshot removido. Restantes:', updatedUrls.length);
        }
        
        return true;
      } catch (error) {
        console.error('❌ Erro ao deletar screenshot:', error);
        // Retornar false em caso de erro ao invés de throw
        return false;
      }
    },

    // Limpar cache
    async clearCache(parent: any, args: any, context: any) {
      console.log('🧹 Mutation: Limpando cache');
      
      try {
        FinancialCacheService.clearCache();
        
        // Limpar DataLoaders
        context.loaders.influencer.clearAll();
        context.loaders.financial.clearAll();
        context.loaders.campaigns.clearAll();
        context.loaders.brands.clearAll();
        
        return true;
      } catch (error) {
        console.error('Erro ao limpar cache:', error);
        throw new Error('Erro ao limpar cache');
      }
    },

    // Pré-carregar dados
    async preloadInfluencerData(parent: any, { influencerIds }: { influencerIds: string[] }, context: any) {
      console.log(`🚀 Mutation: Pré-carregando dados para ${influencerIds.length} influenciadores`);
      
      try {
        await FinancialCacheService.preloadFinancialData(influencerIds);
        
        // Pré-carregar nos DataLoaders
        await Promise.all([
          context.loaders.influencer.loadMany(influencerIds),
          context.loaders.financial.loadMany(influencerIds),
          context.loaders.campaigns.loadMany(influencerIds),
          context.loaders.brands.loadMany(influencerIds)
        ]);
        
        return true;
      } catch (error) {
        console.error('Erro ao pré-carregar dados:', error);
        throw new Error('Erro ao pré-carregar dados');
      }
    },

    // ===== MUTATIONS PARA CATEGORIES =====

    // Criar categoria
    async createCategory(parent: any, { input }: { input: any }, context: any) {
      console.log('🏷️ Mutation: Criando nova categoria', input);
      
      if (!input.userId) {
        throw new Error('userId é obrigatório para criar categoria');
      }
      
      try {
        const db = getFirestore();
        const categoriesRef = collection(db, 'categories');
        
        // Gerar slug a partir do nome
        const slug = input.name
          .toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '') // Remove acentos
          .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
          .replace(/\s+/g, '-') // Substitui espaços por hífens
          .replace(/-+/g, '-') // Remove hífens duplicados
          .trim();

        const categoryData = {
          name: input.name.trim(),
          slug: slug,
          description: input.description?.trim() || null,
          userId: input.userId,
          isActive: input.isActive ?? true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const docRef = await addDoc(categoriesRef, categoryData);
        
        const category = {
          id: docRef.id,
          ...categoryData
        };

        console.log(`✅ Categoria criada: ${category.name} (${category.id})`);
        
        return category;
      } catch (error) {
        console.error('Erro ao criar categoria:', error);
        throw new Error('Erro ao criar categoria');
      }
    },

    // Atualizar categoria
    async updateCategory(parent: any, { id, input }: { id: string, input: any }, context: any) {
      console.log(`🏷️ Mutation: Atualizando categoria ${id}`, input);
      
      if (!id) {
        throw new Error('ID da categoria é obrigatório');
      }
      
      try {
        const db = getFirestore();
        const categoryRef = doc(db, 'categories', id);
        
        // Verificar se a categoria existe
        const categorySnap = await getDoc(categoryRef);
        if (!categorySnap.exists()) {
          throw new Error('Categoria não encontrada');
        }

        const updateData: any = {
          updatedAt: new Date()
        };

        if (input.name) {
          updateData.name = input.name.trim();
          // Regenerar slug se o nome mudou
          updateData.slug = input.name
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
        }

        if (input.description !== undefined) {
          updateData.description = input.description?.trim() || null;
        }

        if (input.isActive !== undefined) {
          updateData.isActive = input.isActive;
        }

        await updateDoc(categoryRef, updateData);
        
        // Buscar categoria atualizada
        const updatedSnap = await getDoc(categoryRef);
        const updatedData = updatedSnap.data();
        
        const category = {
          id: updatedSnap.id,
          ...updatedData,
          createdAt: updatedData?.createdAt?.toDate?.() || new Date(),
          updatedAt: updatedData?.updatedAt?.toDate?.() || new Date()
        };

        console.log(`✅ Categoria atualizada: ${category.name}`);
        
        return category;
      } catch (error) {
        console.error('Erro ao atualizar categoria:', error);
        throw new Error('Erro ao atualizar categoria');
      }
    },

    // Deletar categoria
    async deleteCategory(parent: any, { id, userId }: { id: string, userId: string }, context: any) {
      console.log(`🏷️ Mutation: Deletando categoria ${id} do usuário ${userId}`);
      
      if (!id || !userId) {
        throw new Error('ID da categoria e userId são obrigatórios');
      }
      
      try {
        const db = getFirestore();
        const categoryRef = doc(db, 'categories', id);
        
        // Verificar se a categoria existe e pertence ao usuário
        const categorySnap = await getDoc(categoryRef);
        if (!categorySnap.exists()) {
          throw new Error('Categoria não encontrada');
        }

        const categoryData = categorySnap.data();
        if (categoryData.userId !== userId) {
          throw new Error('Você não tem permissão para deletar esta categoria');
        }

        await deleteDoc(categoryRef);

        console.log(`✅ Categoria deletada: ${id}`);
        
        return true;
      } catch (error) {
        console.error('Erro ao deletar categoria:', error);
        throw new Error('Erro ao deletar categoria');
      }
    }
  },

  // ===== SUBSCRIPTION RESOLVERS =====
  Subscription: {
    influencerUpdated: {
      subscribe: (parent: any, { userId }: { userId: string }) => {
        return pubsub.asyncIterator(['INFLUENCER_UPDATED']);
      },
      resolve: (payload: any, { userId }: { userId: string }) => {
        // Filtrar apenas para o usuário correto
        if (payload.userId === userId) {
          return payload.influencerUpdated;
        }
        return null;
      }
    },

    influencerCreated: {
      subscribe: (parent: any, { userId }: { userId: string }) => {
        return pubsub.asyncIterator(['INFLUENCER_CREATED']);
      },
      resolve: (payload: any, { userId }: { userId: string }) => {
        if (payload.userId === userId) {
          return payload.influencerCreated;
        }
        return null;
      }
    },

    influencerDeleted: {
      subscribe: (parent: any, { userId }: { userId: string }) => {
        return pubsub.asyncIterator(['INFLUENCER_DELETED']);
      },
      resolve: (payload: any, { userId }: { userId: string }) => {
        if (payload.userId === userId) {
          return payload.influencerDeleted;
        }
        return null;
      }
    },

    cacheStatsUpdated: {
      subscribe: () => pubsub.asyncIterator(['CACHE_STATS_UPDATED'])
    }
  }
};

// Função para criar context com DataLoaders
export function createGraphQLContext(req: any) {
  return {
    user: getUserFromRequest(req), // Implementar autenticação
    loaders: {
      influencer: createInfluencerLoader(),
      financial: createFinancialLoader(),
      campaigns: createCampaignLoader(),
      brands: createBrandLoader()
    }
  };
}

// Placeholder para autenticação
function getUserFromRequest(req: any) {
  // Implementar extração de usuário do token JWT
  return { id: 'user_id_placeholder' };
}

export default resolvers; 

