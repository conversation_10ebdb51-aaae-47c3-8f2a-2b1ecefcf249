import { NextRequest, NextResponse } from "next/server";
import {
  addFilter,
  getAllFilters,
  deleteFilter,
  getFilterById
} from "@/lib/firebase";

export async function GET() {
  try {
    // Buscar todos os filtros - passamos undefined para buscar todos os filtros
    // Para uma implementação com usuários, poderíamos passar o ID do usuário aqui
    const filters = await getAllFilters();
    return NextResponse.json(filters);
  } catch (error) {
    console.error("Erro ao buscar filtros:", error);
    return NextResponse.json(
      { error: "Erro ao buscar filtros" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Obter dados do corpo da requisição
    const data = await request.json();

    // Validar dados mínimos
    if (!data.name) {
      return NextResponse.json(
        { error: "Nome do filtro é obrigatório" },
        { status: 400 }
      );
    }

    // Criar objeto do filtro
    const filterData = {
      name: data.name,
      location: data.location || "",
      minFollowers: data.minFollowers || 0,
      maxFollowers: data.maxFollowers || 10000000,
      minRating: data.minRating || 0,
      verifiedOnly: data.verifiedOnly || false,
      availableOnly: data.availableOnly || false,
      platforms: data.platforms || {},
      // Se tivermos usuários no futuro, adicionar userId aqui
      // userId: session.user.id,
    };

    // Salvar no Firestore
    const newFilter = await addFilter(filterData);

    return NextResponse.json(newFilter);
  } catch (error) {
    console.error("Erro ao salvar filtro:", error);
    return NextResponse.json(
      { error: "Erro ao salvar filtro" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Obter ID do filtro da URL
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "ID do filtro não fornecido" },
        { status: 400 }
      );
    }

    // Verificar se o filtro existe
    const filter = await getFilterById(id);
    if (!filter) {
      return NextResponse.json(
        { error: "Filtro não encontrado" },
        { status: 404 }
      );
    }
    
    // Remover o filtro
    await deleteFilter(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao excluir filtro:", error);
    return NextResponse.json(
      { error: "Erro ao excluir filtro" },
      { status: 500 }
    );
  }
}


