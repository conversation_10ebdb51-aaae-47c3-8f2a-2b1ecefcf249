import { NextResponse } from 'next/server';
import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import fs from 'fs';
import path from 'path';

// Configuração do Firebase
const serviceAccount = require('../../../../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicializar Firebase apenas se não estiver já inicializado
const firebaseApp = getApps().length === 0 
  ? initializeApp({
      credential: cert(serviceAccount),
      storageBucket: 'deumatch-demo.appspot.com'
    }, 'migration-app')
  : getApps().find(app => app.name === 'migration-app') || getApps()[0];

const db = getFirestore(firebaseApp);
const storage = getStorage(firebaseApp);
const bucket = storage.bucket('deumatch-demo.appspot.com');

// Função para fazer upload de um arquivo local para o Firebase Storage
async function uploadLocalFileToFirebase(localFilePath: string, firebasePath: string): Promise<string | null> {
  try {
    const fullLocalPath = path.join(process.cwd(), 'public', localFilePath);
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(fullLocalPath)) {
      console.log(`❌ Arquivo não encontrado: ${fullLocalPath}`);
      return null;
    }
    
    console.log(`📤 Fazendo upload: ${localFilePath} -> ${firebasePath}`);
    
    // Fazer upload do arquivo
    const [file] = await bucket.upload(fullLocalPath, {
      destination: firebasePath,
      metadata: {
        contentType: 'image/jpeg' // Assumindo que são imagens JPG
      }
    });
    
    // Tornar o arquivo público
    await file.makePublic();
    
    // Retornar a URL pública
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${firebasePath}`;
    console.log(`✅ Upload concluído: ${publicUrl}`);
    
    return publicUrl;
  } catch (error: any) {
    console.error(`❌ Erro no upload de ${localFilePath}:`, error.message);
    return null;
  }
}

// Função para atualizar o documento do influenciador no Firestore
async function updateInfluencerAvatar(influencerId: string, newAvatarUrl: string): Promise<void> {
  try {
    const docRef = db.collection('influencers').doc(influencerId);
    await docRef.update({
      avatarUrl: newAvatarUrl,    // Campo principal
      updatedAt: new Date().toISOString()
    });
    console.log(`✅ Influenciador ${influencerId} atualizado com nova URL do avatar`);
  } catch (error: any) {
    console.error(`❌ Erro ao atualizar influenciador ${influencerId}:`, error.message);
    throw error;
  }
}

// API endpoint para migração de avatares
export async function POST(request: Request) {
  try {
    console.log('🚀 Iniciando migração de avatares para Firebase Storage...');
    
    // Buscar todos os influenciadores com avatares locais
    const influencersSnapshot = await db.collection('influencers').get();
    const influencersToMigrate: Array<{id: string, name: string, avatar: string}> = [];
    
    influencersSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.avatar && data.avatar.startsWith('/uploads/influencers/')) {
        influencersToMigrate.push({
          id: doc.id,
          name: data.name || 'Nome não informado',
          avatar: data.avatar
        });
      }
    });
    
    console.log(`📊 Encontrados ${influencersToMigrate.length} influenciadores com avatares locais`);
    
    if (influencersToMigrate.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'Nenhum avatar local encontrado. Migração não necessária.',
        migrated: 0,
        errors: 0
      });
    }
    
    // Migrar cada avatar
    let successCount = 0;
    let errorCount = 0;
    const results: Array<{id: string, name: string, status: string, newUrl?: string, error?: string}> = [];
    
    for (const influencer of influencersToMigrate) {
      console.log(`\n🔄 Processando: ${influencer.name} (${influencer.id})`);
      
      try {
        // Extrair o nome do arquivo do caminho local
        const fileName = path.basename(influencer.avatar);
        const firebasePath = `influencers/${fileName}`;
        
        // Fazer upload para o Firebase
        const newAvatarUrl = await uploadLocalFileToFirebase(influencer.avatar, firebasePath);
        
        if (newAvatarUrl) {
          // Atualizar o documento no Firestore
          await updateInfluencerAvatar(influencer.id, newAvatarUrl);
          successCount++;
          results.push({
            id: influencer.id,
            name: influencer.name,
            status: 'success',
            newUrl: newAvatarUrl
          });
        } else {
          errorCount++;
          results.push({
            id: influencer.id,
            name: influencer.name,
            status: 'error',
            error: 'Falha no upload do arquivo'
          });
        }
      } catch (error: any) {
        errorCount++;
        results.push({
          id: influencer.id,
          name: influencer.name,
          status: 'error',
          error: error.message
        });
      }
    }
    
    console.log(`\n📈 Migração concluída:`);
    console.log(`✅ Sucessos: ${successCount}`);
    console.log(`❌ Erros: ${errorCount}`);
    
    return NextResponse.json({
      success: true,
      message: `Migração concluída. ${successCount} sucessos, ${errorCount} erros.`,
      migrated: successCount,
      errors: errorCount,
      results: results
    });
    
  } catch (error: any) {
    console.error('❌ Erro na migração:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro interno na migração', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// GET endpoint para verificar status dos avatares
export async function GET(request: Request) {
  try {
    const influencersSnapshot = await db.collection('influencers').get();
    let localAvatars = 0;
    let firebaseAvatars = 0;
    let noAvatars = 0;
    
    influencersSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.avatar) {
        if (data.avatar.startsWith('/uploads/influencers/')) {
          localAvatars++;
        } else if (data.avatar.startsWith('http')) {
          firebaseAvatars++;
        }
      } else {
        noAvatars++;
      }
    });
    
    return NextResponse.json({
      total: influencersSnapshot.size,
      localAvatars,
      firebaseAvatars,
      noAvatars,
      needsMigration: localAvatars > 0
    });
    
  } catch (error: any) {
    console.error('❌ Erro ao verificar status:', error);
    return NextResponse.json(
      { error: 'Erro ao verificar status dos avatares' },
      { status: 500 }
    );
  }
}

