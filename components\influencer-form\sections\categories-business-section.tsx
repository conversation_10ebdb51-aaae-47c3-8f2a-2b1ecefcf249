import React, { useState, useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import { X, Plus, Loader2, Settings } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/hooks/use-auth-v2'
import { toast } from 'sonner'
import type { InfluencerFormData } from '@/types/influencer-form'

const COMMON_CATEGORIES = [
  'Moda', 'Beleza', 'Lifestyle', 'Fitness', 'Alimentação',
  'Tecnologia', 'Games', 'Viagem', 'Educação', 'Entretenimento',
  'Saúde', 'Casa e Decoração', 'Pet<PERSON>', 'Música', 'Arte'
]

interface UserCategory {
  id: string
  name: string
  slug: string
  description?: string
}

export function CategoriesBusinessSection() {
  const { register, watch, setValue, formState: { errors } } = useFormContext<InfluencerFormData>()
  const { currentUser } = useAuth()
  const router = useRouter()
  
  const [userCategories, setUserCategories] = useState<UserCategory[]>([])
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  
  const categories = watch('business.categories') || []

  // Carregar categorias do usuário
  useEffect(() => {
    if (currentUser?.id) {
      fetchUserCategories()
    }
  }, [currentUser?.id])

  const fetchUserCategories = async () => {
    if (!currentUser?.id) return

    setIsLoadingCategories(true)
    try {
      const response = await fetch(`/api/categories/user/${currentUser.id}`)
      
      if (response.ok) {
        const data = await response.json()
        setUserCategories(data)
      } else {
        console.warn('Erro ao carregar categorias do usuário')
      }
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    } finally {
      setIsLoadingCategories(false)
    }
  }

  const addCategory = (category: string) => {
    if (!categories.includes(category)) {
      setValue('business.categories', [...categories, category])
    }
  }

  const removeCategory = (category: string) => {
    setValue('business.categories', categories.filter(c => c !== category))
  }

  const goToManageCategories = () => {
    if (currentUser?.id) {
      router.push(`/${currentUser.id}/settings/categories`)
    }
  }

  return (
    <div className="space-y-6">
    

      {/* Categorias */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-sm">Categorias de Conteúdo</CardTitle>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={goToManageCategories}
            className="text-xs text-muted-foreground hover:text-[#ff0074]"
          >
            <Settings className="h-3 w-3 mr-1" />
            Gerenciar
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Categorias Selecionadas */}
          {categories.length > 0 && (
            <div className="space-y-2">
              <Label className="text-xs">Categorias Selecionadas</Label>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Badge key={category} variant="secondary" className="text-xs">
                    {category}
                    <button
                      type="button"
                      onClick={() => removeCategory(category)}
                      className="ml-1 hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Suas Categorias Personalizadas */}
          {userCategories.length > 0 ? (
            <div className="space-y-2">
             
              <div className="flex flex-wrap gap-2">
                {userCategories
                  .filter(cat => !categories.includes(cat.name))
                  .map((category) => (
                    <Button
                      key={category.id}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addCategory(category.name)}
                      className="text-xs border-[#ff0074]/20 hover:border-[#ff0074] hover:bg-[#ff0074]/10"
                      title={category.description}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      {category.name}
                    </Button>
                  ))}
              </div>
              {userCategories.filter(cat => !categories.includes(cat.name)).length === 0 && (
                <p className="text-xs text-muted-foreground italic">
                  Todas suas categorias já foram selecionadas
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">Suas Categorias</Label>
              <div className="p-3 border border-dashed rounded-lg text-center">
                <p className="text-xs text-muted-foreground mb-2">
                  Você ainda não criou categorias personalizadas
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={goToManageCategories}
                  className="text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Criar primeira categoria
                </Button>
              </div>
            </div>
          )}

       

        
        </CardContent>
      </Card>
    </div>
  )
} 


