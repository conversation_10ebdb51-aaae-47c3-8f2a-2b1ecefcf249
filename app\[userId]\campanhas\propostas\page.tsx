'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

// Força renderização dinâmica para evitar problemas de SSG
export const dynamic = 'force-dynamic';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter 
} from '@/components/ui/dialog';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  FileText, 
  Send, 
  Clock, 
  CheckCircle, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Settings,
  Copy,
  AlertTriangle
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

// Importar tipos e serviços do Firebase
import { ProposalService } from '@/services/proposal-service';
import { 
  Proposal, 
  ProposalStatus, 
  ProposalPriority,
  InfluencerInProposal,
  InfluencerTier,
  PROPOSAL_STATUS_LABELS,
  getProposalStatusColor,
  getProposalPriorityColor 
} from '@/types/proposal';
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';

// Tipos para a UI local
interface Proposta {
  id: string;
  nome: string;
  descricao?: string;
  criadoPor: string;
  brandId: string;
  grupo?: string; // Grupo é opcional e diferente do nome
  priority: ProposalPriority;
  status: ProposalStatus;
  influencers: InfluencerInProposal[];
  totalAmount: number;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  dataEnvio?: string;
  services: any[];
}

interface ColumnVisibility {
  influencers: boolean;
  value: boolean;
  priority: boolean;
  criadoPor: boolean;
  dataEnvio: boolean;
  grupo: boolean;
  dataCriacao: boolean;
  ultimaAtualizacao: boolean;
  status: boolean;
  actions: boolean;
}

function PropostasPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { currentUser } = useFirebaseAuth();
  
  // ✅ CORREÇÃO: Prioridade com isolamento adequado
  // 1. brandId explícito da URL (para visualizar marca específica)
  // 2. userId como brandId (propostas próprias do usuário)
  const urlBrandId = searchParams.get('brandId') || searchParams.get('brand');
  const brandId = urlBrandId || currentUser?.id || '';
  const brandName = searchParams.get('brandName') || 
                   (urlBrandId ? 'Marca' : 'Minhas Propostas');
  
  const [propostas, setPropostas] = useState<Proposta[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('todos');
  const [currentBrand, setCurrentBrand] = useState<{id: string, name: string} | null>(null);
  
  // Estados para criação de proposta
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newPropostaName, setNewPropostaName] = useState('');
  const [newPropostaDescription, setNewPropostaDescription] = useState('');
  const [newPropostaPriority, setNewPropostaPriority] = useState<ProposalPriority>('medium');
  
  // Estados para edição de colunas
  const [showColumnsDialog, setShowColumnsDialog] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>({
    influencers: true,
    value: true,
    priority: true,
    criadoPor: true,
    dataEnvio: true,
    grupo: false,
    dataCriacao: true,
    ultimaAtualizacao: true,
    status: true,
    actions: true
  });

  // Estados para edição e confirmação
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false);
  const [editingProposta, setEditingProposta] = useState<Proposta | null>(null);
  const [deletingProposta, setDeleteingProposta] = useState<Proposta | null>(null);
  const [editPropostaName, setEditPropostaName] = useState('');
  const [editPropostaDescription, setEditPropostaDescription] = useState('');
  const [editPropostaPriority, setEditPropostaPriority] = useState<ProposalPriority>('medium');

  // ✅ CORREÇÃO: Validar e configurar marca atual com isolamento
  useEffect(() => {
    if (brandId && currentUser?.id) {
      setCurrentBrand({
        id: brandId,
        name: brandName
      });
    }
  }, [brandId, brandName, currentUser?.id]);

  // ✅ CORREÇÃO: Carregar dados apenas quando usuário autenticado e brandId válido
  useEffect(() => {
    if (brandId && currentUser?.id) {
      loadPropostas();
    }
  }, [brandId, currentUser?.id]);

  const loadPropostas = async () => {
    try {
      setLoading(true);
      
      // ✅ CORREÇÃO: Garantir isolamento de usuário passando userId
      if (!currentUser?.id) {
        toast({
          title: "Erro de Autenticação",
          description: "Usuário não autenticado. Faça login novamente.",
          variant: "destructive",
        });
        return;
      }
      
      // Buscar propostas do Firebase usando o ProposalService com isolamento de usuário
      const firebaseProposals = await ProposalService.getProposals(
        { brandId },
        100, // limit
        0, // offset
        currentUser.id // ✅ CORREÇÃO: Passar userId para isolamento
      );
      
             // Converter para o formato da UI
       const convertedPropostas: Proposta[] = firebaseProposals.map(proposal => ({
         id: proposal.id,
         nome: proposal.nome || `Proposta ${proposal.id.substring(0, 8)}`,
         descricao: proposal.descricao || `Proposta com ${proposal.influencers.length} influenciador(es)`,
         criadoPor: proposal.criadoPor || 'Sistema',
         brandId: proposal.brandId,
         grupo: proposal.grupo,
         priority: proposal.priority,
         status: proposal.status,
         influencers: proposal.influencers.map(inf => ({
           ...inf,
           tier: (inf.tier as InfluencerTier) || undefined
         })),
         totalAmount: proposal.totalAmount,
         dataCriacao: proposal.createdAt,
         ultimaAtualizacao: proposal.updatedAt,
         dataEnvio: proposal.dataEnvio,
         services: proposal.services
       }));
      
      setPropostas(convertedPropostas);
    } catch (error) {
      console.error('Erro ao carregar propostas:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as propostas.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProposta = async () => {
    if (!newPropostaName.trim()) {
      toast({
        title: "Erro",
        description: "Nome da proposta é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    // ✅ CORREÇÃO: Validar se temos um brandId válido e usuário autenticado
    if (!brandId || !currentUser?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado ou marca não identificada.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      // Criar dados da proposta para o Firebase
      const proposalData = {
        nome: newPropostaName,
        descricao: newPropostaDescription || '',
        criadoPor: currentUser?.name || 'Usuário Desconhecido',
        influencers: [], // Proposta vazia inicialmente
        brandId: brandId,
        services: [],
        totalAmount: 0,
        dataEnvio: new Date().toISOString().split('T')[0],
        grupo: '', // Grupo vazio inicialmente
        priority: newPropostaPriority
      };

      // Criar proposta no Firebase
      const proposalId = await ProposalService.createProposal(proposalData);
      
      toast({
        title: "Sucesso",
        description: "Proposta criada com sucesso!",
      });

      // Limpar formulário e fechar dialog
      setNewPropostaName('');
      setNewPropostaDescription('');
      setNewPropostaPriority('medium');
      setShowCreateDialog(false);
      
      // Recarregar propostas
      await loadPropostas();
      
    } catch (error) {
      console.error('Erro ao criar proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível criar a proposta.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditProposta = (proposta: Proposta) => {
    setEditingProposta(proposta);
    setEditPropostaName(proposta.nome);
    setEditPropostaDescription(proposta.descricao || '');
    setEditPropostaPriority(proposta.priority);
    setShowEditDialog(true);
  };

  const handleUpdateProposta = async () => {
    if (!editingProposta || !editPropostaName.trim()) {
      toast({
        title: "Erro",
        description: "Nome da proposta é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      const updateData = {
        nome: editPropostaName,
        descricao: editPropostaDescription,
        priority: editPropostaPriority
      };

      await ProposalService.updateProposal(editingProposta.id, updateData);
      
      toast({
        title: "Sucesso",
        description: "Proposta atualizada com sucesso!",
      });

      setShowEditDialog(false);
      setEditingProposta(null);
      await loadPropostas();
      
    } catch (error) {
      console.error('Erro ao atualizar proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar a proposta.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicateProposta = async (proposta: Proposta) => {
    try {
      setLoading(true);
      
      const duplicateData = {
        nome: `${proposta.nome} - Cópia`,
        descricao: proposta.descricao || '',
        criadoPor: currentUser?.name || 'Usuário Desconhecido',
        influencers: proposta.influencers,
        brandId: proposta.brandId,
        services: proposta.services,
        totalAmount: proposta.totalAmount,
        dataEnvio: new Date().toISOString().split('T')[0],
        grupo: proposta.grupo || '',
        priority: proposta.priority
      };

      await ProposalService.createProposal(duplicateData);
      
      toast({
        title: "Sucesso",
        description: "Proposta duplicada com sucesso!",
      });

      await loadPropostas();
      
    } catch (error) {
      console.error('Erro ao duplicar proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível duplicar a proposta.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProposta = (proposta: Proposta) => {
    setDeleteingProposta(proposta);
    setShowDeleteConfirmDialog(true);
  };

  const confirmDeleteProposta = async () => {
    if (!deletingProposta) return;

    try {
      setLoading(true);
      await ProposalService.deleteProposal(deletingProposta.id);
      toast({
        title: "Sucesso",
        description: "Proposta excluída com sucesso!",
      });
      setShowDeleteConfirmDialog(false);
      setDeleteingProposta(null);
      await loadPropostas();
    } catch (error) {
      console.error('Erro ao excluir proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a proposta.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: ProposalStatus) => {
    const colorClass = getProposalStatusColor(status);
    return (
      <Badge className={colorClass}>
        {PROPOSAL_STATUS_LABELS[status]}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: ProposalPriority) => {
    const colorClass = getProposalPriorityColor(priority);
    const labels = {
      low: 'Baixa',
      medium: 'Média',
      high: 'Alta'
    };
    
    return (
      <Badge className={colorClass}>
        {labels[priority]}
      </Badge>
    );
  };

  const filteredPropostas = propostas.filter(proposta => {
    const matchesSearch = proposta.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposta.grupo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposta.descricao?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'todos' || proposta.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const totalPropostas = propostas.length;
  const totalDraft = propostas.filter(p => p.status === 'draft').length;
  const totalSent = propostas.filter(p => p.status === 'sent').length;
  const totalAccepted = propostas.filter(p => p.status === 'accepted').length;
  const totalValue = propostas.reduce((sum, p) => sum + p.totalAmount, 0);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // ✅ CORREÇÃO: Estado de carregamento e validação de autenticação
  if (!currentUser?.id) {
    return (
      <div className="h-full p-6 flex items-center justify-center">
        <Card className="p-8">
          <div className="text-center">
            <AlertTriangle className="h-16 w-16 mx-auto text-yellow-500 mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Autenticação Necessária</h2>
            <p className="text-muted-foreground mb-4">
              Você precisa estar logado para acessar suas propostas.
            </p>
            <Button onClick={() => window.location.href = '/login'}>
              Fazer Login
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  if (!brandId) {
    return (
      <div className="h-full p-6 flex items-center justify-center">
        <Card className="p-8">
          <div className="text-center">
            <AlertTriangle className="h-16 w-16 mx-auto text-orange-500 mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Carregando...</h2>
            <p className="text-muted-foreground">
              Configurando isolamento de dados do usuário...
            </p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-full p-6 space-y-6 overflow-y-auto">
      {/* ✅ INFORMAÇÃO DE ISOLAMENTO - Debug durante desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 border border-blue-200 p-3 rounded-lg mb-4">
          <p className="text-sm text-blue-800">
            <strong>🔒 Isolamento Ativo:</strong> {' '}
            Usuario: {currentUser.id} | Brand: {brandId} | {urlBrandId ? 'Visualizando marca externa' : 'Minhas propostas'}
          </p>
        </div>
      )}
      
      {/* Estatísticas */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-[#ec003f]">{totalPropostas}</div>
            <p className="text-sm text-muted-foreground">Total de Propostas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{totalSent}</div>
            <p className="text-sm text-muted-foreground">Enviadas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{totalAccepted}</div>
            <p className="text-sm text-muted-foreground">Aceitas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-[#9810fa]">{formatCurrency(totalValue)}</div>
            <p className="text-sm text-muted-foreground">Valor Total</p>
          </CardContent>
        </Card>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          {/* Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar propostas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          {/* Filtros */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Status: {statusFilter === 'todos' ? 'Todos' : PROPOSAL_STATUS_LABELS[statusFilter as ProposalStatus]}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('todos')}>
                Todos os Status
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {Object.entries(PROPOSAL_STATUS_LABELS).map(([status, label]) => (
                <DropdownMenuItem key={status} onClick={() => setStatusFilter(status)}>
                  {label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2">
          {/* Ações da Tabela */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="border border-[#1c1627]">
                <Settings className="border border-[#1c1627] h-4 w-4 mr-2" />
                Colunasaaa
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setShowColumnsDialog(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Editar Colunas
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Criar Proposta */}
          <Button 
            onClick={() => setShowCreateDialog(true)}
            className="bg-[#ec003f] text-white hover:bg-[#ec003f]/90"
            disabled={loading || !brandId || !currentUser?.id}
            title={!brandId || !currentUser?.id ? 'Usuário não autenticado ou marca não identificada' : 'Criar nova proposta'}
          >
            <Plus className="h-4 w-4 mr-2" />
            Criar Proposta
          </Button>
        </div>
      </div>

      {/* Tabela de Propostas */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-[#ec003f] mb-4" />
              <p className="text-muted-foreground">Carregando propostas...</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                                  <TableRow>
                    <TableHead>Nome da Proposta</TableHead>
                    {columnVisibility.influencers && <TableHead className="text-center">Influenciadores</TableHead>}
                    {columnVisibility.value && <TableHead className="text-center">Valor Total</TableHead>}
                    {columnVisibility.priority && <TableHead className="text-center">Prioridade</TableHead>}
                    {columnVisibility.criadoPor && <TableHead>Criado Por</TableHead>}
                    {columnVisibility.dataEnvio && <TableHead>Data Envio</TableHead>}
                    {columnVisibility.grupo && <TableHead>Grupo</TableHead>}
                    {columnVisibility.dataCriacao && <TableHead>Criação</TableHead>}
                    {columnVisibility.ultimaAtualizacao && <TableHead>Última Atualização</TableHead>}
                    {columnVisibility.status && <TableHead>Status</TableHead>}
                    {columnVisibility.actions && <TableHead className="text-center">Ações</TableHead>}
                  </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPropostas.map((proposta) => (
                  <TableRow key={proposta.id}>
                    <TableCell className="group max-w-xs">
                      <div className="flex items-center justify-between">
                        <div className="min-w-0 flex-1 pr-4">
                          <div className="font-medium truncate" title={proposta.nome}>
                            {proposta.nome}
                          </div>
                          {proposta.descricao && (
                            <div className="text-sm text-muted-foreground truncate" title={proposta.descricao}>
                              {proposta.descricao}
                            </div>
                          )}
                        </div>
                        
                        {/* Ícones de ação - visíveis apenas no hover */}
                        <div className="flex items-center gap-1 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                            onClick={() => handleEditProposta(proposta)}
                            title="Editar proposta"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600"
                            onClick={() => handleDuplicateProposta(proposta)}
                            title="Duplicar proposta"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                            onClick={() => handleDeleteProposta(proposta)}
                            title="Excluir proposta"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                    {columnVisibility.influencers && (
                      <TableCell className="text-center">
                        <Badge variant="outline">{proposta.influencers.length}</Badge>
                      </TableCell>
                    )}
                    {columnVisibility.value && (
                      <TableCell className="text-center">
                        <span className="font-medium">{formatCurrency(proposta.totalAmount)}</span>
                      </TableCell>
                    )}
                                         {columnVisibility.priority && (
                       <TableCell className="text-center">
                         {getPriorityBadge(proposta.priority)}
                       </TableCell>
                     )}
                     {columnVisibility.criadoPor && (
                       <TableCell>{proposta.criadoPor}</TableCell>
                     )}
                     {columnVisibility.dataEnvio && (
                       <TableCell>
                         {proposta.dataEnvio ? new Date(proposta.dataEnvio).toLocaleDateString('pt-BR') : '-'}
                       </TableCell>
                     )}
                     {columnVisibility.grupo && (
                       <TableCell>{proposta.grupo || '-'}</TableCell>
                     )}
                     {columnVisibility.dataCriacao && (
                       <TableCell>{proposta.dataCriacao.toLocaleDateString('pt-BR')}</TableCell>
                     )}
                     {columnVisibility.ultimaAtualizacao && (
                       <TableCell>{proposta.ultimaAtualizacao.toLocaleDateString('pt-BR')}</TableCell>
                     )}
                    {columnVisibility.status && <TableCell>{getStatusBadge(proposta.status)}</TableCell>}
                    {columnVisibility.actions && (
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-2">
                                                  <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Preservar parâmetros da URL atual
                            const currentParams = new URLSearchParams();
                            if (brandId) currentParams.set('brandId', brandId);
                            if (brandName) currentParams.set('brandName', brandName);
                            
                            const queryString = currentParams.toString();
                            const url = queryString 
                                                                      ? `/propostas/${proposta.id}?${queryString}`
                : `/propostas/${proposta.id}`;
                            
                            router.push(url);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600"
                                onClick={() => handleDeleteProposta(proposta)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
          
          {!loading && filteredPropostas.length === 0 && (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Nenhuma proposta encontrada</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'todos' 
                  ? 'Tente ajustar os filtros para ver mais resultados.'
                  : 'Crie sua primeira proposta para começar.'
                }
              </p>
              {!searchTerm && statusFilter === 'todos' && (
                <Button 
                  onClick={() => setShowCreateDialog(true)} 
                  className="bg-[#ec003f] hover:bg-[#ec003f]/90"
                  disabled={!brandId || brandId === 'default-brand'}
                  title={!brandId || brandId === 'default-brand' ? 'Selecione uma marca para criar propostas' : 'Criar primeira proposta'}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Criar Primeira Proposta
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog: Criar Proposta */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Criar Nova Proposta</DialogTitle>
            <DialogDescription>
              Preencha os detalhes da proposta. Você poderá adicionar influenciadores depois.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Nome da Proposta *</label>
              <Input
                value={newPropostaName}
                onChange={(e) => setNewPropostaName(e.target.value)}
                placeholder="Ex: Proposta Campanha Verão 2024"
                className="mt-1"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Descrição/Grupo (Opcional)</label>
              <Textarea
                value={newPropostaDescription}
                onChange={(e) => setNewPropostaDescription(e.target.value)}
                placeholder="Descrição da proposta ou nome do grupo..."
                className="mt-1"
                rows={3}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Prioridade</label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full mt-1 justify-between">
                    {newPropostaPriority === 'low' && 'Baixa'}
                    {newPropostaPriority === 'medium' && 'Média'}
                    {newPropostaPriority === 'high' && 'Alta'}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  <DropdownMenuItem onClick={() => setNewPropostaPriority('low')}>
                    Baixa
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setNewPropostaPriority('medium')}>
                    Média
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setNewPropostaPriority('high')}>
                    Alta
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleCreateProposta} 
              className="bg-[#ec003f] hover:bg-[#ec003f]/90"
              disabled={loading}
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Criar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog: Editar Colunas */}
      <Dialog open={showColumnsDialog} onOpenChange={setShowColumnsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Colunas</DialogTitle>
            <DialogDescription>
              Selecione quais colunas devem ser exibidas na tabela.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {Object.entries(columnVisibility).map(([key, visible]) => (
              <div key={key} className="flex items-center space-x-2">
                <Checkbox
                  id={key}
                  checked={visible}
                  onCheckedChange={(checked) =>
                    setColumnVisibility(prev => ({ ...prev, [key]: !!checked }))
                  }
                />
                <label htmlFor={key} className="text-sm font-medium capitalize">
                  {key === 'influencers' && 'Influenciadores'}
                  {key === 'value' && 'Valor Total'}
                  {key === 'priority' && 'Prioridade'}
                  {key === 'criadoPor' && 'Criado Por'}
                  {key === 'dataEnvio' && 'Data de Envio'}
                  {key === 'grupo' && 'Grupo'}
                  {key === 'dataCriacao' && 'Data de Criação'}
                  {key === 'ultimaAtualizacao' && 'Última Atualização'}
                  {key === 'status' && 'Status'}
                  {key === 'actions' && 'Ações'}
                </label>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowColumnsDialog(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog: Editar Proposta */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Proposta</DialogTitle>
            <DialogDescription>
              Modifique os detalhes da proposta conforme necessário.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Nome da Proposta *</label>
              <Input
                value={editPropostaName}
                onChange={(e) => setEditPropostaName(e.target.value)}
                placeholder="Ex: Proposta Campanha Verão 2024"
                className="mt-1"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Descrição/Grupo</label>
              <Textarea
                value={editPropostaDescription}
                onChange={(e) => setEditPropostaDescription(e.target.value)}
                placeholder="Descrição da proposta ou nome do grupo..."
                className="mt-1"
                rows={3}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Prioridade</label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full mt-1 justify-between">
                    {editPropostaPriority === 'low' && 'Baixa'}
                    {editPropostaPriority === 'medium' && 'Média'}
                    {editPropostaPriority === 'high' && 'Alta'}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  <DropdownMenuItem onClick={() => setEditPropostaPriority('low')}>
                    Baixa
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setEditPropostaPriority('medium')}>
                    Média
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setEditPropostaPriority('high')}>
                    Alta
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleUpdateProposta} 
              className="bg-[#ec003f] hover:bg-[#ec003f]/90"
              disabled={loading}
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Atualizar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog: Confirmar Exclusão */}
      <Dialog open={showDeleteConfirmDialog} onOpenChange={setShowDeleteConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Confirmar Exclusão
            </DialogTitle>
            <DialogDescription>
              Tem certeza de que deseja excluir a proposta "{deletingProposta?.nome}"?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirmDialog(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={confirmDeleteProposta} 
              variant="destructive"
              disabled={loading}
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function PropostasPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#ec003f]" />
          <p className="text-muted-foreground">Carregando propostas...</p>
        </div>
      </div>
    }>
      <PropostasPageContent />
    </Suspense>
  );
} 