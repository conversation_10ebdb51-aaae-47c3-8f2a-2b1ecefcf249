const http = require('http');
const https = require('https');

// Função para testar conectividade básica
function testConnection(port) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 5000
    };

    console.log(`\n🔍 Testando conexão na porta ${port}...`);
    
    const req = http.request(options, (res) => {
      console.log(`✅ Porta ${port} está respondendo!`);
      console.log(`Status: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Resposta (primeiros 200 chars): ${data.substring(0, 200)}...`);
        resolve({ port, status: res.statusCode, data });
      });
    });

    req.on('error', (e) => {
      console.log(`❌ Erro na porta ${port}: ${e.message}`);
      reject({ port, error: e.message });
    });

    req.on('timeout', () => {
      console.log(`⏰ Timeout na porta ${port}`);
      req.destroy();
      reject({ port, error: 'Timeout' });
    });

    req.end();
  });
}

// Função para testar a API específica
function testProposalsAPI(port) {
  return new Promise((resolve, reject) => {
    const path = '/api/propostas?brandId=test';
    const options = {
      hostname: 'localhost',
      port: port,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    };

    console.log(`\n🎯 Testando API de propostas na porta ${port}...`);
    
    const req = http.request(options, (res) => {
      console.log(`✅ API respondeu na porta ${port}!`);
      console.log(`Status: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log(`Resposta JSON:`, jsonData);
          resolve({ port, status: res.statusCode, data: jsonData });
        } catch (parseError) {
          console.log(`Resposta (texto):`, data);
          resolve({ port, status: res.statusCode, data });
        }
      });
    });

    req.on('error', (e) => {
      console.log(`❌ Erro na API porta ${port}: ${e.message}`);
      reject({ port, error: e.message });
    });

    req.on('timeout', () => {
      console.log(`⏰ Timeout na API porta ${port}`);
      req.destroy();
      reject({ port, error: 'Timeout' });
    });

    req.end();
  });
}

// Executar testes
async function runTests() {
  console.log('🚀 Iniciando testes de conectividade...');
  
  const ports = [3000, 3001, 3002];
  
  // Testar conectividade básica
  console.log('\n=== TESTE DE CONECTIVIDADE BÁSICA ===');
  for (const port of ports) {
    try {
      await testConnection(port);
    } catch (error) {
      // Erro já foi logado
    }
  }
  
  // Testar API específica
  console.log('\n=== TESTE DA API DE PROPOSTAS ===');
  for (const port of ports) {
    try {
      await testProposalsAPI(port);
    } catch (error) {
      // Erro já foi logado
    }
  }
  
  console.log('\n✨ Testes concluídos!');
}

runTests().catch(console.error);
