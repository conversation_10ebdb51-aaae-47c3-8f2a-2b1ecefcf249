import { NextRequest, NextResponse } from 'next/server';
import { ProposalSharingService } from '@/lib/proposal-sharing-service';

// DELETE - Cancelar convite
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ inviteId: string }> }
) {
  try {
    const { inviteId } = await params;

    await ProposalSharingService.cancelInvite(inviteId);

    return NextResponse.json({
      success: true,
      message: 'Convite cancelado com sucesso'
    });
  } catch (error) {
    console.error('Erro ao cancelar convite:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Aceitar convite
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ inviteId: string }> }
) {
  try {
    const { inviteId } = await params;
    const { token, userData } = await request.json();

    const proposalId = await ProposalSharingService.acceptInvite(token, userData);

    return NextResponse.json({
      success: true,
      proposalId,
      message: 'Convite aceito com sucesso'
    });
  } catch (error) {
    console.error('Erro ao aceitar convite:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 