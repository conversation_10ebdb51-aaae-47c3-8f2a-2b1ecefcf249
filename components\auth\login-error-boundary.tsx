'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, LogIn } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface LoginErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface LoginErrorBoundaryProps {
  children: React.ReactNode;
}

export class LoginErrorBoundary extends React.Component<LoginErrorBoundaryProps, LoginErrorBoundaryState> {
  constructor(props: LoginErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): LoginErrorBoundaryState {
    // Atualizar state para mostrar fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log do erro para debugging
    console.error('❌ Erro na página de login capturado pelo Error Boundary:', error);
    console.error('📍 Stack trace:', errorInfo.componentStack);
    
    // ✅ CORREÇÃO: Detectar erros específicos do Firebase/CSP
    const isFirebaseError = error.message.includes('Firebase') || 
                           error.message.includes('Content Security Policy') ||
                           error.message.includes('auth');
    
    if (isFirebaseError) {
      console.warn('🔥 Erro relacionado ao Firebase detectado - tentando recuperação automática');
    }
    
    this.setState({
      hasError: true,
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    console.log('🔄 Tentando recarregar a página de login...');
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    
    // ✅ CORREÇÃO: Limpar caches que podem estar causando problemas
    if (typeof window !== 'undefined') {
      // Limpar sessionStorage relacionado à autenticação
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.startsWith('token_') || key.startsWith('user_data_') || key.includes('firebase')) {
          sessionStorage.removeItem(key);
        }
      });
      
      // Recarregar a página após limpeza
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  }

  render() {
    if (this.state.hasError) {
      // ✅ CORREÇÃO: UI de fallback melhorada para erros de login
      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-gray-900 via-black to-gray-900">
          <div className="max-w-md w-full bg-black/80 backdrop-blur-xl rounded-xl p-8 shadow-2xl border border-red-500/20">
            <div className="text-center">
              {/* Ícone de erro */}
              <div className="mx-auto flex items-center justify-center w-16 h-16 rounded-full bg-red-500/20 mb-6">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              
              {/* Mensagem principal */}
              <h2 className="text-2xl font-bold text-white mb-4">
                Problema no Carregamento
              </h2>
              
              <p className="text-gray-300 mb-6 text-sm leading-relaxed">
                Houve um problema ao carregar a página de login. Isso pode ser causado por:
              </p>
              
              {/* Lista de possíveis causas */}
              <ul className="text-left text-sm text-gray-400 mb-8 space-y-2">
                <li>• Problemas de conexão com o Firebase</li>
                <li>• Restrições de segurança do navegador</li>
                <li>• Cache corrompido</li>
                <li>• Extensões do navegador interferindo</li>
              </ul>
              
              {/* Botões de ação */}
              <div className="space-y-3">
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-gradient-to-r from-[#ff0074] to-[#9810fa] hover:from-[#9810fa] hover:to-[#ff0074] text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
                >
                  🔄 Tentar Novamente
                </button>
                
                <button
                  onClick={() => window.location.href = '/'}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  🏠 Ir para Página Inicial
                </button>
              </div>
              
              {/* Informações técnicas (apenas em desenvolvimento) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-300">
                    📋 Detalhes Técnicos (Desenvolvimento)
                  </summary>
                  <div className="mt-3 p-3 bg-red-900/20 rounded border border-red-500/30">
                    <p className="text-xs text-red-300 font-mono break-all">
                      {this.state.error.message}
                    </p>
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook para resetar o error boundary
export function useErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  return { captureError, resetError };
} 


