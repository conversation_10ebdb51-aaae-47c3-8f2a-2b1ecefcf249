"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { User, MapPin, Layers, Check, Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PersonalInfoSection } from '@/components/influencer-form/sections/personal-info-section'
import { CategoriesBusinessSection } from '@/components/influencer-form/sections/categories-business-section'
import { SocialPlatformsSection } from '@/components/influencer-form/sections/social-platforms-section'
import { BrandsAssociationSection } from '@/components/influencer-form/sections/brands-association-section'
import { MetricsPreviewSection } from '@/components/influencer-form/sections/metrics-preview-section'

import { InfluencerFormData } from '@/types/influencer-form'
import { useInfluencerForm } from '@/hooks/use-influencer-form'
import { useInfluencerMutations, useBrandInfluencerMutations, GET_INFLUENCERS } from '@/hooks/use-graphql-influencers'
import { useAuth } from '@/hooks/use-auth-v2'
import { useApolloClient, gql } from '@apollo/client'
import { cn } from '@/lib/utils'
import { FormProvider } from 'react-hook-form'
import { X, Smartphone, Tag, BarChart3, Save, Building2 } from 'lucide-react'
import { ScrollArea } from '@/components/ui/scroll-area'

interface AddInfluencerFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit?: (data: any) => Promise<void>
  initialData?: any
  mode?: 'create' | 'edit'
}

export function AddInfluencerForm({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode
}: AddInfluencerFormProps) {
  const { toast } = useToast()
  const { currentUser } = useAuth()
  const apolloClient = useApolloClient()
  const [activeTab, setActiveTab] = useState('personal')
  
  // Hook do GraphQL para mutations
  const { 
    createInfluencer: createInfluencerMutation, 
    updateInfluencer: updateInfluencerMutation,
    createInfluencerPricing,
    createAudienceDemographic
  } = useInfluencerMutations()

  // Hook para mutations de associações marca-influenciador via GraphQL
  const { createBrandInfluencerAssociation } = useBrandInfluencerMutations()
  
  // Determinar modo real (edit vs create)
  const actualMode = mode || (initialData ? 'edit' : 'create')
  
  // Função customizada para GraphQL
  const handleGraphQLSubmit = async (formData: InfluencerFormData) => {
    try {

      
      // 💰 [DEBUG] Verificar especificamente os dados de pricing
      if (formData.platforms) {
        Object.keys(formData.platforms).forEach(platform => {
          const platformData = (formData.platforms as any)[platform]
          if (platformData?.pricing) {
          } else {
          }
        })
      } else {
      }
      
      // 🔥 CORREÇÃO: Usar convertToCompleteStructure para mapear TODOS os campos
      const { influencerData, financialData } = convertToCompleteStructure(formData)
      
      // 🔍 DEBUG: Verificar se responsibleCapturer está sendo capturado do formulário
      console.log('🔍 [DEBUG] formData.business completo:', formData.business)
      console.log('🔍 [DEBUG] responsibleCapturer do formulário:', formData.business?.responsibleCapturer)
      console.log('🔍 [DEBUG] Tipo do responsibleCapturer:', typeof formData.business?.responsibleCapturer)
      
      // Mapear para o formato GraphQL CreateInfluencerInput (APENAS campos aceitos pelo schema)
      const graphqlInput: any = {
        name: influencerData.name || "Nome não informado",
        email: influencerData.email || "",
        phone: influencerData.phone || "",
        whatsapp: influencerData.whatsapp || "",
        country: influencerData.country || "Brasil",
        state: influencerData.state || "",
        city: influencerData.city || "",
        location: influencerData.location || "",
        age: influencerData.age || undefined,
        gender: influencerData.gender || "not_specified",
        bio: influencerData.bio || "",
        avatar: influencerData.avatar || "",
        category: influencerData.category || null,
        categories: influencerData.categories || [],
        totalFollowers: influencerData.totalFollowers || 0,
        engagementRate: influencerData.engagementRate || 0,
        isVerified: influencerData.isVerified || false,
        isAvailable: influencerData.isAvailable || true,
        status: influencerData.status || "active",
        promotesTraders: influencerData.promotesTraders || false,
        responsibleName: influencerData.responsibleName || "",
        agencyName: influencerData.agencyName || "",
        responsibleCapturer: influencerData.responsibleCapturer || "",
        mainNetwork: influencerData.mainNetwork || "",
        mainPlatform: influencerData.mainPlatform || "",
        
        // Campos diretos das plataformas
        instagramUsername: influencerData.instagramUsername,
        instagramFollowers: influencerData.instagramFollowers,
        instagramEngagementRate: influencerData.instagramEngagementRate,
        instagramStoriesViews: influencerData.instagramStoriesViews,
        instagramReelsViews: influencerData.instagramReelsViews,
        
        tiktokUsername: influencerData.tiktokUsername,
        tiktokFollowers: influencerData.tiktokFollowers,
        tiktokEngagementRate: influencerData.tiktokEngagementRate,
        tiktokVideoViews: influencerData.tiktokVideoViews,
        
        youtubeUsername: influencerData.youtubeUsername,
        youtubeFollowers: influencerData.youtubeFollowers,
        youtubeSubscribers: influencerData.youtubeSubscribers,
        youtubeEngagementRate: influencerData.youtubeEngagementRate,
        youtubeShortsViews: influencerData.youtubeShortsViews,
        youtubeLongFormViews: influencerData.youtubeLongFormViews,
        
        facebookUsername: influencerData.facebookUsername,
        facebookFollowers: influencerData.facebookFollowers,
        facebookEngagementRate: influencerData.facebookEngagementRate,
        facebookViews: influencerData.facebookViews,
        facebookReelsViews: influencerData.facebookReelsViews,
        facebookStoriesViews: influencerData.facebookStoriesViews,
        
        twitchUsername: influencerData.twitchUsername,
        twitchFollowers: influencerData.twitchFollowers,
        twitchEngagementRate: influencerData.twitchEngagementRate,
        twitchViews: influencerData.twitchViews,
        
        kwaiUsername: influencerData.kwaiUsername,
        kwaiFollowers: influencerData.kwaiFollowers,
        kwaiEngagementRate: influencerData.kwaiEngagementRate,
        kwaiViews: influencerData.kwaiViews,
        
        // 💰 Dados extraídos mas não enviados via GraphQL (processados separadamente)
        // _pricingData: financialData.prices,
        // _demographicsData: formData.platforms
      }
      
      
      let result: any
      
      if (actualMode === 'create') {
        // 💰 [DEBUG] Verificar se está enviando os dados completos incluindo pricing
        console.log('🔍 [DEBUG] graphqlInput.responsibleCapturer antes do envio:', graphqlInput.responsibleCapturer)
        console.log('🔍 [DEBUG] graphqlInput completo:', JSON.stringify(graphqlInput, null, 2))
        
        // Criar novo influenciador
        result = await createInfluencerMutation(graphqlInput)
        
        // 💰 Criar pricing separadamente se houver dados válidos
        if (financialData.prices && Object.keys(financialData.prices).length > 0) {
          
          try {
            // Converter pricing para formato do Firebase (apenas preços válidos > 0)
            const pricingServices: any = {}
            
            // Função para validar preço
            const isValidPrice = (price: any) => {
              return price && Number(price) > 0
            }
            
            if (financialData.prices.instagramStory && isValidPrice(financialData.prices.instagramStory.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.story = { price: financialData.prices.instagramStory.price, currency: 'BRL' }
            }
            if (financialData.prices.instagramReel && isValidPrice(financialData.prices.instagramReel.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.reel = { price: financialData.prices.instagramReel.price, currency: 'BRL' }
            }
            if (financialData.prices.tiktokVideo && isValidPrice(financialData.prices.tiktokVideo.price)) {
              pricingServices.tiktok = pricingServices.tiktok || {}
              pricingServices.tiktok.video = { price: financialData.prices.tiktokVideo.price, currency: 'BRL' }
            }
            if (financialData.prices.youtubeInsertion && isValidPrice(financialData.prices.youtubeInsertion.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.insertion = { price: financialData.prices.youtubeInsertion.price, currency: 'BRL' }
            }
            if (financialData.prices.youtubeDedicated && isValidPrice(financialData.prices.youtubeDedicated.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.dedicated = { price: financialData.prices.youtubeDedicated.price, currency: 'BRL' }
            }
            if (financialData.prices.youtubeShorts && isValidPrice(financialData.prices.youtubeShorts.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.shorts = { price: financialData.prices.youtubeShorts.price, currency: 'BRL' }
            }
            
            // 🔥 CORREÇÃO: Adicionar Facebook, Twitch e Kwai
            if (financialData.prices.facebookPost && isValidPrice(financialData.prices.facebookPost.price)) {
              pricingServices.facebook = pricingServices.facebook || {}
              pricingServices.facebook.post = { price: financialData.prices.facebookPost.price, currency: 'BRL' }
            }
            if (financialData.prices.twitchStream && isValidPrice(financialData.prices.twitchStream.price)) {
              pricingServices.twitch = pricingServices.twitch || {}
              pricingServices.twitch.stream = { price: financialData.prices.twitchStream.price, currency: 'BRL' }
            }
            if (financialData.prices.kwaiVideo && isValidPrice(financialData.prices.kwaiVideo.price)) {
              pricingServices.kwai = pricingServices.kwai || {}
              pricingServices.kwai.video = { price: financialData.prices.kwaiVideo.price, currency: 'BRL' }
            }
            
            // Só criar pricing se houver pelo menos um preço válido
            if (Object.keys(pricingServices).length > 0) {
              
              // Criar pricing via mutation
              const pricingInput = {
                influencerId: result.id,
                services: pricingServices,
                validFrom: new Date().toISOString(),
                notes: 'Preços criados automaticamente pelo formulário'
              }
              
              const pricingResult = await createInfluencerPricing(pricingInput)
            } else {
            }
            
          } catch (pricingError) {
            // Não falhar a criação do influenciador se pricing falhar
          }
        } else {
        }
        
        // 📊 Criar demographics separadamente se houver dados válidos
        if (formData.platforms) {
          
          try {
            const platforms = formData.platforms as any
            
            // Função para validar se os dados de audiência são válidos (qualquer campo preenchido)
            const isValidAudienceData = (platformData: any) => {
              if (!platformData) return false
              
              // Verificar se há dados de gênero
              if (platformData.audienceGender) {
                const total = (platformData.audienceGender.male || 0) + (platformData.audienceGender.female || 0) + (platformData.audienceGender.other || 0)
                if (total > 0) return true
              }
              
              // Verificar se há localizações
              if (platformData.audienceLocations && Array.isArray(platformData.audienceLocations) && platformData.audienceLocations.length > 0) {
                const hasValidLocation = platformData.audienceLocations.some((item: any) => 
                  item.location && item.location.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidLocation) return true
              }
              
              // Verificar se há cidades
              if (platformData.audienceCities && Array.isArray(platformData.audienceCities) && platformData.audienceCities.length > 0) {
                const hasValidCity = platformData.audienceCities.some((item: any) => 
                  item.location && item.location.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidCity) return true
              }
              
              // Verificar se há faixas etárias
              if (platformData.audienceAgeRange && Array.isArray(platformData.audienceAgeRange) && platformData.audienceAgeRange.length > 0) {
                const hasValidAgeRange = platformData.audienceAgeRange.some((item: any) => 
                  item.range && item.range.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidAgeRange) return true
              }
              
              return false
            }
            
            // Instagram demographics - só criar se há dados válidos
            if (platforms.instagram && isValidAudienceData(platforms.instagram)) {
              
              const instagramDemographicInput = {
                influencerId: result.id,
                platform: 'instagram',
                audienceGender: platforms.instagram.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.instagram.audienceLocations || [],
                audienceCities: platforms.instagram.audienceCities || [],
                audienceAgeRange: platforms.instagram.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const instagramDemographicResult = await createAudienceDemographic(instagramDemographicInput)
            } else {
            }
            
            // TikTok demographics - só criar se há dados válidos
            if (platforms.tiktok && isValidAudienceData(platforms.tiktok)) {
              
              const tiktokDemographicInput = {
                influencerId: result.id,
                platform: 'tiktok',
                audienceGender: platforms.tiktok.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.tiktok.audienceLocations || [],
                audienceCities: platforms.tiktok.audienceCities || [],
                audienceAgeRange: platforms.tiktok.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const tiktokDemographicResult = await createAudienceDemographic(tiktokDemographicInput)
            } else {
            }
            
            // YouTube demographics - só criar se há dados válidos
            if (platforms.youtube && isValidAudienceData(platforms.youtube)) {
              
              const youtubeDemographicInput = {
                influencerId: result.id,
                platform: 'youtube',
                audienceGender: platforms.youtube.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.youtube.audienceLocations || [],
                audienceCities: platforms.youtube.audienceCities || [],
                audienceAgeRange: platforms.youtube.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const youtubeDemographicResult = await createAudienceDemographic(youtubeDemographicInput)
            } else {
            }
            
            // Facebook demographics - só criar se há dados válidos
            if (platforms.facebook && isValidAudienceData(platforms.facebook)) {
              
              const facebookDemographicInput = {
                influencerId: result.id,
                platform: 'facebook',
                audienceGender: platforms.facebook.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.facebook.audienceLocations || [],
                audienceCities: platforms.facebook.audienceCities || [],
                audienceAgeRange: platforms.facebook.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const facebookDemographicResult = await createAudienceDemographic(facebookDemographicInput)
            } else {
            }
            
            // Twitch demographics - só criar se há dados válidos
            if (platforms.twitch && isValidAudienceData(platforms.twitch)) {
              
              const twitchDemographicInput = {
                influencerId: result.id,
                platform: 'twitch',
                audienceGender: platforms.twitch.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.twitch.audienceLocations || [],
                audienceCities: platforms.twitch.audienceCities || [],
                audienceAgeRange: platforms.twitch.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const twitchDemographicResult = await createAudienceDemographic(twitchDemographicInput)
            } else {
            }
            
            // Kwai demographics - só criar se há dados válidos
            if (platforms.kwai && isValidAudienceData(platforms.kwai)) {
              
              const kwaiDemographicInput = {
                influencerId: result.id,
                platform: 'kwai',
                audienceGender: platforms.kwai.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.kwai.audienceLocations || [],
                audienceCities: platforms.kwai.audienceCities || [],
                audienceAgeRange: platforms.kwai.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const kwaiDemographicResult = await createAudienceDemographic(kwaiDemographicInput)
            } else {
            }
            
          } catch (demoError) {
            // Não falhar a criação do influenciador se demographics falhar
          }
        }
        
        // 🤝 Criar associações marca-influenciador via GraphQL se houver marcas selecionadas
        if (formData.brands && formData.brands.length > 0) {
          
          try {
            // Verificar se o usuário está autenticado
            if (!currentUser) {
              throw new Error('Usuário não autenticado')
            }


            // Primeiro, verificar quais associações já existem
            const { data: existingAssociationsData } = await apolloClient.query({
              query: gql`
                query GetBrandInfluencerAssociations($userId: ID!, $influencerId: ID) {
                  brandInfluencerAssociations(userId: $userId, influencerId: $influencerId) {
                    id
                    brandId
                    influencerId
                    status
                  }
                }
              `,
              variables: { 
                userId: currentUser.id,
                influencerId: result.id
              },
              fetchPolicy: 'network-only' // Garantir dados frescos
            })

            const existingBrandIds = (existingAssociationsData.brandInfluencerAssociations || []).map((assoc: any) => assoc.brandId)

            // Filtrar apenas marcas que ainda não têm associação
            const brandsToAssociate = formData.brands.filter((brandId: string) => !existingBrandIds.includes(brandId))

            if (brandsToAssociate.length === 0) {
              toast({
                title: "Marcas já associadas",
                description: "Todas as marcas selecionadas já estão associadas a este influenciador.",
                duration: 5000
              })
              return
            }

            // Preparar dados para criar as associações via GraphQL
            const associationPromises = brandsToAssociate.map(async (brandId: string) => {
              const associationInput = {
                userId: currentUser.id,
                brandId: brandId,
                influencerId: result.id,
                status: 'active',
                tags: []
              }
              
              
              try {
                // Usar Apollo Client para criar a associação
                const { data } = await apolloClient.mutate({
                  mutation: gql`
                    mutation CreateBrandInfluencerAssociation($input: CreateBrandInfluencerAssociationInput!) {
                      createBrandInfluencerAssociation(input: $input) {
                        id
                        brandId
                        brandName
                        influencerId
                        influencerName
                        status
                      }
                    }
                  `,
                  variables: { input: associationInput }
                })
                
                return data.createBrandInfluencerAssociation
              } catch (mutationError: any) {
                
                // Verificar se é erro de duplicação ou se a função já tratou a duplicação
                if (mutationError.message && (
                  mutationError.message.includes('já existe') ||
                  mutationError.message.includes('already exists') ||
                  mutationError.message.includes('duplicate')
                )) {
                  return { __isDuplicate: true, brandId } // Marcar como duplicata
                }
                
                throw mutationError // Re-lançar outros erros
              }
            })
            
            const associationResults = await Promise.allSettled(associationPromises)
            
            const successfulAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && 
              result.value !== null && 
              result.value !== undefined &&
              !result.value.__isDuplicate &&
              !result.value.__isExisting
            ).length
            
            const failedAssociations = associationResults.filter(result => 
              result.status === 'rejected'
            ).length
            
            const duplicateAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && (
                result.value === null ||
                result.value === undefined ||
                result.value?.__isDuplicate ||
                result.value?.__isExisting
              )
            ).length
            
            console.log('🤝 [BRAND_ASSOCIATION_GRAPHQL] Resultados:', { 
              successful: successfulAssociations, 
              failed: failedAssociations,
              duplicates: duplicateAssociations
            })
            
            if (successfulAssociations > 0) {
              toast({
                title: "Associações criadas",
                description: `${successfulAssociations} associação(ões) com marcas criadas com sucesso.`,
                duration: 5000
              })
            }
            
            if (duplicateAssociations > 0) {
              toast({
                title: "Associações já existentes",
                description: `${duplicateAssociations} associação(ões) já existiam e foram ignoradas.`,
                duration: 5000
              })
            }
            
            if (failedAssociations > 0) {
              toast({
                title: "Algumas associações falharam",
                description: `${failedAssociations} associação(ões) não puderam ser criadas devido a erros.`,
                variant: "destructive",
                duration: 7000
              })
            }
            
          } catch (error) {
            toast({
              title: "Erro nas associações",
              description: "Houve um problema ao verificar ou criar as associações com marcas.",
              variant: "destructive",
              duration: 7000
            })
          }
        }
        
        // 🔄 INVALIDAR CACHE: Atualizar todas as queries relacionadas aos influenciadores
        console.log('🔄 [CACHE] Invalidando cache após criação do influenciador:', result?.id)
        
        try {
          // 🔧 CORREÇÃO: Invalidação cirúrgica - apenas adicionar novo influenciador ao cache
          await apolloClient.refetchQueries({
            updateCache(cache) {
              // ✅ ADIÇÃO INTELIGENTE: Em vez de evict, adicionar o novo influenciador
              try {
                // Tentar ler a query existente
                const existingData: any = cache.readQuery({
                  query: GET_INFLUENCERS,
                  variables: { 
                    userId: result?.userId || 'system',
                    filters: {},
                    pagination: { limit: 20, offset: 0 }
                  }
                });

                if (existingData?.influencers && result) {
                  // Adicionar o novo influenciador ao início da lista
                  cache.writeQuery({
                    query: GET_INFLUENCERS,
                    variables: { 
                      userId: result.userId || 'system',
                      filters: {},
                      pagination: { limit: 20, offset: 0 }
                    },
                    data: {
                      influencers: {
                        ...existingData.influencers,
                        nodes: [result, ...existingData.influencers.nodes],
                        totalCount: existingData.influencers.totalCount + 1
                      }
                    }
                  });
                  console.log('✅ [CACHE] Novo influenciador adicionado ao cache sem perder outros');
                } else {
                  // Se não há dados existentes, apenas invalidar para que seja refetchado
                  cache.evict({ fieldName: 'influencers' });
                  console.log('⚠️ [CACHE] Não há dados existentes, invalidando para refetch');
                }
              } catch (cacheReadError) {
                // Se não conseguir ler/escrever, apenas invalidar a query específica
                console.warn('⚠️ [CACHE] Não foi possível atualizar cache diretamente, invalidando queries');
                cache.evict({ fieldName: 'influencers' });
              }
            }
          })
          
          console.log('✅ [CACHE] Cache atualizado com sucesso após criação')
        } catch (cacheError) {
          console.warn('⚠️ [CACHE] Erro ao atualizar cache após criação:', cacheError)
        }
        
        // 📢 NOTIFICAR COMPONENTE PAI: Chamar callback se definido
        if (onSubmit && typeof onSubmit === 'function') {
          try {
            await onSubmit(result)
            console.log('✅ [CALLBACK] Callback onSubmit executado com sucesso')
          } catch (callbackError) {
            console.warn('⚠️ [CALLBACK] Erro ao executar callback onSubmit:', callbackError)
          }
        }

        // 🔄 FORÇAR ATUALIZAÇÃO ADICIONAL: Broadcast custom event para componentes que podem estar escutando
        try {
          window.dispatchEvent(new CustomEvent('influencer-updated', {
            detail: { 
              influencerId: result?.id,
              action: 'create',
              data: result
            }
          }))
          console.log('📡 [BROADCAST] Evento influencer-updated disparado')
        } catch (eventError) {
          console.warn('⚠️ [BROADCAST] Erro ao disparar evento custom:', eventError)
        }
        
        // Fechar o formulário após criar
        onOpenChange(false)
        
        // Retornar resultado para processamento de screenshots
        return result
        
      } else if (actualMode === 'edit' && initialData?.id) {
        // 🔥 CORREÇÃO: Atualizar com TODOS os campos disponíveis no UpdateInfluencerInput
        const updateInput: any = {
          // Campos básicos
          name: graphqlInput.name,
          email: graphqlInput.email,
          phone: graphqlInput.phone,
          whatsapp: graphqlInput.whatsapp,
          
          // Localização
          country: graphqlInput.country,
          state: graphqlInput.state,     
          city: graphqlInput.city,       
          location: graphqlInput.location,
          
          // Dados demográficos
          age: graphqlInput.age,         
          gender: graphqlInput.gender,   
          bio: graphqlInput.bio,
          
          // Visual
          avatar: graphqlInput.avatar,
          
          // Categorização
          category: graphqlInput.category,
          categories: graphqlInput.categories,
          
          // Métricas
          totalFollowers: graphqlInput.totalFollowers,
          engagementRate: graphqlInput.engagementRate,
          
          // Status e verificação
          isVerified: graphqlInput.isVerified,
          isAvailable: graphqlInput.isAvailable,
          status: graphqlInput.status,
          
          // Dados profissionais
          promotesTraders: graphqlInput.promotesTraders,
          responsibleName: graphqlInput.responsibleName,
          agencyName: graphqlInput.agencyName,
          // 🔥 CORREÇÃO: Incluir responsibleCapturer no updateInput
          responsibleCapturer: graphqlInput.responsibleCapturer,
          
          // Rede social principal
          mainNetwork: graphqlInput.mainNetwork,
          mainPlatform: graphqlInput.mainPlatform,
          
          // Campos diretos das plataformas (nova estrutura)
          instagramUsername: graphqlInput.instagramUsername,
          instagramFollowers: graphqlInput.instagramFollowers,
          instagramEngagementRate: graphqlInput.instagramEngagementRate,
          instagramStoriesViews: graphqlInput.instagramStoriesViews,
          instagramReelsViews: graphqlInput.instagramReelsViews,
          
          tiktokUsername: graphqlInput.tiktokUsername,
          tiktokFollowers: graphqlInput.tiktokFollowers,
          tiktokEngagementRate: graphqlInput.tiktokEngagementRate,
          tiktokVideoViews: graphqlInput.tiktokVideoViews,
          
          youtubeUsername: graphqlInput.youtubeUsername,
          youtubeFollowers: graphqlInput.youtubeFollowers,
          youtubeSubscribers: graphqlInput.youtubeSubscribers,
          youtubeEngagementRate: graphqlInput.youtubeEngagementRate,
          youtubeShortsViews: graphqlInput.youtubeShortsViews,
          youtubeLongFormViews: graphqlInput.youtubeLongFormViews,
          
          facebookUsername: graphqlInput.facebookUsername,
          facebookFollowers: graphqlInput.facebookFollowers,
          facebookEngagementRate: graphqlInput.facebookEngagementRate,
          facebookViews: graphqlInput.facebookViews,
          facebookReelsViews: graphqlInput.facebookReelsViews,
          facebookStoriesViews: graphqlInput.facebookStoriesViews,
          
          twitchUsername: graphqlInput.twitchUsername,
          twitchFollowers: graphqlInput.twitchFollowers,
          twitchEngagementRate: graphqlInput.twitchEngagementRate,
          twitchViews: graphqlInput.twitchViews,
          
          kwaiUsername: graphqlInput.kwaiUsername,
          kwaiFollowers: graphqlInput.kwaiFollowers,
          kwaiEngagementRate: graphqlInput.kwaiEngagementRate,
          kwaiViews: graphqlInput.kwaiViews
        }
        
        result = await updateInfluencerMutation(String(initialData.id), updateInput)
        
        // 🔄 INVALIDAR CACHE: Atualizar todas as queries relacionadas aos influenciadores
        console.log('🔄 [CACHE] Invalidando cache após edição do influenciador:', initialData.id)
        
        try {
          // 🔧 CORREÇÃO: Atualização cirúrgica do influenciador editado
          await apolloClient.refetchQueries({
            updateCache(cache) {
              // ✅ ATUALIZAÇÃO CIRÚRGICA: Atualizar apenas o influenciador editado
              try {
                const influencerId = String(initialData.id);
                
                // Tentar ler a query existente
                const existingData: any = cache.readQuery({
                  query: GET_INFLUENCERS,
                  variables: { 
                    userId: result?.userId || initialData.userId || 'system',
                    filters: {},
                    pagination: { limit: 20, offset: 0 }
                  }
                });

                if (existingData?.influencers && result) {
                  // Atualizar o influenciador específico na lista
                  const updatedNodes = existingData.influencers.nodes.map((inf: any) => 
                    inf.id === influencerId ? result : inf
                  );
                  
                  cache.writeQuery({
                    query: GET_INFLUENCERS,
                    variables: { 
                      userId: result.userId || initialData.userId || 'system',
                      filters: {},
                      pagination: { limit: 20, offset: 0 }
                    },
                    data: {
                      influencers: {
                        ...existingData.influencers,
                        nodes: updatedNodes
                      }
                    }
                  });
                  console.log('✅ [CACHE] Influenciador atualizado no cache sem perder outros');
                } else {
                  // Se não há dados existentes, apenas invalidar para que seja refetchado
                  cache.evict({ fieldName: 'influencers' });
                  console.log('⚠️ [CACHE] Não há dados existentes, invalidando para refetch');
                }
                
                // Evict apenas o influenciador específico para garantir dados frescos
                const cacheId = cache.identify({ 
                  __typename: 'Influencer', 
                  id: influencerId 
                });
                if (cacheId) {
                  cache.evict({ id: cacheId });
                  console.log(`🔄 [CACHE] Evicted cache específico do influenciador: ${influencerId}`);
                }
              } catch (cacheReadError) {
                // Se não conseguir ler/escrever, apenas invalidar o influenciador específico
                console.warn('⚠️ [CACHE] Não foi possível atualizar cache diretamente, invalidando apenas o influenciador');
                const cacheId = cache.identify({ 
                  __typename: 'Influencer', 
                  id: String(initialData.id) 
                });
                if (cacheId) {
                  cache.evict({ id: cacheId });
                }
              }
            }
          })
          
          console.log('✅ [CACHE] Cache atualizado com sucesso após edição')
        } catch (cacheError) {
          console.warn('⚠️ [CACHE] Erro ao atualizar cache após edição:', cacheError)
        }

        // 📢 NOTIFICAR COMPONENTE PAI: Chamar callback se definido
        if (onSubmit && typeof onSubmit === 'function') {
          try {
            await onSubmit(result)
            console.log('✅ [CALLBACK] Callback onSubmit executado com sucesso após edição')
          } catch (callbackError) {
            console.warn('⚠️ [CALLBACK] Erro ao executar callback onSubmit após edição:', callbackError)
          }
        }

        // ✅ CORREÇÃO: Não disparar evento custom para edição
        // O Apollo Cache já foi atualizado pela mutation e o useInfluencersGraphQL 
        // automaticamente atualiza o estado local via onCompleted
        console.log('✅ [CACHE] Edição concluída - Apollo Cache atualizado automaticamente')
        
        // 💰 ATUALIZAR/CRIAR PRICING SEPARADAMENTE DURANTE EDIÇÃO (APENAS COM DADOS VÁLIDOS)
        if (financialData.prices && Object.keys(financialData.prices).length > 0) {
          
          try {
            // Converter pricing para formato do Firebase (apenas preços válidos > 0)
            const pricingServices: any = {}
            
            // Função para validar preço
            const isValidPrice = (price: any) => {
              return price && Number(price) > 0
            }
            
            if (financialData.prices.instagramStory && isValidPrice(financialData.prices.instagramStory.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.story = { price: financialData.prices.instagramStory.price, currency: 'BRL' }
            }
            if (financialData.prices.instagramReel && isValidPrice(financialData.prices.instagramReel.price)) {
              pricingServices.instagram = pricingServices.instagram || {}
              pricingServices.instagram.reel = { price: financialData.prices.instagramReel.price, currency: 'BRL' }
            }
            if (financialData.prices.tiktokVideo && isValidPrice(financialData.prices.tiktokVideo.price)) {
              pricingServices.tiktok = pricingServices.tiktok || {}
              pricingServices.tiktok.video = { price: financialData.prices.tiktokVideo.price, currency: 'BRL' }
            }
            if (financialData.prices.youtubeInsertion && isValidPrice(financialData.prices.youtubeInsertion.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.insertion = { price: financialData.prices.youtubeInsertion.price, currency: 'BRL' }
            }
            if (financialData.prices.youtubeDedicated && isValidPrice(financialData.prices.youtubeDedicated.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.dedicated = { price: financialData.prices.youtubeDedicated.price, currency: 'BRL' }
            }
            if (financialData.prices.youtubeShorts && isValidPrice(financialData.prices.youtubeShorts.price)) {
              pricingServices.youtube = pricingServices.youtube || {}
              pricingServices.youtube.shorts = { price: financialData.prices.youtubeShorts.price, currency: 'BRL' }
            }
            
            // 🔥 CORREÇÃO: Adicionar Facebook, Twitch e Kwai
            if (financialData.prices.facebookPost && isValidPrice(financialData.prices.facebookPost.price)) {
              pricingServices.facebook = pricingServices.facebook || {}
              pricingServices.facebook.post = { price: financialData.prices.facebookPost.price, currency: 'BRL' }
            }
            if (financialData.prices.twitchStream && isValidPrice(financialData.prices.twitchStream.price)) {
              pricingServices.twitch = pricingServices.twitch || {}
              pricingServices.twitch.stream = { price: financialData.prices.twitchStream.price, currency: 'BRL' }
            }
            if (financialData.prices.kwaiVideo && isValidPrice(financialData.prices.kwaiVideo.price)) {
              pricingServices.kwai = pricingServices.kwai || {}
              pricingServices.kwai.video = { price: financialData.prices.kwaiVideo.price, currency: 'BRL' }
            }
            
            // Só criar pricing se houver pelo menos um preço válido
            if (Object.keys(pricingServices).length > 0) {
              
              // Criar novo pricing (desativa o anterior automaticamente)
              const pricingInput = {
                influencerId: initialData.id,
                services: pricingServices,
                validFrom: new Date().toISOString(),
                notes: 'Preços atualizados via formulário de edição'
              }
              
              const pricingResult = await createInfluencerPricing(pricingInput)
            } else {
            }
            
          } catch (pricingError) {
            // Não falhar a atualização do influenciador se pricing falhar
          }
        } else {
        }
        
        // 📊 ATUALIZAR/CRIAR DEMOGRAPHICS SEPARADAMENTE DURANTE EDIÇÃO (APENAS COM DADOS VÁLIDOS)
        if (formData.platforms) {
          
          try {
            const platforms = formData.platforms as any
            
            // Função para validar se os dados de audiência são válidos (qualquer campo preenchido)
            const isValidAudienceData = (platformData: any) => {
              if (!platformData) return false
              
              // Verificar se há dados de gênero
              if (platformData.audienceGender) {
                const total = (platformData.audienceGender.male || 0) + (platformData.audienceGender.female || 0) + (platformData.audienceGender.other || 0)
                if (total > 0) return true
              }
              
              // Verificar se há localizações
              if (platformData.audienceLocations && Array.isArray(platformData.audienceLocations) && platformData.audienceLocations.length > 0) {
                const hasValidLocation = platformData.audienceLocations.some((item: any) => 
                  item.location && item.location.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidLocation) return true
              }
              
              // Verificar se há cidades
              if (platformData.audienceCities && Array.isArray(platformData.audienceCities) && platformData.audienceCities.length > 0) {
                const hasValidCity = platformData.audienceCities.some((item: any) => 
                  item.city && item.city.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidCity) return true
              }
              
              // Verificar se há faixas etárias
              if (platformData.audienceAgeRange && Array.isArray(platformData.audienceAgeRange) && platformData.audienceAgeRange.length > 0) {
                const hasValidAgeRange = platformData.audienceAgeRange.some((item: any) => 
                  item.range && item.range.trim() !== '' && typeof item.percentage === 'number' && item.percentage > 0
                )
                if (hasValidAgeRange) return true
              }
              
              return false
            }
            
            // Instagram demographics - só criar se há dados válidos
            if (platforms.instagram && isValidAudienceData(platforms.instagram)) {
              
              const instagramDemographicInput = {
                influencerId: initialData.id,
                platform: 'instagram',
                audienceGender: platforms.instagram.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.instagram.audienceLocations || [],
                audienceCities: platforms.instagram.audienceCities || [],
                audienceAgeRange: platforms.instagram.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              
              const instagramDemographicResult = await createAudienceDemographic(instagramDemographicInput)
            } else {
            }
            
            // TikTok demographics - só criar se há dados válidos
            if (platforms.tiktok && isValidAudienceData(platforms.tiktok)) {
              
              const tiktokDemographicInput = {
                influencerId: initialData.id,
                platform: 'tiktok',
                audienceGender: platforms.tiktok.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.tiktok.audienceLocations || [],
                audienceCities: platforms.tiktok.audienceCities || [],
                audienceAgeRange: platforms.tiktok.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const tiktokDemographicResult = await createAudienceDemographic(tiktokDemographicInput)
            } else {
            }
            
            // YouTube demographics - só criar se há dados válidos
            if (platforms.youtube && isValidAudienceData(platforms.youtube)) {
              
              const youtubeDemographicInput = {
                influencerId: initialData.id,
                platform: 'youtube',
                audienceGender: platforms.youtube.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.youtube.audienceLocations || [],
                audienceCities: platforms.youtube.audienceCities || [],
                audienceAgeRange: platforms.youtube.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const youtubeDemographicResult = await createAudienceDemographic(youtubeDemographicInput)
            } else {
            }
            
            // Facebook demographics - só criar se há dados válidos
            if (platforms.facebook && isValidAudienceData(platforms.facebook)) {
              
              const facebookDemographicInput = {
                influencerId: initialData.id,
                platform: 'facebook',
                audienceGender: platforms.facebook.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.facebook.audienceLocations || [],
                audienceCities: platforms.facebook.audienceCities || [],
                audienceAgeRange: platforms.facebook.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const facebookDemographicResult = await createAudienceDemographic(facebookDemographicInput)
            } else {
            }
            
            // Twitch demographics - só criar se há dados válidos
            if (platforms.twitch && isValidAudienceData(platforms.twitch)) {
              
              const twitchDemographicInput = {
                influencerId: initialData.id,
                platform: 'twitch',
                audienceGender: platforms.twitch.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.twitch.audienceLocations || [],
                audienceCities: platforms.twitch.audienceCities || [],
                audienceAgeRange: platforms.twitch.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const twitchDemographicResult = await createAudienceDemographic(twitchDemographicInput)
            } else {
            }
            
            // Kwai demographics - só criar se há dados válidos
            if (platforms.kwai && isValidAudienceData(platforms.kwai)) {
              
              const kwaiDemographicInput = {
                influencerId: initialData.id,
                platform: 'kwai',
                audienceGender: platforms.kwai.audienceGender || { male: 0, female: 0, other: 0 },
                audienceLocations: platforms.kwai.audienceLocations || [],
                audienceCities: platforms.kwai.audienceCities || [],
                audienceAgeRange: platforms.kwai.audienceAgeRange || [],
                captureDate: new Date().toISOString(),
                source: 'form'
              }
              
              const kwaiDemographicResult = await createAudienceDemographic(kwaiDemographicInput)
            } else {
            }
            
          } catch (demoError) {
            // Não falhar a atualização do influenciador se demographics falhar
          }
        }
        
        // 🤝 Criar associações marca-influenciador se houver marcas selecionadas (MODO EDIÇÃO)
        if (formData.brands && formData.brands.length > 0) {
          
          try {
            // Verificar se o usuário está autenticado
            if (!currentUser) {
              throw new Error('Usuário não autenticado')
            }


            // Preparar dados para criar as associações via GraphQL
            const associationPromises = formData.brands.map(async (brandId) => {
              const associationInput = {
                userId: currentUser.id,
                brandId: brandId,
                influencerId: initialData.id,
                status: 'active',
                tags: [],
                notes: ''
              }
              
              
              try {
                const associationResult = await createBrandInfluencerAssociation(associationInput)
                return associationResult
              } catch (mutationError: any) {
                
                // Verificar se é erro de duplicação
                if (mutationError.message && (
                  mutationError.message.includes('já existe') ||
                  mutationError.message.includes('already exists') ||
                  mutationError.message.includes('duplicate')
                )) {
                  return { __isDuplicate: true, brandId } // Marcar como duplicata
                }
                
                throw mutationError // Re-lançar outros erros
              }
            })
            
            const associationResults = await Promise.allSettled(associationPromises)
            
            const successfulAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && 
              result.value !== null && 
              result.value !== undefined &&
              !result.value.__isDuplicate &&
              !result.value.__isExisting
            ).length
            
            const failedAssociations = associationResults.filter(result => 
              result.status === 'rejected'
            ).length
            
            const duplicateAssociations = associationResults.filter(result => 
              result.status === 'fulfilled' && (
                result.value === null ||
                result.value === undefined ||
                result.value?.__isDuplicate ||
                result.value?.__isExisting
              )
            ).length
            
            console.log('🤝 [BRAND_ASSOCIATION_EDIT] Resultados:', { 
              successful: successfulAssociations, 
              failed: failedAssociations,
              duplicates: duplicateAssociations,
              details: associationResults.map(result => ({
                status: result.status,
                reason: result.status === 'rejected' ? result.reason : 'success'
              }))
            })
            
            if (successfulAssociations > 0) {
              toast({
                title: "Associações criadas",
                description: `${successfulAssociations} associação(ões) com marcas criadas com sucesso.`,
                duration: 5000
              })
            }
            
            if (duplicateAssociations > 0) {
              toast({
                title: "Associações já existentes",
                description: `${duplicateAssociations} associação(ões) já existiam e foram ignoradas.`,
                duration: 5000
              })
            }
            
            if (failedAssociations > 0) {
              const failedDetails = associationResults
                .filter(result => result.status === 'rejected')
                .map(result => (result as any).reason?.message || 'Erro desconhecido')
                .join(', ')
              
              
              toast({
                title: "Algumas associações falharam",
                description: `${failedAssociations} associação(ões) não puderam ser criadas. Detalhes: ${failedDetails}`,
                variant: "destructive",
                duration: 10000
              })
            }
            
          } catch (error) {
            toast({
              title: "Erro nas associações",
              description: "Houve um problema ao associar as marcas via GraphQL. Tente novamente.",
              variant: "destructive",
              duration: 7000
            })
          }
        } else {
        }
        
        // Fechar o formulário após salvar
        onOpenChange(false)
        
        // Retornar resultado para processamento de screenshots
        return result
      }
      
      return result
      
    } catch (error) {
      throw error
    }
  }
  
  // Os dados iniciais são passados diretamente, sem carregamento de marcas associadas
  // O campo 'brands' permanece vazio para novas seleções
  // As marcas já associadas são gerenciadas pelo BrandsAssociationSection via hooks GraphQL
  const processedData = useMemo(() => {
    return initialData;
  }, [initialData]);

  // Usar dados processados diretamente

  // Hook do formulário sem submit customizado para evitar circular calls
  const {
    form,
    handleSubmit: formHandleSubmit,
    resetForm,
    isSubmitting,
    errors,
    isValid,
    calculateMetrics,
    activePlatforms,
    addPlatform,
    removePlatform,
    availablePlatforms
  } = useInfluencerForm({
    initialData: processedData
    // Removido onSubmit para evitar circular call
  })

  // Criar nossa própria função de submit que usa o form do hook E processa screenshots + avatar
  const handleSubmit = form.handleSubmit(async (formData: InfluencerFormData) => {
    try {
      // PRIMEIRO: Processar AVATAR se houver arquivo selecionado
      if (formData.personalInfo?.avatarFile) {
        console.log('📸 [AVATAR] Arquivo detectado - fazendo upload...')
        
        try {
          const avatarFile = formData.personalInfo.avatarFile as File
          const influencerId = initialData?.id || `temp_${Date.now()}`
          
          // Fazer upload via API
          const formDataUpload = new FormData()
          formDataUpload.append('avatar', avatarFile)
          formDataUpload.append('influencerId', influencerId)
          
          const uploadResponse = await fetch('/api/influencers/avatar', {
            method: 'POST',
            body: formDataUpload
          })
          
          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json()
            throw new Error(errorData.error || 'Erro ao fazer upload do avatar')
          }
          
          const uploadResult = await uploadResponse.json()
          console.log('✅ [AVATAR] Upload concluído:', uploadResult.avatarUrl)
          
          // Atualizar o formulário com a URL do avatar
          form.setValue('personalInfo.avatar', uploadResult.avatarUrl, { shouldValidate: false })
          
          // Limpar o arquivo temporário
          form.setValue('personalInfo.avatarFile', undefined as any, { shouldValidate: false })
          
          // Atualizar os dados do formulário
          formData.personalInfo.avatar = uploadResult.avatarUrl
          delete formData.personalInfo.avatarFile
          
          toast({
            title: "Avatar enviado",
            description: "Foto de perfil enviada com sucesso"
          })
          
        } catch (uploadError) {
          console.error('❌ [AVATAR] Erro no upload:', uploadError)
          toast({
            title: "Erro no Upload do Avatar",
            description: "Falha ao enviar a foto de perfil. O influenciador será criado sem foto.",
            variant: "destructive",
          })
          // Continuar sem avatar
        }
      }

      // SEGUNDO: Criar/atualizar o influenciador
      const result = await handleGraphQLSubmit(formData);
      
      // TERCEIRO: Processar screenshots pendentes APÓS criar o influenciador
      const pendingScreenshots = form.getValues('pendingScreenshots') || []
      
      if (pendingScreenshots && pendingScreenshots.length > 0 && result?.id) {
        console.log(`📸 [FORM-SUBMIT] Processando ${pendingScreenshots.length} screenshots em lote via GraphQL`)
        
        try {
          const influencerId = result.id // Usar ID real do influenciador criado
          
          // Converter arquivos para base64 para envio via GraphQL
          const screenshots = await Promise.all(
            pendingScreenshots.map(async (pendingFile: any) => {
              return new Promise<any>((resolve, reject) => {
                const reader = new FileReader()
                reader.onload = () => {
                  resolve({
                    platform: pendingFile.platform,
                    filename: pendingFile.file.name,
                    fileData: reader.result as string,
                    contentType: pendingFile.file.type,
                    size: pendingFile.file.size
                  })
                }
                reader.onerror = reject
                reader.readAsDataURL(pendingFile.file)
              })
            })
          )

          console.log(`📸 [FORM-SUBMIT] Fazendo upload em lote via GraphQL mutation (${screenshots.length} arquivos)`)
          
          // Fazer upload em lote via GraphQL mutation
          const uploadResponse = await fetch('/api/graphql', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: `
                mutation UploadScreenshotsBatch($input: UploadScreenshotsBatchInput!) {
                  uploadScreenshotsBatch(input: $input) {
                    success
                    totalUploaded
                    results {
                      id
                      influencerId
                      platform
                      url
                      filename
                      size
                      contentType
                      uploadedAt
                      uploadedBy
                    }
                    errors {
                      platform
                      filename
                      error
                    }
                  }
                }
              `,
              variables: {
                input: {
                  influencerId,
                  screenshots
                }
              }
            })
          })

          if (!uploadResponse.ok) {
            throw new Error(`Erro HTTP: ${uploadResponse.status}`)
          }

          const uploadResult = await uploadResponse.json()

          if (uploadResult.errors) {
            throw new Error(uploadResult.errors[0]?.message || 'Erro GraphQL desconhecido')
          }

          const screenshotResult = uploadResult.data?.uploadScreenshotsBatch
          
          if (!screenshotResult.success && screenshotResult.totalUploaded === 0) {
            throw new Error('Todos os uploads falharam')
          }

          console.log(`✅ [FORM-SUBMIT] Upload em lote GraphQL concluído:`, {
            total: screenshots.length,
            sucessos: screenshotResult.totalUploaded,
            falhas: screenshotResult.errors?.length || 0
          })
          
          // Atualizar URLs dos screenshots nas plataformas
          if (screenshotResult.results && screenshotResult.results.length > 0) {
            const currentPlatforms = form.getValues('platforms') || {}
            
            screenshotResult.results.forEach((upload: any) => {
              const platformKey = upload.platform
              const existingPlatform = (currentPlatforms as any)[platformKey]
              
              if (existingPlatform) {
                if (!existingPlatform.screenshots) {
                  existingPlatform.screenshots = []
                }
                existingPlatform.screenshots.push(upload.url)
                
                // Atualizar a plataforma específica
                form.setValue(`platforms.${platformKey}.screenshots` as any, existingPlatform.screenshots, { shouldValidate: false })
              }
            })
            
            // Toast com informações do resultado
            if (screenshotResult.errors && screenshotResult.errors.length > 0) {
              toast({
                title: "Upload parcialmente concluído",
                description: `${screenshotResult.totalUploaded} de ${screenshots.length} screenshot(s) enviado(s). ${screenshotResult.errors.length} falhou(aram).`,
                variant: "default"
              })
            } else {
              toast({
                title: "Screenshots enviados",
                description: `${screenshotResult.totalUploaded} screenshot(s) enviado(s) com sucesso`
              })
            }
          }
          
          // Limpar arquivos pendentes
          form.setValue('pendingScreenshots', [], { shouldValidate: false })
          
        } catch (screenshotError) {
          console.error('❌ [SCREENSHOTS] Erro no upload:', screenshotError)
          toast({
            title: "Erro nos Screenshots",
            description: "Alguns screenshots podem não ter sido enviados. O influenciador foi criado com sucesso.",
            variant: "default",
          })
        }
      }

      return result;
    } catch (error) {
      console.error('Erro no submit:', error);
      throw error;
    }
  })

  // 🔄 Converter dados do formulário para estrutura completa (influencers + financial)
  const convertToCompleteStructure = (data: InfluencerFormData): { influencerData: any, financialData: any } => {
    
    let totalFollowers = 0
    let totalEngagement = 0
    let platformCount = 0
    const socialNetworks: any = {}
    
    // Verificar se há dados de plataformas
    if (data.platforms) {
      const platforms = data.platforms as any
      
      // Instagram
      if (platforms.instagram) {
        const instagram = platforms.instagram
        totalFollowers += instagram.followers || 0
        if (instagram.engagementRate) {
          totalEngagement += instagram.engagementRate
          platformCount++
        }
        
        socialNetworks.instagram = {
          username: instagram.username || '',
          followers: instagram.followers || 0,
          engagementRate: instagram.engagementRate || 0,
          views: instagram.views || {},
          storiesViews: instagram.views?.storiesViews || 0,
          reelsViews: instagram.views?.reelsViews || 0
          // ✅ LIMPEZA: Demographics e pricing são gerenciados separadamente
        }
      } else {
      }
      
      // TikTok
      if (platforms.tiktok) {
        const tiktok = platforms.tiktok
        totalFollowers += tiktok.followers || 0
        if (tiktok.engagementRate) {
          totalEngagement += tiktok.engagementRate
          platformCount++
        }
        
        socialNetworks.tiktok = {
          username: tiktok.username || '',
          followers: tiktok.followers || 0,
          engagementRate: tiktok.engagementRate || 0,
          views: tiktok.views || {},
          videoViews: tiktok.views?.videoViews || 0
          // ✅ LIMPEZA: Demographics e pricing são gerenciados separadamente
        }
      } else {
      }
      
      // YouTube
      if (platforms.youtube) {
        const youtube = platforms.youtube
        totalFollowers += youtube.followers || 0
        if (youtube.engagementRate) {
          totalEngagement += youtube.engagementRate
          platformCount++
        }
        
        socialNetworks.youtube = {
          username: youtube.username || '',
          followers: youtube.followers || 0,
          subscribers: youtube.followers || 0,
          engagementRate: youtube.engagementRate || 0,
          views: youtube.views || {},
          shortsViews: youtube.views?.shortsViews || 0,
          longFormViews: youtube.views?.longFormViews || 0
          // ✅ LIMPEZA: Demographics e pricing são gerenciados separadamente
        }
      } else {
      }
      
      // Facebook
      if (platforms.facebook) {
        socialNetworks.facebook = {
          username: platforms.facebook.username || '',
          followers: platforms.facebook.followers || 0,
          engagementRate: platforms.facebook.engagementRate || 0,
          views: platforms.facebook.views || {},
          facebookReelsViews: platforms.facebook.views?.reelsViews || 0,
          facebookStoriesViews: platforms.facebook.views?.storiesViews || 0
          // 🔥 PRICING REMOVIDO: Agora gerenciado separadamente
        }
        totalFollowers += platforms.facebook.followers || 0
        if (platforms.facebook.engagementRate) {
          totalEngagement += platforms.facebook.engagementRate
          platformCount++
        }
      } else {
      }
      
      // Twitch
      if (platforms.twitch) {
        socialNetworks.twitch = {
          username: platforms.twitch.username || '',
          followers: platforms.twitch.followers || 0,
          engagementRate: platforms.twitch.engagementRate || 0,
          twitchViews: platforms.twitch.avgViews || 0
          // ✅ LIMPEZA: Pricing é gerenciado separadamente
        }
        totalFollowers += platforms.twitch.followers || 0
        if (platforms.twitch.engagementRate) {
          totalEngagement += platforms.twitch.engagementRate
          platformCount++
        }
      } else {
      }
      
      // Kwai
      if (platforms.kwai) {
        socialNetworks.kwai = {
          username: platforms.kwai.username || '',
          followers: platforms.kwai.followers || 0,
          engagementRate: platforms.kwai.engagementRate || 0,
          kwaiViews: platforms.kwai.avgViews || 0
          // ✅ LIMPEZA: Pricing é gerenciado separadamente
        }
        totalFollowers += platforms.kwai.followers || 0
        if (platforms.kwai.engagementRate) {
          totalEngagement += platforms.kwai.engagementRate
          platformCount++
        }
      } else {
      }
      
      console.log('👁️ Dados de visualizações incluídos:', {
        instagram: socialNetworks.instagram?.views,
        tiktok: socialNetworks.tiktok?.views,
        youtube: socialNetworks.youtube?.views
      })
      console.log('🔍 [DEBUG] platforms completo:', JSON.stringify(platforms, null, 2))
      console.log('🔍 [DEBUG] Twitch avgViews nos platforms:', platforms?.twitch?.avgViews)
      console.log('🔍 [DEBUG] Kwai avgViews nos platforms:', platforms?.kwai?.avgViews)
    } else {
    }
    
    const overallEngagementRate = platformCount > 0 ? totalEngagement / platformCount : 0
    
    // Mapear gênero para o formato esperado
    const genderMap: { [key: string]: 'male' | 'female' | 'other' | 'not_specified' } = {
      'Masculino': 'male',
      'Feminino': 'female'
    }
    

    
    // Dados do influenciador (coleção influencers)
    const influencerData = {
      // Dados pessoais
      name: data.personalInfo?.name || '',
      email: data.contact?.email || '',
      phone: data.contact?.whatsapp || '',
      whatsapp: data.contact?.whatsapp || '',
      
      // Localização
      country: data.location?.country || 'Brasil',
      state: data.location?.state || '',
      city: data.location?.city || '',
      location: data.location?.city && data.location?.state 
        ? `${data.location.city}/${data.location.state}` 
        : '',
      
      // Dados demográficos
      age: data.personalInfo?.age || undefined,
      gender: genderMap[data.personalInfo?.gender || ''] || 'not_specified',
      bio: data.personalInfo?.bio || '',
      
      // Visuais
      avatar: data.personalInfo?.avatar || '',
      backgroundImage: '',
      gradient: `linear-gradient(135deg, #ff0074 0%, #5600ce 100%)`,
      
      // Categorização
      category: data.business?.categories?.[0] || null,
      categories: data.business?.categories || [],
      mainCategories: data.business?.categories || [],
      
      // Métricas e engajamento
      totalFollowers: totalFollowers,
      engagementRate: Math.round(overallEngagementRate * 100) / 100,
      rating: 0,
      
      // Status e verificação
      isVerified: data.personalInfo?.verified || false,
      isAvailable: true,
      status: 'active',
      
      // Distribuição de gênero da audiência (média entre plataformas)
      audienceGender: calculateAverageAudienceGender(data.platforms),
      
      // Redes sociais (campos diretos)
      instagramUsername: socialNetworks.instagram?.username || '',
      instagramFollowers: socialNetworks.instagram?.followers || 0,
      instagramEngagementRate: socialNetworks.instagram?.engagementRate || 0,
      instagramStoriesViews: socialNetworks.instagram?.storiesViews || 0,
      instagramReelsViews: socialNetworks.instagram?.reelsViews || 0,
      
      tiktokUsername: socialNetworks.tiktok?.username || '',
      tiktokFollowers: socialNetworks.tiktok?.followers || 0,
      tiktokEngagementRate: socialNetworks.tiktok?.engagementRate || 0,
      tiktokVideoViews: socialNetworks.tiktok?.videoViews || 0,
      
      youtubeUsername: socialNetworks.youtube?.username || '',
      youtubeFollowers: socialNetworks.youtube?.followers || 0,
      youtubeSubscribers: socialNetworks.youtube?.subscribers || 0,
      youtubeEngagementRate: socialNetworks.youtube?.engagementRate || 0,
      youtubeShortsViews: socialNetworks.youtube?.shortsViews || 0,
      youtubeLongFormViews: socialNetworks.youtube?.longFormViews || 0,
      
      facebookUsername: socialNetworks.facebook?.username || '',
      facebookFollowers: socialNetworks.facebook?.followers || 0,
      facebookEngagementRate: socialNetworks.facebook?.engagementRate || 0,
      facebookReelsViews: socialNetworks.facebook?.facebookReelsViews || 0,
      facebookStoriesViews: socialNetworks.facebook?.facebookStoriesViews || 0,
      
      twitchUsername: socialNetworks.twitch?.username || '',
      twitchFollowers: socialNetworks.twitch?.followers || 0,
      twitchEngagementRate: socialNetworks.twitch?.engagementRate || 0,
      twitchViews: socialNetworks.twitch?.twitchViews || 0,
      
      kwaiUsername: socialNetworks.kwai?.username || '',
      kwaiFollowers: socialNetworks.kwai?.followers || 0,
      kwaiEngagementRate: socialNetworks.kwai?.engagementRate || 0,
      kwaiViews: socialNetworks.kwai?.kwaiViews || 0,
      
      // Conteúdo e especialização
      contentTypes: data.business?.categories || [],
      contentLanguages: ['pt-BR'],
      specialties: data.business?.categories || [],
      
      // Configurações profissionais
      promotesTraders: data.business?.promotesTraders || false,
      responsibleName: data.business?.responsibleName || '',
      agencyName: data.business?.agencyName || '',
      responsibleCapturer: data.business?.responsibleCapturer || '',
      
      // Plataforma principal
      mainPlatform: data.mainPlatform || '',
      mainNetwork: data.mainPlatform || '',
      
      // Tags e notas (vazios inicialmente)
      tags: [],
      notes: [],
      
      // Metadados (serão preenchidos pelo backend)
      userId: '',
      createdAt: new Date(),
      updatedAt: new Date()
    }
    

    
    // Dados financeiros (coleção influencer_financials)
    const financialData = {
      influencerId: '', // Será preenchido após criar o influenciador
      responsibleName: data.business?.responsibleName || '',
      agencyName: data.business?.agencyName || '',
      responsibleCapturer: data.business?.responsibleCapturer || '',
      email: data.contact?.email || '',
      whatsapp: data.contact?.whatsapp || '',
      
      // Visualizações em stories (pegar do Instagram se disponível)
      instagramStoriesViews: (data.platforms as any)?.instagram?.metrics?.storiesViews || 0,
      
      // Preços
      prices: extractPrices(data.platforms),
      
      // Histórico de marcas
      brandHistory: extractBrandHistory(data.platforms),
      
      // Dados adicionais
      additionalData: {
        contentType: data.business?.categories || [],
        promotesTraders: data.business?.promotesTraders || false,
        responsibleRecruiter: data.business?.responsibleCapturer || '',
        responsibleCapturer: data.business?.responsibleCapturer || '', // 🔥 CORREÇÃO: Garantir que seja salvo também neste campo
        socialMediaScreenshots: extractScreenshots(data.platforms),
        notes: '',
        documents: []
      },
      
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    
    return { influencerData, financialData }
  }
  
  // Função auxiliar para calcular média de gênero da audiência
  const calculateAverageAudienceGender = (platforms: any) => {
    let totalMale = 0
    let totalFemale = 0
    let totalOther = 0
    let platformCount = 0
    
    if (platforms) {
      Object.values(platforms).forEach((platform: any) => {
        if (platform.audienceGender) {
          totalMale += platform.audienceGender.male || 0
          totalFemale += platform.audienceGender.female || 0
          totalOther += platform.audienceGender.other || 0
          platformCount++
        }
      })
    }
    
    if (platformCount === 0) {
      return { male: 33, female: 33, other: 34 }
    }
    
    return {
      male: Math.round(totalMale / platformCount),
      female: Math.round(totalFemale / platformCount),
      other: Math.round(totalOther / platformCount)
    }
  }
  
  // Função auxiliar para extrair preços
  const extractPrices = (platforms: any) => {
    const prices: any = {}
    
    if (platforms?.instagram?.pricing) {
      if (platforms.instagram.pricing.story) {
        prices.instagramStory = {
          name: 'Stories Instagram',
          price: platforms.instagram.pricing.story
        }
      }
      if (platforms.instagram.pricing.reel) {
        prices.instagramReel = {
          name: 'Reels Instagram',
          price: platforms.instagram.pricing.reel
        }
      }
      // Ignorar campo 'post' se existir nos dados
      if ('post' in platforms.instagram.pricing) {
      }
    } else {
    }
    
    if (platforms?.tiktok?.pricing?.video) {
      prices.tiktokVideo = {
        name: 'Vídeo TikTok',
        price: platforms.tiktok.pricing.video
      }
    } else {
    }
    
    if (platforms?.youtube?.pricing) {
      if (platforms.youtube.pricing.insertion) {
        prices.youtubeInsertion = {
          name: 'Inserção YouTube',
          price: platforms.youtube.pricing.insertion
        }
      }
      if (platforms.youtube.pricing.dedicated) {
        prices.youtubeDedicated = {
          name: 'Vídeo Dedicado YouTube',
          price: platforms.youtube.pricing.dedicated
        }
      }
      if (platforms.youtube.pricing.shorts) {
        prices.youtubeShorts = {
          name: 'YouTube Shorts',
          price: platforms.youtube.pricing.shorts
        }
      }
    } else {
    }
    
    // 🔥 CORREÇÃO: Adicionar Facebook, Twitch e Kwai
    if (platforms?.facebook?.pricing?.post) {
      prices.facebookPost = {
        name: 'Post Facebook',
        price: platforms.facebook.pricing.post
      }
    } else {
    }
    
    if (platforms?.twitch?.pricing?.stream) {
      prices.twitchStream = {
        name: 'Stream Twitch',
        price: platforms.twitch.pricing.stream
      }
    } else {
    }
    
    if (platforms?.kwai?.pricing?.video) {
      prices.kwaiVideo = {
        name: 'Vídeo Kwai',
        price: platforms.kwai.pricing.video
      }
    } else {
    }
    
    return prices
  }
  
  // Função auxiliar para extrair histórico de marcas
  const extractBrandHistory = (platforms: any) => {
    const history: any = {
      instagram: [],
      tiktok: [],
      youtube: []
    }
    
    if (platforms?.instagram?.brandHistory) {
      history.instagram = platforms.instagram.brandHistory
    }
    
    if (platforms?.tiktok?.brandHistory) {
      history.tiktok = platforms.tiktok.brandHistory
    }
    
    if (platforms?.youtube?.brandHistory) {
      history.youtube = platforms.youtube.brandHistory
    }
    
    return history
  }
  
  // Função auxiliar para extrair screenshots
  const extractScreenshots = (platforms: any) => {
    const screenshots: string[] = []
    
    if (platforms) {
      Object.values(platforms).forEach((platform: any) => {
        if (platform.screenshots && Array.isArray(platform.screenshots)) {
          screenshots.push(...platform.screenshots)
        }
      })
    }
    
    return screenshots
  }

  // 📊 Calcular progresso do formulário (baseado em campos preenchidos)
  const calculateProgress = () => {
    const values = form.watch()
    let completed = 0
    let total = 5

    // Seção pessoal - qualquer campo preenchido conta
    if (values.personalInfo?.name || values.personalInfo?.age || values.personalInfo?.gender) completed++
    
    // Seção localização - qualquer campo preenchido conta
    if (values.location?.city || values.location?.state || values.location?.cep) completed++
    
    // Seção contato - qualquer campo preenchido conta
    if (values.contact?.email || values.contact?.whatsapp) completed++
    
    // Seção plataformas - verificar se há plataformas com dados
    const platforms = values.platforms || {}
    const hasActivePlatforms = Object.values(platforms).some((platform: any) => 
      platform && platform.followers > 0
    )
    if (hasActivePlatforms) completed++
    
    // Seção categorias/negócios - qualquer campo preenchido conta
    if (values.business?.categories?.length || values.business?.agencyName || values.business?.responsibleName) completed++
    


    return Math.round((completed / total) * 100)
  }

  const progress = calculateProgress()

  // 📋 Configuração das abas
  const tabs = [
    {
      id: 'personal',
      label: 'Pessoal',
      icon: User,
      component: PersonalInfoSection,
      description: 'Informações pessoais, localização e contato'
    },
    {
      id: 'platforms',
      label: 'Redes Sociais',
      icon: Layers,
      component: SocialPlatformsSection,
      description: 'Plataformas e métricas'
    },
    {
      id: 'categories',
      label: 'Categorias',
      icon: Layers,
      component: CategoriesBusinessSection,
      description: 'Categorias e informações de negócio'
    },
    {
      id: 'brands',
      label: 'Marcas',
      icon: Building2,
      component: BrandsAssociationSection,
      description: 'Associação com marcas'
    },

    {
      id: 'metrics',
      label: 'Resumo',
      icon: Layers,
      component: MetricsPreviewSection,
      description: 'Métricas e visualização final'
    }
  ]

  const currentTab = tabs.find(tab => tab.id === activeTab)
  const isPanelOpen = open

  return (
    <>
      {/* Painel lateral estilo Nova Marca */}
      <div className={cn(
        "fixed inset-y-0 right-0 w-[45rem] bg-muted border-l border-border dark:bg-[#080210] transform transition-transform duration-300 ease-in-out z-50 shadow-xl flex flex-col",
        isPanelOpen ? "translate-x-0" : "translate-x-full"
      )}>
        <FormProvider {...form}>
          {/* Header do Painel */}
          <div className="flex items-center bg-[#5600ce] text-white justify-between p-6 border-b border-border flex-shrink-0">
          <div>
              <h2 className="text-lg font-semibold">
                {actualMode === 'create' ? 'Novo Influenciador' : 'Editar Influenciador'}
              </h2>
            
            </div>
            <Button variant="ghost" size="sm" onClick={() => onOpenChange?.(false)}>
              <X className="h-4 w-4" />
                    </Button>
                  </div>

          {/* Progress */}
          <div className="px-6 py-3 border-b border-border bg-muted/20 flex-shrink-0">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Progresso do formulário</span>
              <span className="font-medium">{progress}% completo</span>
                    </div>
            <Progress value={progress} className="h-2 mt-2" />
                  </div>

          {/* Navigation Tabs */}
          <div className="px-6 py-4 border-b border-border flex-shrink-0">
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab, index) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                const hasError = tab.id !== 'metrics' && Object.keys(errors).some(key => key.startsWith(tab.id))
                
                // Verificar se a aba tem dados obrigatórios preenchidos
                const values = form.watch()
                let hasRequiredData = false
                let showRequiredIndicator = false
                
                if (tab.id === 'personal') {
                  hasRequiredData = !!(values.personalInfo?.name && values.personalInfo.name.trim())
                  showRequiredIndicator = true // Mostrar indicador apenas para dados obrigatórios
                } else if (tab.id === 'platforms') {
                  const platforms = values.platforms || {}
                  hasRequiredData = Object.values(platforms).some((platform: any) => 
                    platform && (platform.followers > 0 || platform.username)
                  )
                  showRequiredIndicator = false // Redes sociais são opcionais agora
                }
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                      className={cn(
                      "flex items-center gap-2 px-3 py-2 rounded-md text-xs font-medium transition-colors",
                      isActive 
                        ? "bg-[#ff0074] text-white" 
                        : "dark:bg-[#181129] dark:text-[#ddcaff] bg-black/5 text-muted-foreground dark:hover:bg-[#181129]/80",
                      hasError && !isActive && "bg-red-50 text-red-600 border border-red-200"
                    )}
                  >
                    <Icon className="h-3 w-3" />
                    {tab.label}
                    {hasError ? (
                      <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                    ) : hasRequiredData ? (
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    ) : showRequiredIndicator ? (
                      <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                    ) : null}
                  </button>
                )
              })}
                      </div>
                  </div>
          
          {/* Validation Errors Summary */}
          {Object.keys(errors).length > 0 && (
            <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex-shrink-0">
                      <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm font-medium text-red-700">
                  {Object.keys(errors).length} campo(s) com erro
                      </span>
                  </div>
                </div>
              )}

          {/* Formulário - Área scrollável */}
          <div className="flex-1 min-h-0">
            <div className="h-full overflow-y-auto p-6 pb-24">
              {currentTab && (
                <currentTab.component />
              )}
                      </div>
                  </div>

          {/* Botões de Ação - Fixos no Final */}
          <div className="p-6 bg-background border-t border-border flex-shrink-0">


            <div className="flex gap-3">
                    <Button
                onClick={() => {
                  console.log('🖱️ [DEBUG] Botão "Criar Influenciador" clicado!');
                  console.log('🖱️ [DEBUG] isSubmitting:', isSubmitting);
                  console.log('🖱️ [DEBUG] form values:', form.getValues());
                  handleSubmit();
                }}
                disabled={isSubmitting}
                className="flex-1 bg-[#ff0074] text-white hover:bg-[#e6006a]"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {actualMode === 'create' ? 'Criando...' : 'Salvando...'}
                  </div>
                ) : (
                  actualMode === 'create' ? 'Criar Influenciador' : 'Salvar Alterações'
                      )}
                    </Button>
              
              <Button variant="outline" onClick={() => onOpenChange?.(false)}>
                Cancelar
                    </Button>
                  </div>
                    </div>
        </FormProvider>
                  </div>
                  
      {/* Overlay */}
      {isPanelOpen && (
        <div 
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={() => onOpenChange?.(false)}
        />
      )}
    </>
  )
}


