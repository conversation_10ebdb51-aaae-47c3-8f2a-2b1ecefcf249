'use client';

import { UserButton } from '@clerk/nextjs';

export function CustomUserButton() {
  return (
    <UserButton
      appearance={{
        elements: {
          avatarBox: "w-8 h-8",
          userButtonTrigger: "p-0 flex items-center gap-2",
          userButtonBox: "flex items-center gap-2",
          userButtonOuterIdentifier: "text-sm font-medium text-gray-900 dark:text-white",
          userButtonInnerIdentifier: "text-xs text-gray-500 dark:text-gray-400"
        }
      }}
      userProfileProps={{
        appearance: {
          variables: {
            colorPrimary: '#ff0074',
            colorText: '#270038',
            colorBackground: '#ffffff',
            borderRadius: '0.5rem',
          },
        },
        additionalOAuthScopes: {
          google: ['profile', 'email'],
        },
      }}
      afterSignOutUrl="/sign-in"
      showName={true}
      localization={{
        userButton: {
          action__manageAccount: 'Gerenciar conta',
          action__signOut: 'Sair',
          action__signOutAll: 'Sair de todas as contas',
          action__addAccount: 'Adicionar conta',
        },
        userButtonBox: {
          action__addAccount: 'Adicionar conta',
          action__manageAccount: 'Gerenciar conta',
          action__signOut: 'Sair',
          action__signOutAll: 'Sair de todas as contas',
        },
        userProfile: {
          navbar: {
            title: 'Perfil da Conta',
            description: 'Gerencie as informações da sua conta e preferências.',
            account: 'Conta',
            security: 'Segurança',
          },
          start: {
            headerTitle__account: 'Informações da Conta',
            headerTitle__security: 'Segurança e Privacidade',
            headerSubtitle__account: 'Gerencie seus dados pessoais e preferências',
            headerSubtitle__security: 'Configure suas opções de segurança',
          },
          profilePage: {
            title: 'Editar Perfil',
            imageFormTitle: 'Foto do perfil',
            imageFormSubtitle: 'Alterar',
            imageFormDestructiveActionSubtitle: 'Remover',
            fileDropAreaHint: 'Recomendado: proporção 1:1, até 10MB',
            successMessage: 'Perfil atualizado com sucesso!',
          },
          emailAddressPage: {
            title: 'Endereços de Email',
            emailCode: {
              formTitle: 'Verificar endereço de email',
              formSubtitle: 'Use o código de verificação enviado para {{identifier}}',
              formHint: 'Este código expira em alguns minutos',
              resendButton: 'Reenviar código',
            },
            verifyTitle: 'Verificar {{identifier}}',
            verifySubtitle: 'Para completar, por favor clique no link enviado para este endereço de email',
            removeResource: {
              title: 'Remover endereço de email',
              messageLine1: 'O endereço {{identifier}} será removido desta conta.',
              messageLine2: 'Você não poderá mais entrar usando este endereço de email.',
              successMessage: 'Email removido com sucesso.',
            },
          },
          phoneNumberPage: {
            title: 'Números de Telefone',
            phoneCode: {
              formTitle: 'Verificar número de telefone',
              formSubtitle: 'Use o código de verificação enviado para {{identifier}}',
              formHint: 'Este código expira em alguns minutos',
              resendButton: 'Reenviar código',
            },
            verifyTitle: 'Verificar {{identifier}}',
            verifySubtitle: 'Digite o código de verificação enviado para este número',
            removeResource: {
              title: 'Remover número de telefone',
              messageLine1: 'O número {{identifier}} será removido desta conta.',
              messageLine2: 'Você não poderá mais entrar usando este número.',
              successMessage: 'Telefone removido com sucesso.',
            },
          },
          passwordPage: {
            title: 'Alterar Senha',
            readonly: 'Você não pode editar sua senha porque fez login através de uma conexão social.',
            successMessage__set: 'Senha definida com sucesso!',
            successMessage__update: 'Senha atualizada com sucesso!',
          },
          mfaPage: {
            title: 'Autenticação de Dois Fatores',
            formHint: 'Selecione um método para adicionar',
          },
          mfaTOTPPage: {
            title: 'Adicionar aplicativo autenticador',
            verifyTitle: 'Código de verificação',
            verifySubtitle: 'Digite o código de verificação gerado pelo seu aplicativo autenticador',
            successMessage: 'Autenticação de dois fatores ativada. Seu aplicativo autenticador será necessário durante o login.',
            authenticatorApp: {
              infoText__ableToScan: 'Configure um novo método de login no seu aplicativo autenticador e escaneie o código QR a seguir para vinculá-lo à sua conta.',
              infoText__unableToScan: 'Configure um novo método de login no seu autenticador e digite a chave abaixo.',
              inputLabel__unableToScan: 'Chave secreta',
              buttonAbleToScan__nonPrimary: 'Escanear código QR',
              buttonUnableToScan__nonPrimary: 'Não consegue escanear?',
            },
          },
          mfaPhoneCodePage: {
            title: 'Adicionar verificação por SMS',
            primaryButton: 'Adicionar número de telefone',
            subtitle__availablePhoneNumbers: 'Selecione um número de telefone para registrar para autenticação de dois fatores por SMS.',
            subtitle__unavailablePhoneNumbers: 'Não há números de telefone disponíveis para registrar para autenticação de dois fatores por SMS.',
          },
          mfaBackupCodePage: {
            title: 'Adicionar códigos de backup',
            subtitle__availableCodes: 'Salve estes códigos de backup em um local seguro. Se você perder acesso ao seu dispositivo de autenticação, poderá usar códigos de backup para entrar.',
            subtitle__regenerateCodes: 'Seus códigos de backup atuais estão mostrados abaixo. Salve estes códigos de backup em um local seguro. Se você perder acesso ao seu dispositivo de autenticação, poderá usar códigos de backup para entrar.',
            infoText1: 'Códigos de backup serão habilitados para esta conta.',
            infoText2: 'Mantenha os códigos de backup em segredo e armazene-os com segurança. Você pode regenerar códigos de backup se suspeitar que foram comprometidos.',
            successSubtitle: 'Você pode usar um destes para entrar na sua conta, se perder acesso ao seu dispositivo de autenticação.',
            successMessage: 'Códigos de backup estão agora habilitados. Você pode usar um destes para entrar na sua conta, se perder acesso ao seu dispositivo de autenticação. Cada código só pode ser usado uma vez.',
            actionLabel__copy: 'Copiar todos',
            actionLabel__copied: 'Copiado!',
            actionLabel__download: 'Download .txt',
            actionLabel__print: 'Imprimir',
          },
          connectedAccountPage: {
            title: 'Contas conectadas',
            subtitle: 'Gerencie as contas conectadas à sua conta.',
            successMessage: 'Conta conectada com sucesso.',
            actionLabel__connectionFailed: 'Tentar novamente',
            actionLabel__reauthorize: 'Autorizar agora',
            removeResource: {
              title: 'Remover conta conectada',
              messageLine1: 'A conta {{identifier}} será removida desta conta.',
              messageLine2: 'Você não conseguirá mais usar esta conta conectada e quaisquer recursos dependentes não funcionarão mais.',
              successMessage: 'Conta conectada removida com sucesso.',
            },
          },
          web3WalletPage: {
            title: 'Carteiras Web3',
            subtitle: 'Gerencie suas carteiras Web3.',
            successMessage: 'Carteira adicionada com sucesso.',
            removeResource: {
              title: 'Remover carteira Web3',
              messageLine1: 'A carteira {{identifier}} será removida desta conta.',
              messageLine2: 'Você não conseguirá mais entrar usando esta carteira Web3.',
              successMessage: 'Carteira removida com sucesso.',
            },
          },
          deletePage: {
            title: 'Excluir conta',
            messageLine1: 'Tem certeza de que deseja excluir sua conta?',
            messageLine2: 'Esta ação é permanente e irreversível.',
            actionDescription: 'Digite "Excluir conta" abaixo para continuar.',
            confirm: 'Excluir conta',
          },
        },
        // Traduções globais para formulários
        formFieldLabel__emailAddress: 'Endereço de email',
        formFieldLabel__emailAddresses: 'Endereços de email',
        formFieldLabel__phoneNumber: 'Número de telefone',
        formFieldLabel__username: 'Nome de usuário',
        formFieldLabel__emailAddress_username: 'Email ou nome de usuário',
        formFieldLabel__password: 'Senha',
        formFieldLabel__currentPassword: 'Senha atual',
        formFieldLabel__newPassword: 'Nova senha',
        formFieldLabel__confirmPassword: 'Confirmar senha',
        formFieldLabel__firstName: 'Nome',
        formFieldLabel__lastName: 'Sobrenome',
        formFieldLabel__organizationName: 'Nome da organização',
        formFieldInputPlaceholder__emailAddress: 'Digite seu email',
        formFieldInputPlaceholder__phoneNumber: 'Digite seu telefone',
        formFieldInputPlaceholder__username: 'Digite seu nome de usuário',
        formFieldInputPlaceholder__emailAddress_username: 'Digite seu email ou nome de usuário',
        formFieldInputPlaceholder__password: 'Digite sua senha',
        formFieldInputPlaceholder__firstName: 'Digite seu nome',
        formFieldInputPlaceholder__lastName: 'Digite seu sobrenome',
        formFieldInputPlaceholder__organizationName: 'Digite o nome da organização',
        formButtonPrimary: 'Continuar',
        formButtonPrimary__save: 'Salvar',
        formButtonPrimary__update: 'Atualizar',
        formButtonPrimary__add: 'Adicionar',
        formButtonPrimary__remove: 'Remover',
        formButtonPrimary__verify: 'Verificar',
        formButtonPrimary__finish: 'Finalizar',
        badge__primary: 'Principal',
        badge__thisDevice: 'Este dispositivo',
        badge__unverified: 'Não verificado',
        badge__you: 'Você',
        footerActionLink__useAnotherMethod: 'Usar outro método',
        blockButton__emailCode: 'Código por email para {{identifier}}',
        blockButton__phoneCode: 'Código por SMS para {{identifier}}',
        blockButton__password: 'Entrar com senha',
        dividerText: 'ou',
        loading: 'Carregando...',
        // Mensagens de validação de senha
        formFieldSuccessText__password: 'Sua senha atende a todos os requisitos necessários.',
        formFieldHintText__password: 'Sua senha deve ter pelo menos 8 caracteres e incluir letras e números.',
        formFieldValidationText__password_meets_requirements: 'Sua senha atende a todos os requisitos necessários.',
        
        // Mensagens de erro comuns
        unstable__errors: {
          identification_deletion_failed: 'Não é possível excluir sua última identificação.',
          phone_number_exists: 'Este número de telefone já está sendo usado por outra conta.',
          email_address_exists: 'Este endereço de email já está sendo usado por outra conta.',
          captcha_invalid: 'Verificação de segurança inválida. Tente novamente.',
          form_param_format_invalid__phone_number: 'O número de telefone deve estar em um formato internacional válido.',
          form_param_format_invalid__email_address: 'Digite um endereço de email válido.',
          form_password_pwned: 'Esta senha foi encontrada em uma violação de dados e não pode ser usada.',
          form_username_invalid_length: 'O nome de usuário deve ter entre {{min_length}} e {{max_length}} caracteres.',
          form_password_length_too_short: 'A senha deve ter pelo menos {{min_length}} caracteres.',
          password_not_strong_enough: 'A senha não é forte o suficiente.',
          form_password_validation_failed: 'Senha incorreta.',
          form_identifier_not_found: 'Não foi possível encontrar uma conta com essas credenciais.',
          not_allowed_access: 'Acesso negado.',
          session_exists: 'Você já está conectado.',
          too_many_requests: 'Muitas tentativas. Tente novamente mais tarde.',
        },
      }}
    />
  );
} 

