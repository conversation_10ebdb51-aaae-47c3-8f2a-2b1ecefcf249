"use client"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { AddInfluencerForm } from "./add-influencer-form"

interface AddInfluencerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: any) => void
}

export function AddInfluencerDialog({
  open,
  onOpenChange,
  onSubmit,
}: AddInfluencerDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Adicionar Novo Influenciador</DialogTitle>
          <DialogDescription>
            Preencha os dados do influenciador para a campanha
          </DialogDescription>
        </DialogHeader>

        <AddInfluencerForm
          onSubmit={onSubmit}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  )
} 

