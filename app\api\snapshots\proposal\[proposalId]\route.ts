import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { ProposalService } from '@/services/proposal-service';
import { SnapshotFilterService } from '@/lib/snapshot-filter-service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ proposalId: string }> }
) {
  try {
    const { proposalId } = await params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!proposalId) {
      return NextResponse.json({
        success: false,
        error: 'proposalId é obrigatório'
      }, { status: 400 });
    }

    // 🔐 VERIFICAR AUTENTICAÇÃO
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const authenticatedUserId = decodedToken.uid;
    const userRole = decodedToken.role || 'user';

    console.log(`📸 [SNAPSHOTS] Usuário autenticado: ${decodedToken.email} (${userRole}) buscando snapshots para proposta: ${proposalId}`);

    // 🔐 VERIFICAR PERMISSÕES DE ACESSO À PROPOSTA
    let collaboratorRole: 'owner' | 'editor' | 'viewer' | 'none' = 'none';
    let isCollaborator = false;

    // Para admins, permitir acesso total
    if (['super_admin', 'admin', 'manager'].includes(userRole)) {
      collaboratorRole = 'owner';
      isCollaborator = true;
      console.log(`👑 [SNAPSHOTS] Usuário ${userRole} tem acesso administrativo total`);
    } else {
      // Para outros usuários, verificar se têm acesso à proposta
      const hasAccess = await ProposalService.canUserAccessProposal(proposalId, authenticatedUserId);
      
      if (!hasAccess) {
        console.log(`❌ [SNAPSHOTS] Usuário não tem acesso à proposta ${proposalId}`);
        return NextResponse.json(
          { success: false, error: 'Sem permissão para acessar esta proposta' },
          { status: 403 }
        );
      }

      // Buscar role específico como colaborador
      const proposal = await ProposalService.getProposalById(proposalId);
      if (proposal) {
        // Verificar se é proprietário
        if (proposal.criadoPor === authenticatedUserId || proposal.brandId === authenticatedUserId) {
          collaboratorRole = 'owner';
          isCollaborator = true;
          console.log(`👑 [SNAPSHOTS] Usuário é proprietário da proposta`);
        } 
        // Verificar se é colaborador
        else if ('collaborators' in proposal && Array.isArray((proposal as any).collaborators)) {
          const collaborator = (proposal as any).collaborators.find(
            (collab: any) => collab.userId === authenticatedUserId && collab.status === 'active'
          );
          if (collaborator) {
            collaboratorRole = collaborator.role;
            isCollaborator = true;
            console.log(`🤝 [SNAPSHOTS] Usuário é colaborador ${collaboratorRole}`);
          }
        }
      }
    }

    // 1. Buscar todas as propostas compartilhadas com esse proposalId
    const proposalSharingsQuery = db.collection('proposal_sharings')
      .where('proposalId', '==', proposalId);
    
    const sharingsSnapshot = await proposalSharingsQuery.get();

    if (sharingsSnapshot.empty) {
      return NextResponse.json({
        success: true,
        data: {
          proposalId,
          userId: authenticatedUserId,
          influencers: [],
          totalCount: 0,
          shares: [],
          accessMetadata: {
            userRole,
            collaboratorRole,
            isCollaborator
          }
        }
      });
    }

    console.log(`📋 [SNAPSHOTS] Encontrados ${sharingsSnapshot.size} compartilhamentos`);

    // 2. Para cada compartilhamento, buscar os snapshots de influenciadores
    const allInfluencers: any[] = [];
    const shareDetails: any[] = [];

    for (const sharingDoc of sharingsSnapshot.docs) {
      const shareToken = sharingDoc.id;
      const sharingData = sharingDoc.data();
      
      shareDetails.push({
        shareToken,
        createdAt: sharingData.createdAt?.toDate()?.toISOString(),
        createdBy: sharingData.createdBy,
        expiresAt: sharingData.expiresAt?.toDate()?.toISOString(),
        isActive: sharingData.isActive
      });

      console.log(`🔍 [SNAPSHOTS] Processando share: ${shareToken}`);

      // Buscar snapshots deste compartilhamento
      const snapshotsCollection = sharingDoc.ref.collection('snapshots');
      const snapshotsSnapshot = await snapshotsCollection.get();

      console.log(`📸 [SNAPSHOTS] Encontrados ${snapshotsSnapshot.size} snapshots no share ${shareToken}`);

      // 3. Para cada snapshot, buscar dados + subcoleções
      for (const snapshotDoc of snapshotsSnapshot.docs) {
        const influencerId = snapshotDoc.id;
        const mainData = snapshotDoc.data();

        console.log(`👤 [SNAPSHOTS] Processando influenciador: ${mainData.name} (${influencerId})`);

        // Buscar subcoleção pricing
        const pricingCollection = snapshotDoc.ref.collection('pricing');
        const pricingSnapshot = await pricingCollection.get();
        
        let currentPricing = null;
        if (!pricingSnapshot.empty) {
          const pricingDoc = pricingSnapshot.docs[0]; // Geralmente 'current'
          currentPricing = {
            id: pricingDoc.id,
            ...pricingDoc.data(),
            snapshotSource: true // Marcar como snapshot
          };
          console.log(`💰 [SNAPSHOTS] Pricing encontrado para ${mainData.name}`);
        }

        // 💰 BUSCAR SUBCOLEÇÃO BUDGETS (NOVO)
        const budgetsCollection = snapshotDoc.ref.collection('budgets');
        const budgetsSnapshot = await budgetsCollection.get();
        
        const currentBudgets: any[] = [];
        budgetsSnapshot.forEach(budgetDoc => {
          currentBudgets.push({
            id: budgetDoc.id,
            ...budgetDoc.data(),
            snapshotSource: true // Marcar como snapshot
          });
        });

        if (currentBudgets.length > 0) {
          console.log(`💰 [SNAPSHOTS] ${currentBudgets.length} budgets encontrados para ${mainData.name} (Total: R$ ${currentBudgets.reduce((sum, b) => sum + (b.amount || 0), 0)})`);
        }

        // Buscar subcoleção demographics
        const demographicsCollection = snapshotDoc.ref.collection('demographics');
        const demographicsSnapshot = await demographicsCollection.get();
        
        const currentDemographics: any[] = [];
        demographicsSnapshot.forEach(demographicDoc => {
          currentDemographics.push({
            id: demographicDoc.id,
            ...demographicDoc.data(),
            snapshotSource: true // Marcar como snapshot
          });
        });

        if (currentDemographics.length > 0) {
          console.log(`📊 [SNAPSHOTS] ${currentDemographics.length} demographics encontradas para ${mainData.name}`);
        }

        // Montar objeto do influenciador com estrutura idêntica ao live
        const influencerSnapshot = {
          // Dados básicos do snapshot
          id: influencerId,
          originalInfluencerId: mainData.originalInfluencerId || influencerId,
          ...mainData,
          
          // Subcoleções do snapshot
          currentPricing,
          currentBudgets, // 💰 NOVO: Budgets específicos da proposta
          currentDemographics,
          
          // Metadados do snapshot
          snapshotMetadata: {
            shareToken,
            capturedAt: mainData.capturedAt?.toDate()?.toISOString(),
            version: mainData.version,
            source: mainData.source,
            isSnapshot: true,
            budgetsCount: currentBudgets.length, // 💰 NOVO: Contagem de budgets
            budgetsTotal: currentBudgets.reduce((sum, b) => sum + (b.amount || 0), 0) // 💰 NOVO: Total dos budgets
          }
        };

        allInfluencers.push(influencerSnapshot);
      }
    }

    console.log(`✅ [SNAPSHOTS] Total de influenciadores processados: ${allInfluencers.length}`);
    
    // Debug: Verificar dados antes da filtragem
    allInfluencers.forEach((inf, index) => {
      console.log(`🔍 [SNAPSHOTS DEBUG] Influenciador ${index + 1}:`, {
        id: inf.id,
        name: inf.name,
        hasDemographics: !!inf.currentDemographics,
        demographicsCount: inf.currentDemographics?.length || 0,
        hasBudgets: !!inf.currentBudgets,
        budgetsCount: inf.currentBudgets?.length || 0
      });
    });

    // 🔒 APLICAR FILTROS BASEADOS NO PAPEL DO USUÁRIO
    // Criar um snapshot da proposta para filtrar
    const proposalSnapshot = {
      id: proposalId,
      nome: 'Snapshot da Proposta',
      influencersSnapshot: allInfluencers, // Array de influenciadores
      services: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const filteredSnapshot = SnapshotFilterService.filterSnapshotByRole(
      proposalSnapshot,
      userRole,
      {
        collaboratorRole,
        isCollaborator
      }
    );
    
    // Extrair apenas os influenciadores filtrados
    const filteredInfluencers = filteredSnapshot.influencersSnapshot;
    
    // Debug: Verificar dados após a filtragem
    console.log(`🔍 [SNAPSHOTS DEBUG] Após filtragem:`, {
      totalFiltered: filteredInfluencers?.length || 0,
      hasInfluencers: !!filteredInfluencers && filteredInfluencers.length > 0
    });
    
    if (filteredInfluencers && filteredInfluencers.length > 0) {
      filteredInfluencers.forEach((inf: any, index) => {
        console.log(`🔍 [SNAPSHOTS DEBUG] Influenciador filtrado ${index + 1}:`, {
          id: inf.id,
          name: inf.name,
          hasDemographics: !!inf.currentDemographics,
          demographicsCount: inf.currentDemographics?.length || 0,
          hasBudgets: !!inf.currentBudgets,
          budgetsCount: inf.currentBudgets?.length || 0
        });
      });
    }

    console.log(`🔒 [SNAPSHOTS] Filtros aplicados baseados no papel: ${userRole}/${collaboratorRole}`);

    // 4. Resultado final
    const result = {
      success: true,
      data: {
        proposalId,
        userId: authenticatedUserId,
        influencers: filteredInfluencers,
        totalCount: filteredInfluencers.length,
        shares: shareDetails,
        accessMetadata: {
          userRole,
          collaboratorRole,
          isCollaborator,
          accessLevel: collaboratorRole === 'owner' ? 'complete' : 'basic',
          filteredFields: SnapshotFilterService.getFilteredFieldsForRole(userRole)
        },
        metadata: {
          totalShares: shareDetails.length,
          hasData: filteredInfluencers.length > 0,
          dataSource: 'snapshot'
        }
      }
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ [SNAPSHOTS] Erro ao buscar snapshots:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno',
      proposalId: await params.then(p => p.proposalId)
    }, { status: 500 });
  }
} 