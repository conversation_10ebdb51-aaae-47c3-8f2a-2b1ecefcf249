import { NextResponse } from 'next/server';
import { addFinancial, getAllFinancials } from '@/lib/firebase-financials';

export async function GET() {
  try {
    const financials = await getAllFinancials();
    return NextResponse.json(financials);
  } catch (error) {
    console.error('Erro ao buscar dados financeiros:', error);
    return NextResponse.json({ error: 'Erro ao buscar dados financeiros' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    console.log('🔍 API Financials - Dados recebidos:', JSON.stringify(data, null, 2));
    
    // Validar dados mínimos
    if (!data.influencerId) {
      console.error('❌ ID do influenciador não fornecido');
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }
    
    console.log('✅ ID do influenciador válido:', data.influencerId);
    
    // Criar o registro financeiro - usar a mesma estrutura do frontend
    const financialData = {
      influencerId: data.influencerId,
      responsibleName: data.responsibleName || '',
      agencyName: data.agencyName || '',
      email: data.email || '',
      whatsapp: data.whatsapp || '',
      instagramStoriesViews: parseInt(data.instagramStoriesViews) || 0,
      
      // Preços
      prices: {
        instagramStory: {
          name: data.prices?.instagramStory?.name || "Stories",
          price: parseFloat(data.prices?.instagramStory?.price || data.prices?.instagramStory) || 0
        },
        instagramReel: {
          name: data.prices?.instagramReel?.name || "Reels",
          price: parseFloat(data.prices?.instagramReel?.price || data.prices?.instagramReel) || 0
        },
        tiktokVideo: {
          name: data.prices?.tiktokVideo?.name || "Vídeo",
          price: parseFloat(data.prices?.tiktokVideo?.price || data.prices?.tiktokVideo) || 0
        },
        youtubeInsertion: {
          name: data.prices?.youtubeInsertion?.name || "Inserção",
          price: parseFloat(data.prices?.youtubeInsertion?.price || data.prices?.youtubeInsertion) || 0
        },
        youtubeDedicated: {
          name: data.prices?.youtubeDedicated?.name || "Dedicado",
          price: parseFloat(data.prices?.youtubeDedicated?.price || data.prices?.youtubeDedicated) || 0
        },
        youtubeShorts: {
          name: data.prices?.youtubeShorts?.name || "Shorts",
          price: parseFloat(data.prices?.youtubeShorts?.price || data.prices?.youtubeShorts) || 0
        }
      },
      
      // Histórico de marcas
      brandHistory: {
        instagram: Array.isArray(data.brandHistory?.instagram) ? data.brandHistory.instagram : [],
        tiktok: Array.isArray(data.brandHistory?.tiktok) ? data.brandHistory.tiktok : [],
        youtube: Array.isArray(data.brandHistory?.youtube) ? data.brandHistory.youtube : []
      },
      
      // Dados adicionais
      additionalData: {
        contentType: Array.isArray(data.additionalData?.contentType) ? data.additionalData.contentType : 
                    (data.additionalData?.contentType ? [data.additionalData.contentType] : []),
        promotesTraders: data.additionalData?.promotesTraders === true,
        responsibleRecruiter: data.additionalData?.responsibleRecruiter || '',
        socialMediaScreenshots: Array.isArray(data.additionalData?.socialMediaScreenshots) ? 
                               data.additionalData.socialMediaScreenshots : [],
        notes: data.additionalData?.notes || '',
        documents: Array.isArray(data.additionalData?.documents) ? data.additionalData.documents : []
      }
    };
    
    console.log('💾 Tentando salvar dados financeiros no Firebase:', JSON.stringify(financialData, null, 2));
    
    const financialId = await addFinancial(financialData);
    
    console.log('✅ Dados financeiros salvos com ID:', financialId);
    
    return NextResponse.json({
      id: financialId,
      success: true,
      message: 'Dados financeiros criados com sucesso'
    });
  } catch (error) {
    console.error('Erro ao criar dados financeiros:', error);
    
    // Fornecer detalhes do erro para facilitar debug
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    return NextResponse.json(
      { 
        error: 'Erro ao criar dados financeiros', 
        details: errorMessage,
        success: false
      },
      { status: 500 }
    );
  }
}


