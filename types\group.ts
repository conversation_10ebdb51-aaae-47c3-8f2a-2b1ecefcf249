export interface Group {
  id: string;
  name: string;
  description: string;
  influencerIds: string[];
  brandId?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface CreateGroupRequest {
  name: string;
  description?: string;
  influencerIds: string[];
  brandId?: string;
  createdBy?: string;
}

export interface UpdateGroupRequest {
  groupId: string;
  name?: string;
  description?: string;
  influencerIds?: string[];
  isActive?: boolean;
} 

