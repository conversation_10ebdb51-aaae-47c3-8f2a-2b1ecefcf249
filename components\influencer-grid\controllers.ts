// Controladores para o gerenciamento de influenciadores
import { Influencer } from "./types";
import { toast } from "sonner";

// Funções para manipulação e gerenciamento de influenciadores

// Abrir diálogo de confirmação de exclusão
export const openDeleteDialog = (
  influencer: Influencer,
  setInfluencerToDelete: (influencer: Influencer | null) => void,
  setDeleteDialogOpen: (open: boolean) => void
) => {
  setInfluencerToDelete(influencer);
  setDeleteDialogOpen(true);
};

// Função para deletar um influenciador (chamada após confirmação)
export const handleDeleteInfluencer = async (
  influencer: Influencer,
  setLoading: (loading: boolean) => void,
  setInfluencers: (callback: (prevInfluencers: Influencer[]) => Influencer[]) => void,
  setDeleteDialogOpen: (open: boolean) => void,
  setInfluencerToDelete: (influencer: Influencer | null) => void) => {
try {
    const response = await fetch(`/api/influencers?id=${influencer.id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Erro ao excluir influenciador');
    }
    
    toast.success('Influenciador excluído com sucesso');
    // Atualizar a lista de influenciadores removendo o excluído
    setInfluencers(prevInfluencers => 
      prevInfluencers.filter(item => item.id !== influencer.id)
    );
  } catch (error) {
    console.error('Erro ao excluir influenciador:', error);
    toast.error('Erro ao excluir influenciador');
  } finally {
    setDeleteDialogOpen(false);
    setInfluencerToDelete(null);
  }
};

// Toggle seleção de influenciador
export const toggleInfluencerSelection = (
  id: string,
  selectedInfluencers: string[],
  setSelectedInfluencers: (callback: (prev: string[]) => string[]) => void
) => {
  if (selectedInfluencers.includes(id)) {
    setSelectedInfluencers(prev => prev.filter(item => item !== id));
  } else {
    setSelectedInfluencers(prev => [...prev, id]);
  }
};

// Abrir diálogo para exclusão em massa
export const openBulkDeleteDialog = (
  selectedInfluencers: string[],
  setBulkDeleteDialogOpen: (open: boolean) => void
) => {
  if (selectedInfluencers.length === 0) return;
  setBulkDeleteDialogOpen(true);
};

// Lidar com exclusão em massa
export const handleBulkDelete = async (
  selectedInfluencers: string[],
  setSelectedInfluencers: (influencers: string[]) => void,
  setInfluencers: (callback: (prevInfluencers: Influencer[]) => Influencer[]) => void,
  setBulkDeleteDialogOpen: (open: boolean) => void
) => {
  if (selectedInfluencers.length === 0) return;
  
  try {
    // Criar um array de promessas para excluir todos os influenciadores selecionados
    const deletePromises = selectedInfluencers.map(async (id) => {
      const response = await fetch(`/api/influencers?id=${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao excluir influenciador ${id}`);
      }
      
      return response;
    });
    
    await Promise.all(deletePromises);
    
    toast.success(`${selectedInfluencers.length} influenciador(es) excluídos com sucesso!`);
    
    // Atualizar a lista removendo os influenciadores excluídos
    setInfluencers(prevInfluencers => 
      prevInfluencers.filter(inf => !selectedInfluencers.includes(inf.id.toString()))
    );
    
    // Limpar seleção
    setSelectedInfluencers([]);
  } catch (error) {
    console.error('Erro ao excluir influenciadores em massa:', error);
    toast.error('Erro ao excluir alguns influenciadores. Tente novamente.');
  } finally {
    setBulkDeleteDialogOpen(false);
  }
};

// Lidar com duplicação em massa
export const handleBulkDuplicate = async (
  selectedInfluencers: string[],
  influencers: Influencer[],
  setSelectedInfluencers: (influencers: string[]) => void,
  setInfluencers: (callback: (prevInfluencers: Influencer[]) => Influencer[]) => void,
  gradients: string[]
) => {
  if (selectedInfluencers.length === 0) return;
  
  try {
    // Buscar dados completos dos influenciadores selecionados
    const selectedInfluencersFull = influencers.filter(
      inf => selectedInfluencers.includes(inf.id.toString())
    );
    
    // Criar promessas para duplicar cada influenciador
    const duplicatePromises = selectedInfluencersFull.map(async (influencer) => {
      // Extrair cidade e estado
      let city = "";
      let state = "";
      
      if (influencer.location) {
        const locationParts = influencer.location.split(' - ');
        if (locationParts.length >= 2) {
          city = locationParts[0];
          state = locationParts[1];
        }
      }
      
      // Preparar dados
      const duplicateData = {
        ...influencer,
        name: `${influencer.name} (Cópia)`,
        id: undefined,
        country: (influencer as any).country || "Brasil",
        // Usamos any para acessar propriedades que podem existir nos dados mas não no tipo
        city: city || (influencer as any).city || "",
        state: state || (influencer as any).state || "",
      };
      
      // Enviar requisição
      const response = await fetch('/api/influencers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(duplicateData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Erro ao duplicar influenciador: ${errorData.error || ''}`);
      }
      
      // Retornar o influenciador criado
      const newInfluencer = await response.json();
      return newInfluencer;
    });
    
    const newInfluencers = await Promise.all(duplicatePromises);
    
    // Processar os novos influenciadores para garantir formato correto
    const processedInfluencers = newInfluencers.map(newInfluencer => ({
      ...newInfluencer,
      gradient: newInfluencer.gradient || gradients[Math.floor(Math.random() * gradients.length)],
      rating: parseFloat(newInfluencer.rating || '4.0'),
      mainCategories: Array.isArray(newInfluencer.mainCategories) ? newInfluencer.mainCategories : [],
      instagram: newInfluencer.instagram?.toString() || '0',
      youtube: newInfluencer.youtube?.toString() || '0',
      tiktok: newInfluencer.tiktok?.toString() || '0',
      whatsapp: newInfluencer.whatsapp?.toString() || '',
    }));
    
    // Adicionar os novos influenciadores ao estado
    setInfluencers(prevInfluencers => [...prevInfluencers, ...processedInfluencers]);
    
    toast.success(`${selectedInfluencers.length} influenciador(es) duplicados com sucesso!`);
    
    // Limpar seleção
    setSelectedInfluencers([]);
  } catch (error) {
    console.error('Erro ao duplicar influenciadores em massa:', error);
    toast.error('Erro ao duplicar alguns influenciadores. Tente novamente.');
  }
};


