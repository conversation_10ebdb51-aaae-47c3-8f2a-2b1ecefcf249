"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, DialogContent } from "@/components/ui/dialog"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
import { toast } from "sonner"
import axios from "axios"

// ===== GRAPHQL IMPORTS =====
import { ApolloProvider, useApolloClient } from '@apollo/client';
import { useAuth } from "@/hooks/use-auth-v2";
import useInfluencersGraphQL from "@/hooks/use-influencers-graphql";
import { useGlobalLoader } from "@/components/ui/loader";
import { useQuery, gql } from '@apollo/client';
import { useUser } from '@clerk/nextjs';

// ===== QUERY GRAPHQL PARA PROPOSTAS =====
const GET_PROPOSAL_INFLUENCERS = gql`
  query GetProposalInfluencers($influencerIds: [ID!]!, $userId: ID!, $proposalId: ID) {
    influencersByIds(ids: $influencerIds, userId: $userId, proposalId: $proposalId) {
      influencers {
        id
        name
        avatar
        totalFollowers
        engagementRate
        rating
        isVerified
        isAvailable
        status
        category
        country
        state
        city
        age
        gender
        email
        phone
        whatsapp
        
        # Redes sociais
        instagramUsername
        instagramFollowers
        instagramEngagementRate
        instagramAvgViews
        
        tiktokUsername
        tiktokFollowers
        tiktokEngagementRate
        tiktokAvgViews
        
        youtubeUsername
        youtubeFollowers
        youtubeEngagementRate
        youtubeAvgViews
        
        facebookUsername
        facebookFollowers
        facebookEngagementRate
        facebookAvgViews
        
        twitchUsername
        twitchFollowers
        twitchEngagementRate
        twitchAvgViews
        
        kwaiUsername
        kwaiFollowers
        kwaiEngagementRate
        kwaiAvgViews
        
        promotesTraders
        responsibleName
        agencyName
        
        # Rede social principal
        mainNetwork
        
        # Pricing atual
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }
        
        createdAt
        updatedAt
      }
      foundIds
      notFoundIds
      totalFound
      totalRequested
      processingTimeMs
      hasPartialFailure
      errors
    }
  }
`;


// Importar componentes refatorados
// Modal removido conforme solicitado
import { AnimatedStats } from "./animated-stats"
import { GridView } from "./grid-view"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { InfluencerAvatar } from "@/components/ui/influencer-avatar"
import { Badge } from "@/components/ui/badge"
import { SocialIcon } from "@/components/ui/social-icons"
import { Square, CheckSquare } from "lucide-react"
import { Influencer, Brand, globalBrands, gradients } from "./types"
import { getBrandById, getInfluencerBrands } from "./utils"
import { openDeleteDialog, handleDeleteInfluencer, toggleInfluencerSelection, openBulkDeleteDialog, handleBulkDelete, handleBulkDuplicate } from "./controllers"

// Importar outros componentes da aplicação
import { AddInfluencerForm } from "@/components/add-influencer-form"
import useInfiniteScroll from "@/hooks/use-infinite-scroll"

// Importar CSS para esconder a barra de rolagem mas manter a funcionalidade
import "../scrollbar-hide.css"

// Interface para a categoria
interface Category {
  id: string;
  name: string;
  slug: string;
  color?: string;
}

// Função para formatar números em K e MI
const formatarNumero = (numero: string | number): string => {
  const num = typeof numero === 'string' ? parseInt(numero.replace(/[^0-9]/g, '')) : numero;
  
  if (isNaN(num)) return '0';
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1).replace('.0', '') + 'MI';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1).replace('.0', '') + 'K';
  } else {
    return num.toString();
  }
};

// Função para formatar localização
const formatLocationDisplay = (influencer: Influencer) => {
  if (influencer.location && influencer.location.trim() !== "") {
    return influencer.location;
  }
  
  // Type-safe check for GraphQL fields that might not exist in the base interface
  const infAny = influencer as any;
  const validCity = infAny.city && infAny.city !== "Não informado" && infAny.city.trim() !== "" ? infAny.city : null;
  const validState = infAny.state && infAny.state !== "Não informado" && infAny.state.trim() !== "" ? infAny.state : null;
  const validCountry = infAny.country && infAny.country !== "Não informado" && infAny.country.trim() !== "" ? infAny.country : null;
  
  if (validCity && validState) {
    return `${validCity}, ${validState}`;
  } else if (validCity && validCountry) {
    return `${validCity}, ${validCountry}`;
  } else if (validState && validCountry) {
    return `${validState}, ${validCountry}`;
  } else if (validCountry) {
    return validCountry;
  } else if (validCity) {
    return validCity;
  } else if (validState) {
    return validState;
  }
  return "Localização não informada";
};

interface InfluencerGridProps {
  onRefresh?: number;
  searchTerm?: string;
  selectedLocation?: string;
  minFollowers?: number;
  maxFollowers?: number;
  minRating?: number;
  verifiedOnly?: boolean;
  availableOnly?: boolean;
  onSelectInfluencer?: (influencer: Influencer) => void;
  selectedBrands?: string[];
  selectedProposal?: any;
  selectedInfluencerId?: string | null;
  selectedInfluencer?: Influencer | null;
}

export default function InfluencerGrid({ 
  onRefresh,
  searchTerm: externalSearchTerm,
  selectedLocation: externalLocation,
  minFollowers: externalMinFollowers,
  maxFollowers: externalMaxFollowers,
  minRating: externalMinRating,
  verifiedOnly: externalVerifiedOnly,
  availableOnly: externalAvailableOnly,
  onSelectInfluencer,
  selectedBrands: externalSelectedBrands = [],
  selectedProposal,
  selectedInfluencerId,
  selectedInfluencer: externalSelectedInfluencer
}: InfluencerGridProps) {
  // ===== AUTENTICAÇÃO =====
  const { currentUser, getToken } = useAuth();
  const { showLoader, hideLoader, updateMessage } = useGlobalLoader();
  
  // Estado para as marcas
  const [brands, setBrands] = useState<Brand[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>(externalSelectedBrands);
  
  // Estado para categorias (necessário para o DataTable)
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState<boolean>(true);
  
  // Estado para o modo de visualização (grade ou lista)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  
  // Estados para controle dos modais e diálogos
  const { toast: uiToast } = useToast();
  const [addInfluencerModalOpen, setAddInfluencerModalOpen] = useState(false);
  const [editInfluencerModalOpen, setEditInfluencerModalOpen] = useState(false);
  const [selectedInfluencers, setSelectedInfluencers] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);
  // Estado do modal removido
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [influencerToDelete, setInfluencerToDelete] = useState<Influencer | null>(null);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  // Estado do influenciador de detalhe removido
  
  // Estados para filtros locais
  const [searchTerm, setSearchQuery] = useState(externalSearchTerm || '');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState(externalLocation || '');
  const [minFollowers, setMinFollowers] = useState(externalMinFollowers || 0);
  const [maxFollowers, setMaxFollowers] = useState(externalMaxFollowers || 0);
  const [minRating, setMinRating] = useState(externalMinRating || 0);
  const [verifiedOnly, setVerifiedOnly] = useState(externalVerifiedOnly || false);
  const [availableOnly, setAvailableOnly] = useState(externalAvailableOnly || false);
  
  // Estados para controle de proposta selecionada
  const [proposalInfluencerIds, setProposalInfluencerIds] = useState<string[]>([]);
  const [snapshotInfluencers, setSnapshotInfluencers] = useState<any[]>([]);
  const [usingSnapshots, setUsingSnapshots] = useState(false); // 📸 APENAS para snapshots históricos reais
  const [usingPreFilteredData, setUsingPreFilteredData] = useState(false); // 🎯 Para dados pré-filtrados (cache ou snapshots)
  const [isLoadingSnapshots, setIsLoadingSnapshots] = useState(false);
  
  // ===== GRAPHQL HOOK =====
  // 🔥 IMPLEMENTAÇÃO CORRETA: Usar o userId do usuário autenticado
  const userId = currentUser?.id || 'system'; // Fallback para 'system' se não autenticado
 
  const {
    influencers,
    totalCount,
    loading,
    error: graphqlError,
    createInfluencer,
    updateInfluencer,
    deleteInfluencer,
    refreshData,
    loadMore
  } = useInfluencersGraphQL({
    userId,
    autoFetch: !selectedProposal, // 🔥 NOVA LÓGICA: Só fazer autoFetch quando NÃO há proposta selecionada
    // 🔥 CORREÇÃO: Usar filtros vazios para permitir cache completo
    filters: {},
    pagination: {
      limit: 50, // Aumentar limite para ter mais dados em cache
      offset: 0
    },
    infiniteScroll: true // Habilitar scroll infinito
  });

  // 🔥 FALLBACKS para propriedades que podem não existir no hook
  const loadingMore = false; // Simplificado  
  const hasNextPage = totalCount > influencers.length; // Calculado baseado no total vs carregados
  const selectedInfluencer = externalSelectedInfluencer; // Usar props externa
  const setSelectedInfluencer = (influencer: Influencer | null) => {
    // Usar callback se fornecido
    if (onSelectInfluencer && influencer) {
      onSelectInfluencer(influencer);
    }
  };
  const fetchInfluencerById = async (id: string) => {
    // Buscar no cache primeiro
    return influencers.find((inf: any) => inf.id === id) || null;
  };

  // Conversão de erro GraphQL para string
  const error = graphqlError?.message || null;
  
  // ===== BUSCAR CATEGORIAS =====
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Tenta obter categorias do cache
        const cachedCategories = localStorage.getItem('categories');
        if (cachedCategories) {
          setCategories(JSON.parse(cachedCategories));
          setIsLoadingCategories(false);
          return;
        }

        // Se não houver cache, busca da API
        const response = await axios.get('/api/categories');
        setCategories(response.data);
        
        // Salva no cache
        localStorage.setItem('categories', JSON.stringify(response.data));
      } catch (error) {
        console.error('Erro ao buscar categorias:', error);
      } finally {
        setIsLoadingCategories(false);
      }
    };
    
    fetchCategories();
  }, []);

  // Função para obter o nome da categoria a partir do ID
  const getCategoryName = (categoryId: string) => {
    if (!categoryId) return 'Sem categoria';
    
    // Se as categorias ainda estão carregando, mostra um placeholder
    if (isLoadingCategories) {
      return 'Carregando...';
    }
    
    // Buscar a categoria no array de categorias
    const category = categories.find(cat => cat.id === categoryId);
    
    if (category) {
      return category.name; // Retorna o nome real da categoria
    }
    
    // Se não encontrou a categoria, retorna o ID formatado como fallback
    return categoryId.charAt(0).toUpperCase() + categoryId.slice(1).toLowerCase();
  };
  
  // Função para obter a cor da categoria
  const getCategoryColor = (categoryId: string) => {
    if (!categoryId) return '#cccccc';
    
    // Buscar a categoria no array de categorias
    const category = categories.find(cat => cat.id === categoryId);
    
    if (category && category.color) {
      return category.color; // Retorna a cor definida da categoria
    }
    
    return "#9810fa"; // Cor padrão
  };

  // ===== COLUNAS DO DATA TABLE =====
  const columns: ColumnDef<Influencer>[] = [
    {
      id: "avatar",
      header: "",
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="relative">
            <InfluencerAvatar
              influencerId={influencer.id.toString()}
              influencerName={influencer.name}
              avatarPath={influencer.avatar}
              className="h-10 w-10"
            />
            {influencer.verified && (
              <div className="absolute -bottom-0.5 -right-[0px] bg-transparent text-white rounded-full h-4 w-4 flex items-center justify-center">
                <svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.61 189.98" width="20" height="20">
                  <defs>
                    <linearGradient id="Gradiente_sem_nome_147" data-name="Gradiente sem nome 147" x1="52.12" y1="184.26" x2="115.13" y2="9.61" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stopColor="#ff0074"/>
                      <stop offset="1" stopColor="#ff0074"/>
                    </linearGradient>
                  </defs>
                  <path d="M78.31,0c20.65,0,40.14,8.05,54.79,22.67,14.65,14.62,22.73,34.07,22.73,54.72v55.27c0,20.65-8.08,40.1-22.73,54.72C118.45,201.93,98.96,210,78.31,210s-40.14-8.07-54.79-22.62C8.87,172.76,.79,153.31,.79,132.66V77.39c0-20.65,8.08-40.1,22.73-54.72C38.17,8.05,57.66,0,78.31,0Z" transform="translate(-0.79)" fill="url(#Gradiente_sem_nome_147)"/>
                  <path d="M69.25,132.66l-22.42-22.42c-2.07-2.07-2.07-5.43,0-7.5l7.5-7.5c2.07-2.07,5.43-2.07,7.5,0l11.17,11.17L104.28,75.14c2.07-2.07,5.43-2.07,7.5,0l7.5,7.5c2.07,2.07,2.07,5.43,0,7.5L76.75,132.66C74.68,134.73,71.32,134.73,69.25,132.66Z" transform="translate(-0.79)" fill="#fff"/>
                </svg>
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
      size: 50,
    },
    {
      id: "name",
      accessorKey: "name",
      header: "NOME",
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">{influencer.name}</span>
          </div>
        );
      },
    },
    {
      id: "category",
      accessorKey: "category",
      header: "CATEGORIA",
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <Badge 
            variant="secondary" 
            style={{ backgroundColor: getCategoryColor(influencer.category) + '20', color: getCategoryColor(influencer.category) }}
          >
            {getCategoryName(influencer.category)}
          </Badge>
        );
      },
    },
    {
      id: "location",
      header: "LOCALIZAÇÃO",
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm text-muted-foreground">
            {formatLocationDisplay(influencer)}
          </span>
        );
      },
    },
    {
      id: "instagram",
      header: "IG",
      cell: ({ row }) => {
        const influencer = row.original;
        const followers = influencer.socialNetworks?.instagram?.followers || 0;
        return (
          <div className="flex items-center gap-1">
            <SocialIcon platform="instagram" size={16} className="text-black dark:text-white" />
            <span className="text-sm">{formatarNumero(followers)}</span>
          </div>
        );
      },
    },
    {
      id: "youtube",
      header: "YT",
      cell: ({ row }) => {
        const influencer = row.original;
        const followers = influencer.socialNetworks?.youtube?.followers || 0;
        return (
          <div className="flex items-center gap-1">
            <SocialIcon platform="youtube" size={16} className="text-black dark:text-white" />
            <span className="text-sm">{formatarNumero(followers)}</span>
          </div>
        );
      },
    },
    {
      id: "tiktok",
      header: "TT",
      cell: ({ row }) => {
        const influencer = row.original;
        const followers = influencer.socialNetworks?.tiktok?.followers || 0;
        return (
          <div className="flex items-center gap-1">
            <SocialIcon platform="tiktok" size={16} className="text-black dark:text-white" />
            <span className="text-sm">{formatarNumero(followers)}</span>
          </div>
        );
      },
    },
  
  ];
  
  // ===== SCROLL INFINITO =====
  const { loadingRef } = useInfiniteScroll({
    hasMore: hasNextPage,
    isLoading: loadingMore || loading,
    onLoadMore: loadMore,
    threshold: 200 // Carregar quando estiver a 200px do final
  });
  
 

  
  // ===== FUNÇÃO DE PROCESSAMENTO GRAPHQL =====
  // Processa dados do GraphQL para garantir compatibilidade com o formato antigo
  
  // Função utilitária para formatar localização filtrando valores inválidos
  const formatLocationDisplayGraphQL = (city: string, state: string, country: string) => {
    const validCity = city && city !== "Não informado" && city.trim() !== "" ? city : null;
    const validState = state && state !== "Não informado" && state.trim() !== "" ? state : null;
    const validCountry = country && country !== "Não informado" && country.trim() !== "" ? country : null;
    
    if (validCity && validState) {
      return `${validCity}, ${validState}`;
    } else if (validCity && validCountry) {
      return `${validCity}, ${validCountry}`;
    } else if (validState && validCountry) {
      return `${validState}, ${validCountry}`;
    } else if (validCountry) {
      return validCountry;
    } else if (validCity) {
      return validCity;
    } else if (validState) {
      return validState;
    }
    return "";
  };
  
  const processGraphQLInfluencer = (inf: any) => ({
        ...inf,
    // Garantir que alguns campos essenciais existam para compatibilidade
        gradient: inf.gradient || gradients[Math.floor(Math.random() * gradients.length)],
        rating: parseFloat(inf.rating || '4.0'),
    mainCategories: Array.isArray(inf.categories) ? inf.categories : [],
    // Formatar campos numéricos como string para compatibilidade
    instagram: inf.totalFollowers?.toString() || '0',
    youtube: '0', // Placeholder
    tiktok: '0', // Placeholder
        whatsapp: inf.whatsapp?.toString() || '',
    // Mapear campos do GraphQL para o formato esperado
    verified: inf.isVerified,
    location: formatLocationDisplayGraphQL(inf.city, inf.state, inf.country) || inf.location || '',
    totalFollowers: inf.totalFollowers?.toString() || '0'
  });
  
  // 🚀 FASE 6: Removido fetchBrands - agora usa hook useBrandsList()
  // As marcas são obtidas diretamente via hook no BrandFilter
  
  // Função para selecionar um influenciador
  const handleOpenInfluencerDetail = (influencer: Influencer) => {
    console.log('🎯 [GRID] Clique no influenciador:', influencer.name, 'ID:', influencer.id);
    console.log('🎯 [GRID] Influencer atualmente selecionado:', selectedInfluencer?.name, 'ID:', selectedInfluencer?.id);
    
    // 🔥 MARCAR que houve seleção manual para evitar conflito com seleção automática
    setHasInitialSelection(true);
    
    // 🔥 SEMPRE chamar a função externa, mesmo se for o mesmo influenciador, para garantir que a URL seja atualizada
    if (onSelectInfluencer) {
      console.log('📞 [GRID] Chamando função externa onSelectInfluencer (seleção manual)');
      onSelectInfluencer(influencer);
    }
    
    // Atualiza o estado local para destacar o card
    setSelectedInfluencer(influencer);
  };
  
  // Função para abrir o modal de edição (GraphQL)
  const handleEditInfluencer = async (influencer: Influencer) => {
      try {
      console.log(`🔄 [GraphQL] Carregando dados do influenciador para edição:`, influencer.id);
      
      // Usar GraphQL para buscar dados completos
      const fullInfluencerData = await fetchInfluencerById(String(influencer.id));
        
      if (fullInfluencerData) {
        console.log('✅ [GraphQL] Dados do influenciador carregados para edição');
      setSelectedInfluencer(fullInfluencerData);
      setEditInfluencerModalOpen(true);
      } else {
        throw new Error('Influenciador não encontrado');
      }
      
    } catch (error) {
      console.error('❌ [GraphQL] Erro ao carregar influenciador para edição:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        toast.error(`Erro ao carregar dados do influenciador: ${errorMessage}`);
    }
  };
  
  // Função para confirmar exclusão de um influenciador
  const handleDeleteConfirm = (influencer: Influencer) => {
    setInfluencerToDelete(influencer);
    setDeleteDialogOpen(true);
  };

  // Função para enviar influenciadores selecionados para uma marca
  const handleSendToBrand = async (brandId: string, brandName: string) => {
    if (selectedInfluencers.length === 0) {
      toast.error('Nenhum influenciador selecionado para enviar.');
      return;
    }

    try {
      console.log('🚀 Enviando influenciadores para a marca:', {
        brandId,
        brandName,
        influencerIds: selectedInfluencers,
        count: selectedInfluencers.length
      });

      // Obter token de autenticação do Firebase
      const { auth } = await import('@/lib/firebase-client');
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        toast.error('Usuário não autenticado. Faça login novamente.');
        return;
      }

      const token = await currentUser.getIdToken();
      console.log('🔑 Token obtido para requisição');

      // Enviar apenas os IDs dos influenciadores para a API
      const response = await fetch('/api/campanhas/influencers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // ✅ Incluir token de autenticação
        },
        body: JSON.stringify({
          brandId,
          brandName,
          influencerIds: selectedInfluencers, // Enviar apenas os IDs
          createdBy: currentUser.uid // ✅ Usar ID do usuário autenticado
        }),
      });

      if (!response.ok) {
        let errorMessage = `Erro HTTP ${response.status}: ${response.statusText}`;
        let errorData = null;
        
        try {
          const responseText = await response.text();
          if (responseText) {
            try {
              errorData = JSON.parse(responseText);
              errorMessage = errorData.error || errorData.message || errorMessage;
            } catch (jsonError) {
              // Se não conseguir fazer parse do JSON, use o texto da resposta
              errorMessage = responseText || errorMessage;
            }
          }
        } catch (readError) {
          console.error('Erro ao ler resposta de erro:', readError);
        }
        
        console.error('Erro da API - Status:', response.status, 'Data:', errorData || 'Sem dados');
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      // Limpar seleção após envio bem-sucedido
      setSelectedInfluencers([]);
      setSelectionMode(false);
      
      // Mostrar feedback de sucesso
      toast.success(result.message || `${selectedInfluencers.length} influenciador(es) enviado(s) com sucesso para ${brandName}!`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao enviar influenciadores para a marca.';
      console.error('Erro ao enviar influenciadores para marca:', error);
      toast.error(errorMessage);
    }
  };

  // Função para duplicar um influenciador (GraphQL)
  const handleDuplicateInfluencer = async (influencerData: any) => {
    try {
      console.log('🔄 [GraphQL] Duplicando influenciador:', influencerData.name);
      
      // Preparar dados para duplicação
      const duplicateData = {
        name: `${influencerData.name} (Cópia)`,
        email: influencerData.email,
        country: influencerData.country || "Brasil",
        state: influencerData.state || "",
        city: influencerData.city || "",
        gender: influencerData.gender || "Não informado",
        category: influencerData.category || "Lifestyle",
        categories: influencerData.categories || [influencerData.category || "Lifestyle"],
      };
      
      // Usar GraphQL para criar o influenciador duplicado
      await createInfluencer(duplicateData);
      
      console.log('✅ [GraphQL] Influenciador duplicado com sucesso');
    } catch (error) {
      console.error('❌ [GraphQL] Erro ao duplicar influenciador:', error);
      toast.error('Erro ao duplicar influenciador. Tente novamente.');
    }
  };
  
  // 🔍 INVESTIGAÇÃO: Monitorar mudanças nos influenciadores
  useEffect(() => {
    console.log('🔄 [InfluencerGrid] Influenciadores mudaram:', {
      count: influencers.length,
      loading,
      error,
      userId,
      names: influencers.map((inf: any) => inf.name)
    });
  }, [influencers, loading, error, userId]);

  // Controlar loader global baseado no estado de loading dos influencers + snapshots
  useEffect(() => {
    if (loading || isLoadingSnapshots) {
      if (isLoadingSnapshots) {
        updateMessage('Carregando dados históricos...');
      }
      showLoader();
    } else {
      hideLoader();
    }
  }, [loading, isLoadingSnapshots, showLoader, hideLoader, updateMessage]);

  // 🔥 CORREÇÃO: Efeito para refresh usando GraphQL
  useEffect(() => {
    // Só executa quando onRefresh for definido
    if (onRefresh !== undefined && onRefresh > 0) {
      console.log('🔄 [GraphQL] Refresh solicitado:', onRefresh);
      refreshData();
    }
  }, [onRefresh, refreshData]);
  
  // Estado para controlar se já foi feita a seleção inicial
  const [hasInitialSelection, setHasInitialSelection] = useState(false);
  
  // 🔥 CORREÇÃO: Seleção automática simplificada - só executa uma vez
  useEffect(() => {
    // Só seleciona automaticamente se:
    // 1. Tem influencers carregados
    // 2. Não está carregando
    // 3. Tem função de seleção
    // 4. Ainda não fez seleção inicial
    // 5. Não tem influencer selecionado via URL
    if (influencers.length > 0 && !loading && onSelectInfluencer && !hasInitialSelection && !selectedInfluencerId) {
      console.log('👤 [AUTO_CHECK] Selecionando primeiro influencer automaticamente...');
      
      // Usar timeout para garantir que a seleção aconteça após o render
      setTimeout(() => {
        if (!selectedInfluencerId && !hasInitialSelection) { // Verificar novamente para evitar race conditions
          onSelectInfluencer(influencers[0]);
          setHasInitialSelection(true);
          console.log('👤 [AUTO] Primeiro card selecionado automaticamente:', influencers[0].name);
        }
      }, 100);
    } else {
      console.log('👤 [AUTO_SKIP] Seleção automática pulada:', {
        hasInfluencers: influencers.length > 0,
        loading,
        hasFunction: !!onSelectInfluencer,
        hasInitialSelection,
        selectedInfluencerId
      });
    }
  }, [influencers.length, loading]); // 🔥 SIMPLIFICADO - só reage a mudanças essenciais
  
  // Reset da seleção inicial quando há refresh
  useEffect(() => {
    if (onRefresh !== undefined && onRefresh > 0) {
      setHasInitialSelection(false);
    }
  }, [onRefresh]);

  // 🔥 NOVO: Seleção automática do influenciador baseado na URL
  useEffect(() => {
    console.log('🔍 [URL_EFFECT] Verificando seleção baseada na URL:', {
      selectedInfluencerId,
      influencersCount: influencers.length,
      loading,
      currentSelectedId: selectedInfluencer?.id
    });
    
    if (selectedInfluencerId && influencers.length > 0 && !loading && onSelectInfluencer) {
      const foundInfluencer = influencers.find((inf: any) => inf.id === selectedInfluencerId);
      
      console.log('🔍 [URL_EFFECT] Influenciador encontrado na lista:', !!foundInfluencer);
      
      if (foundInfluencer && selectedInfluencer?.id !== foundInfluencer.id) {
        console.log('👤 [URL] Selecionando influenciador da URL:', foundInfluencer.name, 'ID:', foundInfluencer.id);
        setSelectedInfluencer(foundInfluencer);
        onSelectInfluencer(foundInfluencer);
        setHasInitialSelection(true); // Prevenir seleção automática do primeiro
      }
    }
  }, [selectedInfluencerId, influencers, loading]); // 🔥 REMOVIDO onSelectInfluencer e selectedInfluencer para evitar loops

  // 🔥 NOVO: Função para carregar snapshots de uma proposta
  const loadProposalSnapshots = async (proposalId: string) => {
    if (!currentUser) return;
    
    setIsLoadingSnapshots(true);
    setUsingSnapshots(false);
    setUsingPreFilteredData(false);
    
    try {
      console.log('📸 [SNAPSHOTS] Carregando snapshots da proposta:', proposalId);
      
      if (!currentUser?.id) {
        console.warn('⚠️ [SNAPSHOTS] Usuário não autenticado, pulando snapshots');
        // Não é erro fatal, apenas pular snapshots e usar dados atuais via GraphQL
        console.log('ℹ️ [SNAPSHOTS] Dados atuais serão carregados via GraphQL automaticamente');
        return;
      }

      // Obter token do Clerk Auth se disponível
      let token = '';
      try {
        token = await getToken() || '';
        console.log('🔐 [SNAPSHOTS] Token obtido:', token ? '✅ Sucesso' : '❌ Vazio');
      } catch (tokenError) {
        console.warn('⚠️ [SNAPSHOTS] Erro ao obter token:', tokenError);
        // Continuar sem token - algumas APIs podem não precisar
      }
      
      const response = await fetch(`/api/snapshots/proposal/${proposalId}?userId=${currentUser.id}`, {
        headers: {
          ...(token && { 'Authorization': `Bearer ${token}` }),
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`⚠️ [SNAPSHOTS] Erro HTTP ${response.status}, usando dados atuais`);
        // Se a API não suporta snapshots ou retorna erro, usar dados atuais via GraphQL
        setUsingSnapshots(false);
        setUsingPreFilteredData(false);
        console.log('ℹ️ [SNAPSHOTS] Dados atuais serão carregados via GraphQL automaticamente');
        return;
      }

      const result = await response.json();
      
      if (result.success && result.data.totalCount > 0) {
        console.log('✅ [SNAPSHOTS] Snapshots carregados:', result.data.totalCount);
        
        // Processar snapshots para o formato compatível com o grid
        const processedSnapshots = result.data.influencers.map((snapshot: any) => ({
          ...snapshot,
          // Manter compatibilidade com estrutura atual
          id: snapshot.originalInfluencerId || snapshot.id,
          // Adicionar flag para indicar que é snapshot
          isSnapshot: true,
          snapshotCapturedAt: snapshot.capturedAt,
          snapshotVersion: snapshot.version,
          // Processar dados das redes sociais para compatibilidade
          instagram: snapshot.socialNetworks?.instagram?.followers?.toString() || '0',
          totalFollowers: snapshot.totalFollowers?.toString() || '0',
          location: snapshot.location || '',
          verified: snapshot.isVerified || false,
          gradient: snapshot.gradient || gradients[Math.floor(Math.random() * gradients.length)]
        }));
        
        setSnapshotInfluencers(processedSnapshots);
        setUsingSnapshots(true); // ✅ AGORA SIM são snapshots históricos reais
        setUsingPreFilteredData(true); // ✅ E também são dados pré-filtrados
        setProposalInfluencerIds(processedSnapshots.map((s: any) => s.id));
        
        console.log('📸 [SNAPSHOTS] Dados processados e aplicados');
        
      } else {
        console.log('📊 [LIVE DATA] Proposta sem snapshots, usando dados atuais');
        setSnapshotInfluencers([]);
        setUsingSnapshots(false);
        setUsingPreFilteredData(false);
        // Continuar com carregamento normal de influencers da proposta via GraphQL
        console.log('ℹ️ [SNAPSHOTS] Dados atuais serão carregados via GraphQL automaticamente');
      }
      
    } catch (error) {
      console.warn('⚠️ [SNAPSHOTS] Erro ao carregar snapshots, usando dados atuais:', error);
      setSnapshotInfluencers([]);
      setUsingSnapshots(false);
      setUsingPreFilteredData(false);
      // Fallback para dados atuais via GraphQL
      console.log('ℹ️ [SNAPSHOTS] Fallback: dados atuais serão carregados via GraphQL automaticamente');
    } finally {
      setIsLoadingSnapshots(false);
    }
  };

  // 🔥 NOVA IMPLEMENTAÇÃO: Hook GraphQL para buscar influencers da proposta
  const {
    data: proposalInfluencersData,
    loading: loadingProposalInfluencers,
    error: proposalInfluencersError,
    refetch: refetchProposalInfluencers
  } = useQuery(GET_PROPOSAL_INFLUENCERS, {
    variables: { 
      influencerIds: proposalInfluencerIds,
      userId: userId,
      proposalId: selectedProposal?.id // 🔑 CHAVE: Passar proposalId para permitir acesso de membros convidados
    },
    skip: !selectedProposal || proposalInfluencerIds.length === 0 || !userId,
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache
    errorPolicy: 'all',
    onCompleted: (data) => {
      if (data?.influencersByIds?.influencers) {
        console.log('🎯 [GRAPHQL PROPOSAL] Influencers carregados via GraphQL:', {
          proposalId: selectedProposal?.id,
          requested: proposalInfluencerIds.length,
          received: data.influencersByIds.influencers.length,
          totalFound: data.influencersByIds.totalFound,
          totalRequested: data.influencersByIds.totalRequested,
          processingTime: data.influencersByIds.processingTimeMs + 'ms',
          fromCache: 'Apollo cache-first'
        });
        setSnapshotInfluencers(data.influencersByIds.influencers);
        setUsingPreFilteredData(true);
        
        // Log metadata adicional
        if (data.influencersByIds.hasPartialFailure) {
          console.warn('⚠️ [GRAPHQL PROPOSAL] Falha parcial:', {
            notFoundIds: data.influencersByIds.notFoundIds,
            errors: data.influencersByIds.errors
          });
        }
      }
    },
    onError: (error) => {
      console.error('❌ [GRAPHQL PROPOSAL] Erro ao buscar influencers via GraphQL:', error);
      setUsingPreFilteredData(false);
    }
  });

  // 🚀 NOVA IMPLEMENTAÇÃO: Query GraphQL dedicada para buscar influencers de proposta
  const GET_PROPOSAL_INFLUENCERS_DIRECT = gql`
    query GetProposalInfluencersDirect($proposalId: ID!, $userId: ID!) {
      proposalInfluencers(proposalId: $proposalId, userId: $userId) {
        influencerIds
        totalCount
        success
      }
    }
  `;

  // Hook GraphQL para buscar IDs dos influencers da proposta
  const {
    data: proposalInfluencerIdsData,
    loading: loadingProposalIds,
    error: proposalIdsError,
    refetch: refetchProposalIds
  } = useQuery(GET_PROPOSAL_INFLUENCERS_DIRECT, {
    variables: { 
      proposalId: selectedProposal?.id,
      userId: userId
    },
    skip: !selectedProposal?.id || !userId,
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    onCompleted: (data) => {
      if (data?.proposalInfluencers?.success) {
        const influencerIds = data.proposalInfluencers.influencerIds || [];
        console.log('🎯 [GRAPHQL PROPOSAL IDS] IDs obtidos via GraphQL:', {
          proposalId: selectedProposal?.id,
          influencerIds,
          count: influencerIds.length
        });
        setProposalInfluencerIds(influencerIds);
        }
    },
    onError: (error) => {
      console.error('❌ [GRAPHQL PROPOSAL IDS] Erro ao buscar IDs:', error);
        setProposalInfluencerIds([]);
      }
  });

  // Effect principal para resetar estado quando proposta é removida
  useEffect(() => {
    if (!selectedProposal?.id) {
      setProposalInfluencerIds([]);
      setSnapshotInfluencers([]);
      setUsingSnapshots(false);
      setUsingPreFilteredData(false);
      console.log('🔄 [PROPOSAL FLOW] Proposta removida, resetando estado');
    } else {
      console.log('🎯 [PROPOSAL FLOW] Proposta selecionada:', selectedProposal.name, 'GraphQL irá carregar automaticamente');
    }
  }, [selectedProposal?.id]);
  
  // 🔥 CORREÇÃO: Props externas - evitar múltiplas atualizações de estado simultâneas
  useEffect(() => {
    let shouldUpdate = false;
    const updates: any = {};
    
    if (externalSearchTerm !== undefined && externalSearchTerm !== searchTerm) {
      updates.searchTerm = externalSearchTerm;
      shouldUpdate = true;
    }
    if (externalLocation !== undefined && externalLocation !== selectedLocation) {
      updates.selectedLocation = externalLocation;
      shouldUpdate = true;
    }
    if (externalMinFollowers !== undefined && externalMinFollowers !== minFollowers) {
      updates.minFollowers = externalMinFollowers;
      shouldUpdate = true;
    }
    if (externalMaxFollowers !== undefined && externalMaxFollowers !== maxFollowers) {
      updates.maxFollowers = externalMaxFollowers;
      shouldUpdate = true;
    }
    if (externalMinRating !== undefined && externalMinRating !== minRating) {
      updates.minRating = externalMinRating;
      shouldUpdate = true;
    }
    if (externalVerifiedOnly !== undefined && externalVerifiedOnly !== verifiedOnly) {
      updates.verifiedOnly = externalVerifiedOnly;
      shouldUpdate = true;
    }
    if (externalAvailableOnly !== undefined && externalAvailableOnly !== availableOnly) {
      updates.availableOnly = externalAvailableOnly;
      shouldUpdate = true;
    }
    if (externalSelectedBrands !== undefined && JSON.stringify(externalSelectedBrands) !== JSON.stringify(selectedBrands)) {
      updates.selectedBrands = externalSelectedBrands;
      shouldUpdate = true;
    }
    
    if (shouldUpdate) {
      // Atualizar todos os estados de uma vez para evitar múltiplas re-renderizações
      if (updates.searchTerm !== undefined) setSearchQuery(updates.searchTerm);
      if (updates.selectedLocation !== undefined) setSelectedLocation(updates.selectedLocation);
      if (updates.minFollowers !== undefined) setMinFollowers(updates.minFollowers);
      if (updates.maxFollowers !== undefined) setMaxFollowers(updates.maxFollowers);
      if (updates.minRating !== undefined) setMinRating(updates.minRating);
      if (updates.verifiedOnly !== undefined) setVerifiedOnly(updates.verifiedOnly);
      if (updates.availableOnly !== undefined) setAvailableOnly(updates.availableOnly);
      if (updates.selectedBrands !== undefined) setSelectedBrands(updates.selectedBrands);
    }
  }, [externalSearchTerm, externalLocation, externalMinFollowers, externalMaxFollowers, externalMinRating, externalVerifiedOnly, externalAvailableOnly, externalSelectedBrands]);
  
  // 🔥 NOVO: Decidir qual fonte de dados usar baseado na proposta selecionada
  const sourceInfluencers = selectedProposal && usingPreFilteredData ? snapshotInfluencers : influencers;
  
  // 🔧 CORREÇÃO: Garantir que sourceInfluencers é sempre um array
  const safeSourceInfluencers = Array.isArray(sourceInfluencers) ? sourceInfluencers : [];
  
  // 🔧 CORREÇÃO: Eliminar duplicações antes da filtragem
  const uniqueInfluencers = safeSourceInfluencers.reduce((acc: any[], current: any) => {
    const existingInfluencer = acc.find((inf: any) => inf.id === current.id);
    if (!existingInfluencer) {
      acc.push(current);
    }
    return acc;
  }, [] as any[]);

  // Log para verificar se há duplicações
  if (safeSourceInfluencers.length !== uniqueInfluencers.length) {
    console.warn(`🚨 [InfluencerGrid] ${safeSourceInfluencers.length - uniqueInfluencers.length} influenciadores duplicados removidos!`);
  }
  
  // Log para debug do tipo de dados recebidos
  if (!Array.isArray(sourceInfluencers)) {
    console.warn('⚠️ [InfluencerGrid] sourceInfluencers não é um array:', typeof sourceInfluencers, sourceInfluencers);
  }

  

  // Filtragem de influenciadores baseada nos filtros
  const filteredInfluencers = uniqueInfluencers.filter((influencer: any) => {
    // 🔥 FILTRO POR PROPOSTA: Se estamos usando dados pré-filtrados, pular filtro de proposta
    if (selectedProposal && usingPreFilteredData) {
      console.log('✅ [FILTER] Usando dados pré-filtrados da proposta (cache Apollo)');
      // Dados já estão filtrados pela proposta, pular para outros filtros
    } else if (selectedProposal && !usingPreFilteredData && proposalInfluencerIds.length > 0) {
      // Se tem proposta mas não está usando dados pré-filtrados, aplicar filtro manual
      const isIncluded = proposalInfluencerIds.includes(influencer.id);
      if (!isIncluded) {
        console.log(`❌ [FILTER] Influencer ${influencer.name} (${influencer.id}) NÃO está na proposta`);
        return false;
      }
      console.log(`✅ [FILTER] Influencer ${influencer.name} (${influencer.id}) ESTÁ na proposta`);
    } else if (selectedProposal && proposalInfluencerIds.length === 0) {
      // Proposta selecionada mas sem influencers ou ainda carregando
      console.log(`⏳ [FILTER] Proposta sem influencers ou ainda carregando`);
      return false; // Não mostrar nada até carregar
    }
    
    // Filtro de pesquisa
    if (searchTerm && !influencer.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    // Filtro de categoria
    if (selectedCategory !== 'all' && influencer.category !== selectedCategory) {
      return false;
    }
    
    // Filtro de localização
    if (selectedLocation && !influencer.location.includes(selectedLocation)) {
      return false;
    }
    
    // Filtro de seguidores
    const totalFollowers = parseInt(influencer.totalFollowers || '0', 10);
    if (minFollowers > 0 && totalFollowers < minFollowers) {
      return false;
    }
    if (maxFollowers > 0 && totalFollowers > maxFollowers) {
      return false;
    }
    
    // Filtro de avaliação mínima
    if (influencer.rating < minRating) {
      return false;
    }
    
    // Filtro de verificados
    if (verifiedOnly && !influencer.verified) {
      return false;
    }
    
    // Filtro de disponibilidade (simplificação: consideramos disponíveis os que têm rating acima de 4)
    if (availableOnly && influencer.rating < 4) {
      return false;
    }
    
    // Filtro de marcas
    if (selectedBrands.length > 0) {
      const influencerBrands = [
        ...(influencer.financialData?.brandHistory?.instagram || []),
        ...(influencer.financialData?.brandHistory?.tiktok || []),
        ...(influencer.financialData?.brandHistory?.youtube || [])
      ];
      
      return selectedBrands.some(brandId => 
        influencerBrands.includes(brandId)
      );
    }
    
    return true;
  });

  // 🔥 ANTIGO: Atualizar dados da proposta quando novos influencers chegam via GraphQL
  // REMOVIDO: Esta lógica foi substituída pela query GraphQL dedicada
  /*
  useEffect(() => {
    if (selectedProposal && proposalInfluencerIds.length > 0 && !usingPreFilteredData && influencers.length > 0) {
      // Verificar se agora temos todos os influencers necessários no cache
      const cachedInfluencers = influencers.filter((inf: any) => proposalInfluencerIds.includes(inf.id));
      
      if (cachedInfluencers.length === proposalInfluencerIds.length) {
        console.log('🔄 [AUTO UPDATE] Todos os influencers da proposta agora estão no cache, atualizando...');
        setSnapshotInfluencers(cachedInfluencers);
        setUsingSnapshots(false); // ❌ Não são snapshots históricos
        setUsingPreFilteredData(true); // ✅ Agora temos dados pré-filtrados
      }
    }
  }, [influencers.length, selectedProposal, proposalInfluencerIds, usingPreFilteredData]);
  */

  // 🔥 NOVO: Detectar quando a query GraphQL da proposta carrega dados
  useEffect(() => {
    if (proposalInfluencersData?.influencersByIds && selectedProposal) {
      const influencersArray = proposalInfluencersData.influencersByIds.influencers || [];
      
      console.log('🔄 [GRAPHQL AUTO UPDATE] Query GraphQL completou, atualizando componente:', {
        proposalId: selectedProposal.id,
        influencersReceived: influencersArray.length,
        dataStructure: typeof proposalInfluencersData.influencersByIds,
        rawData: proposalInfluencersData.influencersByIds,
        loadingState: loadingProposalInfluencers,
        source: 'GraphQL cache-first'
      });
      
      // Garantir que sempre setamos um array
      if (Array.isArray(influencersArray)) {
        setSnapshotInfluencers(influencersArray);
        setUsingSnapshots(false); // ❌ Não são snapshots históricos
        setUsingPreFilteredData(true); // ✅ Dados pré-filtrados via GraphQL
      } else {
        console.warn('⚠️ [GRAPHQL AUTO UPDATE] Dados recebidos não são um array:', influencersArray);
        setSnapshotInfluencers([]);
        setUsingPreFilteredData(false);
      }
    }
  }, [proposalInfluencersData, selectedProposal, loadingProposalInfluencers]);

  // Log do resultado final da filtragem
  useEffect(() => {
    if (selectedProposal) {
      console.log('📊 [FILTER RESULT] Resultado da filtragem:', {
        proposalName: selectedProposal.name,
        totalInfluencers: uniqueInfluencers.length,
        proposalInfluencerIds: proposalInfluencerIds.length,
        filteredCount: filteredInfluencers.length,
        usingSnapshots: usingSnapshots, // 📸 Snapshots históricos reais
        usingPreFilteredData: usingPreFilteredData, // 🎯 Dados pré-filtrados (cache/snapshots)
        sourceType: selectedProposal && usingPreFilteredData ? 'pré-filtrados (cache/snapshots)' : 'todos influencers + filtro manual',
        filteredNames: filteredInfluencers.map((inf: any) => inf.name)
      });
    }
  }, [selectedProposal, proposalInfluencerIds, filteredInfluencers.length, uniqueInfluencers.length, usingSnapshots, usingPreFilteredData]);
  
  return (
    <div className="w-full">
      {/* Header fixo com Estatísticas, Filtros e Toolbar */}
      <AnimatedStats 
        influencersCount={influencers.length} 
        userId={userId}
        searchTerm={searchTerm}
        selectedCategory={selectedCategory}
        onSearchChange={setSearchQuery}
        onCategoryChange={setSelectedCategory}
        viewMode={viewMode}
        selectionMode={selectionMode}
        selectedCount={selectedInfluencers.length}
        onViewModeChange={setViewMode}
        onAddInfluencer={() => setAddInfluencerModalOpen(true)}
        onToggleSelectionMode={() => {
          setSelectionMode(!selectionMode);
          if (selectionMode) setSelectedInfluencers([]);
        }}
        onDeleteSelected={() => openBulkDeleteDialog(selectedInfluencers, setBulkDeleteDialogOpen)}
        onDuplicateSelected={() => handleBulkDuplicate(selectedInfluencers, influencers, setSelectedInfluencers, () => refreshData(), gradients)}
        onBrandFilterChange={(brands: string[]) => {
          if (selectedBrands !== brands) {
            setSelectedBrands(brands);
          }
        }}
        verifiedOnly={verifiedOnly}
        onVerifiedOnlyChange={setVerifiedOnly}
        onSendToBrand={handleSendToBrand}
      />
      
      {/* Conteúdo com espaçamento para compensar headers fixos */}
      <div className="space-y-6 px-2 pt-2">
      
    
      
      {/* Exibição de Influenciadores (Grid ou Lista) */}
      {error ? (
        <div className="text-center py-8 text-red-500">{error}</div>
      ) : filteredInfluencers.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          {selectedProposal && proposalInfluencerIds.length === 0 && !loadingProposalInfluencers ? (
            <div className="space-y-2">
              <p>Esta proposta não possui influencers.</p>
              <p className="text-xs">Adicione influencers à proposta para vê-los aqui.</p>
            </div>
          ) : (
            "Nenhum influenciador encontrado."
          )}
        </div>
      ) : (
        <>
          {viewMode === 'grid' ? (
            <GridView
              influencers={filteredInfluencers}
              onInfluencerClick={handleOpenInfluencerDetail}
              selectedInfluencers={selectedInfluencers}
              selectedInfluencer={selectedInfluencer}
              selectionMode={selectionMode}
              onToggleSelection={(id) => toggleInfluencerSelection(id, selectedInfluencers, setSelectedInfluencers)}
              onDuplicate={handleDuplicateInfluencer}
              onEdit={handleEditInfluencer}
              onDelete={handleDeleteConfirm}
              selectedInfluencerId={selectedInfluencerId}
              selectedProposal={selectedProposal}
            />
          ) : (
            <div className="space-y-2">
              <DataTable
                columns={columns}
                data={filteredInfluencers}
                enableRowSelection={true}
                enableColumnVisibility={true}
                enablePagination={true}
                enableSorting={true}
                enableFiltering={true}
                enableColumnOrdering={true}
                pageSize={20}
                searchPlaceholder="Buscar influenciadores..."
                onRowClick={handleOpenInfluencerDetail}
                selectionMode={selectionMode}
                selectedRows={selectedInfluencers}
                onRowSelect={(id: string) => toggleInfluencerSelection(id, selectedInfluencers, setSelectedInfluencers)}
                className="[&_tbody_tr]:border-b-2"
              />
            </div>
          )}

          {/* Indicador de scroll infinito */}
          {(hasNextPage || loadingMore) && (
            <div ref={loadingRef} className="flex justify-center py-8">
              {loadingMore ? (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                  <span>Carregando mais influenciadores...</span>
                </div>
              ) : (
                <div className="text-muted-foreground/60 text-sm">
                  Carregue mais influenciadores rolando para baixo
                </div>
              )}
            </div>
          )}

          {/* Indicador de fim dos dados */}
          {!hasNextPage && filteredInfluencers.length > 0 && (
            <div className="text-center py-8 text-muted-foreground/60 text-sm">
              Todos os influenciadores foram carregados ({totalCount} total)
            </div>
          )}
        </>
      )}
      
      </div> {/* Fim do conteúdo com espaçamento */}
      
      {/* Modal de Detalhes removido */}
      
      {/* Modal para adicionar influenciador - Isolado do space-y-6 */}
      <div className="absolute">
        <AddInfluencerForm 
          open={addInfluencerModalOpen} 
          onOpenChange={setAddInfluencerModalOpen}
          mode="create"
          onSubmit={async (data) => {
            try {
              console.log('🚀 [GraphQL] Criando novo influenciador:', data.name);
              
              // Usar GraphQL para criar influenciador
              const result = await createInfluencer(data);
              
              // Após submeter o formulário com sucesso, fechar o modal
              setAddInfluencerModalOpen(false);
              console.log('✅ [GraphQL] Influenciador criado com sucesso');
              
              // Retornar o resultado
              return result;
            } catch (error) {
              console.error('❌ [GraphQL] Erro ao adicionar influenciador:', error);
              toast.error('Erro ao adicionar influenciador. Tente novamente.');
              throw error;
            }
          }} 
        />
      </div>
      
      {/* Modal para editar influenciador usando o mesmo formulário de adição - Isolado do space-y-6 */}
      {selectedInfluencer && (
        <div className="absolute">
          <AddInfluencerForm 
            open={editInfluencerModalOpen} 
            onOpenChange={setEditInfluencerModalOpen}
            initialData={selectedInfluencer}
            mode="edit"
            onSubmit={async (data) => {
              try {
                console.log('🔄 [GraphQL] Atualizando influenciador:', selectedInfluencer.id);
                
                // Usar GraphQL para atualizar influenciador
                const result = await updateInfluencer(String(selectedInfluencer.id), data);
                
                // Após submeter o formulário com sucesso, fechar o modal
                setEditInfluencerModalOpen(false);
                console.log('✅ [GraphQL] Influenciador atualizado com sucesso');
                
                // Retornar o resultado
                return result;
              } catch (error) {
                console.error('❌ [GraphQL] Erro ao atualizar influenciador:', error);
                toast.error('Erro ao atualizar influenciador. Tente novamente.');
                throw error;
              }
            }} 
          />
        </div>
      )}
      
      {/* Diálogo de confirmação para exclusão */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o influenciador "{influencerToDelete?.name}"? 
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
                          onClick={async () => {
              if (influencerToDelete) {
                try {
                  console.log('🗑️ [GraphQL] Deletando influenciador:', influencerToDelete.id);
                  await deleteInfluencer(String(influencerToDelete.id));
                  setDeleteDialogOpen(false);
                  setInfluencerToDelete(null);
                  console.log('✅ [GraphQL] Influenciador deletado com sucesso');
                } catch (error) {
                  console.error('❌ [GraphQL] Erro ao deletar influenciador:', error);
                  toast.error('Erro ao deletar influenciador');
                }
              }
            }}
              className="bg-red-600 hover:bg-red-700"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Diálogo de confirmação para exclusão em massa */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão em massa</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir {selectedInfluencers.length} influenciador(es)? 
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
                          onClick={async () => {
              try {
                console.log('🗑️ [GraphQL] Deletando influenciadores em massa:', selectedInfluencers);
                
                // Deletar cada influenciador via GraphQL
                for (const id of selectedInfluencers) {
                  await deleteInfluencer(String(id));
                }
                
                setSelectedInfluencers([]);
                setBulkDeleteDialogOpen(false);
                console.log('✅ [GraphQL] Influenciadores deletados em massa com sucesso');
              } catch (error) {
                console.error('❌ [GraphQL] Erro ao deletar influenciadores em massa:', error);
                toast.error('Erro ao deletar influenciadores');
              }
            }}
              className="bg-red-600 hover:bg-red-700"
            >
              Excluir {selectedInfluencers.length} influenciador(es)
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}



