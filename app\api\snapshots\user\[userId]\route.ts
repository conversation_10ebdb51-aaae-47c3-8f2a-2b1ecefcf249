import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      // DESABILITADO: Firebase Auth removido em favor do Clerk
      // decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { error: 'Token inválido' },
        { status: 401 }
      );
    }

    const requestUserId = decodedToken.uid;
    
    // 🔒 ISOLAMENTO: Verificar se o usuário pode acessar esses snapshots
    if (requestUserId !== userId) {
      return NextResponse.json(
        { error: 'Acesso negado: você só pode acessar seus próprios snapshots' },
        { status: 403 }
      );
    }
    
    console.log('🔍 [USER_SNAPSHOTS] Buscando snapshots do usuário:', userId);
    
    // Buscar todas as coleções proposal_sharings onde há snapshots do usuário
    const sharingCollection = db.collection('proposal_sharings');
    const sharingsSnapshot = await sharingCollection.where('createdBy', '==', userId).get();
    
    const userSnapshots = [];
    
    for (const sharingDoc of sharingsSnapshot.docs) {
      const shareToken = sharingDoc.id;
      const sharingData = sharingDoc.data();
      
      console.log(`📋 [USER_SNAPSHOTS] Verificando snapshots para share: ${shareToken}`);
      
      // Buscar snapshots desta partilha específica
      const snapshotsRef = sharingDoc.ref.collection('snapshots');
      const snapshotsSnapshot = await snapshotsRef.get();
      
      if (!snapshotsSnapshot.empty) {
        const snapshots = snapshotsSnapshot.docs.map(doc => ({
          id: doc.id,
          shareToken,
          proposalId: sharingData.proposalId,
          data: doc.data(),
          createdAt: sharingData.createdAt,
          expiresAt: sharingData.expiresAt
        }));
        
        userSnapshots.push(...snapshots);
      }
    }
    
    console.log(`✅ [USER_SNAPSHOTS] Encontrados ${userSnapshots.length} snapshots para usuário ${userId}`);
    
    return NextResponse.json({
      success: true,
      userId,
      snapshots: {
        count: userSnapshots.length,
        data: userSnapshots
      }
    });
    
  } catch (error) {
    console.error('❌ [USER_SNAPSHOTS] Erro ao buscar snapshots do usuário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 