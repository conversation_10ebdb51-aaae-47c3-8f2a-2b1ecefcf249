import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';

// POST - Recalcular total de uma proposta específica
export async function POST(request: NextRequest) {
  try {
    const { proposalId, userId, recalculateAll } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    // 🔥 RECÁLCULO EM MASSA: Todas as propostas do usuário
    if (recalculateAll) {
      console.log('🔧 [API] Iniciando recálculo em massa para usuário:', userId);
      
      const result = await ProposalService.recalculateAllUserProposalTotals(userId);
      
      return NextResponse.json({
        success: result.success,
        type: 'bulk',
        message: `Recálculo em massa concluído: ${result.updatedCount}/${result.processedCount} propostas atualizadas`,
        data: {
          processedCount: result.processedCount,
          updatedCount: result.updatedCount,
          results: result.results
        }
      });
    }

    // 🔥 RECÁLCULO INDIVIDUAL: Uma proposta específica
    if (!proposalId) {
      return NextResponse.json(
        { error: 'proposalId é obrigatório para recálculo individual' },
        { status: 400 }
      );
    }

    console.log('🔧 [API] Iniciando recálculo individual para proposta:', proposalId);
    
    const result = await ProposalService.forceRecalculateProposalTotal(proposalId);
    
    return NextResponse.json({
      success: result.success,
      type: 'individual',
      message: result.message,
      data: {
        proposalId,
        oldTotal: result.oldTotal,
        newTotal: result.newTotal,
        budgetCount: result.budgetCount,
        changed: result.oldTotal !== result.newTotal
      }
    });

  } catch (error) {
    console.error('❌ [API] Erro no recálculo de totais:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

// GET - Verificar status dos totais (para debug)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const proposalId = searchParams.get('proposalId');

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    if (proposalId) {
      // Verificar uma proposta específica
      const proposal = await ProposalService.getProposalById(proposalId);
      
      if (!proposal) {
        return NextResponse.json(
          { error: 'Proposta não encontrada' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        proposal: {
          id: proposal.id,
          nome: proposal.nome,
          totalAmount: proposal.totalAmount,
          influencerCount: proposal.influencers?.length || 0,
          updatedAt: proposal.updatedAt
        }
      });
    } else {
      // Listar todas as propostas do usuário com resumo
      const proposals = await ProposalService.getAllUserProposalsSimple(userId, 100);
      
      const summary = proposals.map(proposal => ({
        id: proposal.id,
        nome: proposal.nome,
        totalAmount: proposal.totalAmount,
        influencerCount: proposal.influencers?.length || 0,
        updatedAt: proposal.updatedAt
      }));

      return NextResponse.json({
        success: true,
        count: proposals.length,
        totalValue: proposals.reduce((sum, p) => sum + (p.totalAmount || 0), 0),
        proposals: summary
      });
    }

  } catch (error) {
    console.error('❌ [API] Erro ao verificar status dos totais:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
} 

