const admin = require('firebase-admin');

// Configurar Firebase Admin se ainda não foi configurado
if (!admin.apps.length) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

async function testSnapshotsAPI() {
  try {
    console.log('🧪 [TEST] Testando API de snapshots...');
    
    // Gerar token de admin para testar
    const customToken = await admin.auth().createCustomToken('test-user', {
      role: 'admin'
    });
    
    console.log('✅ [TEST] Token gerado com sucesso');
    
    // Fazer requisição para a API (simulando)
    const proposalId = 'Xd8ntVkssHRlqnvISxiz';
    console.log(`🔍 [TEST] Verificando snapshots para proposta: ${proposalId}`);
    
    // Simular o que a API faz manualmente
    const db = admin.firestore();
    
    // 1. Buscar proposal_sharings
    const proposalSharingsQuery = db.collection('proposal_sharings')
      .where('proposalId', '==', proposalId);
    
    const sharingsSnapshot = await proposalSharingsQuery.get();
    console.log(`📋 [TEST] Sharings encontrados: ${sharingsSnapshot.size}`);
    
    if (sharingsSnapshot.empty) {
      console.log('❌ [TEST] Nenhum sharing encontrado');
      return;
    }
    
    // 2. Buscar snapshots
    const allInfluencers = [];
    
    for (const sharingDoc of sharingsSnapshot.docs) {
      const shareToken = sharingDoc.id;
      console.log(`🔍 [TEST] Processando share: ${shareToken}`);
      
      const snapshotsCollection = sharingDoc.ref.collection('snapshots');
      const snapshotsSnapshot = await snapshotsCollection.get();
      
      console.log(`📸 [TEST] Snapshots encontrados: ${snapshotsSnapshot.size}`);
      
      for (const snapshotDoc of snapshotsSnapshot.docs) {
        const influencerId = snapshotDoc.id;
        const mainData = snapshotDoc.data();
        
        // Buscar subcoleções
        const demographicsCollection = snapshotDoc.ref.collection('demographics');
        const demographicsSnapshot = await demographicsCollection.get();
        
        const budgetsCollection = snapshotDoc.ref.collection('budgets');
        const budgetsSnapshot = await budgetsCollection.get();
        
        const currentDemographics = [];
        demographicsSnapshot.forEach(doc => {
          currentDemographics.push({
            id: doc.id,
            ...doc.data()
          });
        });
        
        const currentBudgets = [];
        budgetsSnapshot.forEach(doc => {
          currentBudgets.push({
            id: doc.id,
            ...doc.data()
          });
        });
        
        // Montar influenciador como na API
        const influencerSnapshot = {
          id: influencerId,
          originalInfluencerId: mainData.originalInfluencerId || influencerId,
          ...mainData,
          currentDemographics,
          currentBudgets,
          snapshotMetadata: {
            shareToken,
            capturedAt: mainData.capturedAt,
            version: mainData.version,
            source: mainData.source,
            isSnapshot: true
          }
        };
        
        allInfluencers.push(influencerSnapshot);
        
        console.log(`👤 [TEST] Influenciador processado:`, {
          id: influencerId,
          name: mainData.name,
          demographicsCount: currentDemographics.length,
          budgetsCount: currentBudgets.length,
          hasCurrentDemographics: !!influencerSnapshot.currentDemographics,
          hasCurrentBudgets: !!influencerSnapshot.currentBudgets
        });
      }
    }
    
    console.log(`\n🎯 [TEST RESULTADO]:`);
    console.log(`Total de influenciadores: ${allInfluencers.length}`);
    
    if (allInfluencers.length > 0) {
      const firstInfluencer = allInfluencers[0];
      console.log(`Primeiro influenciador:`, {
        id: firstInfluencer.id,
        name: firstInfluencer.name,
        hasCurrentDemographics: !!firstInfluencer.currentDemographics,
        currentDemographicsLength: firstInfluencer.currentDemographics?.length || 0,
        hasCurrentBudgets: !!firstInfluencer.currentBudgets,
        currentBudgetsLength: firstInfluencer.currentBudgets?.length || 0,
        demographicsData: firstInfluencer.currentDemographics?.map(d => ({
          platform: d.platform,
          hasAudienceGender: !!d.audienceGender
        }))
      });
      
      console.log(`\n✅ [TEST] OS DADOS ESTÃO SENDO CARREGADOS CORRETAMENTE!`);
      console.log(`📊 Demographics: ${firstInfluencer.currentDemographics?.length || 0}`);
      console.log(`💰 Budgets: ${firstInfluencer.currentBudgets?.length || 0}`);
    }
    
  } catch (error) {
    console.error('❌ [TEST] Erro:', error);
  }
}

testSnapshotsAPI(); 
