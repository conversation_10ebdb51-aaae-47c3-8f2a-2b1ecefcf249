// HOOK REACT PARA OPERAÇÕES GRAPHQL DE INFLUENCIADORES
// Substitui as chamadas REST por GraphQL otimizado

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useApolloClient, gql } from '@apollo/client';

// ===== QUERIES GRAPHQL =====

export const GET_INFLUENCERS = gql`
  query GetInfluencers($userId: ID!, $filters: InfluencerFilters, $pagination: PaginationInput) {
    influencers(userId: $userId, filters: $filters, pagination: $pagination) {
      nodes {
        id
        name
        avatar
        totalFollowers
        engagementRate
        rating
        isVerified
        isAvailable
        status
        category
        country
        state
        city
        # Redes sociais (campos diretos)
        instagramUsername
        instagramFollowers
        instagramEngagementRate
        instagramAvgViews
        instagramStoriesViews
        instagramReelsViews
        
        tiktokUsername
        tiktokFollowers
        tiktokEngagementRate
        tiktokAvgViews
        tiktokVideoViews
        
        youtubeUsername
        youtubeFollowers
        youtubeSubscribers
        youtubeEngagementRate
        youtubeAvgViews
        youtubeShortsViews
        youtubeLongFormViews
        
        facebookUsername
        facebookFollowers
        facebookEngagementRate
        facebookAvgViews
        facebookViews
        facebookReelsViews
        facebookStoriesViews
        
        twitchUsername
        twitchFollowers
        twitchEngagementRate
        twitchViews
        
        kwaiUsername
        kwaiFollowers
        kwaiEngagementRate
        kwaiViews
        
        # Campos de pricing diretos
        facebookPost
        twitchStream
        kwaiVideo
        
        # Configurações profissionais
        promotesTraders
        responsibleName
        agencyName
        responsibleCapturer
        
        # Rede social principal
        mainNetwork
        mainPlatform
        
        # Dados pessoais adicionais
        email
        phone
        whatsapp
        age
        gender
        bio
        location
        userId
        totalViews
        backgroundImage
        gradient
        categories
        
        # Pricing denormalizado básico
        pricing {
          hasFinancialData
          priceRange
          avgPrice
          isNegotiable
        }
        
        # Pricing separado (nova estrutura) - dados básicos
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
            facebook {
              post { price currency }
            }
            twitch {
              stream { price currency }
            }
            kwai {
              video { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }
        
        # Demographics separado (nova estrutura) - dados básicos
        currentDemographics {
          id
          platform
          audienceGender {
            male
            female
            other
          }
          audienceLocations {
            country
            percentage
          }
          audienceCities {
            city
            percentage
          }
          audienceAgeRange {
            range
            percentage
          }
          isActive
          source
        }

        # Orçamentos organizados por plataforma
        budgets {
          instagram {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          tiktok {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          youtube {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          facebook {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          twitch {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          kwai {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          personalizado {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
        }
        
        createdAt
        updatedAt
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

const GET_INFLUENCER_DETAILS = gql`
  query GetInfluencerDetails($id: ID!) {
    influencer(id: $id) {
      id
      name
      email
      phone
      whatsapp
      avatar
      bio
      totalFollowers
      engagementRate
      rating
      isVerified
      isAvailable
      status
      category
      categories
      country
      state
      city
      location
      age
      gender
      
      # Campos diretos das plataformas
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      # Campos de pricing diretos
      facebookPost
      twitchStream
      kwaiVideo
      
      promotesTraders
      responsibleName
      agencyName
      responsibleCapturer
      
      # Pricing separado (nova estrutura)
      currentPricing {
        id
        services {
          instagram {
            story {
              price
              currency
            }
            reel {
              price
              currency
            }
          }
          tiktok {
            video {
              price
              currency
            }
          }
          youtube {
            insertion {
              price
              currency
            }
            dedicated {
              price
              currency
            }
            shorts {
              price
              currency
            }
          }
          facebook {
            post {
              price
              currency
            }
          }
          twitch {
            stream {
              price
              currency
            }
          }
          kwai {
            video {
              price
              currency
            }
          }
        }
        isActive
        validFrom
        validUntil
        notes
        createdAt
        updatedAt
      }
      
      # Demographics separado (nova estrutura)
      currentDemographics {
        id
        platform
        audienceGender {
          male
          female
          other
        }
        audienceLocations {
          country
          percentage
        }
        audienceCities {
          city
          percentage
        }
        audienceAgeRange {
          range
          percentage
        }
        captureDate
        isActive
        source
        createdAt
        updatedAt
      }
      
      # Orçamentos organizados por plataforma
      budgets {
        instagram {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        tiktok {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        youtube {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        facebook {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        twitch {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        kwai {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        personalizado {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
      }

      # Fallback para estrutura antiga (compatibilidade)
      pricing {
        hasFinancialData
        priceRange
        avgPrice
        isNegotiable
        lastPriceUpdate
      }
      createdAt
      updatedAt
    }
    
    # Fallback para dados financeiros antigos (compatibilidade)
    influencerFinancial(influencerId: $id) {
      id
      responsibleName
      agencyName
      email
      whatsapp
      prices {
        instagramStory { name price }
        instagramReel { name price }
        tiktokVideo { name price }
        youtubeInsertion { name price }
        youtubeDedicated { name price }
        youtubeShorts { name price }
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_INFLUENCERS_BY_PRICE_RANGE = gql`
  query GetInfluencersByPriceRange($priceRange: String!, $userId: ID!, $limit: Int) {
    influencersByPriceRange(priceRange: $priceRange, userId: $userId, limit: $limit) {
      id
      name
      avatar
      totalFollowers
      pricing {
        avgPrice
        priceRange
        hasFinancialData
      }
    }
  }
`;

const GET_DASHBOARD_DATA = gql`
  query GetDashboardData($userId: ID!) {
    dashboardData(userId: $userId) {
      totalInfluencers
      totalWithFinancialData
      totalCampaigns
      totalBrands
      financialStats {
        totalWithPricing
        avgPrice
        minPrice
        maxPrice
      }
      recentInfluencers {
        id
        name
        avatar
        updatedAt
      }
      topPerformers {
        id
        name
        avatar
        engagementRate
      }
    }
  }
`;

const GET_CACHE_STATS = gql`
  query GetCacheStats {
    cacheStats {
      hits
      misses
      evictions
      size
      hitRate
    }
  }
`;

const GET_CATEGORIES = gql`
  query GetCategories($userId: ID) {
    categories(userId: $userId) {
      id
      name
      slug
      description
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`;

const GET_BRANDS = gql`
  query GetBrands($userId: ID!) {
    brands(userId: $userId) {
      id
      userId
      name
      description
      logo
      website
      industry
      size
      status
      createdAt
      updatedAt
    }
  }
`;

const GET_BRAND_INFLUENCER_ASSOCIATIONS = gql`
  query GetBrandInfluencerAssociations($userId: ID!, $influencerId: ID, $brandId: ID) {
    brandInfluencerAssociations(userId: $userId, influencerId: $influencerId, brandId: $brandId) {
      id
      brandId
      brandName
      brandLogo
      influencerId
      influencerName
      influencerAvatar
      status
      tags
      createdAt
      updatedAt
    }
  }
`;

const GET_STATS = gql`
  query GetStats($userId: ID!) {
    stats(userId: $userId) {
      totalInfluencers
      totalViews
      totalFollowers
      totalBrands
      totalCampaigns
    }
  }
`;

const GET_SAVED_FILTERS = gql`
  query GetSavedFilters($userId: ID!) {
    savedFilters(userId: $userId) {
      id
      userId
      name
      location
      minFollowers
      maxFollowers
      minRating
      verifiedOnly
      availableOnly
      platforms {
        instagram
        youtube
        tiktok
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_PROPOSAL_INFLUENCERS = gql`
  query GetProposalInfluencers($influencerIds: [ID!]!, $userId: ID!, $proposalId: ID) {
    influencersByIds(ids: $influencerIds, userId: $userId, proposalId: $proposalId) {
      influencers {
      id
      name
      avatar
      totalFollowers
      engagementRate
      rating
      isVerified
      isAvailable
      status
      category
      country
      state
      city
      age
      gender
      email
      phone
      whatsapp
      
      # Redes sociais (campos diretos) - COMPLETOS com todos os campos de views
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      facebookReelsViews
      facebookStoriesViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      # Campos de pricing diretos
      facebookPost
      twitchStream
      kwaiVideo
      
      promotesTraders
      responsibleName
      agencyName
      
      # Pricing separado (nova estrutura)
      currentPricing {
        id
        services {
          instagram {
            story { price currency }
            reel { price currency }
          }
          tiktok {
            video { price currency }
          }
          youtube {
            insertion { price currency }
            dedicated { price currency }
            shorts { price currency }
          }
          facebook {
            post { price currency }
          }
          twitch {
            stream { price currency }
          }
          kwai {
            video { price currency }
          }
        }
        isActive
        validFrom
        validUntil
        notes
        createdAt
        updatedAt
      }
      
      # Demographics separado (nova estrutura)
      currentDemographics {
        id
        platform
        audienceGender {
          male
          female
          other
        }
        audienceLocations {
          country
          percentage
        }
        audienceCities {
          city
          percentage
        }
        audienceAgeRange {
          range
          percentage
        }
        captureDate
        isActive
        source
        createdAt
        updatedAt
      }

      # Orçamentos organizados por plataforma
      budgets {
        instagram {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        tiktok {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        youtube {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        facebook {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        twitch {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        kwai {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
        personalizado {
          id
          amount
          currency
          status
          serviceType
          description
          createdAt
          updatedAt
        }
      }
      
      createdAt
      updatedAt
      }
      foundIds
      notFoundIds
      totalFound
      totalRequested
      processingTimeMs
      hasPartialFailure
      errors
    }
  }
`;

// ===== MUTATIONS GRAPHQL =====

const CREATE_INFLUENCER = gql`
  mutation CreateInfluencer($input: CreateInfluencerInput!) {
    createInfluencer(input: $input) {
      id
      name
      email
      phone
      whatsapp
      country
      state
      city
      location
      age
      gender
      bio
      avatar
      category
      categories
      totalFollowers
      engagementRate
      isVerified
      isAvailable
      status
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      facebookReelsViews
      facebookStoriesViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      # 🔥 CORREÇÃO: Adicionar campos administrativos
      promotesTraders
      responsibleName
      agencyName
      responsibleCapturer
      
      # Rede social principal
      mainNetwork
      mainPlatform
      
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_INFLUENCER = gql`
  mutation UpdateInfluencer($id: ID!, $input: UpdateInfluencerInput!) {
    updateInfluencer(id: $id, input: $input) {
      id
      name
      email
      phone
      whatsapp
      country
      state
      city
      location
      age
      gender
      bio
      avatar
      category
      categories
      totalFollowers
      engagementRate
      isVerified
      isAvailable
      status
      
      # Campos diretos das plataformas
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      facebookReelsViews
      facebookStoriesViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      # Campos de pricing diretos
      facebookPost
      twitchStream
      kwaiVideo
      
      promotesTraders
      responsibleName
      agencyName
      responsibleCapturer
      mainNetwork
      mainPlatform
      updatedAt
    }
  }
`;

const DELETE_INFLUENCER = gql`
  mutation DeleteInfluencer($id: ID!) {
    deleteInfluencer(id: $id)
  }
`;

const SYNC_FINANCIAL_DATA = gql`
  mutation SyncFinancialData($influencerId: ID!) {
    syncInfluencerFinancialData(influencerId: $influencerId)
  }
`;

const CLEAR_CACHE = gql`
  mutation ClearCache {
    clearCache
  }
`;

const CREATE_SAVED_FILTER = gql`
  mutation CreateSavedFilter($input: CreateSavedFilterInput!) {
    createSavedFilter(input: $input) {
      id
      name
      location
      minFollowers
      maxFollowers
      minRating
      verifiedOnly
      availableOnly
      platforms {
        instagram
        youtube
        tiktok
      }
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_SAVED_FILTER = gql`
  mutation UpdateSavedFilter($id: ID!, $input: UpdateSavedFilterInput!) {
    updateSavedFilter(id: $id, input: $input) {
      id
      name
      location
      minFollowers
      maxFollowers
      minRating
      verifiedOnly
      availableOnly
      platforms {
        instagram
        youtube
        tiktok
      }
      updatedAt
    }
  }
`;

const DELETE_SAVED_FILTER = gql`
  mutation DeleteSavedFilter($id: ID!) {
    deleteSavedFilter(id: $id)
  }
`;

const CREATE_BRAND_INFLUENCER_ASSOCIATION = gql`
  mutation CreateBrandInfluencerAssociation($input: CreateBrandInfluencerAssociationInput!) {
    createBrandInfluencerAssociation(input: $input) {
      id
      brandId
      brandName
      brandLogo
      influencerId
      influencerName
      influencerAvatar
      status
      tags
      createdAt
      updatedAt
    }
  }
`;

const DELETE_BRAND_INFLUENCER_ASSOCIATION = gql`
  mutation DeleteBrandInfluencerAssociation($id: ID!) {
    deleteBrandInfluencerAssociation(id: $id)
  }
`;

// ===== BRANDS MUTATIONS =====

const CREATE_BRAND = gql`
  mutation CreateBrand($input: CreateBrandInput!) {
    createBrand(input: $input) {
      id
      userId
      name
      description
      logo
      website
      industry
      size
      status
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_BRAND = gql`
  mutation UpdateBrand($id: ID!, $input: UpdateBrandInput!) {
    updateBrand(id: $id, input: $input) {
      id
      userId
      name
      description
      logo
      website
      industry
      size
      status
      updatedAt
    }
  }
`;

const DELETE_BRAND = gql`
  mutation DeleteBrand($id: ID!) {
    deleteBrand(id: $id)
  }
`;

// ===== PRICING MUTATIONS =====

const CREATE_INFLUENCER_PRICING = gql`
  mutation CreateInfluencerPricing($input: CreateInfluencerPricingInput!) {
    createInfluencerPricing(input: $input) {
      id
      influencerId
      services {
        instagram {
          story {
            price
            currency
          }
          reel {
            price
            currency
          }
        }
        tiktok {
          video {
            price
            currency
          }
        }
        youtube {
          insertion {
            price
            currency
          }
          dedicated {
            price
            currency
          }
          shorts {
            price
            currency
          }
        }
        facebook {
          post {
            price
            currency
          }
        }
        twitch {
          stream {
            price
            currency
          }
        }
        kwai {
          video {
            price
            currency
          }
        }
      }
      isActive
      validFrom
      validUntil
      notes
      createdAt
      updatedAt
    }
  }
`;

// ===== DEMOGRAPHICS MUTATIONS =====

const CREATE_AUDIENCE_DEMOGRAPHIC = gql`
  mutation CreateAudienceDemographic($input: CreateAudienceDemographicInput!) {
    createAudienceDemographic(input: $input) {
      id
      influencerId
      platform
      audienceGender {
        male
        female
        other
      }
      audienceLocations {
        country
        percentage
      }
      audienceCities {
        city
        percentage
      }
      audienceAgeRange {
        range
        percentage
      }
      captureDate
      isActive
      source
      createdAt
      updatedAt
    }
  }
`;

// ===== INTERFACES =====

interface InfluencerFilters {
  search?: string;
  category?: string;
  isAvailable?: boolean;
  followersMin?: number;
  followersMax?: number;
  priceRange?: string;
}

interface PaginationOptions {
  limit?: number;
  offset?: number;
}

interface UseInfluencersOptions {
  userId: string;
  filters?: InfluencerFilters;
  pagination?: PaginationOptions;
  enabled?: boolean;
}

// ===== HOOKS PRINCIPAIS =====

/**
 * Hook para buscar lista de influenciadores com GraphQL
 */
export function useInfluencers({ userId, filters = {}, pagination = {}, enabled = true }: UseInfluencersOptions) {
  const { data, loading, error, refetch, fetchMore } = useQuery(GET_INFLUENCERS, {
    variables: {
      userId,
      filters,
      pagination
    },
    skip: !enabled || !userId,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  });

  // Função para carregar mais itens (paginação)
  const loadMore = useCallback(async () => {
    if (!data?.influencers.hasNextPage || loading) return;

    try {
      await fetchMore({
        variables: {
          pagination: {
            ...pagination,
            offset: data.influencers.nodes.length
          }
        }
      });
    } catch (error) {
      console.error('Erro ao carregar mais influenciadores:', error);
    }
  }, [data, fetchMore, loading, pagination]);

  return {
    influencers: data?.influencers.nodes || [],
    totalCount: data?.influencers.totalCount || 0,
    hasNextPage: data?.influencers.hasNextPage || false,
    loading,
    error,
    refetch,
    loadMore
  };
}

/**
 * Hook para buscar detalhes de um influenciador específico
 */
export function useInfluencerDetails(influencerId: string | null) {
  const { data, loading, error, refetch } = useQuery(GET_INFLUENCER_DETAILS, {
    variables: { id: influencerId },
    skip: !influencerId,
    errorPolicy: 'all'
  });

  return {
    influencer: data?.influencer || null,
    financial: data?.influencerFinancial || null,
    loading,
    error,
    refetch
  };
}

/**
 * Hook para buscar influenciadores por faixa de preço
 */
export function useInfluencersByPriceRange(priceRange: string, userId: string, limit: number = 20) {
  const { data, loading, error, refetch } = useQuery(GET_INFLUENCERS_BY_PRICE_RANGE, {
    variables: { priceRange, userId, limit },
    skip: !priceRange || !userId,
    errorPolicy: 'all'
  });

  return {
    influencers: data?.influencersByPriceRange || [],
    loading,
    error,
    refetch
  };
}

/**
 * Hook para dados do dashboard
 */
export function useDashboardData(userId: string) {
  const { data, loading, error, refetch } = useQuery(GET_DASHBOARD_DATA, {
    variables: { userId },
    skip: !userId,
    errorPolicy: 'all'
  });

  return {
    dashboardData: data?.dashboardData || null,
    loading,
    error,
    refetch
  };
}

/**
 * Hook para estatísticas do cache - OTIMIZADO
 */
export function useCacheStats() {
  const { data, loading, error, refetch } = useQuery(GET_CACHE_STATS, {
    skip: true, // 🚫 DESABILITADO: Não executar esta query automaticamente
    errorPolicy: 'all',
    fetchPolicy: 'cache-first',
    notifyOnNetworkStatusChange: false
  });

  return {
    cacheStats: data?.cacheStats || null,
    loading,
    error,
    refetch
  };
}

/**
 * Hook para mutations de influenciadores
 */
export function useInfluencerMutations() {
  const client = useApolloClient();

  const [createInfluencer, { loading: createLoading }] = useMutation(CREATE_INFLUENCER, {
    // 🔧 CORREÇÃO: Removido refetchQueries automático para evitar perda de dados do grid
    // onCompleted: () => {
    //   // Invalidar cache de listas
    //   client.refetchQueries({
    //     include: ['GetInfluencers', 'GetDashboardData']
    //   });
    // }
  });

  const [updateInfluencer, { loading: updateLoading }] = useMutation(UPDATE_INFLUENCER, {
    // 🔧 CORREÇÃO: Removido refetchQueries automático para evitar perda de dados do grid
    // onCompleted: () => {
    //   client.refetchQueries({
    //     include: ['GetInfluencers', 'GetInfluencerDetails', 'GetDashboardData']
    //   });
    // }
  });

  const [deleteInfluencer, { loading: deleteLoading }] = useMutation(DELETE_INFLUENCER, {
    onCompleted: () => {
      // ✅ MANTER: Delete realmente precisa invalidar para remover da lista
      client.refetchQueries({
        include: ['GetInfluencers', 'GetDashboardData']
      });
    }
  });

  const [syncFinancialData, { loading: syncLoading }] = useMutation(SYNC_FINANCIAL_DATA, {
    onCompleted: () => {
      client.refetchQueries({
        include: ['GetInfluencerDetails', 'GetInfluencers']
      });
    }
  });

  const [clearCache, { loading: clearLoading }] = useMutation(CLEAR_CACHE);

  const [createInfluencerPricing, { loading: createPricingLoading }] = useMutation(CREATE_INFLUENCER_PRICING);

  const [createAudienceDemographic, { loading: createDemographicLoading }] = useMutation(CREATE_AUDIENCE_DEMOGRAPHIC);

  return {
    createInfluencer: useCallback(async (input: any) => {
      try {
        const result = await createInfluencer({ variables: { input } });
        return result.data?.createInfluencer;
      } catch (error) {
        console.error('Erro ao criar influenciador:', error);
        throw error;
      }
    }, [createInfluencer]),

    updateInfluencer: useCallback(async (id: string, input: any) => {
      try {
        console.log('🚀 [FUNÇÃO 1 - use-graphql-influencers.ts] === INICIO updateInfluencer ===');
        console.log('🚀 [FUNÇÃO 1] ID recebido:', id);
        console.log('🚀 [FUNÇÃO 1] Input COMPLETO recebido:', JSON.stringify(input, null, 2));

        // 🔥 REFATORAÇÃO: Deixar o GraphQL Schema validar os campos
        // Apenas remover campos técnicos que causam problemas
        const cleanInput = { ...input };

        // Remover campos técnicos problemáticos (incluindo dentro de arrays)
        const technicalFields = ['id', '__typename', 'createdAt', 'updatedAt', 'currentPricing', 'currentDemographics'];
        technicalFields.forEach(field => {
          if (cleanInput.hasOwnProperty(field)) {
            console.log(`🧹 [GraphQL Hook] Removendo campo técnico: ${field}`);
            delete cleanInput[field];
          }
        });

        // 🔥 CORREÇÃO ESPECÍFICA: Limpar __typename dos arrays audienceLocations e audienceCities
        console.log(`🔍 [FUNÇÃO 1] Verificando audienceLocations:`, cleanInput.audienceLocations);
        if (cleanInput.audienceLocations && Array.isArray(cleanInput.audienceLocations)) {
          console.log(`🚨 [FUNÇÃO 1] ENCONTROU audienceLocations com ${cleanInput.audienceLocations.length} itens`);
          cleanInput.audienceLocations = cleanInput.audienceLocations.map((item: any) => {
            const cleanItem = { ...item };
            console.log(`🗑️ [FUNÇÃO 1] ANTES da limpeza:`, item);
            delete cleanItem.__typename;
            console.log(`✅ [FUNÇÃO 1] DEPOIS da limpeza:`, cleanItem);
            return cleanItem;
          });
        }

        if (cleanInput.audienceCities && Array.isArray(cleanInput.audienceCities)) {
          cleanInput.audienceCities = cleanInput.audienceCities.map((item: any) => {
            const cleanItem = { ...item };
            delete cleanItem.__typename;
            console.log(`🧹 [GraphQL Hook] Removendo __typename de audienceCities:`, cleanItem);
            return cleanItem;
          });
        }

        console.log('🔧 [GraphQL Hook] Input LIMPO final:', JSON.stringify(cleanInput, null, 2));
        console.log('🔧 [GraphQL Hook] Mutation variables:', { id, input: cleanInput });

        // 🔧 CORREÇÃO: Evitar invalidação agressiva do cache
        // Removido: client.cache.evict({ fieldName: 'influencers' }); 
        // Isso estava causando perda de todos os influenciadores do grid

        const result = await updateInfluencer({ variables: { id, input: cleanInput } });

        console.log('✅ [GraphQL Hook] Mutation executada com sucesso');
        console.log('✅ [GraphQL Hook] Resultado:', result.data?.updateInfluencer);

        return result.data?.updateInfluencer;
      } catch (error) {
        console.error('❌ [GraphQL Hook] Erro COMPLETO ao atualizar influenciador:', error);
        console.error('❌ [GraphQL Hook] Stack trace:', error instanceof Error ? error.stack : 'Sem stack trace');
        throw error;
      }
    }, [updateInfluencer, client]),

    deleteInfluencer: useCallback(async (id: string) => {
      try {
        const result = await deleteInfluencer({ variables: { id } });
        return result.data?.deleteInfluencer;
      } catch (error) {
        console.error('Erro ao deletar influenciador:', error);
        throw error;
      }
    }, [deleteInfluencer]),

    syncFinancialData: useCallback(async (influencerId: string) => {
      try {
        const result = await syncFinancialData({ variables: { influencerId } });
        return result.data?.syncInfluencerFinancialData;
      } catch (error) {
        console.error('Erro ao sincronizar dados financeiros:', error);
        throw error;
      }
    }, [syncFinancialData]),

    clearCache: useCallback(async () => {
      try {
        const result = await clearCache();
        return result.data?.clearCache;
      } catch (error) {
        console.error('Erro ao limpar cache:', error);
        throw error;
      }
    }, [clearCache]),

    createInfluencerPricing: useCallback(async (input: any) => {
      try {
        console.log('💰 [GraphQL Hook] Criando pricing:', input);
        const result = await createInfluencerPricing({ variables: { input } });
        console.log('✅ [GraphQL Hook] Pricing criado:', result.data?.createInfluencerPricing);
        return result.data?.createInfluencerPricing;
      } catch (error) {
        console.error('❌ [GraphQL Hook] Erro ao criar pricing:', error);
        throw error;
      }
    }, [createInfluencerPricing]),

    createAudienceDemographic: useCallback(async (input: any) => {
      try {
        console.log('📊 [GraphQL Hook] Criando demographic:', input);
        console.log('🎯 [GraphQL Hook] audienceAgeRange original:', input.audienceAgeRange);

        // Limpar __typename de todos os arrays do input
        const cleanInput = { ...input };

        // Limpar e validar audienceLocations
        if (cleanInput.audienceLocations && Array.isArray(cleanInput.audienceLocations)) {
          cleanInput.audienceLocations = cleanInput.audienceLocations
            .map((item: any) => {
              const { __typename, ...cleanItem } = item;
              return cleanItem;
            })
            .filter((item: any) => {
              const isValid = item.country &&
                item.country.trim() !== '' &&
                typeof item.percentage === 'number' &&
                !isNaN(item.percentage) &&
                item.percentage >= 0;
              if (!isValid) {
                console.log('❌ [GraphQL Hook] audienceLocation inválido filtrado:', item);
              }
              return isValid;
            });
        }

        // Limpar, mapear e validar audienceCities (location -> city)
        if (cleanInput.audienceCities && Array.isArray(cleanInput.audienceCities)) {
          cleanInput.audienceCities = cleanInput.audienceCities
            .map((item: any) => {
              const { __typename, location, ...rest } = item;
              // Mapear 'location' para 'city' se existir
              return {
                city: location || item.city,
                percentage: item.percentage,
                ...rest
              };
            })
            .filter((item: any) => {
              const isValid = item.city &&
                item.city.trim() !== '' &&
                typeof item.percentage === 'number' &&
                !isNaN(item.percentage) &&
                item.percentage >= 0;
              if (!isValid) {
                console.log('❌ [GraphQL Hook] audienceCity inválido filtrado:', item);
              }
              return isValid;
            });
        }

        // Limpar e validar audienceAgeRange
        if (cleanInput.audienceAgeRange && Array.isArray(cleanInput.audienceAgeRange)) {
          console.log('🧹 [GraphQL Hook] Limpando audienceAgeRange, itens antes:', cleanInput.audienceAgeRange);
          cleanInput.audienceAgeRange = cleanInput.audienceAgeRange
            .map((item: any) => {
              const { __typename, ...cleanItem } = item;
              return cleanItem;
            })
            .filter((item: any) => {
              // Filtrar apenas itens com range e percentage válidos
              const isValid = item.range &&
                item.range.trim() !== '' &&
                typeof item.percentage === 'number' &&
                !isNaN(item.percentage) &&
                item.percentage >= 0;

              if (!isValid) {
                console.log('❌ [GraphQL Hook] Item inválido filtrado:', item);
              }
              return isValid;
            });
          console.log('🧹 [GraphQL Hook] audienceAgeRange após limpeza e validação:', cleanInput.audienceAgeRange);
        }

        // Limpar audienceGender se necessário
        if (cleanInput.audienceGender && cleanInput.audienceGender.__typename) {
          const { __typename, ...cleanGender } = cleanInput.audienceGender;
          cleanInput.audienceGender = cleanGender;
        }

        console.log('🧹 [GraphQL Hook] Input limpo para demographic:', cleanInput);
        console.log('🎯 [GraphQL Hook] audienceAgeRange final:', cleanInput.audienceAgeRange);

        const result = await createAudienceDemographic({ variables: { input: cleanInput } });
        console.log('✅ [GraphQL Hook] Demographic criado:', result.data?.createAudienceDemographic);
        return result.data?.createAudienceDemographic;
      } catch (error) {
        console.error('❌ [GraphQL Hook] Erro ao criar demographic:', error);
        throw error;
      }
    }, [createAudienceDemographic]),

    loading: {
      create: createLoading,
      update: updateLoading,
      delete: deleteLoading,
      sync: syncLoading,
      clear: clearLoading,
      createPricing: createPricingLoading,
      createDemographic: createDemographicLoading
    }
  };
}

/**
 * Hook para pesquisa avançada com debounce
 */
export function useInfluencerSearch(userId: string, initialFilters: InfluencerFilters = {}) {
  const [filters, setFilters] = useState<InfluencerFilters>(initialFilters);
  const [debouncedFilters, setDebouncedFilters] = useState<InfluencerFilters>(initialFilters);

  // Debounce dos filtros
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 300);

    return () => clearTimeout(timer);
  }, [filters]);

  const influencersQuery = useInfluencers({
    userId,
    filters: debouncedFilters,
    enabled: true
  });

  const updateFilters = useCallback((newFilters: Partial<InfluencerFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  return {
    ...influencersQuery,
    filters,
    updateFilters,
    clearFilters,
    isSearching: filters !== debouncedFilters
  };
}

// ===== HOOKS CUSTOMIZADOS =====

// Hook para buscar categorias
export function useCategories(userId?: string) {
 

  const { data, loading, error, refetch } = useQuery(GET_CATEGORIES, {
    variables: { userId }, // 🔄 CORREÇÃO: Passar userId para incluir categorias do usuário
    errorPolicy: 'all'
  });



  return {
    categories: data?.categories || [],
    loading,
    error,
    refetch
  };
}

// Hook para buscar marcas
export function useBrandsGraphQL(userId: string) {
  const { data, loading, error, refetch } = useQuery(GET_BRANDS, {
    variables: { userId },
    skip: !userId,
    errorPolicy: 'all'
  });

  return {
    brands: data?.brands || [],
    loading,
    error,
    refetch
  };
}

// Hook para buscar associações marca-influenciador
export function useBrandInfluencerAssociations(userId: string, options?: { influencerId?: string; brandId?: string }) {
  const { data, loading, error, refetch } = useQuery(GET_BRAND_INFLUENCER_ASSOCIATIONS, {
    variables: {
      userId,
      influencerId: options?.influencerId || null,
      brandId: options?.brandId || null
    },
    skip: !userId,
    errorPolicy: 'all'
  });

  return {
    associations: data?.brandInfluencerAssociations || [],
    loading,
    error,
    refetch
  };
}

// Hook para mutations de associações
export function useBrandInfluencerMutations() {
  const [createAssociation] = useMutation(CREATE_BRAND_INFLUENCER_ASSOCIATION);
  const [deleteAssociation] = useMutation(DELETE_BRAND_INFLUENCER_ASSOCIATION);

  const createBrandInfluencerAssociation = useCallback(async (input: any) => {
    try {
      console.log('[GraphQL] Criando associação marca-influenciador:', input);

      const result = await createAssociation({
        variables: { input },
        refetchQueries: [GET_BRAND_INFLUENCER_ASSOCIATIONS]
      });

      console.log('[GraphQL] Associação criada com sucesso:', result.data?.createBrandInfluencerAssociation);
      return result.data?.createBrandInfluencerAssociation;
    } catch (error: any) {
      console.error('[GraphQL] Erro ao criar associação:', error);

      // Se a associação já existe, retornar como sucesso com informação de que já existia
      if (error.message && (
        error.message.includes('já existe') ||
        error.message.includes('already exists') ||
        error.message.includes('duplicate')
      )) {
        console.log('ℹ️ [GraphQL] Associação já existe - retornando como sucesso:', {
          brandId: input.brandId,
          influencerId: input.influencerId
        });

        return {
          id: `existing-${input.brandId}-${input.influencerId}`,
          brandId: input.brandId,
          influencerId: input.influencerId,
          status: input.status || 'active',
          __isExisting: true
        };
      }

      throw error;
    }
  }, [createAssociation]);

  const deleteBrandInfluencerAssociation = useCallback(async (id: string) => {
    try {
      console.log('[GraphQL] Deletando associação marca-influenciador:', id);

      const result = await deleteAssociation({
        variables: { id },
        refetchQueries: [GET_BRAND_INFLUENCER_ASSOCIATIONS]
      });

      console.log('[GraphQL] Associação deletada com sucesso');
      return result.data?.deleteBrandInfluencerAssociation;
    } catch (error) {
      console.error('[GraphQL] Erro ao deletar associação:', error);
      throw error;
    }
  }, [deleteAssociation]);

  return {
    createBrandInfluencerAssociation,
    deleteBrandInfluencerAssociation
  };
}

// Hook para mutations de marcas
export function useBrandMutations() {
  const client = useApolloClient();
  const [createBrand] = useMutation(CREATE_BRAND, {
    refetchQueries: [GET_BRANDS]
  });

  const [updateBrand] = useMutation(UPDATE_BRAND, {
    refetchQueries: [GET_BRANDS]
  });

  const [deleteBrand] = useMutation(DELETE_BRAND, {
    refetchQueries: [GET_BRANDS]
  });

  const createBrandMutation = useCallback(async (input: any) => {
    try {
      console.log('[GraphQL] Criando marca:', input);

      // Limpar campos técnicos que não devem ser enviados (mas manter userId)
      const cleanInput = { ...input };
      const technicalFields = ['id', 'createdAt', 'updatedAt', '__typename'];
      technicalFields.forEach(field => {
        if (cleanInput.hasOwnProperty(field)) {
          console.log(`🧹 [GraphQL] Removendo campo técnico: ${field}`);
          delete cleanInput[field];
        }
      });

      // Garantir que campos obrigatórios tenham valores válidos
      if (!cleanInput.name || cleanInput.name.trim() === '') {
        throw new Error('Nome da marca é obrigatório');
      }

      // ✅ CORREÇÃO: Garantir que userId esteja presente (usando context do Apollo ou fallback)
      if (!cleanInput.userId) {
        // Tentar obter do contexto do Apollo Client ou de alguma fonte confiável
        // Por agora, vamos usar o client para tentar obter userId do cache/context
        const cache = client.cache;
        // Se não tiver userId no input, isso é um erro crítico
        throw new Error('UserID é obrigatório para criar marca');
      }

      console.log('[GraphQL] Input limpo para create:', cleanInput);

      const result = await createBrand({
        variables: { input: cleanInput }
      });

      console.log('[GraphQL] Marca criada com sucesso:', result.data?.createBrand);
      return result.data?.createBrand;
    } catch (error) {
      console.error('[GraphQL] Erro ao criar marca:', error);
      throw error;
    }
  }, [createBrand]);

  const updateBrandMutation = useCallback(async (id: string, input: any) => {
    try {
      console.log('[GraphQL] Atualizando marca:', id, input);

      // Limpar campos técnicos que não devem ser enviados
      const cleanInput = { ...input };
      const technicalFields = ['id', 'createdAt', 'updatedAt', '__typename'];
      technicalFields.forEach(field => {
        if (cleanInput.hasOwnProperty(field)) {
          console.log(`🧹 [GraphQL] Removendo campo técnico: ${field}`);
          delete cleanInput[field];
        }
      });

      // Garantir que campos obrigatórios tenham valores válidos
      if (cleanInput.status === null || cleanInput.status === undefined) {
        cleanInput.status = 'active';
      }

      console.log('[GraphQL] Input limpo para update:', cleanInput);

      const result = await updateBrand({
        variables: { id, input: cleanInput }
      });

      console.log('[GraphQL] Marca atualizada com sucesso:', result.data?.updateBrand);
      return result.data?.updateBrand;
    } catch (error) {
      console.error('[GraphQL] Erro ao atualizar marca:', error);
      throw error;
    }
  }, [updateBrand]);

  const deleteBrandMutation = useCallback(async (id: string) => {
    try {
      console.log('[GraphQL] Deletando marca:', id);

      await deleteBrand({
        variables: { id }
      });

      console.log('[GraphQL] Marca deletada com sucesso');
      return true;
    } catch (error) {
      console.error('[GraphQL] Erro ao deletar marca:', error);
      throw error;
    }
  }, [deleteBrand]);

  return {
    createBrand: createBrandMutation,
    updateBrand: updateBrandMutation,
    deleteBrand: deleteBrandMutation
  };
}

// Hook para buscar estatísticas
export function useStatsGraphQL(userId: string) {
  const { data, loading, error, refetch } = useQuery(GET_STATS, {
    variables: { userId },
    skip: !userId,
    errorPolicy: 'all'
  });

  return {
    stats: data?.stats || {
      totalInfluencers: 0,
      totalViews: 0,
      totalFollowers: 0,
      totalBrands: 0,
      totalCampaigns: 0
    },
    loading,
    error,
    refetch
  };
}

// Hook para buscar filtros salvos
export function useSavedFiltersGraphQL(userId: string) {
  const { data, loading, error, refetch } = useQuery(GET_SAVED_FILTERS, {
    variables: { userId },
    skip: !userId,
    errorPolicy: 'all'
  });

  const [createSavedFilter] = useMutation(CREATE_SAVED_FILTER, {
    refetchQueries: [{ query: GET_SAVED_FILTERS, variables: { userId } }]
  });

  const [updateSavedFilter] = useMutation(UPDATE_SAVED_FILTER, {
    refetchQueries: [{ query: GET_SAVED_FILTERS, variables: { userId } }]
  });

  const [deleteSavedFilter] = useMutation(DELETE_SAVED_FILTER, {
    refetchQueries: [{ query: GET_SAVED_FILTERS, variables: { userId } }]
  });

  return {
    savedFilters: data?.savedFilters || [],
    loading,
    error,
    refetch,
    createSavedFilter: async (input: any) => {
      return await createSavedFilter({ variables: { input } });
    },
    updateSavedFilter: async (id: string, input: any) => {
      return await updateSavedFilter({ variables: { id, input } });
    },
    deleteSavedFilter: async (id: string) => {
      return await deleteSavedFilter({ variables: { id } });
    }
  };
}

/**
 * Hook para buscar influenciadores de uma proposta via GraphQL
 */
export function useProposalInfluencers(influencerIds: string[], userId: string, proposalId?: string) {
  const queryResult = useQuery(GET_PROPOSAL_INFLUENCERS, {
    variables: { influencerIds, userId, proposalId },
    skip: !influencerIds || influencerIds.length === 0 || !userId,
    errorPolicy: 'all',
    fetchPolicy: 'cache-first'
  });

  // Processar a resposta para extrair os influenciadores da estrutura complexa
  const processedData = {
    ...queryResult,
    data: queryResult.data ? {
      ...queryResult.data,
      influencersByIds: queryResult.data.influencersByIds?.influencers || []
    } : null,
    metadata: queryResult.data?.influencersByIds ? {
      foundIds: queryResult.data.influencersByIds.foundIds || [],
      notFoundIds: queryResult.data.influencersByIds.notFoundIds || [],
      totalFound: queryResult.data.influencersByIds.totalFound || 0,
      totalRequested: queryResult.data.influencersByIds.totalRequested || 0,
      processingTimeMs: queryResult.data.influencersByIds.processingTimeMs || 0,
      hasPartialFailure: queryResult.data.influencersByIds.hasPartialFailure || false,
      errors: queryResult.data.influencersByIds.errors || []
    } : null
  };

  return processedData;
}

export default {
  useInfluencers,
  useInfluencerDetails,
  useInfluencersByPriceRange,
  useDashboardData,
  useCacheStats,
  useInfluencerMutations,
  useInfluencerSearch,
  useCategories,
  useBrandsGraphQL,
  useStatsGraphQL,
  useSavedFiltersGraphQL,
  useProposalInfluencers
};

