import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';

/**
 * 👥 API para buscar membros de uma organização do Clerk
 * 
 * GET /api/organizations/members?organizationId=org_xxx
 * 
 * ✨ FUNCIONALIDADES:
 * - Busca todos os membros da organização
 * - Retorna dados formatados para uso no frontend
 * - Inclui informações de role e status
 * - Validação de permissões de acesso
 */

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Obter organizationId da query string
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'organizationId é obrigatório' },
        { status: 400 }
      );
    }

    console.log('👥 [ORG_MEMBERS] Buscando membros da organização:', {
      organizationId,
      requestedBy: userId
    });

    // Verificar se o usuário tem acesso à organização
    const client = await clerkClient();
    
    // Verificar se o usuário é membro da organização
    const userMemberships = await client.users.getOrganizationMembershipList({
      userId: userId
    });

    const hasAccess = userMemberships.data.some(
      membership => membership.organization.id === organizationId
    );

    if (!hasAccess) {
      console.warn('⚠️ [ORG_MEMBERS] Usuário sem acesso à organização:', {
        userId,
        organizationId
      });
      return NextResponse.json(
        { error: 'Acesso negado à organização' },
        { status: 403 }
      );
    }

    // Buscar membros da organização
    const memberships = await client.organizations.getOrganizationMembershipList({
      organizationId: organizationId,
      limit: 100 // Ajustar conforme necessário
    });

    console.log('✅ [ORG_MEMBERS] Membros encontrados:', memberships.data.length);

    // Formatar dados dos membros
    const members = memberships.data.map(membership => ({
      userId: membership.publicUserData.userId,
      name: `${membership.publicUserData.firstName || ''} ${membership.publicUserData.lastName || ''}`.trim() || 
            membership.publicUserData.identifier || 
            'Usuário',
      email: membership.publicUserData.identifier || '',
      imageUrl: membership.publicUserData.imageUrl || '',
      role: membership.role,
      status: 'active', // Clerk não tem status específico, assumir ativo
      joinedAt: membership.createdAt,
      // Dados adicionais do Clerk
      clerkData: {
        firstName: membership.publicUserData.firstName,
        lastName: membership.publicUserData.lastName,
        identifier: membership.publicUserData.identifier,
        hasImage: !!membership.publicUserData.imageUrl
      }
    }));

    // Estatísticas dos membros
    const stats = {
      total: members.length,
      adminCount: members.filter(m => m.role === 'org:admin').length,
      memberCount: members.filter(m => m.role === 'org:member').length,
      roles: members.reduce((acc, member) => {
        acc[member.role] = (acc[member.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    console.log('📊 [ORG_MEMBERS] Estatísticas:', stats);

    return NextResponse.json({
      success: true,
      members,
      stats,
      organizationId,
      total: members.length
    });

  } catch (error) {
    console.error('❌ [ORG_MEMBERS] Erro ao buscar membros:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

/**
 * 📝 Tipos para referência (não exportados, apenas documentação)
 */
interface OrganizationMember {
  userId: string;
  name: string;
  email: string;
  imageUrl?: string;
  role: string;
  status: string;
  joinedAt: number;
  clerkData: {
    firstName?: string;
    lastName?: string;
    identifier: string;
    hasImage: boolean;
  };
}

interface MemberStats {
  total: number;
  adminCount: number;
  memberCount: number;
  roles: Record<string, number>;
}
