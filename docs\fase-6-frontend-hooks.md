# 📅 FASE 6: Frontend e Hooks (2-3 dias)

## 🎯 Objetivo
Atualizar todos os componentes e hooks para trabalhar com isolamento automático.

## 📝 Tarefas Principais

### 6.1 Hooks Atualizados

#### Hook para Brands
```typescript
// hooks/use-brands.ts
export function useBrands() {
  const { currentUser } = useAuth();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) {
      setBrands([]);
      setLoading(false);
      return;
    }

    const loadBrands = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/brands');
        if (!response.ok) throw new Error('Erro ao carregar marcas');
        
        const result = await response.json();
        setBrands(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    loadBrands();
  }, [currentUser]);

  const createBrand = async (data: CreateBrandData) => {
    const response = await fetch('/api/brands', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) throw new Error('Erro ao criar marca');
    
    const result = await response.json();
    setBrands(prev => [...prev, result.data]);
    return result.data;
  };

  return { brands, loading, error, createBrand };
}
```

### 6.2 Context Providers Atualizados

#### Data Context
```typescript
// contexts/data-context.tsx
interface DataContextType {
  brands: Brand[];
  campaigns: Campaign[];
  proposals: Proposal[];
  refreshData: () => Promise<void>;
  loading: boolean;
}

export function DataProvider({ children }: { children: React.ReactNode }) {
  const { currentUser } = useAuth();
  const [data, setData] = useState({
    brands: [],
    campaigns: [],
    proposals: []
  });
  const [loading, setLoading] = useState(false);

  const refreshData = useCallback(async () => {
    if (!currentUser) return;
    
    setLoading(true);
    try {
      const [brandsRes, campaignsRes, proposalsRes] = await Promise.all([
        fetch('/api/brands'),
        fetch('/api/campaigns'),
        fetch('/api/proposals')
      ]);

      const [brands, campaigns, proposals] = await Promise.all([
        brandsRes.json(),
        campaignsRes.json(),
        proposalsRes.json()
      ]);

      setData({
        brands: brands.data || [],
        campaigns: campaigns.data || [],
        proposals: proposals.data || []
      });
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    refreshData();
  }, [refreshData]);

  return (
    <DataContext.Provider value={{
      ...data,
      refreshData,
      loading
    }}>
      {children}
    </DataContext.Provider>
  );
}
```

### 6.3 Componentes Atualizados

#### Brand Manager
```typescript
// components/brand-manager.tsx
export function BrandManager() {
  const { brands, loading, error, createBrand } = useBrands();
  
  if (!brands) {
    return <div>Carregando marcas...</div>;
  }

  if (error) {
    return <div>Erro: {error}</div>;
  }

  return (
    <div className="space-y-4">
      <h2>Minhas Marcas</h2>
      
      {brands.map(brand => (
        <div key={brand.id} className="border p-4 rounded">
          <h3>{brand.name}</h3>
          <p>{brand.industry}</p>
        </div>
      ))}
      
      <CreateBrandDialog onCreateBrand={createBrand} />
    </div>
  );
}
```

## 📊 Deliverables

- [x] Hooks com isolamento automático
- [x] Context providers atualizados
- [x] Componentes usando novos hooks
- [x] Estados de loading e erro
- [x] Cache automático de dados

---

# 📅 FASE 7: Testes e Validação (2-3 dias)

## 🎯 Objetivo
Garantir que o isolamento funciona corretamente e não há vazamentos de dados.

## 📝 Tarefas Principais

### 7.1 Testes de Segurança
```typescript
// tests/security.test.ts
describe('Isolamento de Dados', () => {
  test('Usuário não pode acessar dados de outro usuário', async () => {
    // Criar dois usuários
    const user1 = await createTestUser();
    const user2 = await createTestUser();
    
    // User1 cria uma marca
    const brand = await createBrand(user1.id);
    
    // User2 tenta acessar a marca do User1
    const response = await request(app)
      .get(`/api/brands/${brand.id}`)
      .set('Authorization', `Bearer ${user2.token}`);
    
    expect(response.status).toBe(403);
  });
});
```

### 7.2 Testes de Performance
```typescript
// tests/performance.test.ts
describe('Performance com Isolamento', () => {
  test('Consultas com filtro por userId são rápidas', async () => {
    const start = Date.now();
    
    const brands = await BrandService.getBrandsByUser(userId);
    
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(500); // Menos de 500ms
  });
});
```

## 📊 Deliverables

- [x] Suite de testes de segurança
- [x] Testes de performance
- [x] Testes de funcionalidade
- [x] Relatório de cobertura

---

# 📅 FASE 8: Deploy e Monitoramento (1-2 dias)

## 🎯 Objetivo
Implementar em produção com monitoramento ativo.

## 📝 Tarefas Principais

### 8.1 Deploy Gradual
```typescript
// scripts/deploy-with-feature-flag.ts
export async function deployWithFeatureFlag() {
  // 1. Deploy do código com feature flag desabilitada
  await deployCode();
  
  // 2. Executar migração de dados
  await runMigration();
  
  // 3. Habilitar feature flag gradualmente
  await enableFeatureFlag(0.1); // 10% dos usuários
  await wait(1000 * 60 * 30); // Aguardar 30min
  
  if (await checkMetrics()) {
    await enableFeatureFlag(1.0); // 100% dos usuários
  }
}
```

### 8.2 Monitoramento
```typescript
// lib/monitoring.ts
export function logDataAccess(userId: string, resource: string, action: string) {
  console.log(`[AUDIT] ${userId} ${action} ${resource} at ${new Date().toISOString()}`);
  
  // Enviar para serviço de monitoramento
  analytics.track('data_access', {
    userId,
    resource,
    action,
    timestamp: new Date()
  });
}
```

## 📊 Deliverables

- [x] Sistema em produção
- [x] Monitoramento ativo
- [x] Alertas configurados
- [x] Dashboard de métricas

## ✅ Resumo Final

### Total de Fases: 8
### Duração Estimada: 12-20 dias úteis
### Status: ✅ Documentação Completa

**Próximo Passo**: Iniciar Fase 1 - Auditoria e Preparação 