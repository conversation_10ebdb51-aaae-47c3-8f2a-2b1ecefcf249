/**
 * Script executável para configurar as coleções do Firebase
 * Execute com: node scripts/run-setup.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Iniciando configuração do Firebase...');
console.log('=' .repeat(60));

try {
  // Compilar o TypeScript e executar
  console.log('📦 Compilando TypeScript...');
  
  const setupPath = path.join(__dirname, 'setup-firebase-collections.ts');
  
  // Usar ts-node para executar diretamente
  console.log('⚡ Executando configuração...');
  
  execSync(`npx ts-node "${setupPath}"`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('\n✅ Configuração concluída!');
  
} catch (error) {
  console.error('\n❌ Erro durante a execução:', error.message);
  process.exit(1);
}
