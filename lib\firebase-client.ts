// Firebase Client Configuration
// lib/firebase-client.ts
// Para uso em Client Components

import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore, connectFirestoreEmulator, enableIndexedDbPersistence, enableNetwork, disableNetwork } from 'firebase/firestore';
import { getStorage, FirebaseStorage, connectStorageEmulator } from 'firebase/storage';
import { getAnalytics } from 'firebase/analytics';

// ✅ CONFIGURAÇÃO: Firebase config com variáveis de ambiente
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "AIzaSyDhSkFQHdDLke6drWlVJzE3Zys6CJx_arQ",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "deumatch-demo.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "deumatch-demo",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "deumatch-demo.firebasestorage.app",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "306980252445",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:306980252445:web:e6e275cde8b9b9668e0ff4",
};

// ✅ VALIDAÇÃO: Verificar configurações obrigatórias
const requiredConfig = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
const missingConfig = requiredConfig.filter(key => !firebaseConfig[key as keyof typeof firebaseConfig]);

if (missingConfig.length > 0) {
  // ✅ CORREÇÃO: Não tentar inicializar Firebase com configurações inválidas
  throw new Error('❌ Firebase não pode ser inicializado: variáveis de ambiente ausentes. Consulte firebase-config-example.md');
}

// ✅ DEBUG: Log configurações carregadas (sem expor chaves sensíveis)
// ✅ INICIALIZAÇÃO: Firebase App
let app: FirebaseApp;
try {
  app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
  } catch (error) {
  if (error instanceof Error) {
    if (error.message.includes('auth/invalid-api-key')) {
      // Invalid API key error
    } else if (error.message.includes('auth/invalid-app-credential')) {
      // Invalid app credential error
    } else if (error.message.includes('network')) {
      // Network error
    }
  }
  
  throw new Error('❌ Firebase falhou ao inicializar. Verifique a configuração.');
}

// ✅ SERVIÇOS: Inicializar serviços Firebase
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// const auth: Auth = getAuth(app);
const clientDb: Firestore = getFirestore(app);
const storage: FirebaseStorage = getStorage(app);

// ✅ PERSISTÊNCIA: Configurar offline para Firestore usando a nova API
if (typeof window !== 'undefined') {
  // Usar a nova configuração de cache do Firestore
  import('firebase/firestore').then(({ initializeFirestore, persistentLocalCache, persistentMultipleTabManager }) => {
    try {
      // Tentar habilitar cache persistente
      const firestoreWithCache = initializeFirestore(app, {
        localCache: persistentLocalCache({
          tabManager: persistentMultipleTabManager()
        })
      });
    } catch (error: any) {
      if (error.code === 'failed-precondition') {
        // Multiple tabs already open
      } else if (error.code === 'unimplemented') {
        // Not supported in this browser
      } else {
        // Fallback para cache em memória
        import('firebase/firestore').then(({ memoryLocalCache }) => {
          try {
            initializeFirestore(app, {
              localCache: memoryLocalCache()
            });
            } catch (fallbackError) {
              // Cache initialization failed
            }
        });
      }
    }
  }).catch(() => {
    // Fallback para método antigo se a nova API não estiver disponível
    enableIndexedDbPersistence(clientDb, {
      forceOwnership: false
    }).then(() => {
      // Persistence enabled successfully
    }).catch((error) => {
      // Persistence failed, continue without it
    });
  });

  // ✅ CONECTIVIDADE: Monitorar estado da conexão
  window.addEventListener('online', async () => {
    try {
      await enableNetwork(clientDb);
      } catch (error) {
        // Network enable failed
      }
  });

  window.addEventListener('offline', async () => {
    try {
      await disableNetwork(clientDb);
      } catch (error) {
        // Network disable failed
      }
  });
}

// ✅ DESENVOLVIMENTO: Conectar emuladores se configurado
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const useEmulators = process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true';
 
}

// ✅ ANALYTICS: Inicializar apenas se suportado
let analytics: any;
if (typeof window !== 'undefined') {
  import('firebase/analytics').then(async ({ isSupported }) => {
    try {
      const analyticsSupported = await isSupported();
      if (analyticsSupported) {
        analytics = getAnalytics(app);
      }
    } catch (error) {
      // Analytics initialization failed
    }
  }).catch(() => {
    // Analytics não é crítico, ignorar erro
  });
}

// ✅ CORREÇÃO: Adicionar monitoramento de erros de CSP para Firebase Analytics
if (typeof window !== 'undefined') {
  // Detectar violações de CSP específicas do Firebase
  window.addEventListener('securitypolicyviolation', (e) => {
    if (e.blockedURI?.includes('firebase.googleapis.com')) {
      // Tentar uma reinicialização alternativa do Firebase Analytics
      // Aguardar um pouco antes de tentar novamente
      setTimeout(() => {
                 try {
           if ((window as any).gtag && typeof (window as any).gtag === 'function') {
             // Analytics retry
           }
        } catch (retryError) {
          // Retry failed
        }
      }, 2000);
    }
  });
  
  // Detectar erros gerais do Firebase
  window.addEventListener('error', (e) => {
    if (e.error?.message?.includes('firebase') || e.error?.message?.includes('Firebase')) {
      // Firebase error detected
    }
  });
  
  // Detectar erros de promises rejeitadas do Firebase
  window.addEventListener('unhandledrejection', (e) => {
    if (e.reason?.message?.includes('firebase') || e.reason?.message?.includes('Firebase')) {
      // Não impedir o comportamento padrão para manter compatibilidade
    }
  });
}

// ✅ UTILITÁRIOS: Funções auxiliares
export const testFirebaseConnection = async (): Promise<boolean> => {
  try {
    const { doc, getDoc } = await import('firebase/firestore');
    const testDoc = doc(clientDb, 'test', 'connection');
    await getDoc(testDoc);
    return true;
  } catch (error) {
    return false;
  }
};

// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
export const getTokenWithRetry = async (forceRefresh = false): Promise<string | null> => {
  return null;
};

// ✅ EXPORTAÇÕES: Centralizadas e tipadas
export { app };
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// export { auth };
export { clientDb };
export { storage };
export { analytics };

// ✅ COMPATIBILIDADE: Exports alternativos
export { clientDb as firestore };
export { clientDb as db };
export default app;

