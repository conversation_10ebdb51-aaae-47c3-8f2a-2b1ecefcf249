{"indexes": [{"collectionGroup": "influencer_pricing", "queryScope": "COLLECTION", "fields": [{"fieldPath": "influencerId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "validFrom", "order": "DESCENDING"}]}, {"collectionGroup": "audience_demographics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "influencerId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "captureDate", "order": "DESCENDING"}]}, {"collectionGroup": "audience_demographics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "influencerId", "order": "ASCENDING"}, {"fieldPath": "platform", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "captureDate", "order": "DESCENDING"}]}, {"collectionGroup": "influencers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "list_items", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "listId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "criadoPor", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "brands", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "brand_influencers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "brandId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "brand_influencers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "influencerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "brand_influencers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "proposals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "brandId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "proposals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "brandId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "proposals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "brandId", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}