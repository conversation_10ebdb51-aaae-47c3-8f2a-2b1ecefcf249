import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';

interface Params {
  id: string;
  influencerId: string;
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<Params> }
) {
  try {
    const { id: proposalId, influencerId } = await params;

    console.log('🗑️ [API] Removendo influenciador da proposta:', {
      proposalId,
      influencerId
    });

    // Validações
    if (!proposalId) {
      return NextResponse.json(
        { error: 'ID da proposta é obrigatório' },
        { status: 400 }
      );
    }

    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    // Verificar autorização do token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Token de autorização é obrigatório' },
        { status: 401 }
      );
    }

    // Remover influenciador da proposta
    await ProposalService.removeInfluencerFromProposal(
      proposalId,
      influencerId
    );

    console.log('✅ [API] Influenciador removido com sucesso da proposta:', {
      proposalId,
      influencerId
    });

    return NextResponse.json({
      success: true,
      message: 'Influenciador removido da proposta com sucesso',
      proposalId,
      influencerId
    });

  } catch (error) {
    console.error('❌ [API] Erro ao remover influenciador da proposta:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
} 