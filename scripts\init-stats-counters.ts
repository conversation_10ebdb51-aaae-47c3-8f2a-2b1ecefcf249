import { db } from '../lib/firebase-admin';

/**
 * Script para inicializar contadores de estatísticas para usuários existentes
 * Executa uma única vez para migrar dados existentes
 */
async function initStatsCounters() {
  console.log('🚀 Iniciando inicialização dos contadores de estatísticas...');
  
  try {
    // Buscar todos os usuários únicos das coleções
    const [influencersSnapshot, brandsSnapshot] = await Promise.all([
      db.collection('influencers').get(),
      db.collection('brands').get()
    ]);
    
    // Agrupar por usuário
    const userStats: { [userId: string]: any } = {};
    
    // Processar influenciadores
    console.log('📊 Processando influenciadores...');
    influencersSnapshot.docs.forEach(doc => {
      const data = doc.data();
      const userId = data.userId;
      
      if (!userId) return;
      
      if (!userStats[userId]) {
        userStats[userId] = {
          totalInfluencers: 0,
          totalViews: 0,
          totalFollowers: 0,
          totalBrands: 0,
          totalCampaigns: 0
        };
      }
      
      userStats[userId].totalInfluencers++;
      userStats[userId].totalFollowers += data.totalFollowers || 0;
      userStats[userId].totalViews += data.totalViews || data.totalFollowers || 0;
    });
    
    // Processar marcas
    console.log('🏢 Processando marcas...');
    brandsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      const userId = data.userId;
      
      if (!userId) return;
      
      if (!userStats[userId]) {
        userStats[userId] = {
          totalInfluencers: 0,
          totalViews: 0,
          totalFollowers: 0,
          totalBrands: 0,
          totalCampaigns: 0
        };
      }
      
      userStats[userId].totalBrands++;
    });
    
    // Salvar contadores no Firestore
    console.log('💾 Salvando contadores no Firestore...');
    
    const batch = db.batch();
    let batchCount = 0;
    
    for (const [userId, stats] of Object.entries(userStats)) {
      const userStatsRef = db.collection('user_stats').doc(userId);
      
      batch.set(userStatsRef, {
        ...stats,
        lastUpdated: new Date(),
        initializedAt: new Date()
      });
      
      batchCount++;
      
      // Firestore permite máximo 500 operações por batch
      if (batchCount >= 500) {
        await batch.commit();
        console.log(`✅ Batch de ${batchCount} operações salvo`);
        batchCount = 0;
      }
    }
    
    // Salvar batch restante
    if (batchCount > 0) {
      await batch.commit();
      console.log(`✅ Batch final de ${batchCount} operações salvo`);
    }
    
    console.log('🎉 Inicialização completa!');
    console.log(`📈 Contadores criados para ${Object.keys(userStats).length} usuários`);
    
    // Mostrar resumo por usuário
    for (const [userId, stats] of Object.entries(userStats)) {
      console.log(`👤 ${userId}: ${stats.totalInfluencers} influenciadores, ${stats.totalBrands} marcas, ${stats.totalFollowers} seguidores`);
    }
    
  } catch (error) {
    console.error('❌ Erro ao inicializar contadores:', error);
    throw error;
  }
}

/**
 * Verifica se os contadores já foram inicializados
 */
async function checkIfInitialized(): Promise<boolean> {
  try {
    const snapshot = await db.collection('user_stats').limit(1).get();
    return !snapshot.empty;
  } catch (error) {
    console.error('Erro ao verificar inicialização:', error);
    return false;
  }
}

/**
 * Função principal
 */
async function main() {
  console.log('🔍 Verificando se contadores já foram inicializados...');
  
  const isInitialized = await checkIfInitialized();
  
  if (isInitialized) {
    console.log('⚠️  Contadores já foram inicializados. Use --force para reinicializar.');
    
    // Verificar se foi passado --force
    if (process.argv.includes('--force')) {
      console.log('🔄 Reinicializando contadores...');
      await initStatsCounters();
    } else {
      console.log('ℹ️  Execute com --force para reinicializar os contadores.');
    }
  } else {
    console.log('✨ Primeira inicialização dos contadores...');
    await initStatsCounters();
  }
}

// Executar script
if (require.main === module) {
  main()
    .then(() => {
      console.log('✅ Script finalizado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script falhou:', error);
      process.exit(1);
    });
} 

