import Link from 'next/link';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Users, Zap } from 'lucide-react';

export default async function HomePage() {
  // Verificar se o usuário está autenticado
  const { userId } = await auth();

  if (userId) {
    // Se está autenticado, redirecionar para auth-redirect que fará o redirecionamento otimizado
    redirect('/auth-redirect');
  }

  // URLs para login e signup (Clerk usa rotas padrão)
  const signInUrl = '/sign-in';
  const signUpUrl = '/sign-up';

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center">
          <Badge variant="secondary" className="mb-4">
            <Zap className="h-3 w-3 mr-1" />
            Sistema de Gerenciamento de Influenciadores
          </Badge>
          <h1 className="text-4xl text-white md:text-6xl font-bold tracking-tight mb-6">
            Aqui <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">Marcas</span> &{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600">Creators Brilham Juntos!</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Plataforma completa para gerenciar campanhas de marketing de influência, 
            com ferramentas avançadas de análise e relatórios.
          </p>
          
          {/* Botões de CTA */}
          <div className="flex gap-4 justify-center flex-wrap">
            <Link href={signInUrl}>
              <Button size="lg" className="gap-2">
                <ArrowRight className="h-5 w-5" />
                Fazer Login
              </Button>
            </Link>
            <Link href={signUpUrl}>
              <Button size="lg" variant="outline" className="gap-2">
                <Users className="h-5 w-5" />
                Criar Conta
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}



