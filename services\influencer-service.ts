import { Influencer } from "@/types/influencer";

/**
 * Serviço para gerenciamento de influenciadores
 */
export class InfluencerService {
  /**
   * Busca todos os influenciadores
   * @returns Lista de influenciadores
   */
  static async getAll(): Promise<Influencer[]> {
    try {
      const response = await fetch('/api/influencers');
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar influenciadores: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Erro no serviço de influenciadores:', error);
      throw error;
    }
  }

  /**
   * Busca um influenciador pelo ID
   * @param id ID do influenciador
   * @returns Dados do influenciador
   */
  static async getById(id: string): Promise<Influencer> {
    try {
      const response = await fetch(`/api/influencers/${id}`);
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar influenciador: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Erro ao buscar influenciador com ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cria um novo influenciador
   * @param data Dados do influenciador
   * @returns Influenciador criado
   */
  static async create(data: any): Promise<Influencer> {
    try {
      const response = await fetch('/api/influencers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao criar influenciador: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Erro ao criar influenciador:', error);
      throw error;
    }
  }

  /**
   * Atualiza um influenciador existente
   * @param id ID do influenciador
   * @param data Dados atualizados
   * @returns Influenciador atualizado
   */
  static async update(id: string, data: any): Promise<Influencer> {
    try {
      const response = await fetch(`/api/influencers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao atualizar influenciador: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Erro ao atualizar influenciador com ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Remove um influenciador
   * @param id ID do influenciador
   * @returns Resultado da operação
   */
  static async delete(id: string): Promise<any> {
    try {
      const response = await fetch(`/api/influencers/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao excluir influenciador: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Erro ao excluir influenciador com ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Remove múltiplos influenciadores
   * @param ids Lista de IDs a serem removidos
   * @returns Resultado da operação
   */
  static async bulkDelete(ids: string[]): Promise<any> {
    try {
      const response = await fetch('/api/influencers', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao excluir múltiplos influenciadores: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Erro ao excluir múltiplos influenciadores:', error);
      throw error;
    }
  }
}


