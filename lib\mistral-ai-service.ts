// Interface para dados demográficos extraídos
export interface ExtractedDemographics {
  platform: string
  username: string
  followers: number
  following: number
  posts: number
  engagementRate: number
  avgViews: number
  audienceGender: {
    male: number
    female: number
    other: number
  }
  audienceAgeRange: Array<{
    range: string
    percentage: number
  }>
  audienceLocations: Array<{
    country: string
    percentage: number
  }>
  audienceCities: Array<{
    city: string
    state?: string
    percentage: number
  }>
  additionalMetrics?: {
    storiesViews?: number
    reelsViews?: number
    shortsViews?: number
    longFormViews?: number
    videoViews?: number
  }
}

class MistralAIService {
  /**
   * Codifica uma imagem para base64
   */
  private async encodeImageToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // Remove o prefixo data:image/...;base64,
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  /**
   * Extrai dados demográficos de um screenshot usando Mistral AI via API route
   */
  async extractDemographicsFromScreenshot(file: File): Promise<ExtractedDemographics> {
    try {
      console.log('🧠 [Mistral AI] Iniciando extração de dados demográficos...')
      
      // Codificar imagem para base64
      const base64Image = await this.encodeImageToBase64(file)
      
      // Fazer requisição para nossa API route
      const response = await fetch('/api/extract-demographics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ base64Image }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erro na requisição para API')
      }

      const extractedData = await response.json()
      
      // Normalizar e validar os dados extraídos
      const normalizedData = this.normalizeExtractedData(extractedData)
      
      console.log('✅ [Mistral AI] Dados normalizados:', normalizedData)
      
      return normalizedData

    } catch (error) {
      console.error('❌ [Mistral AI] Erro na extração:', error)
      throw new Error(`Erro na extração de dados: ${error instanceof Error ? String(error.message) : 'Erro desconhecido'}`)
    }
  }

  /**
   * Normaliza e valida os dados extraídos pelo Mistral AI
   */
  private normalizeExtractedData(rawData: any): ExtractedDemographics {
    console.log('🔄 [Mistral AI] Normalizando dados:', rawData)

    // Os dados já vêm normalizados da API, então apenas retornar
    return {
      platform: rawData.platform || 'instagram',
      username: rawData.username || '',
      followers: rawData.followers || 0,
      following: rawData.following || 0,
      posts: rawData.posts || 0,
      engagementRate: rawData.engagementRate || 0,
      avgViews: rawData.avgViews || 0,
      audienceGender: rawData.audienceGender || { male: 0, female: 0, other: 0 },
      audienceAgeRange: rawData.audienceAgeRange || [],
      audienceLocations: rawData.audienceLocations || [],
      audienceCities: rawData.audienceCities || [],
      additionalMetrics: rawData.additionalMetrics
    }
  }

  /**
   * Identifica a plataforma social baseada nos dados extraídos
   */
  private identifyPlatform(data: any): string {
    // Verificar indicadores específicos de cada plataforma
    if (data.reels || data.stories || data.instagram) return 'instagram'
    if (data.shorts || data.youtube || data.subscribers) return 'youtube'
    if (data.tiktok || data.videos || data.for_you) return 'tiktok'
    if (data.facebook || data.pages) return 'facebook'
    if (data.twitch || data.stream || data.live) return 'twitch'
    if (data.kwai) return 'kwai'
    
    // Fallback baseado em URL ou outros indicadores
    if (data.platform) return data.platform.toLowerCase()
    
    return 'instagram' // Default
  }

  /**
   * Converte strings de números com sufixos (K, M, B) para números
   */
  private parseNumber(value: any): number {
    if (typeof value === 'number') return value
    if (!value || typeof value !== 'string') return 0

    const cleanValue = value.toString().toLowerCase().replace(/[^0-9kmb.,]/g, '')
    
    if (cleanValue.includes('b')) {
      return parseFloat(cleanValue.replace('b', '')) * 1000000000
    }
    if (cleanValue.includes('m')) {
      return parseFloat(cleanValue.replace('m', '')) * 1000000
    }
    if (cleanValue.includes('k')) {
      return parseFloat(cleanValue.replace('k', '')) * 1000
    }
    
    return parseFloat(cleanValue) || 0
  }

  /**
   * Normaliza dados de gênero da audiência
   */
  private normalizeGenderData(genderData: any) {
    const defaultGender = { male: 0, female: 0, other: 0 }
    
    if (!genderData || typeof genderData !== 'object') return defaultGender

    return {
      male: this.parseNumber(genderData.male || genderData.masculino || genderData.men) || 0,
      female: this.parseNumber(genderData.female || genderData.feminino || genderData.women) || 0,
      other: this.parseNumber(genderData.other || genderData.outros || genderData.non_binary) || 0
    }
  }

  /**
   * Normaliza dados de faixa etária
   */
  private normalizeAgeRangeData(ageData: any): Array<{range: string, percentage: number}> {
    if (!Array.isArray(ageData)) return []
    
    return ageData.map(item => ({
      range: item.range || item.faixa || '',
      percentage: this.parseNumber(item.percentage || item.porcentagem) || 0
    })).filter(item => item.range && item.percentage > 0)
  }

  /**
   * Normaliza dados de localização
   */
  private normalizeLocationData(locationData: any): Array<{country: string, percentage: number}> {
    if (!Array.isArray(locationData)) return []
    
    return locationData.map(item => ({
      country: item.country || item.pais || '',
      percentage: this.parseNumber(item.percentage || item.porcentagem) || 0
    })).filter(item => item.country && item.percentage > 0)
  }

  /**
   * Normaliza dados de cidades
   */
  private normalizeCityData(cityData: any): Array<{city: string, state?: string, percentage: number}> {
    if (!Array.isArray(cityData)) return []
    
    return cityData.map(item => ({
      city: item.city || item.cidade || '',
      state: item.state || item.estado,
      percentage: this.parseNumber(item.percentage || item.porcentagem) || 0
    })).filter(item => item.city && item.percentage > 0)
  }

  /**
   * Extrai métricas específicas da plataforma
   */
  private extractAdditionalMetrics(data: any, platform: string) {
    const metrics: any = {}

    switch (platform) {
      case 'instagram':
        if (data.stories_views || data.visualizacoes_stories) {
          metrics.storiesViews = this.parseNumber(data.stories_views || data.visualizacoes_stories)
        }
        if (data.reels_views || data.visualizacoes_reels) {
          metrics.reelsViews = this.parseNumber(data.reels_views || data.visualizacoes_reels)
        }
        break
      
      case 'youtube':
        if (data.shorts_views || data.visualizacoes_shorts) {
          metrics.shortsViews = this.parseNumber(data.shorts_views || data.visualizacoes_shorts)
        }
        if (data.long_form_views || data.visualizacoes_videos_longos) {
          metrics.longFormViews = this.parseNumber(data.long_form_views || data.visualizacoes_videos_longos)
        }
        break
      
      case 'tiktok':
        if (data.video_views || data.visualizacoes_videos) {
          metrics.videoViews = this.parseNumber(data.video_views || data.visualizacoes_videos)
        }
        break
    }

    return Object.keys(metrics).length > 0 ? metrics : undefined
  }
}

// Instância singleton do serviço
export const mistralAIService = new MistralAIService() 

