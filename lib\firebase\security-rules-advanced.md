# 🔒 Firebase Security Rules - Validações Avançadas

## 📋 FASE 3.3: Validação de Relacionamentos Avançada

Este documento contém funções avançadas de validação para Firebase Security Rules que podem ser copiadas para o arquivo `firestore.rules` quando necessário.

## 🔗 Funções de Validação de Relacionamentos

### Validar Influencers em Grupos
```javascript
// Validar se todos os influencers de um grupo pertencem ao usuário
function validateGroupInfluencers(influencerIds) {
  return influencerIds.size() <= 50 && // Limite máximo de influencers por grupo
         influencerIds.toSet().size() == influencerIds.size(); // Sem duplicatas
}
```

### Validar Relacionamentos de Campanha
```javascript
// Validar se brand e influencers de uma campanha pertencem ao usuário
function validateCampaignRelationships(brandId, influencerIds) {
  return brandBelongsToUser(brandId) &&
         influencerIds.size() <= 20 && // Limite de influencers por campanha
         validateInfluencersList(influencerIds);
}

// Validar lista de influencers
function validateInfluencersList(influencerIds) {
  return influencerIds is list && 
         influencerIds.size() <= 100; // Limite de segurança
}
```

### Validar Dados de Proposta
```javascript
function validateProposalData() {
  return 'brandId' in request.resource.data &&
         'influencerIds' in request.resource.data &&
         brandBelongsToUser(request.resource.data.brandId) &&
         request.resource.data.influencerIds.size() > 0 &&
         request.resource.data.influencerIds.size() <= 10; // Máximo 10 influencers por proposta
}
```

## 💰 Validações de Dados Financeiros

```javascript
// Validar dados financeiros
function validateFinancialData() {
  return request.resource.data.keys().hasAll(['influencerId', 'userId']) &&
         influencerBelongsToUser(request.resource.data.influencerId) &&
         ('pricing' in request.resource.data ? 
          validatePricingData(request.resource.data.pricing) : true);
}

// Validar estrutura de pricing
function validatePricingData(pricing) {
  return pricing is map &&
         ('instagram' in pricing ? pricing.instagram is map : true) &&
         ('tiktok' in pricing ? pricing.tiktok is map : true) &&
         ('youtube' in pricing ? pricing.youtube is map : true);
}
```

## 📝 Validações de Dados Específicos

### Validações de Brand
```javascript
function validateBrandData() {
  return request.resource.data.keys().hasAll(['name', 'userId']) &&
         isValidStringLength('name', 2, 100) &&
         ('contactEmail' in request.resource.data ? 
          isValidEmail(request.resource.data.contactEmail) : true) &&
         ('website' in request.resource.data ? 
          isValidUrl(request.resource.data.website) : true) &&
         ('budget' in request.resource.data ? 
          request.resource.data.budget >= 0 : true);
}
```

### Validações de Campaign
```javascript
function validateCampaignData() {
  return request.resource.data.keys().hasAll(['name', 'brandId', 'startDate', 'endDate', 'budget']) &&
         isValidStringLength('name', 2, 100) &&
         request.resource.data.startDate is timestamp &&
         request.resource.data.endDate is timestamp &&
         request.resource.data.startDate < request.resource.data.endDate &&
         request.resource.data.budget is number &&
         request.resource.data.budget > 0 &&
         request.resource.data.budget <= 10000000 && // Limite máximo de budget
         brandBelongsToUser(request.resource.data.brandId);
}
```

### Validações de Influencer
```javascript
function validateInfluencerData() {
  return request.resource.data.keys().hasAll(['name', 'country', 'state', 'city']) &&
         isValidStringLength('name', 2, 100) &&
         isValidStringLength('country', 2, 50) &&
         isValidStringLength('state', 2, 50) &&
         isValidStringLength('city', 2, 50) &&
         ('email' in request.resource.data ? 
          isValidEmail(request.resource.data.email) : true) &&
         ('socialNetworks' in request.resource.data ? 
          validateSocialNetworks(request.resource.data.socialNetworks) : true) &&
         ('audienceGender' in request.resource.data ? 
          validateAudienceGender(request.resource.data.audienceGender) : true);
}

// Validar estrutura de social networks
function validateSocialNetworks(socialNetworks) {
  return socialNetworks is map &&
         socialNetworks.size() <= 10; // Máximo 10 redes sociais
}

// Validar audienceGender do influencer
function validateAudienceGender(audienceGender) {
  return audienceGender is map &&
         audienceGender.keys().hasAll(['male', 'female']) &&
         audienceGender.male is number &&
         audienceGender.female is number &&
         audienceGender.male >= 0 &&
         audienceGender.female >= 0 &&
         audienceGender.male + audienceGender.female <= 100;
}
```

## 🔒 Funções de Validação de Segurança

### Rate Limiting e Horários
```javascript
// Verificar rate limiting básico
function isWithinRateLimit() {
  return request.time > resource.data.lastUpdated + duration.value(1, 's');
}

// Validar horário de operação
function isOperationTimeAllowed() {
  let hour = request.time.toMillis() / 3600000 % 24;
  return hour >= 6 && hour <= 22; // 6h às 22h
}
```

### Validações de Integridade
```javascript
// Validar datas
function validateDateFields() {
  return request.resource.data.createdAt is timestamp &&
         request.resource.data.updatedAt is timestamp &&
         request.resource.data.createdAt <= request.resource.data.updatedAt &&
         request.resource.data.updatedAt <= request.time;
}

// Validar campos numéricos
function validateNumericFields() {
  return (!('rating' in request.resource.data) || 
          (request.resource.data.rating >= 0 && request.resource.data.rating <= 5)) &&
         (!('engagementRate' in request.resource.data) || 
          (request.resource.data.engagementRate >= 0 && request.resource.data.engagementRate <= 100));
}

// Validar arrays
function validateArrayFields() {
  return (!('tags' in request.resource.data) || request.resource.data.tags.size() <= 20) &&
         (!('categories' in request.resource.data) || request.resource.data.categories.size() <= 10);
}
```

## ⚡ Funções Auxiliares Específicas

### Transições de Status
```javascript
function isValidStatusTransition(oldStatus, newStatus) {
  return (oldStatus == 'draft' && newStatus in ['active', 'cancelled']) ||
         (oldStatus == 'active' && newStatus in ['paused', 'completed', 'cancelled']) ||
         (oldStatus == 'paused' && newStatus in ['active', 'cancelled']) ||
         (oldStatus == 'completed') || // Status final
         (oldStatus == 'cancelled'); // Status final
}
```

### Validações de Relacionamento
```javascript
function canAddInfluencerToCampaign(campaignId, influencerId) {
  return campaignBelongsToUser(campaignId) &&
         influencerBelongsToUser(influencerId) &&
         getDocument('campaigns/' + campaignId).data.status in ['draft', 'active'];
}
```

## 📖 Exemplo de Uso Completo

```javascript
// No arquivo firestore.rules, usar as funções assim:
match /campaigns/{campaignId} {
  allow create: if isValidCreate() &&
                validateCampaignData() &&
                validateDateFields() &&
                validateNumericFields();
  
  allow update: if isValidUpdate() &&
                validateDateFields() &&
                isWithinRateLimit() &&
                ('status' in request.resource.data ? 
                 isValidStatusTransition(resource.data.status, request.resource.data.status) : true);
}

match /influencers/{influencerId} {
  allow create: if isValidCreate() &&
                validateInfluencerData() &&
                validateDateFields() &&
                validateNumericFields();
}
``` 