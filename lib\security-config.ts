/**
 * 🛡️ CONFIGURAÇÃO DE SEGURANÇA - SISTEMA DE ROLES
 * 
 * ⚠️ ATENÇÃO: Este arquivo define as configurações críticas de segurança.
 * Qualquer alteração deve ser cuidadosamente revisada e testada.
 */

// Roles disponíveis no sistema
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  MEMBER: 'member',
  USER: 'user',
  VIEWER: 'viewer',
  ANONYMOUS: 'anonymous'
} as const;

// ⚠️ SEGURANÇA: Role padrão para novos usuários
export const DEFAULT_USER_ROLE = USER_ROLES.ANONYMOUS; // Usuários precisam de onboarding!

// Permissões disponíveis no sistema
export const PERMISSIONS = {
  // Dashboard e Analytics
  VIEW_DASHBOARD: 'view_dashboard',
  VIEW_ANALYTICS: 'view_analytics',
  
  // Campanhas
  VIEW_CAMPAIGNS: 'view_campaigns',
  CREATE_CAMPAIGNS: 'create_campaigns',
  EDIT_CAMPAIGNS: 'edit_campaigns',
  DELETE_CAMPAIGNS: 'delete_campaigns',
  
  // Propostas
  VIEW_PROPOSALS: 'view_proposals',
  CREATE_PROPOSALS: 'create_proposals',
  EDIT_PROPOSALS: 'edit_proposals',
  DELETE_PROPOSALS: 'delete_proposals',
  APPROVE_PROPOSALS: 'approve_proposals',
  
  // Influenciadores
  VIEW_INFLUENCERS: 'view_influencers',
  CREATE_INFLUENCERS: 'create_influencers',
  EDIT_INFLUENCERS: 'edit_influencers',
  DELETE_INFLUENCERS: 'delete_influencers',
  
  // Marcas
  VIEW_BRANDS: 'view_brands',
  CREATE_BRANDS: 'create_brands',
  EDIT_BRANDS: 'edit_brands',
  DELETE_BRANDS: 'delete_brands',
  
  // Financeiro
  VIEW_FINANCIALS: 'view_financials',
  EDIT_FINANCIALS: 'edit_financials',
  APPROVE_PAYMENTS: 'approve_payments',
  
  // Administração
  MANAGE_USERS: 'manage_users',
  MANAGE_SYSTEM: 'manage_system',
  VIEW_SYSTEM_LOGS: 'view_system_logs'
} as const;

// Mapeamento de roles para permissões
export const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: [
    // Dashboard
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ANALYTICS,
    
    // Campanhas
    PERMISSIONS.VIEW_CAMPAIGNS,
    PERMISSIONS.CREATE_CAMPAIGNS,
    PERMISSIONS.EDIT_CAMPAIGNS,
    PERMISSIONS.DELETE_CAMPAIGNS,
    
    // Propostas
    PERMISSIONS.VIEW_PROPOSALS,
    PERMISSIONS.CREATE_PROPOSALS,
    PERMISSIONS.EDIT_PROPOSALS,
    PERMISSIONS.DELETE_PROPOSALS,
    PERMISSIONS.APPROVE_PROPOSALS,
    
    // Influenciadores
    PERMISSIONS.VIEW_INFLUENCERS,
    PERMISSIONS.CREATE_INFLUENCERS,
    PERMISSIONS.EDIT_INFLUENCERS,
    PERMISSIONS.DELETE_INFLUENCERS,
    
    // Marcas
    PERMISSIONS.VIEW_BRANDS,
    PERMISSIONS.CREATE_BRANDS,
    PERMISSIONS.EDIT_BRANDS,
    PERMISSIONS.DELETE_BRANDS,
    
    // Financeiro
    PERMISSIONS.VIEW_FINANCIALS,
    PERMISSIONS.EDIT_FINANCIALS,
    PERMISSIONS.APPROVE_PAYMENTS,
    
    // Administração
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_SYSTEM,
    PERMISSIONS.VIEW_SYSTEM_LOGS
  ],
  
  [USER_ROLES.MANAGER]: [
    // Dashboard
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ANALYTICS,
    
    // Campanhas
    PERMISSIONS.VIEW_CAMPAIGNS,
    PERMISSIONS.CREATE_CAMPAIGNS,
    PERMISSIONS.EDIT_CAMPAIGNS,
    
    // Propostas
    PERMISSIONS.VIEW_PROPOSALS,
    PERMISSIONS.CREATE_PROPOSALS,
    PERMISSIONS.EDIT_PROPOSALS,
    
    // Influenciadores
    PERMISSIONS.VIEW_INFLUENCERS,
    PERMISSIONS.CREATE_INFLUENCERS,
    PERMISSIONS.EDIT_INFLUENCERS,
    
    // Marcas
    PERMISSIONS.VIEW_BRANDS,
    PERMISSIONS.CREATE_BRANDS,
    PERMISSIONS.EDIT_BRANDS,
    
    // Financeiro
    PERMISSIONS.VIEW_FINANCIALS
  ],
  
  [USER_ROLES.MEMBER]: [
    // Dashboard
    PERMISSIONS.VIEW_DASHBOARD,
    
    // Propostas
    PERMISSIONS.VIEW_PROPOSALS,
   
    
    // Influenciadores
    PERMISSIONS.VIEW_INFLUENCERS,
   
    
    // Marcas
    PERMISSIONS.VIEW_BRANDS,
   
  ],
  
  [USER_ROLES.USER]: [
    // Influenciadores - APENAS acesso a visualização e gerenciamento básico
    PERMISSIONS.VIEW_INFLUENCERS,
    
    // ⚠️ RESTRIÇÃO: Role USER tem acesso APENAS às rotas /influencers
    // Todas as outras permissões foram removidas conforme nova política de segurança
  ],
  
  [USER_ROLES.VIEWER]: [
    // Dashboard
    PERMISSIONS.VIEW_DASHBOARD,
    
    // Campanhas
    PERMISSIONS.VIEW_CAMPAIGNS,
    
    // Propostas
    PERMISSIONS.VIEW_PROPOSALS,
    
    // Influenciadores
    PERMISSIONS.VIEW_INFLUENCERS,
    
    // Marcas
    PERMISSIONS.VIEW_BRANDS
  ],
  
  [USER_ROLES.ANONYMOUS]: [
    // ⚠️ ROLE TEMPORÁRIO: Usuário precisa de onboarding
    // Sem permissões - apenas acesso para completar configuração inicial
  ]
} as const;

/**
 * ⚠️ FUNÇÃO DE SEGURANÇA: Validar role do usuário
 */
export function isValidRole(role: string): role is keyof typeof ROLE_PERMISSIONS {
  return Object.values(USER_ROLES).includes(role as any);
}

/**
 * ⚠️ FUNÇÃO DE SEGURANÇA: Obter role seguro
 */
export function getSafeUserRole(role?: string): string {
  if (!role || !isValidRole(role)) {
    console.warn(`⚠️ SEGURANÇA: Role inválido '${role}', usando role padrão '${DEFAULT_USER_ROLE}'`);
    return DEFAULT_USER_ROLE;
  }
  return role;
}

/**
 * ⚠️ FUNÇÃO DE SEGURANÇA: Verificar permissão do usuário
 */
export function hasPermission(userRole: string, permission: string): boolean {
  const safeRole = getSafeUserRole(userRole);
  const roleKey = safeRole as keyof typeof ROLE_PERMISSIONS;
  const permissions = ROLE_PERMISSIONS[roleKey] || [];
  
  // Debug da verificação de permissões
  console.log('[SECURITY] Verificação de permissão:', {
    originalRole: userRole,
    safeRole,
    permission,
    roleKey,
    hasValidPermissions: permissions.length > 0
  });
  
  // Verificação manual para evitar problemas de tipo
  for (const perm of permissions) {
    if (perm === permission) {
      return true;
    }
  }
  
  return false;
}

/**
 * ⚠️ FUNÇÃO DE SEGURANÇA: Obter todas as permissões do usuário
 */
export function getUserPermissions(userRole: string): readonly string[] {
  const safeRole = getSafeUserRole(userRole);
  return ROLE_PERMISSIONS[safeRole as keyof typeof ROLE_PERMISSIONS] || [];
}

/**
 * ⚠️ FUNÇÃO DE SEGURANÇA: Verificar se é admin
 */
export function isAdmin(userRole: string): boolean {
  return getSafeUserRole(userRole) === USER_ROLES.ADMIN;
}

/**
 * ⚠️ FUNÇÃO DE SEGURANÇA: Verificar se pode gerenciar usuários
 */
export function canManageUsers(userRole: string): boolean {
  return hasPermission(userRole, PERMISSIONS.MANAGE_USERS);
}

/**
 * ⚠️ AUDITORIA DE SEGURANÇA: Logs para monitoramento
 */
export function logSecurityEvent(event: string, userId: string, details?: any) {
  const timestamp = new Date().toISOString();
  console.log(`🛡️ SEGURANÇA [${timestamp}] ${event}:`, {
    userId,
    details,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Server'
  });
}

// ⚠️ CONFIGURAÇÕES DE SESSÃO
export const SESSION_CONFIG = {
  JWT_EXPIRY_HOURS: 24,
  REFRESH_THRESHOLD_MINUTES: 30,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MINUTES: 15
} as const;

/**
 * 🔒 CONFIGURAÇÃO DE SEGURANÇA CENTRALIZADA
 * 
 * Este arquivo centraliza todas as configurações de segurança da aplicação,
 * incluindo Content Security Policy (CSP) otimizado para:
 * - Clerk Authentication
 * - Firebase Services
 * - WorkOS
 * - Cloudflare CAPTCHA
 */

// 🔗 URLs permitidas para diferentes serviços
export const SECURITY_DOMAINS = {
  // Clerk Authentication & Telemetry
  clerk: [
    'https://*.clerk.accounts.dev',
    'https://clerk-telemetry.com'
  ],
  
  // Cloudflare CAPTCHA
  cloudflare: [
    'https://challenges.cloudflare.com'
  ],
  
  // Firebase Services
  firebase: [
    'https://firebase.googleapis.com',
    'https://*.firebase.googleapis.com',
    'https://*.firebaseio.com',
    'wss://*.firebaseio.com',
    'https://firebaseinstallations.googleapis.com',
    'https://identitytoolkit.googleapis.com',
    'https://securetoken.googleapis.com',
    'https://firestore.googleapis.com',
    'https://*.firestore.googleapis.com',
    'wss://firestore.googleapis.com',
    'wss://*.firestore.googleapis.com'
  ],
  
  // WorkOS
  workos: [
    'https://js.workos.com',
    'https://api.workos.com',
    'https://workos.com'
  ],
  
  // Analytics & Monitoring
  analytics: [
    'https://www.googletagmanager.com',
    'https://www.google-analytics.com',
    'https://analytics.google.com'
  ],
  
  // External APIs
  external: [
    'https://viacep.com.br'
  ],
  
  // Development & Deployment
  development: [
    'https://vercel.live'
  ],
  
  // Google Fonts
  fonts: [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ]
};

// 🛡️ Content Security Policy otimizado
export const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  
  'script-src': [
    "'self'",
    "'unsafe-eval'",
    "'unsafe-inline'",
    "blob:",
    ...SECURITY_DOMAINS.development,
    ...SECURITY_DOMAINS.analytics,
    ...SECURITY_DOMAINS.clerk,
    ...SECURITY_DOMAINS.cloudflare,
    ...SECURITY_DOMAINS.workos
  ],
  
  'style-src': [
    "'self'",
    "'unsafe-inline'",
    ...SECURITY_DOMAINS.fonts,
    ...SECURITY_DOMAINS.cloudflare
  ],
  
  'img-src': [
    "'self'",
    "data:",
    "https:",
    "blob:"
  ],
  
  'font-src': [
    "'self'",
    "data:",
    ...SECURITY_DOMAINS.fonts
  ],
  
  'connect-src': [
    "'self'",
    ...SECURITY_DOMAINS.firebase,
    ...SECURITY_DOMAINS.analytics,
    ...SECURITY_DOMAINS.external,
    ...SECURITY_DOMAINS.clerk,
    ...SECURITY_DOMAINS.workos
  ],
  
  'frame-src': [
    "'self'",
    ...SECURITY_DOMAINS.clerk,
    ...SECURITY_DOMAINS.cloudflare,
    ...SECURITY_DOMAINS.workos
  ]
};

// 🔧 Função para gerar string de CSP
export function generateCSPString(): string {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ') + ';';
}

// 🛡️ Headers de segurança padrão
export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Content-Security-Policy': generateCSPString()
};

// 🔍 Headers de segurança para produção
export const PRODUCTION_SECURITY_HEADERS = {
  ...SECURITY_HEADERS,
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
};

// 📊 Configuração de Rate Limiting
export const RATE_LIMIT_CONFIG = {
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // limite de requests por IP
  message: 'Muitas tentativas de acesso. Tente novamente em 15 minutos.',
  standardHeaders: true,
  legacyHeaders: false
};

// 🔐 Configuração de CORS
export const CORS_CONFIG = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://deumatch.vercel.app'] 
    : ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// ✅ Validar configuração de segurança
export function validateSecurityConfig(): boolean {
  const requiredEnvVars = [
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY'
  ];
  
  return requiredEnvVars.every(envVar => process.env[envVar]);
}

// 🚨 Log de segurança para CSP
export function logCSPViolation(event: string, details: Record<string, unknown>) {
  if (process.env.NODE_ENV === 'production') {
    console.log(`[CSP-VIOLATION] ${event}:`, JSON.stringify(details));
  }
} 

