'use client';

import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/hooks/use-auth-v2';
import { Protect } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { List, Plus, AlertTriangle, Search, Filter, MoreHorizontal, Edit, Trash2, ExternalLink, X, Shield, Check, Users, Tag, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader } from '@/components/ui/loader';

// Tipos para as listas
interface Lista {
  id: string;
  nome: string;
  tipoLista: 'públicas' | 'privadas' | 'compartilhadas';
  tipoObjeto: 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
  tamanho: number;
  criadoPor: string;
  criadoPorNome: string;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  descricao?: string;
  tags?: string[];
}

interface CreateListaData {
  nome: string;
  tipoLista: 'públicas' | 'privadas' | 'compartilhadas';
  tipoObjeto: 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
  descricao?: string;
  tags?: string[];
}

interface PageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function ListasPage({ params }: PageProps) {
  const { currentUser, firebaseUser, isLoading, isInitialized } = useAuth();
  const [userId, setUserId] = useState<string | null>(null);

  // Resolver o parâmetro userId de forma assíncrona
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setUserId(resolvedParams.userId);
    };
    
    resolveParams();
  }, [params]);

  // Verificação de isolamento por usuário
  const isOwnProfile = currentUser?.id === userId;
  const canAccess = isOwnProfile;
  
  // Estados do formulário
  const [isCreating, setIsCreating] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [editingLista, setEditingLista] = useState<Lista | null>(null);
  const [formData, setFormData] = useState<CreateListaData>({
    nome: '',
    tipoLista: 'privadas',
    tipoObjeto: 'influenciadores',
    descricao: '',
    tags: []
  });

  // Estados de filtros e pesquisa
  const [searchTerm, setSearchTerm] = useState('');
  const [tipoListaFilter, setTipoListaFilter] = useState<string>('all');
  const [tipoObjetoFilter, setTipoObjetoFilter] = useState<string>('all');
  const [selectedListas, setSelectedListas] = useState<string[]>([]);

  // Mock data - em um projeto real, isso viria de uma API filtrada por userId
  const [listas, setListas] = useState<Lista[]>([]);

  // Carregar listas do usuário quando currentUser e userId estiverem disponíveis
  useEffect(() => {
    if (currentUser && userId && isOwnProfile) {
      // Simular carregamento de listas do usuário específico
      const userListas: Lista[] = [
        {
          id: '1',
          nome: 'Top Influenciadores Fashion',
          tipoLista: 'públicas',
          tipoObjeto: 'influenciadores',
          tamanho: 145,
          criadoPor: currentUser.id,
          criadoPorNome: currentUser.name || 'Usuário',
          dataCriacao: new Date('2024-01-15'),
          ultimaAtualizacao: new Date('2024-06-20'),
          descricao: 'Lista curada dos melhores influenciadores de moda',
          tags: ['fashion', 'lifestyle', 'top-tier']
        },
        {
          id: '2',
          nome: 'Marcas Tecnologia',
          tipoLista: 'privadas',
          tipoObjeto: 'marcas',
          tamanho: 28,
          criadoPor: currentUser.id,
          criadoPorNome: currentUser.name || 'Usuário',
          dataCriacao: new Date('2024-02-10'),
          ultimaAtualizacao: new Date('2024-06-18'),
          descricao: 'Marcas do setor tecnológico para campanhas',
          tags: ['tech', 'b2b', 'inovação']
        }
      ];
      setListas(userListas);
    }
  }, [currentUser, userId, isOwnProfile]);

  // Aplicar filtros e pesquisa
  const filteredListas = useMemo(() => {
    return listas.filter(lista => {
      const matchesSearch = lista.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lista.descricao?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lista.criadoPorNome.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesTipoLista = tipoListaFilter === 'all' || lista.tipoLista === tipoListaFilter;
      const matchesTipoObjeto = tipoObjetoFilter === 'all' || lista.tipoObjeto === tipoObjetoFilter;
      
      return matchesSearch && matchesTipoLista && matchesTipoObjeto;
    });
  }, [listas, searchTerm, tipoListaFilter, tipoObjetoFilter]);

  const handleInputChange = (field: keyof CreateListaData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCreateLista = async () => {
    if (!formData.nome.trim()) {
      toast.error('Nome da lista é obrigatório');
      return;
    }

    if (!currentUser) {
      toast.error('Usuário não autenticado');
      return;
    }

    try {
      setIsCreating(true);
      
      const novaLista: Lista = {
        id: Date.now().toString(),
        nome: formData.nome.trim(),
        tipoLista: formData.tipoLista,
        tipoObjeto: formData.tipoObjeto,
        tamanho: 0,
        criadoPor: currentUser.id,
        criadoPorNome: currentUser.name || 'Usuário',
        dataCriacao: new Date(),
        ultimaAtualizacao: new Date(),
        descricao: formData.descricao?.trim(),
        tags: formData.tags || []
      };

      setListas(prev => [...prev, novaLista]);

      // Limpar formulário e fechar painel
      setFormData({
        nome: '',
        tipoLista: 'privadas',
        tipoObjeto: 'influenciadores',
        descricao: '',
        tags: []
      });
      setIsPanelOpen(false);
      setEditingLista(null);

      toast.success('Lista criada com sucesso!');
    } catch (error) {
      console.error('Erro ao criar lista:', error);
      toast.error('Erro ao criar lista. Tente novamente.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleEditLista = (lista: Lista) => {
    setEditingLista(lista);
    setFormData({
      nome: lista.nome,
      tipoLista: lista.tipoLista,
      tipoObjeto: lista.tipoObjeto,
      descricao: lista.descricao || '',
      tags: lista.tags || []
    });
    setIsPanelOpen(true);
  };

  const handleUpdateLista = async () => {
    if (!editingLista || !formData.nome.trim()) {
      toast.error('Nome da lista é obrigatório');
      return;
    }

    try {
      setIsCreating(true);
      
      setListas(prev => prev.map(lista => 
        lista.id === editingLista.id 
          ? {
              ...lista,
              nome: formData.nome.trim(),
              tipoLista: formData.tipoLista,
              tipoObjeto: formData.tipoObjeto,
              descricao: formData.descricao?.trim(),
              tags: formData.tags || [],
              ultimaAtualizacao: new Date()
            }
          : lista
      ));

      // Limpar estados
      setEditingLista(null);
      setFormData({
        nome: '',
        tipoLista: 'privadas',
        tipoObjeto: 'influenciadores',
        descricao: '',
        tags: []
      });
      setIsPanelOpen(false);

      toast.success('Lista atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar lista:', error);
      toast.error('Erro ao atualizar lista. Tente novamente.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteLista = async (lista: Lista) => {
    if (!confirm(`Tem certeza que deseja deletar a lista "${lista.nome}"? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      setListas(prev => prev.filter(l => l.id !== lista.id));
      toast.success('Lista deletada com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar lista:', error);
      toast.error('Erro ao deletar lista. Tente novamente.');
    }
  };

  const handleSelectLista = (listaId: string, checked: boolean) => {
    if (checked) {
      setSelectedListas(prev => [...prev, listaId]);
    } else {
      setSelectedListas(prev => prev.filter(id => id !== listaId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedListas(filteredListas.map(lista => lista.id));
    } else {
      setSelectedListas([]);
    }
  };

  const resetPanel = () => {
    setEditingLista(null);
    setFormData({
      nome: '',
      tipoLista: 'privadas',
      tipoObjeto: 'influenciadores',
      descricao: '',
      tags: []
    });
    setIsPanelOpen(false);
  };

  const openCreatePanel = () => {
    resetPanel();
    setIsPanelOpen(true);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  const getTipoListaBadgeColor = (tipo: string) => {
    switch (tipo) {
      case 'públicas': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'privadas': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'compartilhadas': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
              default: return 'bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300';
    }
  };

  const getTipoObjetoIcon = (tipo: string) => {
    switch (tipo) {
      case 'influenciadores': return <Users className="h-4 w-4" />;
      case 'marcas': return <Tag className="h-4 w-4" />;
      case 'campanhas': return <Calendar className="h-4 w-4" />;
      case 'conteúdo': return <List className="h-4 w-4" />;
      default: return <List className="h-4 w-4" />;
    }
  };

  

  // Verificação de acesso
  if (!currentUser || !canAccess) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            {!currentUser 
              ? "Você precisa estar logado para acessar as listas."
              : "Você só pode acessar suas próprias listas."
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <Protect permission="org:admin">
    <div className="flex bg-muted/30 dark:bg-[#080210] h-screen overflow-hidden">
      {/* Conteúdo Principal */}
      <div className={cn("flex-1 overflow-auto transition-all duration-300", isPanelOpen ? "mr-96" : "mr-0")}>
        <div className="p-6">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold">Listas</h1>
                <p className="text-muted-foreground">
                  Gerencie suas listas de influenciadores, marcas, campanhas e conteúdo
                </p>
              </div>
            </div>

            {/* Barra de Filtros */}
            <div className="flex items-center gap-4 mb-6">
              {/* Pesquisa */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Pesquisar listas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filtro por Tipo de Lista */}
              <Select value={tipoListaFilter} onValueChange={setTipoListaFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Tipo de lista" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os tipos</SelectItem>
                  <SelectItem value="públicas">Públicas</SelectItem>
                  <SelectItem value="privadas">Privadas</SelectItem>
                  <SelectItem value="compartilhadas">Compartilhadas</SelectItem>
                </SelectContent>
              </Select>

              {/* Filtro por Tipo de Objeto */}
              <Select value={tipoObjetoFilter} onValueChange={setTipoObjetoFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Tipo de objeto" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os objetos</SelectItem>
                  <SelectItem value="influenciadores">Influenciadores</SelectItem>
                  <SelectItem value="marcas">Marcas</SelectItem>
                  <SelectItem value="campanhas">Campanhas</SelectItem>
                  <SelectItem value="conteúdo">Conteúdo</SelectItem>
                </SelectContent>
              </Select>

              {/* Badge de filtros aplicados */}
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {filteredListas.length} lista(s) encontrada(s)
                </Badge>
              </div>

              {/* Botão Nova Lista */}
              <Button 
                className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] hover:bg-gradient-to-r hover:from-[#9810fa] hover:to-[#ff0074] text-white"
                onClick={openCreatePanel}
              >
                <Plus className="h-4 w-4 mr-2" />
                Nova Lista
              </Button>

              {/* Ações */}
              {selectedListas.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      Ações ({selectedListas.length})
                      <MoreHorizontal className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedListas([])}>
                      Desmarcar todas
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      className="text-destructive"
                      onClick={() => {
                        if (confirm(`Deletar ${selectedListas.length} lista(s) selecionada(s)?`)) {
                          setListas(prev => prev.filter(lista => !selectedListas.includes(lista.id)));
                          setSelectedListas([]);
                          toast.success('Listas deletadas com sucesso!');
                        }
                      }}
                    >
                      Deletar selecionadas
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          {/* Tabela de Listas */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedListas.length === filteredListas.length && filteredListas.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>NOME DA LISTA</TableHead>
                    <TableHead>TIPO DE LISTA</TableHead>
                    <TableHead>TIPO DE OBJETO</TableHead>
                    <TableHead>TAMANHO</TableHead>
                    <TableHead>CRIADO POR</TableHead>
                    <TableHead>DATA DE CRIAÇÃO</TableHead>
                    <TableHead>ÚLTIMA ATUALIZAÇÃO</TableHead>
                    <TableHead className="w-12"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredListas.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-12">
                        <List className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-muted-foreground mb-2">
                          {searchTerm || tipoListaFilter !== 'all' || tipoObjetoFilter !== 'all'
                            ? 'Nenhuma lista encontrada com os filtros aplicados.'
                            : 'Nenhuma lista criada ainda.'
                          }
                        </p>
                        {!searchTerm && tipoListaFilter === 'all' && tipoObjetoFilter === 'all' && (
                          <Button
                            variant="outline"
                            onClick={openCreatePanel}
                            className="mt-4"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Criar primeira lista
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredListas.map((lista) => (
                      <TableRow key={lista.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedListas.includes(lista.id)}
                            onCheckedChange={(checked) => handleSelectLista(lista.id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">{lista.nome}</span>
                            {lista.descricao && (
                              <span className="text-sm text-muted-foreground">{lista.descricao}</span>
                            )}
                            {lista.tags && lista.tags.length > 0 && (
                              <div className="flex gap-1 mt-1">
                                {lista.tags.map((tag, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTipoListaBadgeColor(lista.tipoLista)}>
                            {lista.tipoLista}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getTipoObjetoIcon(lista.tipoObjeto)}
                            <span className="capitalize">{lista.tipoObjeto}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">{lista.tamanho.toLocaleString()}</span>
                        </TableCell>
                        <TableCell>
                          <span>{lista.criadoPorNome}</span>
                        </TableCell>
                        <TableCell>
                          <span>{formatDate(lista.dataCriacao)}</span>
                        </TableCell>
                        <TableCell>
                          <span>{formatDate(lista.ultimaAtualizacao)}</span>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditLista(lista)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-destructive"
                                onClick={() => handleDeleteLista(lista)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Deletar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Painel Lateral */}
      <Sheet open={isPanelOpen} onOpenChange={setIsPanelOpen}>
        <SheetContent className="w-96 sm:max-w-none">
          <SheetHeader>
            <SheetTitle>
              {editingLista ? 'Editar Lista' : 'Nova Lista'}
            </SheetTitle>
            <SheetDescription>
              {editingLista 
                ? 'Atualize as informações da lista.' 
                : 'Preencha as informações para criar uma nova lista.'
              }
            </SheetDescription>
          </SheetHeader>

          <div className="mt-6 space-y-6">
            {/* Nome da Lista */}
            <div className="space-y-2">
              <Label htmlFor="nome">Nome da Lista *</Label>
              <Input
                id="nome"
                placeholder="Ex: Top Influenciadores Fashion"
                value={formData.nome}
                onChange={(e) => handleInputChange('nome', e.target.value)}
              />
            </div>

            {/* Tipo de Lista */}
            <div className="space-y-2">
              <Label htmlFor="tipoLista">Tipo de Lista *</Label>
              <Select value={formData.tipoLista} onValueChange={(value) => handleInputChange('tipoLista', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="públicas">Pública</SelectItem>
                  <SelectItem value="privadas">Privada</SelectItem>
                  <SelectItem value="compartilhadas">Compartilhada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Tipo de Objeto */}
            <div className="space-y-2">
              <Label htmlFor="tipoObjeto">Tipo de Objeto *</Label>
              <Select value={formData.tipoObjeto} onValueChange={(value) => handleInputChange('tipoObjeto', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="influenciadores">Influenciadores</SelectItem>
                  <SelectItem value="marcas">Marcas</SelectItem>
                  <SelectItem value="campanhas">Campanhas</SelectItem>
                  <SelectItem value="conteúdo">Conteúdo</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Descrição */}
            <div className="space-y-2">
              <Label htmlFor="descricao">Descrição</Label>
              <Textarea
                id="descricao"
                placeholder="Descreva o propósito desta lista..."
                value={formData.descricao}
                onChange={(e) => handleInputChange('descricao', e.target.value)}
                rows={3}
              />
            </div>

            {/* Botões de Ação */}
            <div className="flex gap-2 pt-4">
              <Button
                onClick={editingLista ? handleUpdateLista : handleCreateLista}
                disabled={isCreating || !formData.nome.trim()}
                className="flex-1 bg-[#ec003f] hover:bg-[#d10037]"
              >
                {isCreating ? (
                  <OperationLoader />
                ) : editingLista ? (
                  'Atualizar Lista'
                ) : (
                  'Criar Lista'
                )}
              </Button>
              <Button
                variant="outline"
                onClick={resetPanel}
                disabled={isCreating}
              >
                Cancelar
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
    </Protect>
  );
} 