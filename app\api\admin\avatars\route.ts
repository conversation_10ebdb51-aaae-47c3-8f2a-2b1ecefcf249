import { NextRequest, NextResponse } from 'next/server';
import { getAvailableAvatars } from '@/lib/firebase-storage';

export async function GET() {
  try {
    const avatars = await getAvailableAvatars();
    
    return NextResponse.json({
      success: true,
      avatars: avatars
    });
  } catch (error) {
    console.error('Erro ao buscar avatares:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
} 

