'use client';

import { useState, useEffect, useRef, ChangeEventHandler } from 'react';
import { useAuth, useOrganization, useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CLERK_ROLES, 
  CLERK_PERMISSIONS,
  getRolePermissions,
  type ClerkRole 
} from '@/lib/clerk-fga-config';
import { Trash2, Shield, Users, Settings } from 'lucide-react';

/**
 * 🎭 COMPONENTE PARA GERENCIAR ROLES DE MEMBROS DA ORGANIZAÇÃO
 */

const OrgMembersParams = {
  memberships: {
    pageSize: 10,
    keepPreviousData: true,
  },
};

export function ClerkRoleManager() {
  const { user } = useUser();
  const { has } = useAuth();
  const { isLoaded, memberships, organization } = useOrganization(OrgMembersParams);
  
  // Verificar se usuário tem permissão para gerenciar usuários
  const canManageUsers = has?.({ permission: 'org:users:manage' }) || false;
  const isAdmin = has?.({ role: 'org:admin' }) || false;

  if (!isLoaded) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Carregando membros...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (!canManageUsers && !isAdmin) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-red-500" />
            Acesso Negado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Você não tem permissão para gerenciar membros da organização.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Gerenciar Membros
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Gerencie roles e permissões dos membros da organização
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Lista de Membros */}
          <div className="space-y-3">
            {memberships?.data?.map((membership) => (
              <MemberCard 
                key={membership.id} 
                membership={membership}
                currentUserId={user?.id}
                canManage={canManageUsers || isAdmin}
                onUpdate={() => memberships?.revalidate?.()}
              />
            ))}
          </div>

          {/* Paginação */}
          <div className="flex justify-between items-center pt-4">
            <Button
              variant="outline"
              disabled={!memberships?.hasPreviousPage || memberships?.isFetching}
              onClick={() => memberships?.fetchPrevious?.()}
            >
              Anterior
            </Button>

            <span className="text-sm text-muted-foreground">
              {memberships?.data?.length || 0} membros
            </span>

            <Button
              variant="outline"
              disabled={!memberships?.hasNextPage || memberships?.isFetching}
              onClick={() => memberships?.fetchNext?.()}
            >
              Próximo
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 👤 CARD INDIVIDUAL DE MEMBRO
 */
interface MemberCardProps {
  membership: any; // Tipo do Clerk OrganizationMembership
  currentUserId?: string;
  canManage: boolean;
  onUpdate: () => void;
}

function MemberCard({ membership, currentUserId, canManage, onUpdate }: MemberCardProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  const isCurrentUser = membership.publicUserData.userId === currentUserId;
  const currentRole = membership.role as ClerkRole;
  const permissions = getRolePermissions(currentRole);

  const handleRoleChange = async (newRole: ClerkRole) => {
    if (!canManage || isCurrentUser) return;
    
    setIsUpdating(true);
    try {
      await membership.update({ role: newRole });
      onUpdate();
      console.log(`✅ Role atualizado para ${newRole}`);
    } catch (error) {
      console.error('❌ Erro ao atualizar role:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!canManage || isCurrentUser) return;
    
    setIsRemoving(true);
    try {
      await membership.destroy();
      onUpdate();
      console.log('✅ Membro removido');
    } catch (error) {
      console.error('❌ Erro ao remover membro:', error);
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-[#ff0074] to-[#5600ce] flex items-center justify-center text-white font-medium">
            {membership.publicUserData.identifier?.charAt(0).toUpperCase() || '?'}
          </div>
          
          <div>
            <div className="font-medium">
              {membership.publicUserData.identifier}
              {isCurrentUser && (
                <Badge variant="secondary" className="ml-2">Você</Badge>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              Membro desde {membership.createdAt.toLocaleDateString()}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Role Selector */}
          {canManage && !isCurrentUser ? (
            <RoleSelector
              currentRole={currentRole}
              onRoleChange={handleRoleChange}
              disabled={isUpdating}
            />
          ) : (
            <Badge variant="outline">
              {getRoleDisplayName(currentRole)}
            </Badge>
          )}

          {/* Botão Remover */}
          {canManage && !isCurrentUser && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleRemoveMember}
              disabled={isRemoving}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Permissões */}
      <div className="mt-3 pt-3 border-t">
        <div className="text-xs text-muted-foreground mb-2">Permissões:</div>
        <div className="flex flex-wrap gap-1">
          {permissions.slice(0, 3).map((permission) => (
            <Badge key={permission} variant="secondary" className="text-xs">
              {getPermissionDisplayName(permission)}
            </Badge>
          ))}
          {permissions.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{permissions.length - 3} mais
            </Badge>
          )}
        </div>
      </div>
    </Card>
  );
}

/**
 * 🎯 SELETOR DE ROLE
 */
interface RoleSelectorProps {
  currentRole: ClerkRole;
  onRoleChange: (role: ClerkRole) => void;
  disabled?: boolean;
}

function RoleSelector({ currentRole, onRoleChange, disabled }: RoleSelectorProps) {
  return (
    <Select
      value={currentRole}
      onValueChange={(value) => onRoleChange(value as ClerkRole)}
      disabled={disabled}
    >
      <SelectTrigger className="w-40">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {Object.values(CLERK_ROLES).map((role) => (
          <SelectItem key={role} value={role}>
            {getRoleDisplayName(role)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

/**
 * 🏷️ HELPER FUNCTIONS
 */
function getRoleDisplayName(role: string): string {
  const roleMap = {
    'org:admin': 'Agência',
    'org:manager': 'Manager',
    'org:member': 'Member',
    'org:influencer': 'Influencer',
    'org:brand_manager': 'Brand Manager',
    'org:viewer': 'Visualizador'
  };
  return roleMap[role as keyof typeof roleMap] || role;
}

function getPermissionDisplayName(permission: string): string {
  const permissionMap = {
    'org:campaigns:create': 'Criar Campanhas',
    'org:campaigns:edit': 'Editar Campanhas',
    'org:proposals:manage': 'Gerenciar Propostas',
    'org:influencers:edit': 'Editar Influenciadores',
    'org:financials:view': 'Ver Financeiro',
    'org:users:manage': 'Gerenciar Usuários'
  };
  return permissionMap[permission as keyof typeof permissionMap] || permission.split(':').pop() || permission;
} 

