import { NextRequest, NextResponse } from 'next/server'
import { initializeFirebase, getFirebaseStorage, createStorageRef } from '@/lib/firebase-admin'

/**
 * API Route para servir thumbnails otimizados dos screenshots
 * Gera versões reduzidas das imagens para melhor performance
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Inicializar Firebase
    await initializeFirebase()
    
    // Await params conforme requerido pelo Next.js
    const resolvedParams = await params
    
    // Reconstituir o caminho do arquivo
    const filePath = resolvedParams.path.join('/')
    
    console.log(`🖼️ Buscando thumbnail: ${filePath}`)
    
    // Obter storage e criar referência para o arquivo no Firebase Storage
    const storage = await getFirebaseStorage()
    const fileRef = await createStorageRef(storage, filePath)
    
    // Verificar se o arquivo existe
    const [exists] = await fileRef.exists()
    if (!exists) {
      console.log(`❌ Arquivo não encontrado: ${filePath}`)
      return generatePlaceholderThumbnail()
    }
    
    // Obter metadados do arquivo
    const [metadata] = await fileRef.getMetadata()
    const contentType = metadata.contentType || 'image/jpeg'
    
    // Verificar se é uma imagem
    if (!contentType.startsWith('image/')) {
      console.log(`❌ Arquivo não é uma imagem: ${contentType}`)
      return generatePlaceholderThumbnail()
    }
    
    // Gerar URL assinada com cache longo para thumbnails
    const [url] = await fileRef.getSignedUrl({
      action: 'read',
      expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 dias
    })
    
    // Buscar a imagem original
    const imageResponse = await fetch(url)
    if (!imageResponse.ok) {
      throw new Error(`Erro ao buscar imagem: ${imageResponse.status}`)
    }
    
    const imageBuffer = await imageResponse.arrayBuffer()
    
    // Para thumbnails, vamos retornar a imagem com headers otimizados
    // Em uma implementação mais avançada, você poderia usar uma biblioteca
    // como Sharp para redimensionar a imagem aqui
    
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=604800, immutable', // 7 dias de cache
        'Content-Disposition': 'inline',
        'X-Thumbnail': 'true',
        // Headers para otimização
        'Accept-Ranges': 'bytes',
        'Vary': 'Accept-Encoding',
      },
    })
    
  } catch (error) {
    console.error('❌ Erro ao buscar thumbnail do Firebase Storage:', error)
    return generatePlaceholderThumbnail()
  }
}

/**
 * Gera um thumbnail placeholder em caso de erro
 */
function generatePlaceholderThumbnail() {
  const placeholderSvg = `
    <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 150">
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="#fafafa"/>
      <rect width="100%" height="100%" fill="url(#grid)"/>
      <circle cx="100" cy="75" r="20" fill="#e5e7eb"/>
      <path d="M85 70 L95 80 L115 60" stroke="#9ca3af" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      <text x="100" y="110" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">
        Screenshot
      </text>
    </svg>
  `
  
  return new NextResponse(placeholderSvg, {
    status: 200,
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=3600', // 1 hora de cache para placeholders
    },
  })
}

/**
 * Configuração da API route
 */
export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
