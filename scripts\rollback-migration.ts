import { db } from '@/lib/firebase-admin';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 🔄 SCRIPT DE ROLLBACK DE MIGRAÇÃO
 * FASE 5.5: Rollback seguro e completo da migração de dados
 */

// Interface para resultado de rollback
interface RollbackResult {
  timestamp: string;
  mode: 'full_restore' | 'migration_fields_only';
  backupSource?: string;
  collections: Array<{
    name: string;
    status: 'success' | 'failed' | 'skipped';
    documentsProcessed: number;
    documentsRestored: number;
    errors: string[];
    restorationTime: number;
  }>;
  summary: {
    totalCollections: number;
    totalDocuments: number;
    totalRestored: number;
    totalErrors: number;
    successRate: number;
    totalTime: number;
  };
  errors: string[];
}

// Configurações de rollback
const ROLLBACK_CONFIG = {
  batchSize: 100,
  confirmationRequired: true,
  createBackupBeforeRollback: true,
  validateAfterRollback: true,
  rollbackTimeout: 300000, // 5 minutos
  backupDir: './backups'
};

/**
 * Executar rollback completo a partir de backup
 */
export async function rollbackFromBackup(
  backupPath: string,
  options: {
    collections?: string[];
    dryRun?: boolean;
    skipConfirmation?: boolean;
    batchSize?: number;
  } = {}
): Promise<RollbackResult> {
  console.log('🔄 Iniciando rollback a partir de backup...');
  
  const config = { ...ROLLBACK_CONFIG, ...options };
  const startTime = Date.now();
  
  const result: RollbackResult = {
    timestamp: new Date().toISOString(),
    mode: 'full_restore',
    backupSource: backupPath,
    collections: [],
    summary: {
      totalCollections: 0,
      totalDocuments: 0,
      totalRestored: 0,
      totalErrors: 0,
      successRate: 0,
      totalTime: 0
    },
    errors: []
  };
  
  try {
    // 1. Validar backup
    console.log('📦 Validando backup...');
    const backupValidation = await validateBackup(backupPath);
    
    if (!backupValidation.valid) {
      throw new Error(`Backup inválido: ${backupValidation.error}`);
    }
    
    console.log(`✅ Backup válido: ${backupValidation.files.length} arquivos encontrados`);
    
    // 2. Confirmação de segurança
    if (config.confirmationRequired && !options.skipConfirmation) {
      console.log('\n⚠️  ATENÇÃO: Esta operação irá SOBRESCREVER dados existentes!');
      console.log(`📦 Backup fonte: ${backupPath}`);
      console.log(`📁 Arquivos: ${backupValidation.files.length}`);
      
      if (!config.dryRun) {
        const confirmation = await promptConfirmation();
        if (!confirmation) {
          console.log('❌ Rollback cancelado pelo usuário');
          return result;
        }
      }
    }
    
    // 3. Backup de segurança antes do rollback
    if (config.createBackupBeforeRollback && !config.dryRun) {
      console.log('\n💾 Criando backup de segurança antes do rollback...');
      try {
        const { backupFullSystem } = await import('./backup-firestore');
        const preRollbackBackup = await backupFullSystem();
        console.log(`✅ Backup de segurança criado: ${preRollbackBackup}`);
      } catch (error) {
        console.warn('⚠️ Não foi possível criar backup de segurança:', error);
        result.errors.push(`Backup de segurança falhou: ${error.message}`);
      }
    }
    
    // 4. Processar cada arquivo de backup
    const filesToProcess = options.collections 
      ? backupValidation.files.filter(f => options.collections!.includes(f.collection))
      : backupValidation.files;
    
    console.log(`\n🔄 Processando ${filesToProcess.length} coleções...`);
    
    for (const fileInfo of filesToProcess) {
      console.log(`\n📂 Restaurando coleção: ${fileInfo.collection}`);
      
      try {
        const collectionResult = await restoreCollectionFromBackup(
          fileInfo.path, 
          fileInfo.collection,
          { dryRun: config.dryRun, batchSize: config.batchSize }
        );
        
        result.collections.push(collectionResult);
        result.summary.totalDocuments += collectionResult.documentsProcessed;
        result.summary.totalRestored += collectionResult.documentsRestored;
        result.summary.totalErrors += collectionResult.errors.length;
        
        console.log(`✅ ${fileInfo.collection}: ${collectionResult.documentsRestored}/${collectionResult.documentsProcessed} documentos`);
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
        console.error(`❌ Erro ao restaurar ${fileInfo.collection}:`, error);
        
        result.collections.push({
          name: fileInfo.collection,
          status: 'failed',
          documentsProcessed: 0,
          documentsRestored: 0,
          errors: [errorMsg],
          restorationTime: 0
        });
        
        result.errors.push(`${fileInfo.collection}: ${errorMsg}`);
      }
    }
    
    result.summary.totalCollections = result.collections.length;
    result.summary.totalTime = Date.now() - startTime;
    result.summary.successRate = result.summary.totalCollections > 0 
      ? (result.collections.filter(c => c.status === 'success').length / result.summary.totalCollections) * 100 
      : 0;
    
    // 5. Validação pós-rollback
    if (config.validateAfterRollback && !config.dryRun) {
      console.log('\n🔍 Validando dados após rollback...');
      try {
        const { validateMigration } = await import('./validate-migration');
        await validateMigration({ collections: options.collections });
      } catch (error) {
        console.warn('⚠️ Validação pós-rollback falhou:', error);
        result.errors.push(`Validação pós-rollback: ${error.message}`);
      }
    }
    
    // Relatório final
    printRollbackReport(result, config);
    
    return result;
    
  } catch (error) {
    result.summary.totalTime = Date.now() - startTime;
    console.error('❌ Erro fatal no rollback:', error);
    result.errors.push(`Erro fatal: ${error.message}`);
    throw error;
  }
}

/**
 * Rollback apenas dos campos de migração (userId, migratedAt, etc.)
 */
export async function rollbackMigrationFields(options: {
  collections?: string[];
  dryRun?: boolean;
  batchSize?: number;
} = {}): Promise<RollbackResult> {
  console.log('🔄 Iniciando rollback dos campos de migração...');
  
  const config = { ...ROLLBACK_CONFIG, ...options };
  const startTime = Date.now();
  
  const result: RollbackResult = {
    timestamp: new Date().toISOString(),
    mode: 'migration_fields_only',
    collections: [],
    summary: {
      totalCollections: 0,
      totalDocuments: 0,
      totalRestored: 0,
      totalErrors: 0,
      successRate: 0,
      totalTime: 0
    },
    errors: []
  };
  
  const defaultCollections = ['brands', 'campaigns', 'influencers', 'groups', 'notes', 'tags', 'proposals'];
  const collectionsToProcess = options.collections || defaultCollections;
  
  try {
    for (const collectionName of collectionsToProcess) {
      console.log(`\n🔄 Processando coleção: ${collectionName}`);
      
      try {
        const collectionResult = await removeMigrationFieldsFromCollection(
          collectionName, 
          { dryRun: config.dryRun, batchSize: config.batchSize }
        );
        
        result.collections.push(collectionResult);
        result.summary.totalDocuments += collectionResult.documentsProcessed;
        result.summary.totalRestored += collectionResult.documentsRestored;
        result.summary.totalErrors += collectionResult.errors.length;
        
        console.log(`✅ ${collectionName}: ${collectionResult.documentsRestored} documentos processados`);
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
        console.error(`❌ Erro ao processar ${collectionName}:`, error);
        
        result.collections.push({
          name: collectionName,
          status: 'failed',
          documentsProcessed: 0,
          documentsRestored: 0,
          errors: [errorMsg],
          restorationTime: 0
        });
        
        result.errors.push(`${collectionName}: ${errorMsg}`);
      }
    }
    
    result.summary.totalCollections = result.collections.length;
    result.summary.totalTime = Date.now() - startTime;
    result.summary.successRate = result.summary.totalCollections > 0 
      ? (result.collections.filter(c => c.status === 'success').length / result.summary.totalCollections) * 100 
      : 0;
    
    printRollbackReport(result, config);
    
    return result;
    
  } catch (error) {
    result.summary.totalTime = Date.now() - startTime;
    console.error('❌ Erro fatal no rollback de campos:', error);
    result.errors.push(`Erro fatal: ${error.message}`);
    throw error;
  }
}

/**
 * Validar backup antes do rollback
 */
async function validateBackup(backupPath: string): Promise<{
  valid: boolean;
  error?: string;
  files: Array<{ collection: string; path: string; size: number; documents: number }>;
}> {
  try {
    // Verificar se é um diretório de backup ou arquivo único
    const stats = fs.statSync(backupPath);
    
    if (stats.isDirectory()) {
      // Backup completo (diretório)
      const files = fs.readdirSync(backupPath)
        .filter(file => file.endsWith('.json') && file.startsWith('backup-'))
        .map(file => {
          const filePath = path.join(backupPath, file);
          const fileStats = fs.statSync(filePath);
          
          // Extrair nome da coleção do nome do arquivo
          const collection = file.match(/backup-(.+?)-/)?.[1] || 'unknown';
          
          // Ler arquivo para contar documentos
          let documentCount = 0;
          try {
            const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            documentCount = content.count || content.documents?.length || 0;
          } catch (error) {
            console.warn(`Erro ao ler ${file}:`, error);
          }
          
          return {
            collection,
            path: filePath,
            size: fileStats.size,
            documents: documentCount
          };
        });
      
      if (files.length === 0) {
        return { valid: false, error: 'Nenhum arquivo de backup encontrado no diretório', files: [] };
      }
      
      return { valid: true, files };
      
    } else {
      // Arquivo único
      const content = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
      
      if (!content.collection || !content.documents) {
        return { valid: false, error: 'Formato de backup inválido', files: [] };
      }
      
      return {
        valid: true,
        files: [{
          collection: content.collection,
          path: backupPath,
          size: stats.size,
          documents: content.count || content.documents.length
        }]
      };
    }
    
  } catch (error) {
    return { 
      valid: false, 
      error: `Erro ao validar backup: ${error.message}`, 
      files: [] 
    };
  }
}

/**
 * Restaurar coleção a partir de backup
 */
async function restoreCollectionFromBackup(
  backupFile: string, 
  collectionName: string,
  options: { dryRun?: boolean; batchSize?: number } = {}
): Promise<{
  name: string;
  status: 'success' | 'failed' | 'skipped';
  documentsProcessed: number;
  documentsRestored: number;
  errors: string[];
  restorationTime: number;
}> {
  const startTime = Date.now();
  const config = { dryRun: false, batchSize: 100, ...options };
  
  const result = {
    name: collectionName,
    status: 'success' as const,
    documentsProcessed: 0,
    documentsRestored: 0,
    errors: [] as string[],
    restorationTime: 0
  };
  
  try {
    // Ler arquivo de backup
    const backupContent = JSON.parse(fs.readFileSync(backupFile, 'utf8'));
    const documents = backupContent.documents || [];
    
    result.documentsProcessed = documents.length;
    
    if (documents.length === 0) {
      console.log(`⚠️ Nenhum documento encontrado no backup de ${collectionName}`);
      result.status = 'skipped';
      return result;
    }
    
    console.log(`📄 Restaurando ${documents.length} documentos...`);
    
    // Processar em lotes
    const batches = [];
    for (let i = 0; i < documents.length; i += config.batchSize) {
      batches.push(documents.slice(i, i + config.batchSize));
    }
    
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`  📦 Lote ${batchIndex + 1}/${batches.length}: ${batch.length} documentos`);
      
      if (!config.dryRun) {
        const firebaseBatch = db.batch();
        
        for (const docData of batch) {
          const docRef = db.collection(collectionName).doc(docData.id);
          
          // Converter datas ISO de volta para Timestamps se necessário
          const processedData = convertDatesForFirestore(docData.data);
          
          firebaseBatch.set(docRef, processedData);
        }
        
        await firebaseBatch.commit();
      }
      
      result.documentsRestored += batch.length;
    }
    
    result.restorationTime = Date.now() - startTime;
    
    if (config.dryRun) {
      console.log(`🔍 [DRY RUN] ${result.documentsRestored} documentos seriam restaurados`);
    } else {
      console.log(`✅ ${result.documentsRestored} documentos restaurados com sucesso`);
    }
    
    return result;
    
  } catch (error) {
    result.status = 'failed';
    result.restorationTime = Date.now() - startTime;
    const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
    result.errors.push(errorMsg);
    
    console.error(`❌ Erro na restauração de ${collectionName}:`, error);
    return result;
  }
}

/**
 * Remover apenas campos de migração de uma coleção
 */
async function removeMigrationFieldsFromCollection(
  collectionName: string,
  options: { dryRun?: boolean; batchSize?: number } = {}
): Promise<{
  name: string;
  status: 'success' | 'failed' | 'skipped';
  documentsProcessed: number;
  documentsRestored: number;
  errors: string[];
  restorationTime: number;
}> {
  const startTime = Date.now();
  const config = { dryRun: false, batchSize: 100, ...options };
  
  const result = {
    name: collectionName,
    status: 'success' as const,
    documentsProcessed: 0,
    documentsRestored: 0,
    errors: [] as string[],
    restorationTime: 0
  };
  
  try {
    const snapshot = await db.collection(collectionName).get();
    result.documentsProcessed = snapshot.size;
    
    if (snapshot.empty) {
      result.status = 'skipped';
      return result;
    }
    
    // Filtrar documentos que têm campos de migração
    const docsToUpdate = snapshot.docs.filter(doc => {
      const data = doc.data();
      return data.userId || data.migratedAt || data._migrationMetadata;
    });
    
    if (docsToUpdate.length === 0) {
      console.log(`⚠️ Nenhum documento com campos de migração em ${collectionName}`);
      result.status = 'skipped';
      return result;
    }
    
    console.log(`🔄 Removendo campos de migração de ${docsToUpdate.length} documentos...`);
    
    // Processar em lotes
    const batches = [];
    for (let i = 0; i < docsToUpdate.length; i += config.batchSize) {
      batches.push(docsToUpdate.slice(i, i + config.batchSize));
    }
    
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`  📦 Lote ${batchIndex + 1}/${batches.length}: ${batch.length} documentos`);
      
      if (!config.dryRun) {
        const firebaseBatch = db.batch();
        
        for (const doc of batch) {
          // Usar FieldValue.delete() para remover campos
          const admin = require('firebase-admin');
          const updateData: any = {};
          
          const data = doc.data();
          if (data.userId) updateData.userId = admin.firestore.FieldValue.delete();
          if (data.migratedAt) updateData.migratedAt = admin.firestore.FieldValue.delete();
          if (data._migrationMetadata) updateData._migrationMetadata = admin.firestore.FieldValue.delete();
          
          if (Object.keys(updateData).length > 0) {
            firebaseBatch.update(doc.ref, updateData);
          }
        }
        
        await firebaseBatch.commit();
      }
      
      result.documentsRestored += batch.length;
    }
    
    result.restorationTime = Date.now() - startTime;
    
    if (config.dryRun) {
      console.log(`🔍 [DRY RUN] Campos de migração seriam removidos de ${result.documentsRestored} documentos`);
    } else {
      console.log(`✅ Campos removidos de ${result.documentsRestored} documentos`);
    }
    
    return result;
    
  } catch (error) {
    result.status = 'failed';
    result.restorationTime = Date.now() - startTime;
    const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
    result.errors.push(errorMsg);
    
    console.error(`❌ Erro ao remover campos de ${collectionName}:`, error);
    return result;
  }
}

// Utilitários
function convertDatesForFirestore(data: any): any {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(data)) {
    // Converter string ISO para Timestamp do Firestore
    const admin = require('firebase-admin');
    return admin.firestore.Timestamp.fromDate(new Date(data));
  }
  
  if (Array.isArray(data)) {
    return data.map(convertDatesForFirestore);
  }
  
  if (typeof data === 'object') {
    const converted: any = {};
    for (const [key, value] of Object.entries(data)) {
      converted[key] = convertDatesForFirestore(value);
    }
    return converted;
  }
  
  return data;
}

async function promptConfirmation(): Promise<boolean> {
  // Em ambiente de produção, implementar prompt real
  // Por enquanto, retorna true para testes
  console.log('⚠️ Implementar prompt de confirmação para produção');
  return true;
}

function printRollbackReport(result: RollbackResult, config: any): void {
  console.log('\n📋 RELATÓRIO DE ROLLBACK');
  console.log('========================');
  console.log(`⏰ Data/Hora: ${new Date(result.timestamp).toLocaleString('pt-BR')}`);
  console.log(`🔧 Modo: ${result.mode === 'full_restore' ? 'Restauração Completa' : 'Remoção de Campos'}`);
  console.log(`⏱️ Tempo total: ${formatDuration(result.summary.totalTime)}`);
  
  if (result.backupSource) {
    console.log(`📦 Backup fonte: ${result.backupSource}`);
  }
  
  console.log('\n📊 RESUMO:');
  console.log(`📁 Coleções processadas: ${result.summary.totalCollections}`);
  console.log(`📄 Documentos processados: ${result.summary.totalDocuments}`);
  console.log(`✅ Documentos restaurados: ${result.summary.totalRestored}`);
  console.log(`❌ Erros: ${result.summary.totalErrors}`);
  console.log(`📈 Taxa de sucesso: ${result.summary.successRate.toFixed(1)}%`);
  
  console.log('\n🏆 STATUS POR COLEÇÃO:');
  result.collections.forEach(col => {
    const icon = col.status === 'success' ? '✅' : col.status === 'failed' ? '❌' : '⏭️';
    console.log(`${icon} ${col.name}: ${col.documentsRestored}/${col.documentsProcessed} documentos (${formatDuration(col.restorationTime)})`);
    
    if (col.errors.length > 0) {
      console.log(`   ❌ Erros: ${col.errors.join(', ')}`);
    }
  });
  
  if (result.errors.length > 0) {
    console.log('\n❌ ERROS GERAIS:');
    result.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }
  
  console.log('\n🎯 STATUS FINAL:');
  if (result.summary.successRate === 100 && result.errors.length === 0) {
    console.log('✅ ROLLBACK CONCLUÍDO COM SUCESSO!');
  } else {
    console.log('⚠️ ROLLBACK CONCLUÍDO COM PROBLEMAS');
  }
  
  if (config.dryRun) {
    console.log('\n🔍 ESTE FOI UM DRY RUN - Nenhuma alteração foi feita');
  }
}

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Função principal para execução via linha de comando
 */
export async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const dryRun = args.includes('--dry-run');
  const skipConfirmation = args.includes('--skip-confirmation');
  const collectionsArg = args.find(arg => arg.startsWith('--collections='));
  const backupPathArg = args.find(arg => arg.startsWith('--backup-path='));
  
  const collections = collectionsArg ? collectionsArg.split('=')[1].split(',') : undefined;
  const backupPath = backupPathArg?.split('=')[1];
  
  try {
    switch (command) {
      case 'full':
        if (!backupPath) {
          throw new Error('Caminho do backup é obrigatório para rollback completo. Use --backup-path=<path>');
        }
        
        console.log('🔄 Iniciando rollback completo...\n');
        await rollbackFromBackup(backupPath, {
          collections,
          dryRun,
          skipConfirmation
        });
        break;
        
      case 'fields':
      default:
        console.log('🔄 Iniciando rollback de campos de migração...\n');
        await rollbackMigrationFields({
          collections,
          dryRun
        });
        break;
    }
    
    console.log('\n✅ Rollback concluído');
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ Erro no rollback:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 

