/**
 * Script para recriar a estrutura de dados dos influenciadores no Firebase
 * Este script define a nova estrutura organizada e cria funções para migração
 */

import { db } from '../lib/firebase';
import { collection, doc, setDoc, addDoc, getDocs } from 'firebase/firestore';

// ===== INTERFACES PARA A NOVA ESTRUTURA =====

// Interface para gênero da audiência
export interface AudienceGender {
  male: number;      // Percentual masculino (0-100)
  female: number;    // Percentual feminino (0-100)
  other: number;     // Percentual outros (0-100)
}

// Interface para faixas etárias da audiência
export interface AudienceAgeRange {
  "13-17": number;
  "18-24": number;
  "25-34": number;
  "35-44": number;
  "45-54": number;
  "55-64": number;
  "65+": number;
}

// Interface para localização da audiência
export interface AudienceLocation {
  country: string;
  state?: string;
  city?: string;
  percentage: number;
}

// Interface para dados de rede social
export interface SocialNetworkData {
  username: string;
  followers: number;
  avgViews?: number;
  engagementRate: number;
  screenshots: string[];  // URLs das capturas de tela
  audienceGender: AudienceGender;
  audienceAgeRanges: AudienceAgeRange;
  audienceLocations: AudienceLocation[];
  
  // Campos específicos por plataforma
  storiesViews?: number;    // Instagram - visualizações de stories
  storyPrice?: number;      // Instagram - preço por story
  reelPrice?: number;       // Instagram - preço por reel
  price?: number;           // TikTok - preço por vídeo
  dedicatedPrice?: number;  // YouTube - preço por vídeo dedicado
  insertionPrice?: number;  // YouTube - preço por inserção
  shortsPrice?: number;     // YouTube - preço por shorts
}

// Interface para informações pessoais
export interface PersonalInfo {
  name: string;
  age: number;
  gender: string;  // 'M', 'F', 'Outro'
  bio: string;
  avatarUrl: string;
  backgroundImageUrl?: string;
  verified: boolean;
  location: {
    city: string;
    state: string;
    country: string;
    cep?: string;
    formattedLocation?: string;  // "Cidade - Estado"
  };
}

// Interface para informações de contato
export interface ContactInfo {
  email: string;
  whatsapp: string;
}

// Interface para informações de negócio
export interface BusinessInfo {
  agency: {
    name: string;
    responsibleName: string;
    responsibleCapturer: string;
  };
  mainCategories: string[];  // IDs das categorias principais
  promotesTraders: boolean;
  contentType: string[];     // Tipos de conteúdo que produz
  brandHistory: {
    instagram: string[];     // IDs ou nomes das marcas
    tiktok: string[];
    youtube: string[];
    facebook?: string[];
    twitch?: string[];
    kwai?: string[];
  };
}

// Interface para métricas gerais
export interface Metrics {
  totalFollowers: number;
  overallEngagementRate: number;
  rating?: number;  // Classificação interna (0-5)
}

// Interface para dados financeiros (separada)
export interface FinancialData {
  influencerId: string;
  prices: {
    instagramStory: number;
    instagramReel: number;
    tiktokVideo: number;
    youtubeInsertion: number;
    youtubeDedicated: number;
    youtubeShorts: number;
  };
  instagramStoriesViews: number;
  brandHistory: {
    instagram: string[];
    tiktok: string[];
    youtube: string[];
  };
  additionalData: {
    contentType: string[];
    promotesTraders: boolean;
    responsibleRecruiter: string;
    socialMediaScreenshots: string[];
    notes: string;
    documents: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

// ===== ESTRUTURA PRINCIPAL DO INFLUENCIADOR =====
export interface InfluencerStructured {
  id?: string;
  personalInfo: PersonalInfo;
  contactInfo: ContactInfo;
  businessInfo: BusinessInfo;
  socialNetworks: {
    instagram?: SocialNetworkData;
    tiktok?: SocialNetworkData;
    youtube?: SocialNetworkData;
    facebook?: SocialNetworkData;
    twitch?: SocialNetworkData;
    kwai?: SocialNetworkData;
  };
  metrics: Metrics;
  tags?: string[];           // IDs das tags associadas
  notes?: string[];          // IDs das notas associadas
  createdAt: Date;
  updatedAt: Date;
}

// ===== FUNÇÕES DE UTILIDADE =====

/**
 * Converte dados do formulário antigo para a nova estrutura
 */
export function convertFormDataToStructured(formData: any): InfluencerStructured {
  const now = new Date();
  
  // Processar redes sociais
  const socialNetworks: { [key: string]: SocialNetworkData } = {};
  
  // Instagram
  if (formData.instagram_username || formData.instagram) {
    socialNetworks.instagram = {
      username: formData.instagram_username || '',
      followers: parseNumericValue(formData.instagram) || 0,
      avgViews: parseNumericValue(formData.instagram_views) || 0,
      engagementRate: parseFloat(formData.instagram_engagement_rate) || 0,
      screenshots: [],
      audienceGender: formData.audienceGender?.instagram || { male: 0, female: 0, other: 0 },
      audienceAgeRanges: formData.audienceAgeRanges?.instagram || {
        "13-17": 0, "18-24": 0, "25-34": 0, "35-44": 0,
        "45-54": 0, "55-64": 0, "65+": 0
      },
      audienceLocations: formData.audienceLocations?.instagram || [],
      storiesViews: parseNumericValue(formData.stories_views) || 0,
      storyPrice: parseNumericValue(formData.story_price) || 0,
      reelPrice: parseNumericValue(formData.reels_price) || 0
    };
  }
  
  // TikTok
  if (formData.tiktok_username || formData.tiktok) {
    socialNetworks.tiktok = {
      username: formData.tiktok_username || '',
      followers: parseNumericValue(formData.tiktok) || 0,
      avgViews: parseNumericValue(formData.tiktok_views) || 0,
      engagementRate: parseFloat(formData.tiktok_engagement_rate) || 0,
      screenshots: [],
      audienceGender: formData.audienceGender?.tiktok || { male: 0, female: 0, other: 0 },
      audienceAgeRanges: formData.audienceAgeRanges?.tiktok || {
        "13-17": 0, "18-24": 0, "25-34": 0, "35-44": 0,
        "45-54": 0, "55-64": 0, "65+": 0
      },
      audienceLocations: formData.audienceLocations?.tiktok || [],
      price: parseNumericValue(formData.tiktok_price) || 0
    };
  }
  
  // YouTube
  if (formData.youtube_username || formData.youtube) {
    socialNetworks.youtube = {
      username: formData.youtube_username || '',
      followers: parseNumericValue(formData.youtube) || 0,
      avgViews: parseNumericValue(formData.youtube_views) || 0,
      engagementRate: parseFloat(formData.youtube_engagement_rate) || 0,
      screenshots: [],
      audienceGender: formData.audienceGender?.youtube || { male: 0, female: 0, other: 0 },
      audienceAgeRanges: formData.audienceAgeRanges?.youtube || {
        "13-17": 0, "18-24": 0, "25-34": 0, "35-44": 0,
        "45-54": 0, "55-64": 0, "65+": 0
      },
      audienceLocations: formData.audienceLocations?.youtube || [],
      dedicatedPrice: parseNumericValue(formData.video_dedicated_price) || 0,
      insertionPrice: parseNumericValue(formData.video_insertion_price) || 0,
      shortsPrice: parseNumericValue(formData.shorts_price) || 0
    };
  }
  
  // Calcular total de seguidores
  const totalFollowers = Object.values(socialNetworks)
    .reduce((total, network) => total + (network.followers || 0), 0);
  
  // Calcular engajamento médio
  const avgEngagement = Object.values(socialNetworks)
    .reduce((total, network, _, arr) => total + (network.engagementRate || 0) / arr.length, 0);
  
  return {
    personalInfo: {
      name: formData.name || '',
      age: parseInt(formData.age) || 0,
      gender: formData.gender || '',
      bio: formData.bio || '',
      avatarUrl: formData.avatarUrl || formData.avatarPreview || '',
      backgroundImageUrl: formData.backgroundImageUrl || '',
      verified: formData.verified || false,
      location: {
        city: formData.city || '',
        state: formData.state || '',
        country: formData.country || 'Brasil',
        cep: formData.cep || '',
        formattedLocation: `${formData.city || ''} - ${formData.state || ''}`
      }
    },
    contactInfo: {
      email: formData.email || '',
      whatsapp: formData.whatsapp || ''
    },
    businessInfo: {
      agency: {
        name: formData.agencyName || '',
        responsibleName: formData.responsibleName || '',
        responsibleCapturer: formData.responsibleCapturer || ''
      },
      mainCategories: Array.isArray(formData.mainCategories) ? formData.mainCategories : [],
      promotesTraders: formData.promotesTraders || false,
      contentType: formData.contentType || [],
      brandHistory: {
        instagram: formData.brandHistory?.instagram || [],
        tiktok: formData.brandHistory?.tiktok || [],
        youtube: formData.brandHistory?.youtube || []
      }
    },
    socialNetworks,
    metrics: {
      totalFollowers,
      overallEngagementRate: avgEngagement,
      rating: parseFloat(formData.rating) || 4.0
    },
    tags: formData.tags || [],
    notes: formData.notes || [],
    createdAt: formData.createdAt || now,
    updatedAt: now
  };
}

/**
 * Converte valores numéricos formatados (ex: "1.234.567") para número
 */
function parseNumericValue(value: any): number {
  if (!value) return 0;
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    // Remove pontos de formatação e converte vírgulas em pontos
    const cleaned = value.replace(/\./g, '').replace(/,/g, '.');
    const num = parseFloat(cleaned);
    return isNaN(num) ? 0 : num;
  }
  return 0;
}

/**
 * Cria dados financeiros separados
 */
export function createFinancialData(influencerId: string, formData: any): FinancialData {
  const now = new Date();
  
  return {
    influencerId,
    prices: {
      instagramStory: parseNumericValue(formData.story_price) || 0,
      instagramReel: parseNumericValue(formData.reels_price) || 0,
      tiktokVideo: parseNumericValue(formData.tiktok_price) || 0,
      youtubeInsertion: parseNumericValue(formData.video_insertion_price) || 0,
      youtubeDedicated: parseNumericValue(formData.video_dedicated_price) || 0,
      youtubeShorts: parseNumericValue(formData.shorts_price) || 0
    },
    instagramStoriesViews: parseNumericValue(formData.stories_views) || 0,
    brandHistory: {
      instagram: formData.brandHistory?.instagram || [],
      tiktok: formData.brandHistory?.tiktok || [],
      youtube: formData.brandHistory?.youtube || []
    },
    additionalData: {
      contentType: formData.contentType || [],
      promotesTraders: formData.promotesTraders || false,
      responsibleRecruiter: formData.responsibleCapturer || '',
      socialMediaScreenshots: [],
      notes: formData.observation || '',
      documents: []
    },
    createdAt: formData.createdAt || now,
    updatedAt: now
  };
}

// ===== FUNÇÕES DE FIREBASE =====

/**
 * Adiciona um novo influenciador com a estrutura organizada
 */
export async function addStructuredInfluencer(formData: any): Promise<string> {
  try {
    // Converter dados do formulário para estrutura organizada
    const influencerData = convertFormDataToStructured(formData);
    
    // Adicionar influenciador na coleção principal
    const docRef = await addDoc(collection(db, 'influencers'), influencerData);
    const influencerId = docRef.id;
    
    // Criar dados financeiros separados
    const financialData = createFinancialData(influencerId, formData);
    await addDoc(collection(db, 'influencer_financials'), financialData);
    
    console.log('✅ Influenciador criado com sucesso:', influencerId);
    return influencerId;
  } catch (error) {
    console.error('❌ Erro ao criar influenciador:', error);
    throw error;
  }
}

/**
 * Atualiza um influenciador existente
 */
export async function updateStructuredInfluencer(id: string, formData: any): Promise<void> {
  try {
    // Converter dados do formulário para estrutura organizada
    const influencerData = convertFormDataToStructured(formData);
    influencerData.updatedAt = new Date();
    
    // Atualizar influenciador na coleção principal
    await setDoc(doc(db, 'influencers', id), influencerData, { merge: true });
    
    // Atualizar dados financeiros
    const financialData = createFinancialData(id, formData);
    
    // Buscar documento financeiro existente
    const financialQuery = await getDocs(
      collection(db, 'influencer_financials')
    );
    
    let financialDocId: string | null = null;
    financialQuery.forEach((doc) => {
      if (doc.data().influencerId === id) {
        financialDocId = doc.id;
      }
    });
    
    if (financialDocId) {
      // Atualizar documento existente
      await setDoc(doc(db, 'influencer_financials', financialDocId), financialData, { merge: true });
    } else {
      // Criar novo documento financeiro
      await addDoc(collection(db, 'influencer_financials'), financialData);
    }
    
    console.log('✅ Influenciador atualizado com sucesso:', id);
  } catch (error) {
    console.error('❌ Erro ao atualizar influenciador:', error);
    throw error;
  }
}

/**
 * Cria as coleções básicas necessárias
 */
export async function createBasicCollections(): Promise<void> {
  try {
    console.log('🔄 Criando coleções básicas...');
    
    // Criar documento de exemplo para inicializar as coleções
    const collections = [
      'influencers',
      'influencer_financials',
      'categories',
      'brands',
      'campaigns',
      'proposals',
      'proposal_templates',
      'tags',
      'notes'
    ];
    
    for (const collectionName of collections) {
      const exampleDoc = {
        _example: true,
        createdAt: new Date(),
        description: `Coleção ${collectionName} inicializada`
      };
      
      await addDoc(collection(db, collectionName), exampleDoc);
      console.log(`✅ Coleção ${collectionName} criada`);
    }
    
    console.log('✅ Todas as coleções básicas foram criadas!');
  } catch (error) {
    console.error('❌ Erro ao criar coleções básicas:', error);
    throw error;
  }
}

// ===== EXEMPLO DE USO =====

/**
 * Exemplo de como usar as funções
 */
export async function exemploDeUso() {
  // Dados de exemplo do formulário
  const dadosFormulario = {
    name: "João Silva",
    age: "25",
    gender: "M",
    bio: "Criador de conteúdo focado em tecnologia",
    city: "São Paulo",
    state: "SP",
    country: "Brasil",
    email: "<EMAIL>",
    whatsapp: "+5511999999999",
    instagram_username: "@joaosilva",
    instagram: "100.000",
    instagram_views: "50.000",
    instagram_engagement_rate: "3.5",
    story_price: "500",
    reels_price: "800",
    mainCategories: ["tecnologia", "lifestyle"],
    verified: true
  };
  
  try {
    // Criar influenciador
    const influencerId = await addStructuredInfluencer(dadosFormulario);
    console.log('Influenciador criado com ID:', influencerId);
    
    // Atualizar influenciador
    dadosFormulario.bio = "Bio atualizada";
    await updateStructuredInfluencer(influencerId, dadosFormulario);
    console.log('Influenciador atualizado!');
    
  } catch (error) {
    console.error('Erro no exemplo:', error);
  }
}

