import { z } from 'zod';

// Schema base para todos os documentos com isolamento
export const BaseDocumentSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  userId: z.string().min(1, 'userId é obrigatório para isolamento'),
  createdAt: z.date(),
  updatedAt: z.date()
});

// Schema para documentos que rastreiam criação/atualização
export const UserOwnedDocumentSchema = BaseDocumentSchema.extend({
  createdBy: z.string().min(1, 'createdBy é obrigatório'),
  updatedBy: z.string().min(1, 'updatedBy é obrigatório')
});

// Schema para documentos compartilháveis (futuro)
export const ShareableDocumentSchema = BaseDocumentSchema.extend({
  isShared: z.boolean(),
  sharedWith: z.array(z.string()).optional(),
  sharePermissions: z.enum(['read', 'write', 'admin']).optional()
});

// Schema para enum de status de documentos (usando valores do enum)
export const DocumentStatusSchema = z.enum(['active', 'inactive', 'draft', 'archived'] as const);

// Schema para metadados de documentos
export const DocumentMetadataSchema = z.object({
  version: z.number().int().min(1),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
  lastModifiedBy: z.string().optional(),
  isArchived: z.boolean().optional()
});

// Utilitário para validar ownership
export function validateOwnership(document: any, userId: string): boolean {
  if (!document || typeof document !== 'object') {
    return false;
  }
  
  if (!document.userId) {
    return false;
  }
  
  return document.userId === userId;
}

// Utilitário para validar relacionamento entre entidades
export function validateRelationship(
  parentDocument: any, 
  childDocument: any
): boolean {
  if (!parentDocument?.userId || !childDocument?.userId) {
    return false;
  }
  
  return parentDocument.userId === childDocument.userId;
}

// Função para adicionar userId automaticamente em dados de criação
export function addUserIdToData<T extends Record<string, any>>(
  data: T, 
  userId: string
): T & { userId: string; createdAt: Date; updatedAt: Date } {
  const now = new Date();
  
  return {
    ...data,
    userId,
    createdAt: now,
    updatedAt: now
  };
}

// Função para preparar dados de atualização
export function prepareUpdateData<T extends Record<string, any>>(
  data: T,
  updatedBy?: string
): T & { updatedAt: Date; updatedBy?: string } {
  const updateData: any = {
    ...data,
    updatedAt: new Date()
  };
  
  if (updatedBy) {
    updateData.updatedBy = updatedBy;
  }
  
  return updateData;
}

// Schema para validação de filtros comuns
export const CommonFiltersSchema = z.object({
  userId: z.string().min(1).optional(),
  search: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.array(z.string()).optional(),
  createdAtFrom: z.date().optional(),
  createdAtTo: z.date().optional(),
  updatedAtFrom: z.date().optional(),
  updatedAtTo: z.date().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// Tipos TypeScript derivados dos schemas
export type BaseDocumentValidation = z.infer<typeof BaseDocumentSchema>;
export type UserOwnedDocumentValidation = z.infer<typeof UserOwnedDocumentSchema>;
export type ShareableDocumentValidation = z.infer<typeof ShareableDocumentSchema>;
export type DocumentStatusValidation = z.infer<typeof DocumentStatusSchema>;
export type DocumentMetadataValidation = z.infer<typeof DocumentMetadataSchema>;
export type CommonFiltersValidation = z.infer<typeof CommonFiltersSchema>;

// Constantes para mensagens de erro padrão
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'Este campo é obrigatório',
  INVALID_USER_ID: 'userId inválido',
  OWNERSHIP_DENIED: 'Usuário não tem permissão para acessar este recurso',
  RELATIONSHIP_INVALID: 'Relacionamento entre entidades inválido',
  INVALID_DATE: 'Data inválida',
  INVALID_EMAIL: 'Email inválido',
  INVALID_URL: 'URL inválida',
  INVALID_PHONE: 'Número de telefone inválido',
  MIN_LENGTH: (min: number) => `Deve ter pelo menos ${min} caracteres`,
  MAX_LENGTH: (max: number) => `Deve ter no máximo ${max} caracteres`,
  MIN_VALUE: (min: number) => `Deve ser pelo menos ${min}`,
  MAX_VALUE: (max: number) => `Deve ser no máximo ${max}`
} as const; 

