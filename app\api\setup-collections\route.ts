/**
 * API Route para configurar as coleções do Firebase
 * Acesse: /api/setup-collections para executar a configuração
 */

import { NextRequest, NextResponse } from 'next/server';
import { setupFirebaseCollections, createTestInfluencer } from '../../../scripts/setup-firebase-collections';

/**
 * POST - Configurar coleções do Firebase
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Iniciando configuração via API...');
    
    const result = await setupFirebaseCollections();
    
    console.log('✅ Configuração concluída via API!');
    
    return NextResponse.json({
      success: true,
      message: 'Configuração das coleções concluída com sucesso!',
      data: result
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('❌ Erro na configuração via API:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro ao configurar coleções',
      details: error.message
    }, { status: 500 });
  }
}

/**
 * GET - Verificar status das coleções
 */
export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      message: 'API de configuração das coleções está funcionando!',
      timestamp: new Date().toISOString(),
      endpoint: '/api/setup-collections'
    });
  } catch (error: any) {
    return NextResponse.json({
      error: 'Erro na API',
      details: error.message
    }, { status: 500 });
  }
}

