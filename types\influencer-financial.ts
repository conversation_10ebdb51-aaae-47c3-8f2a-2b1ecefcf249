// Modelos de dados para a parte financeira dos influenciadores

// Interface para um serviço individual
export interface ServicePrice {
  name: string;    // Nome do serviço (ex: "Stories Instagram", "Reels Instagram")
  price: number;   // Preço do serviço
}

// Interface para preços
export interface InfluencerPrices {
  instagramStory: ServicePrice;     // Preço de Story 1 minuto
  instagramReel: ServicePrice;      // Preço de Reels
  tiktokVideo: ServicePrice;        // Preço de vídeo no TikTok
  youtubeInsertion: ServicePrice;   // Preço inserção (vídeo longo)
  youtubeDedicated: ServicePrice;   // Preço dedicado (vídeo longo)
  youtubeShorts: ServicePrice;      // Preço Shorts patrocinado
}

// Interface para histórico de marcas
export interface BrandHistory {
  instagram: string[];        // Marcas que já trabalhou (Instagram)
  tiktok: string[];           // <PERSON><PERSON> que já trabalhou (TikTok)
  youtube: string[];          // Marcas que já trabalhou (YouTube)
}

// Interface para dados adicionais
export interface AdditionalData {
  contentType: string[];           // Tipos de conteúdo
  promotesTraders: boolean;        // Divulga traders?
  responsibleRecruiter: string;    // Captador responsável
  socialMediaScreenshots: string[]; // URLs dos prints dos dados das redes sociais
  notes: string;                   // Observações gerais
  documents?: {                    // Documentos do influenciador
    name: string;                  // Nome do documento
    url: string;                   // URL do documento
    type: string;                  // Tipo do documento (extensão)
    uploadedAt: Date;              // Data de upload
  }[];
}

// Interface principal para dados financeiros
export interface InfluencerFinancial {
  id: string;                  // ID único deste registro financeiro
  influencerId: string;        // Referência ao influencer básico
  responsibleName: string;     // Nome do responsável
  agencyName?: string;         // Nome da assessoria (opcional)
  email: string;               // E-mail de contato
  whatsapp: string;            // WhatsApp
  
  // Visualizações em stories (específico do Instagram)
  instagramStoriesViews: number;
  
  // Preços
  prices: InfluencerPrices;
  
  // Histórico de marcas
  brandHistory: BrandHistory;
  
  // Dados adicionais
  additionalData: AdditionalData;
  
  createdAt: Date;             // Data de cadastro
  updatedAt: Date;             // Data de última atualização
}


