const { initializeApp } = require('firebase/app');
const { getFirestore, collection, doc, setDoc } = require('firebase/firestore');

// Configuração do Firebase (usando variáveis de ambiente ou config padrão)
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "AIzaSyBJqL8QVQqQQQQQQQQQQQQQQQQQQQQQQQQ",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "deumatch-demo.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "deumatch-demo",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "deumatch-demo.appspot.com",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:123456789:web:abcdef"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function createBrandInfluencersCollection() {
  try {
    console.log('Criando coleção brand_influencers...');
    
    // Criar documento inicial para garantir que a coleção existe
    const docRef = doc(collection(db, 'brand_influencers'), 'init');
    await setDoc(docRef, {
      created: new Date(),
      description: 'Coleção para relacionamentos entre marcas e influenciadores',
      version: '1.0.0',
      fields: {
        brandId: 'string - ID da marca',
        brandName: 'string - Nome da marca',
        influencerId: 'string - ID do influenciador',
        influencerName: 'string - Nome do influenciador',
        influencerData: 'object - Dados completos do influenciador',
        status: 'string - Status do relacionamento (enviado, visualizado, interessado, rejeitado, proposta_enviada)',
        sentAt: 'timestamp - Data de envio',
        viewedAt: 'timestamp - Data de visualização (opcional)',
        lastInteractionAt: 'timestamp - Última interação (opcional)',
        notes: 'string - Observações (opcional)',
        createdBy: 'string - Usuário que criou o relacionamento',
        updatedAt: 'timestamp - Data da última atualização'
      }
    });
    
    console.log('✅ Coleção brand_influencers criada com sucesso!');
    console.log('📄 Documento de inicialização criado com ID: init');
    
  } catch (error) {
    console.error('❌ Erro ao criar coleção brand_influencers:', error);
    process.exit(1);
  }
}

// Executar a função
createBrandInfluencersCollection()
  .then(() => {
    console.log('🎉 Processo concluído!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error);
    process.exit(1);
  });
