import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/lib/firebase';

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ userId: string; categoryId: string }> }
) {
  try {
    const params = await context.params;
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId || clerkUserId !== params.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar se a categoria existe e pertence ao usuário
    const categoryRef = db.collection('categories').doc(params.categoryId);
    const categoryDoc = await categoryRef.get();

    if (!categoryDoc.exists) {
      return NextResponse.json({ error: 'Categoria não encontrada' }, { status: 404 });
    }

    const categoryData = categoryDoc.data();
    if (categoryData?.userId !== params.userId) {
      return NextResponse.json({ error: 'Não autorizado a remover esta categoria' }, { status: 403 });
    }

    // Remover a categoria
    await categoryRef.delete();

    return NextResponse.json({ success: true, message: 'Categoria removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover categoria:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 