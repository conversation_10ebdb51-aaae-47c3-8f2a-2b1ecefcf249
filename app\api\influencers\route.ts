import { NextResponse } from 'next/server';
import { getAllInfluencers, getInfluencerById, getInfluencersByIds, addInfluencer, updateInfluencer, deleteInfluencer, getCategoryById } from '@/lib/firebase';
import { getFinancialByInfluencerId } from '@/lib/firebase-financials';

// Util para converter strings numéricas (com pontos como separadores de milhar) em número
const parseCurrency = (value: any): number => {
  if (value === undefined || value === null) return 0;
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    // remover espaços e símbolos não numéricos exceto vírgula e ponto
    const cleaned = value.replace(/[^0-9.,-]/g, '');
    // se tiver mais de um ponto, assumimos que são separadores de milhar
    const normalized = cleaned.replace(/\./g, '').replace(/,/g, '.');
    const num = parseFloat(normalized);
    return isNaN(num) ? 0 : num;
  }
  return 0;
};

// Função para mapear socialNetworks para platforms com campos de views
const mapSocialNetworksToPlatforms = (socialNetworks: any) => {
  if (!socialNetworks) return {};
  
  const platforms: any = {};
  
  Object.keys(socialNetworks).forEach(platform => {
    const networkData = socialNetworks[platform];
    
    // Estrutura base da plataforma
    platforms[platform] = {
      username: networkData.username,
      followers: networkData.followers || networkData.subscribers || 0,
      avgViews: networkData.avgViews || 0,
      engagementRate: networkData.engagementRate || 0,
      audienceGender: networkData.audienceGender,
      audienceLocations: networkData.audienceLocations || [],
      audienceCities: networkData.audienceCities || [],
      audienceAgeRanges: networkData.audienceAgeRange || []
    };
    
    // Adicionar campos de pricing específicos por plataforma
    if (platform === 'instagram') {
      platforms[platform].pricing = {
        story: 0, // Será preenchido via financialData
        reel: 0   // Será preenchido via financialData
      };
      
      // Campos de views específicos do Instagram
      platforms[platform].views = {
        storiesViews: networkData.storiesViews || 0,
        reelsViews: networkData.reelsViews || 0
      };
    } else if (platform === 'youtube') {
      platforms[platform].pricing = {
        shorts: 0,     // Será preenchido via financialData
        dedicated: 0,  // Será preenchido via financialData
        insertion: 0   // Será preenchido via financialData
      };
      
      // Campos de views específicos do YouTube
      platforms[platform].views = {
        shortsViews: networkData.shortsViews || 0,
        longFormViews: networkData.longFormViews || networkData.avgViews || 0
      };
    } else if (platform === 'tiktok') {
      platforms[platform].pricing = {
        video: 0 // Será preenchido via financialData
      };
      
      // Campos de views específicos do TikTok
      platforms[platform].views = {
        videoViews: networkData.videoViews || networkData.avgViews || 0
      };
    }
  });
  
  console.log('🔄 Mapeamento socialNetworks -> platforms:', JSON.stringify(platforms, null, 2));
  return platforms;
};

// Rota GET para buscar todos os influenciadores
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  const ids = searchParams.get('ids'); // Novo parâmetro para múltiplos IDs
  const includeFinancials = searchParams.get('includeFinancials') === 'true';

  try {
    if (ids) {
      // Buscar múltiplos influenciadores pelos IDs (otimizado)
      const idsArray = ids.split(',').map(id => id.trim()).filter(Boolean);
      
      if (idsArray.length === 0) {
        return NextResponse.json({ 
          success: true, 
          influencers: [] 
        });
      }

      // Usar a função otimizada para buscar múltiplos influenciadores
      const influencers = await getInfluencersByIds(idsArray);

      // Se dados financeiros foram solicitados, buscar para cada influenciador
      if (includeFinancials) {
        console.log('Buscando dados financeiros para múltiplos influenciadores:', idsArray.length);
        
        const influencersWithFinancials = await Promise.all(
          influencers.map(async (influencer) => {
            try {
              const financialData = await getFinancialByInfluencerId(influencer.id);
              const inf: any = influencer; // Para adicionar propriedades dinamicamente
              
              if (financialData) {
                console.log(`Dados financeiros encontrados para ${influencer.id}`);
                // @ts-ignore - Adicionando propriedades dinamicamente
                inf.financials = financialData;
                // @ts-ignore - Adicionando propriedades dinamicamente  
                inf.financialData = financialData;
                
                // Também incluir na estrutura esperada pelo frontend (dadosFinanceiros)
                inf.dadosFinanceiros = {
                  responsavel: financialData.responsibleName,
                  agencia: financialData.agencyName,
                  emailFinanceiro: financialData.email,
                  whatsappFinanceiro: financialData.whatsapp,
                  visualizacoesStories: financialData.instagramStoriesViews,
                  precos: financialData.prices
                };
              } else {
                console.log(`Nenhum dado financeiro encontrado para ${influencer.id}`);
                
                // Usar dados padrão do influenciador se existirem  
                const defaultFinancialData = {
                  influencerId: influencer.id,
                  responsibleName: inf.responsible_name || '',
                  agencyName: inf.agency_name || '',
                  email: influencer.email || '',
                  whatsapp: influencer.whatsapp || '',
                  instagramStoriesViews: parseCurrency(inf.stories_views),
                  prices: {
                    instagramStory: { name: "Stories", price: parseCurrency(inf.story_price) },
                    instagramReel: { name: "Reels", price: parseCurrency(inf.reels_price) },
                    tiktokVideo: { name: "Vídeo", price: parseCurrency(inf.tiktok_price) },
                    youtubeInsertion: { name: "Inserção", price: parseCurrency(inf.video_insertion_price) },
                    youtubeDedicated: { name: "Dedicado", price: parseCurrency(inf.video_dedicated_price) },
                    youtubeShorts: { name: "Shorts", price: parseCurrency(inf.shorts_price) }
                  },
                  brandHistory: {
                    instagram: inf.instagram_brands ? inf.instagram_brands.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
                    tiktok: inf.tiktok_brands ? inf.tiktok_brands.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
                    youtube: inf.youtube_brands ? inf.youtube_brands.split(',').map((s: string) => s.trim()).filter(Boolean) : []
                  },
                  additionalData: {
                    contentType: inf.content_type ? inf.content_type.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
                    promotesTraders: inf.promotes_traders || false,
                    responsibleRecruiter: inf.responsible_capturer || '',
                    socialMediaScreenshots: [],
                    notes: inf.observation || '',
                    documents: []
                  },
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  id: ''
                };
                
                // @ts-ignore - Adicionando propriedades dinamicamente
                inf.financials = defaultFinancialData;
                // @ts-ignore - Adicionando propriedades dinamicamente
                inf.financialData = defaultFinancialData;
                
                // Também incluir na estrutura esperada pelo frontend
                inf.dadosFinanceiros = {
                  responsavel: defaultFinancialData.responsibleName,
                  agencia: defaultFinancialData.agencyName,
                  emailFinanceiro: defaultFinancialData.email,
                  whatsappFinanceiro: defaultFinancialData.whatsapp,
                  visualizacoesStories: defaultFinancialData.instagramStoriesViews,
                  precos: defaultFinancialData.prices
                };
              }
              
              return influencer;
            } catch (error) {
              console.error(`Erro ao buscar dados financeiros para influenciador ${influencer.id}:`, error);
              return influencer;
            }
          })
        );
        
        return NextResponse.json({
          success: true,
          influencers: influencersWithFinancials
        });
      }

      return NextResponse.json({
        success: true,
        influencers: influencers
      });
      
    } else if (id) {
      // Buscar um influenciador específico pelo ID
      const influencer = await getInfluencerById(id);
      
      if (!influencer) {
        return NextResponse.json(
          { error: 'Influenciador não encontrado' },
          { status: 404 }
        );
      }
      
      // Buscar dados financeiros apenas se solicitado explicitamente
      if (includeFinancials) {
        console.log('Buscando dados financeiros para o influenciador ID:', id);
        const financialData = await getFinancialByInfluencerId(id);
        // Definir valores padrão caso não existam dados financeiros
        const inf: any = influencer;
        const defaultFinancialData = {
          influencerId: id,
          responsibleName: inf.responsible_name || '',
          agencyName: inf.agency_name || '',
          email: influencer.email || '',
          whatsapp: influencer.whatsapp || '',
          instagramStoriesViews: parseCurrency(inf.stories_views),
          prices: {
            instagramStory: { name: "Stories", price: parseCurrency(inf.story_price) },
            instagramReel: { name: "Reels", price: parseCurrency(inf.reels_price) },
            tiktokVideo: { name: "Vídeo", price: parseCurrency(inf.tiktok_price) },
            youtubeInsertion: { name: "Inserção", price: parseCurrency(inf.video_insertion_price) },
            youtubeDedicated: { name: "Dedicado", price: parseCurrency(inf.video_dedicated_price) },
            youtubeShorts: { name: "Shorts", price: parseCurrency(inf.shorts_price) }
          },
          brandHistory: {
            instagram: inf.instagram_brands ? inf.instagram_brands.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
            tiktok: inf.tiktok_brands ? inf.tiktok_brands.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
            youtube: inf.youtube_brands ? inf.youtube_brands.split(',').map((s: string) => s.trim()).filter(Boolean) : []
          },
          additionalData: {
            contentType: inf.content_type ? inf.content_type.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
            promotesTraders: inf.promotes_traders || false,
            responsibleRecruiter: inf.responsible_capturer || '',
            socialMediaScreenshots: [],
            notes: inf.observation || '',
            documents: []
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          id: ''
        };
        if (financialData) {
          console.log('Dados financeiros encontrados:', financialData);
        } else {
          console.log('Nenhum dado financeiro encontrado para o influenciador ID:', id);
        }
        // Adicionar dados financeiros apenas quando solicitado
        influencer.financials = financialData || defaultFinancialData;
        // Compatibilidade: também expor como financialData
        influencer.financialData = influencer.financials;
      }
      
      // Buscar dados completos das categorias
      if (influencer.mainCategories && Array.isArray(influencer.mainCategories)) {
        const categoriesData = await Promise.all(
          influencer.mainCategories.map(async (categoryId) => {
            // Verificar se é um ID válido ou já é uma string de categoria
            if (typeof categoryId === 'string' && categoryId.length > 10) {
              // Parece ser um ID do Firestore, buscar a categoria
              try {
                const category = await getCategoryById(categoryId);
                return category || { 
                  id: categoryId, 
                  name: categoryId,
                  slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                };
              } catch (error) {
                console.error(`Erro ao buscar categoria ${categoryId}:`, error);
                return { 
                  id: categoryId, 
                  name: categoryId,
                  slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                };
              }
            } else {
              // Já é uma string (nome da categoria), retornar como objeto
              return { 
                id: categoryId, 
                name: categoryId,
                slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
              };
            }
          })
        );
        
        influencer.mainCategoriesData = categoriesData;
      }
      
      return NextResponse.json(influencer);
    } else {
      // Buscar todos os influenciadores
      const influencers = await getAllInfluencers();
      
      // Para cada influenciador, buscar as categorias completas
      const influencersWithCategories = await Promise.all(
        influencers.map(async (influencer) => {
          // Se mainCategories for uma array de IDs
          if (influencer.mainCategories && Array.isArray(influencer.mainCategories)) {
            const categoriesData = await Promise.all(
              influencer.mainCategories.map(async (categoryId) => {
                // Verificar se é um ID válido ou já é uma string de categoria
                if (typeof categoryId === 'string' && categoryId.length > 10) {
                  // Parece ser um ID do Firestore, buscar a categoria
                  try {
                    const category = await getCategoryById(categoryId);
                    return category || { 
                      id: categoryId, 
                      name: categoryId,
                      slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                    };
                  } catch (error) {
                    console.error(`Erro ao buscar categoria ${categoryId}:`, error);
                    return { 
                      id: categoryId, 
                      name: categoryId,
                      slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                    };
                  }
                } else {
                  // Já é uma string (nome da categoria), retornar como objeto
                  return { 
                    id: categoryId, 
                    name: categoryId,
                    slug: categoryId.toLowerCase().replace(/[^a-z0-9]/g, '-')
                  };
                }
              })
            );
            
            return {
              ...influencer,
              mainCategoriesData: categoriesData // Adicionar os dados completos
            };
          }
          return influencer;
        })
      );
      
      return NextResponse.json(influencersWithCategories);
    }
  } catch (error: any) {
    console.error('Erro ao buscar influenciadores:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar dados de influenciadores' },
      { status: 500 }
    );
  }
}

// Rota POST para adicionar um novo influenciador
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // 🔍 DEBUG: Loggar dados recebidos
    console.log('🔍 DEBUG POST /api/influencers - Dados recebidos:', JSON.stringify(data, null, 2));
    
    // 🔍 DEBUG: Verificar especificamente campos de views
    if (data.platforms) {
      console.log('🔍 DEBUG POST /api/influencers - Platforms:', JSON.stringify(data.platforms, null, 2));
      
      Object.keys(data.platforms).forEach(platform => {
        if (data.platforms[platform]?.views) {
          console.log(`🔍 DEBUG POST /api/influencers - Views para ${platform}:`, JSON.stringify(data.platforms[platform].views, null, 2));
        }
      });
    }
    
    // Verificar se recebemos a nova estrutura com duas coleções
    if (data.influencerData && data.financialData) {
      // Nova estrutura - salvar nas duas coleções
      const { influencerData, financialData } = data;
      
      // 🔄 Mapear socialNetworks para platforms com campos de views
      const platforms = mapSocialNetworksToPlatforms(influencerData.socialNetworks);
      
      // Adicionar metadados ao influenciador
      const enrichedInfluencerData = {
        ...influencerData,
        // Adicionar a nova estrutura platforms
        platforms: platforms,
        userId: 'system', // TODO: Pegar do usuário autenticado
        createdBy: 'system',
        updatedBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Criar o influenciador primeiro
      const newInfluencer = await addInfluencer(enrichedInfluencerData);
      
      // Adicionar o ID do influenciador aos dados financeiros
      const enrichedFinancialData = {
        ...financialData,
        influencerId: newInfluencer.id,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Criar os dados financeiros
      let financialDoc = null;
      try {
        const { addFinancial } = await import('@/lib/firebase-financials');
        financialDoc = await addFinancial(enrichedFinancialData);
      } catch (error) {
        console.error('Erro ao criar dados financeiros:', error);
        // Não falhar a operação se os dados financeiros falharem
      }
      
      // Retornar o influenciador completo com referência aos dados financeiros
      return NextResponse.json({
        ...newInfluencer,
        financialId: financialDoc?.id
      }, { status: 201 });
      
    } else {
      // Estrutura antiga - manter compatibilidade
      const sanitizedData = {
        ...data,
        name: data.name || "Influenciador Sem Nome",
        city: data.city || "",
        state: data.state || "",
        country: data.country || "Brasil",
        userId: 'system', // TODO: Pegar do usuário autenticado
        createdBy: 'system',
        updatedBy: 'system'
      };
      
      const newInfluencer = await addInfluencer(sanitizedData);
      return NextResponse.json(newInfluencer, { status: 201 });
    }
  } catch (error: any) {
    console.error('Erro ao adicionar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao adicionar influenciador' },
      { status: 500 }
    );
  }
}

// Rota PUT para atualizar um influenciador existente
export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador não fornecido' },
        { status: 400 }
      );
    }
    
    const data = await request.json();
    
    // 🔍 DEBUG: Loggar dados recebidos
    console.log('🔍 DEBUG PUT /api/influencers - Dados recebidos:', JSON.stringify(data, null, 2));
    
    // 🔍 DEBUG: Verificar especificamente campos de views
    if (data.platforms) {
      console.log('🔍 DEBUG PUT /api/influencers - Platforms:', JSON.stringify(data.platforms, null, 2));
      
      Object.keys(data.platforms).forEach(platform => {
        if (data.platforms[platform]?.views) {
          console.log(`🔍 DEBUG PUT /api/influencers - Views para ${platform}:`, JSON.stringify(data.platforms[platform].views, null, 2));
        }
      });
    }
    
    // 🔍 DEBUG: Verificar estrutura socialNetworks (estrutura atual)
    if (data.influencerData?.socialNetworks) {
      console.log('🔍 DEBUG PUT /api/influencers - SocialNetworks:', JSON.stringify(data.influencerData.socialNetworks, null, 2));
    }
    
    // Verificar se recebemos a nova estrutura com duas coleções
    if (data.influencerData && data.financialData) {
      // Nova estrutura - atualizar nas duas coleções
      const { influencerData, financialData } = data;
      
      // 🔄 Mapear socialNetworks para platforms com campos de views
      const platforms = mapSocialNetworksToPlatforms(influencerData.socialNetworks);
      
      // Adicionar metadados ao influenciador
      const enrichedInfluencerData = {
        ...influencerData,
        // Adicionar a nova estrutura platforms
        platforms: platforms,
        updatedBy: 'system', // TODO: Pegar do usuário autenticado
        updatedAt: new Date()
      };
      
      // Remover campos que não devem ser atualizados
      delete enrichedInfluencerData.id;
      delete enrichedInfluencerData.createdAt;
      delete enrichedInfluencerData.createdBy;
      
      // Atualizar o influenciador
      const updatedInfluencer = await updateInfluencer(id, enrichedInfluencerData);
      
      // Atualizar ou criar dados financeiros
      try {
        const { getFinancialByInfluencerId, updateFinancial, addFinancial } = await import('@/lib/firebase-financials');
        
        // Verificar se já existem dados financeiros
        const existingFinancial = await getFinancialByInfluencerId(id);
        
        if (existingFinancial && existingFinancial.id) {
          // Atualizar dados existentes
          const enrichedFinancialData = {
            ...financialData,
            influencerId: id,
            updatedAt: new Date()
          };
          
          // Remover campos que não devem ser atualizados
          delete enrichedFinancialData.id;
          delete enrichedFinancialData.createdAt;
          
          await updateFinancial(existingFinancial.id, enrichedFinancialData);
        } else {
          // Criar novos dados financeiros
          const enrichedFinancialData = {
            ...financialData,
            influencerId: id,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          
          await addFinancial(enrichedFinancialData);
        }
      } catch (error) {
        console.error('Erro ao atualizar dados financeiros:', error);
        // Não falhar a operação se os dados financeiros falharem
      }
      
      return NextResponse.json(updatedInfluencer);
      
    } else {
      // Estrutura antiga - manter compatibilidade
      // Usar apenas o campo avatar como padrão
      if (data.avatarUrl && !data.avatar) {
        data.avatar = data.avatarUrl;
      }
      // Remover avatarUrl para evitar duplicação
      delete data.avatarUrl;
      
      const updatedInfluencer = await updateInfluencer(id, data);
      
      return NextResponse.json(updatedInfluencer);
    }
  } catch (error: any) {
    console.error('Erro ao atualizar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar influenciador' },
      { status: 500 }
    );
  }
}

// Rota DELETE para remover um influenciador
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador não fornecido' },
        { status: 400 }
      );
    }
    
    await deleteInfluencer(id);
    
    return NextResponse.json(
      { message: 'Influenciador removido com sucesso' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Erro ao excluir influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir influenciador' },
      { status: 500 }
    );
  }
}


