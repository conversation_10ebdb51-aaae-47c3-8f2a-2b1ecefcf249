/**
 * 🔒 CONFIGURAÇÃO DE CONTENT SECURITY POLICY (CSP)
 * 
 * Este arquivo centraliza as configurações de CSP para resolver problemas de:
 * - Clerk Authentication & Telemetry
 * - Firebase Services
 * - WorkOS
 * - Cloudflare CAPTCHA
 */

// 🔗 URLs permitidas para diferentes serviços
export const SECURITY_DOMAINS = {
  // Clerk Authentication & Telemetry
  clerk: [
    'https://*.clerk.accounts.dev',
    'https://clerk-telemetry.com'
  ],
  
  // Cloudflare CAPTCHA
  cloudflare: [
    'https://challenges.cloudflare.com'
  ],
  
  // Firebase Services
  firebase: [
    'https://firebase.googleapis.com',
    'https://*.firebase.googleapis.com',
    'https://*.firebaseio.com',
    'wss://*.firebaseio.com',
    'https://firebaseinstallations.googleapis.com',
    'https://identitytoolkit.googleapis.com',
    'https://securetoken.googleapis.com',
    'https://firestore.googleapis.com',
    'https://*.firestore.googleapis.com',
    'wss://firestore.googleapis.com',
    'wss://*.firestore.googleapis.com'
  ],
  
  // WorkOS
  workos: [
    'https://js.workos.com',
    'https://api.workos.com',
    'https://workos.com'
  ],
  
  // Analytics & Monitoring
  analytics: [
    'https://www.googletagmanager.com',
    'https://www.google-analytics.com',
    'https://analytics.google.com'
  ],
  
  // External APIs
  external: [
    'https://viacep.com.br'
  ],
  
  // Development & Deployment
  development: [
    'https://vercel.live'
  ],
  
  // Google Fonts
  fonts: [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ]
};

// 🛡️ Content Security Policy otimizado
export const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  
  'script-src': [
    "'self'",
    "'unsafe-eval'",
    "'unsafe-inline'",
    "blob:",
    ...SECURITY_DOMAINS.development,
    ...SECURITY_DOMAINS.analytics,
    ...SECURITY_DOMAINS.clerk,
    ...SECURITY_DOMAINS.cloudflare,
    ...SECURITY_DOMAINS.workos
  ],
  
  'style-src': [
    "'self'",
    "'unsafe-inline'",
    ...SECURITY_DOMAINS.fonts,
    ...SECURITY_DOMAINS.cloudflare
  ],
  
  'img-src': [
    "'self'",
    "data:",
    "https:",
    "blob:"
  ],
  
  'font-src': [
    "'self'",
    "data:",
    ...SECURITY_DOMAINS.fonts
  ],
  
  'connect-src': [
    "'self'",
    ...SECURITY_DOMAINS.firebase,
    ...SECURITY_DOMAINS.analytics,
    ...SECURITY_DOMAINS.external,
    ...SECURITY_DOMAINS.clerk,
    ...SECURITY_DOMAINS.workos
  ],
  
  'frame-src': [
    "'self'",
    ...SECURITY_DOMAINS.clerk,
    ...SECURITY_DOMAINS.cloudflare,
    ...SECURITY_DOMAINS.workos
  ]
};

// 🔧 Função para gerar string de CSP
export function generateCSPString(): string {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ') + ';';
}

// 🛡️ Headers de segurança completos
export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Content-Security-Policy': generateCSPString()
};

// 🔍 Headers de segurança para produção
export const PRODUCTION_SECURITY_HEADERS = {
  ...SECURITY_HEADERS,
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
};

// 📊 Configuração de Rate Limiting
export const RATE_LIMIT_CONFIG = {
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // limite de requests por IP
  message: 'Muitas tentativas de acesso. Tente novamente em 15 minutos.',
  standardHeaders: true,
  legacyHeaders: false
};

// 🔐 Configuração de CORS
export const CORS_CONFIG = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://deumatch.vercel.app'] 
    : ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// ✅ Validar configuração de segurança
export function validateSecurityConfig(): boolean {
  const requiredEnvVars = [
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY'
  ];
  
  return requiredEnvVars.every(envVar => process.env[envVar]);
}

// 🚨 Log de violações de CSP
export function logCSPViolation(event: string, details: Record<string, unknown>) {
  if (process.env.NODE_ENV === 'production') {
    console.log(`[CSP-VIOLATION] ${event}:`, JSON.stringify(details));
  }
} 

