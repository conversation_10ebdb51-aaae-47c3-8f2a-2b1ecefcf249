"use client"

import { Protect } from '@clerk/nextjs';
import { Shield } from 'lucide-react';
import { UserCategoryManager } from "@/components/user-category-manager"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Componente protegido por role admin
function CategoriesSettingsContent() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>Categorias de Influenciadores</span>
          </CardTitle>
          <CardDescription>
            Crie categorias para organizar seus influenciadores por segmento, nicho ou tipo de conteúdo.
            Essas categorias serão usadas em todo o sistema para filtrar e agrupar influenciadores.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserCategoryManager />
        </CardContent>
      </Card>
    </div>
  )
}

// Componente principal com proteção de role admin
export default function CategoriesPage() {
  return (
    <Protect 
      role="org:admin"
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">Acesso Restrito</h2>
            <p className="text-muted-foreground">
              Apenas administradores podem acessar as configurações de categorias.
            </p>
          </div>
        </div>
      }
    >
      <CategoriesSettingsContent />
    </Protect>
  );
} 