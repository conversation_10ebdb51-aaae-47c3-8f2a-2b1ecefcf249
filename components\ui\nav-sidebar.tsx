"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  Home,
  Users,
  BarChart2,
  MessageSquare,
  FolderOpen,
  Calendar,
  Settings,
  Search,
  PlusCircle,
  HelpCircle,
  Hash,
  ShoppingBag,
  Grid,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ThemeToggle } from "@/components/ui/theme-toggle";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  isPro?: boolean;
}

function NavItem({ href, icon, label, isActive, isPro = false }: NavItemProps) {
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link href={href} className="group flex items-center justify-center">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-12 w-12 rounded-full relative",
                isActive ? "bg-gray-100 dark:bg-white/10 text-gray-800 dark:text-white" : "text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10"
              )}
            >
              {icon}
              {isPro && (
                <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 border border-black" />
              )}
            </Button>
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right" className="bg-[#080210] text-foreground dark:text-white border-gray-800">
          {label}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function NavSidebar() {
  const pathname = usePathname();

  return (
    <div className="fixed inset-y-0 left-0 z-20 flex w-16 flex-col bg-white dark:bg-black/80 backdrop-blur-md border-r border-gray-200 dark:border-white/10">
      <div className="flex flex-col items-center gap-2 px-2 py-4">
        <Link href="/" className="flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="h-12 w-12 rounded-full text-foreground dark:text-white p-2"
          >
            <Image 
              src="/logo.svg" 
              alt="Deu Match" 
              width={32} 
              height={32} 
              className="w-full h-full"
            />
          </Button>
        </Link>

        <NavItem
          href="/"
          icon={<Home className="h-5 w-5" />}
          label="Início"
          isActive={pathname === "/"}
        />

        <NavItem
          href="/admin/categories"
          icon={<Hash className="h-5 w-5" />}
          label="Categorias"
          isActive={pathname.startsWith("/admin/categories")}
        />
        

      </div>

      <div className="mt-auto flex flex-col items-center gap-2 px-2 py-4">
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10"
        >
          <Search className="h-5 w-5" />
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10"
        >
          <PlusCircle className="h-5 w-5" />
        </Button>


        
        <NavItem
          href="/admin"
          icon={<Settings className="h-5 w-5" />}
          label="Painel de Administração"
          isActive={pathname === "/admin"}
        />
        
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center justify-center">
                <ThemeToggle />
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" className="bg-[#080210] text-foreground dark:text-white ">
              Alternar tema
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10"
        >
          <HelpCircle className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}


