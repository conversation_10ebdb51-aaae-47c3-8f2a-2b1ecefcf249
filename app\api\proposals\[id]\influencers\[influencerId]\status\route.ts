import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { db } from '@/lib/firebase-admin';
import { ProposalService } from '@/services/proposal-service';

interface Params {
  id: string;
  influencerId: string;
}

export const GET = withUserIsolation(async (
  request: NextRequest,
  userId: string
) => {
  try {
    // Extrair proposalId e influencerId da URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const proposalId = pathParts[pathParts.indexOf('proposals') + 1];
    const influencerId = pathParts[pathParts.indexOf('influencers') + 1];

    console.log('🔍 [API] Buscando status do influencer na proposta:', {
      proposalId,
      influencerId,
      userId
    });

    // Validações
    if (!proposalId) {
      return NextResponse.json(
        { error: 'ID da proposta é obrigatório' },
        { status: 400 }
      );
    }

    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    // Buscar o documento do influencer na subcoleção da proposta usando Firebase Admin
    const influencerDoc = await db
      .collection('proposals')
      .doc(proposalId)
      .collection('influencers')
      .doc(influencerId)
      .get();

    if (influencerDoc.exists) {
      const data = influencerDoc.data();
      const status = data?.status || 'pendente';
      
      console.log('✅ [API] Status do influencer encontrado:', {
        proposalId,
        influencerId,
        status
      });

      return NextResponse.json({
        success: true,
        status,
        data: {
          influencerId,
          proposalId,
          status,
          addedAt: data?.addedAt,
          updatedAt: data?.updatedAt,
          addedBy: data?.addedBy
        }
      });
    } else {
      console.log('⚠️ [API] Documento do influencer não encontrado na proposta');
      
      return NextResponse.json({
        success: false,
        status: null,
        error: 'Influenciador não encontrado na proposta'
      }, { status: 404 });
    }

  } catch (error) {
    console.error('❌ [API] Erro ao buscar status do influencer:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
});

export const PUT = withUserIsolation(async (
  request: NextRequest,
  userId: string
) => {
  try {
    // Extrair proposalId e influencerId da URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const proposalId = pathParts[pathParts.indexOf('proposals') + 1];
    const influencerId = pathParts[pathParts.indexOf('influencers') + 1];

    const body = await request.json();
    const { status } = body;

    console.log('🔄 [API] Atualizando status do influencer na proposta:', {
      proposalId,
      influencerId,
      newStatus: status,
      userId
    });

    // Validações
    if (!proposalId) {
      return NextResponse.json(
        { error: 'ID da proposta é obrigatório' },
        { status: 400 }
      );
    }

    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    if (!status) {
      return NextResponse.json(
        { error: 'Status é obrigatório' },
        { status: 400 }
      );
    }

    // Validar status válidos
    const validStatuses = ['pendente', 'aceito', 'rejeitado', 'descartado'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Status inválido. Valores aceitos: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    // Usar o ProposalService para atualizar o status
    await ProposalService.updateInfluencerStatus(
      proposalId,
      influencerId,
      status,
      userId
    );

    console.log('✅ [API] Status do influencer atualizado com sucesso:', {
      proposalId,
      influencerId,
      newStatus: status
    });

    return NextResponse.json({
      success: true,
      message: 'Status atualizado com sucesso',
      data: {
        proposalId,
        influencerId,
        status,
        updatedBy: userId,
        updatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ [API] Erro ao atualizar status do influencer:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}); 