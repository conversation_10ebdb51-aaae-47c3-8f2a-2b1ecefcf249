/**
 * 🏦 SERVIÇO DE ORÇAMENTOS COM CONTROLE DE ACESSO UNIFICADO
 * 
 * Sistema que permite tanto proprietários quanto convidados (via Clerk metadata)
 * acessarem orçamentos de propostas com permissões granulares
 */

import { db } from '@/lib/firebase-admin';
import { ProposalService } from './proposal-service';

// ===== INTERFACES =====

export interface BudgetPermissions {
  canView: boolean;
  canEdit: boolean;
  canCreateCounterProposal: boolean;
  canManageBudgets: boolean;
  canApproveCounterProposals: boolean;
  canViewFinancialData: boolean;
}

export interface BudgetAccessResult {
  hasAccess: boolean;
  role: 'owner' | 'admin' | 'editor' | 'viewer' | null;
  permissions: BudgetPermissions;
  userId: string;
  proposalId: string;
}

export interface ProposalBudgetsResult {
  proposalId: string;
  permissions: BudgetPermissions;
  budgets: any[];
  documents?: any[]; // 🆕 NOVO: Documentos do influenciador na proposta
  totalCount: number;
  userRole: string;
  errors: string[];
  processingTimeMs: number;
}

export interface CollaboratorCounterProposal {
  id?: string;
  budgetId: string;
  proposalId: string;
  influencerId: string;
  originalAmount: number;
  proposedAmount: number;
  currency: string;
  notes?: string;
  serviceType: string;
  proposedBy: {
    userId: string;
    userName: string;
    userEmail: string;
    collaboratorRole: 'editor' | 'viewer' | 'admin';
  };
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: Date;
  updatedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  reviewNote?: string;
}

// ===== SERVIÇO PRINCIPAL =====

export class BudgetService {
  
  // ===== CONTROLE DE ACESSO =====

  /**
   * 🔐 VERIFICAÇÃO UNIFICADA DE ACESSO A ORÇAMENTOS
   * 
   * Verifica se um usuário pode acessar orçamentos de uma proposta
   * Funciona tanto para proprietários quanto convidados via Clerk
   */
  static async canUserAccessBudgets(proposalId: string, userId: string): Promise<BudgetAccessResult> {
    console.log('🔐 [BudgetService] Verificando acesso aos orçamentos:', { proposalId, userId });

    const result: BudgetAccessResult = {
      hasAccess: false,
      role: null,
      permissions: this.getNoAccessPermissions(),
      userId,
      proposalId
    };

    try {
      // 1. Verificar ownership direto da proposta
      const isOwner = await ProposalService.canUserAccessProposal(proposalId, userId);
      
      if (isOwner) {
        console.log('✅ [BudgetService] Usuário é proprietário da proposta');
        result.hasAccess = true;
        result.role = 'owner';
        result.permissions = this.getOwnerPermissions();
        return result;
      }

      // 2. Verificar acesso via Clerk metadata (convidados)
      const clerkAccess = await this.checkClerkMetadataAccess(proposalId, userId);
      
      if (clerkAccess.hasAccess) {
        console.log(`✅ [BudgetService] Usuário tem acesso via Clerk metadata: ${clerkAccess.role}`);
        result.hasAccess = true;
        result.role = clerkAccess.role;
        result.permissions = clerkAccess.permissions;
        return result;
      }

      // 3. Verificar se é colaborador direto na proposta (fallback)
      const collaboratorAccess = await this.checkDirectCollaboratorAccess(proposalId, userId);
      
      if (collaboratorAccess.hasAccess) {
        console.log(`✅ [BudgetService] Usuário é colaborador direto: ${collaboratorAccess.role}`);
        result.hasAccess = true;
        result.role = collaboratorAccess.role;
        result.permissions = collaboratorAccess.permissions;
        return result;
      }

      console.log('❌ [BudgetService] Usuário não tem acesso aos orçamentos');
      return result;

    } catch (error) {
      console.error('❌ [BudgetService] Erro ao verificar acesso:', error);
      return result;
    }
  }

  /**
   * 🔍 VERIFICAR ACESSO VIA CLERK METADATA
   */
  private static async checkClerkMetadataAccess(proposalId: string, userId: string): Promise<{
    hasAccess: boolean;
    role: 'admin' | 'editor' | 'viewer' | null;
    permissions: BudgetPermissions;
  }> {
    try {
      const { clerkClient } = await import('@clerk/nextjs/server');
      const clerk = await clerkClient();
      const user = await clerk.users.getUser(userId);
      
      const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];
      
      if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
        const role = this.mapClerkRoleToRole(proposalAccess);
        return {
          hasAccess: true,
          role,
          permissions: this.getPermissionsForRole(proposalAccess)
        };
      }

      return {
        hasAccess: false,
        role: null,
        permissions: this.getNoAccessPermissions()
      };

    } catch (error) {
      console.warn('⚠️ [BudgetService] Erro ao verificar Clerk metadata:', error);
      return {
        hasAccess: false,
        role: null,
        permissions: this.getNoAccessPermissions()
      };
    }
  }

  /**
   * 👥 VERIFICAR ACESSO VIA COLABORADOR DIRETO
   */
  private static async checkDirectCollaboratorAccess(proposalId: string, userId: string): Promise<{
    hasAccess: boolean;
    role: 'editor' | 'viewer' | null;
    permissions: BudgetPermissions;
  }> {
    try {
      const proposalDoc = await db.collection('proposals').doc(proposalId).get();
      const proposalData = proposalDoc.data();
      
      if (!proposalData) {
        return {
          hasAccess: false,
          role: null,
          permissions: this.getNoAccessPermissions()
        };
      }

      const collaborator = proposalData.collaborators?.find((c: any) => 
        c.userId === userId && c.status === 'active'
      );

      if (collaborator) {
        const role = collaborator.role === 'admin' ? 'editor' : collaborator.role;
        return {
          hasAccess: true,
          role,
          permissions: this.getPermissionsForCollaboratorRole(collaborator.role)
        };
      }

      return {
        hasAccess: false,
        role: null,
        permissions: this.getNoAccessPermissions()
      };

    } catch (error) {
      console.warn('⚠️ [BudgetService] Erro ao verificar colaborador direto:', error);
      return {
        hasAccess: false,
        role: null,
        permissions: this.getNoAccessPermissions()
      };
    }
  }

  // ===== MAPEAMENTO DE ROLES E PERMISSÕES =====

  /**
   * 🗺️ MAPEAR ROLE DO CLERK PARA ROLE INTERNO
   */
  private static mapClerkRoleToRole(clerkRole: string): 'admin' | 'editor' | 'viewer' {
    switch (clerkRole) {
      case 'a': return 'admin';
      case 'e': return 'editor'; 
      case 'v': return 'viewer';
      default: return 'viewer';
    }
  }

  /**
   * 👑 PERMISSÕES DO PROPRIETÁRIO
   */
  private static getOwnerPermissions(): BudgetPermissions {
    return {
      canView: true,
      canEdit: true,
      canCreateCounterProposal: true,
      canManageBudgets: true,
      canApproveCounterProposals: true,
      canViewFinancialData: true
    };
  }

  /**
   * 🎭 PERMISSÕES BASEADAS NO ROLE DO CLERK
   */
  private static getPermissionsForRole(clerkRole: string): BudgetPermissions {
    switch (clerkRole) {
      case 'a': // Admin
        return {
          canView: true,
          canEdit: true,
          canCreateCounterProposal: true,
          canManageBudgets: true,
          canApproveCounterProposals: false,
          canViewFinancialData: true
        };
      
      case 'e': // Editor
        return {
          canView: true,
          canEdit: true,
          canCreateCounterProposal: true,
          canManageBudgets: false,
          canApproveCounterProposals: false,
          canViewFinancialData: true
        };
        
      case 'v': // Viewer
        return {
          canView: true,
          canEdit: false,
          canCreateCounterProposal: false,
          canManageBudgets: false,
          canApproveCounterProposals: false,
          canViewFinancialData: false
        };
        
      default:
        return this.getNoAccessPermissions();
    }
  }

  /**
   * 👥 PERMISSÕES PARA COLABORADOR DIRETO
   */
  private static getPermissionsForCollaboratorRole(collaboratorRole: string): BudgetPermissions {
    switch (collaboratorRole) {
      case 'admin':
      case 'editor':
        return {
          canView: true,
          canEdit: true,
          canCreateCounterProposal: true,
          canManageBudgets: false,
          canApproveCounterProposals: false,
          canViewFinancialData: true
        };
        
      case 'viewer':
        return {
          canView: true,
          canEdit: false,
          canCreateCounterProposal: false,
          canManageBudgets: false,
          canApproveCounterProposals: false,
          canViewFinancialData: false
        };
        
      default:
        return this.getNoAccessPermissions();
    }
  }

  /**
   * 🚫 PERMISSÕES DE ACESSO NEGADO
   */
  private static getNoAccessPermissions(): BudgetPermissions {
    return {
      canView: false,
      canEdit: false,
      canCreateCounterProposal: false,
      canManageBudgets: false,
      canApproveCounterProposals: false,
      canViewFinancialData: false
    };
  }

  // ===== BUSCA DE ORÇAMENTOS =====

  /**
   * 📊 BUSCAR TODOS OS ORÇAMENTOS DE UMA PROPOSTA
   */
  static async getProposalBudgets(proposalId: string, userId: string): Promise<ProposalBudgetsResult> {
    const startTime = Date.now();
    
    const result: ProposalBudgetsResult = {
      proposalId,
      permissions: this.getNoAccessPermissions(),
      budgets: [],
      totalCount: 0,
      userRole: '',
      errors: [],
      processingTimeMs: 0
    };

    try {
      // Verificar acesso
      const accessResult = await this.canUserAccessBudgets(proposalId, userId);
      
      if (!accessResult.hasAccess) {
        result.errors.push('Acesso negado aos orçamentos desta proposta');
        result.processingTimeMs = Date.now() - startTime;
        return result;
      }

      result.permissions = accessResult.permissions;
      result.userRole = accessResult.role || '';

      // Buscar todos os influenciadores da proposta
      const influencersSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .get();

      console.log(`📋 [BudgetService] Encontrados ${influencersSnapshot.docs.length} influenciadores na proposta`);

      const allBudgets: any[] = [];

      // Para cada influenciador, buscar seus orçamentos
      for (const influencerDoc of influencersSnapshot.docs) {
        const influencerId = influencerDoc.id;
        
        try {
          const budgetsSnapshot = await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .doc(influencerId)
            .collection('budgets')
            .orderBy('updatedAt', 'desc')
            .get();

          budgetsSnapshot.docs.forEach(budgetDoc => {
            const budgetData = budgetDoc.data();
            allBudgets.push({
              id: budgetDoc.id,
              ...budgetData,
              // Converter Timestamps do Firebase para Dates
              createdAt: budgetData.createdAt?.toDate?.() || budgetData.createdAt,
              updatedAt: budgetData.updatedAt?.toDate?.() || budgetData.updatedAt,
              expiresAt: budgetData.expiresAt?.toDate?.() || budgetData.expiresAt
            });
          });

        } catch (error) {
          console.warn(`⚠️ [BudgetService] Erro ao buscar orçamentos do influenciador ${influencerId}:`, error);
        }
      }

      result.budgets = allBudgets;
      result.totalCount = allBudgets.length;
      result.processingTimeMs = Date.now() - startTime;

      console.log(`✅ [BudgetService] Encontrados ${allBudgets.length} orçamentos na proposta ${proposalId}`);
      return result;

    } catch (error) {
      console.error('❌ [BudgetService] Erro ao buscar orçamentos da proposta:', error);
      result.errors.push('Erro interno ao buscar orçamentos');
      result.processingTimeMs = Date.now() - startTime;
      return result;
    }
  }

  /**
   * 🎯 BUSCAR ORÇAMENTOS DE UM INFLUENCIADOR ESPECÍFICO
   */
  static async getInfluencerBudgetsInProposal(
    proposalId: string, 
    influencerId: string, 
    userId: string
  ): Promise<ProposalBudgetsResult> {
    const startTime = Date.now();
    
    const result: ProposalBudgetsResult = {
      proposalId,
      permissions: this.getNoAccessPermissions(),
      budgets: [],
      documents: [], // 🆕 NOVO: Incluir documentos no resultado
      totalCount: 0,
      userRole: '',
      errors: [],
      processingTimeMs: 0
    };

    try {
      // Verificar acesso
      const accessResult = await this.canUserAccessBudgets(proposalId, userId);
      
      if (!accessResult.hasAccess) {
        result.errors.push('Acesso negado aos orçamentos desta proposta');
        result.processingTimeMs = Date.now() - startTime;
        return result;
      }

      result.permissions = accessResult.permissions;
      result.userRole = accessResult.role || '';

      // Buscar orçamentos do influenciador específico
      const budgetsSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .orderBy('updatedAt', 'desc')
        .get();

      const budgets = await Promise.all(
        budgetsSnapshot.docs.map(async (doc) => {
          const budgetData = doc.data();
          
          // 🆕 Incluir contrapropostas do array (nova estrutura)
          let counterProposals = budgetData.counterProposals || [];
          
          // Converter datas das contrapropostas
          counterProposals = counterProposals.map((cp: any) => ({
            ...cp,
            proposedAt: cp.proposedAt ? new Date(cp.proposedAt) : null,
            updatedAt: cp.updatedAt ? new Date(cp.updatedAt) : null
          }));

          return {
            id: doc.id,
            ...budgetData,
            counterProposals, // 🆕 Incluir contrapropostas
            // Converter Timestamps do Firebase para Dates
            createdAt: budgetData.createdAt?.toDate?.() || budgetData.createdAt,
            updatedAt: budgetData.updatedAt?.toDate?.() || budgetData.updatedAt,
            expiresAt: budgetData.expiresAt?.toDate?.() || budgetData.expiresAt
          };
        })
      );

      // 🆕 NOVO: Buscar documentos da subcoleção hierárquica
      let documents: any[] = [];
      try {
        console.log(`📄 [BudgetService] Buscando documentos para proposta ${proposalId}, influencer ${influencerId}`);
        
        const documentsSnapshot = await db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .doc(influencerId)
          .collection('documents')
          .orderBy('uploadedAt', 'desc')
          .get();

        documents = documentsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            name: data.name,
            url: data.url,
            type: data.type,
            size: data.size,
            uploadedAt: data.uploadedAt?.toDate?.() || data.uploadedAt,
            uploadedBy: data.uploadedBy,
            proposalId: proposalId,
            influencerId: influencerId
          };
        });

        console.log(`✅ [BudgetService] Encontrados ${documents.length} documentos para influenciador ${influencerId} na proposta ${proposalId}`);
      } catch (error) {
        console.warn(`⚠️ [BudgetService] Erro ao buscar documentos do influenciador ${influencerId}:`, error);
        // Não bloquear a execução se falhar ao buscar documentos
      }

      result.budgets = budgets;
      result.documents = documents; // 🆕 NOVO: Incluir documentos no resultado
      result.totalCount = budgets.length;
      result.processingTimeMs = Date.now() - startTime;

      console.log(`✅ [BudgetService] Encontrados ${budgets.length} orçamentos e ${documents.length} documentos para influenciador ${influencerId} na proposta ${proposalId}`);
      return result;

    } catch (error) {
      console.error('❌ [BudgetService] Erro ao buscar orçamentos do influenciador:', error);
      result.errors.push('Erro interno ao buscar orçamentos');
      result.processingTimeMs = Date.now() - startTime;
      return result;
    }
  }

  // ===== CONTRAPROPOSTAS DE COLABORADORES =====

  /**
   * 💼 CRIAR CONTRAPROPOSTA DE COLABORADOR
   */
  static async createCollaboratorCounterProposal(input: {
    budgetId: string;
    proposalId: string;
    influencerId: string;
    originalAmount: number;
    proposedAmount: number;
    currency: string;
    notes?: string;
    serviceType: string;
    proposedBy: {
      userId: string;
      userName: string;
      userEmail: string;
      collaboratorRole: 'editor' | 'viewer' | 'admin';
    };
  }): Promise<CollaboratorCounterProposal> {
    console.log('💼 [BudgetService] Criando contraproposta de colaborador:', input);

    try {
      // Verificar se o usuário tem permissão para criar contrapropostas
      const accessResult = await this.canUserAccessBudgets(input.proposalId, input.proposedBy.userId);
      
      if (!accessResult.hasAccess || !accessResult.permissions.canCreateCounterProposal) {
        throw new Error('Usuário não tem permissão para criar contrapropostas');
      }

      const counterProposalData: CollaboratorCounterProposal = {
        budgetId: input.budgetId,
        proposalId: input.proposalId,
        influencerId: input.influencerId,
        originalAmount: input.originalAmount,
        proposedAmount: input.proposedAmount,
        currency: input.currency,
        notes: input.notes,
        serviceType: input.serviceType,
        proposedBy: input.proposedBy,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Salvar na subcoleção de contrapropostas de colaboradores
      const counterProposalRef = await db
        .collection('proposals')
        .doc(input.proposalId)
        .collection('influencers')
        .doc(input.influencerId)
        .collection('budgets')
        .doc(input.budgetId)
        .collection('collaboratorCounterProposals')
        .add(counterProposalData);

      // Atualizar o orçamento para indicar que tem contrapropostas de colaboradores
      await db
        .collection('proposals')
        .doc(input.proposalId)
        .collection('influencers')
        .doc(input.influencerId)
        .collection('budgets')
        .doc(input.budgetId)
        .update({
          hasCollaboratorCounterProposals: true,
          updatedAt: new Date()
        });

      console.log('✅ [BudgetService] Contraproposta de colaborador criada:', counterProposalRef.id);

      return {
        id: counterProposalRef.id,
        ...counterProposalData
      };

    } catch (error) {
      console.error('❌ [BudgetService] Erro ao criar contraproposta de colaborador:', error);
      throw error;
    }
  }

  /**
   * 📋 BUSCAR CONTRAPROPOSTAS DE COLABORADORES
   */
  static async getCollaboratorCounterProposals(
    proposalId: string,
    userId: string,
    status?: 'pending' | 'accepted' | 'rejected' | 'expired'
  ): Promise<CollaboratorCounterProposal[]> {
    console.log('📋 [BudgetService] Buscando contrapropostas de colaboradores:', { proposalId, userId, status });

    try {
      // Verificar acesso
      const accessResult = await this.canUserAccessBudgets(proposalId, userId);
      
      if (!accessResult.hasAccess) {
        throw new Error('Acesso negado às contrapropostas desta proposta');
      }

      const counterProposals: CollaboratorCounterProposal[] = [];

      // Buscar todos os influenciadores da proposta
      const influencersSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .get();

      // Para cada influenciador, buscar suas contrapropostas
      for (const influencerDoc of influencersSnapshot.docs) {
        const influencerId = influencerDoc.id;
        
        // Buscar todos os orçamentos do influenciador
        const budgetsSnapshot = await db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .get();

        // Para cada orçamento, buscar suas contrapropostas de colaboradores
        for (const budgetDoc of budgetsSnapshot.docs) {
          const budgetId = budgetDoc.id;
          
          let query = db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .doc(influencerId)
            .collection('budgets')
            .doc(budgetId)
            .collection('collaboratorCounterProposals')
            .orderBy('createdAt', 'desc');

          if (status) {
            query = query.where('status', '==', status);
          }

          const counterProposalsSnapshot = await query.get();

          counterProposalsSnapshot.docs.forEach(doc => {
            const data = doc.data();
            counterProposals.push({
              id: doc.id,
              ...data,
              createdAt: data.createdAt?.toDate?.() || data.createdAt,
              updatedAt: data.updatedAt?.toDate?.() || data.updatedAt,
              reviewedAt: data.reviewedAt?.toDate?.() || data.reviewedAt
            } as CollaboratorCounterProposal);
          });
        }
      }

      console.log(`✅ [BudgetService] Encontradas ${counterProposals.length} contrapropostas de colaboradores`);
      return counterProposals;

    } catch (error) {
      console.error('❌ [BudgetService] Erro ao buscar contrapropostas de colaboradores:', error);
      throw error;
    }
  }
} 