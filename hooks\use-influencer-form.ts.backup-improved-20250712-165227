import { useState, useCallback, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { 
  InfluencerFormSchema, 
  type InfluencerFormData,
  type SocialPlatform,
  PLATFORM_CONFIG 
} from '@/types/influencer-form'
import { useToast } from '@/components/ui/use-toast'
import { compressImage } from '@/lib/utils/image-compression'

interface UseInfluencerFormProps {
  initialData?: any
  onSubmit?: (data: InfluencerFormData) => Promise<void>
}

// Função para converter dados do Firestore para o formato do formulário
function convertFromFirestoreFormat(data: any): InfluencerFormData {
  if (!data) {
    return {
      personalInfo: { name: "", verified: false },
      location: { country: "Brasil" },
      contact: {},
      business: { promotesTraders: false, brandPartnerships: [] },
      brands: []
    }
  }

  console.log('🔄 Convertendo dados do Firestore:', data)

  // Mapear gênero do Firestore
  const genderMap: { [key: string]: 'Masculino' | 'Feminino' } = {
    'male': 'Masculino', 
    'female': 'Feminino'
    // outros valores (other, not_specified) não serão mapeados - ficam undefined
  }

  // Converter plataformas do Firestore
  const platforms: any = {}
  
  // NOVA ESTRUTURA: Campos diretos (instagramUsername, tiktokFollowers, etc.)
  const directFieldPlatforms = [
    { name: 'instagram', usernameField: 'instagramUsername', followersField: 'instagramFollowers', engagementField: 'instagramEngagementRate', viewsField: 'instagramAvgViews' },
    { name: 'tiktok', usernameField: 'tiktokUsername', followersField: 'tiktokFollowers', engagementField: 'tiktokEngagementRate', viewsField: 'tiktokAvgViews' },
    { name: 'youtube', usernameField: 'youtubeUsername', followersField: 'youtubeFollowers', engagementField: 'youtubeEngagementRate', viewsField: 'youtubeAvgViews' },
    { name: 'facebook', usernameField: 'facebookUsername', followersField: 'facebookFollowers', engagementField: 'facebookEngagementRate', viewsField: 'facebookAvgViews' },
    { name: 'twitch', usernameField: 'twitchUsername', followersField: 'twitchFollowers', engagementField: 'twitchEngagementRate', viewsField: 'twitchAvgViews' },
    { name: 'kwai', usernameField: 'kwaiUsername', followersField: 'kwaiFollowers', engagementField: 'kwaiEngagementRate', viewsField: 'kwaiAvgViews' }
  ]
  
  // Processar campos diretos primeiro (nova estrutura)
  directFieldPlatforms.forEach(({ name, usernameField, followersField, engagementField, viewsField }) => {
    const username = data[usernameField]
    const followers = data[followersField]
    
    if ((username && username.trim() !== '') || (followers && followers > 0)) {
      const platformData: any = {
        username: username || "",
        followers: Number(followers) || 0,
        engagementRate: Number(data[engagementField]) || 0,
        avgViews: Number(data[viewsField]) || 0,
        pricing: {},
        metrics: {},
        audienceGender: { male: 0, female: 0, other: 0 },
        audienceLocations: [],
        audienceCities: [],
        audienceAgeRanges: {},
        brandHistory: [],
        screenshots: []
      }
      
      // Adicionar campos específicos de visualizações por plataforma
      if (name === 'instagram') {
        platformData.views = {
          storiesViews: Number(data.instagramStoriesViews) || 0,
          reelsViews: Number(data.instagramReelsViews) || 0
        }
        console.log(`📺 [Form Load] Instagram views específicas:`, platformData.views)
      } else if (name === 'tiktok') {
        platformData.views = {
          videoViews: Number(data.tiktokVideoViews) || 0
        }
        console.log(`📺 [Form Load] TikTok views específicas:`, platformData.views)
      } else if (name === 'youtube') {
        platformData.views = {
          shortsViews: Number(data.youtubeShortsViews) || 0,
          longFormViews: Number(data.youtubeLongFormViews) || 0
        }
        console.log(`📺 [Form Load] YouTube views específicas:`, platformData.views)
      }
      
      platforms[name] = platformData
      console.log(`📱 Plataforma ${name} encontrada via campos diretos:`, platforms[name])
    }
  })
  
  // Fallback para estrutura antiga (socialNetworks) se nenhum campo direto foi encontrado
  if (Object.keys(platforms).length === 0 && data.socialNetworks) {
    console.log('🔄 Usando estrutura antiga (socialNetworks) como fallback')
    Object.keys(data.socialNetworks).forEach(platform => {
      const networkData = data.socialNetworks[platform]
      if (networkData && networkData.followers > 0) {
        platforms[platform] = {
          username: networkData.username || "",
          followers: Number(networkData.followers) || 0,
          engagementRate: Number(networkData.engagementRate) || 0,
          avgViews: Number(networkData.avgViews) || 0,
          pricing: networkData.pricing || {},
          metrics: networkData.metrics || {},
          audienceGender: networkData.audienceGender || { male: 0, female: 0, other: 0 },
          audienceLocations: networkData.audienceLocations || [],
          audienceCities: networkData.audienceCities || [],
          audienceAgeRange: networkData.audienceAgeRange || {},
          brandHistory: networkData.brandHistory || [],
          screenshots: networkData.screenshots || []
        }
      }
    })
  }

  // 💰 BUSCAR PREÇOS DA NOVA ESTRUTURA SEPARADA (currentPricing)
  if (data.currentPricing?.services && Object.keys(platforms).length > 0) {
    console.log('💰 [Form Load] Carregando pricing da nova estrutura:', data.currentPricing.services)
    console.log('💰 [Form Load] Platforms disponíveis para pricing:', Object.keys(platforms))
    
    Object.keys(platforms).forEach(platform => {
      const pricingData = data.currentPricing.services[platform]
      if (pricingData) {
        console.log(`💰 [Form Load] Pricing encontrado para ${platform}:`, pricingData)
        
        if (platform === 'instagram') {
          if (pricingData.story?.price) {
            platforms[platform].pricing.story = Number(pricingData.story.price) || 0
            console.log(`💰 [Form Load] Instagram Story price: ${pricingData.story.price}`)
          }
          if (pricingData.reel?.price) {
            platforms[platform].pricing.reel = Number(pricingData.reel.price) || 0
            console.log(`💰 [Form Load] Instagram Reel price: ${pricingData.reel.price}`)
          }
        }
        if (platform === 'youtube') {
          if (pricingData.insertion?.price) {
            platforms[platform].pricing.insertion = Number(pricingData.insertion.price) || 0
            console.log(`💰 [Form Load] YouTube Insertion price: ${pricingData.insertion.price}`)
          }
          if (pricingData.dedicated?.price) {
            platforms[platform].pricing.dedicated = Number(pricingData.dedicated.price) || 0
            console.log(`💰 [Form Load] YouTube Dedicated price: ${pricingData.dedicated.price}`)
          }
          if (pricingData.shorts?.price) {
            platforms[platform].pricing.shorts = Number(pricingData.shorts.price) || 0
            console.log(`💰 [Form Load] YouTube Shorts price: ${pricingData.shorts.price}`)
          }
        }
        if (platform === 'tiktok' && pricingData.video?.price) {
          platforms[platform].pricing.video = Number(pricingData.video.price) || 0
          console.log(`💰 [Form Load] TikTok Video price: ${pricingData.video.price}`)
        }
      } else {
        console.log(`💰 [Form Load] Nenhum pricing encontrado para ${platform}`)
      }
    })
  } else {
    console.log('💰 [Form Load] FALHA ao carregar pricing - Motivos possíveis:')
    console.log('💰 [Form Load] - data.currentPricing existe?', !!data.currentPricing)
    console.log('💰 [Form Load] - data.currentPricing.services existe?', !!data.currentPricing?.services)
    console.log('💰 [Form Load] - Platforms disponíveis?', Object.keys(platforms).length > 0)
    console.log('💰 [Form Load] - Platforms:', Object.keys(platforms))
  }
  
  // Fallback para estrutura antiga (financialData) se a nova não existir
  const financialData = data.financials || data.financialData
  if (!data.currentPricing && financialData?.prices && Object.keys(platforms).length > 0) {
    console.log('💰 [Form Load] Usando pricing da estrutura antiga como fallback')
    Object.keys(platforms).forEach(platform => {
      if (platform === 'instagram') {
        if (financialData.prices.instagramStory) {
          platforms[platform].pricing.story = Number(financialData.prices.instagramStory.price) || 0
        }
        if (financialData.prices.instagramReel) {
          platforms[platform].pricing.reel = Number(financialData.prices.instagramReel.price) || 0
        }
      }
      if (platform === 'youtube') {
        if (financialData.prices.youtubeInsertion) {
          platforms[platform].pricing.insertion = Number(financialData.prices.youtubeInsertion.price) || 0
        }
        if (financialData.prices.youtubeDedicated) {
          platforms[platform].pricing.dedicated = Number(financialData.prices.youtubeDedicated.price) || 0
        }
        if (financialData.prices.youtubeShorts) {
          platforms[platform].pricing.shorts = Number(financialData.prices.youtubeShorts.price) || 0
        }
      }
      if (platform === 'tiktok' && financialData.prices.tiktokVideo) {
        platforms[platform].pricing.video = Number(financialData.prices.tiktokVideo.price) || 0
      }
    })
  }
  
  // 📊 BUSCAR DEMOGRAPHICS DA NOVA ESTRUTURA SEPARADA (currentDemographics)
  if (data.currentDemographics && Array.isArray(data.currentDemographics) && Object.keys(platforms).length > 0) {
    console.log('📊 [Form Load] Carregando demographics da nova estrutura:', data.currentDemographics)
    
    data.currentDemographics.forEach((demographic: any) => {
      const platform = demographic.platform
      if (platforms[platform]) {
        console.log(`📊 [Form Load] Demographics encontrado para ${platform}:`, demographic)
        
        // Audiência por gênero
        if (demographic.audienceGender) {
          platforms[platform].audienceGender = {
            male: demographic.audienceGender.male || 0,
            female: demographic.audienceGender.female || 0,
            other: demographic.audienceGender.other || 0
          }
          console.log(`📊 [Form Load] ${platform} audience gender:`, platforms[platform].audienceGender)
        }
        
        // Localizações da audiência
        if (demographic.audienceLocations) {
          platforms[platform].audienceLocations = demographic.audienceLocations
          console.log(`📊 [Form Load] ${platform} audience locations:`, demographic.audienceLocations)
        }
        
        // Cidades da audiência
        if (demographic.audienceCities) {
          platforms[platform].audienceCities = demographic.audienceCities
          console.log(`📊 [Form Load] ${platform} audience cities:`, demographic.audienceCities)
        }
        
        // Faixas etárias da audiência - converter de array ou objeto
        if (demographic.audienceAgeRange) {
          let ageRangesArray: any[] = []
          
          if (Array.isArray(demographic.audienceAgeRange)) {
            // Se já é array, usar direto
            ageRangesArray = demographic.audienceAgeRange.filter((item: any) => 
              item && item.range && typeof item.percentage === 'number'
            )
          } else if (typeof demographic.audienceAgeRange === 'object') {
            // Se é objeto, converter para array
            ageRangesArray = Object.entries(demographic.audienceAgeRange).map(([range, percentage]) => ({
              range,
              percentage: Number(percentage) || 0
            })).filter(item => item.range && !isNaN(item.percentage))
          }
          
          platforms[platform].audienceAgeRange = ageRangesArray
          console.log(`📊 [Form Load] ${platform} audience age ranges convertidos:`, ageRangesArray)
                 } else {
           // Se não há dados, criar array vazio
           platforms[platform].audienceAgeRange = []
        }
      }
    })
  }

  console.log('📱 Plataformas convertidas do Firestore:', platforms)

  // Lógica melhorada para extrair avatar - verificar múltiplos campos
  const getAvatarFromData = (data: any): string | undefined => {
    console.log('🔍 Procurando avatar nos dados:', {
      avatar: data.avatar,
      avatarUrl: data.avatarUrl,
      foto: data.foto,
      image: data.image
    })

    // Lista de possíveis campos onde o avatar pode estar
    const avatarFields = [
      data.avatar,
      data.avatarUrl, 
      data.foto,
      data.image,
      data.picture,
      data.profileImage
    ]

    for (const field of avatarFields) {
      if (field && typeof field === 'string' && field.trim() !== '') {
        // Aceitar qualquer URL que comece com https:// ou data:
        if (field.startsWith('https://') || field.startsWith('data:image/')) {
          console.log('✅ Avatar encontrado:', field)
          return field
        }
        // Também aceitar paths relativos (para migração futura)
        if (field.startsWith('/uploads/') || field.startsWith('./uploads/')) {
          console.log('📁 Avatar local encontrado (será migrado):', field)
          return field
        }
      }
    }

    console.log('❌ Nenhum avatar válido encontrado')
    return undefined
  }

  const avatarUrl = getAvatarFromData(data)

  return {
    id: data.id || undefined, // Preservar o ID do documento Firestore
    personalInfo: {
      name: data.name || "",
      age: data.age || undefined,
      gender: genderMap[data.gender] || undefined,
      bio: data.bio || undefined,
      avatar: avatarUrl,
      verified: data.isVerified || data.verified || false,
    },
    location: {
      country: data.country || "Brasil",
      state: data.state || undefined,
      city: data.city || undefined,
      cep: data.cep || undefined,
    },
    contact: {
      email: data.email || undefined,
      whatsapp: data.whatsapp || data.phone || undefined,
    },
    business: {
      agencyName: financialData?.agencyName || undefined,
      responsibleName: financialData?.responsibleName || undefined,
      responsibleCapturer: financialData?.additionalData?.responsibleRecruiter || undefined,
      categories: data.mainCategories || data.categories || [],
      promotesTraders: financialData?.additionalData?.promotesTraders || false,
      brandPartnerships: data.brandPartnerships || [],
    },
    brands: [], // Campo para novas marcas selecionadas (não incluir marcas já associadas)
    platforms: Object.keys(platforms).length > 0 ? platforms as any : undefined,
    mainPlatform: data.mainNetwork || data.mainPlatform || undefined,
    metrics: {
      totalFollowers: data.totalFollowers || 0,
      overallEngagementRate: data.engagementRate || data.overallEngagementRate || 0,
    },
  }
}

// Função para converter dados do formulário para o formato do GraphQL
function convertToGraphQLFormat(formData: InfluencerFormData): any {
  console.log('🔄 Convertendo dados do formulário para GraphQL:', formData)
  console.log('🔍 [Avatar Debug] Campo personalInfo.avatar:', formData.personalInfo.avatar)

  // Mapear gênero para o GraphQL
  const genderMap: { [key: string]: string } = {
    'Masculino': 'male',
    'Feminino': 'female'
  }

  // Dados para o GraphQL CreateInfluencerInput - apenas campos com valores
  const graphqlInput: any = {}

  // Incluir apenas campos preenchidos
  if (formData.personalInfo?.name && formData.personalInfo.name.trim() !== '') {
    graphqlInput.name = formData.personalInfo.name.trim()
  }

  if (formData.contact?.email && formData.contact.email.trim() !== '') {
    graphqlInput.email = formData.contact.email.trim()
  }

  if (formData.contact?.whatsapp && formData.contact.whatsapp.trim() !== '') {
    graphqlInput.phone = formData.contact.whatsapp.trim()
    graphqlInput.whatsapp = formData.contact.whatsapp.trim()
  }

  if (formData.location?.country && formData.location.country.trim() !== '') {
    graphqlInput.country = formData.location.country.trim()
  }

  if (formData.location?.state && formData.location.state.trim() !== '') {
    graphqlInput.state = formData.location.state.trim()
  }

  if (formData.location?.city && formData.location.city.trim() !== '') {
    graphqlInput.city = formData.location.city.trim()
  }

  // Criar location combinada se city e state existirem
  if (formData.location?.city && formData.location?.state) {
    graphqlInput.location = `${formData.location.city}/${formData.location.state}`
  }

  if (formData.personalInfo?.age && formData.personalInfo.age > 0) {
    graphqlInput.age = formData.personalInfo.age
  }

  if (formData.personalInfo?.gender && genderMap[formData.personalInfo.gender]) {
    graphqlInput.gender = genderMap[formData.personalInfo.gender]
  }

  if (formData.personalInfo?.bio && formData.personalInfo.bio.trim() !== '') {
    graphqlInput.bio = formData.personalInfo.bio.trim()
  }

  if (formData.business?.categories && formData.business.categories.length > 0) {
    graphqlInput.category = formData.business.categories[0]
    graphqlInput.categories = formData.business.categories
  }

  if (formData.personalInfo?.avatar && formData.personalInfo.avatar.trim() !== '') {
    graphqlInput.avatar = formData.personalInfo.avatar.trim()
  }

  if (formData.personalInfo?.verified) {
    graphqlInput.isVerified = formData.personalInfo.verified
  }

  // Adicionar dados de métricas
  if (formData.metrics?.totalFollowers && formData.metrics.totalFollowers > 0) {
    graphqlInput.totalFollowers = formData.metrics.totalFollowers
  }

  if (formData.metrics?.overallEngagementRate && formData.metrics.overallEngagementRate > 0) {
    graphqlInput.engagementRate = formData.metrics.overallEngagementRate
  }

  // Dados profissionais
  if (formData.business?.agencyName && formData.business.agencyName.trim() !== '') {
    graphqlInput.agencyName = formData.business.agencyName.trim()
  }

  if (formData.business?.responsibleName && formData.business.responsibleName.trim() !== '') {
    graphqlInput.responsibleName = formData.business.responsibleName.trim()
  }

  if (formData.business?.promotesTraders) {
    graphqlInput.promotesTraders = formData.business.promotesTraders
  }

  // 💰 [CORREÇÃO CRÍTICA] Incluir dados de socialNetworks com pricing
  if (formData.platforms && Object.keys(formData.platforms).length > 0) {
    const socialNetworks: any = {}
    
    Object.keys(formData.platforms).forEach(platformKey => {
      const platformData = (formData.platforms as any)[platformKey]
      if (platformData && (platformData.followers > 0 || platformData.username)) {
        
        const socialNetworkData: any = {
          username: platformData.username || "",
          followers: Number(platformData.followers) || 0,
          engagementRate: Number(platformData.engagementRate) || 0,
          avgViews: Number(platformData.avgViews) || 0,
        }

        // Adicionar views específicas da plataforma
        if (platformData.views) {
          socialNetworkData.views = platformData.views
        }

        // Adicionar campos específicos por plataforma
        if (platformKey === 'instagram') {
          socialNetworkData.storiesViews = platformData.views?.storiesViews || 0
          socialNetworkData.reelsViews = platformData.views?.reelsViews || 0
        } else if (platformKey === 'youtube') {
          socialNetworkData.subscribers = Number(platformData.followers) || 0
          socialNetworkData.shortsViews = platformData.views?.shortsViews || 0
          socialNetworkData.longFormViews = platformData.views?.longFormViews || 0
        } else if (platformKey === 'tiktok') {
          socialNetworkData.videoViews = platformData.views?.videoViews || 0
        }

        // 💰 [CRUCIAL] Adicionar dados de pricing se existirem
        if (platformData.pricing && Object.keys(platformData.pricing).length > 0) {
          socialNetworkData.pricing = {}
          
          // Mapear todos os campos de pricing disponíveis
          Object.keys(platformData.pricing).forEach(priceType => {
            const priceValue = platformData.pricing[priceType]
            if (priceValue && priceValue > 0) {
              socialNetworkData.pricing[priceType] = Number(priceValue)
            }
          })
          
          console.log(`💰 [GraphQL Convert] ${platformKey} pricing incluído:`, socialNetworkData.pricing)
        }

        socialNetworks[platformKey] = socialNetworkData
        console.log(`📱 [GraphQL Convert] ${platformKey} dados incluídos:`, socialNetworkData)
      }
    })

    if (Object.keys(socialNetworks).length > 0) {
      graphqlInput.socialNetworks = socialNetworks
      console.log('💰 [GraphQL Convert] socialNetworks completo incluído:', socialNetworks)
    }
  }

  // Redes sociais como campos diretos (nova estrutura)
  if (formData.platforms && Object.keys(formData.platforms).length > 0) {
    Object.keys(formData.platforms).forEach(platformKey => {
      const platformData = (formData.platforms as any)[platformKey]
      if (platformData && (platformData.followers > 0 || platformData.username)) {
        
        // Instagram - campos diretos
        if (platformKey === 'instagram') {
          graphqlInput.instagramUsername = platformData.username || ''
          graphqlInput.instagramFollowers = Number(platformData.followers) || 0
          graphqlInput.instagramEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.instagramAvgViews = Number(platformData.avgViews) || 0
          
          // Campos específicos de visualizações do Instagram
          graphqlInput.instagramStoriesViews = Number(platformData.views?.storiesViews) || 0
          graphqlInput.instagramReelsViews = Number(platformData.views?.reelsViews) || 0
          
          console.log(`📱 [GraphQL Convert] Instagram campos diretos:`, {
            username: graphqlInput.instagramUsername,
            followers: graphqlInput.instagramFollowers,
            storiesViews: graphqlInput.instagramStoriesViews,
            reelsViews: graphqlInput.instagramReelsViews
          })
        }
        
        // TikTok - campos diretos
        if (platformKey === 'tiktok') {
          graphqlInput.tiktokUsername = platformData.username || ''
          graphqlInput.tiktokFollowers = Number(platformData.followers) || 0
          graphqlInput.tiktokEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.tiktokAvgViews = Number(platformData.avgViews) || 0
          
          // Campos específicos de visualizações do TikTok
          graphqlInput.tiktokVideoViews = Number(platformData.views?.videoViews) || 0
          
          console.log(`📱 [GraphQL Convert] TikTok campos diretos:`, {
            username: graphqlInput.tiktokUsername,
            followers: graphqlInput.tiktokFollowers,
            videoViews: graphqlInput.tiktokVideoViews
          })
        }
        
        // YouTube - campos diretos
        if (platformKey === 'youtube') {
          graphqlInput.youtubeUsername = platformData.username || ''
          graphqlInput.youtubeFollowers = Number(platformData.followers) || 0
          graphqlInput.youtubeSubscribers = Number(platformData.followers) || 0
          graphqlInput.youtubeEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.youtubeAvgViews = Number(platformData.avgViews) || 0
          
          // Campos específicos de visualizações do YouTube
          graphqlInput.youtubeShortsViews = Number(platformData.views?.shortsViews) || 0
          graphqlInput.youtubeLongFormViews = Number(platformData.views?.longFormViews) || 0
          
          console.log(`📱 [GraphQL Convert] YouTube campos diretos:`, {
            username: graphqlInput.youtubeUsername,
            followers: graphqlInput.youtubeFollowers,
            shortsViews: graphqlInput.youtubeShortsViews,
            longFormViews: graphqlInput.youtubeLongFormViews
          })
        }
        
        // Facebook - campos diretos
        if (platformKey === 'facebook') {
          graphqlInput.facebookUsername = platformData.username || ''
          graphqlInput.facebookFollowers = Number(platformData.followers) || 0
          graphqlInput.facebookEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.facebookAvgViews = Number(platformData.avgViews) || 0
        }
        
        // Twitch - campos diretos
        if (platformKey === 'twitch') {
          graphqlInput.twitchUsername = platformData.username || ''
          graphqlInput.twitchFollowers = Number(platformData.followers) || 0
          graphqlInput.twitchEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.twitchAvgViews = Number(platformData.avgViews) || 0
        }
        
        // Kwai - campos diretos
        if (platformKey === 'kwai') {
          graphqlInput.kwaiUsername = platformData.username || ''
          graphqlInput.kwaiFollowers = Number(platformData.followers) || 0
          graphqlInput.kwaiEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.kwaiAvgViews = Number(platformData.avgViews) || 0
        }
      }
    })
  }

  // Plataforma principal
  console.log('🎯 [GraphQL Convert] === DEBUG MAINPLATFORM ===')
  console.log('🎯 [GraphQL Convert] formData completo:', JSON.stringify(formData, null, 2))
  console.log('🎯 [GraphQL Convert] formData.mainPlatform valor:', formData.mainPlatform)
  console.log('🎯 [GraphQL Convert] formData.mainPlatform tipo:', typeof formData.mainPlatform)
  console.log('🎯 [GraphQL Convert] formData.mainPlatform trim?:', formData.mainPlatform?.trim?.())
  
  if (formData.mainPlatform && formData.mainPlatform.trim() !== '') {
    graphqlInput.mainPlatform = formData.mainPlatform.trim()
    graphqlInput.mainNetwork = formData.mainPlatform.trim() // Compatibilidade
    console.log('✅ [GraphQL Convert] mainPlatform INCLUÍDO no input:', graphqlInput.mainPlatform)
    console.log('✅ [GraphQL Convert] mainNetwork INCLUÍDO no input:', graphqlInput.mainNetwork)
  } else {
    console.log('❌ [GraphQL Convert] mainPlatform VAZIO ou INVÁLIDO')
    console.log('❌ [GraphQL Convert] Condições: existe?', !!formData.mainPlatform, 'trim vazio?', formData.mainPlatform?.trim() === '')
  }

  // Campos padrão sempre presentes
  graphqlInput.isAvailable = true
  graphqlInput.status = 'active'

  console.log('💾 Dados convertidos para GraphQL (apenas campos preenchidos):', graphqlInput)
  console.log('🔍 [Avatar Debug] Avatar no input final:', graphqlInput.avatar)
  console.log('🔍 [MainPlatform Debug] mainPlatform no input final:', graphqlInput.mainPlatform)
  console.log('💰 [GraphQL Convert] socialNetworks final:', graphqlInput.socialNetworks)
  
  return graphqlInput
}

// Função para converter dados do formulário para o formato do Firestore
function convertToFirestoreFormat(formData: InfluencerFormData): { influencerData: any, financialData: any } {
  console.log('🔄 Convertendo dados do formulário para Firestore:', formData)

  // Mapear gênero para o Firestore
  const genderMap: { [key: string]: string } = {
    'Masculino': 'male',
    'Feminino': 'female'
  }

  // Dados principais do influenciador
  const influencerData: any = {
    name: formData.personalInfo.name,
    age: formData.personalInfo.age,
    gender: formData.personalInfo.gender ? genderMap[formData.personalInfo.gender] : 'not_specified',
    bio: formData.personalInfo.bio,
    avatar: formData.personalInfo.avatar,
    isVerified: formData.personalInfo.verified,
    country: formData.location.country || "Brasil",
    state: formData.location.state,
    city: formData.location.city,
    location: formData.location.city && formData.location.state 
      ? `${formData.location.city}/${formData.location.state}` 
      : undefined,
    email: formData.contact.email,
    whatsapp: formData.contact.whatsapp,
    phone: formData.contact.whatsapp,
    mainCategories: formData.business.categories || [],
    categories: formData.business.categories || [],
    category: formData.business.categories?.[0] || "",
    brandPartnerships: formData.business.brandPartnerships || [],
    totalFollowers: formData.metrics?.totalFollowers || 0,
    engagementRate: formData.metrics?.overallEngagementRate || 0,
    overallEngagementRate: formData.metrics?.overallEngagementRate || 0,
    mainNetwork: formData.mainPlatform,
    mainPlatform: formData.mainPlatform,
    isAvailable: true,
    status: 'active',
    socialNetworks: {},
    // Distribuição de gênero da audiência (padrão)
    audienceGender: { male: 0, female: 0, other: 0 }
  }

  // Converter plataformas para o formato do Firestore
  if (formData.platforms) {
    Object.keys(formData.platforms).forEach(platform => {
      const platformData = (formData.platforms as any)[platform]
      if (platformData && platformData.followers > 0) {
        influencerData.socialNetworks[platform] = {
          username: platformData.username || "",
          followers: Number(platformData.followers) || 0,
          engagementRate: Number(platformData.engagementRate) || 0,
          avgViews: Number(platformData.avgViews) || 0,
          audienceGender: platformData.audienceGender || { male: 0, female: 0, other: 0 },
          audienceLocations: platformData.audienceLocations || [],
          audienceCities: platformData.audienceCities || [],
          audienceAgeRange: platformData.audienceAgeRange || {},
          brandHistory: platformData.brandHistory || [],
          screenshots: platformData.screenshots || [],
          pricing: platformData.pricing || {},
          metrics: platformData.metrics || {}
        }
      }
    })
  }

  // Dados financeiros separados
  const financialData: any = {
    responsibleName: formData.business.responsibleName,
    agencyName: formData.business.agencyName,
    email: formData.contact.email,
    whatsapp: formData.contact.whatsapp,
    prices: {},
    brandHistory: { instagram: [], tiktok: [], youtube: [] },
    additionalData: {
      promotesTraders: formData.business.promotesTraders,
      responsibleRecruiter: formData.business.responsibleCapturer,
      contentType: [],
      socialMediaScreenshots: [],
      notes: "",
      documents: []
    }
  }

  // Extrair preços das plataformas
  if (formData.platforms) {
    Object.keys(formData.platforms).forEach(platformKey => {
      const platformData = (formData.platforms as any)[platformKey]
      if (platformData?.pricing) {
        if (platformKey === 'instagram') {
          if (platformData.pricing.story) {
            financialData.prices.instagramStory = { 
              name: "Stories", 
              price: Number(platformData.pricing.story) 
            }
          }
          if (platformData.pricing.reel) {
            financialData.prices.instagramReel = { 
              name: "Reels", 
              price: Number(platformData.pricing.reel) 
            }
          }
        }
        if (platformKey === 'youtube') {
          if (platformData.pricing.insertion) {
            financialData.prices.youtubeInsertion = { 
              name: "Inserção", 
              price: Number(platformData.pricing.insertion) 
            }
          }
          if (platformData.pricing.dedicated) {
            financialData.prices.youtubeDedicated = { 
              name: "Dedicado", 
              price: Number(platformData.pricing.dedicated) 
            }
          }
          if (platformData.pricing.shorts) {
            financialData.prices.youtubeShorts = { 
              name: "Shorts", 
              price: Number(platformData.pricing.shorts) 
            }
          }
        }
        if (platformKey === 'tiktok' && platformData.pricing.video) {
          financialData.prices.tiktokVideo = { 
            name: "Vídeo", 
            price: Number(platformData.pricing.video) 
          }
        }

        // Extrair histórico de marcas
        if (platformData.brandHistory && Array.isArray(platformData.brandHistory)) {
          if (platformKey === 'instagram') {
            financialData.brandHistory.instagram = platformData.brandHistory as string[]
          } else if (platformKey === 'tiktok') {
            financialData.brandHistory.tiktok = platformData.brandHistory as string[]
          } else if (platformKey === 'youtube') {
            financialData.brandHistory.youtube = platformData.brandHistory as string[]
          }
        }
      }
    })
  }

  console.log('💾 Dados convertidos para Firestore:')
  console.log('📱 influencerData:', influencerData)
  console.log('💰 financialData:', financialData)

  return { influencerData, financialData }
}

export function useInfluencerForm({ initialData, onSubmit }: UseInfluencerFormProps = {}) {
  const { toast } = useToast()
  const lastInitialDataRef = useRef<any>(null)
  const [activePlatforms, setActivePlatforms] = useState<SocialPlatform[]>([])
  
  // Formulário principal
  const form = useForm<InfluencerFormData>({
    resolver: zodResolver(InfluencerFormSchema),
    defaultValues: {
      personalInfo: { name: "", verified: false },
      location: { country: "Brasil" },
      contact: {},
      business: { promotesTraders: false, brandPartnerships: [] },
      brands: [] // Adicionar campo brands aos valores padrão
    }
  })

  // Inicializar e atualizar formulário
  useEffect(() => {
    if (initialData && JSON.stringify(initialData) !== JSON.stringify(lastInitialDataRef.current)) {
      console.log('🚀 Inicializando/atualizando formulário com dados do Firestore')
      console.log('🚀 initialData completo:', initialData)
      console.log('💰 [Form Load DEBUG] currentPricing na initialData:', initialData.currentPricing)
      console.log('📊 [Form Load DEBUG] currentDemographics na initialData:', initialData.currentDemographics)
      
      lastInitialDataRef.current = initialData
      const formData = convertFromFirestoreFormat(initialData)
      
      console.log('🚀 formData convertido:', formData)
      console.log('💰 [Form Load DEBUG] Pricing nos platforms:', formData.platforms ? Object.keys(formData.platforms).map(platform => ({
        platform,
        pricing: (formData.platforms as any)[platform]?.pricing
      })) : 'sem platforms')
      console.log('📊 [Form Load DEBUG] Demographics nos platforms:', formData.platforms ? Object.keys(formData.platforms).map(platform => ({
        platform,
        audienceGender: (formData.platforms as any)[platform]?.audienceGender
      })) : 'sem platforms')
      
      form.reset(formData)
      
      // Identificar plataformas ativas
      if (formData.platforms) {
        const platforms = Object.keys(formData.platforms).filter(key => {
          const platformData = (formData.platforms as any)[key]
          return platformData && typeof platformData === 'object'
        }) as SocialPlatform[]
        setActivePlatforms(platforms)
        console.log('📱 Plataformas ativas identificadas:', platforms)
      }
    }
  }, [initialData, form])

  // Calcular métricas automaticamente
  const calculateMetrics = useCallback(() => {
    const platforms = form.getValues('platforms') || {}
    let totalFollowers = 0
    let totalEngagement = 0
    let platformCount = 0

    Object.values(platforms).forEach((platform: any) => {
      if (platform?.followers) {
        totalFollowers += Number(platform.followers) || 0
        if (platform.engagementRate) {
          totalEngagement += Number(platform.engagementRate) || 0
          platformCount++
        }
      }
    })

    const overallEngagementRate = platformCount > 0 ? totalEngagement / platformCount : 0

    form.setValue('metrics', {
      totalFollowers,
      overallEngagementRate: Math.round(overallEngagementRate * 100) / 100
    }, { shouldValidate: false })
  }, [form])

  // Gerenciar plataformas
  const addPlatform = useCallback((platform: SocialPlatform) => {
    if (!activePlatforms.includes(platform)) {
      const newPlatforms = [...activePlatforms, platform]
      setActivePlatforms(newPlatforms)
      
      // Adicionar plataforma no formulário
      const currentPlatforms = form.getValues('platforms') || {}
      
      const newPlatformData = {
        username: "",
        followers: 0,
        engagementRate: 0,
        avgViews: 0,
        pricing: {},
        metrics: {},
        audienceGender: { male: 0, female: 0, other: 0 },
        audienceLocations: [],
        audienceCities: [],
        audienceAgeRange: [],
        brandHistory: [],
        screenshots: []
      }
      
      const updatedPlatforms = {
        ...currentPlatforms,
        [platform as string]: newPlatformData
      } as any
      
      form.setValue('platforms', updatedPlatforms, { shouldValidate: false })
      
      console.log('➕ Plataforma adicionada:', platform)
    }
  }, [activePlatforms, form])

  const removePlatform = useCallback((platform: SocialPlatform) => {
    const newPlatforms = activePlatforms.filter(p => p !== platform)
    setActivePlatforms(newPlatforms)
    
          // Remover plataforma do formulário
      const currentPlatforms = form.getValues('platforms') || {}
      const platformKey = platform as string
      const { [platformKey]: removed, ...remainingPlatforms } = currentPlatforms as any
    
    form.setValue('platforms', remainingPlatforms, { shouldValidate: false })
    
    console.log('➖ Plataforma removida:', platform)
  }, [activePlatforms, form])

  const availablePlatforms = (Object.keys(PLATFORM_CONFIG) as SocialPlatform[]).filter(
    platform => !activePlatforms.includes(platform)
  )

  // Submit otimizado para GraphQL
  const handleSubmit = useCallback(async () => {
    try {
      console.log('🚀 [DEBUG] handleSubmit iniciado!')
      console.log('🚀 Iniciando submit para GraphQL')
      
      const formData = form.getValues()
      console.log('📋 [DEBUG] Dados brutos do formulário:', formData)
      console.log('📋 Dados do formulário:', formData)
      
      // 🎯 DEBUG ESPECÍFICO PARA FAIXAS ETÁRIAS
      if (formData.platforms) {
        Object.keys(formData.platforms).forEach(platform => {
          const platformData = (formData.platforms as any)[platform]
          if (platformData?.audienceAgeRange) {
            console.log(`🎯 [DEBUG FAIXAS ETÁRIAS] ${platform}.audienceAgeRange:`, platformData.audienceAgeRange)
            console.log(`🎯 [DEBUG FAIXAS ETÁRIAS] Tipo:`, typeof platformData.audienceAgeRange)
            console.log(`🎯 [DEBUG FAIXAS ETÁRIAS] É array?:`, Array.isArray(platformData.audienceAgeRange))
            console.log(`🎯 [DEBUG FAIXAS ETÁRIAS] Length:`, platformData.audienceAgeRange?.length)
          } else {
            console.log(`❌ [DEBUG FAIXAS ETÁRIAS] ${platform} NÃO TEM audienceAgeRange`)
          }
        })
      }
      
      console.log('✅ [DEBUG] Enviando dados sem validações obrigatórias')

      // 📸 UPLOAD DO AVATAR SE HOUVER ARQUIVO SELECIONADO
      if (formData.personalInfo?.avatarFile) {
        console.log('📸 [Avatar] Arquivo detectado - fazendo upload...')
        
        try {
          const avatarFile = formData.personalInfo.avatarFile as File
          const influencerId = initialData?.id || `temp_${Date.now()}`
          
          // Fazer upload via API
          const formDataUpload = new FormData()
          formDataUpload.append('avatar', avatarFile)
          formDataUpload.append('influencerId', influencerId)
          
          const uploadResponse = await fetch('/api/influencers/avatar', {
            method: 'POST',
            body: formDataUpload
          })
          
          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json()
            throw new Error(errorData.error || 'Erro ao fazer upload do avatar')
          }
          
          const uploadResult = await uploadResponse.json()
          console.log('✅ [Avatar] Upload concluído:', uploadResult.avatarUrl)
          
          // Atualizar o formulário com a URL do avatar
          form.setValue('personalInfo.avatar', uploadResult.avatarUrl, { shouldValidate: false })
          
          // Limpar o arquivo temporário
          form.setValue('personalInfo.avatarFile', undefined as any, { shouldValidate: false })
          
          // Atualizar os dados do formulário
          formData.personalInfo.avatar = uploadResult.avatarUrl
          delete formData.personalInfo.avatarFile
          
        } catch (uploadError) {
          console.error('❌ [Avatar] Erro no upload:', uploadError)
          toast({
            title: "Erro no Upload",
            description: "Falha ao enviar a imagem do avatar. O influenciador será criado sem foto.",
            variant: "destructive",
          })
          // Continuar sem avatar
        }
      }
      
      // 📸 PROCESSAR SCREENSHOTS PENDENTES
      const pendingScreenshots = form.getValues('pendingScreenshots') || []
      console.log('📸 [FORM-SUBMIT] Verificando screenshots pendentes:', pendingScreenshots.length)
      console.log('📸 [FORM-SUBMIT] Dados pendingScreenshots:', pendingScreenshots)
      
      if (pendingScreenshots && pendingScreenshots.length > 0) {
        console.log('📸 [FORM-SUBMIT] ✅ Processando arquivos pendentes:', pendingScreenshots.length)
        
        try {
          const influencerId = initialData?.id || form.getValues('id') || `temp_${Date.now()}`
          console.log('📸 [FORM-SUBMIT] ID do influenciador:', influencerId)
          
          const uploadPromises = pendingScreenshots.map(async (pendingFile: any, index: number) => {
            try {
              console.log(`📸 [FORM-SUBMIT] Processando arquivo ${index + 1}/${pendingScreenshots.length}:`, {
                platform: pendingFile.platform,
                filename: pendingFile.file?.name,
                size: pendingFile.file?.size,
                type: pendingFile.file?.type
              })
              
              // Comprimir imagem antes de converter para base64
              const compressedFile = await compressImage(pendingFile.file, 0.7, 1920) // 70% qualidade, max 1920px
              console.log(`📸 [FORM-SUBMIT] Arquivo comprimido: ${pendingFile.file.size} -> ${compressedFile.size} bytes`)
              
              // Converter arquivo comprimido para base64 para envio via GraphQL
              const base64 = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader()
                reader.onload = () => resolve(reader.result as string)
                reader.onerror = reject
                reader.readAsDataURL(compressedFile)
              })

              console.log(`📸 [FORM-SUBMIT] Enviando via GraphQL mutation com base64`)
              
              // Fazer upload completo via GraphQL (arquivo + metadata)
              const graphqlResponse = await fetch('/api/graphql', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  query: `
                    mutation UploadScreenshot($input: CreateScreenshotInput!) {
                      uploadScreenshot(input: $input) {
                        id
                        influencerId
                        platform
                        url
                        filename
                        size
                        contentType
                        uploadedAt
                        uploadedBy
                      }
                    }
                  `,
                  variables: {
                    input: {
                      influencerId,
                      platform: pendingFile.platform,
                      filename: compressedFile.name,
                      size: compressedFile.size,
                      contentType: compressedFile.type,
                      fileData: base64 // Enviar arquivo comprimido como base64
                    }
                  }
                })
              })

              if (!graphqlResponse.ok) {
                const errorData = await graphqlResponse.json()
                console.error(`📸 [FORM-SUBMIT] Erro GraphQL:`, errorData)
                throw new Error(errorData.error || 'Erro ao fazer upload via GraphQL')
              }

              const graphqlResult = await graphqlResponse.json()
              
              if (graphqlResult.errors) {
                console.error(`📸 [FORM-SUBMIT] Erros GraphQL:`, graphqlResult.errors)
                throw new Error(graphqlResult.errors[0]?.message || 'Erro GraphQL desconhecido')
              }

              const uploadData = graphqlResult.data?.uploadScreenshot
              console.log('✅ [FORM-SUBMIT] Upload GraphQL concluído para', pendingFile.platform, ':', uploadData?.url)
              
              return {
                platform: pendingFile.platform,
                url: uploadData?.url || ''
              }
            } catch (error) {
              console.error('❌ [FORM-SUBMIT] Erro no upload de', pendingFile.file?.name, ':', error)
              return null
            }
          })

          console.log(`📸 [FORM-SUBMIT] Aguardando ${uploadPromises.length} uploads...`)
          const uploadResults = await Promise.all(uploadPromises)
          const successfulUploads = uploadResults.filter(Boolean)
          
          console.log(`📸 [FORM-SUBMIT] Uploads concluídos: ${successfulUploads.length}/${uploadResults.length}`)
          
          if (successfulUploads.length > 0) {
            console.log('✅ [FORM-SUBMIT] Atualizando plataformas com URLs dos screenshots')
            
            // Atualizar URLs dos screenshots nas plataformas
            const currentPlatforms = form.getValues('platforms') || {}
            console.log('📸 [FORM-SUBMIT] Plataformas atuais:', Object.keys(currentPlatforms))
            
            successfulUploads.forEach((upload: any) => {
              const platformKey = upload.platform
              const existingPlatform = (currentPlatforms as any)[platformKey]
              
              console.log(`📸 [FORM-SUBMIT] Atualizando plataforma ${platformKey}:`, existingPlatform ? 'existe' : 'não existe')
              
              if (existingPlatform) {
                if (!existingPlatform.screenshots) {
                  existingPlatform.screenshots = []
                }
                existingPlatform.screenshots.push(upload.url)
                
                console.log(`📸 [FORM-SUBMIT] Screenshots da plataforma ${platformKey}:`, existingPlatform.screenshots.length)
                
                // Atualizar a plataforma específica
                form.setValue(`platforms.${platformKey}.screenshots` as any, existingPlatform.screenshots, { shouldValidate: false })
              }
            })
            
            toast({
              title: "Screenshots enviados",
              description: `${successfulUploads.length} screenshot(s) enviado(s) com sucesso`
            })
          }
          
          // Limpar arquivos pendentes
          console.log('📸 [FORM-SUBMIT] Limpando arquivos pendentes')
          form.setValue('pendingScreenshots', [], { shouldValidate: false })
          
        } catch (screenshotError) {
          console.error('❌ [FORM-SUBMIT] Erro geral no processamento:', screenshotError)
          toast({
            title: "Erro nos Screenshots",
            description: "Alguns screenshots podem não ter sido enviados. O influenciador será salvo sem eles.",
            variant: "destructive",
          })
        }
      } else {
        console.log('📸 [FORM-SUBMIT] ❌ Nenhum screenshot pendente encontrado')
      }
      
      // Chamar onSubmit customizado se fornecido
      if (onSubmit) {
        console.log('🔄 [DEBUG] Chamando onSubmit customizado')
        await onSubmit(formData)
      }
      
      console.log('✅ [DEBUG] Submit concluído com sucesso!')
      toast({
        title: "Sucesso!",
        description: "Influenciador salvo com sucesso.",
      })
      
    } catch (error) {
      console.error('❌ [DEBUG] Erro no handleSubmit:', error)
      
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao salvar influenciador. Tente novamente.",
        variant: "destructive",
      })
    }
  }, [form, onSubmit, toast, initialData])

  const formHandleSubmit = form.handleSubmit(handleSubmit)

  const resetForm = useCallback(() => {
    form.reset()
    setActivePlatforms([])
  }, [form])

  return {
    form,
    handleSubmit: formHandleSubmit,
    resetForm,
    isSubmitting: form.formState.isSubmitting,
    errors: form.formState.errors,
    isValid: form.formState.isValid,
    calculateMetrics,
    activePlatforms,
    addPlatform,
    removePlatform,
    availablePlatforms
  }
} 

