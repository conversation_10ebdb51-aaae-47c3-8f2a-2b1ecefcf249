// SCHEMA GRAPHQL SIMPLIFICADO PARA O SISTEMA DE CRM DE INFLUENCIADORES
// Versão inicial focada no essencial

// Schema GraphQL essencial
export const typeDefs = `
  # ===== TIPOS BASE =====
  
  scalar Date
  scalar JSON
  
  # ===== TIPOS DE PRICING DENORMALIZADO =====
  
  type DenormalizedPricing {
    hasFinancialData: Boolean!
    priceRange: String!
    avgPrice: Float!
    lastPriceUpdate: Date!
    isNegotiable: Boolean!
  }
  
  # ===== TIPOS PRINCIPAIS =====
  

  

  
  type Influencer {
    id: ID!
    userId: ID!
    
    # Dados pessoais
    name: String!
    email: String
    phone: String
    whatsapp: String
    
    # Localização
    country: String!
    state: String!
    city: String!
    location: String
    
    # Dados demográficos
    age: Int
    gender: String!
    bio: String
    
    # Visuais
    avatar: String
    backgroundImage: String
    gradient: String
    
    # Categorização
    category: String
    categories: [String!]
    
    # Métricas
    totalFollowers: Float!
    totalViews: Float!
    engagementRate: Float!
    rating: Float!
    
    # Status
    isVerified: Boolean
    isAvailable: Boolean
    status: String
    
    # Redes sociais (campos diretos)
    instagramUsername: String
    instagramFollowers: Float
    instagramEngagementRate: Float
    instagramAvgViews: Float
    instagramStoriesViews: Float
    instagramReelsViews: Float
    
    tiktokUsername: String
    tiktokFollowers: Float
    tiktokEngagementRate: Float
    tiktokAvgViews: Float
    tiktokVideoViews: Float
    
    youtubeUsername: String
    youtubeFollowers: Float
    youtubeSubscribers: Float
    youtubeEngagementRate: Float
    youtubeAvgViews: Float
    youtubeShortsViews: Float
    youtubeLongFormViews: Float
    
    facebookUsername: String
    facebookFollowers: Float
    facebookEngagementRate: Float
    facebookAvgViews: Float
    facebookViews: Float
    facebookReelsViews: Float
    facebookStoriesViews: Float
    facebookPost: Float
    
    twitchUsername: String
    twitchFollowers: Float
    twitchEngagementRate: Float
    twitchViews: Float
    twitchStream: Float
    
    kwaiUsername: String
    kwaiFollowers: Float
    kwaiEngagementRate: Float
    kwaiViews: Float
    kwaiVideo: Float
    
    # Configurações profissionais
    promotesTraders: Boolean
    responsibleName: String
    agencyName: String
    responsibleCapturer: String
    
    # Rede social principal
    mainNetwork: String
    mainPlatform: String
    
    # Pricing denormalizado (deprecated - manter por compatibilidade)
    pricing: DenormalizedPricing
    
    # Pricing separado (novo)
    currentPricing: InfluencerPricing
    pricingHistory: [InfluencerPricing!]!
    
    # Demographics separado (novo)
    currentDemographics: [AudienceDemographic!]!
    consolidatedDemographics: ConsolidatedDemographics
    
    # Dados financeiros (nested resolver)
    financial: InfluencerFinancial
    
    # Screenshots das redes sociais
    screenshots: [Screenshot!]!
    
    # Orçamentos organizados por plataforma
    budgets: InfluencerBudgets
    
    # Relacionamentos (placeholders para future implementação)
    campaigns: [String!]!
    brands: [String!]!
    tags: [String!]!
    notes: [String!]!
    mainCategoriesData: [String!]!
    
    # Meta
    createdAt: Date!
    updatedAt: Date!
  }
  
  # ===== PRICING DENORMALIZADO (DEPRECATED) =====
  
  type DenormalizedPricing {
    hasFinancialData: Boolean
    priceRange: String
    avgPrice: Float
    isNegotiable: Boolean
    lastPriceUpdate: Date
  }
  
  # ===== DEMOGRAPHICS SEPARADO =====
  
  type AudienceGender {
    male: Float!
    female: Float!
    other: Float!
  }
  
  type AudienceAgeRange {
    range: String!
    percentage: Float!
  }
  
  type AudienceLocation {
    country: String!
    percentage: Float!
  }
  
  type AudienceCity {
    city: String!
    percentage: Float!
  }
  
  type AudienceDemographic {
    id: ID!
    influencerId: ID!
    userId: ID!
    platform: String!
    audienceGender: AudienceGender!
    audienceLocations: [AudienceLocation!]!
    audienceCities: [AudienceCity!]!
    audienceAgeRange: [AudienceAgeRange!]!
    captureDate: Date!
    isActive: Boolean!
    source: String!
    createdAt: Date!
    createdBy: String!
    updatedAt: Date!
    updatedBy: String!
  }
  
  type ConsolidatedDemographics {
    audienceGender: AudienceGender!
    topLocations: [AudienceLocation!]!
    topCities: [AudienceCity!]!
    dominantAgeRange: String!
  }

  # ===== SCREENSHOTS =====
  
  type Screenshot {
    id: ID!
    influencerId: ID!
    platform: String!
    url: String!
    filename: String!
    size: Int!
    contentType: String!
    uploadedAt: Date!
    uploadedBy: String!
  }

  # ===== AVATAR =====
  
  type AvatarUploadResult {
    id: ID!
    userId: ID!
    avatarUrl: String!
    filename: String!
    size: Int!
    contentType: String!
    uploadedAt: Date!
    success: Boolean!
    message: String!
  }

  # ===== PRICING SEPARADO =====
  
  type PlatformPrice {
    price: Float!
    currency: String!
  }
  
  type InstagramPricing {
    story: PlatformPrice
    reel: PlatformPrice
    post: PlatformPrice
  }
  
  type TikTokPricing {
    video: PlatformPrice
  }
  
  type YouTubePricing {
    shorts: PlatformPrice
    insertion: PlatformPrice
    dedicated: PlatformPrice
  }
  
  type FacebookPricing {
    post: PlatformPrice
    story: PlatformPrice
  }
  
  type TwitchPricing {
    stream: PlatformPrice
  }
  
  type KwaiPricing {
    video: PlatformPrice
  }
  
  type PricingServices {
    instagram: InstagramPricing
    tiktok: TikTokPricing
    youtube: YouTubePricing
    facebook: FacebookPricing
    twitch: TwitchPricing
    kwai: KwaiPricing
  }
  
  type InfluencerPricing {
    id: ID!
    influencerId: ID!
    userId: ID!
    services: PricingServices!
    isActive: Boolean!
    validFrom: Date!
    validUntil: Date
    notes: String
    clientSpecific: String
    createdAt: Date!
    createdBy: String!
    updatedAt: Date!
    updatedBy: String!
  }
  
  # ===== DADOS FINANCEIROS SIMPLIFICADOS =====
  
  type InfluencerFinancial {
    id: ID!
    influencerId: ID!
    responsibleName: String!
    agencyName: String
    email: String!
    whatsapp: String!
    # Preços removidos - agora são buscados via InfluencerPricing
    createdAt: Date!
    updatedAt: Date!
  }
  
  # ===== CATEGORIAS (definição removida - usando definição completa no final do arquivo) =====
  
  # ===== MARCAS =====
  
  type Brand {
    id: ID!
    userId: ID!
    name: String!
    description: String
    logo: String
    website: String
    industry: String
    size: String
    status: String!
    createdAt: Date!
    updatedAt: Date!
  }

  # ===== ASSOCIAÇÕES MARCA-INFLUENCIADOR =====
  
  type BrandInfluencerAssociation {
    id: ID!
    userId: ID!
    brandId: ID!
    brandName: String!
    brandLogo: String
    influencerId: ID!
    influencerName: String!
    influencerAvatar: String
    status: AssociationStatus!
    tags: [String!]!
    notes: String
    createdAt: Date!
    updatedAt: Date!
    createdBy: String!
    updatedBy: String!
  }
  
  enum AssociationStatus {
    active
    inactive
    pending
    archived
  }

  # ===== ORÇAMENTOS =====
  
  type Budget {
    id: ID!
    
    # Relacionamentos obrigatórios
    influencerId: ID!
    influencerName: String!
    userId: ID!
    brandId: ID!
    brandName: String
    proposalId: ID # 🆕 Relacionamento com proposta
    
    # Dados do orçamento
    amount: Float!
    originalPrice: Float # 🆕 Preço original do influenciador
    currency: String!
    description: String
    serviceType: ServiceType!
    
    # Serviços detalhados
    services: ServiceBudget
    
    # Workflow e status
    status: BudgetStatus!
    expiresAt: Date
    
    # Contrapropostas
    hasCounterProposal: Boolean!
    counterProposals: [CounterProposal!]!
    
    # 🆕 Contrapropostas de colaboradores
    hasCollaboratorCounterProposals: Boolean!
    collaboratorCounterProposals: [CollaboratorCounterProposal!]!
    
    # Metadados
    createdAt: Date!
    updatedAt: Date!
    createdBy: String
    updatedBy: String
  }

  # 🆕 SISTEMA DE PERMISSÕES PARA ORÇAMENTOS
  type BudgetPermissions {
    canView: Boolean!
    canEdit: Boolean!
    canCreateCounterProposal: Boolean!
    canManageBudgets: Boolean!
    canApproveCounterProposals: Boolean!
    canViewFinancialData: Boolean!
  }

  # 🆕 DOCUMENTO DA PROPOSTA
  type Document {
    id: ID!
    name: String!
    url: String!
    type: String!
    size: Int!
    uploadedAt: Date!
    uploadedBy: String!
    proposalId: ID
    influencerId: ID!
  }

  # 🆕 RESULTADO DE ORÇAMENTOS DE PROPOSTA COM PERMISSÕES
  type ProposalBudgetsResult {
    proposalId: ID!
    permissions: BudgetPermissions!
    budgets: [Budget!]!
    documents: [Document!] # 🆕 NOVO: Documentos do influenciador na proposta
    totalCount: Int!
    userRole: String # 'owner', 'admin', 'editor', 'viewer'
    errors: [String!]!
    processingTimeMs: Int!
  }

  # 🆕 CONTRAPROPOSTA DE COLABORADOR
  type CollaboratorCounterProposal {
    id: ID!
    budgetId: ID!
    proposalId: ID!
    influencerId: ID!
    
    # Dados da contraproposta
    originalAmount: Float!
    proposedAmount: Float!
    currency: String!
    notes: String
    serviceType: String!
    quantity: Int
    
    # Colaborador que fez a contraproposta
    proposedBy: CollaboratorInfo!
    
    # Status e workflow
    status: CollaboratorCounterProposalStatus!
    
    # Metadados
    createdAt: Date!
    updatedAt: Date!
    reviewedAt: Date
    reviewedBy: String
    reviewNote: String
  }

  # 🆕 INFORMAÇÕES DO COLABORADOR
  type CollaboratorInfo {
    userId: ID!
    userName: String!
    userEmail: String!
    collaboratorRole: CollaboratorRole!
  }

  # 🆕 STATUS DO INFLUENCER NA PROPOSTA
  type ProposalInfluencerStatus {
    influencerId: ID!
    status: String!
    addedAt: Date
    updatedAt: Date
    addedBy: String
    updatedBy: String
  }

  # 🆕 ENUMS PARA COLABORADORES
  enum CollaboratorRole {
    editor
    viewer
    admin
  }

  enum CollaboratorCounterProposalStatus {
    pending
    accepted
    rejected
    expired
  }
  
  enum ServiceType {
    personalizado
    # Formatos originais (camelCase)
    instagramStory
    instagramReel
    instagramPost
    youtubeInsertion
    youtubeDedicated
    youtubeShorts
    tiktokVideo
    facebookPost
    twitchStream
    kwaiVideo
    # Formatos com underscore (snake_case)
    instagram_story
    instagram_reel
    instagram_post
    youtube_insertion
    youtube_dedicated
    youtube_shorts
    tiktok_video
    facebook_post
    twitch_stream
    kwai_video
    # Formatos alternativos para compatibilidade
    youtube_video
    instagram_stories
    youtube_longform
  }
  
  enum BudgetStatus {
    draft
    sent
    pending
    negotiating
    approved
    rejected
    expired
    cancelled
  }
  
  type ServiceBudget {
    instagram: InstagramServices
    youtube: YouTubeServices
    tiktok: TikTokServices
    facebook: FacebookServices
    twitch: TwitchServices
    kwai: KwaiServices
  }
  
  type InstagramServices {
    story: PlatformPrice
    reel: PlatformPrice
    post: PlatformPrice
  }
  
  type YouTubeServices {
    shorts: PlatformPrice
    insertion: PlatformPrice
    dedicated: PlatformPrice
  }
  
  type TikTokServices {
    video: PlatformPrice
  }
  
  type FacebookServices {
    post: PlatformPrice
    story: PlatformPrice
  }
  
  type TwitchServices {
    stream: PlatformPrice
  }
  
  type KwaiServices {
    video: PlatformPrice
  }
  
  type PlatformPrice {
    price: Float!
    currency: String!
    quantity: Int
    deliveryTime: Int
    notes: String
  }
  
  type CounterProposal {
    id: ID!
    budgetId: ID
    proposedAmount: Float!
    originalAmount: Float
    currency: String
    notes: String
    proposedBy: String!
    proposedAt: String!
    status: CounterProposalStatus!
    type: String
    quantity: Int
    updatedAt: String
  }
  
  enum CounterProposalStatus {
    pending
    accepted
    rejected
  }
  
  enum ProposalSource {
    brand
    influencer
  }
  
  type BudgetStats {
    totalBudgets: Int!
    totalValue: Float!
    averageValue: Float!
    byStatus: [BudgetStatusCount!]!
    byServiceType: [ServiceTypeCount!]!
    byBrand: [BrandBudgetStats!]!
    conversionRate: Float!
  }
  
  type BudgetStatusCount {
    status: BudgetStatus!
    count: Int!
  }
  
  type ServiceTypeCount {
    serviceType: ServiceType!
    count: Int!
  }
  
  type BrandBudgetStats {
    brandId: ID!
    brandName: String!
    count: Int!
    totalValue: Float!
  }
  
  # ===== ESTATÍSTICAS =====
  
  type Stats {
    totalInfluencers: Int!
    totalViews: Float!
    totalFollowers: Float!
    totalBrands: Int!
    totalCampaigns: Int!
  }
  
  # ===== FILTROS SALVOS =====
  
  type SavedFilter {
    id: ID!
    userId: ID!
    name: String!
    location: String
    minFollowers: Float!
    maxFollowers: Float!
    minRating: Float!
    verifiedOnly: Boolean!
    availableOnly: Boolean!
    platforms: FilterPlatforms
    createdAt: Date!
    updatedAt: Date!
  }
  
  type FilterPlatforms {
    instagram: Boolean
    youtube: Boolean
    tiktok: Boolean
  }
  
  # ===== TIPOS DE RESPOSTA =====
  
  type InfluencerConnection {
    nodes: [Influencer!]!
    totalCount: Int!
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
  }

  # ✨ NOVO TIPO: Resultado otimizado para busca por múltiplos IDs
  type InfluencersByIdsResult {
    # Influenciadores encontrados com sucesso
    influencers: [Influencer!]!
    # IDs que foram encontrados
    foundIds: [ID!]!
    # IDs que não foram encontrados ou não pertencem ao usuário
    notFoundIds: [ID!]!
    # IDs que foram rejeitados por validação
    invalidIds: [ID!]!
    # Contadores para métricas
    totalRequested: Int!
    totalFound: Int!
    totalNotFound: Int!
    totalInvalid: Int!
    # Tempo de processamento em milissegundos
    processingTimeMs: Int!
    # Se houve algum erro parcial (alguns IDs falharam)
    hasPartialFailure: Boolean!
    # Mensagens de erro específicas
    errors: [String!]!
  }

  # 🎯 RESULTADO PARA BUSCA DE INFLUENCERS DE PROPOSTA
  type ProposalInfluencersResult {
    influencerIds: [ID!]!
    totalCount: Int!
    success: Boolean!
    proposalId: ID!
    errors: [String!]!
  }
  
  type FinancialStats {
    totalWithPricing: Int!
    avgPrice: Float!
    minPrice: Float!
    maxPrice: Float!
  }
  
  type CacheStats {
    hits: Int!
    misses: Int!
    evictions: Int!
    size: Int!
    hitRate: Float!
  }
  
  type DashboardData {
    totalInfluencers: Int!
    totalWithFinancialData: Int!
    totalCampaigns: Int!
    totalBrands: Int!
    financialStats: FinancialStats!
    recentInfluencers: [Influencer!]!
    topPerformers: [Influencer!]!
  }
  
  # ===== INPUTS =====
  
  input InfluencerFilters {
    search: String
    category: String
    isAvailable: Boolean
    followersMin: Float
    followersMax: Float
    priceRange: String
  }
  
  input PaginationInput {
    limit: Int = 20
    offset: Int = 0
  }
  




  input CreateInfluencerInput {
    name: String
    email: String
    phone: String
    whatsapp: String
    country: String
    state: String
    city: String
    location: String
    age: Int
    gender: String
    bio: String
    avatar: String
    category: String
    categories: [String]
    totalFollowers: Float
    totalViews: Float
    engagementRate: Float
    isVerified: Boolean
    isAvailable: Boolean
    status: String
    
    # Redes sociais (campos diretos)
    instagramUsername: String
    instagramFollowers: Float
    instagramEngagementRate: Float
    instagramAvgViews: Float
    instagramStoriesViews: Float
    instagramReelsViews: Float
    
    tiktokUsername: String
    tiktokFollowers: Float
    tiktokEngagementRate: Float
    tiktokAvgViews: Float
    tiktokVideoViews: Float
    
    youtubeUsername: String
    youtubeFollowers: Float
    youtubeSubscribers: Float
    youtubeEngagementRate: Float
    youtubeAvgViews: Float
    youtubeShortsViews: Float
    youtubeLongFormViews: Float
    
    facebookUsername: String
    facebookFollowers: Float
    facebookEngagementRate: Float
    facebookAvgViews: Float
    facebookViews: Float
    facebookReelsViews: Float
    facebookStoriesViews: Float
    facebookPost: Float
    
    twitchUsername: String
    twitchFollowers: Float
    twitchEngagementRate: Float
    twitchViews: Float
    twitchStream: Float
    
    kwaiUsername: String
    kwaiFollowers: Float
    kwaiEngagementRate: Float
    kwaiViews: Float
    kwaiVideo: Float
    
    promotesTraders: Boolean
    responsibleName: String
    agencyName: String
    responsibleCapturer: String
    
    # Rede social principal
    mainNetwork: String
    mainPlatform: String
  }
  
  input UpdateInfluencerInput {
    name: String
    email: String
    phone: String
    whatsapp: String
    country: String
    state: String
    city: String
    location: String
    age: Int
    gender: String
    bio: String
    avatar: String
    category: String
    categories: [String]
    totalFollowers: Float
    totalViews: Float
    engagementRate: Float
    isVerified: Boolean
    isAvailable: Boolean
    status: String
    
    # Redes sociais (campos diretos)
    instagramUsername: String
    instagramFollowers: Float
    instagramEngagementRate: Float
    instagramAvgViews: Float
    instagramStoriesViews: Float
    instagramReelsViews: Float
    
    tiktokUsername: String
    tiktokFollowers: Float
    tiktokEngagementRate: Float
    tiktokAvgViews: Float
    tiktokVideoViews: Float
    
    youtubeUsername: String
    youtubeFollowers: Float
    youtubeSubscribers: Float
    youtubeEngagementRate: Float
    youtubeAvgViews: Float
    youtubeShortsViews: Float
    youtubeLongFormViews: Float
    
    facebookUsername: String
    facebookFollowers: Float
    facebookEngagementRate: Float
    facebookAvgViews: Float
    facebookViews: Float
    facebookReelsViews: Float
    facebookStoriesViews: Float
    facebookPost: Float
    
    twitchUsername: String
    twitchFollowers: Float
    twitchEngagementRate: Float
    twitchViews: Float
    twitchStream: Float
    
    kwaiUsername: String
    kwaiFollowers: Float
    kwaiEngagementRate: Float
    kwaiViews: Float
    kwaiVideo: Float
    
    promotesTraders: Boolean
    responsibleName: String
    agencyName: String
    responsibleCapturer: String
    
    # Rede social principal
    mainNetwork: String
    mainPlatform: String
  }
  
  # ===== INPUTS PARA MARCAS =====
  
  input CreateBrandInput {
    name: String!
    description: String
    logo: String
    website: String
    industry: String
    size: String
  }
  
  input UpdateBrandInput {
    name: String
    description: String
    logo: String
    website: String
    industry: String
    size: String
    status: String
  }
  
  # ===== INPUTS PARA ASSOCIAÇÕES MARCA-INFLUENCIADOR =====
  
  input CreateBrandInfluencerAssociationInput {
    userId: ID!
    brandId: ID!
    influencerId: ID!
    status: AssociationStatus = active
    tags: [String!] = []
    notes: String
  }
  
  input UpdateBrandInfluencerAssociationInput {
    status: AssociationStatus
    tags: [String!]
    notes: String
  }
  
  # ===== INPUTS PARA FILTROS =====
  
  input CreateSavedFilterInput {
    name: String!
    location: String
    minFollowers: Float!
    maxFollowers: Float!
    minRating: Float!
    verifiedOnly: Boolean!
    availableOnly: Boolean!
    platforms: FilterPlatformsInput
  }
  
  input UpdateSavedFilterInput {
    name: String
    location: String
    minFollowers: Float
    maxFollowers: Float
    minRating: Float
    verifiedOnly: Boolean
    availableOnly: Boolean
    platforms: FilterPlatformsInput
  }
  
  input FilterPlatformsInput {
    instagram: Boolean
    youtube: Boolean
    tiktok: Boolean
  }
  
  # ===== INPUTS PARA ORÇAMENTOS =====
  
  input CreateBudgetInput {
    influencerId: ID!
    influencerName: String!
    userId: ID!
    brandId: ID!
    
    amount: Float!
    currency: String = "BRL"
    description: String
    serviceType: ServiceType!
    services: ServiceBudgetInput
    
    expiresAt: Date
  }
  
  input UpdateBudgetInput {
    amount: Float
    currency: String
    description: String
    serviceType: ServiceType
    services: ServiceBudgetInput
    status: BudgetStatus
    expiresAt: Date
  }
  
  input ServiceBudgetInput {
    instagram: InstagramServicesInput
    youtube: YouTubeServicesInput
    tiktok: TikTokServicesInput
    facebook: FacebookServicesInput
    twitch: TwitchServicesInput
    kwai: KwaiServicesInput
  }
  
  input InstagramServicesInput {
    story: PlatformPriceInput
    reel: PlatformPriceInput
    post: PlatformPriceInput
  }
  
  input YouTubeServicesInput {
    shorts: PlatformPriceInput
    insertion: PlatformPriceInput
    dedicated: PlatformPriceInput
  }
  
  input TikTokServicesInput {
    video: PlatformPriceInput
  }
  
  input FacebookServicesInput {
    post: PlatformPriceInput
    story: PlatformPriceInput
  }
  
  input TwitchServicesInput {
    stream: PlatformPriceInput
  }
  
  input KwaiServicesInput {
    video: PlatformPriceInput
  }
  
  # ===== INPUTS PARA CONTRAPROPOSTAS =====
  
  input CreateCounterProposalInput {
    budgetId: ID!
    proposedAmount: Float!
    proposedServices: ServiceBudgetInput
    proposalNotes: String
    proposedBy: ProposalSource!
  }
  
  input UpdateCounterProposalInput {
    proposedAmount: Float
    proposedServices: ServiceBudgetInput
    proposalNotes: String
    status: CounterProposalStatus
  }
  
  # ===== INPUTS PARA PRICING =====
  
  input PlatformPriceInput {
    price: Float!
    currency: String = "BRL"
  }
  
  input InstagramPricingInput {
    story: PlatformPriceInput
    reel: PlatformPriceInput
    post: PlatformPriceInput
  }
  
  input TikTokPricingInput {
    video: PlatformPriceInput
  }
  
  input YouTubePricingInput {
    shorts: PlatformPriceInput
    insertion: PlatformPriceInput
    dedicated: PlatformPriceInput
  }
  
  input FacebookPricingInput {
    post: PlatformPriceInput
    story: PlatformPriceInput
  }
  
  input TwitchPricingInput {
    stream: PlatformPriceInput
  }
  
  input KwaiPricingInput {
    video: PlatformPriceInput
  }
  
  input PricingServicesInput {
    instagram: InstagramPricingInput
    tiktok: TikTokPricingInput
    youtube: YouTubePricingInput
    facebook: FacebookPricingInput
    twitch: TwitchPricingInput
    kwai: KwaiPricingInput
  }
  
  input CreateInfluencerPricingInput {
    influencerId: ID!
    services: PricingServicesInput!
    validFrom: Date
    validUntil: Date
    notes: String
    clientSpecific: String
  }
  
  input UpdateInfluencerPricingInput {
    services: PricingServicesInput
    isActive: Boolean
    validFrom: Date
    validUntil: Date
    notes: String
    clientSpecific: String
  }
  
  # ===== INPUTS PARA DEMOGRAPHICS =====
  
  input AudienceGenderInput {
    male: Float!
    female: Float!
    other: Float!
  }
  
  input AudienceAgeRangeInput {
    range: String!
    percentage: Float!
  }
  
  input AudienceLocationInput {
    country: String!
    percentage: Float!
  }
  
  input AudienceCityInput {
    city: String!
    percentage: Float!
  }
  
  input CreateAudienceDemographicInput {
    influencerId: ID!
    platform: String!
    audienceGender: AudienceGenderInput!
    audienceLocations: [AudienceLocationInput!]!
    audienceCities: [AudienceCityInput!]!
    audienceAgeRange: [AudienceAgeRangeInput!]!
    captureDate: Date
    source: String
  }
  
  input UpdateAudienceDemographicInput {
    audienceGender: AudienceGenderInput
    audienceLocations: [AudienceLocationInput!]
    audienceCities: [AudienceCityInput!]
    audienceAgeRange: [AudienceAgeRangeInput!]
    isActive: Boolean
    captureDate: Date
    source: String
  }

  # ===== INPUTS PARA SCREENSHOTS =====
  
  input CreateScreenshotInput {
    influencerId: ID!
    platform: String!
    filename: String!
    size: Int
    contentType: String
    fileData: String # Base64 encoded file data (opcional se url for fornecida)
    url: String # URL já processada (opcional se fileData for fornecido)
  }

  # Input para upload em lote de screenshots - OTIMIZADO
  input UploadScreenshotsBatchInput {
    influencerId: ID!
    screenshots: [ScreenshotFileInput!]!
  }

  input ScreenshotFileInput {
    platform: String!
    filename: String!
    fileData: String!
    contentType: String!
    size: Int!
  }

  # Response para upload em lote
  type UploadScreenshotsBatchResponse {
    success: Boolean!
    totalUploaded: Int!
    results: [Screenshot!]!
    errors: [ScreenshotUploadError!]!
  }

  type ScreenshotUploadError {
    platform: String!
    filename: String!
    error: String!
  }

  # ===== INPUTS PARA AVATAR =====
  
  input UploadAvatarInput {
    userId: ID!
    filename: String!
    size: Int
    contentType: String
    fileData: String! # Base64 encoded file data
  }
  
  # ===== INPUTS PARA BRANDS =====
  
  input BrandServicePriceInput {
    name: String
    price: Float
  }
  
  input BrandPricesInput {
    instagramStory: BrandServicePriceInput
    instagramReel: BrandServicePriceInput
    tiktokVideo: BrandServicePriceInput
    youtubeInsertion: BrandServicePriceInput
    youtubeDedicated: BrandServicePriceInput
    youtubeShorts: BrandServicePriceInput
  }
  
  input CreateBrandInput {
    name: String!
    prices: BrandPricesInput
    influencerId: ID
    userId: ID!
  }
  
  input UpdateBrandInput {
    name: String
    prices: BrandPricesInput
  }
  
  # ===== INPUTS PARA CATEGORIES =====
  
  input CreateCategoryInput {
    name: String!
    userId: ID!
    isActive: Boolean = true
  }
  
  input UpdateCategoryInput {
    name: String
    isActive: Boolean
  }
  
  # ===== INPUTS PARA NOTES =====
  
  input CreateNoteInput {
    title: String!
    content: String!
    type: String!
    influencerId: ID!
    userId: ID!
  }
  
  input UpdateNoteInput {
    title: String
    content: String
    type: String
  }
  
  # ===== QUERIES =====
  
  type Query {
    # Influenciadores
    influencer(id: ID!): Influencer
    influencers(
      filters: InfluencerFilters
      pagination: PaginationInput
      userId: ID!
    ): InfluencerConnection!
    
    # ✨ NOVA QUERY OTIMIZADA: Buscar múltiplos influenciadores por IDs
    influencersByIds(
      ids: [ID!]!
      userId: ID!
      proposalId: ID  # Opcional: permite acesso via proposta para membros convidados
    ): InfluencersByIdsResult!

    # 🎯 QUERY PARA BUSCAR IDS DE INFLUENCERS DE UMA PROPOSTA
    proposalInfluencers(
      proposalId: ID!
      userId: ID!
    ): ProposalInfluencersResult!
    
    # Busca otimizada por preço
    influencersByPriceRange(
      priceRange: String!
      userId: ID!
      limit: Int = 20
    ): [Influencer!]!
    
    # Dados financeiros
    influencerFinancial(influencerId: ID!): InfluencerFinancial
    
    # Categorias
    categories(userId: ID): [Category!]!
    userCategories(userId: ID!): [Category!]!
    category(id: ID!, userId: ID!): Category
    
    # Marcas
    brands(userId: ID!, influencerId: ID): [Brand!]!
    brand(id: ID!, userId: ID!): Brand
    
    # Associações Marca-Influenciador
    brandInfluencerAssociations(
      userId: ID!
      influencerId: ID
      brandId: ID
      status: AssociationStatus
    ): [BrandInfluencerAssociation!]!
    brandInfluencerAssociation(id: ID!): BrandInfluencerAssociation
    
    # Orçamentos
    budgets(
      userId: ID!
      influencerId: ID
      brandId: ID
      status: BudgetStatus
      serviceType: ServiceType
    ): [Budget!]!
    budget(id: ID!): Budget
    budgetStats(userId: ID!, brandId: ID): BudgetStats!
    
    # 🆕 ORÇAMENTOS DE PROPOSTAS COM CONTROLE DE ACESSO
    proposalBudgets(
      proposalId: ID!
      userId: ID!
    ): ProposalBudgetsResult!
    
    # 🆕 ORÇAMENTOS DE UM INFLUENCIADOR ESPECÍFICO EM UMA PROPOSTA
    influencerBudgetsInProposal(
      proposalId: ID!
      influencerId: ID!
      userId: ID!
    ): ProposalBudgetsResult!
    
    # 🆕 BUSCAR ORÇAMENTOS POR IDs COM VERIFICAÇÃO DE ACESSO
    budgetsByIds(
      budgetIds: [ID!]!
      userId: ID!
      proposalId: ID
    ): [Budget!]!
    
    # 🆕 CONTRAPROPOSTAS DE COLABORADORES
    collaboratorCounterProposals(
      proposalId: ID!
      userId: ID!
      status: CollaboratorCounterProposalStatus
    ): [CollaboratorCounterProposal!]!
    
    # 🆕 ORÇAMENTOS INDEPENDENTES (NÃO VINCULADOS A PROPOSTAS)
    independentBudgets(
      userId: ID!
      brandId: ID
      status: BudgetStatus
      serviceType: ServiceType
    ): [Budget!]!
    
    # 🆕 STATUS DOS INFLUENCERS NA PROPOSTA
    proposalInfluencersStatus(
      proposalId: ID!
      userId: ID!
    ): [ProposalInfluencerStatus!]!
    
    # Estatísticas
    stats(userId: ID!): Stats!
    financialStats(userId: ID!): FinancialStats!
    cacheStats: CacheStats!
    
    # Filtros salvos
    savedFilters(userId: ID!): [SavedFilter!]!
    savedFilter(id: ID!): SavedFilter
    
    # Pricing
    influencerPricing(influencerId: ID!): InfluencerPricing
    influencerPricingHistory(influencerId: ID!): [InfluencerPricing!]!
    
    # Demographics
    influencerDemographics(influencerId: ID!): [AudienceDemographic!]!
    influencerConsolidatedDemographics(influencerId: ID!): ConsolidatedDemographics
    
    # Screenshots
    influencerScreenshots(influencerId: ID!, platform: String): [Screenshot!]!
    
    # Dashboard
    dashboardData(userId: ID!): DashboardData!
    
    # Notas
    notes(influencerId: ID!, userId: ID!): [Note!]!
    allNotes(userId: ID!): [Note!]!
    note(id: ID!, userId: ID!): Note
  }
  
  # ===== MUTATIONS =====
  
  type Mutation {
    # Influenciadores
    createInfluencer(input: CreateInfluencerInput!): Influencer!
    updateInfluencer(id: ID!, input: UpdateInfluencerInput!): Influencer!
    deleteInfluencer(id: ID!): Boolean!
    
    # Marcas
    createBrand(input: CreateBrandInput!): Brand!
    updateBrand(id: ID!, input: UpdateBrandInput!): Brand!
    deleteBrand(id: ID!): Boolean!
    
    # Categorias
    createCategory(input: CreateCategoryInput!): Category!
    updateCategory(id: ID!, input: UpdateCategoryInput!): Category!
    deleteCategory(id: ID!, userId: ID!): Boolean!
    
    # Notas
    createNote(input: CreateNoteInput!): Note!
    updateNote(id: ID!, input: UpdateNoteInput!): Note!
    deleteNote(id: ID!, userId: ID!): Boolean!
    
    # Associações Marca-Influenciador
    createBrandInfluencerAssociation(input: CreateBrandInfluencerAssociationInput!): BrandInfluencerAssociation!
    updateBrandInfluencerAssociation(id: ID!, input: UpdateBrandInfluencerAssociationInput!): BrandInfluencerAssociation!
    deleteBrandInfluencerAssociation(id: ID!): Boolean!
    
    # Orçamentos
    createBudget(input: CreateBudgetInput!): Budget!
    updateBudget(id: ID!, input: UpdateBudgetInput!): Budget!
    deleteBudget(id: ID!): Boolean!
    
    # Contrapropostas
    createCounterProposal(input: CreateCounterProposalInput!): CounterProposal!
    updateCounterProposal(id: ID!, input: UpdateCounterProposalInput!): CounterProposal!
    deleteCounterProposal(id: ID!): Boolean!
    
    # 🆕 CONTRAPROPOSTAS DE COLABORADORES
    createCollaboratorCounterProposal(input: CreateCollaboratorCounterProposalInput!): CollaboratorCounterProposal!
    updateCollaboratorCounterProposal(id: ID!, input: UpdateCollaboratorCounterProposalInput!): CollaboratorCounterProposal!
    reviewCollaboratorCounterProposal(
      id: ID!
      action: CollaboratorCounterProposalAction!
      reviewNote: String
      reviewedBy: ID!
    ): CollaboratorCounterProposal!
    deleteCollaboratorCounterProposal(id: ID!): Boolean!
    
    # Filtros salvos
    createSavedFilter(input: CreateSavedFilterInput!): SavedFilter!
    updateSavedFilter(id: ID!, input: UpdateSavedFilterInput!): SavedFilter!
    deleteSavedFilter(id: ID!): Boolean!
    
    # Pricing
    createInfluencerPricing(input: CreateInfluencerPricingInput!): InfluencerPricing!
    updateInfluencerPricing(id: ID!, input: UpdateInfluencerPricingInput!): InfluencerPricing!
    deleteInfluencerPricing(id: ID!): Boolean!
    
    # Demographics
    createAudienceDemographic(input: CreateAudienceDemographicInput!): AudienceDemographic!
    updateAudienceDemographic(id: ID!, input: UpdateAudienceDemographicInput!): AudienceDemographic!
    deleteAudienceDemographic(id: ID!): Boolean!
    
    # Screenshots
    uploadScreenshot(input: CreateScreenshotInput!): Screenshot!
    deleteScreenshot(id: ID!, influencerId: ID!, platform: String!): Boolean!
    
    # Avatar
    uploadAvatar(input: UploadAvatarInput!): AvatarUploadResult!
    
    # Upload em lote de screenshots - OTIMIZADO
    uploadScreenshotsBatch(input: UploadScreenshotsBatchInput!): UploadScreenshotsBatchResponse!
    
    # Cache e sync
    clearCache: Boolean!
    syncInfluencerFinancialData(influencerId: ID!): Boolean!
  }

  # ===== BRANDS =====
  
  type BrandServicePrice {
    name: String
    price: Float
  }
  
  type BrandPrices {
    instagramStory: BrandServicePrice
    instagramReel: BrandServicePrice
    tiktokVideo: BrandServicePrice
    youtubeInsertion: BrandServicePrice
    youtubeDedicated: BrandServicePrice
    youtubeShorts: BrandServicePrice
  }
  
  type Brand {
    id: ID!
    name: String!
    prices: BrandPrices
    influencerId: ID
    userId: ID!
    createdAt: Date!
    updatedAt: Date!
  }
  
  # ===== CATEGORIES =====
  
  type Category {
    id: ID!
    name: String!
    slug: String
    description: String
    count: Int
    userId: ID
    isActive: Boolean!
    createdAt: Date!
    updatedAt: Date!
  }
  
  # ===== NOTES =====
  
  type Note {
    id: ID!
    title: String!
    content: String!
    type: String!
    influencerId: ID!
    userId: ID!
    createdAt: Date!
    updatedAt: Date!
  }

  # ===== ORÇAMENTOS DO INFLUENCIADOR POR PLATAFORMA =====
  
  type InfluencerBudgets {
    instagram: [Budget!]!
    tiktok: [Budget!]!
    youtube: [Budget!]!
    facebook: [Budget!]!
    twitch: [Budget!]!
    kwai: [Budget!]!
    personalizado: [Budget!]!
  }

  # 🆕 INPUTS PARA CONTRAPROPOSTAS DE COLABORADORES

  input CreateCollaboratorCounterProposalInput {
    budgetId: ID!
    proposalId: ID!
    influencerId: ID!
    influencerName: String!
    
    originalAmount: Float!
    proposedAmount: Float!
    currency: String!
    notes: String
    serviceType: String!
    
    # Dados do colaborador (serão preenchidos automaticamente via context)
    proposedBy: CollaboratorInfoInput!
  }

  input UpdateCollaboratorCounterProposalInput {
    proposedAmount: Float
    notes: String
    currency: String
  }

  input CollaboratorInfoInput {
    userId: ID!
    userName: String!
    userEmail: String!
    collaboratorRole: CollaboratorRole!
  }

  enum CollaboratorCounterProposalAction {
    accept
    reject
  }

  # 🆕 INPUTS PARA ORÇAMENTOS COM PROPOSTAS

  input CreateBudgetWithProposalInput {
    influencerId: ID!
    influencerName: String!
    userId: ID!
    brandId: ID!
    brandName: String
    proposalId: ID!
    
    amount: Float!
    currency: String = "BRL"
    description: String
    serviceType: ServiceType!
    services: ServiceBudgetInput
    expiresAt: Date
  }

  input ServiceBudgetInput {
    instagram: InstagramServicesInput
    youtube: YouTubeServicesInput
    tiktok: TikTokServicesInput
    facebook: FacebookServicesInput
    twitch: TwitchServicesInput
    kwai: KwaiServicesInput
  }

  input InstagramServicesInput {
    story: PlatformPriceInput
    reel: PlatformPriceInput
    post: PlatformPriceInput
  }

  input YouTubeServicesInput {
    shorts: PlatformPriceInput
    insertion: PlatformPriceInput
    dedicated: PlatformPriceInput
  }

  input TikTokServicesInput {
    video: PlatformPriceInput
  }

  input FacebookServicesInput {
    post: PlatformPriceInput
    story: PlatformPriceInput
  }

  input TwitchServicesInput {
    stream: PlatformPriceInput
  }

  input KwaiServicesInput {
    video: PlatformPriceInput
  }

  input PlatformPriceInput {
    price: Float!
    currency: String!
    quantity: Int
    deliveryTime: Int
    notes: String
  }
`; 

