'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from '@/hooks/use-translations';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Building, 
  DollarSign, 
  Clock, 
  Target, 
  Calendar,
  TrendingUp,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  PlayCircle,
  Eye,
  ExternalLink,
  User,
  MessageSquare,
  Paperclip,
  Clock3,
  RefreshCw
} from 'lucide-react';
import { Campaign, CampaignInfluencer } from '@/types/campaign';
import { CampaignService } from '@/services/campaign-service';
import { toast } from 'sonner';

interface InfluencerCampaignsPanelProps {
  influencerId: string | null;
  influencerName?: string;
}

interface CampaignWithInfluencerData extends Campaign {
  influencerData?: CampaignInfluencer;
}

export function InfluencerCampaignsPanel({ 
  influencerId, 
  influencerName 
}: InfluencerCampaignsPanelProps) {
  const [campaigns, setCampaigns] = useState<CampaignWithInfluencerData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslations();

  useEffect(() => {
    if (influencerId) {
      fetchInfluencerCampaigns();
    } else {
      setCampaigns([]);
      setError(null);
    }
  }, [influencerId]);

  const fetchInfluencerCampaigns = async () => {
    if (!influencerId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const campaignsList = await CampaignService.getCampaignsByInfluencer(influencerId);
      
      // Enriquecer campanhas com dados específicos do influencer
      const enrichedCampaigns = campaignsList.map(campaign => {
        const influencerData = campaign.influencers?.find(
          inf => inf.influencerId === influencerId || inf.id === influencerId
        );
        
        return {
          ...campaign,
          influencerData
        };
      });
      
      setCampaigns(enrichedCampaigns);
    } catch (err) {
      console.error('Erro ao carregar campanhas do influencer:', err);
      setError('Falha ao carregar campanhas');
      toast.error('Erro ao carregar campanhas do influencer');
    } finally {
      setLoading(false);
    }
  };

  const formatCampaignStatus = (status: string) => {
    const statusMap = {
      'draft': 'Rascunho',
      'active': 'Ativa',
      'completed': 'Concluída',
      'cancelled': 'Cancelada',
      'em-analise': 'Em analise'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      'draft': 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800',
      'active': 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800',
      'completed': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800',
      'cancelled': 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800',
      'em-analise': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800'
    };
    return colorMap[status as keyof typeof colorMap] || 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800';
  };

  const formatInfluencerStatus = (status: string) => {
    const statusMap = {
      'invited': 'Convidado',
      'confirmed': 'Confirmado',
      'declined': 'Recusado',
      'completed': 'Concluído'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getInfluencerStatusIcon = (status: string) => {
    switch (status) {
      case 'invited':
        return <AlertCircle className="h-3 w-3 text-gray-600 dark:text-gray-400" />;
      case 'confirmed':
        return <CheckCircle className="h-3 w-3 text-gray-600 dark:text-gray-400" />;
      case 'declined':
        return <XCircle className="h-3 w-3 text-gray-600 dark:text-gray-400" />;
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-gray-600 dark:text-gray-400" />;
      default:
        return <PlayCircle className="h-3 w-3 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getInfluencerStatusColor = (status: string) => {
    const colorMap = {
      'invited': 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800',
      'confirmed': 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800',
      'declined': 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800',
      'completed': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800'
    };
    return colorMap[status as keyof typeof colorMap] || 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800';
  };

  // Funções auxiliares do kanban board
  const getCardBackgroundColor = (status: string) => {
    const colorMap = {
      'em-analise': 'bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50',
      'aprovado': 'bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50',
      'reprovado': 'bg-gradient-to-br from-red-50 to-red-100/50 border-red-200/50',
      'proposta-recusada': 'bg-gradient-to-br from-orange-50 to-orange-100/50 border-orange-200/50',
      'observacoes': 'bg-gradient-to-br from-yellow-50 to-yellow-100/50 border-yellow-200/50',
      'active': 'bg-gradient-to-br from-green-50 to-green-100/50 border-green-200/50',
      'completed': 'bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200/50',
      'paused': 'bg-gradient-to-br from-yellow-50 to-yellow-100/50 border-yellow-200/50',
      'cancelled': 'bg-gradient-to-br from-red-50 to-red-100/50 border-red-200/50'
    };
    return colorMap[status as keyof typeof colorMap] || 'bg-gradient-to-br from-gray-50 to-gray-100/50 border-gray-200/50';
  };

  const getPriorityBadge = (priority: string) => {
    const priorityMap = {
      'alta': 'bg-red-100 text-red-700 border-red-300',
      'media': 'bg-yellow-100 text-yellow-700 border-yellow-300',
      'baixa': 'bg-green-100 text-green-700 border-green-300'
    };
    return priorityMap[priority as keyof typeof priorityMap] || 'bg-gray-100 text-gray-700 border-gray-300';
  };

  const getBudgetIndicatorColor = (budget: number) => {
    if (budget >= 50000) return 'bg-green-500';
    if (budget >= 20000) return 'bg-yellow-500';
    if (budget >= 5000) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (!influencerId) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-muted-foreground">{t('dashboard.campaigns')}</h3>
        </div>
        
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg bg-muted/20">
          <Target className="h-8 w-8 text-muted-foreground/50 mb-3" />
          <p className="text-sm text-muted-foreground text-center leading-relaxed">
            {t('dashboard.select_influencer_campaigns')}
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-muted-foreground">Campanhas</h3>
          <Skeleton className="h-4 w-16" />
        </div>
        
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-3 w-28" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-muted-foreground">Campanhas</h3>
        </div>
        
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg bg-red-50/50 dark:bg-red-950/20">
          <XCircle className="h-8 w-8 text-red-500 mb-3" />
          <p className="text-sm text-red-600 dark:text-red-400 text-center mb-3">
            {error}
          </p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchInfluencerCampaigns}
            className="text-xs"
          >
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-muted-foreground">Campanhas</h3>
        <Badge variant="secondary" className="text-xs font-medium">
          {campaigns.length}
        </Badge>
      </div>
      
      {campaigns.length > 0 ? (
        <TooltipProvider>
          <ScrollArea className="h-[400px] pr-2">
            <div className="space-y-2">
              {campaigns.map((campaign) => (
                <Tooltip key={campaign.id}>
                  <TooltipTrigger asChild>
                    <div className="group rounded-xl p-3 cursor-pointer hover:shadow-lg transition-all duration-200 bg-white border border-gray-100 hover:border-gray-200 hover:bg-gray-50/50">
                      {/* Header minimalista */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getBudgetIndicatorColor(campaign.budget || 0)}`}></div>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs px-2 py-0.5 rounded-full font-medium ${getStatusColor(campaign.status)}`}
                          >
                            {formatCampaignStatus(campaign.status)}
                          </Badge>
                        </div>
                        <span className="text-xs font-medium text-gray-500 break-words">
                          {formatCurrency(campaign.budget || 0)}
                        </span>
                      </div>

                      {/* Título da campanha */}
                      <h4 className="font-medium text-gray-900 text-sm leading-tight mb-1 truncate">
                        {campaign.name}
                      </h4>

                      {/* Informações básicas */}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="truncate">
                          {campaign.brandName || 'Marca não informada'}
                        </span>
                        {campaign.influencerData && (
                          <Badge 
                            variant="outline" 
                            className={`text-xs px-1.5 py-0.5 ${getInfluencerStatusColor(campaign.influencerData.status)}`}
                          >
                            {formatInfluencerStatus(campaign.influencerData.status)}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-sm p-4">
                    <div className="space-y-3">
                      {/* Cabeçalho do tooltip */}
                      <div>
                        <h4 className="font-semibold text-sm text-gray-900 mb-1">
                          {campaign.name}
                        </h4>
                        <p className="text-xs text-gray-600">
                          {campaign.clientName && `Cliente: ${campaign.clientName}`}
                        </p>
                      </div>

                      {/* Informações da campanha */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-xs">
                          <DollarSign className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                          <span>Orçamento: <strong>{formatCurrency(campaign.budget || 0)}</strong></span>
                        </div>
                        <div className="flex items-center gap-2 text-xs">
                          <Calendar className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                          <span>Período: {formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs">
                          <Target className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                          <span>Status: <strong>{formatCampaignStatus(campaign.status)}</strong></span>
                        </div>
                      </div>

                      {/* Dados específicos do influenciador */}
                      {campaign.influencerData && (
                        <>
                          <Separator />
                          <div className="space-y-2">
                            <h5 className="font-medium text-xs text-gray-700">Sua Participação</h5>
                            
                            <div className="flex items-center gap-2 text-xs">
                              {getInfluencerStatusIcon(campaign.influencerData.status)}
                              <span>Status: <strong>{formatInfluencerStatus(campaign.influencerData.status)}</strong></span>
                            </div>
                            
                            {campaign.influencerData.negotiatedPrice > 0 && (
                              <div className="flex flex-wrap items-center gap-2 text-xs">
                                <DollarSign className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                                <span className="break-words">Valor Negociado: <strong className="text-gray-900 dark:text-gray-100">{formatCurrency(campaign.influencerData.negotiatedPrice)}</strong></span>
                              </div>
                            )}
                            
                            {campaign.influencerData.deliverables && campaign.influencerData.deliverables.length > 0 && (
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 text-xs">
                                  <CheckCircle className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                                  <span>Entregáveis ({campaign.influencerData.deliverables.length}):</span>
                                </div>
                                <div className="flex flex-wrap gap-1 ml-5">
                                  {campaign.influencerData.deliverables.map((deliverable, index) => (
                                    <Badge 
                                      key={index} 
                                      variant="secondary" 
                                      className="text-xs px-2 py-0.5 bg-blue-50 text-blue-700 border-0 rounded-full"
                                    >
                                      {deliverable}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {campaign.influencerData.notes && (
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 text-xs">
                                  <AlertCircle className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                                  <span>Observações:</span>
                                </div>
                                <p className="text-xs text-gray-600 bg-gray-50 p-2 rounded ml-5">
                                  {campaign.influencerData.notes}
                                </p>
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </ScrollArea>
        </TooltipProvider>
      ) : (
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg bg-muted/20">
          <Target className="h-8 w-8 text-muted-foreground/50 mb-3" />
          <p className="text-sm text-muted-foreground text-center leading-relaxed">
            {influencerName ? 
              t('influencers.no_active_campaigns', { name: influencerName }) :
              t('influencers.no_active_campaigns_generic')
            }
          </p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-3 text-xs"
            onClick={fetchInfluencerCampaigns}
          >
            <TrendingUp className="h-3 w-3 mr-1" />
            Atualizar
          </Button>
        </div>
      )}
    </div>
  );
}

export default InfluencerCampaignsPanel;

