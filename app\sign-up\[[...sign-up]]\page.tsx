'use client';

import { SignUp } from '@clerk/nextjs';
import { BackdropEffect } from '@/components/ui/backdrop-effect';
import { Suspense } from 'react';
import { ClerkInviteHandler } from '@/components/auth/clerk-invite-handler';
import { useParams } from 'next/navigation';

// Traduções específicas para a página de registro
const signUpLocalization = {
  signUp: {
    start: {
      title: 'Criar sua conta',
      subtitle: 'Junte-se à nossa plataforma e comece sua jornada',
      actionText: 'Já tem uma conta?',
      actionLink: 'Entrar',
    },
    emailLink: {
      title: 'Verificar seu email',
      subtitle: 'Clique no link enviado para {{identifier}} para continuar',
      formTitle: 'Link de verificação',
      formSubtitle: 'Use o link de verificação enviado para seu endereço de email',
      resendButton: 'Não recebeu o link? Reenviar',
      verified: {
        title: 'Conta criada com sucesso!',
        subtitle: 'Você será redirecionado em breve',
      },
      loading: {
        title: 'Criando sua conta...',
        subtitle: 'Isso pode levar alguns segundos',
      },
      verifiedSwitchTab: {
        title: 'Email verificado com sucesso',
        subtitle: 'Retorne à aba anterior para continuar',
        subtitleNewTab: 'Retorne à aba anterior para continuar',
      },
    },
    emailCode: {
      title: 'Verificar seu email',
      subtitle: 'Digite o código enviado para {{identifier}}',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de 6 dígitos enviado para seu email',
      resendButton: 'Não recebeu o código? Reenviar',
    },
    phoneCode: {
      title: 'Verificar seu telefone',
      subtitle: 'Digite o código enviado para {{identifier}}',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de 6 dígitos enviado para seu telefone',
      resendButton: 'Não recebeu o código? Reenviar',
    },
    continue: {
      title: 'Complete seu perfil',
      subtitle: 'Preencha os campos obrigatórios para finalizar seu cadastro',
      actionText: 'Já tem uma conta?',
      actionLink: 'Entrar',
    },
  },
  userButton: {
    action__manageAccount: 'Gerenciar conta',
    action__signOut: 'Sair',
    action__signOutAll: 'Sair de todas as contas',
    action__addAccount: 'Adicionar conta',
  },
  // Labels e placeholders de formulário
  formFieldLabel__emailAddress: 'Email',
  formFieldLabel__phoneNumber: 'Telefone',
  formFieldLabel__username: 'Nome de usuário',
  formFieldLabel__password: 'Senha',
  formFieldLabel__confirmPassword: 'Confirmar senha',
  formFieldLabel__firstName: 'Nome',
  formFieldLabel__lastName: 'Sobrenome',
  formFieldLabel__organizationName: 'Nome da organização',
  
  formFieldInputPlaceholder__emailAddress: 'Digite seu email',
  formFieldInputPlaceholder__phoneNumber: 'Digite seu telefone',
  formFieldInputPlaceholder__username: 'Digite seu nome de usuário',
  formFieldInputPlaceholder__password: 'Crie uma senha segura',
  formFieldInputPlaceholder__confirmPassword: 'Confirme sua senha',
  formFieldInputPlaceholder__firstName: 'Digite seu nome',
  formFieldInputPlaceholder__lastName: 'Digite seu sobrenome',
  formFieldInputPlaceholder__organizationName: 'Nome da sua organização',
  
  // Botões
  formButtonPrimary: 'Criar conta',
  formButtonPrimary__continue: 'Continuar',
  formButtonPrimary__verify: 'Verificar',
  formButtonPrimary__finish: 'Finalizar cadastro',
  formButtonPrimary__signUp: 'Criar conta',
  
  // Outros textos
  dividerText: 'ou',
  footerActionLink__useAnotherMethod: 'Usar outro método',
  footerActionLink__signIn: 'Já tem uma conta? Entrar',
  
  // Mensagens de validação e erro
  formFieldError__notMatchingPasswords: 'As senhas não coincidem',
  formFieldError__matchingPasswords: 'As senhas coincidem',
  formFieldHintText__optional: 'Opcional',
  formFieldHintText__slug: 'Um identificador único para sua organização',
  
  // Password requirements
  unstable__formFieldHintText__password: 'Sua senha deve ter pelo menos 8 caracteres',
  formFieldSuccessText__password: 'Sua senha atende a todos os requisitos necessários.',
  formFieldHintText__password: 'Sua senha deve ter pelo menos 8 caracteres e incluir letras e números.',
  formFieldValidationText__password_meets_requirements: 'Sua senha atende a todos os requisitos necessários.',
  
  // Loading
  loading: 'Carregando...',
  
  // Badges
  badge__primary: 'Principal',
  badge__thisDevice: 'Este dispositivo',
  badge__unverified: 'Não verificado',
  badge__you: 'Você',
  
  // Captcha
  captcha__title: 'Verificação de segurança',
  captcha__subtitle: 'Complete a verificação para continuar',
  
  // Terms and conditions
  formFieldTermsOfService: 'Ao criar uma conta, você concorda com nossos {{termsOfServiceLink}} e {{privacyPolicyLink}}',
  formFieldTermsOfServiceLink: 'Termos de Serviço',
  formFieldPrivacyPolicyLink: 'Política de Privacidade',
  
  // Messages
  formFieldSuccessText__phoneNumber: 'Número de telefone válido',
  formFieldSuccessText__emailAddress: 'Email válido',
  
  // Mensagens de erro comuns
  unstable__errors: {
    identification_deletion_failed: 'Não é possível excluir sua última identificação.',
    phone_number_exists: 'Este número de telefone já está sendo usado por outra conta.',
    email_address_exists: 'Este endereço de email já está sendo usado por outra conta.',
    captcha_invalid: 'Verificação de segurança inválida. Tente novamente.',
    form_param_format_invalid__phone_number: 'O número de telefone deve estar em um formato internacional válido.',
    form_param_format_invalid__email_address: 'Digite um endereço de email válido.',
    form_password_pwned: 'Esta senha foi encontrada em uma violação de dados e não pode ser usada.',
    form_username_invalid_length: 'O nome de usuário deve ter entre {{min_length}} e {{max_length}} caracteres.',
    form_password_length_too_short: 'A senha deve ter pelo menos {{min_length}} caracteres.',
    password_not_strong_enough: 'A senha não é forte o suficiente.',
    form_password_validation_failed: 'Senha inválida.',
    form_identifier_exists: 'Esta identificação já está sendo usada.',
    not_allowed_access: 'Acesso negado.',
    too_many_requests: 'Muitas tentativas. Tente novamente mais tarde.',
    password_requirements: 'A senha deve atender aos seguintes requisitos:',
    signup_not_allowed: 'Cadastro não permitido neste momento.',
    verification_expired: 'O código de verificação expirou. Solicite um novo código.',
    verification_failed: 'Falha na verificação. Tente novamente.',
    form_param_nil: 'Este campo é obrigatório.',
    form_param_max_length_exceeded: 'Texto muito longo.',
    username_exists: 'Este nome de usuário já está sendo usado.',
    form_username_invalid_character: 'Nome de usuário contém caracteres inválidos.',
  },

  // Mensagens específicas de cadastro desabilitado e interface
  'Sign ups are currently disabled. If you believe you should have access, please contact support.': 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
  'Access restricted': 'Acesso restrito',
  'Email support': 'Suporte por email',
  'Already have an account?': 'Já tem uma conta?',
  'Sign in': 'Entrar',
  'Secured by': 'Protegido por',
  'Development mode': 'Modo de desenvolvimento',

  // Traduções adicionais para elementos do Clerk
  signUpUnavailable: {
    title: 'Acesso restrito',
    subtitle: 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
    primaryButton: 'Suporte por email',
    footerText: 'Já tem uma conta?',
    footerLink: 'Entrar',
  },

  // Variação sem título (apenas mensagem)
  signUpDisabledSimple: {
    message: 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
    primaryButton: 'Suporte por email',
    footerText: 'Já tem uma conta?',
    footerLink: 'Entrar',
  },

  // Footer e branding
  footer: {
    securedBy: 'Protegido por',
    developmentMode: 'Modo de desenvolvimento',
    clerk: 'clerk',
  },
  
  // Organization creation (se aplicável)
  createOrganization: {
    title: 'Criar organização',
    subtitle: 'Crie uma organização para colaborar com sua equipe',
    formButtonPrimary: 'Criar organização',
  },
  
  // Organization invites
  organizationProfile: {
    navbar: {
      title: 'Organização',
      description: 'Gerencie sua organização.',
      general: 'Geral',
      members: 'Membros',
    },
  },

  // Modal de acesso restrito / cadastro desabilitado
  signUpDisabled: {
    title: 'Acesso restrito',
    subtitle: 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
    emailSupportButton: 'Suporte por email',
    alreadyHaveAccount: 'Já tem uma conta?',
    signInLink: 'Entrar',
  },

  // Mensagens de status do sistema
  systemMessages: {
    accessRestricted: 'Acesso restrito',
    signUpsDisabled: 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
    contactSupport: 'Entre em contato com o suporte',
    emailSupport: 'Suporte por email',
    alreadyHaveAccount: 'Já tem uma conta?',
    signIn: 'Entrar',
  },

  // Mensagens específicas do Clerk para acesso restrito
  restrictedAccess: {
    title: 'Acesso restrito',
    message: 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
    alreadyHaveAccountText: 'Já tem uma conta?',
    signInText: 'Entrar',
  },
};

export default function Page() {
  const params = useParams();
  
  // Detectar o idioma atual da URL ou usar o padrão
  let currentLocale = 'pt';
  
  // Se estivermos em uma rota com idioma, extrair da URL
  if (typeof window !== 'undefined') {
    const pathSegments = window.location.pathname.split('/');
    const possibleLocale = pathSegments[1];
    if (['pt', 'en', 'es'].includes(possibleLocale)) {
      currentLocale = possibleLocale;
    }
  }

  // Construir as URLs com base no idioma atual
  const signUpPath = `/${currentLocale}/sign-up`;
  const signInUrl = `/${currentLocale}/sign-in`;
  const redirectUrl = `/${currentLocale}/onboarding`;

  console.log('🔍 [SIGN-UP] Configuração:', {
    currentLocale,
    signUpPath,
    signInUrl,
    redirectUrl,
    pathname: typeof window !== 'undefined' ? window.location.pathname : 'SSR'
  });

  return (
    <div className="min-h-screen relative flex items-center justify-center">
      {/* Backdrop de estrelas animadas */}
      <BackdropEffect />
      
      {/* Overlay para o modo claro */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50/80 to-white/90 dark:from-transparent dark:to-transparent" />
      
      {/* Container principal */}
      <div className="relative z-10 w-full max-w-md">
        <Suspense fallback={<div>Carregando...</div>}>
          <ClerkInviteHandler />
        </Suspense>
        
        <SignUp
          path={signUpPath}
          routing="path"
          signInUrl={signInUrl}
          fallbackRedirectUrl={redirectUrl}
          afterSignUpUrl={redirectUrl}
          redirectUrl={redirectUrl}
          forceRedirectUrl={redirectUrl}
          appearance={{
            elements: {
              rootBox: "mx-auto",
             
     
              headerSubtitle: "text-gray-600 dark:text-gray-300 text-sm",
              
              // Botões sociais
              socialButtonsIconButton: "border border-gray-300 dark:border-[#5600ce]/30 bg-white dark:bg-[#0f0a14] hover:bg-gray-50 dark:hover:bg-[#5600ce]/10 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-200",
              socialButtonsBlockButton: "border border-gray-300 dark:border-[#5600ce]/30 bg-white dark:bg-[#0f0a14] hover:bg-gray-50 dark:hover:bg-[#5600ce]/10 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-200",
              
              // Botão principal
              formButtonPrimary: "bg-gradient-to-r from-[#ff0074] to-[#5600ce] hover:from-[#ff0074]/90 hover:to-[#5600ce]/90 text-white font-medium rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl",
              
              // Campos de entrada
              formFieldInput: "border border-gray-300 dark:border-[#5600ce]/30 bg-white dark:bg-[#0f0a14] text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-[#ff0074] dark:focus:border-[#ff0074] focus:ring-1 focus:ring-[#ff0074]/20 rounded-lg transition-all duration-200",
              formFieldLabel: "text-[#270038] dark:text-white font-medium text-sm",
              
              // Textos e links
              identityPreviewText: "text-gray-600 dark:text-gray-300",
              identityPreviewEditButton: "text-[#5600ce] hover:text-[#ff0074] dark:text-[#5600ce] dark:hover:text-[#ff0074]",
              formResendCodeLink: "text-[#5600ce] hover:text-[#ff0074] font-medium transition-colors duration-200",
              footerActionLink: "text-[#5600ce] hover:text-[#ff0074] font-medium transition-colors duration-200",
              
              // Divisor
              dividerLine: "bg-gray-200 dark:bg-[#5600ce]/20",
              dividerText: "text-gray-500 dark:text-gray-400 text-sm",
              
              // Mensagens de erro e sucesso
              formFieldErrorText: "text-red-500 dark:text-red-400 text-sm",
              formFieldSuccessText: "text-green-600 dark:text-green-400 text-sm",
              formFieldHintText: "text-gray-500 dark:text-gray-400 text-xs",
              
              // Código de verificação
              formFieldInputShowPasswordButton: "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200",
              
              // Loading states
              spinner: "text-[#ff0074]",
              
              // Footer
              footer: "text-gray-600 dark:text-gray-300",
              footerPages: "text-gray-600 dark:text-gray-300",
              
              // Card body
              main: "p-0",
              
              // Form container
              form: "space-y-6",
              
              // Alternative methods
              alternativeMethodsBlockButton: "border border-gray-300 dark:border-[#5600ce]/30 bg-white dark:bg-[#0f0a14] hover:bg-gray-50 dark:hover:bg-[#5600ce]/10 text-gray-700 dark:text-gray-200 rounded-lg transition-all duration-200",
              
              // Internal form elements
              formFieldAction: "text-[#5600ce] hover:text-[#ff0074] font-medium",
              
              // OTP input
              formFieldInputGroup: "space-y-2",
              otpCodeFieldInput: "border border-gray-300 dark:border-[#5600ce]/30 bg-white dark:bg-[#0f0a14] text-gray-900 dark:text-white focus:border-[#ff0074] dark:focus:border-[#ff0074] rounded-lg text-center",
              
              // Modal overlay (if any)
              modalContent: "bg-white dark:bg-[#0f0a14] border border-gray-200 dark:border-[#5600ce]/30",
              modalCloseButton: "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200",
              
              // Password requirements
              formFieldOptionalText: "text-gray-500 dark:text-gray-400 text-sm",
              
              // Step indicator
              formHeaderTitle: "text-[#270038] dark:text-white font-semibold",
              formHeaderSubtitle: "text-gray-600 dark:text-gray-300",
              
              // Continue button states
              formButtonReset: "border border-gray-300 dark:border-[#5600ce]/30 bg-white dark:bg-[#0f0a14] hover:bg-gray-50 dark:hover:bg-[#5600ce]/10 text-gray-700 dark:text-gray-200",
              
              // Progress indicator
              formFieldRadioGroupItem: "border border-gray-300 dark:border-[#5600ce]/30 text-gray-700 dark:text-gray-200",
              
              // Terms and conditions
              formFieldAcceptTermsOfService: "text-gray-600 dark:text-gray-300 text-sm",
              formFieldAcceptTermsOfServiceLink: "text-[#5600ce] hover:text-[#ff0074] underline",
              
              // Captcha
              captcha: "border border-gray-300 dark:border-[#5600ce]/30 rounded-lg",
              
              // Loading overlay
              loadingText: "text-gray-600 dark:text-gray-300",
            },
            variables: {
              colorPrimary: '#ff0074',
              colorSuccess: '#10b981',
              colorWarning: '#f59e0b',
              colorDanger: '#ef4444',
              colorNeutral: '#6b7280',
              fontFamily: 'inherit',
              borderRadius: '0.5rem'
            }
          }}
        />
      </div>
    </div>
  );
} 