const admin = require('firebase-admin');

// Inicializar Firebase Admin (usar suas credenciais)
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json'))
  });
}

const db = admin.firestore();

async function recalculateAllProposalTotals() {
  console.log('🚀 Iniciando recálculo de todas as propostas...');
  
  try {
    // Buscar todas as propostas
    const proposalsSnapshot = await db.collection('proposals').get();
    console.log(`📋 Encontradas ${proposalsSnapshot.docs.length} propostas para recalcular`);
    
    let processedCount = 0;
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const proposalDoc of proposalsSnapshot.docs) {
      const proposalId = proposalDoc.id;
      const proposalData = proposalDoc.data();
      
      try {
        console.log(`\n📊 Processando proposta: ${proposalId} - "${proposalData.nome}"`);
        
        // Buscar todos os influenciadores desta proposta
        const influencersSnapshot = await db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .get();
        
        console.log(`  👥 Influenciadores encontrados: ${influencersSnapshot.docs.length}`);
        
        let totalAmount = 0;
        let budgetCount = 0;
        
        // Para cada influenciador, buscar seus budgets na subcoleção
        for (const influencerDoc of influencersSnapshot.docs) {
          const influencerId = influencerDoc.id;
          
          const budgetsSnapshot = await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .doc(influencerId)
            .collection('budgets')
            .get();
          
          console.log(`    💰 Budgets do influenciador ${influencerId}: ${budgetsSnapshot.docs.length}`);
          
          // Somar valores dos budgets (priorizar budgetedPrice se houver)
          budgetsSnapshot.docs.forEach(budgetDoc => {
            const budgetData = budgetDoc.data();
            const amount = budgetData.budgetedPrice || budgetData.amount || 0;
            
            if (amount > 0) {
              totalAmount += amount;
              budgetCount++;
              console.log(`      ✅ Budget ${budgetDoc.id}: R$ ${amount.toLocaleString('pt-BR')} (${budgetData.serviceType || 'sem tipo'})`);
            } else {
              console.log(`      ⚪ Budget ${budgetDoc.id}: R$ 0 (ignorado)`);
            }
          });
        }
        
        console.log(`  📈 Total calculado: R$ ${totalAmount.toLocaleString('pt-BR')} (${budgetCount} budgets)`);
        console.log(`  📊 Total atual: R$ ${(proposalData.totalAmount || 0).toLocaleString('pt-BR')}`);
        
        // Atualizar o total apenas se for diferente do atual
        if (totalAmount !== (proposalData.totalAmount || 0)) {
          await db.collection('proposals').doc(proposalId).update({
            totalAmount: totalAmount,
            updatedAt: new Date(),
            recalculatedAt: new Date(),
            recalculatedBy: 'migration-script'
          });
          
          console.log(`  ✅ ATUALIZADO: ${proposalData.totalAmount || 0} → ${totalAmount}`);
          updatedCount++;
        } else {
          console.log(`  ⚪ Sem mudanças necessárias`);
        }
        
        processedCount++;
        
      } catch (proposalError) {
        console.error(`  ❌ Erro ao processar proposta ${proposalId}:`, proposalError);
        errorCount++;
      }
    }
    
    console.log('\n🎉 Recálculo concluído!');
    console.log(`📊 Estatísticas:`);
    console.log(`  - Propostas processadas: ${processedCount}`);
    console.log(`  - Propostas atualizadas: ${updatedCount}`);
    console.log(`  - Erros encontrados: ${errorCount}`);
    
  } catch (error) {
    console.error('❌ Erro geral no recálculo:', error);
  }
}

// Executar o script
recalculateAllProposalTotals()
  .then(() => {
    console.log('\n✅ Script finalizado com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script finalizado com erro:', error);
    process.exit(1);
  }); 
