import { NextRequest, NextResponse } from 'next/server';
import { ProposalSharingService } from '@/lib/proposal-sharing-service';
import { PermissionLevel } from '@/types/proposal-sharing';

// PUT - Atualizar permissões do colaborador
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; collaboratorId: string }> }
) {
  try {
    const { id: proposalId, collaboratorId } = await params;
    const { permissionLevel } = await request.json();

    await ProposalSharingService.updateCollaboratorPermissions(
      proposalId,
      collaboratorId,
      permissionLevel as PermissionLevel
    );

    return NextResponse.json({
      success: true,
      message: 'Permissões atualizadas com sucesso'
    });
  } catch (error) {
    console.error('Erro ao atualizar permissões:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Remover colaborador
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; collaboratorId: string }> }
) {
  try {
    const { id: proposalId, collaboratorId } = await params;

    await ProposalSharingService.removeCollaborator(proposalId, collaboratorId);

    return NextResponse.json({
      success: true,
      message: 'Colaborador removido com sucesso'
    });
  } catch (error) {
    console.error('Erro ao remover colaborador:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 