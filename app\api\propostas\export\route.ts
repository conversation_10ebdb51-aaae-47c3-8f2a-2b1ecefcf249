import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';
import { ProposalFilters } from '@/types/proposal';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Construir filtros a partir dos parâmetros de query
    const filters: ProposalFilters = {
      brandId: searchParams.get('brandId') || undefined,
      brandName: searchParams.get('brandName') || undefined,
      search: searchParams.get('search') || undefined,
      assignedTo: searchParams.get('assignedTo') || undefined,
      campaignType: searchParams.get('campaignType') || undefined,
      priority: searchParams.get('priority') as any || undefined,
      influencerTier: searchParams.get('influencerTier') as any || undefined,
      status: searchParams.get('status')?.split(',') as any || undefined,
      tags: searchParams.get('tags')?.split(',') || undefined,
      dateRange: undefined
    };
    
    // Processar intervalo de datas
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    if (startDate && endDate) {
      filters.dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      };
    }
    
    // Buscar propostas com filtros
    const proposals = await ProposalService.getProposals(filters);
    
    // Gerar CSV
    const csvHeaders = [
      'ID',
      'Status',
      'Prioridade',
      'Marca',
      'Influenciador',
      'Campanha',
      'Valor Total',
      'Moeda',
      'Data de Criação',
      'Data de Envio',
      'Prazo de Resposta',
      'Última Interação',
      'Responsável',
      'Tags',
      'Visualizações',
      'Tempo de Resposta (h)',
      'Rodadas de Negociação'
    ];
    
    const csvRows = proposals.map(proposal => [
      proposal.id,
      proposal.status,
      proposal.priority,
      proposal.brandName,
      proposal.influencerName,
      proposal.campaignName || '',
      proposal.totalAmount,
      proposal.currency,
      format(new Date(proposal.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR }),
      proposal.sentAt ? format(new Date(proposal.sentAt), 'dd/MM/yyyy HH:mm', { locale: ptBR }) : '',
      proposal.responseDeadline ? format(new Date(proposal.responseDeadline), 'dd/MM/yyyy HH:mm', { locale: ptBR }) : '',
      proposal.lastInteractionAt ? format(new Date(proposal.lastInteractionAt), 'dd/MM/yyyy HH:mm', { locale: ptBR }) : '',
      proposal.assignedTo,
      proposal.tags.join('; '),
      proposal.viewCount,
      proposal.responseTime || '',
      proposal.negotiationRounds
    ]);
    
    // Converter para CSV
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');
    
    // Retornar arquivo CSV
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="propostas-${format(new Date(), 'yyyy-MM-dd')}.csv"`,
        'Cache-Control': 'no-cache'
      }
    });
    
  } catch (error) {
    console.error('Erro ao exportar propostas:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        message: 'Falha ao exportar propostas'
      },
      { status: 500 }
    );
  }
}

