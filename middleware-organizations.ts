import { NextRequest, NextResponse } from 'next/server';

// Rotas que requerem organização
const ORGANIZATION_REQUIRED_ROUTES = [
  '/dashboard',
  '/campanhas',
  '/influencers',
  '/marcas',
  '/propostas',
  '/settings',
  '/brand-dashboard',
  '/orcamento',
  '/negociacoes',
  '/workspace',
  '/creators',
  '/notes'
];

// Rotas que requerem admin
const ADMIN_REQUIRED_ROUTES = [
  '/admin'
];

// Rotas públicas que não requerem autenticação
const PUBLIC_ROUTES = [
  '/auth',
  '/api/auth',
  '/shared',
  '/test-organizations',
  '/select-organization'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Permitir rotas públicas
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  // Verificar se a rota requer organização
  const requiresOrganization = ORGANIZATION_REQUIRED_ROUTES.some(route => 
    pathname.startsWith(route)
  );
  
  if (requiresOrganization) {
    // Verificar se há organização selecionada nos headers/cookies
    const organizationId = request.headers.get('x-organization-id') || 
                          request.cookies.get('selectedOrganizationId')?.value;
    
    if (!organizationId) {
      // Redirecionar para seleção de organização
      const selectOrgUrl = new URL('/select-organization', request.url);
      return NextResponse.redirect(selectOrgUrl);
    }
    
    // Adicionar organizationId aos headers para as páginas
    const response = NextResponse.next();
    response.headers.set('x-organization-id', organizationId);
    return response;
  }
  
  // Verificar se a rota requer admin
  const requiresAdmin = ADMIN_REQUIRED_ROUTES.some(route => 
    pathname.startsWith(route)
  );
  
  if (requiresAdmin) {
    // Para rotas admin, verificar se é super admin (WorkOS)
    // Por enquanto, permitir acesso baseado apenas na autenticação
    // TODO: Implementar verificação de role admin via WorkOS
    return NextResponse.next();
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}; 

