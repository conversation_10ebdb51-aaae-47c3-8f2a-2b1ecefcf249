// Sistema de colaboração para propostas compartilhadas

export interface ProposalCollaborator {
  userId: string;
  userEmail: string;
  userName: string;
  role: 'owner' | 'editor' | 'viewer';
  permissions: {
    canEdit: boolean;
    canShare: boolean;
    canDelete: boolean;
    canAddInfluencers: boolean;
    canViewBudgets: boolean;
  };
  addedBy: string;
  addedAt: Date;
  status: 'active' | 'pending' | 'revoked';
}

export interface ProposalWithCollaborators {
  id: string;
  // ... campos originais da proposta
  criadoPor: string; // Proprietário original
  brandId: string;   // Marca original
  
  // NOVO: Sistema de colaboração
  collaborators: ProposalCollaborator[];
  isShared: boolean;
  sharedAt?: Date;
  
  // Metadados de acesso
  lastAccessedBy?: string;
  lastAccessedAt?: Date;
}

export interface CollaborationInvite {
  id: string;
  proposalId: string;
  invitedEmail: string;
  invitedBy: string;
  invitedAt: Date;
  role: 'editor' | 'viewer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  expiresAt: Date;
  token: string;
}

// Funções utilitárias
export function canUserAccessProposal(
  proposal: ProposalWithCollaborators, 
  userId: string
): boolean {
  // Proprietário original
  if (proposal.criadoPor === userId || proposal.brandId === userId) {
    return true;
  }
  
  // Colaborador ativo
  return proposal.collaborators.some(
    collab => collab.userId === userId && collab.status === 'active'
  );
}

export function getUserRoleInProposal(
  proposal: ProposalWithCollaborators,
  userId: string
): 'owner' | 'editor' | 'viewer' | 'none' {
  // Proprietário
  if (proposal.criadoPor === userId || proposal.brandId === userId) {
    return 'owner';
  }
  
  // Colaborador
  const collaborator = proposal.collaborators.find(
    collab => collab.userId === userId && collab.status === 'active'
  );
  
  return collaborator?.role || 'none';
} 

