const admin = require('firebase-admin');
const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function checkExistingInfluencers() {
  try {
    console.log('🔍 Verificando influenciadores existentes...');
    const snapshot = await db.collection('influencers').limit(5).get();
    
    if (snapshot.empty) {
      console.log('❌ Não há influenciadores no banco de dados');
      process.exit(0);
    }
    
    console.log(`📊 Total de influenciadores encontrados: ${snapshot.size}`);
    
    snapshot.forEach(doc => {
      const data = doc.data();
      console.log(`\n📱 ID: ${doc.id}`);
      console.log(`  Nome: ${data.personalInfo?.name || data.name || 'N/A'}`);
      
      if (data.platforms) {
        console.log('  Plataformas:');
        Object.keys(data.platforms).forEach(platform => {
          const platformData = data.platforms[platform];
          console.log(`    📍 ${platform}:`);
          console.log(`      - Username: ${platformData.username || 'N/A'}`);
          console.log(`      - Followers: ${platformData.followers || 'N/A'}`);
          
          if (platformData.views) {
            console.log(`      - Views: ${JSON.stringify(platformData.views)}`);
          } else {
            console.log('      - Views: ❌ Sem dados de views');
          }
          
          if (platformData.pricing) {
            console.log(`      - Pricing: ${JSON.stringify(platformData.pricing)}`);
          }
        });
      } else {
        console.log('  ❌ Sem dados de plataformas');
      }
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Erro:', error);
    process.exit(1);
  }
}

checkExistingInfluencers(); 
