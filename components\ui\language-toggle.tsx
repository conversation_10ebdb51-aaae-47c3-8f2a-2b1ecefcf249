'use client';

import { Languages, Globe } from 'lucide-react';
import { useLanguage } from '@/contexts/language-context';
import { useTranslations } from '@/hooks/use-translations';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface LanguageToggleProps {
  isExpanded?: boolean;
}

const languageNames = {
  pt: 'Português',
  en: 'English',
  es: 'Español'
};

const languageFlags = {
  pt: '🇧🇷',
  en: '🇺🇸',
  es: '🇪🇸'
};

export function LanguageToggle({ isExpanded = false }: LanguageToggleProps) {
  const { locale, setLocale, availableLocales } = useLanguage();
  const { t } = useTranslations();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center gap-3 px-3 py-2 rounded-lg w-full text-gray-700 dark:text-gray-300">
        <div className="flex-shrink-0 w-4 h-4">
          <Globe className="h-4 w-4 stroke-[1.5]" />
        </div>
        {isExpanded && (
          <div className="flex-1 text-left">
            <span className="font-medium">Carregando...</span>
          </div>
        )}
      </div>
    );
  }

  if (isExpanded) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 w-full",
              "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10"
            )}
            aria-label={t('sidebar.language_toggle')}
          >
            <div className="flex-shrink-0 relative w-4 h-4">
              <Globe className="h-4 w-4 stroke-[1.5]" />
            </div>
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="flex-1 text-left flex items-center gap-2"
              >
                <span className="text-sm">{languageFlags[locale]}</span>
                <span className="font-medium text-xs">{languageNames[locale]}</span>
              </motion.div>
            </AnimatePresence>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {availableLocales.map((lang) => (
            <DropdownMenuItem
              key={lang}
              onClick={() => setLocale(lang)}
              className={cn(
                "flex items-center gap-3 cursor-pointer",
                locale === lang && "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400"
              )}
            >
              <span className="text-base">{languageFlags[lang]}</span>
              <span className="font-medium">{languageNames[lang]}</span>
              {locale === lang && (
                <div className="ml-auto w-2 h-2 rounded-full bg-gradient-to-r from-purple-600 to-pink-600" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="bg-transparent hover:bg-black/5 dark:hover:bg-white/10 border-none h-8 w-8"
          aria-label={t('sidebar.language_toggle')}
        >
          <Globe className="h-4 w-4 stroke-[1.5] text-black dark:text-white" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {availableLocales.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => setLocale(lang)}
            className={cn(
              "flex items-center gap-3 cursor-pointer",
              locale === lang && "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400"
            )}
          >
            <span className="text-base">{languageFlags[lang]}</span>
            <span className="font-medium">{languageNames[lang]}</span>
            {locale === lang && (
              <div className="ml-auto w-2 h-2 rounded-full bg-gradient-to-r from-purple-600 to-pink-600" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
