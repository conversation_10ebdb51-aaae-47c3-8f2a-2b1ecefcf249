"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface Category {
  id: string;
  name: string;
  slug: string;
  parentId?: string;
}

export function CategoryManager() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [categoryName, setCategoryName] = useState("");
  const [categorySlug, setCategorySlug] = useState("");

  // Carrega as categorias quando o componente é montado
  useEffect(() => {
    fetchCategories();
  }, []);

  // Função para buscar todas as categorias
  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      console.error('Erro ao buscar categorias:', error);
      toast.error('Erro ao carregar categorias');
    } finally {
      setIsLoading(false);
    }
  };

  // Função para gerar slug automaticamente a partir do nome
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');
  };

  // Gerencia a mudança no nome da categoria e atualiza o slug automaticamente
  const handleNameChange = (value: string) => {
    setCategoryName(value);
    setCategorySlug(generateSlug(value));
  };

  // Reinicia os estados do formulário
  const resetForm = () => {
    setCategoryName("");
    setCategorySlug("");
    setSelectedCategory(null);
  };

  // Abre o diálogo de adição
  const openAddDialog = () => {
    resetForm();
    setIsAddDialogOpen(true);
  };

  // Abre o diálogo de edição com os dados da categoria selecionada
  const openEditDialog = (category: Category) => {
    setSelectedCategory(category);
    setCategoryName(category.name);
    setCategorySlug(category.slug);
    setIsEditDialogOpen(true);
  };

  // Abre o diálogo de exclusão
  const openDeleteDialog = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteDialogOpen(true);
  };

  // Adiciona uma nova categoria
  const addCategory = async () => {
    if (!categoryName || !categorySlug) {
      toast.error('Nome e slug são obrigatórios');
      return;
    }

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: categoryName,
          slug: categorySlug,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao adicionar categoria');
      }

      toast.success('Categoria adicionada com sucesso');
      setIsAddDialogOpen(false);
      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('Erro ao adicionar categoria:', error);
      toast.error('Erro ao adicionar categoria');
    }
  };

  // Atualiza uma categoria existente
  const updateCategory = async () => {
    if (!selectedCategory || !categoryName || !categorySlug) {
      toast.error('Nome e slug são obrigatórios');
      return;
    }

    try {
      const response = await fetch('/api/categories', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedCategory.id,
          name: categoryName,
          slug: categorySlug,
          parentId: selectedCategory.parentId,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao atualizar categoria');
      }

      toast.success('Categoria atualizada com sucesso');
      setIsEditDialogOpen(false);
      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
      toast.error('Erro ao atualizar categoria');
    }
  };

  // Remove uma categoria
  const deleteCategory = async () => {
    if (!selectedCategory) return;

    try {
      const response = await fetch(`/api/categories?id=${selectedCategory.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Erro ao excluir categoria');
      }

      toast.success('Categoria excluída com sucesso');
      setIsDeleteDialogOpen(false);
      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('Erro ao excluir categoria:', error);
      toast.error('Erro ao excluir categoria');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-black">Gerenciar Categorias</h2>
        <Button onClick={openAddDialog} className="bg-primary text-white hover:bg-primary/90">
          <PlusCircle className="h-4 w-4 mr-2" />
          Nova Categoria
        </Button>
      </div>

      {categories.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-gray-600">Nenhuma categoria encontrada</p>
          <Button 
            variant="link" 
            onClick={openAddDialog} 
            className="text-primary mt-2"
          >
            Adicionar categoria
          </Button>
        </div>
      ) : (
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <Table>
            <TableHeader className="bg-gray-50">
              <TableRow>
                <TableHead className="text-black font-medium">Nome</TableHead>
                <TableHead className="text-black font-medium">Slug</TableHead>
                <TableHead className="text-black font-medium w-[100px]">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell className="text-black">{category.name}</TableCell>
                  <TableCell className="text-gray-600">{category.slug}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => openEditDialog(category)}
                        className="h-8 w-8 p-0 text-gray-600 hover:text-black"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => openDeleteDialog(category)}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Diálogo para adicionar categoria */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="bg-white border border-gray-200 text-black">
          <DialogHeader>
            <DialogTitle>Adicionar Categoria</DialogTitle>
            <DialogDescription className="text-gray-600">
              Crie uma nova categoria para organizar influenciadores.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium text-gray-800">
                Nome
              </label>
              <Input
                id="name"
                value={categoryName}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Ex: Moda e Beleza"
                className="bg-white border-gray-200 text-black"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="slug" className="text-sm font-medium text-gray-800">
                Slug
              </label>
              <Input
                id="slug"
                value={categorySlug}
                onChange={(e) => setCategorySlug(e.target.value)}
                placeholder="Ex: moda-e-beleza"
                className="bg-white border-gray-200 text-black"
              />
              <p className="text-xs text-gray-600">
                O slug é usado em URLs e deve conter apenas letras, números e hífens.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddDialogOpen(false)}
              className="border-gray-200 text-gray-800 hover:bg-gray-100"
            >
              Cancelar
            </Button>
            <Button 
              onClick={addCategory}
              className="bg-primary text-white hover:bg-primary/90"
            >
              Adicionar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar categoria */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-white border border-gray-200 text-black">
          <DialogHeader>
            <DialogTitle>Editar Categoria</DialogTitle>
            <DialogDescription className="text-gray-600">
              Atualize os detalhes da categoria selecionada.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="edit-name" className="text-sm font-medium text-gray-800">
                Nome
              </label>
              <Input
                id="edit-name"
                value={categoryName}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Ex: Moda e Beleza"
                className="bg-white border-gray-200 text-black"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="edit-slug" className="text-sm font-medium text-gray-800">
                Slug
              </label>
              <Input
                id="edit-slug"
                value={categorySlug}
                onChange={(e) => setCategorySlug(e.target.value)}
                placeholder="Ex: moda-e-beleza"
                className="bg-white border-gray-200 text-black"
              />
              <p className="text-xs text-gray-600">
                O slug é usado em URLs e deve conter apenas letras, números e hífens.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsEditDialogOpen(false)}
              className="border-gray-200 text-gray-800 hover:bg-gray-100"
            >
              Cancelar
            </Button>
            <Button 
              onClick={updateCategory}
              className="bg-primary text-white hover:bg-primary/90"
            >
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para excluir categoria */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-white border border-gray-200 text-black">
          <DialogHeader>
            <DialogTitle>Excluir Categoria</DialogTitle>
            <DialogDescription className="text-gray-600">
              Tem certeza que deseja excluir esta categoria? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-200 text-gray-800 hover:bg-gray-100"
            >
              Cancelar
            </Button>
            <Button 
              onClick={deleteCategory}
              variant="destructive"
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}


