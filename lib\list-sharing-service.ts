import { db } from './firebase-admin';

/**
 * 🔗 SERVIÇO PARA COMPARTILHAMENTO DE LISTAS E PROPOSTAS
 * Gerencia tokens de compartilhamento e registra visualizações/interações
 */
export class ListSharingService {
  
  /**
   * 📊 Registrar visualização ou interação com token de compartilhamento
   */
  static async registrarVisualizacao(
    token: string, 
    dadosVisualizacao: {
      ip?: string;
      userAgent?: string;
      [key: string]: any;
    }
  ): Promise<void> {
    try {
      console.log('📊 [LIST_SHARING] Registrando visualização:', { token, dadosVisualizacao });

      const agora = new Date();
      
      // Dados da visualização
      const visualizacaoData = {
        token,
        timestamp: agora,
        ip: dadosVisualizacao.ip || 'unknown',
        userAgent: dadosVisualizacao.userAgent || 'unknown',
        ...dadosVisualizacao
      };

      // Salvar visualização na subcoleção de interações
      await db
        .collection('share_tokens')
        .doc(token)
        .collection('interactions')
        .add(visualizacaoData);

      // Atualizar contador no documento principal
      const tokenRef = db.collection('share_tokens').doc(token);
      const tokenDoc = await tokenRef.get();
      
      if (tokenDoc.exists) {
        await tokenRef.update({
          accessCount: (tokenDoc.data()?.accessCount || 0) + 1,
          lastAccessed: agora
        });
      }

      console.log('✅ [LIST_SHARING] Visualização registrada com sucesso');

    } catch (error) {
      console.error('❌ [LIST_SHARING] Erro ao registrar visualização:', error);
      // Não lançar erro para não quebrar o fluxo principal
    }
  }

  /**
   * 📈 Buscar estatísticas de um token de compartilhamento
   */
  static async buscarEstatisticasToken(token: string): Promise<{
    accessCount: number;
    lastAccessed: Date | null;
    interactions: any[];
  }> {
    try {
      // Buscar dados do token
      const tokenDoc = await db.collection('share_tokens').doc(token).get();
      
      if (!tokenDoc.exists) {
        return {
          accessCount: 0,
          lastAccessed: null,
          interactions: []
        };
      }

      const tokenData = tokenDoc.data();

      // Buscar interações
      const interactionsSnapshot = await db
        .collection('share_tokens')
        .doc(token)
        .collection('interactions')
        .orderBy('timestamp', 'desc')
        .limit(100)
        .get();

      const interactions = interactionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));

      return {
        accessCount: tokenData?.accessCount || 0,
        lastAccessed: tokenData?.lastAccessed?.toDate() || null,
        interactions
      };

    } catch (error) {
      console.error('❌ [LIST_SHARING] Erro ao buscar estatísticas:', error);
      return {
        accessCount: 0,
        lastAccessed: null,
        interactions: []
      };
    }
  }

  /**
   * 🗑️ Limpar interações antigas (mais de 30 dias)
   */
  static async limparInteracoesAntigas(): Promise<void> {
    try {
      const trintaDiasAtras = new Date();
      trintaDiasAtras.setDate(trintaDiasAtras.getDate() - 30);

      // Buscar todos os tokens
      const tokensSnapshot = await db.collection('share_tokens').get();

      for (const tokenDoc of tokensSnapshot.docs) {
        const interacoesAntigas = await tokenDoc.ref
          .collection('interactions')
          .where('timestamp', '<', trintaDiasAtras)
          .get();

        // Deletar interações antigas em lotes
        const batch = db.batch();
        interacoesAntigas.docs.forEach(doc => {
          batch.delete(doc.ref);
        });

        if (interacoesAntigas.docs.length > 0) {
          await batch.commit();
          console.log(`🗑️ [LIST_SHARING] Removidas ${interacoesAntigas.docs.length} interações antigas do token ${tokenDoc.id}`);
        }
      }

    } catch (error) {
      console.error('❌ [LIST_SHARING] Erro ao limpar interações antigas:', error);
    }
  }

  /**
   * 🔍 Verificar se token existe e está ativo
   */
  static async verificarToken(token: string): Promise<{
    exists: boolean;
    isActive: boolean;
    isExpired: boolean;
    data?: any;
  }> {
    try {
      const tokenDoc = await db.collection('share_tokens').doc(token).get();

      if (!tokenDoc.exists) {
        return {
          exists: false,
          isActive: false,
          isExpired: false
        };
      }

      const data = tokenDoc.data();
      const agora = new Date();
      const isExpired = data?.expiresAt && data.expiresAt.toDate() < agora;

      return {
        exists: true,
        isActive: data?.isActive || false,
        isExpired,
        data: {
          ...data,
          createdAt: data?.createdAt?.toDate(),
          expiresAt: data?.expiresAt?.toDate(),
          lastAccessed: data?.lastAccessed?.toDate()
        }
      };

    } catch (error) {
      console.error('❌ [LIST_SHARING] Erro ao verificar token:', error);
      return {
        exists: false,
        isActive: false,
        isExpired: false
      };
    }
  }
}
