"use client"

import { <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"

import { Button } from "@/components/ui/button"

interface ThemeToggleProps {
  isExpanded?: boolean
}

export function ThemeToggle({ isExpanded = false }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="flex items-center gap-3 px-3 py-2 rounded-lg w-full text-gray-700 w-0 dark:text-gray-300">
        <div className="flex-shrink-0 w-4 h-4">
          <Sun className="h-4 w-4 stroke-[1.5]" />
        </div>
        {isExpanded && (
          <div className="flex-1 text-left">
            <span className="font-medium">Carregando...</span>
          </div>
        )}
      </div>
    )
  }

  if (isExpanded) {
    return (
      <button
        onClick={() => setTheme(theme === "light" ? "dark" : "light")}
        className={cn(
          "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 w-full",
          "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10"
        )}
        aria-label="Alternar tema"
      >
        <div className="flex-shrink-0 relative w-4 h-4">
          <Sun className="absolute h-4 w-4 stroke-[1.5] rotate-0 scale-100 transition-all dark:rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 stroke-[1.5] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        </div>
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.2 }}
            className="flex-1 text-left"
          >
            <span className="font-medium text-xs">{theme === "light" ? "Modo Light" : "Modo Noturno"}</span>
          </motion.div>
        </AnimatePresence>
      </button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      className="bg-transparent hover:bg-black/5 dark:hover:bg-white/10 border-none h-8 w-5"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      aria-label="Alternar tema"
    >
      <Sun className="h-4 w-4 stroke-[1.5] rotate-0 scale-100 transition-all dark:rotate-90 dark:scale-0 text-black dark:text-white" />
      <Moon className="absolute h-4 w-4 stroke-[1.5] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-black dark:text-white" />
    </Button>
  )
}


