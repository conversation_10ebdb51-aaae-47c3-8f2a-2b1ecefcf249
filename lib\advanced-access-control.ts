// 🎯 SISTEMA ABAC - ATTRIBUTE-BASED ACCESS CONTROL

export interface AccessContext {
  // Informações do usuário
  user: {
    id: string;
    role: string;
    permissions: string[];
    organizationId?: string;
    department?: string;
  };
  
  // Contexto da requisição
  request: {
    timestamp: Date;
    ipAddress: string;
    userAgent: string;
    resource: string;
    action: 'READ' | 'WRITE' | 'DELETE' | 'SHARE';
  };
  
  // Contexto do recurso
  resource: {
    id: string;
    type: 'proposal' | 'influencer' | 'budget';
    ownerId: string;
    organizationId?: string;
    sensitivity: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'SECRET';
    sharedWith?: string[];
  };
  
  // Contexto temporal
  temporal: {
    timeOfDay: number; // 0-23
    dayOfWeek: number; // 0-6
    timezone: string;
    businessHours: boolean;
  };
}

export interface AccessRule {
  id: string;
  name: string;
  description: string;
  priority: number; // Maior número = maior prioridade
  
  // Condições (todas devem ser verdadeiras)
  conditions: {
    userRoles?: string[];
    userPermissions?: string[];
    resourceTypes?: string[];
    resourceSensitivity?: string[];
    actions?: string[];
    timeRestrictions?: {
      startHour?: number;
      endHour?: number;
      allowedDays?: number[];
    };
    organizationMatch?: boolean; // User e resource mesma org
  };
  
  // Efeito da regra
  effect: 'ALLOW' | 'DENY';
  
  // Campos que podem ser acessados (se ALLOW)
  allowedFields?: string[];
  
  // Campos que devem ser negados (se DENY)
  deniedFields?: string[];
  
  // Transformações dos dados
  dataTransformations?: {
    field: string;
    transformation: 'MASK' | 'HASH' | 'REMOVE' | 'ANONYMIZE';
  }[];
}

export class AdvancedAccessControl {
  
  // 📋 REGRAS PREDEFINIDAS
  private static readonly DEFAULT_RULES: AccessRule[] = [
    {
      id: 'deny-all-by-default',
      name: 'Negar Tudo por Padrão',
      description: 'Princípio de menor privilégio - negar tudo por padrão',
      priority: 0,
      conditions: {},
      effect: 'DENY',
      deniedFields: ['*']
    },
    
    {
      id: 'admin-full-access',
      name: 'Admin Acesso Completo',
      description: 'Admins têm acesso total durante horário comercial',
      priority: 100,
      conditions: {
        userRoles: ['super_admin', 'admin'],
        timeRestrictions: {
          startHour: 8,
          endHour: 18,
          allowedDays: [1, 2, 3, 4, 5] // Segunda a sexta
        }
      },
      effect: 'ALLOW',
      allowedFields: ['*']
    },
    
    {
      id: 'user-basic-proposal-access',
      name: 'User - Acesso Básico a Propostas',
      description: 'Users podem ver propostas compartilhadas (dados básicos)',
      priority: 50,
      conditions: {
        userRoles: ['user'],
        resourceTypes: ['proposal'],
        actions: ['READ'],
        organizationMatch: true
      },
      effect: 'ALLOW',
      allowedFields: [
        'id', 'nome', 'descricao', 'status', 'priority', 
        'createdAt', 'updatedAt', 'influencers.basic'
      ],
      dataTransformations: [
        { field: 'email', transformation: 'MASK' },
        { field: 'phone', transformation: 'REMOVE' }
      ]
    },
    
    {
      id: 'user-no-pricing-access',
      name: 'User - Sem Acesso a Pricing',
      description: 'Users nunca podem ver dados de pricing',
      priority: 90,
      conditions: {
        userRoles: ['user']
      },
      effect: 'DENY',
      deniedFields: [
        'totalAmount', 'pricing', 'budget', 'cost', 'payment',
        'financial', 'rate', 'commission', 'fee'
      ]
    },
    
    {
      id: 'external-limited-access',
      name: 'Usuário Externo - Acesso Limitado',
      description: 'Usuários de outras organizações têm acesso muito limitado',
      priority: 80,
      conditions: {
        organizationMatch: false
      },
      effect: 'ALLOW',
      allowedFields: ['id', 'nome', 'status'],
      dataTransformations: [
        { field: 'nome', transformation: 'ANONYMIZE' }
      ]
    },
    
    {
      id: 'confidential-data-protection',
      name: 'Proteção de Dados Confidenciais',
      description: 'Dados confidenciais só para admins',
      priority: 95,
      conditions: {
        resourceSensitivity: ['CONFIDENTIAL', 'SECRET'],
        userRoles: ['user', 'viewer']
      },
      effect: 'DENY',
      deniedFields: ['*']
    }
  ];

  /**
   * 🔐 AVALIAR ACESSO USANDO ABAC
   */
  static evaluateAccess(context: AccessContext): {
    granted: boolean;
    allowedFields: string[];
    deniedFields: string[];
    transformations: any[];
    appliedRules: string[];
    reason: string;
  } {
    
    console.log('🔍 Avaliando acesso ABAC:', {
      user: context.user.role,
      resource: context.resource.type,
      action: context.request.action,
      sensitivity: context.resource.sensitivity
    });

    // Ordenar regras por prioridade (maior primeiro)
    const sortedRules = [...this.DEFAULT_RULES]
      .sort((a, b) => b.priority - a.priority);

    let finalDecision = { granted: false, reason: 'Acesso negado por padrão' };
    let allowedFields: string[] = [];
    let deniedFields: string[] = [];
    let transformations: any[] = [];
    let appliedRules: string[] = [];

    // Avaliar cada regra
    for (const rule of sortedRules) {
      const matches = this.evaluateRule(rule, context);
      
      if (matches) {
        appliedRules.push(rule.id);
        console.log(`📋 Regra aplicada: ${rule.name} (${rule.effect})`);
        
        if (rule.effect === 'ALLOW') {
          finalDecision = { granted: true, reason: `Permitido por: ${rule.name}` };
          
          // Adicionar campos permitidos
          if (rule.allowedFields?.includes('*')) {
            allowedFields = ['*'];
          } else if (rule.allowedFields) {
            allowedFields.push(...rule.allowedFields);
          }
          
          // Adicionar transformações
          if (rule.dataTransformations) {
            transformations.push(...rule.dataTransformations);
          }
          
        } else if (rule.effect === 'DENY') {
          // DENY sempre ganha (mesmo com prioridade menor que ALLOW anteriores)
          if (rule.deniedFields?.includes('*')) {
            finalDecision = { granted: false, reason: `Negado por: ${rule.name}` };
            deniedFields = ['*'];
            break; // Parar avaliação
          } else if (rule.deniedFields) {
            deniedFields.push(...rule.deniedFields);
          }
        }
      }
    }

    // Remover campos negados dos permitidos
    if (!deniedFields.includes('*')) {
      allowedFields = allowedFields.filter(field => !deniedFields.includes(field));
    } else {
      allowedFields = [];
      finalDecision.granted = false;
    }

    console.log('✅ Decisão ABAC:', {
      granted: finalDecision.granted,
      reason: finalDecision.reason,
      allowedFields: allowedFields.slice(0, 5), // Só primeiros 5 para log
      deniedFields: deniedFields.slice(0, 5),
      rulesApplied: appliedRules.length
    });

    return {
      granted: finalDecision.granted,
      allowedFields: [...new Set(allowedFields)], // Remover duplicatas
      deniedFields: [...new Set(deniedFields)],
      transformations,
      appliedRules,
      reason: finalDecision.reason
    };
  }

  /**
   * 📋 AVALIAR SE UMA REGRA SE APLICA AO CONTEXTO
   */
  private static evaluateRule(rule: AccessRule, context: AccessContext): boolean {
    const { conditions } = rule;
    
    // Verificar role do usuário
    if (conditions.userRoles && !conditions.userRoles.includes(context.user.role)) {
      return false;
    }
    
    // Verificar permissões
    if (conditions.userPermissions) {
      const hasPermission = conditions.userPermissions.some(perm => 
        context.user.permissions.includes(perm)
      );
      if (!hasPermission) return false;
    }
    
    // Verificar tipo de recurso
    if (conditions.resourceTypes && !conditions.resourceTypes.includes(context.resource.type)) {
      return false;
    }
    
    // Verificar sensibilidade do recurso
    if (conditions.resourceSensitivity && !conditions.resourceSensitivity.includes(context.resource.sensitivity)) {
      return false;
    }
    
    // Verificar ação
    if (conditions.actions && !conditions.actions.includes(context.request.action)) {
      return false;
    }
    
    // Verificar restrições de tempo
    if (conditions.timeRestrictions) {
      const { startHour, endHour, allowedDays } = conditions.timeRestrictions;
      const { timeOfDay, dayOfWeek } = context.temporal;
      
      if (startHour && timeOfDay < startHour) return false;
      if (endHour && timeOfDay > endHour) return false;
      if (allowedDays && !allowedDays.includes(dayOfWeek)) return false;
    }
    
    // Verificar se usuário e recurso são da mesma organização
    if (conditions.organizationMatch !== undefined) {
      const sameOrg = context.user.organizationId === context.resource.organizationId;
      if (conditions.organizationMatch !== sameOrg) return false;
    }
    
    return true; // Todas as condições foram atendidas
  }

  /**
   * 🔄 APLICAR TRANSFORMAÇÕES NOS DADOS
   */
  static applyDataTransformations(data: any, transformations: any[]): any {
    let transformedData = JSON.parse(JSON.stringify(data)); // Deep clone
    
    for (const transform of transformations) {
      const { field, transformation } = transform;
      
      if (this.hasNestedField(transformedData, field)) {
        switch (transformation) {
          case 'MASK':
            this.setNestedField(transformedData, field, this.maskValue(this.getNestedField(transformedData, field)));
            break;
          case 'HASH':
            this.setNestedField(transformedData, field, this.hashValue(this.getNestedField(transformedData, field)));
            break;
          case 'REMOVE':
            this.removeNestedField(transformedData, field);
            break;
          case 'ANONYMIZE':
            this.setNestedField(transformedData, field, this.anonymizeValue(this.getNestedField(transformedData, field)));
            break;
        }
      }
    }
    
    return transformedData;
  }

  // Utilitários para manipulação de campos aninhados
  private static getNestedField(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private static setNestedField(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => current[key] = current[key] || {}, obj);
    target[lastKey] = value;
  }

  private static hasNestedField(obj: any, path: string): boolean {
    return this.getNestedField(obj, path) !== undefined;
  }

  private static removeNestedField(obj: any, path: string): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => current?.[key], obj);
    if (target) delete target[lastKey];
  }

  // Funções de transformação
  private static maskValue(value: any): string {
    if (typeof value === 'string') {
      if (value.includes('@')) {
        // Email
        const [username, domain] = value.split('@');
        return `${username.slice(0, 2)}***@${domain}`;
      }
      // Texto genérico
      return value.slice(0, 2) + '*'.repeat(Math.max(0, value.length - 2));
    }
    return '***';
  }

  private static hashValue(value: any): string {
    // Simples hash para demo (usar crypto em produção)
    return `hash_${Math.abs(JSON.stringify(value).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0)).toString(36)}`;
  }

  private static anonymizeValue(value: any): string {
    if (typeof value === 'string') {
      return `Usuario_${Math.random().toString(36).substr(2, 6)}`;
    }
    return 'ANONIMO';
  }

  /**
   * 🔧 CRIAR CONTEXTO A PARTIR DA REQUISIÇÃO
   */
  static createContext(
    user: any,
    request: any,
    resource: any,
    ipAddress: string = 'unknown'
  ): AccessContext {
    const now = new Date();
    
    return {
      user: {
        id: user.userId,
        role: user.role,
        permissions: user.permissions || [],
        organizationId: user.organizationId,
        department: user.department
      },
      
      request: {
        timestamp: now,
        ipAddress,
        userAgent: request.headers.get('user-agent') || 'unknown',
        resource: request.url,
        action: this.mapHttpMethodToAction(request.method)
      },
      
      resource: {
        id: resource.id,
        type: this.inferResourceType(request.url),
        ownerId: resource.criadoPor || resource.ownerId,
        organizationId: resource.organizationId || resource.brandId,
        sensitivity: resource.sensitivity || 'INTERNAL',
        sharedWith: resource.sharedWith || []
      },
      
      temporal: {
        timeOfDay: now.getHours(),
        dayOfWeek: now.getDay(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        businessHours: this.isBusinessHours(now)
      }
    };
  }

  private static mapHttpMethodToAction(method: string): 'READ' | 'WRITE' | 'DELETE' | 'SHARE' {
    switch (method.toUpperCase()) {
      case 'GET': return 'READ';
      case 'POST': case 'PUT': case 'PATCH': return 'WRITE';
      case 'DELETE': return 'DELETE';
      default: return 'READ';
    }
  }

  private static inferResourceType(url: string): 'proposal' | 'influencer' | 'budget' {
    if (url.includes('proposal')) return 'proposal';
    if (url.includes('influencer')) return 'influencer';
    if (url.includes('budget')) return 'budget';
    return 'proposal'; // default
  }

  private static isBusinessHours(date: Date): boolean {
    const hour = date.getHours();
    const day = date.getDay();
    return day >= 1 && day <= 5 && hour >= 8 && hour <= 18;
  }
} 

