/* Estilos para animações de metamorfismo na barra de navegação */

/* Efeito de transição suave para todos os elementos */
.nav-morph * {
  transition: all 0.3s ease-in-out;
}

/* Efeito de metamorfismo para o fundo da navegação */
.nav-morph-bg {
  background: rgba(10, 10, 15, 0.7);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Efeito de metamorfismo para os itens do menu */
.nav-menu-item {
  position: relative;
  overflow: hidden;
}

.nav-menu-item::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #9333EA, #EC4899);
  transition: width 0.3s ease;
}

.nav-menu-item:hover::before,
.nav-menu-item.active::before {
  width: 100%;
}

/* Efeito de brilho nos botões e elementos interativos */
.nav-glow {
  position: relative;
}

.nav-glow::after {
  content: '';
  position: absolute;
  inset: -5px;
  background: linear-gradient(90deg, #9333EA, #EC4899);
  filter: blur(15px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.nav-glow:hover::after {
  opacity: 0.5;
}

/* Animação de flutuação para logo e ícones */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.nav-float {
  animation: float 4s ease-in-out infinite;
}

/* Efeito de vidro para elementos dropdown */
.glass-dropdown {
  background: rgba(15, 15, 20, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Efeitos de foco para controle de acessibilidade */
.nav-focus-ring:focus {
  outline: 2px solid #9333EA;
  outline-offset: 2px;
}

/* Animação de entrada para o menu quando aparece */
@keyframes menuAppear {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-appear {
  animation: menuAppear 0.2s ease-out forwards;
}

