import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation, withRelationshipValidation, withCreateIsolation } from '@/lib/middleware/user-isolation';
import { CampaignService } from '@/services/campaign-service';
import { BrandService } from '@/services/brand-service';
import { CreateCampaignSchema, UpdateCampaignSchema } from '@/lib/validation/campaign';
import { CreateCampaignData } from '@/types/campaign';

export const dynamic = 'force-dynamic';

/**
 * 🔍 GET /api/campaigns - Buscar campanhas do usuário autenticado
 * FASE 4.2: API com isolamento automático e validação de ownership de brands
 */
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(req.url);
    const brandId = searchParams.get('brandId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';
    
    // Validar se brandId pertence ao usuário (se fornecido)
    if (brandId) {
      const brand = await BrandService.getBrandById(brandId);
      if (!brand || brand.userId !== userId) {
        return NextResponse.json({
          error: 'Marca não encontrada ou usuário não tem permissão'
        }, { status: 403 });
      }
    }
    
    // Buscar campanhas (aplicaremos filtro de userId no Firebase Rules)
    let campaigns = await CampaignService.getCampaigns({
      brandId: brandId || undefined,
      status: status || undefined
    }, 1000); // Buscar mais resultados para filtrar depois
    
    // Filtrar por userId (redundante com Firebase Rules, mas garante segurança)
    campaigns = campaigns.filter((campaign: any) => campaign.userId === userId);
    
    // Aplicar filtro de busca
    if (search) {
      campaigns = campaigns.filter((campaign: any) =>
        campaign.name.toLowerCase().includes(search.toLowerCase()) ||
        campaign.description?.toLowerCase().includes(search.toLowerCase()) ||
        campaign.objectives?.toLowerCase().includes(search.toLowerCase())
      );
}

    // Aplicar ordenação
    campaigns.sort((a, b) => {
      const aValue = (a as any)[sortBy] || '';
      const bValue = (b as any)[sortBy] || '';
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      }
      return aValue < bValue ? 1 : -1;
    });
    
    // Aplicar paginação
    const total = campaigns.length;
    const startIndex = (page - 1) * limit;
    const paginatedCampaigns = campaigns.slice(startIndex, startIndex + limit);
    
    return NextResponse.json({
      success: true,
      data: paginatedCampaigns,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      filters: { brandId, status, search }
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error('[CAMPAIGNS_API_GET]', { userId, error: errorMessage });
    
    return NextResponse.json({
      error: 'Erro ao buscar campanhas',
      details: errorMessage
    }, { status: 500 });
  }
});

/**
 * 📝 POST /api/campaigns - Criar nova campanha para o usuário autenticado
 * FASE 4.2: API com validação de relacionamento com brand e isolamento por userId
 */
export const POST = withRelationshipValidation(
  withCreateIsolation(async (req: NextRequest, userId: string, context, data) => {
    try {
      // Validar dados com Zod
      const validatedData = CreateCampaignSchema.parse(data);
      
      // Adicionar userId e campos obrigatórios aos dados validados
      const campaignDataWithUserId = { 
        ...validatedData, 
        userId,
        progress: 0,
        status: validatedData.status || 'em-analise'
      } as any;
      
      // Criar campanha com isolamento automático
      const campaignId = await CampaignService.createCampaign(campaignDataWithUserId);
      
      // Buscar campanha criada para retornar dados completos
      const createdCampaign = await CampaignService.getCampaign(campaignId);
      
      return NextResponse.json({
        success: true,
        data: createdCampaign,
        message: 'Campanha criada com sucesso'
      }, { status: 201 });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('[CAMPAIGNS_API_POST]', { userId, error: errorMessage });
      
      // Tratar erros de validação do Zod
      if ((error as any).name === 'ZodError') {
        return NextResponse.json({
          error: 'Dados inválidos',
          details: (error as any).errors
        }, { status: 400 });
      }
      
      return NextResponse.json({ 
        error: 'Erro ao criar campanha',
        details: errorMessage 
      }, { status: 500 });
  }
  }),
  [
    {
      field: 'brandId',
      getResource: (brandId: string) => BrandService.getBrandById(brandId),
      errorMessage: 'Brand especificada não pertence ao usuário'
    }
  ]
);

/**
 * ✏️ PUT /api/campaigns - Atualizar campanha do usuário autenticado
 * FASE 4.2: API com validação de ownership e isolamento por userId
 */
export const PUT = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ 
        error: 'ID da campanha é obrigatório' 
      }, { status: 400 });
    }
    
    const data = await req.json();
    
    // Verificar se a campanha existe e pertence ao usuário
    const existingCampaign = await CampaignService.getCampaign(id);
    
    if (!existingCampaign) {
      return NextResponse.json({
        error: 'Campanha não encontrada'
      }, { status: 404 });
    }
    
    if ((existingCampaign as any).userId !== userId) {
      return NextResponse.json({
        error: 'Usuário não tem permissão para atualizar esta campanha'
      }, { status: 403 });
    }
    
    // Validar dados com Zod (sem campos obrigatórios para update)
    const validatedData = UpdateCampaignSchema.parse(data);
    
    // Se brandId está sendo atualizado, validar ownership
    if (validatedData.brandId && validatedData.brandId !== existingCampaign.brandId) {
      const brand = await BrandService.getBrandById(validatedData.brandId);
      if (!brand || (brand as any).userId !== userId) {
        return NextResponse.json({
          error: 'Brand especificada não pertence ao usuário'
        }, { status: 403 });
      }
    }
    
    // Atualizar campanha no Firestore
    await CampaignService.updateCampaign(id, validatedData as any);
    
    // Buscar campanha atualizada para retornar dados completos
    const updatedCampaign = await CampaignService.getCampaign(id);
    
    return NextResponse.json({
      success: true,
      data: updatedCampaign,
      message: 'Campanha atualizada com sucesso'
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error('[CAMPAIGNS_API_PUT]', { userId, error: errorMessage });
    
    // Tratar erros de validação do Zod
    if ((error as any).name === 'ZodError') {
      return NextResponse.json({
        error: 'Dados inválidos',
        details: (error as any).errors
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      error: 'Erro ao atualizar campanha',
      details: errorMessage 
    }, { status: 500 });
  }
});

/**
 * 🗑️ DELETE /api/campaigns - Deletar campanha do usuário autenticado
 * FASE 4.2: API com validação de ownership e isolamento por userId
 */
export const DELETE = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ 
        error: 'ID da campanha é obrigatório' 
      }, { status: 400 });
    }
    
    // Verificar se a campanha existe e pertence ao usuário
    const existingCampaign = await CampaignService.getCampaign(id);
    
    if (!existingCampaign) {
      return NextResponse.json({
        error: 'Campanha não encontrada'
      }, { status: 404 });
    }
    
    if ((existingCampaign as any).userId !== userId) {
      return NextResponse.json({
        error: 'Usuário não tem permissão para deletar esta campanha'
      }, { status: 403 });
    }
    
    // Deletar campanha do Firestore
    await CampaignService.deleteCampaign(id);
    
    return NextResponse.json({
      success: true,
      message: 'Campanha deletada com sucesso'
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error('[CAMPAIGNS_API_DELETE]', { userId, error: errorMessage });
    
    return NextResponse.json({ 
      error: 'Erro ao deletar campanha',
      details: errorMessage 
    }, { status: 500 });
  }
});

