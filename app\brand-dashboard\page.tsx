'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import { CalendarDateRangePicker } from "./components/date-range-picker"
import { Button } from "@/components/ui/button"
import { Brand } from "@/types/brand-dashboard"
import BrandCampaigns from "./components/brand-campaigns"
import { Search } from "./components/search"


// Dados de exemplo - será substituído por dados reais do backend
const mockBrand: Brand = {
  id: "1",
  name: "<PERSON><PERSON> De<PERSON>",
  campaigns: [
    {
      id: "1",
      name: "Campanha Verão 2024",
      month: "2024-01",
      influencers: [
        {
          id: "1",
          name: "<PERSON>",
          socialNetworks: [
            {
              network: "instagram",
              username: "joaosilva",
              url: "https://instagram.com/joaosilva",
              isPrimary: true,
            },
          ],
          approvalMonth: "2024-01",
          metrics: {
            screenshots: [],
            views: 1000,
            engagement: 5.2,
          },
          previousDeliveries: [],
          services: [
            {
              type: "stories",
              price: 1500,
              quantity: 3,
            },
          ],
          proposalDate: "2024-01-15",
          campaignMonth: "2024-02",
          status: "approved",
        },
      ],
    },
  ],
}

export default function BrandDashboardPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Painel de Marcas</h1>
        <div className="flex items-center gap-4">
          <Search />
          <CalendarDateRangePicker />
        </div>
      </div>
      
      <Tabs defaultValue="proposals" className="space-y-4">
        <TabsList>
          <TabsTrigger value="proposals">Propostas CRM</TabsTrigger>
          <TabsTrigger value="campaigns">Campanhas</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="proposals" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Propostas CRM</h3>
            <p className="text-muted-foreground">Sistema de propostas em desenvolvimento.</p>
          </Card>
        </TabsContent>
        
        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <BrandCampaigns brand={mockBrand} />
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Analytics em desenvolvimento</h3>
            <p className="text-muted-foreground">Métricas e relatórios serão implementados em breve.</p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}



