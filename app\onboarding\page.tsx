'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { ConfettiSideCannons } from '@/components/ui/confetti';
import { CheckCircle, ArrowRight, Building2, Users, UserCheck } from 'lucide-react';

export default function OnboardingPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [userType, setUserType] = useState('');
  const [organizationName, setOrganizationName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isLoaded && !user) {
      router.push('/');
    }
  }, [user, isLoaded, router]);

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="relative w-8 h-8 mx-auto mb-6">
            <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
            <div 
              className="absolute inset-0 rounded-full border-4 border-transparent animate-spin"
              style={{borderTopColor: '#9810fa'}}
            ></div>
          </div>
          <p className="text-gray-600 font-medium">Preparando sua experiência...</p>
        </div>
      </div>
    );
  }

  if (!user) return null;

  const maxStep = userType === 'agency' ? 2 : 1;

  const handleNext = () => {
    if (step < maxStep) {
      setStep(step + 1);
    }
  };
  
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleComplete = async () => {
    if (!userType) return;
    
    setIsLoading(true);

    // Dispara o confetti side cannons imediatamente
    ConfettiSideCannons(['#9810fa', '#ff0074', '#270038', '#ffffff']);
    
    try {
      const response = await fetch('/api/user/complete-onboarding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          userType,
          organizationName: userType === 'agency' ? organizationName : `${userType}-${user.id}`,
          role: userType === 'agency' ? 'admin' : userType === 'manager' ? 'manager' : 'member'
        }),
      });

      if (response.ok) {
        // Delay para mostrar o confetti antes de redirecionar
        setTimeout(() => {
          router.push(`/${user.id}/influencers`);
        }, 2000);
      } else {
        alert('Erro ao configurar conta. Tente novamente.');
        setIsLoading(false);
      }
    } catch (error) {
      alert('Erro ao configurar conta. Tente novamente.');
      setIsLoading(false);
    }
  };

  const userTypeOptions = [
    { 
      id: 'agency', 
      label: 'Agência', 
      desc: 'Gerencio campanhas e múltiplos influenciadores',
      icon: Building2,
      gradient: 'from-purple-500 to-pink-500'
    },
    { 
      id: 'manager', 
      label: 'Manager', 
      desc: 'Coordeno campanhas e equipes de marketing',
      icon: Users,
      gradient: 'from-blue-500 to-purple-500'
    },
    { 
      id: 'influencer', 
      label: 'Influenciador', 
      desc: 'Crio conteúdo e colaboro com marcas',
      icon: UserCheck,
      gradient: 'from-pink-500 to-red-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="flex items-center justify-center min-h-screen px-4 py-8">
        <div className="w-full max-w-lg">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-3">
              Bem-vindo a Deu Match!
            </h1>
            <p className="text-gray-600 text-lg">
              Vamos configurar sua conta em poucos passos
            </p>
          </div>



          {/* Content Card */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8">
            {step === 1 && (
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Qual é o seu perfil?
                </h2>
                <p className="text-gray-600 mb-8">
                  Selecione a opção que melhor descreve sua função
                </p>
                <div className="space-y-4">
                  {userTypeOptions.map((type) => {
                    const IconComponent = type.icon;
                    return (
                      <label 
                        key={type.id} 
                        className={`group block p-6 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-lg ${
                          userType === type.id 
                            ? 'border-purple-500 bg-gradient-to-r from-purple-50 to-pink-50 shadow-md' 
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <input
                          type="radio"
                          name="userType"
                          value={type.id}
                          checked={userType === type.id}
                          onChange={(e) => setUserType(e.target.value)}
                          className="sr-only"
                        />
                        <div className="flex items-start space-x-4">
                          <div 
                            className={`flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r ${type.gradient} text-white`}
                          >
                            <IconComponent className="w-6 h-6" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className="text-lg font-semibold text-gray-900">
                                {type.label}
                              </h3>
                              {userType === type.id && (
                                <CheckCircle className="w-5 h-5 text-purple-500" />
                              )}
                            </div>
                            <p className="text-gray-600 mt-1">{type.desc}</p>
                          </div>
                        </div>
                      </label>
                    );
                  })}
                </div>
              </div>
            )}

            {step === 2 && userType === 'agency' && (
              <div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Nome da sua agência
                </h2>
                <p className="text-gray-600 mb-8">
                  Como você gostaria que sua agência fosse identificada?
                </p>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nome da agência
                    </label>
                    <Input
                      placeholder="Ex: Digital Marketing Pro"
                      value={organizationName}
                      onChange={(e) => setOrganizationName(e.target.value)}
                      className="w-full h-12 px-4 text-gray-900 bg-white border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between space-x-4">
            <Button
              variant="ghost"
              onClick={handleBack}
              disabled={step === 1 || isLoading}
              className="px-8 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              Voltar
            </Button>

            {/* Botão dinâmico baseado na etapa e tipo de usuário */}
            {(step === 1 && userType === 'agency') ? (
              <Button
                onClick={handleNext}
                disabled={!userType || isLoading}
                className="px-8 py-3 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
                style={{
                  background: 'linear-gradient(135deg, #9810fa 0%, #ff0074 100%)'
                }}
              >
                <span>Continuar</span>
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                disabled={
                  (step === 1 && !userType) ||
                  (step === 2 && userType === 'agency' && !organizationName.trim()) ||
                  isLoading
                }
                className="px-8 py-3 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
                style={{
                  background: isLoading 
                    ? 'linear-gradient(135deg, rgba(152, 16, 250, 0.7) 0%, rgba(236, 0, 63, 0.7) 100%)'
                    : 'linear-gradient(135deg, #9810fa 0%, #ff0074 100%)'
                }}
              >
                <span>{isLoading ? 'Configurando...' : 'Finalizar configuração'}</span>
                {!isLoading && <CheckCircle className="w-4 h-4" />}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 


