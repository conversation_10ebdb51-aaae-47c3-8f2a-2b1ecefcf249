import { 
  BrandInfluencer, 
  CreateBrandInfluencerData,
  UpdateBrandInfluencerData,
  BrandInfluencerFilters,
  BrandInfluencerSearchResult 
} from '@/types/brand-influencer';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';
import { db, FieldValue } from '@/lib/firebase-admin';

/**
 * 🤝 SERVICE PARA ASSOCIAÇÕES MARCA-INFLUENCIADOR
 * Gerencia relacionamentos entre marcas e influenciadores
 */
export class BrandInfluencerService {
  private static COLLECTION_NAME = 'brand_influencers';

  /**
   * Criar nova associação entre marca e influenciador
   */
  static async createAssociation(
    data: CreateBrandInfluencerData,
    userId: string
  ): Promise<BrandInfluencer> {
    try {
      console.log('[BRAND_INFLUENCER_SERVICE] Criando associação:', { 
        brandId: data.brandId,
        influencerId: data.influencerId,
        userId 
      });

      // Verificar se a associação já existe
      console.log('[BRAND_INFLUENCER_SERVICE] Verificando associação existente...');
      const existingAssociation = await this.findExistingAssociation(
        data.brandId,
        data.influencerId,
        userId
      );

      if (existingAssociation) {
        const errorMsg = `Associação entre marca ${data.brandId} e influenciador ${data.influencerId} já existe`;
        console.log('[BRAND_INFLUENCER_SERVICE] ERRO - Associação duplicada:', errorMsg);
        throw new Error(errorMsg);
      }

      // Buscar dados da marca e influenciador para cache
      console.log('[BRAND_INFLUENCER_SERVICE] Buscando dados da marca e influenciador...');
      const [brandData, influencerData] = await Promise.all([
        this.getBrandData(data.brandId, userId),
        this.getInfluencerData(data.influencerId, userId)
      ]);

      console.log('[BRAND_INFLUENCER_SERVICE] Dados da marca encontrados:', !!brandData);
      console.log('[BRAND_INFLUENCER_SERVICE] Dados do influenciador encontrados:', !!influencerData);

      if (!brandData) {
        const errorMsg = `Marca ${data.brandId} não encontrada ou não pertence ao usuário ${userId}`;
        console.error('[BRAND_INFLUENCER_SERVICE] ERRO - Marca não encontrada:', errorMsg);
        throw new Error(errorMsg);
      }

      if (!influencerData) {
        const errorMsg = `Influenciador ${data.influencerId} não encontrado ou não pertence ao usuário ${userId}`;
        console.error('[BRAND_INFLUENCER_SERVICE] ERRO - Influenciador não encontrado:', errorMsg);
        throw new Error(errorMsg);
      }

      // Preparar dados completos
      console.log('[BRAND_INFLUENCER_SERVICE] Preparando dados da associação...');
      const associationData = IsolationUtils.prepareCreateData(
        {
          brandId: data.brandId,
          influencerId: data.influencerId,
          userId: userId,
          
          // Cache dos dados
          brandName: brandData.name || 'Nome não informado',
          brandLogo: brandData.logo || '',
          influencerName: influencerData.name || 'Nome não informado',
          influencerAvatar: influencerData.avatar || '',
          
          // Configurações
          tags: data.tags || [],
          
          // Histórico vazio inicialmente
          collaborationHistory: [],
          
          // Detalhes do contrato
          contractDetails: data.contractDetails || null
        },
        userId,
        userId
      );

      console.log('[BRAND_INFLUENCER_SERVICE] Dados da associação preparados:', {
        brandName: associationData.brandName,
        influencerName: associationData.influencerName,
        userId: associationData.userId
      });

      // Criar documento no Firestore
      console.log('[BRAND_INFLUENCER_SERVICE] Criando documento no Firestore...');
      const docRef = await db.collection(BrandInfluencerService.COLLECTION_NAME).add(associationData);

      console.log('[BRAND_INFLUENCER_SERVICE] Documento criado com ID:', docRef.id);

      // Log da criação
      IsolationUtils.logIsolationEvent(
        'create',
        'brand_influencers',
        docRef.id,
        userId,
        {
          brandId: data.brandId,
          brandName: brandData.name,
          influencerId: data.influencerId,
          influencerName: influencerData.name
        }
      );

      const result = {
        id: docRef.id,
        ...associationData
      } as BrandInfluencer;

      console.log('[BRAND_INFLUENCER_SERVICE] Associação criada com sucesso:', result.id);
      
      return result;

    } catch (error: any) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro detalhado ao criar associação:', {
        error: error.message,
        stack: error.stack,
        brandId: data.brandId,
        influencerId: data.influencerId,
        userId
      });
      
      // Re-lançar o erro com mais contexto
      throw new Error(`Falha ao criar associação (Marca: ${data.brandId}, Influenciador: ${data.influencerId}): ${error.message}`);
    }
  }

  /**
   * Buscar associações do usuário
   */
  static async getAssociations(
    userId: string,
    filters: BrandInfluencerFilters = {}
  ): Promise<BrandInfluencerSearchResult> {
    try {
      let query = db.collection(BrandInfluencerService.COLLECTION_NAME)
        .where('userId', '==', userId);

      // Aplicar filtros
      if (filters.brandId) {
        query = query.where('brandId', '==', filters.brandId);
      }

      if (filters.influencerId) {
        query = query.where('influencerId', '==', filters.influencerId);
      }





      const snapshot = await query.get();
      
      let associations = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as BrandInfluencer[];

      // Filtros em memória
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        associations = associations.filter(assoc => 
          assoc.brandName.toLowerCase().includes(searchTerm) ||
          assoc.influencerName.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.tags && filters.tags.length > 0) {
        associations = associations.filter(assoc =>
          filters.tags!.some(tag => assoc.tags?.includes(tag))
        );
      }

      return {
        associations,
        total: associations.length,
        page: 1,
        limit: associations.length
      };

    } catch (error: any) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao buscar associações:', error);
      throw new Error(`Erro ao buscar associações: ${error.message}`);
    }
  }

  /**
   * Buscar associações por influenciador
   */
  static async getAssociationsByInfluencer(
    influencerId: string,
    userId: string
  ): Promise<BrandInfluencer[]> {
    const result = await this.getAssociations(userId, { influencerId });
    return result.associations;
  }

  /**
   * Buscar associações por marca
   */
  static async getAssociationsByBrand(
    brandId: string,
    userId: string
  ): Promise<BrandInfluencer[]> {
    const result = await this.getAssociations(userId, { brandId });
    return result.associations;
  }

  /**
   * Buscar influenciadores de uma marca específica (para auto-importação em listas)
   */
  static async getBrandInfluencersByBrandId(
    brandId: string,
    userId: string
  ): Promise<{ influencerId: string; influencerName: string; influencerAvatar: string }[]> {
    try {
      console.log('[BRAND_INFLUENCER_SERVICE] Buscando influenciadores da marca:', { brandId, userId });

      const snapshot = await db.collection(BrandInfluencerService.COLLECTION_NAME)
        .where('userId', '==', userId)
        .where('brandId', '==', brandId)
        .get();

      const influencers = snapshot.docs.map(doc => {
        const data = doc.data() as BrandInfluencer;
        return {
          influencerId: data.influencerId,
          influencerName: data.influencerName || 'Nome não informado',
          influencerAvatar: data.influencerAvatar || ''
        };
      });

      console.log('[BRAND_INFLUENCER_SERVICE] Influenciadores encontrados:', {
        brandId,
        totalInfluencers: influencers.length,
        influencerIds: influencers.map(inf => inf.influencerId)
      });

      return influencers;

    } catch (error: any) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao buscar influenciadores da marca:', error);
      throw new Error(`Erro ao buscar influenciadores: ${error.message}`);
    }
  }

  /**
   * Atualizar associação
   */
  static async updateAssociation(
    associationId: string,
    data: UpdateBrandInfluencerData,
    userId: string
  ): Promise<BrandInfluencer> {
    try {
      const docRef = db.collection(BrandInfluencerService.COLLECTION_NAME).doc(associationId);
      const docSnap = await docRef.get();

      if (!docSnap.exists) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Associação não encontrada',
          userId,
          associationId
        );
      }

      const associationData = docSnap.data() as BrandInfluencer;

      // Verificar ownership
      if (associationData.userId !== userId) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para atualizar esta associação',
          userId,
          associationId
        );
      }

      const updateData = IsolationUtils.prepareUpdateData(data, userId);
      await docRef.update(updateData as any);

      // Log da atualização
      IsolationUtils.logIsolationEvent(
        'update',
        'brand_influencers',
        associationId,
        userId,
        { camposAlterados: Object.keys(data) }
      );

      return {
        ...associationData,
        ...updateData
      } as BrandInfluencer;

    } catch (error: any) {
      if (error instanceof IsolationError) {
        throw error;
      }
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao atualizar associação:', error);
      throw new Error(`Erro ao atualizar associação: ${error.message}`);
    }
  }

  /**
   * Deletar associação
   */
  static async deleteAssociation(
    associationId: string,
    userId: string
  ): Promise<void> {
    try {
      const docRef = db.collection(BrandInfluencerService.COLLECTION_NAME).doc(associationId);
      const docSnap = await docRef.get();

      if (!docSnap.exists) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Associação não encontrada',
          userId,
          associationId
        );
      }

      const associationData = docSnap.data() as BrandInfluencer;

      // Verificar ownership
      if (associationData.userId !== userId) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para deletar esta associação',
          userId,
          associationId
        );
      }

      await docRef.delete();

      // Log da deleção
      IsolationUtils.logIsolationEvent(
        'delete',
        'brand_influencers',
        associationId,
        userId,
        {
          brandName: associationData.brandName,
          influencerName: associationData.influencerName
        }
      );

    } catch (error: any) {
      if (error instanceof IsolationError) {
        throw error;
      }
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao deletar associação:', error);
      throw new Error(`Erro ao deletar associação: ${error.message}`);
    }
  }

  /**
   * Criar múltiplas associações (para quando um influenciador é associado a várias marcas)
   */
  static async createMultipleAssociations(
    influencerId: string,
    brandIds: string[],
    userId: string,
    options: {} = {}
  ): Promise<BrandInfluencer[]> {
    try {
      const associations: BrandInfluencer[] = [];

      for (const brandId of brandIds) {
        try {
          const association = await this.createAssociation(
            {
              brandId,
              influencerId
            },
            userId
          );
          associations.push(association);
        } catch (error: any) {
          console.warn(`Falha ao criar associação para marca ${brandId}:`, error.message);
          // Continuar com as outras marcas mesmo se uma falhar
        }
      }

      return associations;

    } catch (error: any) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao criar múltiplas associações:', error);
      throw new Error(`Erro ao criar associações: ${error.message}`);
    }
  }

  /**
   * Métodos auxiliares privados
   */
  private static async findExistingAssociation(
    brandId: string,
    influencerId: string,
    userId: string
  ): Promise<BrandInfluencer | null> {
    try {
      console.log('[BRAND_INFLUENCER_SERVICE] Verificando associação existente:', {
        brandId,
        influencerId,
        userId,
        collection: BrandInfluencerService.COLLECTION_NAME
      });

      const snapshot = await db.collection(BrandInfluencerService.COLLECTION_NAME)
        .where('userId', '==', userId)
        .where('brandId', '==', brandId)
        .where('influencerId', '==', influencerId)
        .limit(1)
        .get();

      console.log('[BRAND_INFLUENCER_SERVICE] Resultado da consulta:', {
        empty: snapshot.empty,
        size: snapshot.size
      });

      if (snapshot.empty) {
        console.log('[BRAND_INFLUENCER_SERVICE] Nenhuma associação existente encontrada');
        return null;
      }

      const existingAssociation = {
        id: snapshot.docs[0].id,
        ...snapshot.docs[0].data()
      } as BrandInfluencer;

      console.log('[BRAND_INFLUENCER_SERVICE] Associação existente encontrada:', existingAssociation.id);
      return existingAssociation;

    } catch (error) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao verificar associação existente:', {
        brandId,
        influencerId,
        userId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  private static async getBrandData(brandId: string, userId: string): Promise<any> {
    try {
      console.log('[BRAND_INFLUENCER_SERVICE] Buscando marca:', { brandId, userId });
      
      const docRef = db.collection('brands').doc(brandId);
      const docSnap = await docRef.get();

      if (!docSnap.exists) {
        console.log('[BRAND_INFLUENCER_SERVICE] Marca não existe no Firestore:', brandId);
        return null;
      }

      const brandData = docSnap.data();
      console.log('[BRAND_INFLUENCER_SERVICE] Dados da marca encontrados:', {
        id: brandId,
        name: brandData?.name,
        userId: brandData?.userId,
        userMatch: brandData?.userId === userId
      });
      
      // Verificar ownership
      if (brandData?.userId !== userId) {
        console.log('[BRAND_INFLUENCER_SERVICE] Marca não pertence ao usuário:', {
          brandId,
          brandUserId: brandData?.userId,
          requestUserId: userId
        });
        return null;
      }

      console.log('[BRAND_INFLUENCER_SERVICE] Marca válida encontrada:', brandData.name);
      return brandData;

    } catch (error) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao buscar dados da marca:', {
        brandId,
        userId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  private static async getInfluencerData(influencerId: string, userId: string): Promise<any> {
    try {
      console.log('[BRAND_INFLUENCER_SERVICE] Buscando influenciador:', { influencerId, userId });
      
      const docRef = db.collection('influencers').doc(influencerId);
      const docSnap = await docRef.get();

      if (!docSnap.exists) {
        console.log('[BRAND_INFLUENCER_SERVICE] Influenciador não existe no Firestore:', influencerId);
        return null;
      }

      const influencerData = docSnap.data();
      console.log('[BRAND_INFLUENCER_SERVICE] Dados do influenciador encontrados:', {
        id: influencerId,
        name: influencerData?.name,
        userId: influencerData?.userId,
        userMatch: influencerData?.userId === userId
      });
      
      // Verificar ownership
      if (influencerData?.userId !== userId) {
        console.log('[BRAND_INFLUENCER_SERVICE] Influenciador não pertence ao usuário:', {
          influencerId,
          influencerUserId: influencerData?.userId,
          requestUserId: userId
        });
        return null;
      }

      console.log('[BRAND_INFLUENCER_SERVICE] Influenciador válido encontrado:', influencerData.name);
      return influencerData;

    } catch (error) {
      console.error('[BRAND_INFLUENCER_SERVICE] Erro ao buscar dados do influenciador:', {
        influencerId,
        userId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }
}

// Export individual da função para facilitar importação
export const getBrandInfluencersByBrandId = BrandInfluencerService.getBrandInfluencersByBrandId; 

