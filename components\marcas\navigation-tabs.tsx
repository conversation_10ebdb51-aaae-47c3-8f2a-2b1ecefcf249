import React from 'react';
import { Button } from '@/components/ui/button';
import { Users, MessageCircle, FileText } from 'lucide-react';

interface NavigationTabsProps {
  activeTab: 'all' | 'sent' | 'proposals';
  setActiveTab: (tab: 'all' | 'sent' | 'proposals') => void;
  filteredInfluencersCount: number;
  brandInfluencersCount: number;
}

export function NavigationTabs({
  activeTab,
  setActiveTab,
  filteredInfluencersCount,
  brandInfluencersCount
}: NavigationTabsProps) {
  return (
    <div className="flex items-center gap-3">
      <Button
        variant={activeTab === 'all' ? 'default' : 'outline'}
        onClick={() => setActiveTab('all')}
        className="flex bg-[#008eff] text-white items-center gap-2"
      >
        <Users className="h-4 w-4" />
        Todos os Influenciadores ({filteredInfluencersCount})
      </Button>
      <Button
        variant={activeTab === 'sent' ? 'default' : 'outline'}
        onClick={() => setActiveTab('sent')}
        className="flex border dark:text-white items-center gap-2"
      >
        <MessageCircle className="h-4 w-4" />
        Influenciadores Enviados ({brandInfluencersCount})
      </Button>
      <Button
        variant={activeTab === 'proposals' ? 'default' : 'outline'}
        onClick={() => setActiveTab('proposals')}
        className="flex border dark:text-white dark:bg-background items-center gap-2"
      >
        <FileText className="h-4 w-4" />
        Propostas Criadas
      </Button>
    </div>
  );
}

