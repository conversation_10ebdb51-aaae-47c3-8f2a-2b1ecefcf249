'use client'

import { useMemo } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Search, Filter, X, MapPin, Users, Star, Shield, Clock } from "lucide-react"
import { useInfluencersUrlState } from "@/hooks/use-influencers-url-state"

/**
 * 🔥 COMPONENTE CLIENT OTIMIZADO - Apenas para filtros interativos
 * Usa nuqs para gerenciar estado via URL, eliminando useState
 */
export default function FiltersClient() {
  const { state, actions, isFiltered } = useInfluencersUrlState()

  // 🔥 FORMATAÇÃO DE NÚMEROS COM MEMOIZAÇÃO
  const formatNumber = useMemo(() => (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(0)}K`
    return num.toString()
  }, [])

  return (
    <div className="w-80 bg-background border-r border-border overflow-auto">
      <div className="p-4 space-y-6">
        {/* 🔥 HEADER COM CONTADOR DE FILTROS */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-[#ff0074]" />
            <h2 className="font-semibold">Filtros</h2>
            {isFiltered && (
              <Badge variant="secondary" className="text-xs">
                Ativo
              </Badge>
            )}
          </div>
          {isFiltered && (
            <Button
              variant="ghost"
              size="sm"
              onClick={actions.resetFilters}
              className="h-8 px-2 text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Limpar
            </Button>
          )}
        </div>

        <Separator />

        {/* 🔥 BUSCA POR TEXTO */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Buscar
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label htmlFor="search" className="text-xs">Nome ou @username</Label>
              <Input
                id="search"
                placeholder="Digite para buscar..."
                value={state.searchTerm}
                onChange={(e) => actions.setSearchTerm(e.target.value)}
                className="h-9"
              />
            </div>
          </CardContent>
        </Card>

        {/* 🔥 LOCALIZAÇÃO */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center">
              <MapPin className="h-4 w-4 mr-2" />
              Localização
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              placeholder="Cidade, Estado ou País"
              value={state.selectedLocation}
              onChange={(e) => actions.setSelectedLocation(e.target.value)}
              className="h-9"
            />
          </CardContent>
        </Card>

        {/* 🔥 SEGUIDORES */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Seguidores
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{formatNumber(state.minFollowers)}</span>
                <span>{formatNumber(state.maxFollowers)}</span>
              </div>
              <Slider
                value={[state.minFollowers, state.maxFollowers]}
                onValueChange={([min, max]) => actions.setFollowersRange(min, max)}
                max={10000000}
                min={0}
                step={1000}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>

        {/* 🔥 RATING */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center">
              <Star className="h-4 w-4 mr-2" />
              Rating Mínimo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>0</span>
                <span>5</span>
              </div>
              <Slider
                value={[state.minRating]}
                onValueChange={([rating]) => actions.setMinRating(rating)}
                max={5}
                min={0}
                step={0.5}
                className="w-full"
              />
              <div className="text-center text-sm font-medium">
                {state.minRating} estrelas
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 🔥 FILTROS BOOLEANOS */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Opções</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="verified"
                checked={state.verifiedOnly}
                onCheckedChange={actions.setVerifiedOnly}
              />
              <Label htmlFor="verified" className="text-sm flex items-center">
                <Shield className="h-4 w-4 mr-1 text-blue-500" />
                Apenas verificados
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="available"
                checked={state.availableOnly}
                onCheckedChange={actions.setAvailableOnly}
              />
              <Label htmlFor="available" className="text-sm flex items-center">
                <Clock className="h-4 w-4 mr-1 text-green-500" />
                Apenas disponíveis
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* 🔥 DEBUG INFO (apenas em desenvolvimento) */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="border-dashed">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs text-muted-foreground">Debug URL</CardTitle>
            </CardHeader>
            <CardContent>
              <code className="text-xs break-all">{state.currentUrl}</code>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
