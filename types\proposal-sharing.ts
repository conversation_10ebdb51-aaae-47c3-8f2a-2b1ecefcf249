// Tipos para sistema de compartilhamento de propostas

export type UserRole = 'brand' | 'admin' | 'influencer';

export type PermissionLevel = 'view' | 'comment' | 'edit' | 'admin';

export interface ProposalCollaborator {
  id: string;
  userId: string;
  userEmail: string;
  userName: string;
  userRole: UserRole;
  permissionLevel: PermissionLevel;
  invitedBy: string;
  invitedAt: Date;
  joinedAt?: Date;
  status: 'pending' | 'accepted' | 'declined' | 'revoked';
  brandId?: string; // Para usuários do tipo 'brand'
  brandName?: string;
  avatar?: string;
}

export interface ProposalInvite {
  id: string;
  proposalId: string;
  proposalName: string;
  inviterName: string;
  inviterEmail: string;
  inviteeEmail: string;
  inviteeName?: string;
  userRole: UserRole;
  permissionLevel: PermissionLevel;
  brandId?: string;
  brandName?: string;
  token: string; // Token único para aceitar convite
  expiresAt: Date;
  createdAt: Date;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  message?: string; // Mensagem personalizada do convite
}

export interface ShareProposalRequest {
  proposalId: string;
  inviteeEmail: string;
  inviteeName: string;
  userRole: UserRole;
  permissionLevel: PermissionLevel;
  brandId?: string;
  brandName?: string;
  message?: string;
  onlyGenerateLink?: boolean;
}

// NOVO: Tipos para contrapropostas de convidados
export interface GuestProposal {
  id: string;
  value: number;              // Valor proposto (deve ser ≤ budgetedPrice)
  proposedBy: string;         // Email do convidado
  proposedAt: Date;
  status: 'pending' | 'accepted' | 'rejected';
  comments?: string;
  guestInfo: {
    name: string;
    email: string;
    role: UserRole;
    permissionLevel: PermissionLevel;
  };
}

export interface CreateGuestProposalRequest {
  profileId: string;
  service: string;
  value: number;
  comments?: string;
  guestInfo: {
    name: string;
    email: string;
    role: UserRole;
    permissionLevel: PermissionLevel;
  };
}

export interface UpdateGuestProposalRequest {
  value?: number;
  comments?: string;
  status?: 'pending' | 'accepted' | 'rejected';
}

// Estrutura estendida para orçamentos com contrapropostas
export interface ExtendedBudgetItem {
  budgetedPrice: number;
  originalPrice: number;
  updatedAt: Date;
  updatedBy: string;
  guestProposals?: GuestProposal[];
}

// Mapeamento de permissões para interface
export const PERMISSION_LABELS: Record<PermissionLevel, string> = {
  view: 'Visualizar apenas',
  comment: 'Visualizar e comentar',
  edit: 'Visualizar, comentar e editar',
  admin: 'Acesso total'
};

export const PERMISSION_DESCRIPTIONS: Record<PermissionLevel, string> = {
  view: 'Pode visualizar a proposta e detalhes dos influenciadores',
  comment: 'Pode visualizar e participar dos chats com influenciadores',
  edit: 'Pode modificar a proposta, adicionar/remover influenciadores',
  admin: 'Acesso completo, incluindo compartilhar com outros usuários'
};

export const USER_ROLE_LABELS: Record<UserRole, string> = {
  brand: 'Marca',
  admin: 'Administrador',
  influencer: 'Influenciador'
};

// Verificação de permissões
export const hasPermission = (
  userPermission: PermissionLevel,
  requiredPermission: PermissionLevel
): boolean => {
  const hierarchy: Record<PermissionLevel, number> = {
    view: 1,
    comment: 2,
    edit: 3,
    admin: 4
  };
  
  return hierarchy[userPermission] >= hierarchy[requiredPermission];
};

// Verificar se usuário pode realizar ação específica
export const canUserPerformAction = (
  userPermission: PermissionLevel,
  action: 'view' | 'comment' | 'edit' | 'share' | 'delete'
): boolean => {
  switch (action) {
    case 'view':
      return hasPermission(userPermission, 'view');
    case 'comment':
      return hasPermission(userPermission, 'comment');
    case 'edit':
      return hasPermission(userPermission, 'edit');
    case 'share':
    case 'delete':
      return hasPermission(userPermission, 'admin');
    default:
      return false;
  }
}; 

