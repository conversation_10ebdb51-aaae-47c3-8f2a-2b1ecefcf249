// SERVIÇO DE PRICING PARA INFLUENCIADORES
// Gerenciamento completo de preços com subcoleções

import { db } from './firebase';
import { getInfluencerById } from './firebase';
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// import { auth } from 'firebase-admin';

// Interface para pricing de plataforma individual
interface PlatformPrice {
  price: number;
  currency: string;
}

// Interfaces para pricing por plataforma
interface InstagramPricing {
  story?: PlatformPrice;
  reel?: PlatformPrice;
  post?: PlatformPrice;
}

interface TikTokPricing {
  video?: PlatformPrice;
}

interface YouTubePricing {
  shorts?: PlatformPrice;
  insertion?: PlatformPrice;
  dedicated?: PlatformPrice;
}

interface FacebookPricing {
  post?: PlatformPrice;
  story?: PlatformPrice;
}

interface TwitchPricing {
  stream?: PlatformPrice;
}

interface KwaiPricing {
  video?: PlatformPrice;
}

// Interface principal para os serviços de pricing
interface PricingServices {
  instagram?: InstagramPricing;
  tiktok?: TikTokPricing;
  youtube?: YouTubePricing;
  facebook?: FacebookPricing;
  twitch?: TwitchPricing;
  kwai?: KwaiPricing;
}

// Interface principal para documento de pricing
export interface InfluencerPricing {
  id?: string;
  influencerId: string;
  userId: string;
  services: PricingServices;
  isActive: boolean;
  validFrom: Date;
  validUntil?: Date;
  notes?: string;
  clientSpecific?: string;
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
}

// 🔒 Validação de acesso ao pricing
async function validatePricingAccess(
  influencerId: string, 
  requestingUserId: string
): Promise<{ hasAccess: boolean; reason: string }> {
  try {
    // 1. Buscar dados do influenciador
    const influencer = await getInfluencerById(influencerId);
    if (!influencer) {
      return { hasAccess: false, reason: 'Influenciador não encontrado' };
    }
    
    // 2. Verificar se é o proprietário do influenciador
    if (influencer.userId === requestingUserId) {
      return { hasAccess: true, reason: 'Proprietário do influenciador' };
    }
    
    // ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
    // Para now, só permitir acesso ao próprio pricing (owner)
    return { hasAccess: false, reason: 'Acesso limitado ao proprietário apenas (Firebase Auth desabilitado)' };
    
    /*
    // 3. Verificar role do usuário solicitante
    try {
      const userRecord = await auth().getUser(requestingUserId);
      const customClaims = userRecord.customClaims || {};
      const userRole = customClaims.role as string;
      
      if (userRole === 'admin' || userRole === 'manager') {
        return { hasAccess: true, reason: `Role privilegiado: ${userRole}` };
      }
      
      return { hasAccess: false, reason: `Role ${userRole} não tem acesso a pricing de outros usuários` };
      
    } catch (authError) {
      return { hasAccess: false, reason: 'Erro na verificação de permissões' };
    }
    */
    
  } catch (error) {
    return { hasAccess: false, reason: 'Erro interno na validação' };
  }
}

// Função para obter referência da subcoleção pricing
const getPricingCollection = (influencerId: string) => 
  db.collection('influencers').doc(influencerId).collection('pricing');

// ===== FUNÇÕES DE BUSCA =====

// Buscar pricing ativo de um influenciador (com validação de acesso)
export async function getCurrentPricing(
  influencerId: string, 
  requestingUserId?: string
): Promise<InfluencerPricing | null> {
  try {
    // 🔒 Validar acesso se userId for fornecido
    if (requestingUserId) {
      const accessValidation = await validatePricingAccess(influencerId, requestingUserId);
      
      if (!accessValidation.hasAccess) {
        throw new Error(`Acesso negado: ${accessValidation.reason}`);
      }
    }
    
    const pricingCollection = getPricingCollection(influencerId);
    // Simplificar consulta para evitar índice composto
    const snapshot = await pricingCollection
      .where('isActive', '==', true)
      .get();

    if (snapshot.empty) {
      return null;
    }

    // Fazer ordenação no código ao invés de no Firebase
    const activePricings = snapshot.docs.map(doc => ({
      doc,
      data: doc.data(),
      validFrom: doc.data().validFrom?.toDate() || new Date(0)
    }));

    // Ordenar por validFrom (mais recente primeiro)
    activePricings.sort((a, b) => b.validFrom.getTime() - a.validFrom.getTime());

    const doc = activePricings[0].doc;
    const data = activePricings[0].data;
    
    const pricing: InfluencerPricing = {
      id: doc.id,
      influencerId: influencerId, // Garantir que o ID está correto
      userId: data.userId,
      services: data.services || {},
      isActive: data.isActive,
      validFrom: data.validFrom?.toDate() || new Date(),
      validUntil: data.validUntil?.toDate(),
      notes: data.notes,
      clientSpecific: data.clientSpecific,
      createdAt: data.createdAt?.toDate() || new Date(),
      createdBy: data.createdBy,
      updatedAt: data.updatedAt?.toDate() || new Date(),
      updatedBy: data.updatedBy
    };

    return pricing;
  } catch (error) {
    console.error('❌ [PRICING] Erro em getCurrentPricing:', {
      error: error instanceof Error ? error.message : String(error),
      influencerId
    });
    
    throw error;
  }
}

// Buscar histórico de pricing de um influenciador (com validação de acesso)
export async function getPricingHistory(
  influencerId: string,
  requestingUserId?: string
): Promise<InfluencerPricing[]> {
  try {
    // 🔒 Validar acesso se userId for fornecido
    if (requestingUserId) {
      const accessValidation = await validatePricingAccess(influencerId, requestingUserId);
      if (!accessValidation.hasAccess) {
        throw new Error(`Acesso negado: ${accessValidation.reason}`);
      }
    }
    
    const pricingCollection = getPricingCollection(influencerId);
    // Buscar todos sem orderBy para evitar índice composto
    const snapshot = await pricingCollection.get();

    if (snapshot.empty) {
      return [];
    }

    const history = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        influencerId: influencerId, // Garantir que o ID está correto
        userId: data.userId,
        services: data.services || {},
        isActive: data.isActive,
        validFrom: data.validFrom?.toDate() || new Date(),
        validUntil: data.validUntil?.toDate(),
        notes: data.notes,
        clientSpecific: data.clientSpecific,
        createdAt: data.createdAt?.toDate() || new Date(),
        createdBy: data.createdBy,
        updatedAt: data.updatedAt?.toDate() || new Date(),
        updatedBy: data.updatedBy
      } as InfluencerPricing;
    });

    // Ordenar por validFrom (mais recente primeiro) no código
    history.sort((a, b) => b.validFrom.getTime() - a.validFrom.getTime());

    return history;
  } catch (error) {
  
    throw error;
  }
}

// Buscar pricing por ID
export async function getPricingById(influencerId: string, pricingId: string): Promise<InfluencerPricing | null> {
  try {
    const pricingCollection = getPricingCollection(influencerId);
    const doc = await pricingCollection.doc(pricingId).get();
    
    if (!doc.exists) {
      return null;
    }

    const data = doc.data();
    if (!data) {
      return null;
    }
    
    const pricing: InfluencerPricing = {
      id: doc.id,
      influencerId: influencerId,
      userId: data.userId,
      services: data.services || {},
      isActive: data.isActive,
      validFrom: data.validFrom?.toDate() || new Date(),
      validUntil: data.validUntil?.toDate(),
      notes: data.notes,
      clientSpecific: data.clientSpecific,
      createdAt: data.createdAt?.toDate() || new Date(),
      createdBy: data.createdBy,
      updatedAt: data.updatedAt?.toDate() || new Date(),
      updatedBy: data.updatedBy
    };

    return pricing;
  } catch (error) {
 
    throw error;
  }
}

// ===== FUNÇÕES DE CRIAÇÃO E ATUALIZAÇÃO =====

// Criar novo pricing
export async function createPricing(
  pricingData: Omit<InfluencerPricing, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string
): Promise<string> {
  try {
    // Desativar pricing anterior se existir
    await deactivateCurrentPricing(pricingData.influencerId);
    
    const now = new Date();
    const dataToSave = {
      userId: pricingData.userId,
      services: pricingData.services,
      isActive: true, // Novo pricing sempre ativo
      validFrom: pricingData.validFrom || now,
      validUntil: pricingData.validUntil || null,
      notes: pricingData.notes || '',
      clientSpecific: pricingData.clientSpecific || '',
      createdAt: now,
      createdBy: userId,
      updatedAt: now,
      updatedBy: userId
    };

    const pricingCollection = getPricingCollection(pricingData.influencerId);
    const docRef = await pricingCollection.add(dataToSave);
    
    return docRef.id;
  } catch (error) {
   
    throw error;
  }
}

// Atualizar pricing existente
export async function updatePricing(
  influencerId: string,
  pricingId: string,
  updateData: Partial<Omit<InfluencerPricing, 'id' | 'influencerId' | 'userId' | 'createdAt' | 'createdBy'>>,
  userId: string
): Promise<boolean> {
  try {
    const dataToUpdate = {
      ...updateData,
      updatedAt: new Date(),
      updatedBy: userId
    };

    const pricingCollection = getPricingCollection(influencerId);
    await pricingCollection.doc(pricingId).update(dataToUpdate);
    
    return true;
  } catch (error) {
   
    throw error;
  }
}

// Deletar pricing
export async function deletePricing(influencerId: string, pricingId: string): Promise<boolean> {
  try {
    const pricingCollection = getPricingCollection(influencerId);
    await pricingCollection.doc(pricingId).delete();
    
    return true;
  } catch (error) {
    
    throw error;
  }
}

// ===== FUNÇÕES AUXILIARES =====

// Desativar pricing atual
async function deactivateCurrentPricing(influencerId: string): Promise<void> {
  try {
    const pricingCollection = getPricingCollection(influencerId);
    const snapshot = await pricingCollection
      .where('isActive', '==', true)
      .get();

    if (!snapshot.empty) {
      const batch = db.batch();
      
      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          isActive: false,
          validUntil: new Date(),
          updatedAt: new Date()
        });
      });
      
      await batch.commit();
    }
  } catch (error) {
   
    throw error;
  }
}

// Verificar se influenciador tem pricing ativo
export async function hasActivePricing(influencerId: string): Promise<boolean> {
  try {
    const current = await getCurrentPricing(influencerId);
    return current !== null;
  } catch (error) {
   
    return false;
  }
}

// Buscar todos os influenciadores com pricing (para relatórios)
export async function getInfluencersWithPricing(userId: string): Promise<string[]> {
  try {
    // Com subcoleções, precisamos buscar na coleção principal de influencers primeiro
    const influencersSnapshot = await db.collection('influencers')
      .where('userId', '==', userId)
      .get();

    const influencerIds: string[] = [];
    
    // Para cada influenciador, verificar se tem pricing ativo
    for (const influencerDoc of influencersSnapshot.docs) {
      const pricingSnapshot = await getPricingCollection(influencerDoc.id)
        .where('isActive', '==', true)
        .limit(1)
        .get();
      
      if (!pricingSnapshot.empty) {
        influencerIds.push(influencerDoc.id);
      }
    }
    
    return influencerIds;
  } catch (error) {
    
    throw error;
  }
} 

