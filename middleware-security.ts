import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { SECURITY_HEADERS, PRODUCTION_SECURITY_HEADERS, logCSPViolation } from '@/lib/csp-config';

// 🔓 Rotas públicas que não precisam de autenticação
const publicRoutes = [
  '/',
  '/sign-in',
  '/sign-up',
  '/api/webhooks',
  '/_next',
  '/favicon.ico',
  '/logo.png',
  '/images',
  '/api/graphql',
  '/shared',
  '/public'
];

// 🔒 Rotas protegidas que requerem autenticação
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/admin(.*)',
  '/settings(.*)',
  '/onboarding',
  '/(.*)/(influencers|lists|propostas)(.*)',
  '/negociacoes(.*)',
  '/campanhas(.*)',
  '/marcas(.*)',
  '/orcamento(.*)'
]);

// 📊 Rate limiting simples (em produção usar Redis)
const requestCounts = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(ip: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutos
  const maxRequests = 100;
  
  const key = ip;
  const current = requestCounts.get(key);
  
  if (!current || now > current.resetTime) {
    const resetTime = now + windowMs;
    requestCounts.set(key, { count: 1, resetTime });
    return { allowed: true, remaining: maxRequests - 1, resetTime };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  requestCounts.set(key, current);
  return { allowed: true, remaining: maxRequests - current.count, resetTime: current.resetTime };
}

// 🛡️ Aplicar headers de segurança
function addSecurityHeaders(response: NextResponse, rateLimitInfo?: { remaining: number; resetTime: number }) {
  const headers = process.env.NODE_ENV === 'production' ? PRODUCTION_SECURITY_HEADERS : SECURITY_HEADERS;
  
  // Aplicar headers de segurança
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  // Headers de rate limiting
  if (rateLimitInfo) {
    response.headers.set('X-RateLimit-Limit', '100');
    response.headers.set('X-RateLimit-Remaining', rateLimitInfo.remaining.toString());
    response.headers.set('X-RateLimit-Reset', Math.ceil(rateLimitInfo.resetTime / 1000).toString());
  }
  
  return response;
}

// 🚦 Middleware principal
export default clerkMiddleware(async (auth, request: NextRequest) => {
  const { pathname } = request.nextUrl;
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';
  
  // Rate limiting
  const rateLimitResult = checkRateLimit(ip);
  if (!rateLimitResult.allowed) {
    const response = new NextResponse('Rate limit exceeded', { status: 429 });
    return addSecurityHeaders(response, rateLimitResult);
  }
  
  // Log de CSP violations se recebidas
  if (pathname === '/api/csp-violation') {
    const body = await request.json().catch(() => null);
    if (body) {
      logCSPViolation('CSP Violation Report', body);
    }
  }
  
  // Permitir acesso a rotas públicas
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return addSecurityHeaders(NextResponse.next(), { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });
  }
  
  // Permitir acesso a assets estáticos
  if (
    pathname.startsWith('/_next/static') ||
    pathname.startsWith('/_next/image') ||
    pathname.startsWith('/favicon.ico') ||
    (pathname.includes('.') && !pathname.startsWith('/api'))
  ) {
    return addSecurityHeaders(NextResponse.next(), { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });
  }
  
  // Para rotas protegidas, verificar autenticação
  if (isProtectedRoute(request)) {
    const { userId } = await auth();
    
    if (!userId) {
      const response = NextResponse.redirect(new URL('/sign-in', request.url));
      return addSecurityHeaders(response, { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });
    }
  }
  
  // Aplicar headers de segurança a todas as respostas
  return addSecurityHeaders(NextResponse.next(), { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ]
}; 

