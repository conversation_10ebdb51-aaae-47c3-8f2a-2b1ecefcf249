const admin = require('firebase-admin');
const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function updateViewsManually() {
  const influencerId = '8Df9R9mZnIBLwvQQVrDQ';
  
  try {
    console.log('🔄 Atualizando campos de views manualmente...');
    
    // Buscar documento atual
    const doc = await db.collection('influencers').doc(influencerId).get();
    if (!doc.exists) {
      console.log('❌ Influenciador não encontrado');
      return;
    }
    
    const data = doc.data();
    console.log('📋 Dados atuais das plataformas:');
    console.log(JSON.stringify(data.platforms, null, 2));
    
    // Atualizar apenas os campos de views com valores de teste
    const updatedPlatforms = {
      ...data.platforms,
      instagram: {
        ...data.platforms.instagram,
        views: {
          storiesViews: 15000,
          reelsViews: 28000
        }
      },
      youtube: {
        ...data.platforms.youtube,
        views: {
          shortsViews: 45000,
          longFormViews: 120000
        }
      },
      tiktok: {
        ...data.platforms.tiktok,
        views: {
          videoViews: 85000
        }
      }
    };
    
    // Atualizar no Firestore
    await db.collection('influencers').doc(influencerId).update({
      platforms: updatedPlatforms,
      updatedAt: new Date()
    });
    
    console.log('✅ Campos de views atualizados com sucesso!');
    
    // Verificar resultado
    const updatedDoc = await db.collection('influencers').doc(influencerId).get();
    const updatedData = updatedDoc.data();
    
    console.log('\n🎉 Novos dados de views:');
    Object.keys(updatedData.platforms).forEach(platform => {
      const platformData = updatedData.platforms[platform];
      console.log(`\n📱 ${platform.toUpperCase()}:`);
      console.log(`  - Username: ${platformData.username}`);
      console.log(`  - Followers: ${platformData.followers}`);
      console.log(`  - Views: ${JSON.stringify(platformData.views, null, 4)}`);
    });
    
  } catch (error) {
    console.error('❌ Erro:', error);
  }
  
  process.exit(0);
}

updateViewsManually(); 
