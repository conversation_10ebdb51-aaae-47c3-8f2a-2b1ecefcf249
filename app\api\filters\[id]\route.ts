import { NextRequest, NextResponse } from "next/server";
import {
  deleteFilter,
  getFilterById,
  updateFilter
} from "@/lib/firebase";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "ID do filtro não fornecido" },
        { status: 400 }
      );
    }

    // Verificar se o filtro existe
    const filter = await getFilterById(id);
    if (!filter) {
      return NextResponse.json(
        { error: "Filtro não encontrado" },
        { status: 404 }
      );
    }
    
    // Remover o filtro
    await deleteFilter(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao excluir filtro:", error);
    return NextResponse.json(
      { error: "Erro ao excluir filtro" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: "ID do filtro não fornecido" },
        { status: 400 }
      );
    }

    // Validar dados mínimos
    if (!data.name) {
      return NextResponse.json(
        { error: "Nome do filtro é obrigatório" },
        { status: 400 }
      );
    }

    // Verificar se o filtro existe
    const existingFilter = await getFilterById(id);
    if (!existingFilter) {
      return NextResponse.json(
        { error: "Filtro não encontrado" },
        { status: 404 }
      );
    }

    // Criar objeto do filtro atualizado
    const filterData = {
      name: data.name,
      location: data.location || "",
      minFollowers: data.minFollowers || 0,
      maxFollowers: data.maxFollowers || 10000000,
      minRating: data.minRating || 0,
      verifiedOnly: data.verifiedOnly || false,
      availableOnly: data.availableOnly || false,
      platforms: data.platforms || {},
    };

    // Atualizar no Firestore
    const updatedFilter = await updateFilter(id, filterData);

    return NextResponse.json(updatedFilter);
  } catch (error) {
    console.error("Erro ao atualizar filtro:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar filtro" },
      { status: 500 }
    );
  }
}