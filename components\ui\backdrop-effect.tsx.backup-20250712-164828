"use client";

import { useEffect, useState } from 'react';

type StarProps = {
  key: number;
  size: number;
  left: string;
  animationDuration: string;
  animationDelay: string;
  color: string;
  opacity: number;
  twinkleDelay: string;
  twinkleDuration: string;
};

export function BackdropEffect() {
  // Estado para armazenar as estrelas geradas no cliente
  const [stars, setStars] = useState<StarProps[]>([]);
  const [isClient, setIsClient] = useState(false);

  // ✅ DEBUG: Log para verificar se o componente está sendo renderizado
  useEffect(() => {
    console.log('🌟 BackdropEffect: Componente montado');
    
    return () => {
      console.log('🌟 BackdropEffect: Componente desmontado');
    };
  }, []);

  // Adicionar estilos e configurar a detecção do cliente
  useEffect(() => {
    // Marcar que agora estamos no cliente
    setIsClient(true);
    console.log('🌟 BackdropEffect: Cliente detectado, adicionando estilos');
    
    // Adiciona estilos ao head do documento
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes stars {
        0% {
          transform: translateY(110vh) translateZ(0);
        }
        100% {
          transform: translateY(-10vh) translateZ(0);
        }
      }
      
      @keyframes twinkle {
        0%, 80%, 100% {
          opacity: 0.7;
          box-shadow: 0 0 0 #fff, 0 0 0 #fff;
        }
        95% {
          opacity: 1;
          box-shadow: 0 0 2px #fff, 0 0 4px #fff;
        }
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      // Limpa os estilos quando o componente é desmontado
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // Gerar as estrelas apenas quando estivermos no cliente
  useEffect(() => {
    if (!isClient) return;
    
    console.log('🌟 BackdropEffect: Gerando estrelas...');
    
    const generatedStars: StarProps[] = [];
    for (let i = 0; i < 100; i++) {
      const size = Math.random() < 0.3 ? 1 : Math.random() < 0.8 ? 2 : 3;
      const left = `${Math.random() * 100}vw`;
      const animationDuration = `${Math.random() * 150 + 50}s`;
      const animationDelay = `-${Math.random() * 1000}s`;
      const twinkleDelay = `-${Math.random() * 50}s`;
      const twinkleDuration = `${Math.random() * 50 + 5}s`;
      
      // Define cores para as estrelas
      let color;
      const colorType = Math.random();
      if (colorType < 0.3) {
        color = '#9810fa';
      } else if (colorType < 0.6) {
        color = '#ff0074';
      } else if (colorType < 0.8) {
        color = '#ffffff';
      } else {
        color = '#9a0dce';
      }
      
      const opacity = Math.random() * 0.5 + 0.5;
      
      generatedStars.push({
        key: i,
        size,
        left,
        animationDuration,
        animationDelay,
        color,
        opacity,
        twinkleDelay,
        twinkleDuration,
      });
    }
    
    setStars(generatedStars);
  
  }, [isClient]);

  

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
     
      
      {/* ✅ MELHORADO: Efeito de luz radiante mais visível */}
      <div 
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(ellipse at 50% 100%, rgba(0, 0, 0, 0) 0%, rgba(89, 0, 255, 0) 30%, rgba(98, 0, 255, 0) 50%, transparent 70%)',
          pointerEvents: 'none',
        }}
      />
      
      {/* ✅ NOVO: Gradiente adicional no topo */}
      <div 
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(ellipse at 50% 0%, rgba(2, 2, 3, 0) 0%, rgba(14, 5, 9, 0.05) 30%, transparent 50%)',
          pointerEvents: 'none',
        }}
      />
      
      {/* ✅ CORREÇÃO: Estrelas com paralaxe - renderizadas apenas no cliente */}
      <div className="stars absolute inset-0">
        {isClient && stars.map((star) => (
          <div 
            key={star.key} 
            className="absolute"
            style={{
              width: `${star.size}px`,
              height: `${star.size}px`,
              left: star.left,
              animation: `stars linear infinite`,
              animationDuration: star.animationDuration,
              animationDelay: star.animationDelay,
            }}
          >
            <div 
              style={{
                width: 'inherit',
                height: 'inherit',
                background: star.color,
                borderRadius: '100%',
                opacity: star.opacity,
                animation: `twinkle linear infinite`,
                animationDelay: star.twinkleDelay,
                animationDuration: star.twinkleDuration,
                transform: 'translateZ(0)',
                boxShadow: `0 0 ${star.size * 2}px ${star.color}20`,
              }}
            />
          </div>
        ))}
      </div>
      
      {/* ✅ NOVO: Partículas flutuantes adicionais */}
      <div className="absolute inset-0 opacity-30">
        {isClient && Array.from({ length: 20 }).map((_, i) => (
          <div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `twinkle ${Math.random() * 10 + 5}s linear infinite`,
              animationDelay: `${Math.random() * 10}s`,
            }}
          />
        ))}
      </div>
      
      {/* ✅ MELHORADO: Overlay de textura sutil */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)',
          backgroundSize: "20px 20px",
        }}
      />
      
     
    </div>
  );
}



