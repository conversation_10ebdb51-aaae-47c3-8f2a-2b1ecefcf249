// API ROUTE GRAPHQL SIMPLIFICADO
// Implementação GraphQL nativa para Vercel

import { graphql } from 'graphql';
import { makeExecutableSchema } from '@graphql-tools/schema';
import { NextApiRequest, NextApiResponse } from 'next';
import { typeDefs } from '../../lib/graphql/schema';
import { resolvers, createGraphQLContext, createGraphQLContextNoAuth } from '../../lib/graphql-resolvers';

// Logs removidos para limpar console

// Criar schema GraphQL executável
const schema = makeExecutableSchema({
  typeDefs,
  resolvers
});

// Configurar limite de body size para uploads
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb', // Aumentar para 50MB para suportar uploads de imagens
    },
  },
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Logs removidos para limpar console
  
  // Habilitar CORS
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.end();
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { query, variables, operationName } = req.body;
    
    if (!query) {
      res.status(400).json({ error: 'No query provided' });
      return;
    }

    // Logs removidos para limpar console
    
    // Criar contexto
    let context;
    try {
      context = await createGraphQLContext(req);
      // Logs removidos para limpar console
    } catch (error) {
      console.error('⚠️ [GraphQL] Erro na autenticação, usando contexto sem auth:', error);
      context = createGraphQLContextNoAuth(req);
    }

    // Executar query GraphQL
    const result = await graphql({
      schema,
      source: query,
      variableValues: variables,
      contextValue: context,
      rootValue: resolvers,
      operationName
    });
  
    // Log removido para limpar console
    
    res.status(200).json(result);
    
  } catch (error) {
    console.error('❌ [GraphQL] Erro na execução:', error);
    
    const errorMessage = process.env.NODE_ENV === 'production' 
      ? 'Erro interno do servidor' 
      : error instanceof Error ? error.message : 'Erro desconhecido';
      
    res.status(500).json({ 
      errors: [{ 
        message: errorMessage 
      }] 
    });
  }
} 

