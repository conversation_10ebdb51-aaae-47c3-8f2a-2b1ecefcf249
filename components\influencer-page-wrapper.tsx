'use client';

import { Suspense } from 'react';
import { useTranslations } from '@/hooks/use-translations';

interface InfluencerPageWrapperProps {
  children: React.ReactNode;
}

// Componente wrapper simples para testar traduções
export function InfluencerPageWrapper({ children }: InfluencerPageWrapperProps) {
  const { t, isLoading } = useTranslations();
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">
          {t('common.loading')}...
        </div>
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Carregando...</div>
      </div>
    }>
      {children}
    </Suspense>
  );
} 