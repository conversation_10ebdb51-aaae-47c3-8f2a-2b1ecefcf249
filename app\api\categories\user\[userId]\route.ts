import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/lib/firebase';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '');
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const params = await context.params;
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId || clerkUserId !== params.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Buscar categorias do usuário na coleção 'categories'
    const categoriesRef = db.collection('categories');
    const querySnapshot = await categoriesRef
      .where('userId', '==', params.userId)
      .orderBy('createdAt', 'desc')
      .get();
    
    const userCategories: Category[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      userCategories.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate().toISOString() : data.updatedAt
      } as Category);
    });
    
    return NextResponse.json(userCategories);
  } catch (error) {
    console.error('Erro ao buscar categorias:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const params = await context.params;
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId || clerkUserId !== params.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description } = body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json({ error: 'Nome da categoria é obrigatório' }, { status: 400 });
    }

    // Verificar se já existe uma categoria com o mesmo nome para este usuário
    const categoriesRef = db.collection('categories');
    const existingCategories = await categoriesRef
      .where('userId', '==', params.userId)
      .where('name', '==', name.trim())
      .get();

    if (!existingCategories.empty) {
      return NextResponse.json({ error: 'Já existe uma categoria com este nome' }, { status: 409 });
    }

    const now = new Date();
    
    // Preparar dados removendo campos undefined
    const categoryData: Record<string, any> = {
      name: name.trim(),
      slug: generateSlug(name.trim()),
      userId: params.userId,
      createdAt: now,
      updatedAt: now,
    };

    // Adicionar campo description apenas se tiver valor válido
    if (description && description.trim()) {
      categoryData.description = description.trim();
    }

    // Salvar na coleção 'categories'
    const docRef = await categoriesRef.add(categoryData);

    const newCategory: Category = {
      id: docRef.id,
      name: categoryData.name,
      slug: categoryData.slug,
      description: categoryData.description,
      userId: categoryData.userId,
      createdAt: now.toISOString(),
      updatedAt: now.toISOString()
    };

    return NextResponse.json(newCategory, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar categoria:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 