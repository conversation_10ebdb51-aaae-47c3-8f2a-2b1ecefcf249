'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth-v2';
import { useFirebaseAuth } from '@/hooks/use-clerk-auth';
import { ColumnDef } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Label } from '@/components/ui/label';
import { motion } from 'framer-motion';
import {
  ChevronDown,
  Save,
  Database,
  MapPin,
  Smartphone,
  Heart,
  TrendingUp,
  Play,
  DollarSign,
  MessageCircle,
  X
} from 'lucide-react';
// Importar DataTable e Colunas Completas
import { DataTable } from '@/components/ui/data-table';
import * as TableColumns from '@/components/campanhas/table-columns';
import { useDataTable } from '@/hooks/use-data-table';
import { createInfluencerColumns, Influenciador as InfluencerType } from '@/lib/table-columns/influencers';
import { formatters } from '@/lib/table-utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ArrowLeft,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Copy,
  Shield,
  List,
  Users,
  Tag,
  Calendar,
  ImageIcon,
  Settings,
  Download,
  Upload,
  ArrowRight,
  Share2,
  Check,
  User
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader } from '@/components/ui/loader';
import { AddProfileToListModal } from '@/components/ui/add-profile-to-list-modal';
import { ShareListDialog } from '@/components/ui/share-list-dialog';
import { SendToProposalModal } from '@/components/ui/send-to-proposal-modal';

// Tipos da lista
interface Lista {
  id: string;
  nome: string;
  tipoLista: 'estática' | 'dinâmica';
  tipoObjeto: 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
  tamanho: number;
  criadoPor: string;
  criadoPorNome: string;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  descricao?: string;
  tags?: string[];
  criterios?: {
    campo: string;
    operador: string;
    valor: string;
  }[];
}

// Tipos específicos para cada objeto
interface Influenciador {
  id: string;
  nome: string;
  usuario: string;
  seguidores: number;
  engajamento: number;
  nicho: string;
  localizacao: string;
  foto?: string;
  status: 'ativo' | 'inativo' | 'pendente';
}

interface Marca {
  id: string;
  nome: string;
  setor: string;
  orcamento: number;
  status: 'ativo' | 'inativo' | 'negociando';
  contato: string;
  logo?: string;
  funcionarios: number;
}

interface Campanha {
  id: string;
  nome: string;
  brand: string;
  budget: number;
  status: 'planejamento' | 'ativo' | 'finalizado' | 'cancelado';
  dataInicio: Date;
  dataFim: Date;
  alcance: number;
}

interface Conteudo {
  id: string;
  nome: string;
  formato: 'video' | 'imagem' | 'texto' | 'audio';
  tamanho: number;
  dataUpload: Date;
  engajamento?: number;
  autor: string;
  url?: string;
}

interface PageProps {
  params: Promise<{
    userId: string;
    listId: string;
  }>;
}

export default function ListaDetailPage({ params }: PageProps) {
  const { currentUser, isLoading, getToken } = useAuth();
  const { firebaseUser } = useFirebaseAuth();
  const router = useRouter();
  const [userId, setUserId] = useState<string | null>(null);
  const [listId, setListId] = useState<string | null>(null);

  // Resolver parâmetros
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setUserId(resolvedParams.userId);
      setListId(resolvedParams.listId);
      setDataLoaded(false); // Resetar flag quando parâmetros mudarem
    };
    resolveParams();
  }, [params]);

  // Verificação de acesso - memoizada para evitar loops infinitos
  const isOwnProfile = useMemo(() => {
    return currentUser?.id === userId;
  }, [currentUser?.id, userId]);

  const canAccess = isOwnProfile;

  // Estados da lista
  const [lista, setLista] = useState<Lista | null>(null);
  const [items, setItems] = useState<any[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false); // Flag para controlar se os dados já foram carregados

  // Estados para nova tabela (copiados da página de propostas)
  const [expandedRows, setExpandedRows] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);

  // Estado para seleção de influenciadores na tabela
  const [selectedListInfluencers, setSelectedListInfluencers] = useState<string[]>([]);

  // Estados para controle de visibilidade das colunas
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<{ [key: string]: boolean }>({
    // Inicializar algumas colunas como visíveis por padrão
    plataforma: true,
    seguidores: true,
    country: true,
    location: true,
    age: true,
    gender: true,
    category: true,
    verified: true,
    engagementRate: true,
    instagram_followers: true,
    tiktok_followers: true,
    youtube_followers: true,
  });
  const [columnOrder, setColumnOrder] = useState<string[]>([]);

  // Estados para presets personalizados
  const [columnPresets, setColumnPresets] = useState<any[]>([]);
  const [presetName, setPresetName] = useState('');
  const [showCreatePresetModal, setShowCreatePresetModal] = useState(false);
  const [isSavingPreset, setIsSavingPreset] = useState(false);
  const [activePreset, setActivePreset] = useState<string | null>(null);

  // Estado da aba ativa
  const [activeTab, setActiveTab] = useState<'perfis' | 'agregados'>('perfis');

  // Estado do modal de adicionar perfis
  const [showAddProfileModal, setShowAddProfileModal] = useState(false);

  // Estado do modal de compartilhamento
  const [showShareDialog, setShowShareDialog] = useState(false);

  // Estado para envio para propostas
  const [showSendToProposalModal, setShowSendToProposalModal] = useState(false);

  // Função para quando a ordem das colunas mudar
  const handleColumnOrderChange = (newOrder: string[]) => {
    // ✨ GARANTIR que a coluna 'select' (checkboxes) sempre fique no início
    const filteredOrder = newOrder.filter(col => col !== 'select');
    const finalOrder = ['select', ...filteredOrder];

    setColumnOrder(finalOrder);
    // saveColumnSettingsToDb(visibleColumns, finalOrder); // TODO: Implementar salvamento
  };

  // Hook para DataTable
  const { dataTableProps } = useDataTable<InfluencerType>({
    enableColumnOrdering: true,
    enableRowSelection: true,
    pageSize: 20
  });

  // 🎯 FUNÇÕES PARA GERENCIAMENTO DE COLUNAS E PRESETS
  const toggleColumnVisibility = (columnKey: string) => {
    // Não permitir edição das colunas fixas
    const fixedColumns = ['nomeInfluencer', 'acoes'];
    if (fixedColumns.includes(columnKey)) return;

    const newVisibleColumns = {
      ...visibleColumns,
      [columnKey]: !visibleColumns[columnKey]
    };
    setVisibleColumns(newVisibleColumns);

    // 🎯 LIMPAR PRESET ATIVO QUANDO CONFIGURAÇÃO É ALTERADA MANUALMENTE
    setActivePreset(null);

    saveColumnSettingsToDb(newVisibleColumns, columnOrder);
  };

  // Função para salvar configurações das colunas no banco
  const saveColumnSettingsToDb = async (visible: { [key: string]: boolean }, order: string[]) => {
    try {
      if (!currentUser?.id) return;

      const settingsData = {
        visibleColumns: visible,
        columnOrder: order,
        updatedAt: new Date()
      };

      console.log('💾 [FRONTEND] Salvando configurações das colunas:', {
        userId: currentUser.id,
        listId: listId,
        hasSettings: !!settingsData
      });

      // Por enquanto, salvar apenas no localStorage até a API estar pronta
      const storageKey = `columnSettings_${currentUser.id}_${listId}`;
      localStorage.setItem(storageKey, JSON.stringify(settingsData));

      console.log('✅ [FRONTEND] Configurações das colunas salvas no localStorage');

      // TODO: Implementar salvamento na API quando estiver pronta
      /*
      const response = await fetch('/api/lists/column-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          listId: listId,
          settings: settingsData,
          userId: currentUser.id
        })
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar configurações');
      }

      console.log('✅ [FRONTEND] Configurações das colunas salvas com sucesso');
      */
    } catch (error) {
      console.error('❌ [FRONTEND] Erro ao salvar configurações das colunas:', error);
      // Não mostrar erro para o usuário, apenas logar
    }
  };

  // Carregar presets de colunas
  const loadColumnPresets = async () => {
    try {
      if (!currentUser?.id) return;

      // Por enquanto, carregar do localStorage até a API estar pronta
      const storageKey = `columnPresets_${currentUser.id}`;
      const savedPresets = localStorage.getItem(storageKey);

      if (savedPresets) {
        const presets = JSON.parse(savedPresets);
        setColumnPresets(presets || []);
        console.log('✅ [PRESETS] Presets carregados do localStorage:', presets?.length || 0);
      }

      // TODO: Implementar carregamento da API quando estiver pronta
      /*
      const response = await fetch(`/api/lists/column-presets?userId=${currentUser.id}`);
      const data = await response.json();

      if (data.success) {
        setColumnPresets(data.presets || []);
        console.log('✅ [PRESETS] Presets carregados:', data.presets?.length || 0);
      }
      */
    } catch (error) {
      console.error('❌ [PRESETS] Erro ao carregar presets:', error);
    }
  };

  // Salvar preset de colunas
  const saveColumnPreset = async () => {
    if (!presetName.trim() || !currentUser?.id) return;

    try {
      setIsSavingPreset(true);

      // Criar novo preset
      const newPreset = {
        id: Date.now().toString(), // ID temporário
        name: presetName.trim(),
        visibleColumns,
        columnOrder,
        userId: currentUser.id,
        createdAt: new Date().toISOString()
      };

      // Salvar no localStorage por enquanto
      const storageKey = `columnPresets_${currentUser.id}`;
      const existingPresets = JSON.parse(localStorage.getItem(storageKey) || '[]');
      const updatedPresets = [newPreset, ...existingPresets];
      localStorage.setItem(storageKey, JSON.stringify(updatedPresets));

      toast.success(`Preset "${presetName}" foi salvo com sucesso!`);

      // ✅ ATUALIZAR IMEDIATAMENTE O DROPDOWN
      setColumnPresets(updatedPresets);
      setPresetName('');
      setShowCreatePresetModal(false);

      // TODO: Implementar salvamento na API quando estiver pronta
      /*
      const response = await fetch('/api/lists/column-presets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentUser.id,
          presetName: presetName.trim(),
          visibleColumns,
          columnOrder
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Preset "${presetName}" foi salvo com sucesso!`);
        setColumnPresets(prev => [data.preset, ...prev]);
        setPresetName('');
        setShowCreatePresetModal(false);
      } else {
        throw new Error(data.error);
      }
      */
    } catch (error) {
      console.error('❌ [PRESETS] Erro ao salvar preset:', error);
      toast.error('Erro ao salvar preset');
    } finally {
      setIsSavingPreset(false);
    }
  };

  // Aplicar preset de colunas
  const applyColumnPreset = (preset: any) => {
    setVisibleColumns(preset.visibleColumns);
    if (preset.columnOrder?.length > 0) {
      const finalOrder = ['select', ...preset.columnOrder.filter((col: string) => col !== 'select')];
      setColumnOrder(finalOrder);
    }

    // 🎯 DEFINIR PRESET COMO ATIVO
    setActivePreset(preset.id);

    // Salvar como configuração atual
    saveColumnSettingsToDb(preset.visibleColumns, preset.columnOrder || []);

    toast.success(`Configuração "${preset.name}" foi aplicada!`);
  };

  // Deletar preset de colunas
  const deleteColumnPreset = async (presetId: string) => {
    try {
      if (!currentUser?.id) return;

      // Remover do localStorage por enquanto
      const storageKey = `columnPresets_${currentUser.id}`;
      const existingPresets = JSON.parse(localStorage.getItem(storageKey) || '[]');
      const updatedPresets = existingPresets.filter((p: any) => p.id !== presetId);
      localStorage.setItem(storageKey, JSON.stringify(updatedPresets));

      setColumnPresets(updatedPresets);

      // Se o preset deletado estava ativo, limpar
      if (activePreset === presetId) {
        setActivePreset(null);
      }

      toast.success('Preset removido com sucesso!');

      // TODO: Implementar remoção na API quando estiver pronta
      /*
      const response = await fetch(`/api/lists/column-presets?presetId=${presetId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        setColumnPresets(prev => prev.filter(p => p.id !== presetId));

        if (activePreset === presetId) {
          setActivePreset(null);
        }

        toast.success('Preset removido com sucesso!');
      } else {
        throw new Error(data.error);
      }
      */
    } catch (error) {
      console.error('❌ [PRESETS] Erro ao deletar preset:', error);
      toast.error('Erro ao remover preset');
    }
  };

  // Função para carregar influenciadores via GraphQL (adaptada da página de propostas)
  const loadInfluencersViaGraphQL = async (influencerIds: string[]): Promise<{ [key: string]: any }> => {
    try {
      if (influencerIds.length === 0) return {};

      // 🔒 VERIFICAÇÃO CRÍTICA: Garantir que o usuário está autenticado
      if (!currentUser?.id) {
        console.warn('⚠️ [GraphQL] Usuário não autenticado - aguardando login...');
        return {};
      }

      console.log('🚀 [GraphQL] Carregando influenciadores via GraphQL...', {
        totalIds: influencerIds.length,
        userId: currentUser.id,
        listId: listId,
        timestamp: new Date().toISOString()
      });

      // 🔑 Obter token de autenticação do Clerk
      const idToken = await getToken();
      console.log('🔑 [GraphQL] Token obtido:', !!idToken);

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Adicionar token se disponível
      if (idToken) {
        headers['Authorization'] = `Bearer ${idToken}`;
      }

      // ✅ UMA ÚNICA QUERY para buscar TODOS os influenciadores
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          query: `
            query GetInfluencersByIds($ids: [ID!]!, $userId: ID!) {
              influencersByIds(ids: $ids, userId: $userId) {
                influencers {
                  id
                  name
                  email
                  phone
                  whatsapp
                  avatar
                  bio
                  totalFollowers
                  engagementRate
                  rating
                  isVerified
                  isAvailable
                  status
                  category
                  country
                  state
                  city
                  location
                  age
                  gender

                  instagramUsername
                  instagramFollowers
                  instagramEngagementRate

                  tiktokUsername
                  tiktokFollowers
                  tiktokEngagementRate

                  youtubeUsername
                  youtubeFollowers
                  youtubeEngagementRate

                  promotesTraders
                  responsibleName

                  createdAt
                  updatedAt
                }
                foundIds
                notFoundIds
                totalFound
                totalRequested
                processingTimeMs
              }
            }
          `,
          variables: {
            ids: influencerIds,
            userId: currentUser.id
          }
        })
      });

      if (!response.ok) {
        console.warn(`❌ [GraphQL] Erro na resposta: ${response.status} ${response.statusText}`);

        // Se for 404, a API GraphQL pode não estar disponível
        if (response.status === 404) {
          console.warn('⚠️ [GraphQL] API GraphQL não encontrada - usando fallback');
        }

        return {};
      }

      const result = await response.json();

      if (result.errors) {
        console.error(`❌ [GraphQL] Erros GraphQL detalhados:`, {
          errors: result.errors,
          query: 'GetInfluencersByIds',
          variables: { ids: influencerIds, userId: currentUser.id },
          response: result
        });

        // Tentar continuar mesmo com erros parciais
        if (!result.data?.influencersByIds) {
          return {};
        }
      }

      const influencersResult = result.data?.influencersByIds;

      if (!influencersResult) {
        console.warn(`⚠️ [GraphQL] Resultado vazio ou inválido`);
        return {};
      }

      // 🎯 PROCESSAR RESULTADOS - Uma única vez para todos
      const foundInfluencers = influencersResult.influencers || [];

      console.log(`📊 [GraphQL] Resultado da busca:`, {
        totalRequested: influencersResult.totalRequested,
        totalFound: influencersResult.totalFound,
        foundInfluencers: foundInfluencers.length,
        notFoundIds: influencersResult.notFoundIds?.length || 0,
        processingTimeMs: influencersResult.processingTimeMs
      });

      // Converter dados do GraphQL para o formato esperado (versão simplificada)
      const influencersData: { [key: string]: any } = {};

      for (const gqlInfluencer of foundInfluencers) {
        const influencer = {
          id: gqlInfluencer.id || '',
          nome: gqlInfluencer.name || 'Nome não informado',
          usuario: gqlInfluencer.instagramUsername ? `@${gqlInfluencer.instagramUsername}` : '@usuario',
          email: gqlInfluencer.email || '',
          telefone: gqlInfluencer.phone || '',
          whatsapp: gqlInfluencer.whatsapp || '',
          avatar: gqlInfluencer.avatar || '',
          bio: gqlInfluencer.bio || '',

          // Localização
          pais: gqlInfluencer.country || '',
          estado: gqlInfluencer.state || '',
          cidade: gqlInfluencer.city || '',
          localizacao: gqlInfluencer.location || '',

          // Dados demográficos
          idade: gqlInfluencer.age || null,
          genero: gqlInfluencer.gender || '',
          categoria: gqlInfluencer.category || '',

          // Métricas gerais
          seguidores: gqlInfluencer.totalFollowers || 0,
          engajamento: gqlInfluencer.engagementRate || 0,
          avaliacao: gqlInfluencer.rating || 0,
          verificado: gqlInfluencer.isVerified || false,
          disponivel: gqlInfluencer.isAvailable !== false,
          status: gqlInfluencer.status || 'ativo',

          // Redes sociais básicas
          instagram_followers: gqlInfluencer.instagramFollowers || 0,
          tiktok_followers: gqlInfluencer.tiktokFollowers || 0,
          youtube_followers: gqlInfluencer.youtubeFollowers || 0,

          // Profissional
          promotesTraders: gqlInfluencer.promotesTraders || false,
          responsibleInfo: gqlInfluencer.responsibleName || '',

          // Timestamps
          dataCriacao: gqlInfluencer.createdAt || '',
          ultimaAtualizacao: gqlInfluencer.updatedAt || ''
        };

        influencersData[gqlInfluencer.id] = influencer;
      }

      return influencersData;

    } catch (error) {
      console.error('❌ [GraphQL] Erro ao carregar influenciadores:', error);
      return {};
    }
  };

  // Função para carregar influenciadores da lista - memoizada para evitar loops
  const loadInfluencersFromList = useCallback(async () => {
    if (!firebaseUser || !listId) return;

    try {
      setLoadingItems(true);
      console.log('🔄 Carregando itens da lista:', listId);

      const token = await getToken();

      const response = await fetch(`/api/lists/${listId}/items`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Itens da lista carregados:', {
        data,
        itensCount: data.itens?.length || 0,
        total: data.total,
        itens: data.itens
      });

      // Buscar dados completos dos influenciadores via GraphQL
      const influencerIds = data.itens?.map((item: any) => item.itemId) || [];
      let influencersDataMap: { [key: string]: any } = {};

      console.log('🔍 IDs dos influenciadores encontrados:', influencerIds);

      if (influencerIds.length > 0) {
        try {
          // 🌐 Buscar dados dos influenciadores via GraphQL
          console.log('🚀 Buscando influenciadores via GraphQL...', influencerIds);
          influencersDataMap = await loadInfluencersViaGraphQL(influencerIds);
          console.log('✅ Dados GraphQL carregados:', Object.keys(influencersDataMap).length);

          // Se GraphQL não retornou dados, usar fallback
          if (Object.keys(influencersDataMap).length === 0) {
            console.log('⚠️ GraphQL não retornou dados, usando fallback...');
            // Criar dados básicos para cada ID
            influencerIds.forEach((id: string, index: number) => {
              influencersDataMap[id] = {
                id: id,
                nome: `Influenciador ${index + 1}`,
                usuario: `@influencer${index + 1}`,
                email: '',
                seguidores: Math.floor(Math.random() * 100000) + 10000,
                engajamento: Math.floor(Math.random() * 10) + 1,
                categoria: 'Lifestyle',
                status: 'ativo',
                avatar: '',
                pais: 'Brasil',
                verificado: false
              };
            });
          }
        } catch (error) {
          console.error('❌ Erro ao buscar dados dos influenciadores via GraphQL:', error);

          // Fallback completo em caso de erro
          console.log('🔄 Usando dados de fallback devido ao erro...');
          influencerIds.forEach((id: string, index: number) => {
            influencersDataMap[id] = {
              id: id,
              nome: `Influenciador ${index + 1}`,
              usuario: `@influencer${index + 1}`,
              email: '',
              seguidores: Math.floor(Math.random() * 100000) + 10000,
              engajamento: Math.floor(Math.random() * 10) + 1,
              categoria: 'Lifestyle',
              status: 'ativo',
              avatar: '',
              pais: 'Brasil',
              verificado: false
            };
          });
        }
      }

      // Mapear os itens para o formato esperado pela DataTable
      const influencersFormatted = data.itens?.map((item: any) => {
        // Encontrar os dados do influenciador no mapa GraphQL
        const influencerData = influencersDataMap[item.itemId];

        // Se não encontrou no GraphQL, usar dados básicos
        if (!influencerData) {
          return {
            id: item.itemId,
            nome: `Influenciador ${item.itemId}`,
            usuario: '@usuario',
            seguidores: 0,
            engajamento: 0,
            status: 'ativo',
            listItemId: item.id,
            dataAdicao: item.dataAdicao,
            posicao: item.posicao
          };
        }

        // Usar dados do GraphQL que já estão no formato correto
        return {
          ...influencerData,
          // Campos específicos da lista
          listItemId: item.id,
          dataAdicao: item.dataAdicao,
          posicao: item.posicao
        };
      }) || [];

      setItems(influencersFormatted);
      console.log('📊 Influenciadores formatados:', influencersFormatted.length);

    } catch (error) {
      console.error('❌ Erro ao carregar itens da lista:', error);
      toast.error('Erro ao carregar itens da lista');
      setItems([]);
    } finally {
      setLoadingItems(false);
    }
  }, [firebaseUser, listId]);

  // Função para carregar itens por tipo - memoizada para evitar loops
  const loadItemsByType = useCallback(async (tipoObjeto: string) => {
    switch (tipoObjeto) {
      case 'influenciadores':
        await loadInfluencersFromList();
        break;
      case 'marcas':
        setItems(getMockMarcas());
        break;
      case 'campanhas':
        setItems(getMockCampanhas());
        break;
      case 'conteúdo':
        setItems(getMockConteudo());
        break;
      default:
        setItems([]);
    }
  }, [loadInfluencersFromList]);

  // Função para carregar dados da lista - memoizada para evitar loops
  const loadListaData = useCallback(async () => {
    try {
      setLoadingItems(true);

      // Carregar dados reais da lista da API
      const token = await getToken();
      const response = await fetch(`/api/lists/${listId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar lista');
      }

      const listaData = await response.json();

      console.log('✅ Lista carregada:', listaData);
      setLista(listaData);
      setDataLoaded(true); // Marcar que os dados foram carregados

      // Carregar itens baseado no tipo - chamada direta para evitar dependência circular
      if (listaData.tipoObjeto === 'influenciadores') {
        await loadInfluencersFromList();
      } else {
        // Para outros tipos, usar dados mock
        switch (listaData.tipoObjeto) {
          case 'marcas':
            setItems(getMockMarcas());
            break;
          case 'campanhas':
            setItems(getMockCampanhas());
            break;
          case 'conteúdo':
            setItems(getMockConteudo());
            break;
          default:
            setItems([]);
        }
      }

    } catch (error) {
      console.error('Erro ao carregar lista:', error);
      toast.error('Erro ao carregar dados da lista');
    } finally {
      setLoadingItems(false);
    }
  }, [listId, currentUser]); // Removido loadItemsByType para evitar dependência circular

  // Carregar dados da lista quando componente monta ou dependências mudam
  useEffect(() => {
    console.log('🔄 useEffect loadListaData - Verificando condições:', {
      currentUser: !!currentUser,
      userId,
      listId,
      isOwnProfile,
      loadingItems,
      dataLoaded,
      listaId: lista?.id
    });

    // Verificações de segurança para evitar loops infinitos
    if (!currentUser || !userId || !listId || !isOwnProfile) {
      console.log('❌ useEffect loadListaData - Condições básicas não atendidas');
      return;
    }

    // Evitar múltiplas execuções desnecessárias
    if (loadingItems) {
      console.log('❌ useEffect loadListaData - Já está carregando');
      return;
    }

    // Evitar execução se já temos dados da lista
    if (dataLoaded && lista && lista.id === listId) {
      console.log('❌ useEffect loadListaData - Dados já carregados');
      return;
    }

    console.log('✅ useEffect loadListaData - Executando loadListaData');
    loadListaData();
  }, [currentUser?.id, userId, listId, isOwnProfile]); // Removido loadingItems e loadListaData para evitar loops

  // Carregar presets de colunas quando o usuário estiver disponível
  useEffect(() => {
    if (currentUser?.id) {
      loadColumnPresets();

      // Carregar configurações salvas das colunas
      const storageKey = `columnSettings_${currentUser.id}_${listId}`;
      const savedSettings = localStorage.getItem(storageKey);

      if (savedSettings) {
        try {
          const settings = JSON.parse(savedSettings);
          if (settings.visibleColumns) {
            setVisibleColumns(settings.visibleColumns);
          }
          if (settings.columnOrder) {
            setColumnOrder(settings.columnOrder);
          }
          console.log('✅ [FRONTEND] Configurações das colunas carregadas do localStorage');
        } catch (error) {
          console.error('❌ [FRONTEND] Erro ao carregar configurações do localStorage:', error);
        }
      }
    }
  }, [currentUser?.id, listId]);



  // Dados reais serão carregados da API

  const getMockMarcas = (): Marca[] => [
    {
      id: '1',
      nome: 'Nike Brasil',
      setor: 'Esporte',
      orcamento: 50000,
      status: 'ativo',
      contato: '<EMAIL>',
      funcionarios: 500
    },
    {
      id: '2',
      nome: 'Adidas',
      setor: 'Esporte',
      orcamento: 45000,
      status: 'negociando',
      contato: '<EMAIL>',
      funcionarios: 350
    }
  ];

  const getMockCampanhas = (): Campanha[] => [
    {
      id: '1',
      nome: 'Verão 2024',
      brand: 'Nike',
      budget: 25000,
      status: 'ativo',
      dataInicio: new Date('2024-01-01'),
      dataFim: new Date('2024-03-31'),
      alcance: 1500000
    },
    {
      id: '2',
      nome: 'Black Friday',
      brand: 'Adidas',
      budget: 40000,
      status: 'planejamento',
      dataInicio: new Date('2024-11-20'),
      dataFim: new Date('2024-11-30'),
      alcance: 2000000
    }
  ];

  const getMockConteudo = (): Conteudo[] => [
    {
      id: '1',
      nome: 'Look do Dia - Verão',
      formato: 'video',
      tamanho: 15.5,
      dataUpload: new Date('2024-06-15'),
      engajamento: 8.2,
      autor: 'Ana Silva'
    },
    {
      id: '2',
      nome: 'Tutorial Maquiagem',
      formato: 'video',
      tamanho: 22.1,
      dataUpload: new Date('2024-06-10'),
      engajamento: 6.7,
      autor: 'Julia Fashion'
    }
  ];

  // Funções de utilidade
  const getTipoObjetoIcon = (tipo: string) => {
    switch (tipo) {
      case 'influenciadores': return <Users className="h-4 w-4" />;
      case 'marcas': return <Tag className="h-4 w-4" />;
      case 'campanhas': return <Calendar className="h-4 w-4" />;
      case 'conteúdo': return <ImageIcon className="h-4 w-4" />;
      default: return <List className="h-4 w-4" />;
    }
  };



  // Função para lidar com perfis adicionados
  const handleProfilesAdded = () => {
    // Recarregar dados da lista
    if (lista) {
      loadItemsByType(lista.tipoObjeto);
    }
  };

  // Função para enviar influenciadores selecionados para proposta
  const handleSendToProposal = () => {
    if (selectedListInfluencers.length === 0) {
      toast.error('Selecione pelo menos um influenciador');
      return;
    }
    setShowSendToProposalModal(true);
  };

  // Função chamada após sucesso no envio para proposta
  const handleProposalSendSuccess = () => {
    setSelectedListInfluencers([]);
    setShowSendToProposalModal(false);
  };

  // Função para formatar influenciadores para o modal
  const getSelectedInfluencersForModal = () => {
    return selectedListInfluencers.map(itemId => {
      const item = items.find(inf => inf.id === itemId);
      return {
        id: item?.id || itemId,
        nome: item?.nome || 'Influenciador',
        foto: item?.foto,
        seguidores: item?.seguidores || 0,
        nicho: item?.nicho || 'Não informado'
      };
    });
  };

  // Adapter para converter itens da lista para o formato da DataTable (expandido para todas as colunas)
  const adaptListItemsForTable = useMemo(() => {
    if (!items) return [];

    return items.map(item => {
      return {
        id: item.id,
        nomeInfluencer: item.nome,
        nome: item.nome,
        usuario: item.usuario,
        username: item.usuario,

        // Dados básicos
        email: item.email,
        telefone: item.telefone,
        whatsapp: item.whatsapp,
        age: item.idade,
        gender: item.genero,
        category: item.categoria || item.nicho,
        verified: item.verificado || item.verified,
        bio: item.bio,
        isAvailable: item.disponivel,
        rating: item.avaliacao,

        // Localização
        pais: item.pais,
        country: item.pais,
        estado: item.estado,
        state: item.estado,
        cidade: item.cidade,
        city: item.cidade,
        localizacao: item.localizacao,
        location: item.localizacao,

        // Seguidores e engajamento
        seguidores: item.seguidores,
        totalFollowers: item.totalFollowers || item.seguidores,
        engajamento: item.engajamento,
        engagementRate: item.engajamento,

        // Redes sociais
        redesSociais: item.redesSociais,
        socialNetworks: item.socialNetworks,
        mainNetwork: item.redePrincipal,

        // Instagram
        instagramUsername: item.redesSociais?.instagram?.username,
        instagram_followers: item.redesSociais?.instagram?.seguidores,
        instagramFollowers: item.redesSociais?.instagram?.seguidores,
        instagramEngagementRate: item.redesSociais?.instagram?.engajamento,
        instagramStoriesViews: item.instagramStoriesViews,
        instagramReelsViews: item.instagramReelsViews,
        instagramAvgViews: item.instagramAvgViews,

        // TikTok
        tiktokUsername: item.redesSociais?.tiktok?.username,
        tiktok_followers: item.redesSociais?.tiktok?.seguidores,
        tiktokFollowers: item.redesSociais?.tiktok?.seguidores,
        tiktokEngagementRate: item.redesSociais?.tiktok?.engajamento,
        tiktok_views: item.tiktokViews,
        tiktokVideoViews: item.tiktokVideoViews,
        tiktokAvgViews: item.tiktokAvgViews,

        // YouTube
        youtubeUsername: item.redesSociais?.youtube?.username,
        youtube_followers: item.redesSociais?.youtube?.seguidores,
        youtubeFollowers: item.redesSociais?.youtube?.seguidores,
        youtubeSubscribers: item.redesSociais?.youtube?.seguidores,
        youtubeEngagementRate: item.redesSociais?.youtube?.engajamento,
        youtube_views: item.youtubeViews,
        youtubeShortsViews: item.youtubeShortsViews,
        youtubeLongFormViews: item.youtubeLongFormViews,
        youtubeAvgViews: item.youtubeAvgViews,

        // Facebook
        facebookUsername: item.redesSociais?.facebook?.username,
        facebookFollowers: item.redesSociais?.facebook?.seguidores,
        facebookEngagementRate: item.redesSociais?.facebook?.engajamento,
        facebookStoriesViews: item.facebookStoriesViews,
        facebookReelsViews: item.facebookReelsViews,
        facebookAvgViews: item.facebookAvgViews,

        // Outras redes
        twitchUsername: item.redesSociais?.twitch?.username,
        twitchFollowers: item.redesSociais?.twitch?.seguidores,
        twitchViews: item.twitchViews,
        kwaiUsername: item.redesSociais?.kwai?.username,
        kwaiFollowers: item.redesSociais?.kwai?.seguidores,
        kwaiViews: item.kwaiViews,

        // Profissional
        promotesTraders: item.divulgaTrader,
        responsibleName: item.responsavel,
        responsibleInfo: item.infoResponsavel,
        agencyName: item.agencia,
        contato: item.contato,

        // Outros campos
        nicho: item.nicho,
        avatar: item.avatar,
        status: item.status,
        createdAt: item.dataCriacao,
        updatedAt: item.ultimaAtualizacao,

        // Manter todos os campos originais
        ...item
      };
    });
  }, [items]);

  // Memoizar o objeto rowSelection para evitar loops infinitos
  const rowSelection = useMemo(() => {
    return selectedListInfluencers.reduce((acc, id) => {
      const index = adaptListItemsForTable.findIndex(row => row.id === id);
      if (index !== -1) acc[index] = true;
      return acc;
    }, {} as Record<string, boolean>);
  }, [selectedListInfluencers, adaptListItemsForTable]);

  // Função para lidar com mudanças na seleção
  const handleRowSelectionChange = useCallback((selectedRows: Record<string, boolean> | boolean[]) => {
    if (Array.isArray(selectedRows)) {
      // Se for array de booleans, converter para Record
      const selectedIds = selectedRows
        .map((selected, index) => selected ? adaptListItemsForTable[index]?.id : null)
        .filter(Boolean);
      setSelectedListInfluencers(selectedIds);
    } else {
      // Se for Record<string, boolean>
      const selectedIds = Object.keys(selectedRows)
        .filter(key => selectedRows[key])
        .map(index => adaptListItemsForTable[parseInt(index)]?.id)
        .filter(Boolean);
      setSelectedListInfluencers(selectedIds);
    }
  }, [adaptListItemsForTable]);

  // Items para exibição (DataTable gerencia filtros internamente)
  const filteredItems = adaptListItemsForTable;

  // 🆕 Função utilitária para formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Função para formatar números
  const formatNumber = (value: number) => {
    if (!value || value === 0) return '0';

    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }

    return value.toLocaleString('pt-BR');
  };

  // Função para obter dados do influenciador (adaptada para listas)
  const getInfluencerData = (influencerId: string) => {
    return items.find(item => item.id === influencerId);
  };

  // Constante para cor do texto (copiada da página de propostas)
  const TEXT_COLOR = 'hsl(var(--foreground))';

  // Colunas da tabela para dados de perfis da lista - EXATAMENTE as mesmas da página de propostas
  const allInfluencerColumns = useMemo(() => [
    {
      accessorKey: "nomeInfluencer",
      header: () => (
        <div style={{
          position: 'sticky',
          left: '48px', // Largura padrão da coluna checkbox
          zIndex: 20,
          background: 'hsl(var(--background))',
          borderLeft: '1px solid hsl(var(--border) / 0.3)',
          borderRight: '1px solid hsl(var(--border) / 0.3)',
          paddingRight: '1rem'
        }}>
          <span style={{ color: TEXT_COLOR }}>INFLUENCIADOR</span>
        </div>
      ),
      meta: {
        sticky: 'left',
        stickyOffset: 40, // Offset para ficar após a coluna de checkbox
        headerStyle: {
          position: 'sticky',
          left: '40px',
          zIndex: 20,
          background: 'hsl(var(--background))',
          borderRight: '1px solid hsl(var(--border) / 0.5)'
        },
        cellStyle: {
          position: 'sticky',
          left: '40px',
          zIndex: 10,
          background: 'hsl(var(--background))',
          borderRight: '1px solid hsl(var(--border) / 0.5)'
        }
      },
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        return (
          <div
            className="flex-1 min-w-0 flex items-center justify-end space-x-3 min-w-[180px]"
            style={{
              position: 'sticky',
              left: '48px', // Largura padrão da coluna checkbox
              zIndex: 10,
              background: 'hsl(var(--background))',
              borderLeft: '1px solid hsl(var(--border) / 0.3)',
              borderRight: '1px solid hsl(var(--border) / 0.3)',
              paddingRight: '1rem',
              marginLeft: '-1rem', // Compensar padding do TD
              paddingLeft: '1rem'
            }}
          >
            <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
              {influencerData?.avatar ? (
                <img
                  src={influencerData.avatar}
                  alt={perfil.nomeInfluencer}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Em caso de erro, mostrar fallback
                    e.currentTarget.style.display = 'none';
                    const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
              ) : null}
              <div
                className={`w-full h-full bg-gradient-to-r from-[#ff0074] to-[#9810fa] text-white flex items-center justify-center text-xs font-medium ${influencerData?.avatar ? 'hidden' : 'flex'}`}
                style={{ display: influencerData?.avatar ? 'none' : 'flex' }}
              >
                <User className="h-4 w-4 text-white" />
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-left">{perfil.nomeInfluencer}</div>
              <div className="text-xs text-left text-muted-foreground">{perfil.usuario}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "plataforma",
      header: () => <span style={{ color: TEXT_COLOR }}>PLATAFORMA</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        if (!influencerData) {
          return <span className="text-sm text-muted-foreground">Multi</span>;
        }

        const platforms = [];
        if (influencerData.redesSociais?.instagram || influencerData.instagram_followers) {
          platforms.push({
            type: 'instagram',
            icon: <span className="text-pink-500 text-lg">📷</span>,
            title: 'Instagram'
          });
        }
        if (influencerData.redesSociais?.youtube || influencerData.youtube_followers) {
          platforms.push({
            type: 'youtube',
            icon: <span className="text-red-500 text-lg">📺</span>,
            title: 'YouTube'
          });
        }
        if (influencerData.redesSociais?.tiktok || influencerData.tiktok_followers) {
          platforms.push({
            type: 'tiktok',
            icon: <span className="text-black text-lg">🎵</span>,
            title: 'TikTok'
          });
        }

        return (
          <div className="flex items-center justify-end gap-2">
            {platforms.map((platform, index) => (
              <div
                key={index}
                className="flex items-center justify-center w-6 h-6"
                title={platform.title}
              >
                {platform.icon}
              </div>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "seguidores",
      header: () => <span style={{ color: TEXT_COLOR }}>SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        if (!influencerData) {
          return (
            <div className="text-sm min-w-[120px] text-right">
              <div className="font-medium text-sm text-[#ec003f]">
                {formatNumber(perfil.seguidores)}
              </div>
              <div className="text-xs text-muted-foreground/70">total</div>
            </div>
          );
        }

        const maxFollowers = Math.max(
          influencerData.redesSociais?.instagram?.seguidores || influencerData.instagram_followers || 0,
          influencerData.redesSociais?.youtube?.seguidores || influencerData.youtube_followers || 0,
          influencerData.redesSociais?.tiktok?.seguidores || influencerData.tiktok_followers || 0
        );

        return (
          <div className="text-sm min-w-[120px] text-right">
            <div className="font-medium text-sm text-[#ec003f]">
              {formatNumber(maxFollowers)}
            </div>
            <div className="text-xs text-muted-foreground/70">maior rede</div>
          </div>
        );
      },
    },
    {
      accessorKey: "country",
      header: () => <span style={{ color: TEXT_COLOR }}>PAÍS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        return (
          <div className="min-w-[100px] text-right">
            <span className="text-sm dark:text-white" style={{ color: TEXT_COLOR }}>
              {influencerData?.country || influencerData?.pais || perfil.country || perfil.pais || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "location",
      header: () => <span style={{ color: TEXT_COLOR }}>LOCALIZAÇÃO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        const location = influencerData?.location ||
                        (influencerData?.cidade && influencerData?.estado ?
                         `${influencerData.cidade}/${influencerData.estado}` : null) ||
                        perfil.location || perfil.localizacao;

        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {location || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "age",
      header: () => <span style={{ color: TEXT_COLOR }}>IDADE</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        return (
          <div className="min-w-[80px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {influencerData?.age || influencerData?.idade || perfil.age || perfil.idade || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "gender",
      header: () => <span style={{ color: TEXT_COLOR }}>GÊNERO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        const gender = influencerData?.gender || influencerData?.genero || perfil.gender || perfil.genero;
        const genderText = gender === 'female' ? 'Feminino' :
                          gender === 'male' ? 'Masculino' :
                          gender === 'Feminino' ? 'Feminino' :
                          gender === 'Masculino' ? 'Masculino' :
                          gender || 'N/A';

        return (
          <div className="min-w-[100px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {genderText}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: () => <span style={{ color: TEXT_COLOR }}>CATEGORIA</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {influencerData?.category || influencerData?.categoria || perfil.category || perfil.categoria || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "verified",
      header: () => <span style={{ color: TEXT_COLOR }}>EXCLUSIVO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);

        const isVerified = influencerData?.isVerified || influencerData?.verificado || influencerData?.verified || perfil.verified || perfil.verificado;

        return (
          <div className="min-w-[100px] text-right">
            {isVerified ? (
              <div className="flex items-center justify-end gap-1">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-600">Sim</span>
              </div>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>Não</span>
            )}
          </div>
        );
      },
    },
    // Colunas de Redes Sociais - Instagram
    {
      accessorKey: "instagram_followers",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.instagram_followers || perfil.instagramFollowers ||
                     influencerData?.redesSociais?.instagram?.seguidores ||
                     influencerData?.instagram_followers;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-pink-600">
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "instagramStoriesViews",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM STORIES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.instagramStoriesViews;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "instagramReelsViews",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM REELS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.instagramReelsViews;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Redes Sociais - TikTok
    {
      accessorKey: "tiktok_followers",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.tiktok_followers || perfil.tiktokFollowers ||
                     influencerData?.redesSociais?.tiktok?.seguidores ||
                     influencerData?.tiktok_followers;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-black">
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "tiktok_views",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.tiktok_views || perfil.tiktokViews;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "tiktokVideoViews",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK VÍDEO VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.tiktokVideoViews;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Redes Sociais - YouTube
    {
      accessorKey: "youtube_followers",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.youtube_followers || perfil.youtubeFollowers || perfil.youtubeSubscribers ||
                     influencerData?.redesSociais?.youtube?.seguidores ||
                     influencerData?.youtube_followers;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-red-600">
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_views",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.youtube_views || perfil.youtubeViews;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "youtubeShortsViews",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE SHORTS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.youtubeShortsViews;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "youtubeLongFormViews",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE LONG FORM</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.youtubeLongFormViews;
        return (
          <div className="min-w-[150px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Engajamento
    {
      accessorKey: "engagementRate",
      header: () => <span style={{ color: TEXT_COLOR }}>ENGAJAMENTO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.engagementRate || perfil.engajamento ||
                     influencerData?.engagementRate || influencerData?.engajamento;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `${Number(value).toFixed(2)}%` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "promotesTraders",
      header: () => <span style={{ color: TEXT_COLOR }}>DIVULGA TRADER</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.promotesTraders || perfil.divulgaTrader ||
                     influencerData?.promotesTraders || influencerData?.divulgaTrader;
        return (
          <div className="min-w-[120px] text-right">
            {value ? (
              <div className="flex items-center justify-end gap-1">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-600">Sim</span>
              </div>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>Não</span>
            )}
          </div>
        );
      },
    },
    // Colunas de Redes Sociais - Facebook
    {
      accessorKey: "facebookFollowers",
      header: () => <span style={{ color: TEXT_COLOR }}>FACEBOOK SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.facebookFollowers;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-blue-600">
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "facebookStoriesViews",
      header: () => <span style={{ color: TEXT_COLOR }}>FACEBOOK STORIES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.facebookStoriesViews;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "facebookReelsViews",
      header: () => <span style={{ color: TEXT_COLOR }}>FACEBOOK REELS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.facebookReelsViews;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Redes Sociais - Twitch
    {
      accessorKey: "twitchFollowers",
      header: () => <span style={{ color: TEXT_COLOR }}>TWITCH SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.twitchFollowers;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-purple-600">
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "twitchViews",
      header: () => <span style={{ color: TEXT_COLOR }}>TWITCH VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.twitchViews;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Redes Sociais - Kwai
    {
      accessorKey: "kwaiFollowers",
      header: () => <span style={{ color: TEXT_COLOR }}>KWAI SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.kwaiFollowers;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-orange-600">
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "kwaiViews",
      header: () => <span style={{ color: TEXT_COLOR }}>KWAI VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const value = perfil.kwaiViews;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value ? formatNumber(value) : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Serviços/Preços
    {
      accessorKey: "instagram_story",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM STORY</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.instagram_story || influencerData?.instagram_story;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "instagram_reel",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM REEL</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.instagram_reel || influencerData?.instagram_reel;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_insertion",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE INSERÇÃO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.youtube_insertion || influencerData?.youtube_insertion;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_video",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE DEDICADO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.youtube_video || influencerData?.youtube_video;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_shorts",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE SHORTS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.youtube_shorts || influencerData?.youtube_shorts;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "tiktok_video",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK VÍDEO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.tiktok_video || influencerData?.tiktok_video;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm font-medium text-green-600">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "total_budget",
      header: () => <span style={{ color: TEXT_COLOR }}>TOTAL</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.total_budget || influencerData?.total_budget;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm font-bold text-green-700">
              {value ? `R$ ${Number(value).toLocaleString('pt-BR')}` : 'N/A'}
            </span>
          </div>
        );
      },
    },
    // Colunas de Contato
    {
      accessorKey: "responsibleInfo",
      header: () => <span style={{ color: TEXT_COLOR }}>INFO RESPONSÁVEL</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.responsibleInfo || perfil.infoResponsavel ||
                     influencerData?.responsibleInfo || influencerData?.responsavel;
        return (
          <div className="min-w-[140px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "contato",
      header: () => <span style={{ color: TEXT_COLOR }}>CONTATO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.id);
        const value = perfil.contato || influencerData?.contato ||
                     influencerData?.email || influencerData?.telefone;
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {value || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "acoes",
      header: () => <span style={{ color: TEXT_COLOR }}>AÇÕES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;

        return (
          <div className="flex items-center gap-2 text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <span className="sr-only">Abrir menu</span>
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => console.log('Ver perfil:', perfil)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Ver Perfil
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log('Editar:', perfil)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log('Remover da lista:', perfil)}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remover da Lista
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ], []);

  // Labels das colunas para o dialog de configuração (copiado exatamente da página de propostas)
  const columnLabels: { [key: string]: string } = {
    // Dados Básicos
    nomeInfluencer: 'Influenciador',
    age: 'Idade',
    gender: 'Gênero',
    category: 'Categoria',
    verified: 'Exclusivo',
    bio: 'Biografia',
    email: 'Email',
    phone: 'Telefone',
    whatsapp: 'WhatsApp',
    isAvailable: 'Disponível',
    rating: 'Avaliação',

    // Localização
    country: 'País',
    state: 'Estado',
    city: 'Cidade',
    location: 'Localização',

    // Plataformas
    plataforma: 'Plataforma',
    mainNetwork: 'Rede Principal',

    // Seguidores Gerais
    seguidores: 'Seguidores',
    totalFollowers: 'Total Seguidores',
    engagementRate: 'Engajamento',

    // Instagram
    instagramUsername: 'Instagram Username',
    instagram_followers: 'Instagram Seguidores',
    instagramFollowers: 'Instagram Seguidores',
    instagramEngagementRate: 'Instagram Engajamento',
    instagramStoriesViews: 'Instagram Stories Views',
    instagramReelsViews: 'Instagram Reels Views',
    instagramAvgViews: 'Instagram Média Views',

    // TikTok
    tiktokUsername: 'TikTok Username',
    tiktok_followers: 'TikTok Seguidores',
    tiktokFollowers: 'TikTok Seguidores',
    tiktokEngagementRate: 'TikTok Engajamento',
    tiktok_views: 'TikTok Views',
    tiktokVideoViews: 'TikTok Video Views',
    tiktokAvgViews: 'TikTok Média Views',

    // YouTube
    youtubeUsername: 'YouTube Username',
    youtube_followers: 'YouTube Seguidores',
    youtubeFollowers: 'YouTube Seguidores',
    youtubeSubscribers: 'YouTube Inscritos',
    youtubeEngagementRate: 'YouTube Engajamento',
    youtube_views: 'YouTube Views',
    youtubeShortsViews: 'YouTube Shorts Views',
    youtubeLongFormViews: 'YouTube Long Form Views',
    youtubeAvgViews: 'YouTube Média Views',

    // Facebook
    facebookUsername: 'Facebook Username',
    facebookFollowers: 'Facebook Seguidores',
    facebookEngagementRate: 'Facebook Engajamento',
    facebookStoriesViews: 'Facebook Stories Views',
    facebookReelsViews: 'Facebook Reels Views',
    facebookAvgViews: 'Facebook Média Views',

    // Twitch
    twitchUsername: 'Twitch Username',
    twitchFollowers: 'Twitch Seguidores',
    twitchEngagementRate: 'Twitch Engajamento',
    twitchViews: 'Twitch Views',

    // Kwai
    kwaiUsername: 'Kwai Username',
    kwaiFollowers: 'Kwai Seguidores',
    kwaiEngagementRate: 'Kwai Engajamento',
    kwaiViews: 'Kwai Views',

    // Profissional
    promotesTraders: 'Divulga Trader',
    responsibleName: 'Responsável',
    responsibleInfo: 'Info Responsável',
    agencyName: 'Agência',
    responsibleCapturer: 'Captador',
    contato: 'Contato',

    // Serviços/Preços
    instagram_story: 'Stories',
    instagram_reel: 'Reels',
    youtube_insertion: 'Inserção',
    youtube_video: 'Dedicado',
    youtube_shorts: 'Shorts',
    tiktok_video: 'Vídeo',
    facebook_post: 'Facebook Post',
    twitch_stream: 'Twitch Stream',
    kwai_video: 'Kwai Vídeo',
    total_budget: 'Total',

    // Status e Ações
    status: 'Status',
    createdAt: 'Data Criação',
    updatedAt: 'Última Atualização',
    acoes: 'Ações',
  };

  // Colunas filtradas baseadas na visibilidade
  const influencerColumns = useMemo(() => {
    return allInfluencerColumns
      .filter(column => {
        const key = column.accessorKey;
        // Colunas fixas que sempre aparecem
        const fixedColumns = ['nomeInfluencer', 'acoes'];
        return fixedColumns.includes(key) || visibleColumns[key] !== false;
      });
  }, [allInfluencerColumns, visibleColumns]);

  // Loading states
  if (isLoading || !userId || !listId) {
    return <Loader isLoading={true} message="" showLogo={true} />;
  }

  if (!currentUser || !canAccess) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            Você só pode acessar suas próprias listas.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex  h-screen">
      {/* Sidebar com detalhes da lista */}
      <div className="w-96 border-r bg-background dark:bg-[#080210]">
        <div className="p-6 space-y-6">
          {/* Header com botão voltar */}
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Detalhes da Lista</h1>
              <p className="text-sm text-muted-foreground">
                Gerencie os itens da sua lista
              </p>
            </div>
          </div>

          {lista && (
            <>
              {/* Perfil da Lista */}
              <Card>
                <CardContent className="p-6">
                  <div className="text-center space-y-4">
                    {/* Avatar da lista */}
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-[#ec003f] to-[#9810fa] flex items-center justify-center">
                      {lista.tipoObjeto === 'influenciadores' && <Users className="h-8 w-8 text-white" />}
                      {lista.tipoObjeto === 'marcas' && <Tag className="h-8 w-8 text-white" />}
                      {lista.tipoObjeto === 'campanhas' && <Calendar className="h-8 w-8 text-white" />}
                      {lista.tipoObjeto === 'conteúdo' && <ImageIcon className="h-8 w-8 text-white" />}
                      {!['influenciadores', 'marcas', 'campanhas', 'conteúdo'].includes(lista.tipoObjeto) && <List className="h-8 w-8 text-white" />}
                    </div>

                    {/* Nome da lista */}
                    <div>
                      <h2 className="text-xl text-gray-700 font-bold dark:text-white">{lista.nome}</h2>
                    </div>

                    {/* Quantidade na lista */}
                    <div>
                      <div className="text-3xl font-bold text-[#ec003f]">{filteredItems.length}</div>
                      <div className="text-sm text-muted-foreground">
                        {lista.tipoObjeto} na lista
                      </div>
                    </div>

                    {/* Tipo da lista */}
                    <div>
                      <Badge className={lista.tipoLista === 'estática' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}>
                        Lista {lista.tipoLista}
                      </Badge>
                    </div>

                    {/* Ícones de ação */}
                    <div className="flex justify-center gap-4 pt-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-10 w-10 rounded-full hover:bg-[#ff0074] hover:text-white transition-colors"
                        onClick={() => setShowShareDialog(true)}
                        title="Compartilhar Lista"
                      >
                        <Share2 className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-10 w-10 rounded-full hover:bg-muted"
                        onClick={() => toast.success('Funcionalidade de exportar em breve!')}
                        title="Exportar Lista"
                      >
                        <Download className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-10 w-10 rounded-full hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => {
                          if (confirm(`Tem certeza que deseja deletar a lista "${lista.nome}"?`)) {
                            toast.success('Lista deletada com sucesso!');
                            router.back();
                          }
                        }}
                        title="Deletar Lista"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              
            </>
          )}
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-hidden">
        <div className="h-full p-6 space-y-6 overflow-y-auto">
          {/* Navegação por Abas */}
          <div className="border-b border-border">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveTab('perfis')}
                className={cn(
                  "pb-3 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'perfis'
                    ? "border-[#ec003f] text-[#ec003f]"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Perfis
                <span className="ml-2 bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs">
                  {filteredItems.length}
                </span>
              </button>
              
              <button
                onClick={() => setActiveTab('agregados')}
                className={cn(
                  "pb-3 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'agregados'
                    ? "border-[#ec003f] text-[#ec003f]"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Dados agregados
              </button>
            </div>
          </div>

          {/* Conteúdo das Abas */}
          {activeTab === 'perfis' && (
            <>
              {/* DataTable de Perfis */}
              {loadingItems ? (
                <div className="flex items-center justify-center py-12">
                  <Loader isLoading={true} message="" showLogo={true} />
                </div>
              ) : filteredItems.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    {getTipoObjetoIcon(lista?.tipoObjeto || '')}
                    <h3 className="text-lg font-medium mt-4">Nenhum item encontrado</h3>
                    <p className="text-muted-foreground mb-4">
                      Esta lista ainda não possui itens.
                    </p>
                    <Button
                      className="bg-[#ec003f] hover:bg-[#d10037]"
                      onClick={() => setShowAddProfileModal(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Adicionar Primeiro Item
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                // View de Tabela separando Toolbar da Tabela (estrutura exata da página de propostas)
                <div className="h-full flex flex-col">
                  {/* Toolbar fixo (sem overflow) */}
                  <div className="flex items-center justify-between p-4 shrink-0">
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Buscar influenciadores..."
                        className="w-80"
                      />
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.location.reload()}
                        className="border dark:border-[#1c1627] hover:bg-gray-50"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Atualizar
                      </Button>

                      <Button
                        className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white hover:bg-[#ec003f]/90"
                        size="sm"
                        onClick={() => setShowAddProfileModal(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Adicionar perfis
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            size="sm"
                            className="bg-[#9810fa] text-white hover:bg-[#9810fa]/90"
                            disabled={selectedListInfluencers.length === 0}
                          >
                            Ações {selectedListInfluencers.length > 0 && `(${selectedListInfluencers.length})`}
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={handleSendToProposal}
                            disabled={selectedListInfluencers.length === 0}
                          >
                            <ArrowRight className="h-4 w-4 mr-2 text-blue-600" />
                            Enviar para Proposta ({selectedListInfluencers.length})
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              if (confirm(`Tem certeza que deseja remover ${selectedListInfluencers.length} influenciador(es) selecionado(s) desta lista?`)) {
                                selectedListInfluencers.forEach(itemId => {
                                  setItems(prev => prev.filter(item => item.id !== itemId));
                                });
                                setSelectedListInfluencers([]);
                                toast.success(`${selectedListInfluencers.length} influenciador(es) removido(s) da lista!`);
                              }
                            }}
                            className="text-red-600"
                            disabled={selectedListInfluencers.length === 0}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Remover Selecionados ({selectedListInfluencers.length})
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowColumnDialog(true)}
                        className="border dark:border-[#1c1627] hover:bg-gray-50"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Colunas
                      </Button>
                    </div>
                  </div>

                  {/* Área da tabela com overflow horizontal */}
                  <div className="flex-1 overflow-x-auto">
                    <DataTable
                      columns={influencerColumns}
                      data={filteredItems}
                      searchKey="nomeInfluencer"
                      searchPlaceholder="Buscar influenciadores..."
                      className="w-full h-full"
                      enableRowSelection={true}
                      enableColumnOrdering={true}
                      pageSize={20}
                    />
                  </div>
                </div>
              )}
            </>
          )}

          {/* Aba Dados Agregados */}
          {activeTab === 'agregados' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">Dados Agregados</h3>
                <p className="text-muted-foreground">
                  Esta seção será implementada em breve.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal de Adicionar Perfis */}
      <AddProfileToListModal
        isOpen={showAddProfileModal && !!lista}
        onClose={() => setShowAddProfileModal(false)}
        listId={lista?.id || ''}
        onSuccess={handleProfilesAdded}
      />

      {/* Modal de Compartilhamento */}
      <ShareListDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        listName={lista?.nome || ''}
        listId={lista?.id || ''}
        listType={lista?.tipoObjeto || ''}
        itemCount={filteredItems.length}
      />

      {/* Modal de Envio para Proposta */}
      <SendToProposalModal
        isOpen={showSendToProposalModal}
        onClose={() => setShowSendToProposalModal(false)}
        selectedInfluencers={getSelectedInfluencersForModal()}
        onSuccess={handleProposalSendSuccess}
      />

      {/* Dialog: Editar Colunas da Tabela (implementação exata da página de propostas) */}
      <Dialog
        open={showColumnDialog}
        modal={true}
        onOpenChange={setShowColumnDialog}
      >
        <DialogContent className="max-w-4xl p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Editar colunas da tabela</DialogTitle>
          </DialogHeader>

          <div className="bg-[#9810fa] text-white p-6 rounded-t-lg">
            <h2 className="text-xl font-semibold">Editar Colunas da Tabela</h2>
          </div>

          <div className="p-4 xl:p-6 space-y-3 xl:space-y-4">
            <p className="text-sm text-muted-foreground mb-3 xl:mb-4">
              Selecione quais colunas você deseja exibir na tabela de influenciadores:
            </p>

            {/* 🎯 SEÇÃO DE PRESETS PERSONALIZADOS - SIMPLIFICADA */}
            <div className="flex items-center justify-between mb-4 p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-3">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowCreatePresetModal(true)}
                  className="h-8 px-3 text-xs"
                  title="Salvar configuração atual como preset"
                >
                  <Save className="h-3 w-3" />
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-8 px-3 text-xs"
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      {activePreset ? (
                        <>
                          {columnPresets.find(p => p.id === activePreset)?.name || 'Preset Ativo'}
                          <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                        </>
                      ) : (
                        `Presets (${columnPresets.length})`
                      )}
                      <ChevronDown className="h-3 w-3 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56">
                    {columnPresets.length > 0 ? (
                      columnPresets.map((preset) => (
                        <div key={preset.id} className="flex items-center">
                          <DropdownMenuItem
                            onClick={() => applyColumnPreset(preset)}
                            className={`flex-1 cursor-pointer ${
                              activePreset === preset.id
                                ? 'bg-green-50 text-green-700 font-medium'
                                : ''
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              {activePreset === preset.id && (
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              )}
                              {preset.name}
                            </div>
                          </DropdownMenuItem>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteColumnPreset(preset.id);
                            }}
                            className="h-6 w-6 p-0 text-red-500 hover:bg-red-50 hover:text-red-600 mr-2"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))
                    ) : (
                      <DropdownMenuItem disabled className="text-muted-foreground text-xs">
                        Nenhum preset salvo
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <p className="text-xs text-muted-foreground">
                Salve e aplique configurações personalizadas
              </p>
            </div>

            <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-2"
                 style={{
                   scrollbarWidth: 'thin',
                   scrollbarColor: '#5600ce30 transparent'
                 }}
            >
              {/* Dados Básicos */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <Database className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Dados Básicos</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['category', 'verified'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'category' ? 'Categoria' :
                             key === 'verified' ? 'Exclusivo' :
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Localização */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <MapPin className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Localização</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['location', 'age', 'gender'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'location' ? 'Localização' :
                             key === 'age' ? 'Idade' :
                             key === 'gender' ? 'Gênero' :
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Plataformas */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <Smartphone className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Plataformas</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['plataforma', 'mainNetwork', 'seguidores'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'plataforma' ? 'Plataforma' :
                             key === 'mainNetwork' ? 'Rede Principal' :
                             key === 'seguidores' ? 'Seguidores' :
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Seguidores */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <Heart className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Seguidores</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['instagram_followers', 'youtube_followers', 'tiktok_followers'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'instagram_followers' ? 'Instagram Seguidores' :
                             key === 'youtube_followers' ? 'YouTube Seguidores' :
                             key === 'tiktok_followers' ? 'TikTok Seguidores' :
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Métricas de Engajamento */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Engajamento</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['engagementRate', 'promotesTraders'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'engagementRate' ? 'Engajamento' :
                             key === 'promotesTraders' ? 'Divulga Trader' :
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Views */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#270038]/10 flex items-center justify-center">
                      <Play className="h-4 w-4 text-[#270038]" />
                    </div>
                    <span className="text-sm font-semibold text-[#270038]">Views</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['tiktok_views', 'youtube_views', 'instagramStoriesViews', 'instagramReelsViews', 'tiktokVideoViews', 'youtubeShortsViews', 'youtubeLongFormViews', 'facebookStoriesViews', 'facebookReelsViews', 'twitchViews', 'kwaiViews'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                        {columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Serviços/Preços */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <DollarSign className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Serviços</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['instagram_story', 'instagram_reel', 'youtube_insertion', 'youtube_video', 'youtube_shorts', 'tiktok_video', 'total_budget'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                        {columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Contato */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <MessageCircle className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Contato</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['responsibleInfo', 'contato'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'responsibleInfo' ? 'Info Responsável' :
                             key === 'contato' ? 'Contato' :
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>
            </div>

            {/* Presets Rápidos */}
            <div className="border-t pt-4">
              <p className="text-sm font-medium mb-3">Presets Rápidos:</p>
              <div className="flex gap-1 xl:gap-1.5 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const allowedColumns = [
                      'category', 'verified', 'status', 'location', 'age', 'gender',
                      'plataforma', 'mainNetwork', 'seguidores', 'instagram_followers',
                      'youtube_followers', 'tiktok_followers', 'engagementRate', 'promotesTraders',
                      'tiktok_views', 'youtube_views', 'instagramStoriesViews', 'instagramReelsViews',
                      'tiktokVideoViews', 'youtubeShortsViews', 'youtubeLongFormViews', 'facebookStoriesViews',
                      'facebookReelsViews', 'twitchViews', 'kwaiViews', 'responsibleInfo', 'contato',
                      'instagram_story', 'instagram_reel', 'youtube_insertion', 'youtube_video',
                      'youtube_shorts', 'tiktok_video', 'total_budget'
                    ];
                    const allVisible = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = allowedColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(allVisible);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(allVisible, columnOrder);
                  }}
                >
                  Todas
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const essentialColumns = ['category', 'verified'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = essentialColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Essenciais
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#5600ce] border-[#5600ce]/30 hover:bg-[#5600ce]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const basicColumns = ['location', 'age', 'gender', 'category', 'verified'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = basicColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Básicos
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#ff0074] border-[#ff0074]/30 hover:bg-[#ff0074]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const socialColumns = ['instagram_followers', 'youtube_followers', 'tiktok_followers', 'engagementRate'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = socialColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Social
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#270038] dark:text-white border hover:bg-[#270038]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const viewsColumns = ['tiktok_views', 'youtube_views', 'instagramStoriesViews', 'instagramReelsViews', 'tiktokVideoViews', 'youtubeShortsViews', 'youtubeLongFormViews', 'facebookStoriesViews', 'facebookReelsViews', 'twitchViews', 'kwaiViews'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = viewsColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Views
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#5600ce] border-[#5600ce]/30 hover:bg-[#5600ce]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const serviceColumns = ['instagram_story', 'instagram_reel', 'youtube_insertion', 'youtube_video', 'youtube_shorts', 'tiktok_video', 'total_budget'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = serviceColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Serviços
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#ff0074] border-[#ff0074]/30 hover:bg-[#ff0074]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const contactColumns = ['responsibleInfo', 'contato'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = contactColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Contato
                </Button>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 xl:gap-3 p-4 xl:p-6 border-t bg-muted/30">
            <Button
              variant="outline"
              size="sm"
              className="h-8 xl:h-9 px-3 xl:px-4 text-sm"
              onClick={() => setShowColumnDialog(false)}
            >
              Cancelar
            </Button>
            <Button
              size="sm"
              className="bg-[#ec003f] hover:bg-[#ec003f]/90 text-white h-8 xl:h-9 px-3 xl:px-4 text-sm"
              onClick={() => setShowColumnDialog(false)}
            >
              Aplicar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog: Criar Preset Personalizado (implementação exata da página de propostas) */}
      <Dialog
        open={showCreatePresetModal}
        modal={true}
        onOpenChange={setShowCreatePresetModal}
      >
        <DialogContent className="max-w-md p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Criar preset personalizado</DialogTitle>
          </DialogHeader>

          <div className="bg-gradient-to-r from-[#ff0074] to-[#5600ce] text-white p-6 rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <Save className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-lg font-semibold">Criar Preset</h2>
                <p className="text-sm opacity-90">Salve a configuração atual das colunas</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="preset-name" className="text-sm font-medium">
                Nome do Preset
              </Label>
              <Input
                id="preset-name"
                type="text"
                value={presetName}
                onChange={(e) => setPresetName(e.target.value)}
                placeholder="Ex: Análise Completa, Setup Marketing..."
                className="w-full"
                autoFocus
                disabled={isSavingPreset}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && presetName.trim() && !isSavingPreset) {
                    e.preventDefault();
                    saveColumnPreset();
                  }
                }}
              />
            </div>

            <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
              <p className="font-medium mb-1">💡 Dica:</p>
              <p>O preset salvará a configuração atual de colunas visíveis. Você poderá aplicá-lo rapidamente depois.</p>
            </div>
          </div>

          <div className="flex items-center justify-end gap-3 p-6 border-t bg-muted/30">
            <Button
              variant="outline"
              size="sm"
              className="h-9 px-4"
              onClick={() => {
                setShowCreatePresetModal(false);
                setPresetName('');
              }}
              disabled={isSavingPreset}
            >
              Cancelar
            </Button>
            <Button
              size="sm"
              className="bg-gradient-to-r from-[#ff0074] to-[#5600ce] hover:opacity-90 text-white h-9 px-4"
              onClick={saveColumnPreset}
              disabled={!presetName.trim() || isSavingPreset}
            >
              {isSavingPreset ? (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin"></div>
                  Salvando...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-3 w-3" />
                  Salvar Preset
                </div>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
