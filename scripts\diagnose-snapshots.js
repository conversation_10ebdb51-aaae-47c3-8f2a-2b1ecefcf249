const admin = require('firebase-admin');

// Configurar Firebase Admin se ainda não foi configurado
if (!admin.apps.length) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

async function diagnosticSnapshots(proposalId) {
  console.log(`🔍 [DEBUG] Iniciando diagnóstico para proposalId: ${proposalId}`);
  
  try {
    // STEP 1: Verificar se existem proposal_sharings
    console.log(`🔍 [DEBUG] STEP 1: Verificando proposal_sharings...`);
    const proposalSharingsQuery = db.collection('proposal_sharings')
      .where('proposalId', '==', proposalId);
    
    const sharingsSnapshot = await proposalSharingsQuery.get();
    
    console.log(`📋 [DEBUG] Encontrados ${sharingsSnapshot.size} proposal_sharings`);
    
    if (sharingsSnapshot.empty) {
      console.log(`❌ [DEBUG] PROBLEMA: Nenhum proposal_sharing encontrado para ${proposalId}`);
      console.log(`💡 [DEBUG] SOLUÇÃO: Execute POST /api/proposals/${proposalId}/sharing para criar snapshots`);
      return;
    }

    // STEP 2: Para cada sharing, verificar snapshots
    console.log(`🔍 [DEBUG] STEP 2: Verificando snapshots em cada sharing...`);
    
    let totalSnapshots = 0;
    
    for (const sharingDoc of sharingsSnapshot.docs) {
      const shareToken = sharingDoc.id;
      const sharingData = sharingDoc.data();
      
      console.log(`🔍 [DEBUG] Verificando snapshots para token: ${shareToken}`);
      console.log(`📊 [DEBUG] Sharing data:`, {
        proposalId: sharingData.proposalId,
        createdBy: sharingData.createdBy,
        createdAt: sharingData.createdAt?.toDate(),
        isActive: sharingData.isActive
      });
      
      // Buscar snapshots deste compartilhamento
      const snapshotsCollection = sharingDoc.ref.collection('snapshots');
      const snapshotsSnapshot = await snapshotsCollection.get();
      
      console.log(`📸 [DEBUG] Encontrados ${snapshotsSnapshot.size} snapshots no share ${shareToken}`);
      totalSnapshots += snapshotsSnapshot.size;
      
      // STEP 3: Para cada snapshot, verificar subcoleções
      for (const snapshotDoc of snapshotsSnapshot.docs) {
        const influencerId = snapshotDoc.id;
        const snapshotData = snapshotDoc.data();
        
        console.log(`👤 [DEBUG] Analisando snapshot: ${influencerId}`);
        console.log(`📄 [DEBUG] Snapshot main data keys:`, Object.keys(snapshotData));
        console.log(`📄 [DEBUG] Name:`, snapshotData.name);
        console.log(`📄 [DEBUG] Original ID:`, snapshotData.originalInfluencerId);
        
        // Verificar subcoleções
        const pricingCollection = snapshotDoc.ref.collection('pricing');
        const pricingSnapshot = await pricingCollection.get();
        console.log(`💰 [DEBUG] Pricing documents: ${pricingSnapshot.size}`);
        
        const budgetsCollection = snapshotDoc.ref.collection('budgets');
        const budgetsSnapshot = await budgetsCollection.get();
        console.log(`💰 [DEBUG] Budget documents: ${budgetsSnapshot.size}`);
        
        const demographicsCollection = snapshotDoc.ref.collection('demographics');
        const demographicsSnapshot = await demographicsCollection.get();
        console.log(`📊 [DEBUG] Demographics documents: ${demographicsSnapshot.size}`);
        
        // Mostrar detalhes dos demographics se existirem
        if (!demographicsSnapshot.empty) {
          demographicsSnapshot.forEach(demoDoc => {
            const demoData = demoDoc.data();
            console.log(`📊 [DEBUG] Demographics found:`, {
              id: demoDoc.id,
              platform: demoData.platform,
              hasAudienceGender: !!demoData.audienceGender,
              hasAudienceLocations: !!demoData.audienceLocations,
              hasAudienceAgeRange: !!demoData.audienceAgeRange
            });
          });
        } else {
          console.log(`❌ [DEBUG] NO DEMOGRAPHICS found for ${influencerId}`);
        }
      }
    }

    // STEP 4: Verificar proposta original
    console.log(`🔍 [DEBUG] STEP 4: Verificando proposta original...`);
    
    const proposalRef = db.collection('proposals').doc(proposalId);
    const proposalDoc = await proposalRef.get();
    
    if (proposalDoc.exists()) {
      const proposalData = proposalDoc.data();
      console.log(`📋 [DEBUG] Proposta encontrada:`, {
        nome: proposalData.nome,
        hasSnapshots: proposalData.hasSnapshots,
        influencersInMainDoc: proposalData.influencers?.length || 0
      });
      
      // Verificar subcoleção
      const influencersSubcollection = proposalRef.collection('influencers');
      const influencersSnapshot = await influencersSubcollection.get();
      console.log(`👥 [DEBUG] Influencers na subcoleção: ${influencersSnapshot.size}`);
      
    } else {
      console.log(`❌ [DEBUG] Proposta ${proposalId} não encontrada`);
    }

    // STEP 5: Verificar influenciador original para comparação
    console.log(`🔍 [DEBUG] STEP 5: Verificando dados originais do influenciador...`);
    
    const influencerRef = db.collection('influencers').doc('JMTS1denFFLWVGJ7UARJ');
    const influencerDoc = await influencerRef.get();
    
    if (influencerDoc.exists()) {
      const influencerData = influencerDoc.data();
      console.log(`👤 [DEBUG] Influenciador original encontrado:`, {
        name: influencerData.name,
        hasCurrentDemographics: !!influencerData.currentDemographics
      });
      
      // Verificar subcoleção de demographics do influenciador original
      const originalDemographicsRef = influencerRef.collection('demographics');
      const originalDemographicsSnapshot = await originalDemographicsRef.get();
      console.log(`📊 [DEBUG] Demographics originais: ${originalDemographicsSnapshot.size}`);
      
      if (!originalDemographicsSnapshot.empty) {
        originalDemographicsSnapshot.forEach(demoDoc => {
          const demoData = demoDoc.data();
          console.log(`📊 [DEBUG] Original demographic:`, {
            id: demoDoc.id,
            platform: demoData.platform,
            isActive: demoData.isActive
          });
        });
      }
    }

    // RESUMO
    console.log(`\n🎯 [RESUMO] Diagnóstico completo:`);
    console.log(`📋 Proposal sharings encontrados: ${sharingsSnapshot.size}`);
    console.log(`📸 Total de snapshots: ${totalSnapshots}`);
    
    if (totalSnapshots === 0) {
      console.log(`❌ PROBLEMA: Nenhum snapshot encontrado`);
      console.log(`💡 SOLUÇÃO: Verifique se o processo de captura está funcionando`);
    } else {
      console.log(`✅ Snapshots encontrados, verificar subcoleções acima para mais detalhes`);
    }

  } catch (error) {
    console.error('❌ [DEBUG] Erro no diagnóstico:', error);
  }
}

// Executar diagnóstico
const proposalId = process.argv[2] || 'Xd8ntVkssHRlqnvISxiz';
diagnosticSnapshots(proposalId); 
