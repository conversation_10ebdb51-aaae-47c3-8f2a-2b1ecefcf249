"use client";

import { useState } from "react";
import { Plus, Trash2, Upload, Loader2, Edit, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useBrands } from "@/hooks/use-brands";
import { Brand, CreateBrandData, UpdateBrandData } from "@/types/brand";

export function BrandManager() {
  const { toast } = useToast();
  
  // Usar o hook atualizado com isolamento automático
  const { 
    brands, 
    loading, 
    error, 
    createBrand, 
    updateBrand, 
    deleteBrand,
    refreshBrands 
  } = useBrands({
    onError: (errorMessage) => {
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    },
    onSuccess: (successMessage) => {
      toast({
        title: "Sucesso",
        description: successMessage,
      });
    }
  });

  const [isAdding, setIsAdding] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [newBrand, setNewBrand] = useState<CreateBrandData>({
    name: "",
    industry: "",
    logoBackgroundColor: "#ffffff",
    logo: ""
  });
  const [editBrand, setEditBrand] = useState<UpdateBrandData>({
    name: "",
    industry: "",
    logo: "",
    logoBackgroundColor: "#ffffff",
  });
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);

  // Manipular mudança nos campos do formulário
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewBrand((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Manipular seleção de arquivo de logo
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setLogoFile(file);
      
      // Criar preview da imagem
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Função para adicionar uma nova marca
  const handleAddBrand = async () => {
    if (!newBrand.name || !logoFile) {
      toast({
        title: "Campos obrigatórios",
        description: "Nome da marca e logo são obrigatórios.",
        variant: "destructive",
      });
      return;
    }

    setIsAdding(true);
    try {
      // 1. Fazer upload da logo
      const uploadData = new FormData();
      uploadData.append("file", logoFile);
      uploadData.append("folder", "brands");
      
      const uploadResponse = await fetch("/api/admin/upload", {
        method: "POST",
        body: uploadData,
      });
      
      if (!uploadResponse.ok) {
        throw new Error("Falha ao fazer upload da logo");
      }
      
      const uploadResult = await uploadResponse.json();
      const logoUrl = uploadResult.url;
      
      // 2. Criar a marca com a URL da logo usando o hook
      const brandData: CreateBrandData = {
        ...newBrand,
        logo: logoUrl,
      };
      
      await createBrand(brandData);
        
        // Resetar o formulário
      setNewBrand({ name: "", industry: "", logoBackgroundColor: "#ffffff", logo: "" });
        setLogoFile(null);
        setLogoPreview(null);
        setIsOpen(false);
        
    } catch (error) {
      console.error("Erro ao adicionar marca:", error);
      // O erro já é tratado pelo hook através do onError
    } finally {
      setIsAdding(false);
    }
  };

  // Função para excluir uma marca
  const handleDeleteBrand = async (brandId: string) => {
    if (confirm("Tem certeza que deseja excluir esta marca?")) {
      try {
        await deleteBrand(brandId);
        // O sucesso é tratado pelo hook através do onSuccess
      } catch (error) {
        console.error("Erro ao excluir marca:", error);
        // O erro já é tratado pelo hook através do onError
      }
    }
  };

  // Função para iniciar edição de uma marca
  const handleStartEdit = (brand: any) => {
    setSelectedBrand(brand);
    setEditBrand({
      name: brand.name,
      industry: brand.industry || "",
      logo: brand.logo,
      logoBackgroundColor: brand.logoBackgroundColor || "#ffffff",
    });
    setLogoPreview(brand.logo);
    setIsEditOpen(true);
  };

  // Função para atualizar uma marca
  const handleUpdateBrand = async () => {
    if (!selectedBrand || !editBrand.name) {
      toast({
        title: "Campos obrigatórios",
        description: "Nome da marca é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    setIsEditing(true);
    try {
      let logoUrl = editBrand.logo || "";

      // Se um novo arquivo de logo foi selecionado, fazer upload
      if (logoFile) {
        const uploadData = new FormData();
        uploadData.append("file", logoFile);
        uploadData.append("folder", "brands");
        
        const uploadResponse = await fetch("/api/admin/upload", {
          method: "POST",
          body: uploadData,
        });
        
        if (!uploadResponse.ok) {
          throw new Error("Falha ao fazer upload da nova logo");
        }
        
        const uploadResult = await uploadResponse.json();
        logoUrl = uploadResult.url;
      }
      
      // Atualizar a marca com os novos dados usando o hook
      const brandData: UpdateBrandData = {
        ...editBrand,
        logo: logoUrl,
      };
      
      await updateBrand(selectedBrand.id, brandData);
        
        // Resetar o formulário
        setEditBrand({ name: "", industry: "", logo: "", logoBackgroundColor: "#ffffff" });
        setLogoFile(null);
        setLogoPreview(null);
        setIsEditOpen(false);
        setSelectedBrand(null);
        
    } catch (error) {
      console.error("Erro ao atualizar marca:", error);
      // O erro já é tratado pelo hook através do onError
    } finally {
      setIsEditing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-black">Gerenciar Marcas</h2>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsOpen(true)} className="bg-primary text-white hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Nova Marca
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white border border-gray-200 text-black max-w-md">
            <DialogHeader>
              <DialogTitle>Adicionar Nova Marca</DialogTitle>
              <DialogDescription className="text-gray-600">
                Preencha as informações e faça upload da logo da marca.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome da Marca</Label>
                  <Input
                    type="text"
                    name="name"
                    value={newBrand.name}
                    onChange={handleInputChange}
                    placeholder="Nome da marca"
                    className="bg-white border-gray-200 text-black"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="industry">Segmento/Indústria (opcional)</Label>
                  <Input
                    type="text"
                    name="industry"
                    value={newBrand.industry}
                    onChange={handleInputChange}
                    placeholder="Ex: Tecnologia, Moda, Alimentação"
                    className="bg-white border-gray-200 text-black"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="logoBackgroundColor">Cor de Fundo da Logo</Label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      id="logoBackgroundColor"
                      value={newBrand.logoBackgroundColor}
                      onChange={(e) => setNewBrand(prev => ({ ...prev, logoBackgroundColor: e.target.value }))}
                      className="w-12 h-10 rounded border border-gray-200 cursor-pointer"
                    />
                    <Input
                      type="text"
                      value={newBrand.logoBackgroundColor}
                      onChange={(e) => setNewBrand(prev => ({ ...prev, logoBackgroundColor: e.target.value }))}
                      placeholder="#ffffff"
                      className="bg-white border-gray-200 text-black flex-1"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="logo">Logo da Marca</Label>
                  <div className="flex items-center space-x-4">
                    <div className="border border-dashed border-gray-200 rounded-md p-4 w-full">
                      <label
                        htmlFor="logo-upload"
                        className="flex flex-col items-center justify-center cursor-pointer"
                      >
                        {logoPreview ? (
                          <div className="flex flex-col items-center space-y-2">
                            <div 
                              className="w-32 h-32 rounded-lg flex items-center justify-center p-2"
                              style={{ backgroundColor: newBrand.logoBackgroundColor }}
                            >
                              <img
                                src={logoPreview}
                                alt="Preview"
                                className="max-w-full max-h-full object-contain"
                              />
                            </div>
                            <p className="text-xs text-gray-400">Clique para alterar</p>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center space-y-2 py-4">
                            <Upload className="h-10 w-10 text-gray-400" />
                            <label className="block text-sm font-medium text-gray-800 mb-1">
                              Clique para fazer upload
                            </label>
                            <span className="text-xs text-gray-500">
                              PNG, JPG ou SVG (max 2MB)
                            </span>
                            <input
                              id="logo-upload"
                              type="file"
                              accept="image/png, image/jpeg, image/svg+xml"
                              className="hidden"
                              onChange={handleLogoChange}
                            />
                          </div>
                        )}
                        <input
                          id="logo-upload"
                          type="file"
                          accept="image/png, image/jpeg, image/svg+xml"
                          className="hidden"
                          onChange={handleLogoChange}
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                className="border-gray-200 text-gray-800 hover:bg-gray-100"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleAddBrand}
                disabled={isAdding}
                className="bg-primary text-white hover:bg-primary/90"
              >
                {isAdding ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Adicionando...
                  </>
                ) : (
                  "Adicionar Marca"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Dialog para edição de marca */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="bg-white border border-gray-200 text-black max-w-md">
          <DialogHeader>
            <DialogTitle>Editar Marca</DialogTitle>
            <DialogDescription className="text-gray-600">
              Atualize as informações da marca.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nome da Marca</Label>
                <Input
                  type="text"
                  id="edit-name"
                  name="name"
                  value={editBrand.name}
                  onChange={(e) =>
                    setEditBrand((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  className="bg-white text-black"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-industry">Indústria</Label>
                <Input
                  type="text"
                  id="edit-industry"
                  name="industry"
                  value={editBrand.industry}
                  onChange={(e) =>
                    setEditBrand((prev) => ({
                      ...prev,
                      industry: e.target.value,
                    }))
                  }
                  className="bg-white text-black"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-logoBackgroundColor">Cor de Fundo da Logo</Label>
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    id="edit-logoBackgroundColor"
                    value={editBrand.logoBackgroundColor}
                    onChange={(e) => setEditBrand(prev => ({ ...prev, logoBackgroundColor: e.target.value }))}
                    className="w-12 h-10 rounded border border-gray-200 cursor-pointer"
                  />
                  <Input
                    type="text"
                    value={editBrand.logoBackgroundColor}
                    onChange={(e) => setEditBrand(prev => ({ ...prev, logoBackgroundColor: e.target.value }))}
                    placeholder="#ffffff"
                    className="bg-white border-gray-200 text-black flex-1"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-logo">Logo</Label>
                <div className="flex items-center gap-4">
                  {logoPreview && (
                    <div 
                      className="w-16 h-16 rounded-lg flex items-center justify-center p-1"
                      style={{ backgroundColor: editBrand.logoBackgroundColor }}
                    >
                      <img
                        src={logoPreview}
                        alt="Preview"
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                  )}
                  <Label
                    htmlFor="edit-logo"
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md cursor-pointer hover:bg-gray-200 transition-colors"
                  >
                    <Upload className="h-4 w-4" />
                    Alterar Logo
                  </Label>
                  <Input
                    type="file"
                    id="edit-logo"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="hidden"
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditOpen(false)}
              disabled={isEditing}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              onClick={handleUpdateBrand}
              disabled={isEditing}
              className="bg-primary text-white hover:bg-primary/90"
            >
              {isEditing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Atualizando...
                </>
              ) : (
                "Atualizar"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mostrar erro se houver */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshBrands}
              className="ml-2"
            >
              Tentar novamente
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <img 
              src="/loader-dm.webp" 
              alt="Carregando..."
              className="w-16 h-16 object-contain"
            />
        </div>
      ) : brands.length > 0 ? (
        <Table className="bg-white shadow-sm rounded-lg overflow-hidden">
          <TableHeader className="bg-gray-50">
            <TableRow className="hover:bg-gray-100">
              <TableHead className="text-black font-medium w-20">Logo</TableHead>
              <TableHead className="text-black font-medium">Nome</TableHead>
              <TableHead className="text-black font-medium">Segmento</TableHead>
              <TableHead className="text-black font-medium w-20 text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {brands.map((brand) => (
              <TableRow
                key={brand.id}
                className="hover:bg-gray-50"
              >
                <TableCell>
                  {brand.logo ? (
                    <div 
                      className="w-10 h-10 flex items-center justify-center rounded-md overflow-hidden border border-gray-200"
                      style={{ backgroundColor: brand.logoBackgroundColor || '#f3f4f6' }}
                    >
                      <img
                        src={brand.logo}
                        alt={`${brand.name} logo`}
                        className="w-8 h-8 object-contain"
                      />
                    </div>
                  ) : (
                    <div className="w-10 h-10 flex items-center justify-center bg-gray-200 rounded-md text-gray-600">
                      {brand.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </TableCell>
                <TableCell className="font-medium text-black">
                  {brand.name}
                </TableCell>
                <TableCell className="text-gray-600">
                  {brand.industry || "-"}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleStartEdit(brand)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                                            onClick={() => handleDeleteBrand(brand.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-500/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <p className="text-gray-600">Nenhuma marca cadastrada.</p>
          <p className="text-gray-500 text-sm mt-2">
            Clique em "Nova Marca" para adicionar.
          </p>
        </div>
      )}
    </div>
  );
}


