import { z } from 'zod';
import { BaseDocumentSchema, DocumentStatusSchema, ERROR_MESSAGES } from './base';

// Schema para redes sociais da marca
export const BrandSocialMediaSchema = z.object({
  instagram: z.string().url(ERROR_MESSAGES.INVALID_URL).optional(),
  facebook: z.string().url(ERROR_MESSAGES.INVALID_URL).optional(),
  twitter: z.string().url(ERROR_MESSAGES.INVALID_URL).optional(),
  linkedin: z.string().url(ERROR_MESSAGES.INVALID_URL).optional()
}).optional();

// Schema completo para Brand
export const BrandSchema = BaseDocumentSchema.extend({
  name: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim(),
  
  logo: z.string()
    .refine((val) => !val || val.length === 0 || z.string().url().safeParse(val).success, {
      message: 'URL do logo inválida'
    })
    .optional(),
  
  logoBackgroundColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Cor deve estar em formato hexadecimal (#RRGGBB)')
    .optional(),
  
  industry: z.string()
    .max(50, ERROR_MESSAGES.MAX_LENGTH(50))
    .optional(),
  
  website: z.string()
    .url(ERROR_MESSAGES.INVALID_URL)
    .optional(),
  
  contactEmail: z.string()
    .email(ERROR_MESSAGES.INVALID_EMAIL)
    .optional(),
  
  contactPhone: z.string()
    .regex(/^\+?[\d\s\-\(\)]{8,}$/, ERROR_MESSAGES.INVALID_PHONE)
    .optional(),
  
  contactName: z.string()
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .optional(),
  
  budget: z.number()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0))
    .max(10000000, ERROR_MESSAGES.MAX_VALUE(10000000))
    .optional(),
  
  notes: z.string()
    .max(1000, ERROR_MESSAGES.MAX_LENGTH(1000))
    .optional(),
  
  description: z.string()
    .max(500, ERROR_MESSAGES.MAX_LENGTH(500))
    .optional(),
  
  status: DocumentStatusSchema,
  
  socialMedia: BrandSocialMediaSchema
});

// Schema para criação de nova marca (sem campos de sistema)
export const CreateBrandSchema = z.object({
  name: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim(),
  
  logo: z.string()
    .refine((val) => !val || val.length === 0 || z.string().url().safeParse(val).success, {
      message: 'URL do logo inválida'
    })
    .optional(),
  
  logoBackgroundColor: z.string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Cor deve estar em formato hexadecimal (#RRGGBB)')
    .optional(),
  
  industry: z.string()
    .max(50, ERROR_MESSAGES.MAX_LENGTH(50))
    .optional(),
  
  website: z.string()
    .url(ERROR_MESSAGES.INVALID_URL)
    .optional(),
  
  contactEmail: z.string()
    .email(ERROR_MESSAGES.INVALID_EMAIL)
    .optional(),
  
  contactPhone: z.string()
    .regex(/^\+?[\d\s\-\(\)]{8,}$/, ERROR_MESSAGES.INVALID_PHONE)
    .optional(),
  
  contactName: z.string()
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .optional(),
  
  budget: z.number()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0))
    .max(10000000, ERROR_MESSAGES.MAX_VALUE(10000000))
    .optional(),
  
  notes: z.string()
    .max(1000, ERROR_MESSAGES.MAX_LENGTH(1000))
    .optional(),
  
  description: z.string()
    .max(500, ERROR_MESSAGES.MAX_LENGTH(500))
    .optional(),
  
  status: DocumentStatusSchema.optional(),
  
  socialMedia: BrandSocialMediaSchema
});

// Schema para atualização de marca (todos os campos opcionais)
export const UpdateBrandSchema = CreateBrandSchema.partial();

// Schema para filtros de marca
export const BrandFiltersSchema = z.object({
  userId: z.string().min(1).optional(),
  search: z.string().optional(),
  industry: z.array(z.string()).optional(),
  status: z.array(DocumentStatusSchema).optional(),
  budgetMin: z.number().min(0).optional(),
  budgetMax: z.number().min(0).optional(),
  hasWebsite: z.boolean().optional(),
  hasEmail: z.boolean().optional(),
  hasPhone: z.boolean().optional(),
  createdAtFrom: z.date().optional(),
  createdAtTo: z.date().optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  offset: z.number().int().min(0).optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'budget']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// Schema para validação de relacionamento brand-user
export const BrandOwnershipSchema = z.object({
  brandId: z.string().min(1, 'Brand ID é obrigatório'),
  userId: z.string().min(1, 'User ID é obrigatório')
});

// Tipos TypeScript derivados dos schemas
export type BrandValidation = z.infer<typeof BrandSchema>;
export type CreateBrandInput = z.infer<typeof CreateBrandSchema>;
export type UpdateBrandInput = z.infer<typeof UpdateBrandSchema>;
export type BrandFiltersInput = z.infer<typeof BrandFiltersSchema>;
export type BrandOwnershipInput = z.infer<typeof BrandOwnershipSchema>;

// Funções de validação customizadas
export function validateBrandOwnership(brandData: any, userId: string): boolean {
  if (!brandData || !brandData.userId) {
    return false;
  }
  return brandData.userId === userId;
}

export function validateBrandUniqueness(
  newBrandName: string, 
  existingBrands: any[], 
  userId: string,
  excludeBrandId?: string
): boolean {
  const normalizedName = newBrandName.trim().toLowerCase();
  
  return !existingBrands.some(brand => 
    brand.userId === userId &&
    brand.name.toLowerCase() === normalizedName &&
    brand.id !== excludeBrandId
  );
}

export function sanitizeBrandData(data: CreateBrandInput): CreateBrandInput {
  const sanitized = { ...data };
  
  // Normalizar nome
  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }
  
  // Normalizar URLs
  if (sanitized.website && !sanitized.website.startsWith('http')) {
    sanitized.website = `https://${sanitized.website}`;
  }
  
  if (sanitized.logo && !sanitized.logo.startsWith('http')) {
    sanitized.logo = `https://${sanitized.logo}`;
  }
  
  // Normalizar redes sociais
  if (sanitized.socialMedia) {
    Object.keys(sanitized.socialMedia).forEach(key => {
      const url = (sanitized.socialMedia as any)[key];
      if (url && !url.startsWith('http')) {
        (sanitized.socialMedia as any)[key] = `https://${url}`;
      }
    });
  }
  
  // Definir status padrão se não fornecido
  if (!sanitized.status) {
    sanitized.status = 'active';
  }
  
  return sanitized;
}

export function calculateBrandScore(brand: any): number {
  let score = 0;
  
  // Pontuação base por campo preenchido
  if (brand.name) score += 10;
  if (brand.logo) score += 10;
  if (brand.description) score += 5;
  if (brand.website) score += 10;
  if (brand.contactEmail) score += 10;
  if (brand.contactPhone) score += 5;
  if (brand.contactName) score += 5;
  if (brand.industry) score += 5;
  if (brand.budget && brand.budget > 0) score += 15;
  
  // Pontuação por redes sociais
  if (brand.socialMedia) {
    const socialCount = Object.values(brand.socialMedia).filter(Boolean).length;
    score += socialCount * 5;
  }
  
  // Bonificação por completude
  if (score >= 60) score += 10; // Perfil bem completo
  if (score >= 80) score += 15; // Perfil muito completo
  
  return Math.min(score, 100); // Máximo 100
} 

