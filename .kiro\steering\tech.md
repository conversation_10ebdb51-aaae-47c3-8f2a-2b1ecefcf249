# Technology Stack

## Core Framework
- **Next.js 15** with App Router architecture
- **TypeScript** for type safety
- **React 19** for UI components

## Styling & UI
- **Tailwind CSS v4** for styling
- **Shadcn/ui** component library with Radix UI primitives
- **Framer Motion** for animations
- **Lucide React** for icons

## Backend & Database
- **Firebase Firestore** as primary database
- **Firebase Storage** for file uploads
- **Firebase Admin SDK** for server-side operations
- **GraphQL** with Apollo Client for data fetching

## Authentication & Authorization
- **Clerk** for user authentication and organization management
- **Role-based access control** with org:admin, org:manager, org:member roles
- **Multi-organization support** with workspace isolation

## State Management
- **Apollo Client** with InMemoryCache for GraphQL state
- **React Context** for global UI state (sidebar, language, auth)
- **React Hook Form** with Zod validation for forms

## Development Tools
- **ESLint** for code linting
- **TypeScript** strict mode enabled
- **Vercel** for deployment

## Common Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Firebase
firebase deploy      # Deploy Firestore rules and functions
firebase emulators:start  # Start local emulators

# Database scripts
node scripts/init-database.js     # Initialize database collections
node scripts/migrate-all.ts       # Run data migrations
```

## Environment Variables Required
- `NEXT_PUBLIC_FIREBASE_*` - Firebase configuration
- `NEXT_PUBLIC_CLERK_*` - Clerk authentication
- `FIREBASE_ADMIN_*` - Firebase Admin SDK credentials