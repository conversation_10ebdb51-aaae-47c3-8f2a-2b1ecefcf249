'use client'

import { useQueryStates, parseAsString, parseAsInteger, parseAsBoolean } from 'nuqs'
import { useMemo } from 'react'

// 🔥 TIPOS PARA ESTADO DA URL
interface InfluencersUrlState {
  // Filtros de busca
  searchTerm: string
  selectedLocation: string
  minFollowers: number
  maxFollowers: number
  minRating: number
  verifiedOnly: boolean
  availableOnly: boolean
  
  // Seleções
  selectedProposal: string | null
  selectedInfluencer: string | null
  selectedBrands: string[]
  
  // Visualização
  viewMode: 'grid' | 'table'
  sortBy: string
  sortOrder: 'asc' | 'desc'
  
  // Paginação
  page: number
  limit: number
}

// 🔥 PARSERS PARA QUERY PARAMETERS
const urlStateConfig = {
  // Filtros
  searchTerm: parseAsString.withDefault(''),
  selectedLocation: parseAsString.withDefault(''),
  minFollowers: parseAsInteger.withDefault(0),
  maxFollowers: parseAsInteger.withDefault(10000000),
  minRating: parseAsInteger.withDefault(0),
  verifiedOnly: parseAsBoolean.withDefault(false),
  availableOnly: parseAsBoolean.withDefault(false),
  
  // Seleções
  proposta: parseAsString.withDefault(''),
  influencer: parseAsString.withDefault(''),
  brands: parseAsString.withDefault(''),
  
  // Visualização
  view: parseAsString.withDefault('grid'),
  sort: parseAsString.withDefault('followers'),
  order: parseAsString.withDefault('desc'),
  
  // Paginação
  page: parseAsInteger.withDefault(1),
  limit: parseAsInteger.withDefault(20)
}

/**
 * 🚀 Hook para gerenciar estado dos influenciadores via URL
 * Elimina a necessidade de useState e torna o estado persistente
 */
export function useInfluencersUrlState() {
  const [urlState, setUrlState] = useQueryStates(urlStateConfig, {
    // 🔥 OTIMIZAÇÃO: Shallow routing para não recarregar a página
    shallow: true,
    // 🔥 OTIMIZAÇÃO: Throttle para evitar muitas atualizações
    throttleMs: 100,
    // 🔥 OTIMIZAÇÃO: Limpar parâmetros vazios da URL
    clearOnDefault: true
  })

  // 🔥 ESTADO COMPUTADO COM MEMOIZAÇÃO
  const state = useMemo((): InfluencersUrlState => ({
    searchTerm: urlState.searchTerm,
    selectedLocation: urlState.selectedLocation,
    minFollowers: urlState.minFollowers,
    maxFollowers: urlState.maxFollowers,
    minRating: urlState.minRating,
    verifiedOnly: urlState.verifiedOnly,
    availableOnly: urlState.availableOnly,
    selectedProposal: urlState.proposta || null,
    selectedInfluencer: urlState.influencer || null,
    selectedBrands: urlState.brands ? urlState.brands.split(',').filter(Boolean) : [],
    viewMode: (urlState.view as 'grid' | 'table') || 'grid',
    sortBy: urlState.sort,
    sortOrder: (urlState.order as 'asc' | 'desc') || 'desc',
    page: urlState.page,
    limit: urlState.limit
  }), [urlState])

  // 🔥 AÇÕES OTIMIZADAS PARA ATUALIZAR ESTADO
  const actions = useMemo(() => ({
    // Filtros
    setSearchTerm: (searchTerm: string) => setUrlState({ searchTerm }),
    setSelectedLocation: (selectedLocation: string) => setUrlState({ selectedLocation }),
    setFollowersRange: (min: number, max: number) => 
      setUrlState({ minFollowers: min, maxFollowers: max }),
    setMinRating: (minRating: number) => setUrlState({ minRating }),
    setVerifiedOnly: (verifiedOnly: boolean) => setUrlState({ verifiedOnly }),
    setAvailableOnly: (availableOnly: boolean) => setUrlState({ availableOnly }),
    
    // Seleções
    setSelectedProposal: (proposalId: string | null) => 
      setUrlState({ proposta: proposalId || '' }),
    setSelectedInfluencer: (influencerId: string | null) => 
      setUrlState({ influencer: influencerId || '' }),
    setSelectedBrands: (brands: string[]) => 
      setUrlState({ brands: brands.join(',') }),
    
    // Visualização
    setViewMode: (view: 'grid' | 'table') => setUrlState({ view }),
    setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => 
      setUrlState({ sort: sortBy, order: sortOrder }),
    
    // Paginação
    setPage: (page: number) => setUrlState({ page }),
    setLimit: (limit: number) => setUrlState({ limit }),
    
    // Ações combinadas
    resetFilters: () => setUrlState({
      searchTerm: '',
      selectedLocation: '',
      minFollowers: 0,
      maxFollowers: 10000000,
      minRating: 0,
      verifiedOnly: false,
      availableOnly: false
    }),
    
    resetAll: () => setUrlState({
      searchTerm: '',
      selectedLocation: '',
      minFollowers: 0,
      maxFollowers: 10000000,
      minRating: 0,
      verifiedOnly: false,
      availableOnly: false,
      proposta: '',
      influencer: '',
      brands: '',
      view: 'grid',
      sort: 'followers',
      order: 'desc',
      page: 1,
      limit: 20
    })
  }), [setUrlState])

  return {
    state,
    actions,
    // 🔥 UTILITÁRIOS
    isFiltered: state.searchTerm || state.selectedLocation || state.minFollowers > 0 || 
               state.maxFollowers < 10000000 || state.minRating > 0 || 
               state.verifiedOnly || state.availableOnly,
    hasSelection: !!state.selectedProposal || !!state.selectedInfluencer,
    // 🔥 URL ATUAL PARA DEBUG
    currentUrl: typeof window !== 'undefined' ? window.location.search : ''
  }
}

// 🔥 HOOK SIMPLIFICADO PARA COMPONENTES QUE SÓ PRECISAM DE LEITURA
export function useInfluencersUrlStateRead() {
  const { state } = useInfluencersUrlState()
  return state
}
