# 📅 FASE 1: Auditoria e Preparação (1-2 dias)

## 🎯 Objetivo
Mapear todas as coleções existentes e identificar quais precisam de isolamento por usuário.

## 📝 Tarefas Detalhadas

### 1.1 Auditoria de Coleções Firebase

#### Coleções Identificadas no Projeto:
- [ ] **users** - ✅ Base para isolamento
- [ ] **brands** - ❌ Precisa de `userId`
- [ ] **campaigns** - ❌ Precisa de `userId`
- [ ] **proposals** - ✅ Já tem `userId` 
- [ ] **influencers** - ❌ Precisa de `userId`
- [ ] **brand_influencers** - ❌ Precisa de `userId`
- [ ] **categories** - ❓ Avaliar se precisa isolamento
- [ ] **filters** - ✅ Já tem `userId`
- [ ] **groups** - ❌ Precisa de `userId`
- [ ] **notes** - ❌ Precisa de `userId`
- [ ] **influencer_financials** - ❌ Precisa de `userId`

#### Campos a Adicionar:
```typescript
// Estrutura padrão para isolamento
interface BaseDocument {
  id: string;
  userId: string;          // 🆕 Campo obrigatório
  createdAt: Date;
  updatedAt: Date;
}
```

### 1.2 Análise de APIs

#### APIs que precisam de atualização:
- [ ] `/api/brands` - Adicionar filtro por userId
- [ ] `/api/campaigns` - Adicionar filtro por userId
- [ ] `/api/influencers` - Adicionar filtro por userId
- [ ] `/api/marcas/influencers` - Adicionar filtro por userId
- [ ] `/api/groups` - Adicionar filtro por userId
- [ ] `/api/notes` - Adicionar filtro por userId

#### APIs que já estão corretas:
- [x] `/api/proposals` - Já usa middleware de autenticação
- [x] `/api/filters` - Já filtra por userId

### 1.3 Análise de Componentes Frontend

#### Componentes que fazem consultas diretas:
- [ ] `components/brand-manager.tsx`
- [ ] `components/campaigns-kanban.tsx`
- [ ] `components/influencer-grid/`
- [ ] `components/groups/`
- [ ] `components/NotesManager.tsx`

#### Hooks que precisam de atualização:
- [ ] Hook para brands (criar)
- [ ] Hook para campaigns (criar)
- [ ] Hook para influencers (atualizar)
- [ ] Hook para groups (criar)

### 1.4 Relacionamentos Entre Entidades

```mermaid
graph TD
    A[User] --> B[Brands]
    A --> G[Groups]
    A --> N[Notes]
    B --> C[Campaigns]
    B --> P[Proposals]
    B --> BI[Brand Influencers]
    C --> P
    BI --> I[Influencers]
    I --> IF[Influencer Financials]
```

## 📊 Deliverables da Fase 1

### 1. Documento de Auditoria Completa
```markdown
# Relatório de Auditoria - Isolamento Multi-Tenancy

## Coleções Mapeadas: 11
## APIs Identificadas: 15
## Componentes Afetados: 20
## Relacionamentos Mapeados: 8

## Status Atual:
- ✅ Propostas: Já isoladas
- ✅ Filtros: Já isolados
- ❌ Brands: Sem isolamento
- ❌ Campaigns: Sem isolamento
- ❌ Influencers: Sem isolamento
```

### 2. Lista de Coleções para Migração
| Coleção | Status | Prioridade | Complexidade |
|---------|--------|------------|--------------|
| brands | ❌ Sem userId | Alta | Baixa |
| campaigns | ❌ Sem userId | Alta | Média |
| influencers | ❌ Sem userId | Alta | Baixa |
| brand_influencers | ❌ Sem userId | Alta | Alta |
| groups | ❌ Sem userId | Média | Baixa |
| notes | ❌ Sem userId | Baixa | Baixa |

### 3. Mapa de Dependências
```
Ordem de Implementação:
1. Users (base) ✅
2. Brands 
3. Campaigns (depende de Brands)
4. Proposals (já feito) ✅
5. Brand Influencers (depende de Brands)
6. Groups
7. Notes
```

### 4. Plano de Migração de Dados

#### Estratégia de Migração:
1. **Backup completo** antes de iniciar
2. **Migração por lotes** (batch operations)
3. **Validação em cada etapa**
4. **Rollback automático** em caso de erro

#### Scripts necessários:
- `migrate-brands.ts`
- `migrate-campaigns.ts`
- `migrate-influencers.ts`
- `migrate-brand-influencers.ts`
- `migrate-groups.ts`
- `migrate-notes.ts`

## ✅ Critérios de Aceitação

- [ ] Todas as coleções mapeadas e documentadas
- [ ] Relacionamentos identificados e validados
- [ ] APIs categorizadas por necessidade de atualização
- [ ] Componentes frontend auditados
- [ ] Ordem de implementação definida
- [ ] Plano de migração validado pela equipe
- [ ] Backup strategy definida

## 🚀 Próxima Fase

Após completar a auditoria, seguir para [Fase 2: Estrutura de Dados](./fase-2-estrutura-dados.md) 