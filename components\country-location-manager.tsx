import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Globe } from 'lucide-react';
import { getAllCountries, getPopularCountries, type Country } from '@/lib/countries';
import { CountryFlag } from '@/components/ui/country-flag';

export interface CountryLocation {
  country: string;
  percentage: number;
}

interface CountryLocationManagerProps {
  locations: CountryLocation[];
  onChange: (locations: CountryLocation[]) => void;
  platform: string;
}

export const CountryLocationManager: React.FC<CountryLocationManagerProps> = ({
  locations,
  onChange,
  platform
}) => {
  // Obter países organizados: populares primeiro, depois todos os outros
  const popularCountries = getPopularCountries();
  const allCountries = getAllCountries();
  const otherCountries = allCountries.filter(country => !country.popular);

  const addLocation = () => {
    onChange([...locations, { country: '', percentage: 0 }]);
  };

  const updateLocation = (index: number, field: keyof CountryLocation, value: string | number) => {
    const newLocations = [...locations];
    newLocations[index] = { ...newLocations[index], [field]: value };
    onChange(newLocations);
  };

  const removeLocation = (index: number) => {
    onChange(locations.filter((_, i) => i !== index));
  };

  const totalPercentage = locations.reduce((sum, location) => sum + (location.percentage || 0), 0);

  return (
    <div className="space-y-3 mb-3">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium dark:text-white text-gray-700">
          Localização
        </Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addLocation}
          className="h-6 px-2 text-xs"
        >
          + Adicionar País
        </Button>
      </div>
      
      {locations.map((location, index) => (
        <div key={index} className="flex items-center gap-2">
          <Select
            value={location.country}
            onValueChange={(value) => updateLocation(index, 'country', value)}
          >
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Selecione o país" />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {/* Países populares primeiro */}
              {popularCountries.length > 0 && (
                <>
                  <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
                    <Globe className="inline h-3 w-3 mr-1" />
                    Países Populares
                  </div>
                                     {popularCountries.map((country: Country) => (
                     <SelectItem key={country.code} value={country.name}>
                       <span className="flex items-center gap-2">
                         <CountryFlag countryCode={country.code} />
                         <span>{country.name}</span>
                       </span>
                     </SelectItem>
                   ))}
                </>
              )}
              
              {/* Separador */}
              {popularCountries.length > 0 && otherCountries.length > 0 && (
                <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
                  Outros Países
                </div>
              )}
              
                             {/* Outros países em ordem alfabética */}
               {otherCountries.map((country: Country) => (
                 <SelectItem key={country.code} value={country.name}>
                   <span className="flex items-center gap-2">
                     <CountryFlag countryCode={country.code} />
                     <span>{country.name}</span>
                   </span>
                 </SelectItem>
               ))}
              
                             {/* Opção "Outro" no final */}
               <SelectItem value="Outro">
                 <span className="flex items-center gap-2">
                   <span className="w-4 h-3 flex items-center justify-center text-sm">🌍</span>
                   <span>Outro</span>
                 </span>
               </SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex items-center gap-1 min-w-[80px]">
            <Input
              type="number"
              min="0"
              max="100"
              value={location.percentage}
              onChange={(e) => {
                const newPercentage = Math.min(100, Math.max(0, parseInt(e.target.value) || 0));
                updateLocation(index, 'percentage', newPercentage);
              }}
              className="w-16 text-center"
              placeholder="%"
            />
            <span className="text-xs text-gray-500">%</span>
          </div>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => removeLocation(index)}
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ))}
      
      {locations.length > 0 && (
        <div className="text-xs text-gray-500">
          Total: {totalPercentage}%
          {totalPercentage > 100 && (
            <span className="text-red-500 ml-2">
              (Excede 100%)
            </span>
          )}
        </div>
      )}
    </div>
  );
};

