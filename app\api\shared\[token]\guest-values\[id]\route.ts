import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';
import { UpdateGuestProposalRequest, GuestProposal } from '@/types/proposal-sharing';

// PUT - Atualizar contraproposta existente
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ token: string; id: string }> }
) {
  try {
    const { token, id } = await params;
    const updateRequest: UpdateGuestProposalRequest = await request.json();

    // Buscar dados do compartilhamento
    const sharing = await ProposalService.getProposalSharingByToken(token);
    
    if (!sharing) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se está ativo
    if (!sharing.isActive) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento foi desativado' },
        { status: 403 }
      );
    }

    // Verificar se não expirou
    const now = new Date();
    if (sharing.expiresAt && sharing.expiresAt < now) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento expirou' },
        { status: 403 }
      );
    }

    // Buscar a proposta
    const proposal = await ProposalService.getProposalById(sharing.proposalId);
    
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    // Encontrar e atualizar a contraproposta
    const currentBudgets = (proposal as any).budgets || {};
    let proposalFound = false;
    let updatedBudgets = { ...currentBudgets };

    Object.entries(currentBudgets).forEach(([profileId, profileBudgets]) => {
      if (profileBudgets && typeof profileBudgets === 'object') {
        Object.entries(profileBudgets).forEach(([service, budget]) => {
          if (budget && typeof budget === 'object' && 'guestProposals' in budget && budget.guestProposals) {
            const proposalIndex = budget.guestProposals.findIndex((gp: GuestProposal) => gp.id === id);
            
            if (proposalIndex !== -1) {
              proposalFound = true;
              const existingProposal = budget.guestProposals[proposalIndex];
              
              // Criar nova proposta atualizada
              const updatedProposal = {
                ...existingProposal,
                ...updateRequest,
                updatedAt: new Date()
              };

              // Validar novo valor se fornecido
              if (updateRequest.value !== undefined) {
                if (updateRequest.value <= 0) {
                  throw new Error('Valor deve ser maior que zero');
                }
                if (updateRequest.value > (budget as any).budgetedPrice) {
                  throw new Error(`Valor deve ser menor ou igual ao orçado (R$ ${(budget as any).budgetedPrice})`);
                }
              }

              // Atualizar no objeto
              updatedBudgets[profileId] = {
                ...updatedBudgets[profileId],
                [service]: {
                  ...budget,
                  guestProposals: [
                    ...budget.guestProposals.slice(0, proposalIndex),
                    updatedProposal,
                    ...budget.guestProposals.slice(proposalIndex + 1)
                  ]
                }
              };
            }
          }
        });
      }
    });

    if (!proposalFound) {
      return NextResponse.json(
        { success: false, error: 'Contraproposta não encontrada' },
        { status: 404 }
      );
    }

    // Salvar no Firebase
    await ProposalService.updateProposalBudgets(sharing.proposalId, updatedBudgets, 'guest');

    console.log('✅ Contraproposta atualizada:', id);

    return NextResponse.json({
      success: true,
      message: 'Contraproposta atualizada com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar contraproposta:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Remover contraproposta
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ token: string; id: string }> }
) {
  try {
    const { token, id } = await params;

    // Buscar dados do compartilhamento
    const sharing = await ProposalService.getProposalSharingByToken(token);
    
    if (!sharing) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se está ativo
    if (!sharing.isActive) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento foi desativado' },
        { status: 403 }
      );
    }

    // Buscar a proposta
    const proposal = await ProposalService.getProposalById(sharing.proposalId);
    
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    // Encontrar e remover a contraproposta
    const currentBudgets = (proposal as any).budgets || {};
    let proposalFound = false;
    let updatedBudgets = { ...currentBudgets };

    Object.entries(currentBudgets).forEach(([profileId, profileBudgets]) => {
      if (profileBudgets && typeof profileBudgets === 'object') {
        Object.entries(profileBudgets).forEach(([service, budget]) => {
          if (budget && typeof budget === 'object' && 'guestProposals' in budget && budget.guestProposals) {
            const proposalIndex = budget.guestProposals.findIndex((gp: GuestProposal) => gp.id === id);
            
            if (proposalIndex !== -1) {
              proposalFound = true;
              
              // Remover do array
              const newGuestProposals = budget.guestProposals.filter((gp: GuestProposal) => gp.id !== id);
              
              updatedBudgets[profileId] = {
                ...updatedBudgets[profileId],
                [service]: {
                  ...budget,
                  guestProposals: newGuestProposals
                }
              };
            }
          }
        });
      }
    });

    if (!proposalFound) {
      return NextResponse.json(
        { success: false, error: 'Contraproposta não encontrada' },
        { status: 404 }
      );
    }

    // Salvar no Firebase
    await ProposalService.updateProposalBudgets(sharing.proposalId, updatedBudgets, 'guest');

    console.log('✅ Contraproposta removida:', id);

    return NextResponse.json({
      success: true,
      message: 'Contraproposta removida com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao remover contraproposta:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 