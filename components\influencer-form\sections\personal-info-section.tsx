'use client'

import { useState, useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { User, X, Loader2, Upload, ImageIcon, MapPin, Mail, Phone, Search } from 'lucide-react'
import { InfluencerFormData } from '@/types/influencer-form'
import { toast } from '@/hooks/use-toast'
import { useInfluencerAvatarPreview } from '@/hooks/use-influencer-avatar'
import { PhoneInput } from 'react-international-phone'
import 'react-international-phone/style.css'

// Estados brasileiros
const STATES = [
  "AC", "AL", "AP", "AM", "BA", "CE", "DF", "ES", "GO", "MA",
  "MT", "MS", "MG", "PA", "PB", "PR", "PE", "PI", "RJ", "RN",
  "RS", "RO", "RR", "SC", "SP", "SE", "TO"
]

interface PersonalInfoSectionProps {
  influencerId?: string;
}

export function PersonalInfoSection({ influencerId: propInfluencerId }: PersonalInfoSectionProps = {}) {
  const { register, setValue, watch, formState: { errors } } = useFormContext<InfluencerFormData>()
  const [isLoadingCep, setIsLoadingCep] = useState(false)
  
  // Hook para preview de avatar (sem upload imediato)
  const { selectedFile, previewUrl, isProcessing, selectFile, clearSelection } = useInfluencerAvatarPreview()

  // Observar campos do formulário
  const name = watch('personalInfo.name') || ''
  const avatar = watch('personalInfo.avatar') || ''
  const age = watch('personalInfo.age')
  const gender = watch('personalInfo.gender')
  const bio = watch('personalInfo.bio')
  const verified = watch('personalInfo.verified')
  const promotesTraders = watch('business.promotesTraders')
  
  // Campos de localização e contato
  const cep = watch('location.cep')
  const whatsapp = watch('contact.whatsapp')

  // Lógica de exibição do avatar com prioridades
  const avatarSrc = (() => {
    // 1. Preview local (arquivo selecionado)
    if (previewUrl && previewUrl.trim() !== '') {
      return previewUrl
    }
    
    // 2. Avatar do formulário (salvo)
    if (avatar && avatar.trim() !== '') {
      return avatar
    }
    
    // 3. Sem avatar
    return null
  })()

  // Função para gerar iniciais
  const getInitials = (name: string) => {
    if (!name || name.trim() === '') return 'IN'
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Seleção de arquivo (apenas preview)
  const handleAvatarClick = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = handleAvatarSelect
    input.click()
  }

  // Apenas gerar preview local (sem upload)
  const handleAvatarSelect = async (event: Event) => {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]
    if (!file) return

    try {
      await selectFile(file)
      
      // Armazenar o arquivo no formulário para upload posterior
      setValue('personalInfo.avatarFile', file as any, { shouldValidate: false })
      
      toast({
        title: "Imagem Selecionada",
        description: "A imagem será enviada quando você salvar o influenciador",
      })

    } catch (error) {

      toast({
        title: "Erro na Seleção",
        description: error instanceof Error ? error.message : "Falha ao selecionar imagem",
        variant: "destructive",
      })
    }
  }

  // Remover seleção de avatar
  const removeAvatar = () => {
    // Limpar formulário
    setValue('personalInfo.avatar', '', { shouldValidate: true })
    setValue('personalInfo.avatarFile', undefined as any, { shouldValidate: false })
    
    // Limpar preview
    clearSelection()
    
    toast({
      title: "Avatar Removido",
      description: "Foto de perfil removida com sucesso",
    })
  }

  // Função para buscar CEP
  const fetchAddressInfo = async (cepValue: string) => {
    const cleanCep = cepValue.replace(/\D/g, '')
    
    if (cleanCep.length !== 8) return

    setIsLoadingCep(true)
    try {
      const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors'
      })
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar CEP: ${response.status} ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (!data.erro) {
        setValue('location.city', data.localidade)
        setValue('location.state', data.uf)
      } else {
        console.warn('CEP não encontrado:', cleanCep)
      }
    } catch (error) {
      console.error("Erro ao buscar CEP:", error)
      // Se for erro de CORS/CSP, tentar buscar via API proxy
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        console.error('Erro de rede/CORS ao buscar CEP. Verifique o CSP ou considere usar uma API proxy.')
      }
    } finally {
      setIsLoadingCep(false)
    }
  }

  const handleCepChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '')
    const formattedCep = value.replace(/(\d{5})(\d{3})/, '$1-$2')
    setValue('location.cep', formattedCep)
    
    if (value.length === 8) {
      fetchAddressInfo(value)
    }
  }

  // Debug: Log mudanças do avatar
  useEffect(() => {
    // Avatar state tracking removed for production
  }, [avatar, previewUrl, avatarSrc, selectedFile])

  return (
    <Card className="dark:bg-[#080210] border-gray-200 dark:border-[#1c1627]">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <User className="h-5 w-5 text-[#ff0074]" />
          Informações Pessoais
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Avatar Preview */}
        <div className="flex items-start gap-6">
          <div className="flex flex-col items-center space-y-3">
            {/* Avatar Display - Clicável */}
            <div 
              className="relative cursor-pointer group"
              onClick={handleAvatarClick}
            >
              <Avatar className="h-24 w-24 border-4 border-gray-200 dark:border-gray-700 shadow-lg group-hover:border-[#ff0074]/50 transition-colors">
                {avatarSrc ? (
                  <AvatarImage 
                    src={avatarSrc} 
                    alt={name || "Avatar"}
                    className="object-cover object-center group-hover:opacity-80 transition-opacity"
                    onLoad={() => {}}
                    onError={(e) => {
                      // Remover src inválida apenas se for um URL salvo (não preview)
                      if (avatar && !previewUrl) {
                        setValue('personalInfo.avatar', '', { shouldValidate: false })
                      }
                    }}
                  />
                ) : null}
                <AvatarFallback className="bg-gradient-to-br from-[#ff0074] to-[#9810fa] text-white text-lg font-semibold group-hover:from-[#ff0074]/80 group-hover:to-[#9810fa]/80 transition-colors">
                  {getInitials(name)}
                </AvatarFallback>
              </Avatar>
              
              {/* Overlay de seleção */}
              <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <Upload className="h-6 w-6 text-white" />
              </div>
              
              {/* Loading overlay */}
              {isProcessing && (
                <div className="absolute inset-0 bg-black/70 rounded-full flex items-center justify-center">
                  <Loader2 className="h-6 w-6 text-white animate-spin" />
                </div>
              )}
            </div>

          </div>

          {/* Upload Info e Controles */}
          <div className="flex-1 space-y-4">
            <div>
              <Label className="text-sm font-medium">Foto do Perfil</Label>
              <p className="text-xs text-gray-500">
                Clique no avatar para selecionar uma foto
              </p>
              <p className="text-xs text-gray-400">
                JPG, PNG até 5MB • Enviado ao salvar
              </p>
             
            </div>
            
            {/* Botão remover (apenas quando há avatar ou arquivo selecionado) */}
            {(avatarSrc || selectedFile) && (
              <div className="flex gap-3">
                <Button 
                  type="button"
                  variant="ghost" 
                  size="sm"
                  onClick={removeAvatar}
                  disabled={isProcessing}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-2" />
                  Remover Foto
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Nome */}
        <div className="space-y-2">
          <Label htmlFor="name" className="text-sm font-medium">
            Nome Completo *
          </Label>
          <Input
            id="name"
            placeholder="Ex: João Silva"
            {...register('personalInfo.name', { required: 'Nome é obrigatório' })}
            className={errors.personalInfo?.name ? 'border-red-500' : ''}
          />
          {errors.personalInfo?.name && (
            <p className="text-sm text-red-500">{errors.personalInfo.name.message}</p>
          )}
        </div>

        {/* Idade e Gênero */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="age" className="text-sm font-medium">Idade</Label>
            <Input
              id="age"
              type="number"
              min="13"
              max="100"
              placeholder="Ex: 25"
              {...register('personalInfo.age', { 
                valueAsNumber: true,
                min: { value: 13, message: 'Idade mínima: 13 anos' },
                max: { value: 100, message: 'Idade máxima: 100 anos' }
              })}
              className={errors.personalInfo?.age ? 'border-red-500' : ''}
            />
            {errors.personalInfo?.age && (
              <p className="text-sm text-red-500">{errors.personalInfo.age.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="gender" className="text-sm font-medium">Gênero</Label>
            <Select 
              value={gender || ''} 
              onValueChange={(value) => setValue('personalInfo.gender', value as 'Masculino' | 'Feminino')}
            >
              <SelectTrigger className={errors.personalInfo?.gender ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione o gênero" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Masculino">Masculino</SelectItem>
                <SelectItem value="Feminino">Feminino</SelectItem>
              </SelectContent>
            </Select>
            {errors.personalInfo?.gender && (
              <p className="text-sm text-red-500">{errors.personalInfo.gender.message}</p>
            )}
          </div>
        </div>

        {/* Biografia */}
        <div className="space-y-2">
          <Label htmlFor="bio" className="text-sm font-medium">Biografia</Label>
          <Textarea
            id="bio"
            placeholder="Conte um pouco sobre você e seu trabalho como influenciador..."
            rows={4}
            {...register('personalInfo.bio')}
            className={`resize-none ${errors.personalInfo?.bio ? 'border-red-500' : ''}`}
          />
          
          {errors.personalInfo?.bio && (
            <p className="text-sm text-red-500">{errors.personalInfo.bio.message}</p>
          )}
        </div>

        {/* Verificado e Promotes Traders */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Perfil Verificado */}
          <div className="flex items-center space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <Switch
              id="verified"
              checked={verified || false}
              onCheckedChange={(checked) => setValue('personalInfo.verified', checked)}
            />
            <div className="flex-1">
              <Label 
                htmlFor="verified" 
                className="text-sm font-medium cursor-pointer flex items-center gap-2"
              >
                 
                Perfil Exclusivo
              </Label>
            </div>
          </div>

          {/* Promotes Traders */}
          <div className="flex items-center space-x-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <Switch
              id="promotesTraders"
              checked={promotesTraders || false}
              onCheckedChange={(checked) => setValue('business.promotesTraders', checked)}
            />
            <div className="flex-1">
              <Label 
                htmlFor="promotesTraders" 
                className="text-sm font-medium cursor-pointer flex items-center gap-2"
              >
               
                Promove Traders
              </Label>
            </div>
          </div>
        </div>

        {/* Informações de Agência */}
        <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <Label className="text-sm font-semibold text-gray-900 dark:text-white">
              Informações de Agência
            </Label>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="agencyName" className="text-sm font-medium">Nome da Agência</Label>
              <Input
                id="agencyName"
                {...register('business.agencyName')}
                placeholder="Nome da agência representante"
                className="text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="responsibleName" className="text-sm font-medium">Nome do Responsável</Label>
              <Input
                id="responsibleName"
                {...register('business.responsibleName')}
                placeholder="Nome do contato principal"
                className="text-sm"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="responsibleCapturer" className="text-sm font-medium">Responsável pela Captação</Label>
            <Input
              id="responsibleCapturer"
              {...register('business.responsibleCapturer')}
              placeholder="Quem captou este influenciador"
              className="text-sm"
            />
          </div>
        </div>

        {/* Seção de Localização */}
        <div className="space-y-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-[#ff0074]" />
            <Label className="text-lg font-semibold text-gray-900 dark:text-white">
              Localização
            </Label>
          </div>

          {/* CEP */}
          <div className="space-y-2">
            <Label htmlFor="cep" className="text-sm font-medium">
              CEP <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="cep"
                value={cep || ''}
                onChange={handleCepChange}
                placeholder="00000-000"
                maxLength={9}
                className={errors.location?.cep ? 'border-red-500' : ''}
              />
              {isLoadingCep && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                </div>
              )}
              {!isLoadingCep && cep && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
              )}
            </div>
            {errors.location?.cep && (
              <p className="text-sm text-red-500">{errors.location.cep.message}</p>
            )}
            <p className="text-xs text-gray-500">
              Digite o CEP para preenchimento automático da cidade e estado
            </p>
          </div>

          {/* Cidade e Estado */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city" className="text-sm font-medium">
                Cidade <span className="text-red-500">*</span>
              </Label>
              <Input
                id="city"
                {...register('location.city')}
                placeholder="Nome da cidade"
                className={errors.location?.city ? 'border-red-500' : ''}
                readOnly={isLoadingCep}
              />
              {errors.location?.city && (
                <p className="text-sm text-red-500">{errors.location.city.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state" className="text-sm font-medium">
                Estado <span className="text-red-500">*</span>
              </Label>
              <Select
                value={watch('location.state')}
                onValueChange={(value) => setValue('location.state', value)}
                disabled={isLoadingCep}
              >
                <SelectTrigger className={errors.location?.state ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Selecione o estado" />
                </SelectTrigger>
                <SelectContent>
                  {STATES.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.location?.state && (
                <p className="text-sm text-red-500">{errors.location.state.message}</p>
              )}
            </div>
          </div>

          {/* País */}
          <div className="space-y-2">
            <Label htmlFor="country" className="text-sm font-medium">País</Label>
            <Input
              id="country"
              {...register('location.country')}
              placeholder="Brasil"
              defaultValue="Brasil"
            />
          </div>
        </div>

        {/* Seção de Contato */}
        <div className="space-y-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-[#ff0074]" />
            <Label className="text-lg font-semibold text-gray-900 dark:text-white">
              Contato
            </Label>
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              E-mail <span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              type="email"
              {...register('contact.email')}
              placeholder="<EMAIL>"
              className={errors.contact?.email ? 'border-red-500' : ''}
            />
            {errors.contact?.email && (
              <p className="text-sm text-red-500">{errors.contact.email.message}</p>
            )}
            <p className="text-xs text-gray-500">
              E-mail principal para contato comercial
            </p>
          </div>

          {/* WhatsApp */}
          <div className="space-y-2">
            <Label htmlFor="whatsapp" className="text-sm font-medium">
              WhatsApp
            </Label>
            <div className="relative">
              <PhoneInput
                defaultCountry="br"
                value={whatsapp || ''}
                onChange={(phone) => setValue('contact.whatsapp', phone || '')}
                inputClassName="flex h-10 w-full rounded-md border border-input dark:border-[#1c1627] dark:bg-[#080210] bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="(11) 99999-9999"
                forceDialCode
                disableDialCodeAndPrefix={false}
              />
            </div>
            <p className="text-xs text-gray-500">
              Número do WhatsApp para contato direto
            </p>
          </div>
        </div>

        {/* Resumo de Localização e Contato */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <MapPin className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-blue-900 dark:text-blue-100">
                Localização do Influenciador
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                {watch('location.city') && watch('location.state') 
                  ? `${watch('location.city')} - ${watch('location.state')}, ${watch('location.country') || 'Brasil'}`
                  : 'Preencha os dados de localização'
                }
              </p>
              {watch('contact.email') && (
                <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                  📧 {watch('contact.email')}
                </p>
              )}
              {whatsapp && (
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  📱 {whatsapp}
                </p>
              )}
            </div>
          </div>
        </div>

      </CardContent>
    </Card>
  )
} 

