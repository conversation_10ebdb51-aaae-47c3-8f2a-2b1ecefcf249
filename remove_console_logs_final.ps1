# Script final para remover console.log do use-influencer-form.ts
param(
    [string]$FilePath = "hooks/use-influencer-form.ts"
)

Write-Host "🧹 Iniciando limpeza final do arquivo $FilePath..." -ForegroundColor Yellow

if (-not (Test-Path $FilePath)) {
    Write-Host "❌ Arquivo não encontrado: $FilePath" -ForegroundColor Red
    exit 1
}

# Ler conteúdo do arquivo
$content = Get-Content $FilePath -Raw

# Backup do arquivo
$backupPath = "$FilePath.backup-" + (Get-Date -Format "yyyyMMdd-HHmmss")
Copy-Item $FilePath $backupPath
Write-Host "📁 Backup criado: $backupPath" -ForegroundColor Green

# Contador de logs removidos
$removedCount = 0

# Remover todos os tipos de console.log
$patterns = @(
    # Console.log simples
    'console\.log\([^)]*\);?',
    # Console.error
    'console\.error\([^)]*\);?',
    # Console.warn
    'console\.warn\([^)]*\);?',
    # Console.info
    'console\.info\([^)]*\);?',
    # Console.debug
    'console\.debug\([^)]*\);?'
)

foreach ($pattern in $patterns) {
    $matches = [regex]::Matches($content, $pattern, [Text.RegularExpressions.RegexOptions]::Multiline)
    $removedCount += $matches.Count
    
    # Remover as linhas encontradas
    $content = [regex]::Replace($content, $pattern, '', [Text.RegularExpressions.RegexOptions]::Multiline)
}

# Limpar linhas vazias extras criadas pela remoção
$content = [regex]::Replace($content, '\n\s*\n\s*\n', "`n`n", [Text.RegularExpressions.RegexOptions]::Multiline)

# Escrever arquivo limpo
$content | Set-Content $FilePath -NoNewline

Write-Host "✅ Limpeza concluída!" -ForegroundColor Green
Write-Host "📊 Console.log removidos: $removedCount" -ForegroundColor Cyan
Write-Host "💾 Arquivo limpo salvo: $FilePath" -ForegroundColor Green
Write-Host "📁 Backup disponível: $backupPath" -ForegroundColor Blue

# Verificar se ainda existem console.log
$remaining = Select-String -Path $FilePath -Pattern "console\.(log|error|warn|info|debug)" -AllMatches
if ($remaining) {
    Write-Host "⚠️ Ainda restam $($remaining.Count) console.log no arquivo:" -ForegroundColor Yellow
    $remaining | ForEach-Object { Write-Host "   Linha $($_.LineNumber): $($_.Line.Trim())" -ForegroundColor Yellow }
} else {
    Write-Host "🎉 Nenhum console.log encontrado! Arquivo completamente limpo." -ForegroundColor Green
} 