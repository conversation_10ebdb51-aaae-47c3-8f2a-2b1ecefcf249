import { NextRequest, NextResponse } from 'next/server';

// Configuração do Firebase
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Função para inicializar Firebase com importação dinâmica
async function initializeFirebase() {
  const { initializeApp, getApps } = await import('firebase/app');
  if (!getApps().length) {
    initializeApp(firebaseConfig);
  }
}

// Função para obter storage com importação dinâmica
async function getFirebaseStorage() {
  const { getStorage } = await import('firebase/storage');
  return getStorage();
}

// Função para criar referência com importação dinâmica
async function createStorageRef(storage: any, path: string) {
  const { ref } = await import('firebase/storage');
  return ref(storage, path);
}

// Função para obter URL de download com importação dinâmica
async function getStorageDownloadURL(storageRef: any) {
  const { getDownloadURL } = await import('firebase/storage');
  return getDownloadURL(storageRef);
}

/**
 * API Route para servir screenshots do Firebase Storage
 * Redireciona para Firebase Storage com token de acesso
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Inicializar Firebase
    await initializeFirebase();
    
    // Await params conforme requerido pelo Next.js
    const resolvedParams = await params;
    
    // Reconstituir o caminho do arquivo
    const filePath = resolvedParams.path.join('/');
    
    console.log(`📸 Buscando screenshot: ${filePath}`);
    
    // Obter storage e criar referência para o arquivo no Firebase Storage
    const storage = await getFirebaseStorage();
    const fileRef = await createStorageRef(storage, filePath);
    
    try {
      // Obter URL de download do Firebase Storage
      const downloadURL = await getStorageDownloadURL(fileRef);
      
      console.log(`✅ Screenshot encontrado, redirecionando para: ${downloadURL}`);
      
      // Redirecionar para a URL do Firebase Storage
      return NextResponse.redirect(downloadURL, 302);
      
    } catch (storageError: any) {
      console.log(`❌ Screenshot não encontrado no Firebase Storage: ${filePath}`);
      
             // Se o arquivo não existir no Firebase, retornar uma imagem SVG como placeholder
       const placeholderSvg = `
         <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
           <rect width="100%" height="100%" fill="#f3f4f6"/>
           <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#6b7280" font-family="Arial" font-size="14">
             Screenshot não encontrado
           </text>
         </svg>
       `;
       
       return new NextResponse(placeholderSvg, {
         status: 200,
         headers: {
           'Content-Type': 'image/svg+xml',
           'Cache-Control': 'public, max-age=3600',
         },
       });
    }
    
  } catch (error) {
    console.error('❌ Erro ao buscar screenshot do Firebase Storage:', error);
    
    // Em caso de erro, retornar uma imagem SVG como placeholder
    const errorSvg = `
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#fef2f2"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#dc2626" font-family="Arial" font-size="14">
          Erro ao carregar screenshot
        </text>
      </svg>
    `;
    
    return new NextResponse(errorSvg, {
      status: 200,
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=300',
      },
    });
  }
}

/**
 * Configuração para permitir cache das imagens
 */
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic'; 