'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, PERMISSIONS, ROLE_PERMISSIONS } from '@/types/user';
import { Brand } from '@/types/brand';
import { useRouter } from 'next/navigation';
import { useGlobalLoader } from '@/components/ui/loader';

interface AuthContextType {
  currentUser: User | null;
  currentBrand: Brand | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshUser: () => Promise<void>;
  switchBrand: (brandId: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export { AuthContext };

export function AuthProvider({ children }: { children: ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  
  // Hook para controlar loader global
  const { showLoader, hideLoader } = useGlobalLoader();

  // Verificar autenticação ao carregar
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      
      // ✅ REMOVIDO: API desnecessária - Clerk já fornece isso no frontend
      // Usar hooks nativos do Clerk para verificar autenticação
      
      // Simular verificação para compatibilidade
      if (!user) {
        setIsLoading(false);
        return;
      }
      
      const data = await response.json();
      
      if (data.user) {
        setCurrentUser(data.user);
        
        // Se houver dados da marca, definir também
        if (data.brand) {
          setCurrentBrand(data.brand);
        }
      }
      
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Autenticar usuário usando a rota /api/auth/login
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ email, password })
      });
      
      if (!response.ok) {
        return false;
      }
      
      const data = await response.json();
      
      if (data.success && data.user) {
        setCurrentUser(data.user);
        
        // Se houver dados da marca, definir também
        if (data.brand) {
          setCurrentBrand(data.brand);
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro no login:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log('🚪 [AUTH] Iniciando processo de logout...');
      showLoader('Fazendo logout...');
      
      // Fazer logout usando a rota /api/auth/logout
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
      
      console.log('✅ [AUTH] Logout API finalizado');
      
      // Limpar estado local
      setCurrentUser(null);
      setCurrentBrand(null);
      
      console.log('🔄 [AUTH] Estados locais limpos');
      
      // Dar um breve tempo para que as mudanças sejam processadas
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log('🚀 [AUTH] Redirecionando para login...');
      
      // Esconder loader antes do redirecionamento
      hideLoader();
      
      // Redirecionar para login
      router.push('/login');
      
    } catch (error) {
      console.error('❌ [AUTH] Erro no logout:', error);
      
      // Mesmo com erro, limpar estado local
      setCurrentUser(null);
      setCurrentBrand(null);
      
      // Esconder loader mesmo em caso de erro
      hideLoader();
      
      // Redirecionar mesmo com erro
      router.push('/login');
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!currentUser) return false;
    
    // Verificar permissões do papel com verificação de tipo segura
    const roleKey = currentUser.role as keyof typeof ROLE_PERMISSIONS;
    const rolePermissions = ROLE_PERMISSIONS[roleKey];
    
    if (rolePermissions && rolePermissions.includes(permission as any)) {
      return true;
    }
    
    // TODO: Implementar verificação de permissões com Firebase Auth
    
    return false;
  };

  const hasRole = (role: string): boolean => {
    return currentUser?.role === role;
  };

  const refreshUser = async (): Promise<void> => {
    // ✅ OTIMIZADO: Usar dados do Clerk diretamente (sem API desnecessária)
    // Os dados do usuário já estão disponíveis via hooks do Clerk
    
    // Simular carregamento para compatibilidade
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // O estado será atualizado automaticamente pelos hooks do Clerk
    console.log('🔄 [AUTH] Refresh user - usando dados nativos do Clerk');
  };

  const switchBrand = async (brandId: string): Promise<boolean> => {
    if (!currentUser) return false;
    
    try {
      // Sistema de marcas removido - função não é mais necessária
      console.warn('Função switchBrand removida - sistema de marcas não existe mais');
      return false;
    } catch (error) {
      console.error('Erro ao trocar marca:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    currentUser,
    currentBrand,
    isAuthenticated: !!currentUser,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    refreshUser,
    switchBrand
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook para verificar permissões específicas
export function usePermission(permission: string) {
  const { hasPermission } = useAuth();
  return hasPermission(permission);
}

// Hook para verificar papéis específicos
export function useRole(role: string) {
  const { hasRole } = useAuth();
  return hasRole(role);
}

// Hook para dados filtrados (sistema simplificado)
export function useFilteredData() {
  const { currentUser } = useAuth();
  
  const filterData = (data: any[]) => {
    if (currentUser?.role === 'admin') {
      return data; // Admin vê todos os dados
    }
    
    // Outros usuários veem dados baseados em suas permissões
    return data.filter(item => item.userId === currentUser?.id || currentUser?.role === 'admin');
  };
  
  return {
    filterData,
    isAdmin: currentUser?.role === 'admin'
  };
}

// Hook para proteção de rotas
export function useRouteProtection() {
  const { currentUser, isLoading } = useAuth();
  const router = useRouter();
  
  const requireAuth = () => {
    if (!isLoading && !currentUser) {
      router.push('/login');
      return false;
    }
    return true;
  };
  
  const requireRole = (role: string) => {
    if (!requireAuth()) return false;
    
    if (currentUser?.role !== role) {
      router.push('/unauthorized');
      return false;
    }
    return true;
  };
  
  const requirePermission = (permission: string) => {
    if (!requireAuth()) return false;
    
    // Verificação de tipo segura para evitar erros de tipagem
    const roleKey = currentUser!.role as keyof typeof ROLE_PERMISSIONS;
    const rolePermissions = ROLE_PERMISSIONS[roleKey];
    
    if (!rolePermissions || !rolePermissions.includes(permission as any)) {
      router.push('/unauthorized');
      return false;
    }
    return true;
  };
  
  return {
    requireAuth,
    requireRole,
    requirePermission
  };
}

