import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { Influencer } from '../types/influencer';
import { InfluencerFinancial } from '../types/influencer-financial';
import { Campaign } from '../types/campaign';
import { Category } from '../types/category';
import { Brand } from '../types/brand';

// Definindo a interface Filter aqui para evitar problemas de importação
interface Filter {
  id?: string;
  name: string;
  location?: string;
  minFollowers?: number;
  maxFollowers?: number;
  minRating?: number;
  verifiedOnly?: boolean;
  availableOnly?: boolean;
  platforms?: {
    instagram?: boolean;
    tiktok?: boolean;
    youtube?: boolean;
    twitter?: boolean;
  };
  userId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicialize o Firebase apenas se não estiver já inicializado
const firebaseApp = getApps().length === 0 
  ? initializeApp({
      credential: cert(serviceAccount)
    })
  : getApps()[0];

// Exporte o Firestore para uso em toda a aplicação
export const db = getFirestore(firebaseApp);

// Coleções para cada tipo de dados
export const influencersCollection = db.collection('influencers');
export const financialsCollection = db.collection('influencer_financials');
export const campaignsCollection = db.collection('campaigns');
export const categoriesCollection = db.collection('categories');
export const brandsCollection = db.collection('brands');
export const filtersCollection = db.collection('filters');

// FUNÇÕES PARA INFLUENCIADORES (DADOS BÁSICOS)

// Buscar todos os influenciadores
export async function getAllInfluencers() {
  try {
    const snapshot = await influencersCollection.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as Influencer[];
  } catch (error) {
    console.error('Erro ao buscar influenciadores:', error);
    return [];
  }
}

// Buscar um influenciador por ID
export async function getInfluencerById(id: string) {
  try {
    const doc = await influencersCollection.doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return { 
      id: doc.id, 
      ...data,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate()
    } as Influencer;
  } catch (error) {
    console.error(`Erro ao buscar influenciador com ID ${id}:`, error);
    return null;
  }
}

// Buscar múltiplos influenciadores por IDs (otimizado com "where in")
export async function getInfluencersByIds(ids: string[]) {
  try {
    if (ids.length === 0) return [];
    
    // Firestore "where in" suporta até 10 itens por query
    const batchSize = 10;
    const batches: string[][] = [];
    
    for (let i = 0; i < ids.length; i += batchSize) {
      batches.push(ids.slice(i, i + batchSize));
    }
    
    const allInfluencers: Influencer[] = [];
    
    // Executar queries em paralelo
    const batchPromises = batches.map(async (batch) => {
      const snapshot = await influencersCollection.where('__name__', 'in', 
        batch.map(id => influencersCollection.doc(id))).get();
      
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data?.createdAt?.toDate(),
          updatedAt: data?.updatedAt?.toDate()
        } as Influencer;
      });
    });
    
    const batchResults = await Promise.all(batchPromises);
    
    // Combinar todos os resultados
    batchResults.forEach(batch => {
      allInfluencers.push(...batch);
    });
    
    return allInfluencers;
  } catch (error) {
    console.error('Erro ao buscar influenciadores por IDs:', error);
    return [];
  }
}

// Adicionar um novo influenciador
export async function addInfluencer(influencerData: Partial<Influencer>) {
  try {
    // 🔧 CORREÇÃO: Converter undefined para null (aceito pelo Firestore)
    const processedData: any = {};
    
    Object.entries(influencerData).forEach(([key, value]) => {
      processedData[key] = value === undefined ? null : value;
    });
    
    const now = new Date();
    const dataToAdd = {
      ...processedData,
      createdAt: now,
      updatedAt: now
    };
    
    console.log('📝 [Firebase] Dados processados para criação:', dataToAdd);
    
    const docRef = await influencersCollection.add(dataToAdd);
    return { id: docRef.id, ...dataToAdd } as Influencer;
  } catch (error) {
    console.error('Erro ao adicionar influenciador:', error);
    throw error;
  }
}

// Atualizar um influenciador existente
export async function updateInfluencer(id: string, influencerData: Partial<Influencer>) {
  try {
    // 🔧 CORREÇÃO: Processar dados sem converter undefined para null em campos obrigatórios
    const processedData: any = {};
    
    // Lista de campos obrigatórios que não devem ser null
    const requiredFields = ['name', 'country', 'state', 'city', 'gender', 'category'];
    
    Object.entries(influencerData).forEach(([key, value]) => {
      // Para campos obrigatórios, só incluir se não for undefined
      if (requiredFields.includes(key)) {
        if (value !== undefined) {
          processedData[key] = value;
        }
      } else {
        // Para campos opcionais, converter undefined para null (aceito pelo Firestore)
        processedData[key] = value === undefined ? null : value;
      }
    });
    
    const dataToUpdate = {
      ...processedData,
      updatedAt: new Date()
    };
    
    console.log('📝 [Firebase] Dados processados para atualização:', dataToUpdate);
    
    // Atualizar no Firestore
    await influencersCollection.doc(id).update(dataToUpdate);
    
    // 🔧 CORREÇÃO CRÍTICA: Buscar e retornar o documento completo após a atualização
    const updatedDoc = await influencersCollection.doc(id).get();
    if (!updatedDoc.exists) {
      throw new Error('Influenciador não encontrado após atualização');
    }
    
    const fullData = updatedDoc.data();
    return {
      id: updatedDoc.id,
      ...fullData,
      createdAt: fullData?.createdAt?.toDate(),
      updatedAt: fullData?.updatedAt?.toDate()
    } as Influencer;
  } catch (error) {
    console.error(`Erro ao atualizar influenciador com ID ${id}:`, error);
    throw error;
  }
}

// Remover um influenciador
export async function deleteInfluencer(id: string) {
  try {
    await influencersCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir influenciador com ID ${id}:`, error);
    throw error;
  }
}

// FUNÇÕES PARA DADOS FINANCEIROS DE INFLUENCIADORES

// Buscar todos os dados financeiros
export async function getAllFinancials() {
  try {
    const snapshot = await financialsCollection.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as InfluencerFinancial[];
  } catch (error) {
    console.error('Erro ao buscar dados financeiros:', error);
    return [];
  }
}

// Buscar dados financeiros por ID do influenciador
export async function getFinancialByInfluencerId(influencerId: string) {
  try {
    const snapshot = await financialsCollection.where('influencerId', '==', influencerId).get();
    if (snapshot.empty) return null;
    
    const doc = snapshot.docs[0];
    const data = doc.data();
    return { 
      id: doc.id, 
      ...data,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate() 
    } as InfluencerFinancial;
  } catch (error) {
    console.error(`Erro ao buscar dados financeiros para influenciador ${influencerId}:`, error);
    return null;
  }
}

// Buscar dados financeiros por ID próprio
export async function getFinancialById(id: string) {
  try {
    const doc = await financialsCollection.doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return { 
      id: doc.id, 
      ...data,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate() 
    } as InfluencerFinancial;
  } catch (error) {
    console.error(`Erro ao buscar dados financeiros com ID ${id}:`, error);
    return null;
  }
}

// Adicionar novos dados financeiros
export async function addFinancial(financialData: Partial<InfluencerFinancial>) {
  try {
    const now = new Date();
    const dataToAdd = {
      ...financialData,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await financialsCollection.add(dataToAdd);
    return { id: docRef.id, ...dataToAdd } as InfluencerFinancial;
  } catch (error) {
    console.error('Erro ao adicionar dados financeiros:', error);
    throw error;
  }
}

// Atualizar dados financeiros existentes
export async function updateFinancial(id: string, financialData: Partial<InfluencerFinancial>) {
  try {
    const dataToUpdate = {
      ...financialData,
      updatedAt: new Date()
    };
    
    await financialsCollection.doc(id).update(dataToUpdate);
    return { id, ...dataToUpdate } as InfluencerFinancial;
  } catch (error) {
    console.error(`Erro ao atualizar dados financeiros com ID ${id}:`, error);
    throw error;
  }
}

// Remover dados financeiros
export async function deleteFinancial(id: string) {
  try {
    await financialsCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir dados financeiros com ID ${id}:`, error);
    throw error;
  }
}

// FUNÇÕES PARA CAMPANHAS

// Buscar todas as campanhas
export async function getAllCampaigns() {
  try {
    console.log('🔍 Iniciando busca de campanhas no Firestore...');
    const snapshot = await campaignsCollection.get();
    console.log('📊 Número de campanhas encontradas:', snapshot.docs.length);
    
    const campaigns = snapshot.docs.map(doc => {
      const data = doc.data();
      console.log('📋 Dados da campanha:', doc.id, data);
      return {
        id: doc.id,
        ...data,
        startDate: data.startDate?.toDate ? data.startDate.toDate() : data.startDate,
        endDate: data.endDate?.toDate ? data.endDate.toDate() : data.endDate,
        createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
        updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt
      };
    }) as Campaign[];
    
    console.log('✅ Campanhas processadas:', campaigns.length);
    return campaigns;
  } catch (error) {
    console.error('❌ Erro ao buscar campanhas:', error);
    return [];
  }
}

// Buscar uma campanha por ID
export async function getCampaignById(id: string) {
  try {
    const doc = await campaignsCollection.doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return { 
      id: doc.id, 
      ...data,
      startDate: data?.startDate?.toDate ? data.startDate.toDate() : data?.startDate,
      endDate: data?.endDate?.toDate ? data.endDate.toDate() : data?.endDate,
      createdAt: data?.createdAt?.toDate ? data.createdAt.toDate() : data?.createdAt,
      updatedAt: data?.updatedAt?.toDate ? data.updatedAt.toDate() : data?.updatedAt 
    } as Campaign;
  } catch (error) {
    console.error(`Erro ao buscar campanha com ID ${id}:`, error);
    return null;
  }
}

// Adicionar nova campanha
export async function addCampaign(campaignData: Partial<Campaign>) {
  try {
    const now = new Date();
    const dataToAdd = {
      ...campaignData,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await campaignsCollection.add(dataToAdd);
    return { id: docRef.id, ...dataToAdd } as Campaign;
  } catch (error) {
    console.error('Erro ao adicionar campanha:', error);
    throw error;
  }
}

// Atualizar campanha existente
export async function updateCampaign(id: string, campaignData: Partial<Campaign>) {
  try {
    const dataToUpdate = {
      ...campaignData,
      updatedAt: new Date()
    };
    
    await campaignsCollection.doc(id).update(dataToUpdate);
    return { id, ...dataToUpdate } as Campaign;
  } catch (error) {
    console.error(`Erro ao atualizar campanha com ID ${id}:`, error);
    throw error;
  }
}

// Remover campanha
export async function deleteCampaign(id: string) {
  try {
    await campaignsCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir campanha com ID ${id}:`, error);
    throw error;
  }
}

// FUNÇÕES PARA CATEGORIAS

// Buscar todas as categorias
export async function getAllCategories() {
  try {
    const snapshot = await categoriesCollection.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as Category[];
  } catch (error) {
    console.error('Erro ao buscar categorias:', error);
    return [];
  }
}

// Buscar categorias de primeiro nível (sem pai)
export async function getRootCategories() {
  try {
    const snapshot = await categoriesCollection
      .where('parentId', '==', null)
      .orderBy('order', 'asc')
      .get();
      
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as Category[];
  } catch (error) {
    console.error('Erro ao buscar categorias de primeiro nível:', error);
    return [];
  }
}

// Buscar subcategorias de uma categoria pai
export async function getSubcategories(parentId: string) {
  try {
    const snapshot = await categoriesCollection
      .where('parentId', '==', parentId)
      .orderBy('order', 'asc')
      .get();
      
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as Category[];
  } catch (error) {
    console.error(`Erro ao buscar subcategorias da categoria ${parentId}:`, error);
    return [];
  }
}

// Buscar uma categoria por ID
export async function getCategoryById(id: string) {
  try {
    const doc = await categoriesCollection.doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return { 
      id: doc.id, 
      ...data,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate() 
    } as Category;
  } catch (error) {
    console.error(`Erro ao buscar categoria com ID ${id}:`, error);
    return null;
  }
}

// Adicionar nova categoria
export async function addCategory(categoryData: Partial<Category>) {
  try {
    // Gerar slug se não fornecido
    if (categoryData.name && !categoryData.slug) {
      categoryData.slug = categoryData.name
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
    }
    
    const now = new Date();
    const dataToAdd = {
      ...categoryData,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await categoriesCollection.add(dataToAdd);
    return { id: docRef.id, ...dataToAdd } as Category;
  } catch (error) {
    console.error('Erro ao adicionar categoria:', error);
    throw error;
  }
}

// Atualizar categoria existente
export async function updateCategory(id: string, categoryData: Partial<Category>) {
  try {
    // Atualizar slug se o nome foi alterado
    if (categoryData.name && !categoryData.slug) {
      categoryData.slug = categoryData.name
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
    }
    
    const dataToUpdate = {
      ...categoryData,
      updatedAt: new Date()
    };
    
    await categoriesCollection.doc(id).update(dataToUpdate);
    return { id, ...dataToUpdate } as Category;
  } catch (error) {
    console.error(`Erro ao atualizar categoria com ID ${id}:`, error);
    throw error;
  }
}

// Remover categoria
export async function deleteCategory(id: string) {
  try {
    // Verificar se existem subcategorias
    const subcategories = await getSubcategories(id);
    if (subcategories.length > 0) {
      throw new Error('Não é possível excluir uma categoria que possui subcategorias.');
    }
    
    await categoriesCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir categoria com ID ${id}:`, error);
    throw error;
  }
}

// FUNÇÕES PARA MARCAS

// Buscar todas as marcas
export async function getAllBrands() {
  try {
    const snapshot = await brandsCollection.get();
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        firebaseId: doc.id, // ID real do documento
        id: data.id || doc.id, // Preserva o ID interno se existir, senão usa o ID do documento
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate()
      };
    });
  } catch (error) {
    console.error('Erro ao buscar marcas:', error);
    return [];
  }
}

// Buscar marcas por segmento
export async function getBrandsByIndustry(industry: string) {
  try {
    const snapshot = await brandsCollection
      .where('industry', '==', industry)
      .orderBy('name', 'asc')
      .get();
      
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        firebaseId: doc.id, // ID real do documento
        id: data.id || doc.id, // Preserva o ID interno se existir, senão usa o ID do documento
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate()
      };
    });
  } catch (error) {
    console.error(`Erro ao buscar marcas do segmento ${industry}:`, error);
    return [];
  }
}

// Buscar uma marca por ID
export async function getBrandById(id: string) {
  try {
    const doc = await brandsCollection.doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return { 
      ...data,
      firebaseId: doc.id, // ID real do documento
      id: data?.id || doc.id, // Preserva o ID interno se existir, senão usa o ID do documento
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate() 
    };
  } catch (error) {
    console.error(`Erro ao buscar marca com ID ${id}:`, error);
    return null;
  }
}

// Adicionar nova marca
export async function addBrand(brandData: Partial<Brand>) {
  try {
    const now = new Date();
    const dataToAdd = {
      ...brandData,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await brandsCollection.add(dataToAdd);
    return { id: docRef.id, ...dataToAdd } as Brand;
  } catch (error) {
    console.error('Erro ao adicionar marca:', error);
    throw error;
  }
}

// Atualizar marca existente
export async function updateBrand(id: string, brandData: Partial<Brand>) {
  try {
    const dataToUpdate = {
      ...brandData,
      updatedAt: new Date()
    };
    
    await brandsCollection.doc(id).update(dataToUpdate);
    return { id, ...dataToUpdate } as Brand;
  } catch (error) {
    console.error(`Erro ao atualizar marca com ID ${id}:`, error);
    throw error;
  }
}

// Remover marca
export async function deleteBrand(id: string) {
  try {
    await brandsCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir marca com ID ${id}:`, error);
    throw error;
  }
}

// FUNÇÕES PARA FILTROS SALVOS

// Buscar todos os filtros
export async function getAllFilters(userId?: string) {
  try {
    let query: any = filtersCollection;
    
    // Se um ID de usuário for fornecido, filtre por esse usuário
    if (userId) {
      query = query.where('userId', '==', userId);
    }
    
    // Ordenar por data de criação (mais recentes primeiro)
    query = query.orderBy('createdAt', 'desc');
    
    const snapshot = await query.get();
    
    return snapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate(),
      updatedAt: doc.data().updatedAt?.toDate()
    })) as Filter[];
  } catch (error) {
    console.error('Erro ao buscar filtros:', error);
    return [];
  }
}

// Buscar um filtro por ID
export async function getFilterById(id: string) {
  try {
    const doc = await filtersCollection.doc(id).get();
    if (!doc.exists) return null;
    
    const data = doc.data();
    return { 
      id: doc.id, 
      ...data,
      createdAt: data?.createdAt?.toDate(),
      updatedAt: data?.updatedAt?.toDate()
    } as Filter;
  } catch (error) {
    console.error(`Erro ao buscar filtro com ID ${id}:`, error);
    return null;
  }
}

// Adicionar um novo filtro
export async function addFilter(filterData: Partial<Filter>) {
  try {
    const now = new Date();
    const dataToAdd = {
      ...filterData,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await filtersCollection.add(dataToAdd);
    return { id: docRef.id, ...dataToAdd } as Filter;
  } catch (error) {
    console.error('Erro ao adicionar filtro:', error);
    throw error;
  }
}

// Atualizar um filtro existente
export async function updateFilter(id: string, filterData: Partial<Filter>) {
  try {
    const dataToUpdate = {
      ...filterData,
      updatedAt: new Date()
    };
    
    await filtersCollection.doc(id).update(dataToUpdate);
    return { id, ...dataToUpdate } as Filter;
  } catch (error) {
    console.error(`Erro ao atualizar filtro com ID ${id}:`, error);
    throw error;
  }
}

// Remover um filtro
export async function deleteFilter(id: string) {
  try {
    await filtersCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir filtro com ID ${id}:`, error);
    throw error;
  }
}


