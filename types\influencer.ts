// Modelos de dados para influenciadores

import { BaseDocument, CreateData, UpdateData } from './base';

// Interface para rede social genérica
export interface SocialNetwork {
  username: string;
  followers: number;
  avgViews: number;
}

// Interface para Instagram com campos específicos
export interface Instagram extends SocialNetwork {
  reelsViews: number;
}

// Interface para TikTok
export interface TikTok extends SocialNetwork {
  // Campos específicos do TikTok, se necessário
}

// Interface para YouTube
export interface YouTube extends SocialNetwork {
  // Campos específicos do YouTube, se necessário
}

// Interface para outras redes sociais possíveis
export interface OtherSocialNetwork extends SocialNetwork {
  platform: string;  // Nome da plataforma (Facebook, Kwai, Twitch, etc.)
}

// Interface para gênero da audiência
export interface AudienceGender {
  male: number;      // Percentual masculino
  female: number;    // Percentual feminino
  other: number;     // Percentual outros
}

// Interface para objetos de categoria
export interface Category {
  id: string;       // ID único da categoria
  name: string;     // Nome da categoria
  slug: string;     // Slug URL-friendly
  description?: string; // Descrição opcional
  color?: string;   // Cor da categoria (código hexadecimal)
  icon?: string;    // Nome do ícone
  order?: number;   // Ordem de exibição
}

// Interface para etiquetas (tags)
export interface Tag {
  id: string;       // ID único da etiqueta
  name: string;     // Nome da etiqueta
  color: string;    // Cor da etiqueta (código hexadecimal)
  createdAt: Date;  // Data de criação
}

// Interface para anotações
export interface Note {
  id: string;       // ID único da anotação
  title: string;    // Título da anotação
  content: string;  // Conteúdo da anotação
  type?: string;    // Tipo de anotação (reunião, negociação, etc.)
  influencerId: string; // ID do influenciador associado à anotação
  createdAt: Date;  // Data de criação
  updatedAt: Date;  // Data de atualização
  createdBy?: string; // Usuário que criou a anotação
}

// Interface principal para dados básicos de influenciadores
export interface Influencer extends BaseDocument {
  // Dados pessoais
  name: string;                // Nome do influenciador
  email?: string;              // Endereço de e-mail
  phone?: string;              // Telefone de contato
  whatsapp?: string;           // Número de WhatsApp
  
  // Localização
  country: string;             // País
  state: string;               // Estado
  city: string;                // Cidade
  location?: string;           // Localização formatada (Cidade/Estado)
  
  // Dados demográficos
  age?: number;                // Idade
  gender: 'male' | 'female' | 'other' | 'not_specified'; // Gênero
  bio?: string;                // Biografia/descrição
  
  // Visuais
  avatar?: string;             // URL da foto do avatar
  backgroundImage?: string;    // URL da imagem de fundo
  gradient?: string;           // Gradiente personalizado para o card
  
  // Categorização
  category: string;            // Categoria principal
  categories: string[];        // IDs das categorias
  mainCategories: string[];    // IDs das categorias principais
  mainCategoriesData?: Category[]; // Dados completos das categorias (populado via join)
  
  // Métricas e engajamento
  totalFollowers: number;      // Total de seguidores em todas as redes
  totalViews: number;          // Total de visualizações em todas as redes
  engagementRate: number;      // Taxa de engajamento (%)
  rating: number;              // Classificação do influenciador (0-5)
  
  // Status e verificação
  isVerified: boolean;         // Indica se o influenciador é verificado
  isAvailable: boolean;        // Disponível para novas campanhas
  status: 'active' | 'inactive' | 'pending' | 'blocked';
  
  // Distribuição de gênero da audiência
  audienceGender: AudienceGender;
  
  // Redes sociais - campos diretos (nova estrutura)
  instagramUsername?: string;
  instagramFollowers?: number;
  instagramEngagementRate?: number;
  instagramAvgViews?: number;
  instagramStoriesViews?: number;
  instagramReelsViews?: number;
  
  tiktokUsername?: string;
  tiktokFollowers?: number;
  tiktokEngagementRate?: number;
  tiktokAvgViews?: number;
  tiktokVideoViews?: number;
  
  youtubeUsername?: string;
  youtubeFollowers?: number;
  youtubeSubscribers?: number;
  youtubeEngagementRate?: number;
  youtubeAvgViews?: number;
  youtubeShortsViews?: number;
  youtubeLongFormViews?: number;
  
  facebookUsername?: string;
  facebookFollowers?: number;
  facebookEngagementRate?: number;
  facebookAvgViews?: number;
  facebookViews?: number;
  facebookReelsViews?: number;
  facebookStoriesViews?: number;
  
  twitchUsername?: string;
  twitchFollowers?: number;
  twitchEngagementRate?: number;
  twitchViews?: number;
  
  kwaiUsername?: string;
  kwaiFollowers?: number;
  kwaiEngagementRate?: number;
  kwaiViews?: number;
  
  // Redes sociais - estrutura antiga (deprecada, mantida para compatibilidade)
  socialNetworks?: {
    instagram?: Instagram;
    tiktok?: TikTok;
    youtube?: YouTube;
    twitter?: SocialNetwork;
    facebook?: SocialNetwork;
    others?: OtherSocialNetwork[];
  };
  
  // Conteúdo e especialização
  contentTypes: string[];      // Tipos de conteúdo (lifestyle, tech, beauty, etc.)
  contentLanguages: string[];  // Idiomas do conteúdo
  specialties: string[];       // Especialidades específicas
  
  // Configurações profissionais
  promotesTraders: boolean;    // Indica se promove traders
  responsibleName?: string;    // Nome do responsável/agência
  agencyName?: string;         // Nome da agência
  responsibleCapturer?: string; // Nome do responsável pela captação
  mainNetwork?: string;        // Rede social principal do influenciador (deprecated - usar mainPlatform)
  mainPlatform?: string;       // Rede social principal do influenciador
  
  // Dados relacionados (populados via join - não salvos diretamente)
  financialData?: {
    brandHistory?: {
      instagram?: string[];
      tiktok?: string[];
      youtube?: string[];
    }
  };
  
  // Denormalização seletiva - Dados financeiros básicos para queries eficientes
  pricing?: {
    hasFinancialData: boolean;           // Indica se possui dados financeiros
    priceRange: 'low' | 'medium' | 'high' | 'premium'; // Faixa de preço
    avgPrice: number;                    // Preço médio geral
    mainService: {                       // Serviço principal
      platform: 'instagram' | 'tiktok' | 'youtube'; // Plataforma principal
      serviceType: string;               // Tipo de serviço (story, reel, video, etc)
      price: number;                     // Preço do serviço principal
    };
    lastPriceUpdate: Date;               // Data da última atualização dos preços
    isNegotiable: boolean;               // Se o preço é negociável
  };
  
  // Referências a outras entidades
  tags: string[];              // IDs das tags associadas
  notes: string[];             // IDs das anotações
}

// Interface para criação de novo influencer (userId será adicionado automaticamente)
export interface CreateInfluencerData {
  name: string;
  email?: string;
  phone?: string;
  whatsapp?: string;
  country: string;
  state: string;
  city: string;
  location?: string;
  age?: number;
  gender: 'male' | 'female' | 'other' | 'not_specified';
  bio?: string;
  avatar?: string;
  backgroundImage?: string;
  gradient?: string;
  category: string;
  categories: string[];
  mainCategories: string[];
  totalFollowers?: number;
  totalViews?: number;
  engagementRate?: number;
  rating?: number;
  isVerified?: boolean;
  isAvailable?: boolean;
  status?: 'active' | 'inactive' | 'pending' | 'blocked';
  audienceGender?: AudienceGender;
  socialNetworks?: {
    instagram?: Omit<Instagram, 'id'>;
    tiktok?: Omit<TikTok, 'id'>;
    youtube?: Omit<YouTube, 'id'>;
    twitter?: Omit<SocialNetwork, 'id'>;
    facebook?: Omit<SocialNetwork, 'id'>;
    others?: Omit<OtherSocialNetwork, 'id'>[];
  };
  contentTypes?: string[];
  contentLanguages?: string[];
  specialties?: string[];
  promotesTraders?: boolean;
  responsibleName?: string;
  agencyName?: string;
  responsibleCapturer?: string;
  tags?: string[];
  notes?: string[];
}

// Interface para atualização de influencer usando tipo utilitário
export type UpdateInfluencerData = UpdateData<Influencer>;

// Interface para influencer com validação de ownership e permissões
export interface InfluencerWithPermissions extends Influencer {
  canEdit: boolean;
  canDelete: boolean;
  canAddToGroups: boolean;
  canInviteToCampaigns: boolean;
  canViewFinancials: boolean;
}

// Interface para filtros de influencer
export interface InfluencerFilters {
  category?: string;
  categories?: string[];
  country?: string;
  state?: string;
  city?: string;
  gender?: string;
  ageMin?: number;
  ageMax?: number;
  followersMin?: number;
  followersMax?: number;
  engagementMin?: number;
  engagementMax?: number;
  ratingMin?: number;
  ratingMax?: number;
  isVerified?: boolean;
  isAvailable?: boolean;
  status?: string[];
  contentTypes?: string[];
  specialties?: string[];
  promotesTraders?: boolean;
  tags?: string[];
  search?: string;
  platforms?: string[];
}

// Utilitários para validação e cálculos
export function calculateTotalFollowers(influencer: Pick<Influencer, 'instagramFollowers' | 'tiktokFollowers' | 'youtubeFollowers' | 'facebookFollowers' | 'twitchFollowers' | 'kwaiFollowers' | 'socialNetworks'>): number {
  let total = 0;
  
  // Nova estrutura - campos diretos
  if (influencer.instagramFollowers) total += influencer.instagramFollowers;
  if (influencer.tiktokFollowers) total += influencer.tiktokFollowers;
  if (influencer.youtubeFollowers) total += influencer.youtubeFollowers;
  if (influencer.facebookFollowers) total += influencer.facebookFollowers;
  if (influencer.twitchFollowers) total += influencer.twitchFollowers;
  if (influencer.kwaiFollowers) total += influencer.kwaiFollowers;
  
  // Fallback para estrutura antiga
  if (influencer.socialNetworks) {
    if (influencer.socialNetworks.instagram) total += influencer.socialNetworks.instagram.followers;
    if (influencer.socialNetworks.tiktok) total += influencer.socialNetworks.tiktok.followers;
    if (influencer.socialNetworks.youtube) total += influencer.socialNetworks.youtube.followers;
    if (influencer.socialNetworks.twitter) total += influencer.socialNetworks.twitter.followers;
    if (influencer.socialNetworks.facebook) total += influencer.socialNetworks.facebook.followers;
    if (influencer.socialNetworks.others) {
      total += influencer.socialNetworks.others.reduce((sum, network) => sum + network.followers, 0);
    }
  }
  
  return total;
}

export function calculateTotalViews(influencer: Pick<Influencer, 'instagramAvgViews' | 'tiktokAvgViews' | 'youtubeAvgViews' | 'facebookAvgViews' | 'twitchViews' | 'kwaiViews' | 'socialNetworks'>): number {
  let total = 0;
  
  // Nova estrutura - campos diretos
  if (influencer.instagramAvgViews) total += influencer.instagramAvgViews;
  if (influencer.tiktokAvgViews) total += influencer.tiktokAvgViews;
  if (influencer.youtubeAvgViews) total += influencer.youtubeAvgViews;
  if (influencer.facebookAvgViews) total += influencer.facebookAvgViews;
  if (influencer.twitchViews) total += influencer.twitchViews;
  if (influencer.kwaiViews) total += influencer.kwaiViews;
  
  // Fallback para estrutura antiga
  if (influencer.socialNetworks) {
    if (influencer.socialNetworks.instagram) total += influencer.socialNetworks.instagram.avgViews;
    if (influencer.socialNetworks.tiktok) total += influencer.socialNetworks.tiktok.avgViews;
    if (influencer.socialNetworks.youtube) total += influencer.socialNetworks.youtube.avgViews;
    if (influencer.socialNetworks.twitter) total += influencer.socialNetworks.twitter.avgViews;
    if (influencer.socialNetworks.facebook) total += influencer.socialNetworks.facebook.avgViews;
    if (influencer.socialNetworks.others) {
      total += influencer.socialNetworks.others.reduce((sum, network) => sum + network.avgViews, 0);
    }
  }
  
  return total;
}

export function calculateOverallEngagement(influencer: Pick<Influencer, 'instagramFollowers' | 'instagramAvgViews' | 'tiktokFollowers' | 'tiktokAvgViews' | 'youtubeFollowers' | 'youtubeAvgViews' | 'facebookFollowers' | 'facebookAvgViews' | 'twitchFollowers' | 'twitchViews' | 'kwaiFollowers' | 'kwaiViews' | 'socialNetworks'>): number {
  let totalFollowers = 0;
  let totalViews = 0;
  
  // Nova estrutura - campos diretos
  if (influencer.instagramFollowers) {
    totalFollowers += influencer.instagramFollowers;
    if (influencer.instagramAvgViews) totalViews += influencer.instagramAvgViews;
  }
  if (influencer.tiktokFollowers) {
    totalFollowers += influencer.tiktokFollowers;
    if (influencer.tiktokAvgViews) totalViews += influencer.tiktokAvgViews;
  }
  if (influencer.youtubeFollowers) {
    totalFollowers += influencer.youtubeFollowers;
    if (influencer.youtubeAvgViews) totalViews += influencer.youtubeAvgViews;
  }
  if (influencer.facebookFollowers) {
    totalFollowers += influencer.facebookFollowers;
    if (influencer.facebookAvgViews) totalViews += influencer.facebookAvgViews;
  }
  if (influencer.twitchFollowers) {
    totalFollowers += influencer.twitchFollowers;
    if (influencer.twitchViews) totalViews += influencer.twitchViews;
  }
  if (influencer.kwaiFollowers) {
    totalFollowers += influencer.kwaiFollowers;
    if (influencer.kwaiViews) totalViews += influencer.kwaiViews;
  }
  
  // Fallback para estrutura antiga
  if (influencer.socialNetworks) {
    const networks = [
      influencer.socialNetworks.instagram,
      influencer.socialNetworks.tiktok,
      influencer.socialNetworks.youtube,
      influencer.socialNetworks.twitter,
      influencer.socialNetworks.facebook,
      ...(influencer.socialNetworks.others || [])
    ].filter(Boolean);
    
    totalFollowers += networks.reduce((sum, network) => sum + network!.followers, 0);
    totalViews += networks.reduce((sum, network) => sum + network!.avgViews, 0);
  }
  
  return totalFollowers > 0 ? (totalViews / totalFollowers) * 100 : 0;
}

export function formatLocation(city: string, state: string, country: string): string {
  return `${city}, ${state} - ${country}`;
}

export function validateInfluencerData(data: CreateInfluencerData): string[] {
  const errors: string[] = [];
  
  if (!data.name.trim()) errors.push('Nome é obrigatório');
  if (!data.country.trim()) errors.push('País é obrigatório');
  if (!data.state.trim()) errors.push('Estado é obrigatório');
  if (!data.city.trim()) errors.push('Cidade é obrigatória');
  if (!data.category.trim()) errors.push('Categoria principal é obrigatória');
  
  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Email inválido');
  }
  
  if (data.age && (data.age < 13 || data.age > 100)) {
    errors.push('Idade deve estar entre 13 e 100 anos');
  }
  
  if (data.rating && (data.rating < 0 || data.rating > 5)) {
    errors.push('Rating deve estar entre 0 e 5');
  }
  
  if (data.engagementRate && (data.engagementRate < 0 || data.engagementRate > 100)) {
    errors.push('Taxa de engajamento deve estar entre 0 e 100');
  }
  
  return errors;
}


