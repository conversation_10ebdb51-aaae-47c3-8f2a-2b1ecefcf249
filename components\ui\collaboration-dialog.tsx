"use client";

import { useState } from 'react';
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, UserPlus2, X } from 'lucide-react';

interface CollaborationDialogProps {
  open: boolean;
  onClose: () => void;
  proposalToken: string;
  proposalName?: string;
  onConfirm?: () => void;
}

export function CollaborationDialog({
  open,
  onClose,
  proposalToken,
  proposalName = 'esta proposta',
  onConfirm
}: CollaborationDialogProps) {
  const { currentUser, firebaseUser } = useFirebaseAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleCollaboration = () => {
    setIsLoading(true);
    
    if (!firebaseUser) {
      // Salvar token para redirecionamento após login
      localStorage.setItem('pendingSharedToken', proposalToken);
      localStorage.setItem('pendingCollaboration', 'true');
      
      if (onConfirm) {
        onConfirm();
      } else {
        // Redirecionar para login com parâmetro redirect
        const redirectUrl = encodeURIComponent(`/shared/${proposalToken}`);
        router.push(`/login?redirect=${redirectUrl}`);
      }
      return;
    }

    // Usuário já está logado, processar colaboração
    if (onConfirm) {
      onConfirm();
    } else {
      processCollaboration();
    }
  };

  const processCollaboration = async () => {
    try {
      // Obter token de autenticação
      const idToken = await firebaseUser!.getIdToken();
      
      // Chamar API para acessar proposta como colaborador
      const response = await fetch(`/api/shared/${proposalToken}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${idToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Falha ao processar colaboração');
      }

      const result = await response.json();
      
      // Acessar proposta diretamente
      if (result.success && result.data?.proposal?.id) {
        router.push(`/propostas/${result.data.proposal.id}`);
      } else {
        // Redirecionar para página de influenciadores como fallback
        router.push(currentUser?.id ? `/influencers/${currentUser.id}` : '/influencers');
      }
    } catch (error) {
      console.error('Erro ao processar colaboração:', error);
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  const handleDecline = () => {
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Colaborar com a proposta?</DialogTitle>
          <DialogDescription>
            Você foi convidado para colaborar com {proposalName}. Ao aceitar, você terá acesso para visualizar esta proposta.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col items-center justify-center py-4">
          <div className="h-16 w-16 rounded-full bg-gradient-to-br from-[#ff0074] to-[#9810fa] flex items-center justify-center">
            <UserPlus2 className="h-8 w-8 text-white" />
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleDecline}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            <X className="h-4 w-4 mr-2" />
            Recusar
          </Button>
          
          <Button 
            onClick={handleCollaboration}
            disabled={isLoading}
            className="w-full sm:w-auto bg-gradient-to-r from-[#ff0074] to-[#9810fa] hover:from-[#ff0074]/90 hover:to-[#9810fa]/90"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <UserPlus2 className="h-4 w-4 mr-2" />
            )}
            Colaborar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 


