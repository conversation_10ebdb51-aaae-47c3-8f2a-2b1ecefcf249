import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    const { userId, has } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    console.log('🔄 [MIGRATE_METADATA] Iniciando migração para usuário:', userId);

    // Buscar dados do usuário
    const clerk = await clerkClient();
    const user = await clerk.users.getUser(userId);
    
    // Verificar se já tem userType no privateMetadata
    const currentUserType = (user.privateMetadata as any)?.userType;
    
    if (currentUserType) {
      console.log('✅ [MIGRATE_METADATA] Usuário já tem userType:', currentUserType);
      return NextResponse.json({
        success: true,
        message: 'Usuário já tem metadata atualizado',
        userType: currentUserType
      });
    }

    // Determinar userType baseado no role do Clerk
    let userType: string | undefined;
    
    if (has && has({ role: 'org:admin' })) {
      userType = 'agency';
    } else if (has && has({ role: 'org:manager' })) {
      userType = 'manager';
    } else if (has && has({ role: 'org:member' })) {
      userType = 'influencer';
    } else if (has && has({ role: 'org:brand_manager' })) {
      userType = 'brand';
    } else if (has && has({ role: 'org:viewer' })) {
      userType = 'viewer';
    }

    if (!userType) {
      console.log('❌ [MIGRATE_METADATA] Não foi possível determinar userType para o usuário');
      return NextResponse.json(
        { error: 'Não foi possível determinar o tipo de usuário' },
        { status: 400 }
      );
    }

    console.log('🎯 [MIGRATE_METADATA] Mapeando role para userType:', {
      hasOrgAdmin: has?.({ role: 'org:admin' }),
      hasOrgManager: has?.({ role: 'org:manager' }),
      hasOrgMember: has?.({ role: 'org:member' }),
      determinedUserType: userType
    });

    // Atualizar metadata do usuário
    await clerk.users.updateUserMetadata(userId, {
      publicMetadata: {
        ...user.publicMetadata,
        userType,
        onboardingCompleted: true,
        metadataMigrated: true,
        migrationDate: new Date().toISOString()
      }
    });

    console.log('✅ [MIGRATE_METADATA] Metadata atualizado com sucesso:', {
      userId,
      userType,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      message: 'Metadata migrado com sucesso',
      userType,
      migrated: true
    });

  } catch (error) {
    console.error('❌ [MIGRATE_METADATA] Erro na migração:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 