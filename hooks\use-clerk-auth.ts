'use client';

import { useAuth as useClerkAuth, useUser, useClerk } from '@clerk/nextjs';

export function useAuth() {
  const { userId, sessionId, getToken, isLoaded, isSignedIn, has } = useClerkAuth();
  const { user } = useUser();
  const clerk = useClerk();
  
  // Extrair userType do publicMetadata (não privateMetadata que não está disponível no frontend)
  const userType = user?.publicMetadata?.userType as string;
  
  const mapUserTypeToRole = (type: string | undefined): string => {
    switch (type) {
      case 'agency': return 'admin';
      case 'manager': return 'manager';
      case 'influencer': return 'member';
      case 'brand': return 'member';
      case 'viewer': return 'viewer';
      default: return 'anonymous'; // Forçar onboarding se não tem userType
    }
  };
  
  // Determinar role baseado em privateMetadata OU role do Clerk
  const determineUserRole = (): string => {
    // Se tem userType no privateMetadata, use ele
    if (userType) {
      return mapUserTypeToRole(userType);
    }
    
    // Se não tem privateMetadata, verificar roles do Clerk
    if (user && has) {
      // Migração automática removida - já foi concluída com sucesso
      
      if (has({ role: 'org:admin' })) return 'admin';
      if (has({ role: 'org:manager' })) return 'manager';
      if (has({ role: 'org:member' })) return 'member';
    }
    
    // Se não conseguiu determinar, usar anonymous (forçar onboarding)
    return 'anonymous';
  };
  
  const userRole = determineUserRole();
  
  // Debug removido para produção
  
  // Criar currentUser com estrutura compatível
  const currentUser = user ? {
    ...user,
    id: user.id,
    email: user.primaryEmailAddress?.emailAddress || user.emailAddresses?.[0]?.emailAddress || '',
    role: userRole,
    name: user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`.trim()
      : user.firstName || user.lastName || user.username || ''
  } : null;
  
  return {
    currentUser,
    firebaseUser: user, // Mantém compatibilidade com código existente
    isLoading: !isLoaded,
    isInitialized: isLoaded,
    userId,
    sessionId,
    hasPermission: (permission: string) => {
      // Implementar lógica de permissões baseada no Clerk
      if (!user) return false;
      // Por enquanto, retorna true para todos os usuários autenticados
      return true;
    },
    logout: async () => {
      await clerk.signOut();
    },
    getToken: async () => {
      return await getToken();
    }
  };
}

// Hook legacy para compatibilidade total
export function useFirebaseAuth() {
  return useAuth();
} 

