{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@clerk/clerk-sdk-node": "^5.1.6", "@clerk/localizations": "^3.18.0", "@clerk/nextjs": "^6.24.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@graphql-tools/schema": "^10.0.7", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@mistralai/mistralai": "^1.7.3", "@modelcontextprotocol/sdk": "^1.12.1", "@radix-ui/primitive": "^1.1.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reactour/mask": "^1.2.0", "@reactour/popover": "^1.3.0", "@reactour/tour": "^3.8.0", "@reactour/utils": "^0.6.0", "@smithery/sdk": "^1.5.2", "@tanstack/react-table": "^8.21.3", "@types/bcryptjs": "^2.4.6", "@types/intro.js": "^5.1.5", "@types/jsonwebtoken": "^9.0.10", "@types/react": "^18.2.67", "@types/react-joyride": "^2.0.2", "@types/styled-components": "^5.1.34", "autoprefixer": "^10.4.20", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "driver.js": "^1.3.6", "embla-carousel-react": "8.5.1", "firebase": "^11.9.0", "firebase-admin": "^13.4.0", "framer-motion": "^12.18.1", "graphql": "^16.11.0", "html2canvas": "^1.4.1", "input-otp": "1.4.1", "intro.js": "^8.3.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "motion": "^12.23.3", "nanoid": "^5.1.5", "next": "15.2.4", "next-auth": "^4.24.5", "next-intl": "^4.3.4", "next-themes": "^0.4.4", "nextstepjs": "^2.1.1", "nuqs": "^2.4.3", "postcss": "^8", "react": "^19", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-day-picker": "8.10.1", "react-dom": "^19", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.2", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-international-phone": "^4.5.0", "react-joyride": "^2.9.3", "react-resizable-panels": "^2.1.7", "react-shepherd": "^6.1.8", "react-treeview": "^0.4.7", "react-type-animation": "^3.2.0", "react-world-flags": "^1.6.0", "recharts": "2.15.0", "sharp": "^0.34.2", "shepherd.js": "^14.5.0", "sonner": "^1.7.4", "styled-components": "^6.1.19", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.3", "vaul": "^0.9.6", "world-countries": "^5.1.0", "zod": "^3.24.1"}, "devDependencies": {"@types/canvas-confetti": "^1.9.0", "@types/mime-types": "^3.0.1", "@types/node": "^24.0.1", "@types/react-dom": "^19", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "ignore-loader": "^0.1.2", "ts-node": "^10.9.2"}}