const admin = require('firebase-admin');

// Inicializar Firebase Admin
try {
  admin.app();
} catch (e) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'deumatch-demo'
  });
}

const auth = admin.auth();

async function testAuthMiddleware() {
  try {
    console.log('🧪 Testando middleware de autenticação...\n');

    // Listar usuários existentes
    const listUsers = await auth.listUsers(10);
    console.log('👥 Usuários existentes:');
    listUsers.users.forEach(user => {
      console.log(`  - ${user.uid}: ${user.email} (${user.displayName || 'Sem nome'})`);
    });

    if (listUsers.users.length === 0) {
      console.log('❌ Nenhum usuário encontrado. Execute create-test-user-auth.js primeiro.');
      return;
    }

    // Testar criação de token para o primeiro usuário
    const testUser = listUsers.users[0];
    console.log(`\n🔑 Criando token para usuário: ${testUser.uid}`);

    const customToken = await auth.createCustomToken(testUser.uid, {
      role: 'user',
      email: testUser.email
    });

    console.log('✅ Token criado com sucesso!');
    console.log('📝 Token (primeiros 50 chars):', customToken.substring(0, 50) + '...');

    // Verificar se podemos decodificar o token
    const decodedToken = await auth.verifyIdToken(customToken).catch(() => {
      console.log('⚠️ Custom token não pode ser verificado diretamente (esperado)');
      return null;
    });

    console.log('\n✅ Teste do middleware de autenticação concluído!');
    console.log('\n📋 Próximos passos:');
    console.log('1. Faça login na aplicação web');
    console.log('2. Tente criar uma marca');
    console.log('3. Verifique no console se o userId correto está sendo usado');
    console.log('4. Confira no Firestore se a marca foi criada com o userId correto');

  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    process.exit(0);
  }
}

testAuthMiddleware(); 
