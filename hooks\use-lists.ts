import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useAuth } from '@clerk/nextjs';
import { 
  Lista, 
  CriarListaData, 
  AtualizarListaData, 
  FiltrosLista,
  ResultadoBuscaListas,
  OperacaoLoteListaData,
  ResultadoOperacaoLote
} from '@/types/list';

/**
 * 🔗 HOOK PARA INTEGRAÇÃO COM API DE LISTAS
 * Gerencia todas as operações CRUD com listas
 */

interface UseListsOptions {
  autoLoad?: boolean;
  filtrosIniciais?: FiltrosLista;
}

interface UseListsReturn {
  listas: Lista[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  pagination: {
    total: number;
    pagina: number;
    porPagina: number;
    totalPaginas: number;
  };
  
  // Operações CRUD
  criarLista: (data: CriarListaData) => Promise<Lista | null>;
  atualizarLista: (id: string, data: AtualizarListaData) => Promise<Lista | null>;
  deletarLista: (id: string) => Promise<boolean>;
  buscarListas: (filtros?: FiltrosLista) => Promise<void>;
  operacaoLote: (data: OperacaoLoteListaData) => Promise<ResultadoOperacaoLote | null>;
  
  // Operações de busca
  refetch: () => Promise<void>;
  clearError: () => void;
}

export function useLists(options: UseListsOptions = {}): UseListsReturn {
  const { getToken, isLoaded } = useAuth();
  const { autoLoad = true, filtrosIniciais = {} } = options;

  // Memorizar filtros iniciais para evitar recriar funções
  const filtrosMemorizados = useMemo(() => filtrosIniciais, [
    filtrosIniciais.busca,
    filtrosIniciais.tipoLista,
    filtrosIniciais.tipoObjeto,
    filtrosIniciais.status,
    filtrosIniciais.isPublica,
    filtrosIniciais.tags?.join(',')
  ]);

  // Flag para controlar se já foi executado o carregamento inicial
  const carregamentoInicialExecutado = useRef(false);

  // Estados
  const [listas, setListas] = useState<Lista[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    pagina: 1,
    porPagina: 20,
    totalPaginas: 0
  });

  /**
   * Buscar listas do usuário
   */
  const buscarListas = useCallback(async (
    filtros: FiltrosLista = {}
  ) => {
    if (!isLoaded) return;

    try {
      setIsLoading(true);
      setError(null);

      // Obter token de autenticação
      const token = await getToken();
      
      // Construir query params
      const searchParams = new URLSearchParams();
      if (filtros.busca) searchParams.append('busca', filtros.busca);
      if (filtros.tipoLista) searchParams.append('tipoLista', filtros.tipoLista);
      if (filtros.tipoObjeto) searchParams.append('tipoObjeto', filtros.tipoObjeto);
      if (filtros.status) searchParams.append('status', filtros.status);
      if (filtros.isPublica !== undefined) searchParams.append('isPublica', filtros.isPublica.toString());
      if (filtros.tags && filtros.tags.length > 0) {
        filtros.tags.forEach(tag => searchParams.append('tags[]', tag));
      }

      const response = await fetch(`/api/lists?${searchParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao buscar listas');
      }

      const resultado: ResultadoBuscaListas = await response.json();
      
      setListas(resultado.listas);
      setPagination({
        total: resultado.total,
        pagina: 1,
        porPagina: resultado.listas.length || 20,
        totalPaginas: 1
      });

    } catch (err: any) {
      console.error('[USE_LISTS_FETCH]', err);
      setError(err.message || 'Erro ao buscar listas');
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, getToken]);

  /**
   * Criar nova lista
   */
  const criarLista = useCallback(async (data: CriarListaData): Promise<Lista | null> => {
    if (!isLoaded) {
      setError('Usuário não autenticado');
      return null;
    }

    try {
      setIsCreating(true);
      setError(null);

      const token = await getToken();

      const response = await fetch('/api/lists', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao criar lista');
      }

      const novaLista: Lista = await response.json();
      
      // Atualizar estado local
      setListas(prev => [novaLista, ...prev]);
      setPagination(prev => ({ ...prev, total: prev.total + 1 }));

      return novaLista;

    } catch (err: any) {
      console.error('[USE_LISTS_CREATE]', err);
      setError(err.message || 'Erro ao criar lista');
      return null;
    } finally {
      setIsCreating(false);
    }
  }, [isLoaded, getToken]);

  /**
   * Atualizar lista existente
   */
  const atualizarLista = useCallback(async (
    id: string, 
    data: AtualizarListaData
  ): Promise<Lista | null> => {
    if (!isLoaded) {
      setError('Usuário não autenticado');
      return null;
    }

    try {
      setIsUpdating(true);
      setError(null);

      const token = await getToken();

      const response = await fetch(`/api/lists/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao atualizar lista');
      }

      const listaAtualizada: Lista = await response.json();
      
      // Atualizar estado local
      setListas(prev => prev.map(lista => 
        lista.id === id ? listaAtualizada : lista
      ));

      return listaAtualizada;

    } catch (err: any) {
      console.error('[USE_LISTS_UPDATE]', err);
      setError(err.message || 'Erro ao atualizar lista');
      return null;
    } finally {
      setIsUpdating(false);
    }
  }, [isLoaded, getToken]);

  /**
   * Deletar lista
   */
  const deletarLista = useCallback(async (id: string): Promise<boolean> => {
    if (!isLoaded) {
      setError('Usuário não autenticado');
      return false;
    }

    try {
      setIsDeleting(true);
      setError(null);

      const token = await getToken();

      const response = await fetch(`/api/lists/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao deletar lista');
      }

      // Atualizar estado local
      setListas(prev => prev.filter(lista => lista.id !== id));
      setPagination(prev => ({ ...prev, total: Math.max(0, prev.total - 1) }));

      return true;

    } catch (err: any) {
      console.error('[USE_LISTS_DELETE]', err);
      setError(err.message || 'Erro ao deletar lista');
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [isLoaded, getToken]);

  /**
   * Operações em lote
   */
  const operacaoLote = useCallback(async (
    data: OperacaoLoteListaData
  ): Promise<ResultadoOperacaoLote | null> => {
    if (!isLoaded) {
      setError('Usuário não autenticado');
      return null;
    }

    try {
      setIsLoading(true);
      setError(null);

      const token = await getToken();

      const response = await fetch('/api/lists', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro na operação em lote');
      }

      const resultado: ResultadoOperacaoLote = await response.json();

      // Atualizar estado local baseado na operação
      if (data.operacao === 'deletar') {
        setListas(prev => prev.filter(lista => !resultado.sucesso.includes(lista.id)));
        setPagination(prev => ({ 
          ...prev, 
          total: Math.max(0, prev.total - resultado.sucesso.length) 
        }));
      } else if (data.operacao === 'arquivar') {
        setListas(prev => prev.map(lista => 
          resultado.sucesso.includes(lista.id) 
            ? { ...lista, status: 'arquivada' as const }
            : lista
        ));
      }

      return resultado;

    } catch (err: any) {
      console.error('[USE_LISTS_BATCH]', err);
      setError(err.message || 'Erro na operação em lote');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, getToken]);

  /**
   * Recarregar dados
   */
  const refetch = useCallback(async () => {
    await buscarListas(filtrosMemorizados);
  }, [buscarListas, filtrosMemorizados]);

  /**
   * Limpar erro
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-carregamento inicial - executa apenas uma vez quando o usuário está autenticado
  useEffect(() => {
    if (autoLoad && isLoaded && !carregamentoInicialExecutado.current) {
      carregamentoInicialExecutado.current = true;
      buscarListas(filtrosMemorizados);
    }
  }, [autoLoad, isLoaded, buscarListas, filtrosMemorizados]);

  return {
    listas,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    pagination,
    criarLista,
    atualizarLista,
    deletarLista,
    buscarListas,
    operacaoLote,
    refetch,
    clearError
  };
} 

