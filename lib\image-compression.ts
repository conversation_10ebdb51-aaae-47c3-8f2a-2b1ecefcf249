// Utilitário de compressão de imagem similar ao Instagram e Facebook

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp' | 'png';
}

const DEFAULT_OPTIONS: Required<CompressionOptions> = {
  maxWidth: 1080,
  maxHeight: 1080,
  quality: 0.85,
  format: 'jpeg'
};

/**
 * Comprime uma imagem usando Canvas API
 * Reduz o tamanho do arquivo mantendo qualidade visual similar ao Instagram/Facebook
 */
export function compressImage(
  file: File, 
  options: CompressionOptions = {}
): Promise<File> {
  return new Promise((resolve, reject) => {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    
    // Validar se é uma imagem
    if (!file.type.startsWith('image/')) {
      reject(new Error('Arquivo deve ser uma imagem'));
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      reject(new Error('Canvas não suportado'));
      return;
    }

    const img = new Image();
    
    img.onload = () => {
      try {
        // Calcular novas dimensões mantendo proporção
        let { width, height } = img;
        const aspectRatio = width / height;
        
        // Redimensionar baseado na largura máxima
        if (width > opts.maxWidth) {
          width = opts.maxWidth;
          height = width / aspectRatio;
        }
        
        // Redimensionar baseado na altura máxima
        if (height > opts.maxHeight) {
          height = opts.maxHeight;
          width = height * aspectRatio;
        }
        
        // Configurar canvas
        canvas.width = width;
        canvas.height = height;
        
        // Aplicar filtros para melhor qualidade (similar ao Instagram)
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // Desenhar imagem redimensionada
        ctx.drawImage(img, 0, 0, width, height);
        
        // Converter para blob com compressão
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Falha ao comprimir imagem'));
              return;
            }
            
            // Manter o nome original do arquivo
            // Apenas ajustar a extensão se necessário para o formato especificado
            let fileName = file.name;
            if (opts.format === 'jpeg' && !fileName.toLowerCase().endsWith('.jpg') && !fileName.toLowerCase().endsWith('.jpeg')) {
              fileName = file.name.replace(/\.[^/.]+$/, '') + '.jpg';
            } else if (opts.format !== 'jpeg' && !fileName.toLowerCase().endsWith(`.${opts.format}`)) {
              fileName = file.name.replace(/\.[^/.]+$/, '') + `.${opts.format}`;
            }

            const compressedFile = new File([blob], fileName, {
              type: `image/${opts.format}`,
              lastModified: Date.now()
            });
            
            resolve(compressedFile);
          },
          `image/${opts.format}`,
          opts.quality
        );
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => {
      reject(new Error('Falha ao carregar imagem'));
    };
    
    // Carregar imagem
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Comprime múltiplas imagens em paralelo
 */
export async function compressImages(
  files: File[], 
  options: CompressionOptions = {}
): Promise<File[]> {
  const compressionPromises = files.map(file => compressImage(file, options));
  return Promise.all(compressionPromises);
}

/**
 * Valida se um arquivo é uma imagem válida
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Verificar tipo
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'Arquivo deve ser uma imagem' };
  }
  
  // Verificar tamanho (máximo 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { 
      valid: false, 
      error: `Arquivo muito grande. Máximo: ${(maxSize / 1024 / 1024).toFixed(0)}MB` 
    };
  }
  
  // Verificar formatos suportados
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!supportedTypes.includes(file.type)) {
    return { 
      valid: false, 
      error: 'Formato não suportado. Use: JPEG, PNG ou WebP' 
    };
  }
  
  return { valid: true };
}

/**
 * Calcula a redução de tamanho após compressão
 */
export function getCompressionStats(originalFile: File, compressedFile: File) {
  const originalSize = originalFile.size;
  const compressedSize = compressedFile.size;
  const reduction = ((originalSize - compressedSize) / originalSize) * 100;
  
  return {
    originalSize: (originalSize / 1024 / 1024).toFixed(2) + 'MB',
    compressedSize: (compressedSize / 1024 / 1024).toFixed(2) + 'MB',
    reduction: reduction.toFixed(1) + '%'
  };
}

