'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { useModalCleanup } from '@/hooks/use-modal-cleanup';

// Força renderização dinâmica para evitar problemas de SSG
export const dynamic = 'force-dynamic';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter 
} from '@/components/ui/dialog';
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle 
} from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  FileText, 
  Send, 
  Clock, 
  CheckCircle, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Settings,
  Copy,
  AlertTriangle,
  Calendar,
  Users,
  Star,
  Heart,
  Target,
  Megaphone,
  Zap,
  TrendingUp,
  Gift,
  Sparkles,
  Crown,
  Flame,
  Trophy,
  Rocket,
  Diamond,
  Music,
  Camera,
  Video,
  Palette,
  ChevronDown
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

// Importar componente de gerenciamento de convites
import { ProposalInviteManager } from '@/components/proposals/proposal-invite-manager';

// Importar tipos e serviços do Firebase
import { ProposalService } from '@/services/proposal-service';
import { 
  Proposal, 
  ProposalStatus, 
  ProposalPriority,
  InfluencerInProposal,
  InfluencerTier,
  PROPOSAL_STATUS_LABELS,
  getProposalPriorityColor 
} from '@/types/proposal';
import { ProposalStatusBadge } from '@/components/ui/proposal-status-badge';
import { useAuth } from '@/hooks/use-auth-v2';
import { useBrandsList } from '@/hooks/use-brands';

// ✨ NOVO: Importar hook de organizações do Clerk
import { useAuthWithOrganizations } from '@/hooks/use-auth-organizations';

// ✨ IMPORTAR LOADER GLOBAL
import { useGlobalLoader } from '@/components/ui/loader';

// Removido: Firebase imports não são mais necessários - usando API do Clerk

// Tipos para a UI local
interface Proposta {
  id: string;
  nome: string;
  descricao?: string;
  criadoPor: string;
  criadoPorNome?: string;
  criadoPorImagem?: string; // ✅ NOVO: Campo para imagem do criador
  brandId: string;
  grupo?: string; // Grupo é opcional e diferente do nome
  priority: ProposalPriority;
  status: ProposalStatus;
  influencers: InfluencerInProposal[];
  totalAmount: number;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  dataEnvio?: string;
  services: any[];
  icon?: string; // ✅ NOVO: Campo para ícone da proposta
}

interface ColumnVisibility {
  marca: boolean;
  influencers: boolean;
  value: boolean;
  priority: boolean;
  criadoPor: boolean;
  dataEnvio: boolean;
  grupo: boolean;
  dataCriacao: boolean;
  ultimaAtualizacao: boolean;
  status: boolean;
  actions: boolean;
}

// ✅ NOVO: Lista de ícones disponíveis para propostas
const AVAILABLE_ICONS = [
  { name: 'FileText', icon: FileText, label: 'Documento' },
  { name: 'Star', icon: Star, label: 'Estrela' },
  { name: 'Heart', icon: Heart, label: 'Coração' },
  { name: 'Target', icon: Target, label: 'Alvo' },
  { name: 'Megaphone', icon: Megaphone, label: 'Megafone' },
  { name: 'Zap', icon: Zap, label: 'Raio' },
  { name: 'TrendingUp', icon: TrendingUp, label: 'Crescimento' },
  { name: 'Gift', icon: Gift, label: 'Presente' },
  { name: 'Sparkles', icon: Sparkles, label: 'Brilho' },
  { name: 'Crown', icon: Crown, label: 'Coroa' },
  { name: 'Flame', icon: Flame, label: 'Chama' },
  { name: 'Trophy', icon: Trophy, label: 'Troféu' },
  { name: 'Rocket', icon: Rocket, label: 'Foguete' },
  { name: 'Diamond', icon: Diamond, label: 'Diamante' },
  { name: 'Music', icon: Music, label: 'Música' },
  { name: 'Camera', icon: Camera, label: 'Câmera' },
  { name: 'Video', icon: Video, label: 'Vídeo' },
  { name: 'Palette', icon: Palette, label: 'Paleta' },
];

function PropostasPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { currentUser, isLoading: authLoading, isInitialized } = useAuth();
  const { brands } = useBrandsList();

  // 🔒 PROTEÇÃO: Verificar se usuário é admin
  const isAdmin = currentUser?.role === 'admin';
  const shouldShowAccessDenied = isInitialized && !authLoading && currentUser && !isAdmin;
  
  // ✅ NOVO: Usar hook real de organizações do Clerk
  const { 
    currentOrganization, 
    organizations, 
    isLoading: orgLoading 
  } = useAuthWithOrganizations();
  
  // ✨ USAR LOADER GLOBAL EM VEZ DO ESTADO LOCAL
  const { isLoading, showLoader, hideLoader } = useGlobalLoader();
  
  console.log('🏢 [PROPOSTAS] Organizações do Clerk:', {
    currentOrganization,
    organizationsCount: organizations.length,
    organizations: organizations.map(org => ({ id: org.id, name: org.name, role: org.role }))
  });
  
  // ✅ DEBUG: Logs detalhados do Clerk
 
  
  // Verificar se há filtro específico de marca na URL
  const brandIdFromUrl = searchParams?.get('brandId') || searchParams?.get('brand');
  const brandName = searchParams?.get('brandName') || 'Marca';
  
  // Para "Todas as Propostas": não há brandId na URL
  // Para marca específica: há brandId na URL
  const isAllProposals = !brandIdFromUrl;
  const brandId = brandIdFromUrl || null; // ✅ CORREÇÃO: Não usar currentUser?.id como fallback
  
 
  
  const [propostas, setPropostas] = useState<Proposta[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('todos');
  const [currentBrand, setCurrentBrand] = useState<{id: string, name: string} | null>(null);
  
  // ✅ NOVO: Cache para dados de usuários (nome e imagem)
  const [userDataCache, setUserDataCache] = useState<Record<string, { name: string; profileImage?: string }>>({});
  
  // Estados para criação/edição de proposta (Sheet unificado)
  const [showPropostaSheet, setShowPropostaSheet] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false); // ✅ NOVO: Modo de edição
  const [editingProposta, setEditingProposta] = useState<Proposta | null>(null); // ✅ MOVIDO: Para usar no Sheet
  const [newPropostaName, setNewPropostaName] = useState('');
  const [newPropostaDescription, setNewPropostaDescription] = useState('');
  const [newPropostaDate, setNewPropostaDate] = useState<Date | undefined>(new Date());
  const [selectedIcon, setSelectedIcon] = useState<string>('FileText'); // ✅ NOVO: Estado para ícone selecionado
  const [newPropostaPriority, setNewPropostaPriority] = useState<ProposalPriority>('medium'); // ✅ NOVO: Prioridade no sheet
  
  // Estados para edição de colunas
  const [showColumnsDialog, setShowColumnsDialog] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>({
    marca: true,
    influencers: true,
    value: true,
    priority: true,
    criadoPor: true,
    dataEnvio: true,
    grupo: false,
    dataCriacao: true,
    ultimaAtualizacao: true,
    status: true,
    actions: true
  });

  // Estados removidos - agora usando o Sheet unificado

  // Estados para gerenciamento de convites
  const [showInviteManager, setShowInviteManager] = useState(false);
  const [selectedProposal, setSelectedProposal] = useState<Proposta | null>(null);

  // Hook para limpeza de modais
  const { onModalClose } = useModalCleanup();

  // ✅ FUNÇÃO SIMPLIFICADA: Buscar dados do usuário (usando Clerk)
  const getUserData = async (userId: string): Promise<{ name: string; profileImage?: string }> => {
    if (!userId) return { name: 'Usuário Desconhecido' };
    
    // Verificar cache primeiro
    if (userDataCache[userId]) {
      return userDataCache[userId];
    }
    
    // ✅ Se for o usuário atual, usar os dados do Clerk diretamente
    if (userId === currentUser?.id && currentUser) {
      const userInfo = { 
        name: currentUser.name || currentUser.firstName || currentUser.email?.split('@')[0] || 'Usuário',
        profileImage: currentUser.imageUrl
      };
      console.log('✅ [USER_DATA] Usando dados do currentUser:', userInfo);
      
      // Atualizar cache
      setUserDataCache(prev => ({ ...prev, [userId]: userInfo }));
      return userInfo;
    }
    
    // ✅ Para outros usuários, usar nome baseado no ID por enquanto
    // TODO: Implementar busca de usuários de organização via Clerk API
    const fallbackData = { name: `Usuário ${userId.substring(0, 8)}` };
    console.log('ℹ️ [USER_DATA] Usando fallback para usuário:', userId, fallbackData);
    
    // Atualizar cache
    setUserDataCache(prev => ({ ...prev, [userId]: fallbackData }));
    return fallbackData;
  };

  // ✅ NOVA FUNÇÃO: Resolver dados de usuários para uma lista de propostas
  const resolveUserDataForPropostas = async (propostas: Proposta[]): Promise<Proposta[]> => {
    const uniqueUserIds = [...new Set(propostas.map(p => p.criadoPor).filter(Boolean))];
    
    // Buscar dados para todos os userIds únicos
    const dataPromises = uniqueUserIds.map(async (userId) => {
      if (!userDataCache[userId]) {
        const userData = await getUserData(userId);
        return { userId, userData };
      }
      return { userId, userData: userDataCache[userId] };
    });
    
    const userDataResults = await Promise.all(dataPromises);
    
    // Criar mapa de userId para dados do usuário
    const userDataMap = userDataResults.reduce((acc, { userId, userData }) => {
      acc[userId] = userData;
      return acc;
    }, {} as Record<string, { name: string; profileImage?: string }>);
    
    // Aplicar dados de usuários às propostas
    return propostas.map(proposta => ({
      ...proposta,
      criadoPorNome: userDataMap[proposta.criadoPor]?.name || proposta.criadoPor || 'Usuário Desconhecido',
      criadoPorImagem: userDataMap[proposta.criadoPor]?.profileImage
    }));
  };

  // ✅ FUNÇÃO ATUALIZADA: Verificar se usuário tem organização válida
  const hasValidOrganization = () => {
    if (!currentOrganization) {
      console.log('❌ [PROPOSTAS] Usuário não tem organização ativa');
      return false;
    }
    
    console.log('✅ [PROPOSTAS] Organização válida encontrada:', {
      id: currentOrganization.id,
      name: currentOrganization.name,
      role: currentOrganization.role
    });
    
    return true;
  };

  // Detectar mudança na URL e configurar currentBrand
  useEffect(() => {
    console.log('🔍 [PROPOSTAS_PAGE] useEffect configurando currentBrand:', {
      isAllProposals,
      brandIdFromUrl,
      brandsLength: brands.length
    });
    
    if (!isAllProposals && brandIdFromUrl && brands.length > 0) {
      const brand = brands.find(b => b.id === brandIdFromUrl);
      if (brand) {
        setCurrentBrand({ id: brand.id, name: brand.name });
      } else {
        setCurrentBrand({ id: brandIdFromUrl, name: brandName });
      }
    } else if (isAllProposals) {
      setCurrentBrand(null);
    }
  }, [brandIdFromUrl, brandName, isAllProposals, brands]);

  // ✅ CORREÇÃO: Carregar dados iniciais aguardando Clerk carregar
  useEffect(() => {
    console.log('🔍 [PROPOSTAS_PAGE] useEffect loadPropostas:', {
      isInitialized: isInitialized,
      isAuthLoading: authLoading,
      hasCurrentUser: !!currentUser?.id,
      currentUserId: currentUser?.id,
      isAllProposals,
      brandId
    });
    
    // ✅ AGUARDAR Clerk carregar completamente
    if (!isInitialized || authLoading) {
      console.log('⏳ [PROPOSTAS_PAGE] Aguardando Clerk carregar...');
      return;
    }
    
    // ✅ Verificar se usuário está autenticado
    if (!currentUser?.id) {
      console.log('❌ [PROPOSTAS_PAGE] Usuário não autenticado após Clerk carregar');
      setPropostas([]); // Limpar propostas se não há usuário
      return;
    }
    
    // Para "Todas as Propostas": só precisa do currentUser
    if (isAllProposals) {
      console.log('✅ [PROPOSTAS_PAGE] Carregando todas as propostas para usuário:', currentUser.id);
      loadPropostas();
      return;
    }
    
    // Para marca específica: precisa do brandId válido
    if (!isAllProposals && brandId) {
      console.log('✅ [PROPOSTAS_PAGE] Carregando propostas da marca:', brandId);
      loadPropostas();
      return;
    }
    
    console.log('⚠️ [PROPOSTAS_PAGE] Condições não atendidas para carregar propostas');
  }, [isInitialized, authLoading, currentUser?.id, isAllProposals, brandId]);

  const loadPropostas = async () => {
    console.log('🚀 [PROPOSTAS_PAGE] Iniciando loadPropostas...', {
      isAllProposals,
      brandId,
      currentUserId: currentUser?.id
    });
    
    if (!currentUser?.id) {
      console.warn('⚠️ [PROPOSTAS_PAGE] Usuário não autenticado, abortando carregamento');
      return;
    }
    
    try {
      // Não usar loader global para evitar pointer-events: none
      console.log('⏳ [PROPOSTAS_PAGE] Carregando sem loader global');
      
      let firebaseProposals: any[] = [];

      if (isAllProposals) {
        // Para "Todas as Propostas", usar função específica
        console.log('🔍 [PROPOSTAS_PAGE] Carregando TODAS as propostas do usuário:', currentUser.id);
        firebaseProposals = await ProposalService.getAllUserProposalsSimple(
          currentUser.id,
          100, // limit
          0 // offset
        );
        console.log(`📊 [PROPOSTAS_PAGE] Recebidas ${firebaseProposals.length} propostas do usuário`);
      } else if (brandId) {
        // Para marca específica, filtra por brandId
        const filters = { brandId };
        console.log('🔍 [PROPOSTAS_PAGE] Carregando propostas da marca:', brandId);
        firebaseProposals = await ProposalService.getProposals(
          filters,
          100, // limit
          0, // offset
          currentUser.id // userId para isolamento
        );
        console.log(`📊 [PROPOSTAS_PAGE] Recebidas ${firebaseProposals.length} propostas da marca`);
      } else {
        console.log('⚠️ [PROPOSTAS_PAGE] Nenhum filtro válido, retornando vazio');
        setPropostas([]);
        return;
      }
      
      console.log(`📊 [PROPOSTAS_PAGE] Total de propostas recebidas da API: ${firebaseProposals.length}`);
      console.log('🔍 [PROPOSTAS_PAGE] Dados brutos da primeira proposta:', firebaseProposals[0]);
      
     
      
      // Converter para o formato da UI
      let convertedPropostas: Proposta[] = firebaseProposals.map(proposal => ({
        id: proposal.id,
        nome: proposal.nome || `Proposta ${proposal.id.substring(0, 8)}`,
        descricao: proposal.descricao || `Proposta com ${proposal.influencers.length} influenciador(es)`,
        criadoPor: proposal.criadoPor || 'Sistema',
        brandId: proposal.brandId,
        grupo: proposal.grupo,
        priority: proposal.priority,
        status: proposal.status,
        influencers: proposal.influencers.map((inf: any) => ({
          ...inf,
          tier: (inf.tier as InfluencerTier) || undefined
        })),
        totalAmount: proposal.totalAmount,
        dataCriacao: proposal.createdAt,
        ultimaAtualizacao: proposal.updatedAt,
        dataEnvio: proposal.dataEnvio,
        services: proposal.services,
        icon: proposal.icon // ✅ CORRIGIDO: Incluir o campo icon na conversão
      }));
      
      console.log(`🔄 [PROPOSTAS_PAGE] Propostas convertidas: ${convertedPropostas.length}`);
      console.log('🎨 [PROPOSTAS_PAGE] Verificando ícones das propostas:', 
        convertedPropostas.map(p => ({ nome: p.nome, icon: p.icon }))
      );
      
      // ✅ RESOLVER DADOS DE USUÁRIOS
      console.log('🔍 [PROPOSTAS_PAGE] Resolvendo dados de usuários...');
      convertedPropostas = await resolveUserDataForPropostas(convertedPropostas);
      
      setPropostas(convertedPropostas);
      console.log('✅ [PROPOSTAS_PAGE] Propostas processadas e estado atualizado:', convertedPropostas.length);
      
    } catch (error) {
      console.error('❌ [PROPOSTAS_PAGE] Erro ao carregar propostas:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as propostas.",
        variant: "destructive",
      });
      setPropostas([]); // ✅ Limpar estado em caso de erro
    }
  };

  // ✅ NOVA FUNÇÃO UNIFICADA: Salvar proposta (criar ou editar)
  const handleSaveProposta = async () => {
    if (!newPropostaName.trim()) {
      toast({
        title: "Erro",
        description: "Nome da proposta é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    // Validações específicas para criação
    if (!isEditMode) {
      if (isAllProposals) {
        toast({
          title: "Aviso",
          description: "Selecione uma marca específica para criar propostas.",
          variant: "destructive",
        });
        return;
      }

      if (!brandId || brandId === 'default-brand') {
        toast({
          title: "Erro",
          description: "Marca não identificada. Selecione uma marca válida.",
          variant: "destructive",
        });
        return;
      }
    }

    try {
      if (isEditMode && editingProposta) {
        // ✅ MODO EDIÇÃO
        const updateData = {
          nome: newPropostaName,
          descricao: newPropostaDescription,
          priority: newPropostaPriority,
          icon: selectedIcon,
          dataEnvio: newPropostaDate ? newPropostaDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
        };

        await ProposalService.updateProposal(editingProposta.id, updateData);
        
        toast({
          title: "Sucesso",
          description: "Proposta atualizada com sucesso!",
        });
      } else {
        // ✅ MODO CRIAÇÃO
        const hasOrg = hasValidOrganization();
        
        if (!hasOrg || !currentOrganization) {
          toast({
            title: "Organização Necessária",
            description: "Você precisa fazer parte de uma organização para criar propostas.",
            variant: "destructive",
          });
          return;
        }

        const proposalData = {
          nome: newPropostaName,
          descricao: newPropostaDescription || '',
          criadoPor: currentUser?.id || 'unknown-user',
          criadoPorNome: currentUser?.name || currentUser?.firstName || 'Usuário Desconhecido',
          organizationId: currentOrganization.id,
          organizationName: currentOrganization.name,
          influencers: [],
          brandId: brandId!,
          services: [],
          totalAmount: 0,
          dataEnvio: newPropostaDate ? newPropostaDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          grupo: '',
          priority: newPropostaPriority,
          icon: selectedIcon
        };

        const proposalId = await ProposalService.createProposal(proposalData);
        await createProposalAccess(proposalId, currentOrganization.id);
        
        toast({
          title: "Sucesso",
          description: "Proposta criada com sucesso!",
        });
      }

      // Limpar e fechar
      setNewPropostaName('');
      setNewPropostaDescription('');
      setNewPropostaDate(new Date());
      setSelectedIcon('FileText');
      setNewPropostaPriority('medium');
      setIsEditMode(false);
      setEditingProposta(null);
      setShowPropostaSheet(false);
      
      await loadPropostas();
      
    } catch (error) {
      console.error('❌ Erro ao salvar proposta:', error);
      toast({
        title: "Erro",
        description: `Não foi possível ${isEditMode ? 'atualizar' : 'criar'} a proposta.`,
        variant: "destructive",
      });
    }
  };

  // ✅ NOVA FUNÇÃO: Criar entrada de acesso usando metadata do Clerk
  const createProposalAccess = async (proposalId: string, organizationId: string) => {
    try {
      if (!currentUser) {
        console.error('❌ [PROPOSAL_ACCESS] Usuário não encontrado');
        return;
      }

      // ✅ Usar metadata do Clerk para armazenar acesso
      const currentMetadata = (currentUser.unsafeMetadata as any) || {};
      const newMetadata = {
        ...currentMetadata,
        p: {
          ...currentMetadata.p,
                      [proposalId]: 'a' // a = agência (criador da proposta é sempre agência)
        }
      };

      await currentUser.update({
        unsafeMetadata: newMetadata
      });

      console.log('✅ [PROPOSAL_ACCESS] Acesso adicionado ao metadata do Clerk:', {
        proposalId,
        role: 'admin',
        userId: currentUser.id,
        organizationId
      });

    } catch (error) {
      console.error('❌ [PROPOSAL_ACCESS] Erro ao atualizar metadata:', error);
      // Não bloquear a criação da proposta por erro de acesso
    }
  };

  const handleEditProposta = (proposta: Proposta) => {
    setIsEditMode(true);
    setEditingProposta(proposta);
    setNewPropostaName(proposta.nome);
    setNewPropostaDescription(proposta.descricao || '');
    setNewPropostaPriority(proposta.priority);
    setSelectedIcon(proposta.icon || 'FileText');
    setNewPropostaDate(proposta.dataEnvio ? new Date(proposta.dataEnvio) : new Date());
    setShowPropostaSheet(true);
  };

  const handleCreateProposta = () => {
    setIsEditMode(false);
    setEditingProposta(null);
    setNewPropostaName('');
    setNewPropostaDescription('');
    setNewPropostaPriority('medium');
    setSelectedIcon('FileText');
    setNewPropostaDate(new Date());
    setShowPropostaSheet(true);
  };

  const handleManageAccess = (proposta: Proposta) => {
    setSelectedProposal(proposta);
    setShowInviteManager(true);
  };

  const handleUpdateProposta = async () => {
    if (!editingProposta || !newPropostaName.trim()) {
      toast({
        title: "Erro",
        description: "Nome da proposta é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Não usar loader global para evitar pointer-events: none
      
      const updateData = {
        nome: newPropostaName,
        descricao: newPropostaDescription,
        priority: newPropostaPriority,
        icon: selectedIcon // ✅ NOVO: Incluir ícone na atualização
      };

      await ProposalService.updateProposal(editingProposta.id, updateData);
      
      toast({
        title: "Sucesso",
        description: "Proposta atualizada com sucesso!",
      });

      setShowPropostaSheet(false);
      setEditingProposta(null);
      await loadPropostas();
      
    } catch (error) {
      console.error('Erro ao atualizar proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar a proposta.",
        variant: "destructive",
      });
    }
  };

  const handleDuplicateProposta = async (proposta: Proposta) => {
    try {
      // Não usar loader global para evitar pointer-events: none
      
      const duplicateData = {
        nome: `${proposta.nome} - Cópia`,
        descricao: proposta.descricao || '',
        criadoPor: currentUser?.name || 'Usuário Desconhecido',
        influencers: proposta.influencers,
        brandId: proposta.brandId,
        services: proposta.services,
        totalAmount: proposta.totalAmount,
        dataEnvio: new Date().toISOString().split('T')[0],
        grupo: proposta.grupo || '',
        priority: proposta.priority,
        icon: proposta.icon || 'FileText' // ✅ NOVO: Incluir ícone na duplicação
      };

      await ProposalService.createProposal(duplicateData);
      
      toast({
        title: "Sucesso",
        description: "Proposta duplicada com sucesso!",
      });

      await loadPropostas();
      
    } catch (error) {
      console.error('Erro ao duplicar proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível duplicar a proposta.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteProposta = async (proposta: Proposta) => {
    try {
      // Excluir diretamente sem confirmação
      await ProposalService.deleteProposal(proposta.id);
      
      // Remover a proposta da lista local imediatamente
      setPropostas(prevPropostas => 
        prevPropostas.filter(p => p.id !== proposta.id)
      );
      
      toast({
        title: "Sucesso",
        description: "Proposta excluída com sucesso!",
      });
      
    } catch (error) {
      console.error('❌ Erro ao excluir proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a proposta.",
        variant: "destructive",
      });
    }
  };

  // ✅ NOVA FUNÇÃO: Alterar status da proposta
  const handleChangeStatus = async (proposta: Proposta, newStatus: ProposalStatus) => {
    if (!currentUser) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive",
      });
      return;
    }

    // Não fazer nada se o status já é o mesmo
    if (proposta.status === newStatus) {
      return;
    }

    try {
      const statusLabel = PROPOSAL_STATUS_LABELS[newStatus];
      
      await ProposalService.updateProposal(proposta.id, {
        status: newStatus,
        updatedAt: new Date()
      });
      
      toast({
        title: "Sucesso",
        description: `Status alterado para "${statusLabel}"!`,
      });
      
      await loadPropostas();
    } catch (error) {
      console.error('Erro ao atualizar status da proposta:', error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status da proposta.",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: ProposalStatus) => {
    return <ProposalStatusBadge status={status} size="sm" />;
  };

  const getPriorityBadge = (priority: ProposalPriority) => {
    const colorClass = getProposalPriorityColor(priority);
    const labels = {
      low: 'Baixa',
      medium: 'Média',
      high: 'Alta'
    };
    
    return (
      <Badge className={colorClass}>
        {labels[priority]}
      </Badge>
    );
  };

  const filteredPropostas = propostas.filter(proposta => {
    const matchesSearch = proposta.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposta.grupo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposta.descricao?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'todos' || proposta.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const totalPropostas = propostas.length;
  const totalDraft = propostas.filter(p => p.status === 'draft').length;
  const totalSent = propostas.filter(p => p.status === 'sent').length;
  const totalAccepted = propostas.filter(p => p.status === 'accepted').length;
  const totalValue = propostas.reduce((sum, p) => sum + p.totalAmount, 0);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getBrandInfo = (brandId: string) => {
    const brand = brands.find(b => b.id === brandId);
    return brand || { id: brandId, name: brandId.substring(0, 8), logo: null };
  };

  // ✅ NOVA FUNÇÃO: Renderizar ícone da proposta
  const renderProposalIcon = (iconName: string = 'FileText') => {
    const iconConfig = AVAILABLE_ICONS.find(icon => icon.name === iconName);
    if (iconConfig) {
      const IconComponent = iconConfig.icon;
      return <IconComponent className="h-4 w-4" />;
    }
    return <FileText className="h-4 w-4" />;
  };

  // ✅ Mostrar loading enquanto Clerk carrega
  if (!isInitialized || authLoading || orgLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
          <p className="text-gray-600 dark:text-gray-400">Carregando autenticação e organizações...</p>
        </div>
      </div>
    );
  }

  // ✅ Mostrar mensagem se usuário não autenticado
  if (!currentUser?.id) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 mx-auto mb-4 text-yellow-600" />
          <p className="text-gray-600 dark:text-gray-400">Usuário não autenticado</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">Faça login para acessar suas propostas</p>
        </div>
      </div>
    );
  }

  // 🔒 NOVA PROTEÇÃO: Retornar 404 se usuário não for admin
  if (shouldShowAccessDenied) {
    router.push('/404');
    return null;
  }

  // ✅ NOVO: Mostrar mensagem se usuário não tem organização
  if (!currentOrganization) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 mx-auto mb-4 text-yellow-600" />
          <p className="text-gray-600 dark:text-gray-400">Organização Necessária</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
            Você precisa fazer parte de uma organização para acessar propostas
          </p>
          <Button 
            className="mt-4 bg-[#ff0074] hover:bg-[#ff0074]/90" 
            onClick={() => window.location.reload()}
          >
            Recarregar Página
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen p-6 flex flex-col gap-6 overflow-hidden">
      {/* Estatísticas */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 flex-shrink-0">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-[#ff0074]">{totalPropostas}</div>
            <p className="text-sm text-muted-foreground">Total de Propostas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{totalSent}</div>
            <p className="text-sm text-muted-foreground">Enviadas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{totalAccepted}</div>
            <p className="text-sm text-muted-foreground">Aceitas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-[#9810fa]">{formatCurrency(totalValue)}</div>
            <p className="text-sm text-muted-foreground">Valor Total</p>
          </CardContent>
        </Card>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between gap-4 flex-shrink-0">
        <div className="flex items-center gap-4">
          {/* Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar propostas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
       
          
          {/* Filtros */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Status: {statusFilter === 'todos' ? 'Todos' : PROPOSAL_STATUS_LABELS[statusFilter as ProposalStatus]}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('todos')}>
                Todos os Status
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {Object.entries(PROPOSAL_STATUS_LABELS).map(([status, label]) => (
                <DropdownMenuItem key={status} onClick={() => setStatusFilter(status)}>
                  {label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2">
          {/* Ações da Tabela */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Colunas
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setShowColumnsDialog(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Editar Colunas
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Criar Proposta */}
          <Button 
            onClick={handleCreateProposta}
            className="bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
            disabled={isLoading || !brandId || brandId === 'default-brand'}
            title={!brandId || brandId === 'default-brand' ? 'Selecione uma marca para criar propostas' : 'Criar nova proposta'}
          >
            <Plus className="h-4 w-4 mr-2" />
            Criar Proposta
          </Button>
        </div>
      </div>

      {/* Tabela de Propostas */}
      <Card className="flex-1 flex flex-col min-h-0 overflow-hidden">
        <CardContent className="p-0 flex-1 flex flex-col overflow-hidden">
          {/* ❌ REMOVER LOADER MANUAL E USAR APENAS O CONTROLE GLOBAL */}
          {filteredPropostas.length === 0 && !isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Nenhuma proposta encontrada</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== 'todos' 
                    ? 'Tente ajustar os filtros para ver mais resultados.'
                    : 'Crie sua primeira proposta para começar.'
                  }
                </p>
                {!searchTerm && statusFilter === 'todos' && (
                  <Button 
                    onClick={() => handleCreateProposta()} 
                    className="bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
                    disabled={!brandId || brandId === 'default-brand'}
                    title={!brandId || brandId === 'default-brand' ? 'Selecione uma marca para criar propostas' : 'Criar primeira proposta'}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Criar Primeira Proposta
                  </Button>
                )}
              </div>
            </div>
          ) : !isLoading ? (
            <div className="flex-1 overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                  <TableRow>
                    <TableHead>Nome da Proposta</TableHead>
                    {columnVisibility.marca && <TableHead>Marca</TableHead>}
                    {columnVisibility.influencers && <TableHead className="text-center">Influenciadores</TableHead>}
                    {columnVisibility.value && <TableHead className="text-center">Valor Total</TableHead>}
             
                    {columnVisibility.criadoPor && <TableHead>Criado Por</TableHead>}
                    {columnVisibility.dataEnvio && <TableHead>Data Envio</TableHead>}
                    {columnVisibility.grupo && <TableHead>Grupo</TableHead>}
                    {columnVisibility.dataCriacao && <TableHead>Criação</TableHead>}
                    {columnVisibility.ultimaAtualizacao && <TableHead>Última Atualização</TableHead>}
                    {columnVisibility.status && <TableHead>Status</TableHead>}
                    {columnVisibility.actions && <TableHead className="text-center">Ações</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPropostas.map((proposta) => (
                    <TableRow key={proposta.id}>
                      <TableCell className="group max-w-xs">
                        <div className="flex items-center justify-between">
                          <div className="min-w-0 flex-1 pr-4">
                            <button
                              className="font-medium truncate text-left hover:text-[#ff0074] transition-all duration-200 cursor-pointer focus:outline-none flex items-center gap-2"
                              title={`Abrir proposta: ${proposta.nome}`}
                              onClick={() => {
                                // Preservar parâmetros da URL atual
                                const currentParams = new URLSearchParams();
                                if (brandId) currentParams.set('brandId', brandId);
                                if (brandName) currentParams.set('brandName', brandName);
                                
                                const queryString = currentParams.toString();
                                const url = queryString 
                                  ? `/propostas/${proposta.id}?${queryString}`
                                  : `/propostas/${proposta.id}`;
                                
                                router.push(url);
                              }}
                            >
                              {/* ✅ NOVO: Mostrar ícone da proposta */}
                              <span className="text-[#ff0074] flex-shrink-0">
                                {renderProposalIcon(proposta.icon)}
                              </span>
                              <span className="truncate">
                                {proposta.nome}
                              </span>
                            </button>
                            {proposta.descricao && (
                              <div className="text-sm text-muted-foreground truncate" title={proposta.descricao}>
                                {proposta.descricao}
                              </div>
                            )}
                          </div>
                          
                          {/* Ícones de ação - visíveis apenas no hover */}
                          <div className="flex items-center gap-1 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                              onClick={() => handleEditProposta(proposta)}
                              title="Editar proposta"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600"
                              onClick={() => handleDuplicateProposta(proposta)}
                              title="Duplicar proposta"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                              onClick={() => handleDeleteProposta(proposta)}
                              title="Excluir proposta"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </TableCell>
                      {columnVisibility.marca && (
                        <TableCell>
                          {(() => {
                            const brandInfo = getBrandInfo(proposta.brandId);
                            return (
                              <div className="flex items-center gap-2">
                                {brandInfo.logo ? (
                                  <img 
                                    src={brandInfo.logo} 
                                    alt={brandInfo.name}
                                    className="w-6 h-6 rounded object-cover"
                                  />
                                ) : (
                                  <div className="w-6 h-6 rounded bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center text-xs text-white font-bold">
                                    {brandInfo.name.charAt(0).toUpperCase()}
                                  </div>
                                )}
                                <span className="truncate text-sm">{brandInfo.name}</span>
                              </div>
                            );
                          })()}
                        </TableCell>
                      )}
                      {columnVisibility.influencers && (
                        <TableCell className="text-center">
                          <Badge variant="outline">{proposta.influencers.length}</Badge>
                        </TableCell>
                      )}
                      {columnVisibility.value && (
                        <TableCell className="text-center">
                          <span className="font-medium">{formatCurrency(proposta.totalAmount)}</span>
                        </TableCell>
                      )}

                      {columnVisibility.criadoPor && (
                        <TableCell>
                          {/* ✅ CORRIGIDO: Exibir nome e imagem do usuário */}
                          <div className="flex items-center gap-2">
                            {proposta.criadoPorImagem ? (
                              <img 
                                src={proposta.criadoPorImagem} 
                                alt={proposta.criadoPorNome || proposta.criadoPor}
                                className="w-6 h-6 rounded-full object-cover border border-border"
                                onError={(e) => {
                                  // Fallback para avatar com inicial se a imagem falhar
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const fallbackDiv = target.nextElementSibling as HTMLElement;
                                  if (fallbackDiv) {
                                    fallbackDiv.style.display = 'flex';
                                  }
                                }}
                              />
                            ) : null}
                            <div 
                              className={`w-6 h-6 rounded-full bg-gradient-to-r from-[#9810fa] to-[#ff0074] flex items-center justify-center text-xs text-white font-bold ${proposta.criadoPorImagem ? 'hidden' : ''}`}
                            >
                              {(proposta.criadoPorNome || proposta.criadoPor).charAt(0).toUpperCase()}
                            </div>
                            <span className="text-sm" title={proposta.criadoPorNome || proposta.criadoPor}>
                              {proposta.criadoPorNome || proposta.criadoPor}
                            </span>
                          </div>
                        </TableCell>
                      )}
                      {columnVisibility.dataEnvio && (
                        <TableCell>
                          {proposta.dataEnvio ? new Date(proposta.dataEnvio).toLocaleDateString('pt-BR') : '-'}
                        </TableCell>
                      )}
                      {columnVisibility.grupo && (
                        <TableCell>{proposta.grupo || '-'}</TableCell>
                      )}
                      {columnVisibility.dataCriacao && (
                        <TableCell>{proposta.dataCriacao.toLocaleDateString('pt-BR')}</TableCell>
                      )}
                      {columnVisibility.ultimaAtualizacao && (
                        <TableCell>{proposta.ultimaAtualizacao.toLocaleDateString('pt-BR')}</TableCell>
                      )}
                      {columnVisibility.status && <TableCell>{getStatusBadge(proposta.status)}</TableCell>}
                      {columnVisibility.actions && (
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center gap-2">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem
                                  onClick={() => {
                                    // Preservar parâmetros da URL atual
                                    const currentParams = new URLSearchParams();
                                    if (brandId) currentParams.set('brandId', brandId);
                                    if (brandName) currentParams.set('brandName', brandName);
                                    
                                    const queryString = currentParams.toString();
                                    const url = queryString 
                                      ? `/propostas/${proposta.id}?${queryString}`
                                      : `/propostas/${proposta.id}`;
                                    
                                    router.push(url);
                                  }}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  Abrir
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleEditProposta(proposta)}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Editar
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleManageAccess(proposta)}
                                >
                                  <Users className="h-4 w-4 mr-2" />
                                  Gerenciar Acesso
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {/* ✅ NOVO: Dropdown de Status */}
                                <DropdownMenuItem
                                  onClick={() => handleChangeStatus(proposta, 'negotiating')}
                                  className={proposta.status === 'negotiating' ? 'bg-blue-50 text-blue-700' : ''}
                                >
                                  <Clock className="h-4 w-4 mr-2" />
                                  Em Andamento
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleChangeStatus(proposta, 'approved')}
                                  className={proposta.status === 'approved' ? 'bg-green-50 text-green-700' : ''}
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Concluída
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleChangeStatus(proposta, 'sent')}
                                  className={proposta.status === 'sent' ? 'bg-yellow-50 text-yellow-700' : ''}
                                >
                                  <Send className="h-4 w-4 mr-2" />
                                  Enviada
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleChangeStatus(proposta, 'draft')}
                                  className={proposta.status === 'draft' ? 'bg-gray-50 text-gray-700' : ''}
                                >
                                  <FileText className="h-4 w-4 mr-2" />
                                  Rascunho
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  className="text-red-600"
                                  onClick={() => handleDeleteProposta(proposta)}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Excluir
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* Sheet: Criar/Editar Proposta */}
      <Sheet open={showPropostaSheet} onOpenChange={setShowPropostaSheet}>
        <SheetContent className="w-[400px] sm:w-[500px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              {isEditMode ? (
                <Edit className="h-5 w-5 text-[#ff0074]" />
              ) : (
                <Plus className="h-5 w-5 text-[#ff0074]" />
              )}
              {isEditMode ? 'Editar Proposta' : 'Criar Nova Proposta'}
            </SheetTitle>
            <SheetDescription>
              {isEditMode 
                ? 'Modifique os detalhes da proposta conforme necessário.'
                : 'Preencha os detalhes da proposta. Você poderá adicionar influenciadores depois.'
              }
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Nome da Proposta *</label>
              <Input
                value={newPropostaName}
                onChange={(e) => setNewPropostaName(e.target.value)}
                placeholder="Ex: Proposta Campanha Verão 2024"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Descrição/Grupo (Opcional)</label>
              <Textarea
                value={newPropostaDescription}
                onChange={(e) => setNewPropostaDescription(e.target.value)}
                placeholder="Descrição da proposta ou nome do grupo..."
                rows={3}
                className="resize-none"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Data da Proposta
              </label>
              <Input
                type="date"
                value={newPropostaDate ? newPropostaDate.toISOString().split('T')[0] : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value) : new Date();
                  setNewPropostaDate(date);
                }}
                className="bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
              />
            </div>

            {/* ✅ NOVA SEÇÃO: Seleção de Ícone */}
            <div className="space-y-3">
              <label className="text-sm font-medium flex items-center gap-1">
                {renderProposalIcon(selectedIcon)}
                Ícone da Proposta
              </label>
              <div className="grid grid-cols-6 gap-2 p-3 border rounded-lg bg-background/50">
                {AVAILABLE_ICONS.map((iconConfig) => {
                  const IconComponent = iconConfig.icon;
                  return (
                    <button
                      key={iconConfig.name}
                      type="button"
                      onClick={() => setSelectedIcon(iconConfig.name)}
                      className={cn(
                        "p-2 rounded-md border transition-all duration-200 hover:scale-105 flex items-center justify-center",
                        selectedIcon === iconConfig.name
                          ? "border-[#ff0074] bg-[#ff0074]/10 text-[#ff0074]"
                          : "border-border hover:border-[#ff0074]/50 hover:bg-[#ff0074]/5"
                      )}
                      title={iconConfig.label}
                    >
                      <IconComponent className="h-4 w-4" />
                    </button>
                  );
                })}
              </div>
              <p className="text-xs text-muted-foreground">
                Escolha um ícone para representar visualmente esta proposta
              </p>
            </div>

          </div>

          <div className="flex items-center justify-end gap-3 mt-8">
            <Button 
              variant="outline" 
              onClick={() => setShowPropostaSheet(false)} 
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button 
              onClick={isEditMode ? handleUpdateProposta : handleSaveProposta} 
              className="bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
              disabled={isLoading || !newPropostaName.trim()}
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              {isEditMode ? 'Atualizar Proposta' : 'Criar Proposta'}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Dialog: Editar Colunas */}
      <Dialog open={showColumnsDialog} onOpenChange={setShowColumnsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Colunas</DialogTitle>
            <DialogDescription>
              Selecione quais colunas devem ser exibidas na tabela.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {Object.entries(columnVisibility).map(([key, visible]) => (
              <div key={key} className="flex items-center space-x-2">
                <Checkbox
                  id={key}
                  checked={visible}
                  onCheckedChange={(checked: boolean | 'indeterminate') =>
                    setColumnVisibility(prev => ({ ...prev, [key]: !!checked }))
                  }
                />
                <label htmlFor={key} className="text-sm font-medium capitalize">
                  {key === 'marca' && 'Marca'}
                  {key === 'influencers' && 'Influenciadores'}
                  {key === 'value' && 'Valor Total'}
             
                  {key === 'criadoPor' && 'Criado Por'}
                  {key === 'dataEnvio' && 'Data de Envio'}
                  {key === 'grupo' && 'Grupo'}
                  {key === 'dataCriacao' && 'Data de Criação'}
                  {key === 'ultimaAtualizacao' && 'Última Atualização'}
                  {key === 'status' && 'Status'}
                  {key === 'actions' && 'Ações'}
                </label>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowColumnsDialog(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>



      {/* Dialog: Gerenciar Acesso à Proposta */}
      {selectedProposal && (
        <ProposalInviteManager
          open={showInviteManager}
          onOpenChange={(open) => {
            setShowInviteManager(open);
            if (!open) {
              onModalClose(); // Executar limpeza quando modal for fechado
            }
          }}
          proposalId={selectedProposal.id}
          proposalName={selectedProposal.nome}
          currentUserRole="a" // Assumir que criador da proposta é agência
        />
      )}

    </div>
  );
}

export default function PropostasPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
          <p className="text-muted-foreground">Carregando propostas...</p>
        </div>
      </div>
    }>
      <PropostasPageContent />
    </Suspense>
  );
} 


