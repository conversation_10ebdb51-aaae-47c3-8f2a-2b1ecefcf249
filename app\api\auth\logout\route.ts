import { NextRequest, NextResponse } from 'next/server';

const COOKIE_NAME = 'brand-session';

export async function POST(request: NextRequest) {
  try {
    // Criar resposta de sucesso
    const response = NextResponse.json(
      { 
        success: true, 
        message: 'Logout realizado com sucesso' 
      },
      { status: 200 }
    );

    // Limpar o cookie de sessão
    response.cookies.set(COOKIE_NAME, '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0, // Expira imediatamente
      path: '/'
    });

    return response;
  } catch (error) {
    console.error('Erro na API de logout:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

