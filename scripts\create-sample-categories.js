// Script para criar categorias de exemplo no Firestore
const admin = require('firebase-admin');

// Inicializar Firebase Admin (assumindo que já está configurado)
if (!admin.apps.length) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'deumatch-demo'
  });
}

const db = admin.firestore();

const sampleCategories = [
  {
    name: 'Moda e Estilo',
    slug: 'moda-e-estilo',
    description: 'Influenciadores focados em moda, estilo e tendências',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Beleza e Cuidados',
    slug: 'beleza-e-cuidados',
    description: 'Conteúdo sobre beleza, skincare e cuidados pessoais',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Fitness e Saúde',
    slug: 'fitness-e-saude',
    description: 'Exercícios, nutrição e vida saudável',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Tecnologia',
    slug: 'tecnologia',
    description: 'Reviews, tutoriais e novidades tech',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Lifestyle',
    slug: 'lifestyle',
    description: 'Estilo de vida, viagens e experiências',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Gastronomia',
    slug: 'gastronomia',
    description: 'Culinária, receitas e experiências gastronômicas',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Gaming',
    slug: 'gaming',
    description: 'Jogos, streams e cultura gamer',
    userId: null, // Categoria global
    isActive: true
  },
  {
    name: 'Educação',
    slug: 'educacao',
    description: 'Conteúdo educativo e informativo',
    userId: null, // Categoria global
    isActive: true
  }
];

async function createSampleCategories() {
  try {
    console.log('🏷️ Criando categorias de exemplo...');
    
    const batch = db.batch();
    const categoriesRef = db.collection('categories');
    
    for (const category of sampleCategories) {
      const docRef = categoriesRef.doc();
      batch.set(docRef, {
        ...category,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
    
    await batch.commit();
    
    console.log(`✅ ${sampleCategories.length} categorias criadas com sucesso!`);
    
    // Verificar se foram criadas
    const snapshot = await categoriesRef.where('isActive', '==', true).get();
    console.log(`📊 Total de categorias ativas no banco: ${snapshot.size}`);
    
    snapshot.forEach(doc => {
      const data = doc.data();
      console.log(`  - ${data.name} (${data.slug})`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao criar categorias:', error);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  createSampleCategories().then(() => {
    console.log('🎉 Script concluído!');
    process.exit(0);
  });
}

module.exports = { createSampleCategories };