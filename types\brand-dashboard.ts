export type SocialNetwork = 'instagram' | 'youtube' | 'tiktok' | 'twitter' | 'facebook';

export interface SocialMediaProfile {
  network: SocialNetwork;
  username: string;
  url: string;
  isPrimary: boolean;
}

export type CampaignStatus = 'approved' | 'rejected' | 'analyzing' | 'proposal_rejected';

export type DeliveryType = 'stories' | 'shorts' | 'long_video' | 'fixed_post' | 'tiktok';

export interface Service {
  type: DeliveryType;
  price: number;
  quantity: number;
  counterProposal?: number;
}

export interface Metrics {
  screenshots: string[];
  demographicReport?: string;
  views?: number;
  engagement?: number;
}

export interface Influencer {
  id: string;
  name: string;
  socialNetworks: SocialMediaProfile[];
  approvalMonth: string;
  metrics: Metrics;
  previousDeliveries: {
    date: string;
    link: string;
    type: DeliveryType;
  }[];
  services: Service[];
  proposalDate: string;
  campaignMonth: string;
  postDate?: string;
  postLink?: string;
  status: CampaignStatus;
  observations?: string;
}

export interface Brand {
  id: string;
  name: string;
  logo?: string;
  campaigns: {
    id: string;
    name: string;
    month: string;
    influencers: Influencer[];
  }[];
} 

