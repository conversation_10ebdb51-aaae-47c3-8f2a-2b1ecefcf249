import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { BaseDocument } from '@/types/base';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';

/**
 * Interface para resultado de validação
 */
interface ValidationResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: string[];
}

/**
 * Interface para contexto de validação
 */
interface ValidationContext {
  userId: string;
  userRole?: string;
  requestId?: string;
  userAgent?: string;
  ipAddress?: string;
}

/**
 * Middleware para validar body da requisição com schema Zod
 */
export function validateRequestBody<T>(schema: z.ZodSchema<T>) {
  return async (req: NextRequest): Promise<ValidationResult<T>> => {
    try {
      const body = await req.json();
      const data = schema.parse(body);
      
      return {
        success: true,
        data
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return {
          success: false,
          error: 'Dados de entrada inválidos',
          errors
        };
      }
      
      return {
        success: false,
        error: 'Erro ao processar dados da requisição'
      };
    }
  };
}

/**
 * Middleware para validar ownership de documento
 */
export function validateOwnershipMiddleware<T extends BaseDocument>(
  getDocument: (id: string) => Promise<T | null>
) {
  return async (documentId: string, userId: string): Promise<ValidationResult<T>> => {
    try {
      const document = await getDocument(documentId);
      
      if (!document) {
        return {
          success: false,
          error: 'Documento não encontrado'
        };
      }

      if (!IsolationUtils.validateOwnership(document, userId)) {
        // Log de tentativa de acesso não autorizado
        IsolationUtils.logIsolationEvent(
          'read',
          'document',
          documentId,
          userId,
          { 
            ownerId: document.userId,
            violation: 'ownership_denied'
          }
        );
        
        return {
          success: false,
          error: 'Usuário não tem permissão para acessar este recurso'
        };
      }

      return {
        success: true,
        data: document
      };
    } catch (error) {
      return {
        success: false,
        error: 'Erro interno ao validar documento'
      };
    }
  };
}

/**
 * Middleware para validar relacionamentos entre entidades
 */
export function validateRelationshipMiddleware<P extends BaseDocument, C extends BaseDocument>(
  getParent: (id: string) => Promise<P | null>,
  getChild: (id: string) => Promise<C | null>
) {
  return async (
    parentId: string,
    childId: string,
    userId: string
  ): Promise<ValidationResult<{ parent: P; child: C }>> => {
    try {
      const [parent, child] = await Promise.all([
        getParent(parentId),
        getChild(childId)
      ]);

      if (!parent) {
        return {
          success: false,
          error: 'Documento pai não encontrado'
        };
      }

      if (!child) {
        return {
          success: false,
          error: 'Documento filho não encontrado'
        };
      }

      // Validar ownership de ambos
      if (!IsolationUtils.validateOwnership(parent, userId)) {
        return {
          success: false,
          error: 'Usuário não tem permissão para o documento pai'
        };
      }

      if (!IsolationUtils.validateOwnership(child, userId)) {
        return {
          success: false,
          error: 'Usuário não tem permissão para o documento filho'
        };
      }

      // Validar relacionamento
      if (!IsolationUtils.validateRelationship(parent, child)) {
        return {
          success: false,
          error: 'Documentos não pertencem ao mesmo usuário'
        };
      }

      return {
        success: true,
        data: { parent, child }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Erro interno ao validar relacionamento'
      };
    }
  };
}

/**
 * Middleware para validar batch de IDs
 */
export function validateBatchOwnershipMiddleware<T extends BaseDocument>(
  getDocuments: (ids: string[]) => Promise<T[]>
) {
  return async (
    documentIds: string[],
    userId: string
  ): Promise<ValidationResult<T[]>> => {
    try {
      if (!Array.isArray(documentIds) || documentIds.length === 0) {
        return {
          success: false,
          error: 'Lista de IDs é obrigatória'
        };
      }

      const documents = await getDocuments(documentIds);
      
      if (documents.length !== documentIds.length) {
        const foundIds = documents.map(doc => doc.id);
        const missingIds = documentIds.filter(id => !foundIds.includes(id));
        
        return {
          success: false,
          error: `Documentos não encontrados: ${missingIds.join(', ')}`
        };
      }

      const ownershipValidation = IsolationUtils.validateBatchOwnership(documents, userId);
      
      if (!ownershipValidation.isValid) {
        return {
          success: false,
          error: `Usuário não tem permissão para os documentos: ${ownershipValidation.invalidIds.join(', ')}`
        };
      }

      return {
        success: true,
        data: documents
      };
    } catch (error) {
      return {
        success: false,
        error: 'Erro interno ao validar lote de documentos'
      };
    }
  };
}

/**
 * Middleware wrapper para APIs que precisam de autenticação e isolamento
 */
export function withUserIsolation<T>(
  handler: (req: NextRequest, context: ValidationContext, ...args: any[]) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: any[]): Promise<NextResponse> => {
    try {
      // Extrair userId do token/sessão (implementação específica do projeto)
      const userId = await getUserIdFromRequest(req);
      
      if (!userId) {
        return NextResponse.json(
          { error: 'Usuário não autenticado' },
          { status: 401 }
        );
      }

      // Criar contexto de validação
      const context: ValidationContext = {
        userId,
        requestId: generateRequestId(),
        userAgent: req.headers.get('user-agent') || undefined,
        ipAddress: getClientIp(req)
      };

      // Log da requisição
      IsolationUtils.logIsolationEvent(
        'read',
        'api_request',
        req.url,
        userId,
        {
          method: req.method,
          userAgent: context.userAgent,
          ipAddress: context.ipAddress
        }
      );

      return await handler(req, context, ...args);
    } catch (error) {
      if (error instanceof IsolationError) {
        return NextResponse.json(
          { 
            error: error.message,
            code: error.code
          },
          { status: 403 }
        );
      }

      console.error('Erro no middleware de isolamento:', error);
      return NextResponse.json(
        { error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware para validar e sanitizar query parameters
 */
export function validateQueryParams<T>(schema: z.ZodSchema<T>) {
  return (req: NextRequest): ValidationResult<T> => {
    try {
      const { searchParams } = new URL(req.url);
      const params: Record<string, any> = {};

      // Converter URLSearchParams para objeto
      searchParams.forEach((value, key) => {
        // Tentar converter arrays (ex: tags[]=tag1&tags[]=tag2)
        if (key.endsWith('[]')) {
          const realKey = key.slice(0, -2);
          if (!params[realKey]) params[realKey] = [];
          params[realKey].push(value);
        }
        // Tentar converter números
        else if (!isNaN(Number(value))) {
          params[key] = Number(value);
        }
        // Tentar converter booleans
        else if (value === 'true' || value === 'false') {
          params[key] = value === 'true';
        }
        // Manter como string
        else {
          params[key] = value;
        }
      });

      const data = schema.parse(params);
      
      return {
        success: true,
        data
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return {
          success: false,
          error: 'Parâmetros de consulta inválidos',
          errors
        };
      }

      return {
        success: false,
        error: 'Erro ao processar parâmetros de consulta'
      };
    }
  };
}

/**
 * Middleware para rate limiting por usuário
 */
export function withRateLimit(
  maxRequests: number = 100,
  windowMs: number = 60000 // 1 minuto
) {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (userId: string): boolean => {
    const now = Date.now();
    const userRequests = requests.get(userId);

    if (!userRequests || now > userRequests.resetTime) {
      // Reset ou primeira requisição
      requests.set(userId, {
        count: 1,
        resetTime: now + windowMs
      });
      return true;
    }

    if (userRequests.count >= maxRequests) {
      return false; // Limite excedido
    }

    userRequests.count++;
    return true;
  };
}

/**
 * Utilitários auxiliares
 */
async function getUserIdFromRequest(req: NextRequest): Promise<string | null> {
  // Implementação específica para extrair userId do token JWT, sessão, etc.
  // Esta é uma implementação exemplo
  const authHeader = req.headers.get('authorization');
  if (!authHeader?.startsWith('Bearer ')) {
    return null;
  }

  try {
    // Aqui você decodificaria o JWT e extrairia o userId
    // Por enquanto, retorna um valor exemplo
    return 'user-from-token';
  } catch {
    return null;
  }
}

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function getClientIp(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIp) {
    return realIp;
  }
  
  return 'unknown';
}

/**
 * Tipos exportados
 */
export type { ValidationResult, ValidationContext }; 

