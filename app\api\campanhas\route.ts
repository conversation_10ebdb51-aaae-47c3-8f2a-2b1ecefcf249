import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { IsolationUtils } from '@/lib/utils/isolation';
import { CampaignService } from '@/services/campaign-service';

/**
 * 🔒 API DE CAMPANHAS COM ISOLAMENTO COMPLETO
 * Garantindo que cada usuário só acesse suas próprias campanhas
 */

// ✅ GET - Listar campanhas com isolamento por usuário
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    console.log('🔍 [CAMPAIGNS_API] Buscando campanhas para usuário:', userId);
    
    const { searchParams } = new URL(req.url);
    const requestedBrandId = searchParams.get('brandId');
    
    // ✅ VALIDAÇÃO: brandId deve ser igual ao userId (isolamento)
    if (requestedBrandId && requestedBrandId !== userId) {
      console.warn('⚠️ [CAMPAIGNS_API] Tentativa de acesso a marca diferente:', {
        userId,
        requestedBrandId
      });
      
      return NextResponse.json(
        { 
          error: 'Acesso negado. Você só pode acessar suas próprias campanhas.',
          code: 'OWNERSHIP_DENIED'
        },
        { status: 403 }
      );
    }

    // ✅ USAR userId COMO brandId PARA GARANTIR ISOLAMENTO
    const brandId = userId;
    
    // Aplicar filtros da query string
    const status = searchParams.get('status');
    const platform = searchParams.get('platform');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    const filters: any = {
      brandId // ✅ FORÇAR FILTRO POR brandId = userId
    };
    
    if (status) filters.status = status;
    if (platform) filters.platform = platform;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    
    // ✅ BUSCAR CAMPANHAS COM ISOLAMENTO
    const campanhas = await CampaignService.getCampaignsByBrand(brandId);
    
    // ✅ VALIDAÇÃO ADICIONAL: Confirmar que todas pertencem ao usuário
    const validCampanhas = campanhas.filter(campaign => 
      campaign.brandId === userId || campaign.createdBy === userId
    );
    
    if (validCampanhas.length !== campanhas.length) {
      console.warn('⚠️ [CAMPAIGNS_API] Algumas campanhas foram filtradas por não pertencerem ao usuário');
    }
    
    // Aplicar filtros adicionais no lado do servidor
    let filteredCampanhas = validCampanhas;
    
    if (status) {
      filteredCampanhas = filteredCampanhas.filter(c => c.status === status);
    }
    
    if (platform) {
      filteredCampanhas = filteredCampanhas.filter(c => 
        c.platforms?.includes(platform)
      );
    }
    
    if (startDate) {
      filteredCampanhas = filteredCampanhas.filter(c => 
        new Date(c.startDate || '') >= new Date(startDate)
      );
    }
    
    if (endDate) {
      filteredCampanhas = filteredCampanhas.filter(c => 
        new Date(c.endDate || '') <= new Date(endDate)
      );
    }
    
    // ✅ CALCULAR ESTATÍSTICAS ISOLADAS
    const stats = {
      total: filteredCampanhas.length,
      active: filteredCampanhas.filter(c => c.status === 'active').length,
      completed: filteredCampanhas.filter(c => c.status === 'completed').length,
      draft: filteredCampanhas.filter(c => c.status === 'draft').length,
      totalBudget: filteredCampanhas.reduce((sum, c) => sum + (c.budget || 0), 0),
      activeBudget: filteredCampanhas
        .filter(c => c.status === 'active')
        .reduce((sum, c) => sum + (c.budget || 0), 0)
    };

    console.log(`✅ [CAMPAIGNS_API] ${filteredCampanhas.length} campanhas carregadas para usuário ${userId}`);

    // ✅ LOG DE AUDITORIA
    IsolationUtils.logIsolationEvent(
      'read',
      'campaigns',
      'list',
      userId,
      { count: filteredCampanhas.length, filters }
    );

    return NextResponse.json({
      success: true,
      campanhas: filteredCampanhas,
      stats,
      brandId: userId, // ✅ SEMPRE RETORNAR userId COMO brandId
      userId,
      filters: filters
    });

  } catch (error) {
    console.error('❌ [CAMPAIGNS_API] Erro ao buscar campanhas:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
});

// ✅ POST - Criar campanha com isolamento
export const POST = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    console.log('➕ [CAMPAIGNS_API] Criando campanha para usuário:', userId);
    
    const body = await req.json();
    
    // ✅ VALIDAÇÃO DOS DADOS OBRIGATÓRIOS
    const requiredFields = ['name', 'description', 'budget', 'startDate', 'endDate', 'targetAudience', 'platforms', 'objectives'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Campos obrigatórios faltando: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }
    
    // ✅ VALIDAÇÃO DE DATAS
    const startDate = new Date(body.startDate);
    const endDate = new Date(body.endDate);
    
    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Data de início deve ser anterior à data de fim' },
        { status: 400 }
      );
    }
    
    // ✅ VALIDAÇÃO: brandId deve ser igual ao userId
    const requestedBrandId = body.brandId;
    if (requestedBrandId && requestedBrandId !== userId) {
      console.warn('⚠️ [CAMPAIGNS_API] Tentativa de criar campanha para marca diferente:', {
        userId,
        requestedBrandId
      });
      
      return NextResponse.json(
        { 
          error: 'Você só pode criar campanhas para sua própria marca.',
          code: 'OWNERSHIP_DENIED'
        },
        { status: 403 }
      );
    }
    
    // ✅ PREPARAR DADOS COM ISOLAMENTO
    const campaignData = IsolationUtils.prepareCreateData(
      {
        name: body.name,
        description: body.description,
        status: body.status || 'draft',
        budget: Number(body.budget),
        startDate: body.startDate,
        endDate: body.endDate,
        targetAudience: body.targetAudience,
        platforms: Array.isArray(body.platforms) ? body.platforms : [body.platforms],
        objectives: Array.isArray(body.objectives) ? body.objectives : [body.objectives],
        kpis: body.kpis || {
          reach: 0,
          engagement: 0,
          conversions: 0
        },
        brandId: userId // ✅ FORÇAR brandId = userId
      },
      userId,
      userId // createdBy = userId
    );
    
    // ✅ CRIAR CAMPANHA
    const campaignId = await CampaignService.createCampaign(campaignData);
    
    console.log(`✅ [CAMPAIGNS_API] Campanha criada com ID: ${campaignId}`);

    // ✅ LOG DE AUDITORIA
    IsolationUtils.logIsolationEvent(
      'create',
      'campaigns',
      campaignId,
      userId,
      { 
        name: campaignData.name,
        budget: campaignData.budget 
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Campanha criada com sucesso',
      campaignId,
      campanha: {
        id: campaignId,
        ...campaignData
      }
    }, { status: 201 });

  } catch (error) {
    console.error('❌ [CAMPAIGNS_API] Erro ao criar campanha:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
});

