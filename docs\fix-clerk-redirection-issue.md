# Como Resolver o Problema de Redirecionamento do Clerk

## 🚨 Problema Identificado

Quando usuários convidados aceitam convites e fazem signup, eles estão sendo redirecionados para:
```
http://localhost:3000/?__clerk_db_jwt=dvb_2zZGqLg16LFGWwZhcvcCGowmoKd
```

## 🔍 Causa Raiz

O token `__clerk_db_jwt` é específico do ambiente de desenvolvimento do Clerk e indica configurações incorretas no Account Portal.

## ✅ Soluções Implementadas

### 1. Middleware Atualizado
- O middleware já detecta URLs com `__clerk_db_jwt`
- Redireciona automaticamente para o dashboard quando detectado
- Previne loops de redirecionamento

### 2. Componente ClerkInviteHandler
- Captura tokens JWT problemáticos no frontend
- Remove parâmetros inválidos da URL
- Redireciona usuários autenticados para onboarding

### 3. Configurações do SignUp
- Adicionadas múltiplas URLs de redirecionamento de fallback
- `afterSignUpUrl="/onboarding"`
- `redirectUrl="/onboarding"`
- `fallbackRedirectUrl="/onboarding"`

## 🛠️ Configurações Necessárias no Account Portal do Clerk

### Acesse: https://dashboard.clerk.com/

1. **Paths → Redirect URLs**
   - After sign-up fallback: `/onboarding`
   - After sign-in fallback: `/dashboard`

2. **Paths → Organization redirects**
   - After create organization: `/dashboard`
   - After leave organization: `/sign-in`

3. **Development Environment**
   - Verificar se as URLs de desenvolvimento estão corretas
   - Remover qualquer configuração que aponte para tokens específicos

## 🔧 Verificações Adicionais

### No `.env`:
```env
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/onboarding"
```

### Logs para Debug:
- O middleware registra quando detecta o JWT problemático
- O componente ClerkInviteHandler registra limpezas de URL

## 📋 Testes Recomendados

1. Criar um convite para um novo usuário
2. Aceitar o convite e fazer signup
3. Verificar se o redirecionamento vai para `/onboarding`
4. Verificar se não há parâmetros `__clerk_db_jwt` na URL

## 🚀 Próximos Passos

1. Verificar configurações no Account Portal do Clerk
2. Testar o fluxo completo de convites
3. Monitorar logs para detectar outros casos edge
4. Considerar implementar webhook para processar convites aceitos 