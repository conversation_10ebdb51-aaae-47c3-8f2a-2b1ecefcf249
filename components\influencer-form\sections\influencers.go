{"data": {"influencers": {"totalCount": 20, "hasNextPage": false, "hasPreviousPage": false, "nodes": [{"id": "j2ElhOopXa5vCAZ1CczW", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 23, "gender": "not_specified", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/j2ElhOopXa5vCAZ1CczW_1753126469281.jpg", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 6833235, "totalViews": 0, "engagementRate": 12.52, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222222, "instagramReelsViews": 2222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "john", "youtubeFollowers": 2222222, "youtubeSubscribers": 2222222, "youtubeEngagementRate": 22, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "casalrevelidade", "facebookFollowers": 1688223, "facebookEngagementRate": 3.05, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.967Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "70JEEmllFNIn8AJuVYRx", "services": {"instagram": {"story": {"price": 22.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T19:34:46.034Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "WVQ7WAixxstJGteosPdY", "platform": "facebook", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON>, BA, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São <PERSON>lo, RJ, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São Paulo, SP, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.8, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "itRrTYxn1CZtDi4VtHJc", "platform": "youtube", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "18-24", "percentage": 47.8, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 36.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.9, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 7.7, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.9, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.2, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "ZyyGT98vzQT2C2kYOyR1", "platform": "tiktok", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "oaZSaz8kFVNnT5TXvpgb", "platform": "instagram", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Paulo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São <PERSON>lo, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Ribeirão <PERSON>, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Florianópolis, SC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Campo Grande, MS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, MA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Macapá, AP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Palmas, TO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T19:21:11.969Z", "updatedAt": "2025-07-21T19:34:42.145Z", "__typename": "Influencer"}, {"id": "qyf2WZ0UoWaqNbQJK3tY", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "MARIAH SOARES", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/qyf2WZ0UoWaqNbQJK3tY_1753126207141.jpeg", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 5845580, "totalViews": 0, "engagementRate": 9.78, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 1688223, "youtubeSubscribers": 1688223, "youtubeEngagementRate": 3.05, "youtubeAvgViews": null, "youtubeShortsViews": 222222, "youtubeLongFormViews": 2222222, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "youtube", "mainPlatform": "youtube", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.968Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "nDGRXTc6ezltUkJcMLJ8", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T19:30:28.792Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "sYtmRYETgaaV7iVqLk3W", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "YndIMoMeTxGvXahVk16W", "platform": "youtube", "audienceGender": {"male": 14.7, "female": 85.3, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, RJ, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 14.7, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 17.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 13.5, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 11.2, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 10.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "rkjKMQelnKJ2X7VJLYNo", "platform": "tiktok", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "rxtxc7uYBE0PXSNuQFym", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:58:38.432Z", "updatedAt": "2025-07-21T19:30:23.310Z", "__typename": "Influencer"}, {"id": "03kyiEw9NnlQD1UNQYNu", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "POOCAAA", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 36, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 13376446, "totalViews": 0, "engagementRate": 6.75, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 666666, "instagramReelsViews": 666666666, "tiktokUsername": "<PERSON><PERSON>", "tiktokFollowers": 1688223, "tiktokEngagementRate": 14.7, "tiktokAvgViews": null, "tiktokVideoViews": 333333, "youtubeUsername": "ArsenalFC", "youtubeFollowers": 10000000, "youtubeSubscribers": 10000000, "youtubeEngagementRate": 2.5, "youtubeAvgViews": null, "youtubeShortsViews": 222222, "youtubeLongFormViews": 22222, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.968Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "LwBHCNDijCggVgpq8Iv3", "services": {"instagram": {"story": {"price": 555555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 666666.66, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 3333.33, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": null, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:57:22.437Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "JxNgfKazd8ZeB6ObY16O", "platform": "youtube", "audienceGender": {"male": 60, "female": 40, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "United Kingdom", "percentage": 40, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 20, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 10, "__typename": "AudienceLocation"}, {"country": "Spain", "percentage": 10, "__typename": "AudienceLocation"}, {"country": "India", "percentage": 10, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "London", "percentage": 20, "__typename": "AudienceCity"}, {"city": "New York", "percentage": 10, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 5, "__typename": "AudienceCity"}, {"city": "Madrid", "percentage": 5, "__typename": "AudienceCity"}, {"city": "Mumbai", "percentage": 5, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "18-24", "percentage": 30, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 40, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 20, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "0dpVRMR1JraxFzMe7hqq", "platform": "tiktok", "audienceGender": {"male": 14.7, "female": 85.3, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 99.9, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 0.1, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON>, BA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São <PERSON>lo, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, RS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Campo Grande, MS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Florianópolis, SC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>menau, SC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, MA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Macapá, AP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Palmas, TO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Aracaju, SE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Vitória, ES, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Rio Branco, AC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São Paulo, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON>, BA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São <PERSON>lo, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, RS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Campo Grande, MS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Florianópolis, SC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>menau, SC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON>, MA, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Macapá, AP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Palmas, TO, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Aracaju, SE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Vitória, ES, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Rio Branco, AC, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "aT17E2E0M0FP4jzysv4o", "platform": "instagram", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "São José do Rio Preto, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Ribeirão <PERSON>, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Sorocaba, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:57:21.591Z", "updatedAt": "2025-07-21T18:57:21.591Z", "__typename": "Influencer"}, {"id": "TYBZJpWgOO8im9aavUI4", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "XXBBBB", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 36, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 1688223, "totalViews": 0, "engagementRate": 3.05, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 666666, "instagramReelsViews": 666666666, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.968Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "j0S9sSMb4hXGvpEaEpqh", "services": {"instagram": {"story": {"price": 555555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 666666.66, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": null, "youtube": null, "facebook": null, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:54:30.800Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "ndrQP1BuTYuUno9HfpI0", "platform": "instagram", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "São José do Rio Preto, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Ribeirão <PERSON>, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Sorocaba, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.4, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:54:28.724Z", "updatedAt": "2025-07-21T18:54:28.724Z", "__typename": "Influencer"}, {"id": "G0iItzm3uaThemuEHGMh", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "ASDFGH", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 5845580, "totalViews": 0, "engagementRate": 9.78, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 1688223, "youtubeSubscribers": 1688223, "youtubeEngagementRate": 3.05, "youtubeAvgViews": null, "youtubeShortsViews": 222222, "youtubeLongFormViews": 2222222, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 22222, "facebookStoriesViews": 2222, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "youtube", "mainPlatform": "youtube", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.968Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "pUITcyJJNAZ8aLNNqRpl", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:49:26.922Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "30WHdWz0Sn7mcAfoE0JU", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "c2izkDRAFGywpd2y8Mt6", "platform": "youtube", "audienceGender": {"male": 14.7, "female": 85.3, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, RJ, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 14.7, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 17.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 13.5, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 11.2, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 10.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "fdT3lIqQqo1GfWO5NWrz", "platform": "tiktok", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "T81HohlsoWUsKo047kw2", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:49:25.006Z", "updatedAt": "2025-07-21T18:49:25.006Z", "__typename": "Influencer"}, {"id": "COfQSCs36yyzoKkMMLbI", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "RRRRRRRR", "email": "<EMAIL>", "phone": "+5555555555555", "whatsapp": "+5555555555555", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 22, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 0, "totalViews": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "instagramStoriesViews": 0, "instagramReelsViews": 0, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "prashoop.com", "agencyName": "prashoop.com", "responsibleCapturer": "prashoop.com", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:46:16.829Z", "updatedAt": "2025-07-21T18:46:16.829Z", "__typename": "Influencer"}, {"id": "b4JNpLpOf4AEfUPs53WN", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "VVVVVV", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 5845580, "totalViews": 0, "engagementRate": 9.78, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 1688223, "youtubeSubscribers": 1688223, "youtubeEngagementRate": 3.05, "youtubeAvgViews": null, "youtubeShortsViews": 222222, "youtubeLongFormViews": 2222222, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 22222, "facebookStoriesViews": 2222, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "TXBMZm6bjV4pMKEcnd1F", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:46:13.651Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "Kt2ILbrGyof96LDv8rxH", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "7rjf9JTVrXr4LnTPio2t", "platform": "youtube", "audienceGender": {"male": 14.7, "female": 85.3, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, RJ, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 14.7, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 17.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 13.5, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 11.2, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 10.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "6nxMnQvjuZt0SXgo7S4g", "platform": "tiktok", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "JMhUu5sLMuANhdH9ZSLg", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:46:08.446Z", "updatedAt": "2025-07-21T18:46:08.446Z", "__typename": "Influencer"}, {"id": "zbmSKDyUGlL7cSPeiWDU", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "TTTTTTTTTTTTTTT", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 5845580, "totalViews": 0, "engagementRate": 9.78, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 1688223, "youtubeSubscribers": 1688223, "youtubeEngagementRate": 3.05, "youtubeAvgViews": null, "youtubeShortsViews": 222222, "youtubeLongFormViews": 2222222, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 22222, "facebookStoriesViews": 2222, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "VqULOmigJRflzjyK2oDo", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:39:40.753Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "gCQjMLYQ0oZpYdIUifqY", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "iQwThpyK9rOjrAZSJMuO", "platform": "youtube", "audienceGender": {"male": 14.7, "female": 85.3, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, RJ, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 14.7, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 17.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 13.5, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 11.2, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 10.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "tS2fDMBFxWniMAw50xHk", "platform": "tiktok", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "wZmgUJtlWKQcmmhcv5kU", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:39:39.243Z", "updatedAt": "2025-07-21T18:39:39.243Z", "__typename": "Influencer"}, {"id": "gOIq4aFy3QYPzJ0C78oK", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "TTTTTTTTTTTTTTT", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 5845580, "totalViews": 0, "engagementRate": 9.78, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 1688223, "youtubeSubscribers": 1688223, "youtubeEngagementRate": 3.05, "youtubeAvgViews": null, "youtubeShortsViews": 222222, "youtubeLongFormViews": 2222222, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 22222, "facebookStoriesViews": 2222, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "b8HsmsgCbyhGIDLqeMiE", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:36:15.984Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "C6SbEQkdILfFh3MSJIGe", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "2ImJq9UEZ6KXPsGiX4tv", "platform": "youtube", "audienceGender": {"male": 14.7, "female": 85.3, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Manaus, AM, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Goiânia, GO, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Maceió, AL, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Belém, PA, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Porto Alegre, RS, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON>, PB, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, RJ, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Cuiabá, MT, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}, {"city": "Boa Vista, RR, Brazil", "percentage": 0.6, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 14.7, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 17.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 13.5, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 11.2, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 10.4, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "MV9DL25K9yja5mBGkneo", "platform": "tiktok", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "LiTx4RSR3h7hDXqNEG7c", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:36:11.773Z", "updatedAt": "2025-07-21T18:36:11.773Z", "__typename": "Influencer"}, {"id": "2ma1L1o3Sa73f6T4o3hw", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "TTTTTTTTTTTTTTT", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 4157357, "totalViews": 0, "engagementRate": 12.02, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "john", "tiktokFollowers": 1234567, "tiktokEngagementRate": 22, "tiktokAvgViews": null, "tiktokVideoViews": 2222222, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 22222, "facebookStoriesViews": 2222, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "4LWylt7nOmlQvkqJmOX2", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": null, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:33:31.164Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "KQZt33vdBRfK4ekzS6Lu", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "mYInGWrLGA4ge1VlZMWg", "platform": "tiktok", "audienceGender": {"male": 81.9, "female": 17.9, "other": 0.2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "7WuqSHs7ngGVbCaTrRiQ", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:33:30.384Z", "updatedAt": "2025-07-21T18:33:30.384Z", "__typename": "Influencer"}, {"id": "9ygg6UvTXYPjurgHzPzB", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "YYYYYYYYYY", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 32, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 2922790, "totalViews": 0, "engagementRate": 7.03, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "<PERSON><PERSON>", "instagramFollowers": 1688223, "instagramEngagementRate": 3.05, "instagramAvgViews": null, "instagramStoriesViews": 2222, "instagramReelsViews": 222222, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "<PERSON><PERSON>", "facebookFollowers": 1234567, "facebookEngagementRate": 11, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 22222, "facebookStoriesViews": 2222, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "aN6wHoEAJQMPqsvUrLqf", "services": {"instagram": {"story": {"price": 22222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": null, "youtube": null, "facebook": {"post": {"price": 222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:31:39.441Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "oUNRWw6cZq3b0uqxO8BJ", "platform": "facebook", "audienceGender": {"male": 87.8, "female": 12.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "FnmDJbRy6O5cp4Ty4bMi", "platform": "instagram", "audienceGender": {"male": 14.9, "female": 85.1, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brazil", "percentage": 86.2, "__typename": "AudienceLocation"}, {"country": "United States", "percentage": 5.7, "__typename": "AudienceLocation"}, {"country": "Brazil", "percentage": 100, "__typename": "AudienceLocation"}, {"country": "Brasil", "percentage": 100, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo, SP, Brazil", "percentage": 14.9, "__typename": "AudienceCity"}, {"city": "Campinas, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON>, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "São Bernardo, SP, Brazil", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro, RJ, Brazil", "percentage": 0.9, "__typename": "AudienceCity"}, {"city": "Fortaleza, CE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Bel<PERSON> Horizon<PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Brasília, DF, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Maringá, PR, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Santa Cruz, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Diadema, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São José dos Campos, SP, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Natal, RN, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Recife, PE, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON>, MG, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "Salvador, BA, Brazil", "percentage": 0.7, "__typename": "AudienceCity"}, {"city": "São Paulo", "percentage": 15.4, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.7, "__typename": "AudienceCity"}, {"city": "Rio de Janeiro", "percentage": 1.2, "__typename": "AudienceCity"}, {"city": "Salvador", "percentage": 1, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 16.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 33.9, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 26.3, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 15.7, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 4.6, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 1.7, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 0.3, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:31:39.146Z", "updatedAt": "2025-07-21T18:31:39.146Z", "__typename": "Influencer"}, {"id": "vW00MwhjLxzWKuJCDvGV", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "GGGGGGGG", "email": "", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "", "city": "", "location": "", "age": 22, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 0, "totalViews": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "instagramStoriesViews": 0, "instagramReelsViews": 0, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "", "agencyName": "", "responsibleCapturer": null, "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:24:05.417Z", "updatedAt": "2025-07-21T18:24:05.417Z", "__typename": "Influencer"}, {"id": "WLkHIlXJQS9ROv13IYoM", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "FFFFFFF", "email": "", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "", "city": "", "location": "", "age": 22, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 222222222, "totalViews": 0, "engagementRate": 22, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "marina<PERSON><PERSON>va_", "instagramFollowers": 222222222, "instagramEngagementRate": 22, "instagramAvgViews": null, "instagramStoriesViews": 0, "instagramReelsViews": 0, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "", "agencyName": "", "responsibleCapturer": null, "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "b9vKpev3N1tMu1rrr80s", "services": {"instagram": {"story": {"price": 2222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 222222.22, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": null, "youtube": null, "facebook": null, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-21T18:18:13.960Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:18:13.232Z", "updatedAt": "2025-07-21T18:18:13.232Z", "__typename": "Influencer"}, {"id": "cBHwpEqSUE65vhQix3Cv", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 22, "gender": "male", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 0, "totalViews": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "instagramStoriesViews": 0, "instagramReelsViews": 0, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-21T18:13:49.705Z", "updatedAt": "2025-07-21T18:13:49.705Z", "__typename": "Influencer"}, {"id": "Dvr9bWx5ffTAESPRh0Xd", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "ttttYYYY", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "", "city": "<PERSON><PERSON><PERSON>", "location": "", "age": 22, "gender": "male", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": "Esportes", "categories": ["Esportes", "Vlogs", "Moda & Beleza"], "totalFollowers": 0, "totalViews": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "instagramStoriesViews": 0, "instagramReelsViews": 0, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 0, "facebookStoriesViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": null, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-18T21:44:09.074Z", "updatedAt": "2025-07-18T21:48:30.897Z", "__typename": "Influencer"}, {"id": "12kIhCdlyaf5oWfxq0dh", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "JJAAAA", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 23, "gender": "female", "bio": "", "avatar": "", "backgroundImage": null, "gradient": null, "category": "Lifestyle", "categories": [], "totalFollowers": 0, "totalViews": 0, "engagementRate": 0, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "", "instagramFollowers": 0, "instagramEngagementRate": 0, "instagramAvgViews": null, "instagramStoriesViews": 0, "instagramReelsViews": 0, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": null, "facebookStoriesViews": null, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "CGayhLMui5Y6pQhqr5k7", "services": {"instagram": {"story": {"price": 77.77, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 7.77, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": null, "youtube": null, "facebook": null, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-18T19:40:15.981Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-18T19:26:58.219Z", "updatedAt": "2025-07-18T19:26:58.219Z", "__typename": "Influencer"}, {"id": "e834SnQ1qPWBlFobQJ4O", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "Mariana", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 18, "gender": "female", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752865929078_1752865929992.jpg", "backgroundImage": null, "gradient": null, "category": "Lifestyle", "categories": [], "totalFollowers": 26713478, "totalViews": 111110, "engagementRate": 39.33, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "filiperet", "instagramFollowers": 132000, "instagramEngagementRate": 21, "instagramAvgViews": null, "instagramStoriesViews": 55555, "instagramReelsViews": 55555, "tiktokUsername": "@marinasilva", "tiktokFollowers": 22222222, "tiktokEngagementRate": 23, "tiktokAvgViews": null, "tiktokVideoViews": 55555, "youtubeUsername": "fgggg", "youtubeFollowers": 1234567, "youtubeSubscribers": 1234567, "youtubeEngagementRate": 23, "youtubeAvgViews": null, "youtubeShortsViews": 55555, "youtubeLongFormViews": 55555, "facebookUsername": "rafaelchocolate", "facebookFollowers": 1234567, "facebookEngagementRate": 53, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": null, "facebookStoriesViews": null, "twitchUsername": "fress", "twitchFollowers": 655555, "twitchEngagementRate": 63, "twitchViews": 55555, "kwaiUsername": "hhhh", "kwaiFollowers": 1234567, "kwaiEngagementRate": 53, "kwaiViews": 55555, "promotesTraders": true, "responsibleName": "prashoop.com", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "<PERSON><PERSON><PERSON>", "mainNetwork": "", "mainPlatform": "", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.969Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "yAReWqOqU4frllhT2wzc", "services": {"instagram": {"story": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": {"stream": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TwitchPricing"}, "kwai": {"video": {"price": 555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "KwaiPricing"}, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-18T19:12:13.146Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-18T19:12:11.102Z", "updatedAt": "2025-07-18T19:12:11.102Z", "__typename": "Influencer"}, {"id": "3kxm1UutKCnitRvfPqTq", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 23, "gender": "female", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752728373279_1752728378748.jpg", "backgroundImage": null, "gradient": null, "category": "Lifestyle", "categories": [], "totalFollowers": 33529638, "totalViews": 0, "engagementRate": 22, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "filiperet", "instagramFollowers": 132000, "instagramEngagementRate": 22, "instagramAvgViews": null, "instagramStoriesViews": 5555555, "instagramReelsViews": 5555555, "tiktokUsername": "@rafaelchef", "tiktokFollowers": 64305, "tiktokEngagementRate": 0, "tiktokAvgViews": null, "tiktokVideoViews": 0, "youtubeUsername": "<PERSON><PERSON>", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": null, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "rafaelchocolate", "facebookFollowers": 33333333, "facebookEngagementRate": 22, "facebookAvgViews": null, "facebookViews": null, "facebookReelsViews": 8888888, "facebookStoriesViews": 888888, "twitchUsername": "fress", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "hhhh", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": true, "responsibleName": "CARLOS", "agencyName": "NONSTOP", "responsibleCapturer": "HELLEN", "mainNetwork": "instagram", "mainPlatform": "instagram", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.970Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "0EALZGL8faMUKt5dkiXW", "services": {"instagram": null, "tiktok": null, "youtube": null, "facebook": {"post": {"price": 333.33, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-18T20:09:11.881Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-17T05:00:13.675Z", "updatedAt": "2025-07-18T20:09:10.583Z", "__typename": "Influencer"}, {"id": "lxrMDbxTQGCpYvRrkCPZ", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+5588997028711", "whatsapp": "+5588997028711", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 24, "gender": "male", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752352966267_1752352965497.jpg", "backgroundImage": null, "gradient": null, "category": null, "categories": [], "totalFollowers": 2364554, "totalViews": 5334444, "engagementRate": 34.5, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "aquiehparmera", "instagramFollowers": 135444, "instagramEngagementRate": 34, "instagramAvgViews": null, "instagramStoriesViews": 56666, "instagramReelsViews": 87777, "tiktokUsername": "@rafaelchef", "tiktokFollowers": 657000, "tiktokEngagementRate": 24, "tiktokAvgViews": null, "tiktokVideoViews": 544444, "youtubeUsername": "fgggg", "youtubeFollowers": 34555, "youtubeSubscribers": 34555, "youtubeEngagementRate": 23, "youtubeAvgViews": null, "youtubeShortsViews": 454676, "youtubeLongFormViews": 654566, "facebookUsername": "aquiehparmera", "facebookFollowers": 132000, "facebookEngagementRate": 25, "facebookAvgViews": null, "facebookViews": 777, "facebookReelsViews": 111111, "facebookStoriesViews": 1111111, "twitchUsername": "Público", "twitchFollowers": 655555, "twitchEngagementRate": 56, "twitchViews": 777777, "kwaiUsername": "hhhh", "kwaiFollowers": 750000, "kwaiEngagementRate": 45, "kwaiViews": 777777, "promotesTraders": true, "responsibleName": "aaa", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "Luana555", "mainNetwork": "twitch", "mainPlatform": "twitch", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.970Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "3XZaUFAnP18mjDZ2HSfl", "services": {"instagram": {"story": {"price": 12.34, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 543.21, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 65.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 4565.55, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 6.55, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 45.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": {"post": {"price": 7777.77, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "FacebookPricing"}, "twitch": {"stream": {"price": 7777.77, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TwitchPricing"}, "kwai": {"video": {"price": 777.77, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "KwaiPricing"}, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-18T21:04:46.293Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-12T20:42:46.499Z", "updatedAt": "2025-07-18T21:04:44.541Z", "__typename": "Influencer"}, {"id": "7PnAxZLXq0ih5IcLz3nF", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "Gigante Glorioso", "email": "<EMAIL>", "phone": "+55888888888", "whatsapp": "+55888888888", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 42, "gender": "male", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752171603008_1752171604310.png", "backgroundImage": null, "gradient": null, "category": "Lifestyle", "categories": [], "totalFollowers": 2068888, "totalViews": 3443334, "engagementRate": 48.67, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "marina<PERSON><PERSON>va_", "instagramFollowers": 34333, "instagramEngagementRate": 45, "instagramAvgViews": 3, "instagramStoriesViews": 3454765, "instagramReelsViews": 765432, "tiktokUsername": "@marinasilva", "tiktokFollowers": 2000000, "tiktokEngagementRate": 45, "tiktokAvgViews": 0, "tiktokVideoViews": 567777, "youtubeUsername": "fgggg", "youtubeFollowers": 34555, "youtubeSubscribers": 34555, "youtubeEngagementRate": 56, "youtubeAvgViews": 5, "youtubeShortsViews": 3434, "youtubeLongFormViews": 3434, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": 0, "facebookViews": null, "facebookReelsViews": null, "facebookStoriesViews": null, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiViews": 0, "promotesTraders": false, "responsibleName": "<PERSON><PERSON><PERSON>", "agencyName": "<PERSON><PERSON><PERSON>", "responsibleCapturer": "", "mainNetwork": "youtube", "mainPlatform": "youtube", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-21T19:37:07.970Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "5rwFMtvRI7JM61Avuu63", "services": {"instagram": {"story": {"price": 54.44, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 755.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 4555.59, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 678.77, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 87.66, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 65.43, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "facebook": null, "twitch": null, "kwai": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-18T19:02:38.380Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "OAR22JyfxIHlFC3NuXD4", "platform": "youtube", "audienceGender": {"male": 88.5, "female": 11.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 13.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "13-17", "percentage": 0.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 5.6, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 14.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 22.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 15.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 20.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 20.8, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "Tq3mwH0unS8vKHUbRjFz", "platform": "tiktok", "audienceGender": {"male": 85.5, "female": 14.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "hH8ZUAcXr2QoDCIA1j9V", "platform": "instagram", "audienceGender": {"male": 85.5, "female": 14.5, "other": 2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 17.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo", "percentage": 2, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.6, "__typename": "AudienceCity"}, {"city": "<PERSON>", "percentage": 1.4, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1.3, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-10T18:20:07.798Z", "updatedAt": "2025-07-18T19:02:36.627Z", "__typename": "Influencer"}], "__typename": "InfluencerConnection"}}}