import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { doc, getDoc } from 'firebase/firestore';
import { clientDb as db } from '@/lib/firebase-client';

const JWT_SECRET = process.env.JWT_SECRET || '';
const COOKIE_NAME = 'brand-session';

interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'viewer';
  permissions: string[];
  isActive: boolean;
}

// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// Função para verificar autenticação via JWT
async function verifyAuth(request: NextRequest): Promise<{ user: User } | null> {
  console.warn('verifyAuth (Firebase) está desabilitado - use Clerk Auth');
  return null;
  
  /*
  try {
    console.log('🔐 Verificando autenticação...');
    const token = request.cookies.get(COOKIE_NAME)?.value;

    if (!token) {
      console.log('❌ Token não encontrado nos cookies');
      return null;
    }

    console.log('🔑 Token encontrado, verificando...');
    // Verificar e decodificar o token
    let decoded: TokenPayload;
    try {
      decoded = jwt.verify(token, JWT_SECRET) as TokenPayload;
      console.log('✅ Token válido para usuário:', decoded.userId);
    } catch (error) {
      console.log('❌ Token inválido:', error);
      return null;
    }

    // Verificar se o token não expirou
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp < now) {
      console.log('❌ Token expirado');
      return null;
    }

    // Buscar dados do usuário no Firestore
    console.log('👤 Buscando dados do usuário no Firestore...');
    const userDoc = await getDoc(doc(db, 'users', decoded.userId));
    
    if (!userDoc.exists()) {
      console.log('❌ Usuário não encontrado no Firestore');
      return null;
    }

    const userData = userDoc.data() as User;
    console.log('📋 Dados do usuário encontrados:', { id: decoded.userId, email: userData.email, role: userData.role });
    
    if (!userData.isActive) {
      console.log('❌ Usuário inativo');
      return null;
    }

    console.log('✅ Autenticação bem-sucedida');
    return {
      user: {
        ...userData,
        id: decoded.userId
      }
    };
  } catch (error) {
    console.error('❌ Erro na verificação de autenticação:', error);
    return null;
  }
  */
}

// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// Middleware para APIs que requerem autenticação
export async function withAuthentication(
  handler: (req: NextRequest, userId: string) => Promise<Response>
) {
  return async (req: NextRequest) => {
    console.warn('withAuthentication (Firebase) está desabilitado - use Clerk Auth ou withUserIsolation');
    return NextResponse.json(
      { error: 'Middleware Firebase desabilitado - use Clerk Auth' },
      { status: 501 }
    );
  };
}

// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// Middleware para verificar permissões específicas
export function withPermission(
  permission: 'read' | 'write' | 'admin',
  handler: (req: NextRequest, userId: string) => Promise<Response>
) {
  return async (req: NextRequest) => {
    console.warn(`withPermission (Firebase) está desabilitado - use Clerk Auth ou withUserIsolation`);
    return NextResponse.json(
      { error: 'Middleware Firebase desabilitado - use Clerk Auth' },
      { status: 501 }
    );
  };
}

// Utilitário para filtrar dados por usuário (se necessário)
export function filterByUser<T extends { userId?: string }>(data: T[], userId: string): T[] {
  return data.filter(item => item.userId === userId);
}

// Utilitário para validar se um item pertence ao usuário atual
export function validateUserOwnership(itemUserId: string, currentUserId: string): boolean {
  return itemUserId === currentUserId;
}

// Headers de resposta padrão para APIs
export function getSecurityHeaders() {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };
}

// Middleware para adicionar headers de segurança
export function withSecurityHeaders(
  handler: (req: NextRequest) => Promise<Response>
) {
  return async (req: NextRequest) => {
    const response = await handler(req);
    const headers = getSecurityHeaders();
    
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  };
}

// Middleware combinado para máxima segurança
export function withSecurity(
  permission: 'read' | 'write' | 'admin' = 'read',
  handler: (req: NextRequest, userId: string) => Promise<Response>
) {
  return withSecurityHeaders(
    withPermission(permission, handler)
  );
}

// Alias para compatibilidade com código existente
export const withBrandSecurity = withSecurity;

