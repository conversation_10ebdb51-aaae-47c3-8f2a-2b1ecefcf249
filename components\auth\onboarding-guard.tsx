'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth-v2';
import { useOrganizationList } from '@clerk/nextjs';
import { OnboardingModal } from './onboarding-modal';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

export function OnboardingGuard({ children }: OnboardingGuardProps) {
  const { currentUser: user, isLoading: loading } = useAuth();
  const { 
    isLoaded: isOrganizationListLoaded, 
    userMemberships 
  } = useOrganizationList({
    userMemberships: {
      infinite: true,
    },
  });
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);

  useEffect(() => {
    if (loading || !isOrganizationListLoaded) return;
    
    if (!user) {
      // Se não está logado, redirecionar para login
      router.push('/');
      return;
    }

    checkOnboardingStatus();
  }, [user, loading, isOrganizationListLoaded, router]);

  const checkOnboardingStatus = async () => {
    try {
      console.log('🔍 Verificando status de onboarding para:', user?.id);
      
      // Verificar se onboarding já foi concluído (localStorage)
      const onboardingCompleted = localStorage.getItem(`onboarding_completed_${user?.id}`);
      
      if (onboardingCompleted === 'true') {
        console.log('✅ Onboarding já concluído (localStorage)');
        setIsChecking(false);
        return;
      }

      // Verificar organizações usando Clerk
      const userOrganizations = userMemberships?.data || [];
      
      // Se tem organizações próprias (não "Sistema Global"), já fez onboarding
      const hasOwnOrganization = userOrganizations.some(
        (membership: any) => membership.organization.name !== 'Sistema Global' && membership.role === 'org:admin'
      );
      
      if (hasOwnOrganization) {
        console.log('✅ Usuário tem organização própria - onboarding concluído');
        localStorage.setItem(`onboarding_completed_${user?.id}`, 'true');
        setIsChecking(false);
        return;
      }

      // Verificar se precisa de onboarding baseado nos roles
      const globalMembership = userOrganizations.find(
        (membership: any) => membership.organization.name === 'Sistema Global'
      );

      if (globalMembership) {
        // Se só tem "Sistema Global" como member E não tem outras organizações próprias, precisa onboarding
        const onlyGlobalMember = globalMembership.role === 'org:member' && 
                               userOrganizations.length === 1;
        
        if (onlyGlobalMember) {
          console.log('🆕 Usuário tem apenas membership global - mostrando modal de onboarding');
          setShowOnboardingModal(true);
          setIsChecking(false);
          return;
        }
      }

      // Se não tem organizações, precisa de onboarding
      if (userOrganizations.length === 0) {
        console.log('🆕 Usuário sem organizações - mostrando modal de onboarding');
        setShowOnboardingModal(true);
        setIsChecking(false);
        return;
      }

      // Se chegou aqui, assumir que onboarding foi feito
      console.log('✅ Onboarding assumido como concluído');
      localStorage.setItem(`onboarding_completed_${user?.id}`, 'true');
      setIsChecking(false);
      
    } catch (error) {
      console.error('❌ Erro ao verificar onboarding:', error);
      setIsChecking(false);
    }
  };

  const handleOnboardingComplete = () => {
    setShowOnboardingModal(false);
  };

  if (loading || !isOrganizationListLoaded || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-2 border-[#ff0074]/30 border-t-[#ff0074] rounded-full animate-spin mx-auto" />
          <div>
            <h2 className="text-lg font-semibold">Carregando...</h2>
            <p className="text-muted-foreground">Verificando configuração da conta</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {children}
      <OnboardingModal 
        isOpen={showOnboardingModal}
        onClose={handleOnboardingComplete}
      />
    </>
  );
} 

