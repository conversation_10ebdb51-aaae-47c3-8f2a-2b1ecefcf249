import { auth, currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { headers } from 'next/headers';

/**
 * 🎯 Rota de Redirecionamento Inteligente
 * 
 * Esta rota é configurada no Clerk para redirecionamento após login.
 * Elimina a necessidade de passar pelo /dashboard, redirecionando
 * diretamente para /{locale}/{userId}/influencers após autenticação.
 */

// Função para detectar idioma dos headers
async function getLocaleFromHeaders(): Promise<string> {
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  const locale = headersList.get('x-locale'); // Nosso middleware define isso
  
  // Se o middleware já definiu o locale, usar ele
  if (locale && ['pt', 'en', 'es'].includes(locale)) {
    return locale;
  }
  
  // Caso contrá<PERSON>, detectar do accept-language
  const supportedLocales = ['pt', 'en', 'es'];
  for (const loc of supportedLocales) {
    if (acceptLanguage.toLowerCase().includes(loc)) {
      return loc;
    }
  }
  
  return 'pt'; // Padrão
}

export default async function AuthRedirectPage() {
  // ⚡ OTIMIZAÇÃO: Processamento paralelo de dados
  const [authResult, currentLocale, headersList] = await Promise.all([
    auth(),
    getLocaleFromHeaders(),
    headers()
  ]);
  
  const { userId } = authResult;
  
  // ⚡ OTIMIZAÇÃO: Early return para casos de erro
  if (!userId) {
    redirect(`/${currentLocale}/sign-in`);
  }

  // ⚡ OTIMIZAÇÃO: Buscar dados do usuário de forma otimizada
  const user = await currentUser();
  
  if (!user) {
    redirect(`/${currentLocale}/sign-in`);
  }

  // ⚡ OTIMIZAÇÃO: Verificação simplificada de onboarding
  const onboardingCompleted = user.privateMetadata?.onboardingCompleted;
  
  if (onboardingCompleted === false) {
    redirect(`/${currentLocale}/onboarding`);
  }

  // 🚀 OTIMIZAÇÃO: Redirect direto com prefetch de dados críticos
  const targetUrl = `/${currentLocale}/${user.id}/influencers`;
  
  // 🔥 NOVO: Headers de otimização para o próximo carregamento
  const response = new Response(null, {
    status: 302,
    headers: {
      'Location': targetUrl,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Link': '</api/proposals>; rel=prefetch, </api/brands>; rel=prefetch',
      'X-Preload-Data': 'true'
    }
  });
  
  redirect(targetUrl);
} 