// Validador de Segurança para Upload de Arquivos
// Previne: File bombs, executáveis maliciosos, XSS via SVG, etc.

export interface FileValidationOptions {
  maxSize: number;                    // Tamanho máximo em bytes
  allowedMimeTypes: string[];         // Tipos MIME permitidos
  allowedExtensions: string[];        // Extensões permitidas
  requireMimeMatch: boolean;          // MIME deve bater com extensão
  scanContent: boolean;               // Verificar conteúdo interno
  preventExecutables: boolean;        // Bloquear arquivos executáveis
}

export interface FileSecurityResult {
  isValid: boolean;
  errors: string[];
  sanitizedFileName: string;
  detectedMimeType: string;
  fileSize: number;
}

export class FileSecurityValidator {

  // 🔒 TIPOS MIME SEGUROS PRÉ-DEFINIDOS
  static readonly SAFE_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp'
  ];

  static readonly SAFE_DOCUMENT_TYPES = [
    'application/pdf',
    'text/plain'
  ];

  // 🚫 EXTENSÕES PERIGOSAS BLOQUEADAS
  static readonly DANGEROUS_EXTENSIONS = [
    'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar',
    'app', 'deb', 'pkg', 'dmg', 'rpm', 'run', 'msi', 'cab',
    'php', 'asp', 'aspx', 'jsp', 'py', 'rb', 'pl', 'sh', 'bash'
  ];

  // 🔍 MAGIC NUMBERS PARA DETECÇÃO DE TIPO REAL
  static readonly MAGIC_NUMBERS = new Map([
    ['89504E47', 'image/png'],          // PNG
    ['FFD8FFE0', 'image/jpeg'],         // JPEG
    ['FFD8FFE1', 'image/jpeg'],         // JPEG
    ['47494638', 'image/gif'],          // GIF
    ['52494646', 'image/webp'],         // WEBP (RIFF header)
    ['25504446', 'application/pdf'],    // PDF
    ['504B0304', 'application/zip'],    // ZIP
    ['4D5A9000', 'application/exe']     // EXE
  ]);

  /**
   * 🔒 VALIDAÇÃO COMPLETA DE SEGURANÇA DE ARQUIVO
   */
  static async validateFile(
    file: File | { name: string; type: string; size: number; arrayBuffer: () => Promise<ArrayBuffer> },
    options: FileValidationOptions
  ): Promise<FileSecurityResult> {
    
    const errors: string[] = [];
    
    // 1️⃣ VALIDAR TAMANHO DO ARQUIVO
    if (file.size > options.maxSize) {
      errors.push(`Arquivo muito grande. Máximo: ${this.formatBytes(options.maxSize)}, recebido: ${this.formatBytes(file.size)}`);
    }

    if (file.size === 0) {
      errors.push('Arquivo está vazio');
    }

    // 2️⃣ SANITIZAR NOME DO ARQUIVO
    const sanitizedFileName = this.sanitizeFileName(file.name);
    
    // 3️⃣ VALIDAR EXTENSÃO
    const extension = this.getFileExtension(sanitizedFileName).toLowerCase();
    
    if (this.DANGEROUS_EXTENSIONS.includes(extension)) {
      errors.push(`Extensão perigosa detectada: .${extension}`);
    }

    if (options.allowedExtensions.length > 0 && !options.allowedExtensions.includes(extension)) {
      errors.push(`Extensão não permitida: .${extension}. Permitidas: ${options.allowedExtensions.join(', ')}`);
    }

    // 4️⃣ VALIDAR TIPO MIME DECLARADO
    if (options.allowedMimeTypes.length > 0 && !options.allowedMimeTypes.includes(file.type)) {
      errors.push(`Tipo MIME não permitido: ${file.type}. Permitidos: ${options.allowedMimeTypes.join(', ')}`);
    }

    // 5️⃣ DETECTAR TIPO MIME REAL (MAGIC NUMBERS)
    let detectedMimeType = file.type;
    
    if (options.scanContent) {
      try {
        const buffer = await file.arrayBuffer();
        const realMimeType = this.detectRealMimeType(buffer);
        
        if (realMimeType) {
          detectedMimeType = realMimeType;
          
          // 🚨 VERIFICAR SE MIME REAL BATE COM DECLARADO
          if (options.requireMimeMatch && realMimeType !== file.type) {
            errors.push(`Tipo real (${realMimeType}) difere do declarado (${file.type}). Possível tentativa de bypass.`);
          }

          // 🚨 VERIFICAR SE MIME REAL É PERMITIDO
          if (options.allowedMimeTypes.length > 0 && !options.allowedMimeTypes.includes(realMimeType)) {
            errors.push(`Tipo real não permitido: ${realMimeType}`);
          }
        }

        // 6️⃣ VERIFICAR CONTEÚDO MALICIOSO
        if (options.preventExecutables) {
          const maliciousContent = this.scanForMaliciousContent(buffer, extension);
          if (maliciousContent.length > 0) {
            errors.push(...maliciousContent);
          }
        }

      } catch (error) {
        errors.push('Erro ao analisar conteúdo do arquivo');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedFileName,
      detectedMimeType,
      fileSize: file.size
    };
  }

  /**
   * 🧹 SANITIZAR NOME DO ARQUIVO
   */
  private static sanitizeFileName(fileName: string): string {
    return fileName
      // Remover caracteres perigosos
      .replace(/[<>:"/\\|?*\x00-\x1f]/g, '')
      // Remover espaços múltiplos
      .replace(/\s+/g, '_')
      // Remover pontos no início/fim
      .replace(/^\.+|\.+$/g, '')
      // Limitar tamanho
      .substring(0, 255)
      // Garantir que não está vazio
      || 'arquivo_sem_nome';
  }

  /**
   * 🔍 DETECTAR TIPO MIME REAL VIA MAGIC NUMBERS
   */
  private static detectRealMimeType(buffer: ArrayBuffer): string | null {
    const bytes = new Uint8Array(buffer);
    const header = Array.from(bytes.slice(0, 4))
      .map(b => b.toString(16).padStart(2, '0').toUpperCase())
      .join('');

    return this.MAGIC_NUMBERS.get(header) || null;
  }

  /**
   * 🚨 ESCANEAR CONTEÚDO MALICIOSO
   */
  private static scanForMaliciousContent(buffer: ArrayBuffer, extension: string): string[] {
    const errors: string[] = [];
    const content = new TextDecoder('utf-8', { fatal: false }).decode(buffer.slice(0, 1024));

    // Detectar scripts em SVGs
    if (extension === 'svg') {
      if (content.includes('<script') || content.includes('javascript:') || content.includes('onload=')) {
        errors.push('SVG contém scripts maliciosos');
      }
    }

    // Detectar headers de executáveis
    const executableSignatures = ['MZ', 'PK', '7z', 'Rar!'];
    for (const sig of executableSignatures) {
      if (content.startsWith(sig)) {
        errors.push(`Assinatura de executável detectada: ${sig}`);
      }
    }

    // Detectar PHP/JavaScript embarcado
    const maliciousPatterns = [
      '<?php', '<%', '<script', 'eval(', 'exec(', 'system(', 'shell_exec('
    ];
    
    for (const pattern of maliciousPatterns) {
      if (content.toLowerCase().includes(pattern.toLowerCase())) {
        errors.push(`Código malicioso detectado: ${pattern}`);
      }
    }

    return errors;
  }

  /**
   * 📁 EXTRAIR EXTENSÃO DO ARQUIVO
   */
  private static getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot + 1) : '';
  }

  /**
   * 📊 FORMATAR BYTES PARA LEITURA HUMANA
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * ⚙️ CONFIGURAÇÕES PRÉ-DEFINIDAS PARA DIFERENTES TIPOS
   */
  static readonly PRESETS = {
    AVATAR_IMAGES: {
      maxSize: 5 * 1024 * 1024, // 5MB
      allowedMimeTypes: this.SAFE_IMAGE_TYPES,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'webp'],
      requireMimeMatch: true,
      scanContent: true,
      preventExecutables: true
    } as FileValidationOptions,

    BRAND_LOGOS: {
      maxSize: 2 * 1024 * 1024, // 2MB
      allowedMimeTypes: [...this.SAFE_IMAGE_TYPES, 'image/svg+xml'],
      allowedExtensions: ['jpg', 'jpeg', 'png', 'webp', 'svg'],
      requireMimeMatch: true,
      scanContent: true,
      preventExecutables: true
    } as FileValidationOptions,

    DOCUMENTS: {
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: this.SAFE_DOCUMENT_TYPES,
      allowedExtensions: ['pdf', 'txt'],
      requireMimeMatch: true,
      scanContent: true,
      preventExecutables: true
    } as FileValidationOptions
  };
} 

