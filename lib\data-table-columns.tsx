import React from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Users, 
  Tag, 
  Calendar, 
  List,
  HelpCircle,
  ExternalLink,
  Share,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Tipos base para diferentes entidades
export interface BaseInfluencer {
  id: string;
  nome: string;
  pseudonimo?: string;
  avatar?: string;
  seguidores: number;
  engajamento: number;
  nicho?: string;
  status?: string;
  origem?: string;
}

export interface BaseBrand {
  id: string;
  nome: string;
  logo?: string;
  setor: string;
  orcamento?: number;
  status: string;
  funcionarios?: number;
  dataCriacao: Date;
}

export interface BaseCampaign {
  id: string;
  nome: string;
  status: string;
  budget?: number;
  dataInicio: Date;
  dataFim?: Date;
  alcance?: number;
  marca: string;
}

export interface BaseList {
  id: string;
  nome: string;
  tipoLista: 'estática' | 'dinâmica';
  tipoObjeto: 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
  tamanho: number;
  criadoPor: string;
  criadoPorNome: string;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  descricao?: string;
}

// Utilitários para formatação
export const formatters = {
  number: (value: number) => value.toLocaleString('pt-BR'),
  currency: (value: number) => new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value),
  date: (date: Date) => new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date),
  percentage: (value: number) => `${value.toFixed(1)}%`,
};

// Utilitários para badges de status
export const statusBadges = {
  influencer: (status: string) => {
    const variants = {
      ativo: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      inativo: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      pendente: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    };
    return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  },
  
  campaign: (status: string) => {
    const variants = {
      'ativa': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'pausada': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'finalizada': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'cancelada': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    };
    return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  },

  list: (tipo: string) => {
    const variants = {
      'estática': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'dinâmica': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    };
    return variants[tipo as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  }
};

// Utilitários para ícones
export const typeIcons = {
  influenciadores: <Users className="h-4 w-4" />,
  marcas: <Tag className="h-4 w-4" />,
  campanhas: <Calendar className="h-4 w-4" />,
  conteúdo: <List className="h-4 w-4" />,
};

// Colunas padrão para Influenciadores
export function createInfluencerColumns(
  onEdit?: (influencer: BaseInfluencer) => void,
  onDelete?: (influencer: BaseInfluencer) => void,
  onView?: (influencer: BaseInfluencer) => void,
  showActions = true
): ColumnDef<BaseInfluencer>[] {
  const columns: ColumnDef<BaseInfluencer>[] = [
    {
      accessorKey: 'nome',
      id: 'nome',
      header: 'PERFIL',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={influencer.avatar} alt={influencer.nome} />
              <AvatarFallback className="bg-gradient-to-r from-[#ff0074] to-[#9810fa] text-white">
                {influencer.nome.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{influencer.nome}</div>
              {influencer.pseudonimo && (
                <div className="text-sm text-muted-foreground">@{influencer.pseudonimo}</div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'origem',
      id: 'origem',
      header: 'ORIGEM',
      cell: ({ row }) => row.original.origem || 'N/A',
    },
    {
      accessorKey: 'seguidores',
      id: 'seguidores',
      header: 'SEGUIDORES',
      cell: ({ row }) => (
        <span className="font-medium">
          {formatters.number(row.original.seguidores)}
        </span>
      ),
    },
    {
      accessorKey: 'engajamento',
      id: 'engajamento',
      header: 'ENGAJAMENTO',
      cell: ({ row }) => (
        <span className="font-medium text-sm text-[#ff0074]">
          {formatters.percentage(row.original.engajamento)}
        </span>
      ),
    },
    {
      accessorKey: 'nicho',
      id: 'nicho',
      header: 'NICHO',
      cell: ({ row }) => row.original.nicho || 'N/A',
    },
    {
      accessorKey: 'status',
      id: 'status',
      header: 'STATUS',
      cell: ({ row }) => {
        const status = row.original.status || 'ativo';
        return (
          <Badge className={statusBadges.influencer(status)}>
            {status}
          </Badge>
        );
      },
    },
  ];

  if (showActions) {
    columns.push({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(influencer)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(influencer)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onDelete(influencer)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 48,
    });
  }

  return columns;
}

// Colunas padrão para Marcas
export function createBrandColumns(
  onEdit?: (brand: BaseBrand) => void,
  onDelete?: (brand: BaseBrand) => void,
  onView?: (brand: BaseBrand) => void,
  showActions = true
): ColumnDef<BaseBrand>[] {
  const columns: ColumnDef<BaseBrand>[] = [
    {
      accessorKey: 'nome',
      id: 'nome',
      header: 'MARCA',
      cell: ({ row }) => {
        const brand = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={brand.logo} alt={brand.nome} />
              <AvatarFallback className="bg-gradient-to-r from-[#ff0074] to-[#9810fa] text-white">
                {brand.nome.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="font-medium">{brand.nome}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'setor',
      id: 'setor',
      header: 'SETOR',
      cell: ({ row }) => row.original.setor,
    },
    {
      accessorKey: 'orcamento',
      id: 'orcamento',
      header: 'ORÇAMENTO',
      cell: ({ row }) => {
        const orcamento = row.original.orcamento;
        return orcamento ? formatters.currency(orcamento) : 'N/A';
      },
    },
    {
      accessorKey: 'funcionarios',
      id: 'funcionarios',
      header: 'FUNCIONÁRIOS',
      cell: ({ row }) => {
        const funcionarios = row.original.funcionarios;
        return funcionarios ? formatters.number(funcionarios) : 'N/A';
      },
    },
    {
      accessorKey: 'status',
      id: 'status',
      header: 'STATUS',
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge className={statusBadges.influencer(status)}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'dataCriacao',
      id: 'dataCriacao',
      header: 'DATA DE CRIAÇÃO',
      cell: ({ row }) => formatters.date(row.original.dataCriacao),
    },
  ];

  if (showActions) {
    columns.push({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const brand = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(brand)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(brand)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onDelete(brand)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 48,
    });
  }

  return columns;
}

// Colunas padrão para Campanhas
export function createCampaignColumns(
  onEdit?: (campaign: BaseCampaign) => void,
  onDelete?: (campaign: BaseCampaign) => void,
  onView?: (campaign: BaseCampaign) => void,
  showActions = true
): ColumnDef<BaseCampaign>[] {
  const columns: ColumnDef<BaseCampaign>[] = [
    {
      accessorKey: 'nome',
      id: 'nome',
      header: 'CAMPANHA',
      cell: ({ row }) => (
        <div className="font-medium">{row.original.nome}</div>
      ),
    },
    {
      accessorKey: 'marca',
      id: 'marca',
      header: 'MARCA',
      cell: ({ row }) => row.original.marca,
    },
    {
      accessorKey: 'status',
      id: 'status',
      header: 'STATUS',
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge className={statusBadges.campaign(status)}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'budget',
      id: 'budget',
      header: 'BUDGET',
      cell: ({ row }) => {
        const budget = row.original.budget;
        return budget ? formatters.currency(budget) : 'N/A';
      },
    },
    {
      accessorKey: 'dataInicio',
      id: 'dataInicio',
      header: 'DATA INÍCIO',
      cell: ({ row }) => formatters.date(row.original.dataInicio),
    },
    {
      accessorKey: 'dataFim',
      id: 'dataFim',
      header: 'DATA FIM',
      cell: ({ row }) => {
        const dataFim = row.original.dataFim;
        return dataFim ? formatters.date(dataFim) : 'N/A';
      },
    },
    {
      accessorKey: 'alcance',
      id: 'alcance',
      header: 'ALCANCE',
      cell: ({ row }) => {
        const alcance = row.original.alcance;
        return alcance ? formatters.number(alcance) : 'N/A';
      },
    },
  ];

  if (showActions) {
    columns.push({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const campaign = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(campaign)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(campaign)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onDelete(campaign)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 48,
    });
  }

  return columns;
}

// Colunas padrão para Listas
export function createListColumns(
  onEdit?: (list: BaseList) => void,
  onDelete?: (list: BaseList) => void,
  onView?: (list: BaseList) => void,
  onShare?: (list: BaseList) => void,
  onExport?: (list: BaseList) => void,
  showActions = true,
  enableClickableNames = false,
  onNameClick?: (list: BaseList) => void
): ColumnDef<BaseList>[] {
  const columns: ColumnDef<BaseList>[] = [
    {
      accessorKey: 'nome',
      id: 'nome',
      header: 'NOME DA LISTA',
      cell: ({ row }) => {
        const list = row.original;
        const content = (
          <div className="flex flex-col">
            <div className={cn(
              "font-medium",
              enableClickableNames && "hover:text-[#ff0074] cursor-pointer"
            )}>
              {list.nome}
            </div>
            {list.descricao && (
              <div className="text-sm text-muted-foreground mt-1">
                {list.descricao}
              </div>
            )}
          </div>
        );

        if (enableClickableNames && onNameClick) {
          return (
            <button 
              onClick={() => onNameClick(list)}
              className="text-left w-full"
            >
              {content}
            </button>
          );
        }

        return content;
      },
    },
    {
      accessorKey: 'tipoLista',
      id: 'tipoLista',
      header: () => (
        <div className="flex items-center gap-1">
          TIPO DE LISTA
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <div className="max-w-xs">
                  <p className="font-medium mb-1">Como a lista funciona:</p>
                  <ul className="text-xs space-y-1">
                    <li>• <strong>Estática:</strong> Você adiciona/remove itens manualmente</li>
                    <li>• <strong>Dinâmica:</strong> Lista atualiza automaticamente por critérios</li>
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      cell: ({ row }) => {
        const list = row.original;
        return (
          <Badge className={statusBadges.list(list.tipoLista)}>
            {list.tipoLista}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'tipoObjeto',
      id: 'tipoObjeto',
      header: () => (
        <div className="flex items-center gap-1">
          TIPO DE OBJETO
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <div className="max-w-xs">
                  <p className="font-medium mb-1">O que vai ficar dentro da lista:</p>
                  <ul className="text-xs space-y-1">
                    <li>• <strong>Influenciadores:</strong> Perfis de creators</li>
                    <li>• <strong>Marcas:</strong> Empresas parceiras</li>
                    <li>• <strong>Campanhas:</strong> Projetos/trabalhos</li>
                    <li>• <strong>Conteúdo:</strong> Fotos, vídeos, posts</li>
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      cell: ({ row }) => {
        const list = row.original;
        return (
          <div className="flex items-center gap-2">
            {typeIcons[list.tipoObjeto as keyof typeof typeIcons]}
            <span className="capitalize">{list.tipoObjeto}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'tamanho',
      id: 'tamanho',
      header: 'TAMANHO',
      cell: ({ row }) => (
        <span className="font-medium">
          {formatters.number(row.original.tamanho)}
        </span>
      ),
    },
    {
      accessorKey: 'criadoPorNome',
      id: 'criadoPor',
      header: 'CRIADO POR',
      cell: ({ row }) => row.original.criadoPorNome,
    },
    {
      accessorKey: 'dataCriacao',
      id: 'dataCriacao',
      header: 'DATA DE CRIAÇÃO',
      cell: ({ row }) => formatters.date(row.original.dataCriacao),
    },
    {
      accessorKey: 'ultimaAtualizacao',
      id: 'ultimaAtualizacao',
      header: 'ÚLTIMA ATUALIZAÇÃO',
      cell: ({ row }) => formatters.date(row.original.ultimaAtualizacao),
    },
  ];

  if (showActions) {
    columns.push({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const list = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(list)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(list)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
              )}
              {onShare && (
                <DropdownMenuItem onClick={() => onShare(list)}>
                  <Share className="h-4 w-4 mr-2" />
                  Compartilhar
                </DropdownMenuItem>
              )}
              {onExport && (
                <DropdownMenuItem onClick={() => onExport(list)}>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onDelete(list)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 48,
    });
  }

  return columns;
}

// Hook para gerenciar estado comum do DataTable
export function useDataTableState() {
  const [globalFilter, setGlobalFilter] = React.useState('');
  const [columnFilters, setColumnFilters] = React.useState<any[]>([]);
  const [sorting, setSorting] = React.useState<any[]>([]);
  const [rowSelection, setRowSelection] = React.useState({});

  const resetFilters = () => {
    setGlobalFilter('');
    setColumnFilters([]);
    setSorting([]);
    setRowSelection({});
  };

  return {
    globalFilter,
    setGlobalFilter,
    columnFilters,
    setColumnFilters,
    sorting,
    setSorting,
    rowSelection,
    setRowSelection,
    resetFilters,
  };
}

// Utilitários para ações comuns
export const commonActions = {
  confirmDelete: (itemName: string, onConfirm: () => void) => {
    if (confirm(`Tem certeza que deseja deletar "${itemName}"? Esta ação não pode ser desfeita.`)) {
      onConfirm();
    }
  },
  
  handleBulkDelete: (selectedItems: any[], itemName: string, onConfirm: (items: any[]) => void) => {
    if (confirm(`Deletar ${selectedItems.length} ${itemName}(s) selecionado(s)?`)) {
      onConfirm(selectedItems);
    }
  },
}; 



