import { NextRequest, NextResponse } from 'next/server';
import { statsCache } from '@/lib/stats-cache-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, action } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'recalculate':
        // Força recálculo mantendo dados denormalizados
        result = await statsCache.forceRecalculate(userId);
        break;
        
      case 'reset':
        // Remove dados denormalizados e recalcula do zero
        result = await statsCache.resetUserStats(userId);
        break;
        
      default:
        // Padrão: invalidar cache e recalcular
        statsCache.invalidateCache(userId);
        result = await statsCache.getStats(userId);
        break;
    }

    return NextResponse.json({
      success: true,
      action: action || 'invalidate',
      userId,
      stats: result,
      message: `Estatísticas ${action === 'reset' ? 'resetadas' : 'recalculadas'} com sucesso`
    });

  } catch (error) {
    console.error('Erro ao resetar estatísticas:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
} 

