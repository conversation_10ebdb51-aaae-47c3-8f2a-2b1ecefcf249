import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/lib/firebase';

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ userId: string; brandId: string }> }
) {
  try {
    const params = await context.params;
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId || clerkUserId !== params.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar se a marca existe e pertence ao usuário
    const brandRef = db.collection('brands').doc(params.brandId);
    const brandDoc = await brandRef.get();

    if (!brandDoc.exists) {
      return NextResponse.json({ error: 'Marca não encontrada' }, { status: 404 });
    }

    const brandData = brandDoc.data();
    if (brandData?.userId !== params.userId) {
      return NextResponse.json({ error: 'Não autorizado a remover esta marca' }, { status: 403 });
    }

    // Remover a marca
    await brandRef.delete();

    return NextResponse.json({ success: true, message: 'Marca removida com sucesso' });
  } catch (error) {
    console.error('Erro ao remover marca:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 