import React, { useState, useEffect } from 'react';
import { Note } from '@/types/influencer';
import { PlusCircle, X, Edit, Save, Trash, MessageSquare, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface NotesManagerProps {
  influencerId?: string;
  readOnly?: boolean;
  showNoteForm?: boolean;
  setShowNoteForm?: (show: boolean) => void;
}

const NotesManager: React.FC<NotesManagerProps> = ({
  influencerId,
  readOnly = false,
  showNoteForm = false,
  setShowNoteForm
}) => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newNoteTitle, setNewNoteTitle] = useState('');
  const [newNoteContent, setNewNoteContent] = useState('');
  const [newNoteType, setNewNoteType] = useState('geral');
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);

  // 🔥 CORREÇÃO: Carregar anotações com cache e debounce
  useEffect(() => {
    if (!influencerId) {
      setNotes([]);
      return;
    }

    let isMounted = true;
    const timeoutId = setTimeout(async () => {
      // Cache por influenciador
      const cacheKey = `notes_${influencerId}`;
      const cacheTimestamp = `notes_timestamp_${influencerId}`;
      const cached = sessionStorage.getItem(cacheKey);
      const timestamp = sessionStorage.getItem(cacheTimestamp);
      const now = Date.now();
      
      // Cache válido por 2 minutos
      if (cached && timestamp && (now - parseInt(timestamp)) < 120000) {
        if (isMounted) {
          setNotes(JSON.parse(cached));
          setIsLoading(false);
        }
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(`/api/notes?influencerId=${influencerId}`);
        if (response.ok && isMounted) {
          const data = await response.json();
          setNotes(data);
          
          // Salvar no cache
          sessionStorage.setItem(cacheKey, JSON.stringify(data));
          sessionStorage.setItem(cacheTimestamp, now.toString());
        } else if (isMounted) {
          console.error('Erro ao carregar anotações');
          setNotes([]);
        }
      } catch (error) {
        if (isMounted) {
        console.error('Erro ao carregar anotações:', error);
        setNotes([]);
        }
      } finally {
        if (isMounted) {
        setIsLoading(false);
        }
      }
    }, 300); // 🔥 Debounce de 300ms para evitar requisições em rajada

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [influencerId]);

  // Função para adicionar nova anotação
  const handleAddNote = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!influencerId || !newNoteTitle.trim() || !newNoteContent.trim()) return;
    
    try {
      const response = await fetch('/api/notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          influencerId,
          title: newNoteTitle.trim(),
          content: newNoteContent.trim(),
          type: newNoteType
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Adicionar nova anotação à lista
        const newNote: Note = {
          id: data.id,
          title: newNoteTitle.trim(),
          content: newNoteContent.trim(),
          type: newNoteType,
          influencerId: influencerId,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        setNotes([newNote, ...notes]);
        
        // Limpar formulário
        setNewNoteTitle('');
        setNewNoteContent('');
        setNewNoteType('geral');
        setShowNoteForm?.(false);
      } else {
        console.error('Erro ao criar anotação');
      }
    } catch (error) {
      console.error('Erro ao criar anotação:', error);
    }
  };

  // Função para atualizar uma anotação
  const handleUpdateNote = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingNoteId || !newNoteTitle.trim() || !newNoteContent.trim()) return;
    
    try {
      const response = await fetch(`/api/notes?id=${editingNoteId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: newNoteTitle.trim(),
          content: newNoteContent.trim(),
          type: newNoteType
        })
      });
      
      if (response.ok) {
        // Atualizar anotação na lista
        const updatedNotes = notes.map(note => 
          note.id === editingNoteId 
            ? { 
                ...note, 
                title: newNoteTitle.trim(), 
                content: newNoteContent.trim(),
                type: newNoteType,
                updatedAt: new Date()
              }
            : note
        );
        
        setNotes(updatedNotes);
        
        // Limpar formulário
        setNewNoteTitle('');
        setNewNoteContent('');
        setNewNoteType('geral');
        setEditingNoteId(null);
        setShowNoteForm?.(false);
      } else {
        console.error('Erro ao atualizar anotação');
      }
    } catch (error) {
      console.error('Erro ao atualizar anotação:', error);
    }
  };

  // Função para excluir uma anotação
  const handleDeleteNote = async (noteId: string) => {
    if (!window.confirm('Tem certeza que deseja excluir esta anotação?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/notes?id=${noteId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        // Remover anotação da lista
        setNotes(notes.filter(note => note.id !== noteId));
      } else {
        console.error('Erro ao excluir anotação');
      }
    } catch (error) {
      console.error('Erro ao excluir anotação:', error);
    }
  };

  // Função para iniciar edição de anotação
  const startEditingNote = (note: Note) => {
    setEditingNoteId(note.id);
    setNewNoteTitle(note.title);
    setNewNoteContent(note.content);
    setNewNoteType(note.type || 'geral');
    setShowNoteForm?.(true);
  };

  // Função para formatar data
  const formatDate = (date: Date | string | number | null | undefined) => {
    if (!date) return 'Data desconhecida';
    
    try {
      return format(new Date(date), "dd 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return 'Data inválida';
    }
  };

  // Função para obter cor baseada no tipo de anotação
  const getNoteTypeColor = (type: string = 'geral') => {
    const typeColors: Record<string, string> = {
      geral: '#3B82F6', // azul
      reuniao: '#10B981', // verde
      negociacao: '#F59E0B', // amarelo
      contrato: '#6366F1', // roxo
      feedback: '#EC4899', // rosa
      alerta: '#EF4444', // vermelho
    };
    
    return typeColors[type] || typeColors.geral;
  };

  return (
    <div className="space-y-6">
      {!readOnly && (
        <div>
          {showNoteForm ? (
            <form onSubmit={editingNoteId ? handleUpdateNote : handleAddNote} className="space-y-4 bg-gray-50 p-4 rounded-lg">
              <div>
                <label htmlFor="noteTitle" className="block text-sm font-medium text-gray-700 mb-1">
                  Título
                </label>
                <input
                  id="noteTitle"
                  type="text"
                  value={newNoteTitle}
                  onChange={(e) => setNewNoteTitle(e.target.value)}
                  placeholder="Título da anotação"
                  className="w-full px-3 py-2 border rounded-md text-sm"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="noteType" className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  id="noteType"
                  value={newNoteType}
                  onChange={(e) => setNewNoteType(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md text-sm"
                >
                  <option value="geral">Geral</option>
                  <option value="reuniao">Reunião</option>
                  <option value="negociacao">Negociação</option>
                  <option value="contrato">Contrato</option>
                  <option value="feedback">Feedback</option>
                  <option value="alerta">Alerta</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="noteContent" className="block text-sm font-medium text-gray-700 mb-1">
                  Conteúdo
                </label>
                <textarea
                  id="noteContent"
                  value={newNoteContent}
                  onChange={(e) => setNewNoteContent(e.target.value)}
                  placeholder="Conteúdo da anotação"
                  className="w-full px-3 py-2 border rounded-md text-sm min-h-32"
                  required
                />
              </div>
              
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="flex items-center gap-1 px-4 py-2 bg-blue-500 text-white rounded-md text-sm"
                >
                  {editingNoteId ? <Save size={16} /> : <PlusCircle size={16} />}
                  {editingNoteId ? 'Salvar alterações' : 'Adicionar anotação'}
                </button>
                <button
                  type="button"
                  className="flex items-center gap-1 px-4 py-2 bg-gray-200 rounded-md text-sm"
                  onClick={() => {
                    setShowNoteForm?.(false);
                    setEditingNoteId(null);
                    setNewNoteTitle('');
                    setNewNoteContent('');
                    setNewNoteType('geral');
                  }}
                >
                  <X size={16} />
                  Cancelar
                </button>
              </div>
            </form>
          ) : (
            <button
              type="button"
              className="flex items-center gap-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors w-full justify-center dark:bg-transparent border border-gray/5"
              onClick={() => setShowNoteForm?.(true)}
            >
              <PlusCircle size={16} />
              Adicionar nova anotação
            </button>
          )}
        </div>
      )}

      <div className="space-y-4">
        <h3 className="text-lg font-medium">
          Anotações {notes.length > 0 && `(${notes.length})`}
        </h3>
        
        {isLoading ? (
          <div className="text-sm text-gray-500 p-4 bg-gray-50 rounded-lg text-center dark:text-muted-foreground bg-transparent border border-dashed">
            Carregando anotações...
          </div>
        ) : notes.length === 0 ? (
          <div className="text-sm text-gray-500 p-4 bg-gray-50 rounded-lg text-center dark:text-muted-foreground bg-transparent border border-dashed">
            Nenhuma anotação disponível
          </div>
        ) : (
          <div className="space-y-4">
            {notes.map(note => {
              const typeColor = getNoteTypeColor(note.type);
              
              return (
                <div 
                  key={note.id}
                  className="border rounded-lg overflow-hidden shadow-sm"
                >
                  <div 
                    className="flex justify-between items-center p-3 border-b"
                    style={{ borderLeftWidth: '4px', borderLeftColor: typeColor }}
                  >
                    <div className="flex items-center gap-2">
                      <MessageSquare size={16} style={{ color: typeColor }} />
                      <h4 className="font-medium">{note.title}</h4>
                      <span 
                        className="text-xs px-2 py-0.5 rounded-full"
                        style={{ backgroundColor: `${typeColor}20`, color: typeColor }}
                      >
                        {note.type || 'geral'}
                      </span>
                    </div>
                    
                    {!readOnly && (
                      <div className="flex gap-1">
                        <button
                          type="button"
                          className="p-1 rounded-full hover:bg-gray-100"
                          onClick={() => startEditingNote(note)}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          type="button"
                          className="p-1 rounded-full hover:bg-gray-100"
                          onClick={() => handleDeleteNote(note.id)}
                        >
                          <Trash size={16} />
                        </button>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-4 whitespace-pre-wrap text-sm">
                    {note.content}
                  </div>
                  
                  <div className="bg-gray-50 p-2 text-xs text-gray-500 flex items-center gap-1">
                    <Calendar size={12} />
                    {formatDate(note.createdAt)}
                    {note.updatedAt && note.createdAt && 
                     typeof note.updatedAt.getTime === 'function' && 
                     typeof note.createdAt.getTime === 'function' && 
                     note.updatedAt.getTime() !== note.createdAt.getTime() && (
                      <span className="ml-2">
                        (Editado em {formatDate(note.updatedAt)})
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotesManager;


