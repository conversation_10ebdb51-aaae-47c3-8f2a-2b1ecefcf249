import { useState, useEffect, useCallback } from 'react'

interface Screenshot {
  id: string
  influencerId: string
  url: string
}

export function useScreenshots(influencerId: string | undefined, platform?: string) {
  const [screenshots, setScreenshots] = useState<Screenshot[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchScreenshots = useCallback(async () => {
    if (!influencerId) {
      setScreenshots([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query GetInfluencerScreenshots($influencerId: ID!, $platform: String) {
              influencerScreenshots(influencerId: $influencerId, platform: $platform) {
                id
                influencerId
                url
                filename
                platform
              }
            }
          `,
          variables: {
            influencerId,
            platform
          }
        })
      })

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`)
      }

      const result = await response.json()

      if (result.errors) {
        throw new Error(result.errors[0]?.message || 'Erro GraphQL desconhecido')
      }

      const screenshotsData = result.data?.influencerScreenshots || []
      setScreenshots(screenshotsData)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(errorMessage)
      setScreenshots([])
    } finally {
      setLoading(false)
    }
  }, [influencerId, platform])

  // Buscar screenshots quando influencerId ou platform mudarem
  useEffect(() => {
    fetchScreenshots()
  }, [fetchScreenshots])

  // Função para recarregar screenshots manualmente
  const refetch = useCallback(() => {
    fetchScreenshots()
  }, [fetchScreenshots])

  // Função para adicionar screenshot localmente (otimistic update)
  const addScreenshot = useCallback((screenshot: Screenshot) => {
    setScreenshots(prev => {
      // Evitar duplicatas
      const exists = prev.some(s => s.id === screenshot.id)
      if (exists) return prev
      
      return [...prev, screenshot]
    })
  }, [])

  // Função para remover screenshot localmente
  const removeScreenshot = useCallback((screenshotId: string) => {
    setScreenshots(prev => prev.filter(s => s.id !== screenshotId))
  }, [])

  return {
    screenshots,
    loading,
    error,
    refetch,
    addScreenshot,
    removeScreenshot
  }
} 

