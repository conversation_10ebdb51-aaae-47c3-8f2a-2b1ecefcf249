// Funções para gerenciar anotações

import { db } from './firebase';
import { Note } from '../types/influencer';
import { firestore } from 'firebase-admin';

// Referência à coleção de anotações
export const notesCollection = db.collection('notes');

// Buscar todas as anotações do sistema
export async function getAllNotes(): Promise<Note[]> {
  try {
    const snapshot = await notesCollection.get();
    
    // Convertemos os documentos para o formato Note
    const notes = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        content: data.content,
        type: data.type,
        influencerId: data.influencerId,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        createdBy: data.createdBy
      } as Note;
    });
    
    // Ordenamos manualmente pelo campo createdAt
    return notes.sort((a, b) => {
      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  } catch (error) {
    console.error('Erro ao buscar todas as anotações:', error);
    throw error;
  }
}

// Buscar todas as anotações de um influenciador
export async function getNotesByInfluencerId(influencerId: string): Promise<Note[]> {
  try {
    // Primeiro tentamos buscar apenas pelo influencerId sem ordenação
    // para evitar a necessidade de um índice composto
    let snapshot = await notesCollection
      .where('influencerId', '==', influencerId)
      .get();
    
    // Convertemos os documentos para o formato Note
    const notes = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        title: data.title,
        content: data.content,
        type: data.type,
        influencerId: data.influencerId,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        createdBy: data.createdBy
      } as Note;
    });
    
    // Ordenamos manualmente pelo campo createdAt
    return notes.sort((a, b) => {
      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  } catch (error) {
    console.error(`Erro ao buscar anotações do influenciador ${influencerId}:`, error);
    throw error;
  }
}

// Buscar uma anotação específica por ID
export async function getNoteById(id: string): Promise<Note | null> {
  try {
    const doc = await notesCollection.doc(id).get();
    if (!doc.exists) {
      return null;
    }
    const data = doc.data();
    if (!data) {
      return null;
    }
    return {
      id: doc.id,
      title: data.title,
      content: data.content,
      type: data.type,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
      createdBy: data.createdBy
    } as Note;
  } catch (error) {
    console.error(`Erro ao buscar anotação com ID ${id}:`, error);
    throw error;
  }
}

// Adicionar uma nova anotação
export async function addNote(influencerId: string, data: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>, createdBy?: string): Promise<string> {
  try {
    // Verificar se o influenciador existe
    const influencerRef = db.collection('influencers').doc(influencerId);
    const influencerDoc = await influencerRef.get();
    
    if (!influencerDoc.exists) {
      throw new Error(`Influenciador com ID ${influencerId} não encontrado`);
    }
    
    const now = firestore.Timestamp.now();
    const noteData = {
      influencerId,
      title: data.title,
      content: data.content,
      type: data.type || 'geral',
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy || 'sistema'
    };
    
    const docRef = await notesCollection.add(noteData);
    
    // Atualizar o influenciador com referência à nova anotação
    await influencerRef.update({
      noteIds: firestore.FieldValue.arrayUnion(docRef.id)
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Erro ao adicionar anotação:', error);
    throw error;
  }
}

// Atualizar uma anotação
export async function updateNote(id: string, data: Partial<Omit<Note, 'id' | 'createdAt'>>): Promise<boolean> {
  try {
    const updateData: Record<string, any> = {
      updatedAt: firestore.Timestamp.now()
    };
    
    if (data.title !== undefined) updateData.title = data.title;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.type !== undefined) updateData.type = data.type;
    
    await notesCollection.doc(id).update(updateData);
    return true;
  } catch (error) {
    console.error(`Erro ao atualizar anotação com ID ${id}:`, error);
    throw error;
  }
}

// Excluir uma anotação
export async function deleteNote(id: string): Promise<boolean> {
  try {
    // Buscar a anotação para obter o ID do influenciador
    const noteDoc = await notesCollection.doc(id).get();
    if (!noteDoc.exists) {
      throw new Error(`Anotação com ID ${id} não encontrada`);
    }
    
    const noteData = noteDoc.data();
    const influencerId = noteData?.influencerId;
    
    // Excluir a anotação
    await notesCollection.doc(id).delete();
    
    // Remover a referência da anotação no influenciador
    if (influencerId) {
      const influencerRef = db.collection('influencers').doc(influencerId);
      await influencerRef.update({
        noteIds: firestore.FieldValue.arrayRemove(id)
      });
    }
    
    return true;
  } catch (error) {
    console.error(`Erro ao excluir anotação com ID ${id}:`, error);
    throw error;
  }
}


