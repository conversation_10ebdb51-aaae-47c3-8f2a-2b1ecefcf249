"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, X, Loader2 } from "lucide-react";
import Image from "next/image";
import { compressImage, validateImageFile, getCompressionStats } from "@/lib/image-compression";

interface ImageUploadProps {
  onImageUpload: (file: File) => Promise<string>;
  currentImageUrl?: string;
  className?: string;
}

export function ImageUpload({ 
  onImageUpload, 
  currentImageUrl, 
  className = "" 
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validar arquivo usando o utilitário
    const validation = validateImageFile(file);
    if (!validation.valid) {
      alert(validation.error);
      return;
    }

    try {
      setIsUploading(true);
      
      // Comprimir imagem antes do upload (como Instagram/Facebook)
      const compressedFile = await compressImage(file, {
        maxWidth: 160,
        maxHeight: 160,
        quality: 0.85,
        format: 'jpeg'
      });
      
      // Log das estatísticas de compressão
      const stats = getCompressionStats(file, compressedFile);
      console.log(`📸 Compressão de imagem:`);
      console.log(`   Original: ${stats.originalSize}`);
      console.log(`   Comprimida: ${stats.compressedSize}`);
      console.log(`   Redução: ${stats.reduction}`);
      
      // Exibir preview da imagem comprimida
      const reader = new FileReader();
      reader.onload = (event) => {
        setPreviewUrl(event.target?.result as string);
      };
      reader.readAsDataURL(compressedFile);

      // Upload da imagem comprimida
      const imageUrl = await onImageUpload(compressedFile);
      setPreviewUrl(imageUrl);
    } catch (error) {
      console.error("Erro ao fazer upload da imagem:", error);
      alert("Erro ao fazer upload da imagem. Tente novamente.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className={`relative ${className}`}>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        ref={fileInputRef}
        className="hidden"
        id="image-upload"
      />

      {previewUrl ? (
        <div className="relative rounded-full overflow-hidden w-20 h-20 mx-auto">
          <Image
            src={previewUrl}
            alt="Imagem carregada"
            fill
            className="object-cover"
          />
          <button
            onClick={handleRemoveImage}
            className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-full"
            type="button"
          >
            <X size={16} />
          </button>
        </div>
      ) : (
        <label
          htmlFor="image-upload"
          className="flex flex-col items-center justify-center w-20 h-20 border-2 border-dashed border-gray-400 rounded-full cursor-pointer hover:border-gray-300 dark:border-gray-600 dark:hover:border-gray-500 mx-auto"
        >
          <div className="flex flex-col items-center justify-center">
            <Upload className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400 mt-1">Upload</span>
          </div>
        </label>
      )}

      {isUploading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
          <Loader2 className="w-6 h-6 text-white animate-spin" />
        </div>
      )}
    </div>
  );
}


