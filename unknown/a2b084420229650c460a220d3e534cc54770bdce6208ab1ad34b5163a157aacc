'use client';

import React, { useState, useEffect } from 'react';
import { TourProvider, useTour } from '@reactour/tour';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  HelpCircle, 
  X, 
  ChevronLeft, 
  ChevronRight,
  User,
  Phone,
  Calendar,
  Sparkles,
  BarChart3,
  CheckCircle2,
  DollarSign,
  TrendingUp,
  MessageSquare,
  Receipt
} from 'lucide-react';

// 🔧 UTILITÁRIO: Verificar se elemento está visível e tem conteúdo
const isElementVisibleAndHasContent = (selector: string): boolean => {
  const element = document.querySelector(selector);
  if (!element) return false;

  // Verificar se está visível
  const rect = element.getBoundingClientRect();
  const isVisible = rect.width > 0 && rect.height > 0;

  // Verificações específicas para diferentes tipos de elementos

  // Para seções de orçamento - verificar se há cards de orçamento
  if (selector.includes('budget-section')) {
    const budgetCards = element.querySelectorAll('.border-dashed, [data-budget-card]');
    return Boolean(isVisible && budgetCards.length > 0);
  }

  // Para seções de campanhas - verificar se há campanhas listadas
  if (selector.includes('influencer-campaigns')) {
    const campaignItems = element.querySelectorAll('[data-campaign-item]');
    const noCampaignsMessage = element.querySelector('.text-muted-foreground');
    return isVisible && (campaignItems.length > 0 || noCampaignsMessage !== null);
  }

  // Para seções de documentos - verificar se há documentos ou área de upload
  if (selector.includes('documents-section')) {
    const documentItems = element.querySelectorAll('[data-document-item]');
    const uploadArea = element.querySelector('[data-upload-area]');
    return isVisible && (documentItems.length > 0 || uploadArea !== null);
  }

  // Para links do WhatsApp - verificar se o href está presente
  if (selector.includes('wa.me')) {
    const link = element as HTMLAnchorElement;
    return Boolean(isVisible) && link.href && link.href.includes('wa.me');
  }

  // Verificação geral - verificar se tem conteúdo (não está vazio)
  const hasContent = (element.textContent?.trim().length ?? 0) > 0 ||
                    element.querySelector('img, svg, canvas, video') !== null ||
                    element.children.length > 0;

  return isVisible && hasContent;
};

// 🔧 UTILITÁRIO: Filtrar steps baseado na visibilidade dos elementos
const filterVisibleSteps = (steps: any[], showLogs: boolean = false) => {
  return steps.filter(step => {
    // Se não tem selector, sempre incluir (steps informativos)
    if (!step.selector) return true;

    // Verificar se o elemento está visível e tem conteúdo
    const isVisible = isElementVisibleAndHasContent(step.selector);

    // 🔥 CORREÇÃO: Só mostrar logs quando o tour for iniciado explicitamente
    if (!isVisible && showLogs) {
      console.log(`🚫 [TOUR] Pulando step - elemento não visível: ${step.selector}`);
    }

    return isVisible;
  });
};

// 🎯 TOUR UNIFICADO: Painel de Relacionamento + Contato + Perfil + Orçamentos Detalhados
const unifiedTourSteps = [
  // 1. Introdução Geral
  {
    selector: '[data-tour="responsive-layout"]',
    content: (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            🎯 Tour Completo do Sistema
          </h3>
          <p>Bem-vindo ao tour completo! Vamos explorar todas as funcionalidades do painel de relacionamento, contato e perfil dos influenciadores.</p>
        </div>
        
      </div>
    ),
  },

  // 2. Steps do Painel de Perfil
  {
    selector: '[data-tour="profile-panel"] .relative:first-child',
    content: (
      <div className="space-y-3">
        <div>
          <p>O avatar do influenciador</p>
        </div>
     
      </div>
    ),
  },
  {
    selector: '.flex.items-start.gap-2.md\\:gap-4',
    content: 'Nome do influenciador, idade, gênero e localização. Dados essenciais para entender o perfil demográfico.',
  },
 
  
  
  // Steps dos Gráficos Analytics
  {
    selector: '[data-tour="gender-chart"]',
    content: (
      <div className="space-y-3">
        <div>
          <p>Gráfico de Pizza que mostra a distribuição por gênero da audiência do influenciador. Essencial para entender o perfil demográfico e adequar campanhas ao público-alvo.</p>
        </div>
        
      </div>
    ),
  },
  {
    selector: '[data-tour="age-range-chart"]',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>Gráfico de Faixas Etárias</strong></p>
          <p>Mostra a distribuição por idades da audiência. Essencial para campanhas direcionadas a grupos específicos.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <img
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23f9fafb'/%3E%3Crect x='20' y='160' width='40' height='20' fill='%23ff0074'/%3E%3Crect x='70' y='120' width='40' height='60' fill='%235600ce'/%3E%3Crect x='120' y='80' width='40' height='100' fill='%23ff0074'/%3E%3Crect x='170' y='100' width='40' height='80' fill='%235600ce'/%3E%3Crect x='220' y='140' width='40' height='40' fill='%23ff0074'/%3E%3Ctext x='40' y='195' text-anchor='middle' font-size='10'%3E13-17%3C/text%3E%3Ctext x='90' y='195' text-anchor='middle' font-size='10'%3E18-24%3C/text%3E%3Ctext x='140' y='195' text-anchor='middle' font-size='10'%3E25-34%3C/text%3E%3Ctext x='190' y='195' text-anchor='middle' font-size='10'%3E35-44%3C/text%3E%3Ctext x='240' y='195' text-anchor='middle' font-size='10'%3E45+%3C/text%3E%3C/svg%3E"
            alt="Exemplo de Gráfico de Faixa Etária"
            className="w-full rounded-md border"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">Exemplo: Distribuição por faixas etárias</p>
        </div>
      </div>
    ),
  },
  {
    selector: '[data-tour="location-chart"]',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>Gráfico de Localização</strong></p>
          <p>Mostra os países de origem da audiência. Fundamental para campanhas com foco geográfico.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <img
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23f9fafb'/%3E%3Ccircle cx='150' cy='100' r='80' fill='none' stroke='%23e5e7eb' stroke-width='2'/%3E%3Cpath d='M150 20 A80 80 0 0 1 230 100 L150 100 Z' fill='%23ff0074'/%3E%3Cpath d='M230 100 A80 80 0 0 1 150 180 L150 100 Z' fill='%235600ce'/%3E%3Cpath d='M150 180 A80 80 0 0 1 70 100 L150 100 Z' fill='%23ff0074'/%3E%3Cpath d='M70 100 A80 80 0 0 1 150 20 L150 100 Z' fill='%235600ce'/%3E%3Ctext x='190' y='60' font-size='10' fill='white'%3EBrasil%3C/text%3E%3Ctext x='190' y='140' font-size='10' fill='white'%3EEUA%3C/text%3E%3Ctext x='110' y='140' font-size='10' fill='white'%3EArgentina%3C/text%3E%3Ctext x='110' y='60' font-size='10' fill='white'%3EPortugal%3C/text%3E%3C/svg%3E"
            alt="Exemplo de Gráfico de Localização"
            className="w-full rounded-md border"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">Exemplo: Distribuição geográfica da audiência</p>
        </div>
      </div>
    ),
  },
    {
    selector: '[data-tour="engagement-chart"]',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>Gráfico de Engajamento</strong></p>
          <p>Taxa de engajamento por rede social. Métricas cruciais para avaliar ROI potencial.</p>
        </div>
       
      </div>
    ),
  },
  
  // Steps do Painel de Relacionamento
  {
    selector: '[data-tour="relationship-panel"]',
    content: (
      <div className="space-y-3">
        <div>
          <p>Este é o Painel de Relacionamento! Centraliza todas as informações e interações comerciais com o influenciador: propostas, orçamentos e documentos.</p>
        </div>
      
      </div>
    ),
  },
  {
    selector: '[data-tour="proposal-selector"]',
    content: 'Seletor de Propostas permite filtrar a visualização por uma proposta específica ou ver todos os influenciadores. Clique para alternar entre propostas e ver dados contextualizados.',
  },

  // 🆕 SEÇÃO DETALHADA DE ORÇAMENTOS (integrada do budget-section-tour.tsx)
  {
    selector: '[data-tour="budget-section"]',
    content: (
      <div className="space-y-3">
        <div>
          <p>Bem-vindo à seção de Orçamento de Serviços! Esta é onde você gerencia todas as negociações financeiras com influenciadores, organizadas por plataformas e tipos de conteúdo.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <img 
            src="/1.png"
            alt="Exemplo real da Seção de Orçamento de Serviços" 
            className="w-full rounded-md border max-h-96 object-cover"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">Exemplo real: Seção completa de orçamentos organizados por plataforma</p>
        </div>
      </div>
    ),
  },
  {
    selector: '[data-tour="approved-total"]',
    content: (
      <div className="space-y-3">
        <div>
          <p>Total aprovado de orçamentos: Este valor mostra a soma de todos os orçamentos aprovados/aceitos. Apenas contrapropostas aceitas ou orçamentos aprovados são contabilizados neste valor.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <img 
            src="/2.png"
            alt="Exemplo real do Total Aprovado de Orçamentos" 
            className="w-full rounded-md border max-h-96 object-cover"
          />
       
        </div>
      </div>
    ),
  },
  {
    selector: '[data-tour="budget-section"] .space-y-4, [data-tour="budget-section"] .space-y-3',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>📋 Lista de Orçamentos por Plataforma</strong></p>
          <p>Orçamentos organizados por plataforma (Instagram, YouTube, TikTok). Cada plataforma agrupa seus respectivos tipos de serviços.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <img
            src="/4.png"
            alt="Exemplo real da Lista de Orçamentos por Plataforma"
            className="w-full rounded-md border max-h-96 object-cover"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">Exemplo: Instagram, YouTube e TikTok com orçamentos e contrapropostas</p>
        </div>
        
      </div>
    ),
  },
  {
    selector: '[data-tour="budget-section"] .border-dashed',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>Card Individual de Orçamento</strong></p>
          <p>Mostra: valor inicial (riscado), valor atual em negociação (colorido), histórico de contrapropostas e ações disponíveis.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <img
            src="/5.png"
            alt="Exemplo real de Card de Orçamento Individual"
            className="w-full rounded-md border max-h-96 object-cover"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">Exemplo: Card de orçamento com contraproposta pendente</p>
        </div>
       
      </div>
    ),
  },
  {
    selector: '[data-tour="budget-section"] .bg-green-50, [data-tour="budget-section"] .bg-green-900\\/20',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>✅ Contrapropostas Aceitas</strong></p>
          <p>Destacadas em verde. O sistema mantém histórico completo de todas as negociações com timestamps e status.</p>
        </div>
        
      </div>
    ),
  },
  {
    selector: '[data-tour="budget-section"] .lucide-message-square, [data-tour="budget-section"] svg[class*="message-square"]',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>💬 Ícone de Observações</strong></p>
          <p>Clique para ver anotações adicionais feitas pelo usuário na contraproposta.</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-blue-500" />
            <span className="text-sm">Ver observações</span>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Permite adicionar contexto e detalhes importantes sobre a negociação
          </p>
        </div>
      </div>
    ),
  },
  {
    selector: '[data-tour="budget-section"] .flex.gap-2.pt-2.border-t',
    content: 'Ações do orçamento: "Contraproposta" para iniciar nova negociação com valor diferente.',
  },
  {
    selector: '[data-tour="budget-section"] button:has(.lucide-dollar-sign)',
    content: 'Botão "Editar Orçamento" permite modificar o valor base, descrição e outros detalhes do orçamento original.',
  },
  {
    selector: '[data-tour="budget-section"] button:has(.lucide-trending-up)',
    content: 'Botão "Contraproposta" inicia uma nova negociação. Permite propor valor diferente mantendo histórico completo da conversa.',
  },

  // Continuação dos steps originais
  {
    selector: '[data-tour="social-screenshots"]',
    content: 'Seção de Screenshots das Redes Sociais permite capturar e armazenar prints das páginas do influenciador para documentar métricas e conteúdo para relatórios.',
  },
  
  {
    selector: '[data-tour="documents-section"]',
    content: (
      <div className="space-y-3">
        <div>
          <p><strong>Gerenciamento de Documentos</strong> para upload e organização de contratos, briefings, materiais de campanha e outros arquivos importantes relacionados ao influenciador.</p>
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 rounded-lg p-3">
          <p className="text-sm text-purple-800 dark:text-purple-200">
            📁 <strong>Organização:</strong> Esta seção permite manter todos os documentos organizados por influenciador, facilitando o acesso durante negociações e execução de campanhas.
          </p>
        </div>
      </div>
    ),
  },
  
  // Step Final de Conclusão
  {
    selector: '[data-tour="profile-panel"]',
    content: (
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <CheckCircle2 className="w-12 h-12 text-green-500" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            🎉 Tour Concluído!
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Agora você conhece todas as funcionalidades do painel de relacionamento, incluindo o gerenciamento detalhado de orçamentos e contrapropostas.
          </p>
         
        </div>
      </div>
    ),
  },
];



// Componente interno para escutar eventos do botão de ajuda
function DynamicTourContent({ children }: { children: React.ReactNode }) {
  const { setSteps, setIsOpen, setCurrentStep } = useTour();

  // 🔥 CORREÇÃO: APENAS escutar evento do botão de ajuda - sem verificações automáticas
  useEffect(() => {
    const handleStartTour = () => {
      console.log('🚀 [TOUR] Iniciando tour via botão de ajuda');
      // Filtrar steps apenas quando o tour for iniciado
      const visibleSteps = filterVisibleSteps(unifiedTourSteps, true); // 🔥 CORREÇÃO: Mostrar logs apenas aqui
      console.log(`📊 [TOUR] Steps disponíveis: ${unifiedTourSteps.length}, Steps visíveis: ${visibleSteps.length}`);
      setSteps?.(visibleSteps);
      setCurrentStep?.(0);
      setIsOpen?.(true);
    };

    window.addEventListener('start-unified-tour', handleStartTour);

    return () => {
      window.removeEventListener('start-unified-tour', handleStartTour);
    };
  }, [setSteps, setIsOpen, setCurrentStep]);



  return <>{children}</>;
}

// Provider principal do tour unificado
export function UnifiedTourProvider({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return <>{children}</>;

  return (
    <TourProvider
      steps={unifiedTourSteps}
      showBadge={true}
      showCloseButton={true}
      showNavigation={true}
      showDots={true}
      scrollSmooth={true}
      padding={{ mask: 10, popover: [10, 10] }}
      position="right"
      styles={{
        popover: (base: any) => ({
          ...base,
          backgroundColor: 'white',
          color: 'black',
          borderRadius: '8px',
          padding: '16px',
          maxWidth: '400px',
          boxShadow: '0 10px 25px rgba(0,0,0,0.2)',
        }),
        maskArea: (base: any) => ({
          ...base,
          rx: 8,
        }),
        badge: (base: any) => ({
          ...base,
          backgroundColor: '#ff0074',
          color: 'white',
        }),
        controls: (base: any) => ({
          ...base,
          marginTop: '20px',
        }),
        close: (base: any) => ({
          ...base,
          right: '15px',
          top: '15px',
        }),
      }}
      nextButton={({ currentStep, stepsLength, setIsOpen, setCurrentStep }: any) => {
        const isLastStep = currentStep === stepsLength - 1;
        return (
          <Button
            onClick={() => {
              if (isLastStep) {
                setIsOpen(false);
              } else {
                setCurrentStep((prev: any) => prev + 1);
              }
            }}
            className="bg-[#ff0074] hover:bg-[#e6006a] text-white"
          >
            {isLastStep ? (
              <>
                <CheckCircle2 className="w-4 h-4 mr-2" />
                Finalizar
              </>
            ) : (
              <>
                Próximo
                <ChevronRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        );
      }}
      prevButton={({ currentStep, setCurrentStep }: any) => {
        const isFirstStep = currentStep === 0;
        return !isFirstStep ? (
          <Button
            onClick={() => setCurrentStep((prev: any) => prev - 1)}
            className="mr-2"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>
        ) : null;
      }}
      onClickMask={({ setIsOpen }: any) => setIsOpen(false)}
    >
             <DynamicTourContent>
        {children}
      </DynamicTourContent>
    </TourProvider>
  );
}

// Hook personalizado para controlar o tour unificado
export function useUnifiedTour() {
  const { isOpen, setIsOpen, currentStep, setCurrentStep } = useTour();

  const startTour = () => {
    setCurrentStep(0);
    setIsOpen(true);
  };

  const stopTour = () => {
    setIsOpen(false);
  };

  const nextStep = () => {
    setCurrentStep((prev: any) => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep((prev: any) => Math.max(0, prev - 1));
  };

  const startBudgetTour = () => {
    // Encontrar o primeiro step da seção de orçamentos
    const budgetStepIndex = unifiedTourSteps.findIndex(step => 
      step.selector === '[data-tour="budget-section"]'
    );
    
    if (budgetStepIndex !== -1) {
      setCurrentStep(budgetStepIndex);
      setIsOpen(true);
    }
  };

  return {
    isOpen,
    currentStep,
    startTour,
    stopTour,
    nextStep,
    prevStep,
    startBudgetTour,
  };
}

// Manter compatibilidade com componentes antigos

export const ProfilePanelTourProvider = UnifiedTourProvider;
export const useProfilePanelTour = useUnifiedTour;

// 🆕 Exportar funcionalidades específicas do budget para compatibilidade
export const BudgetSectionTourProvider = UnifiedTourProvider;
export const useBudgetSectionTour = useUnifiedTour;
export function BudgetSectionAutoTour() {
  return null; // Funcionalidade integrada no UnifiedTourProvider
} 