"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useAuth as useCustomAuth } from '@/hooks/use-auth-v2'
import { useAuth as useFirebaseAuth } from '@/hooks/use-firebase-auth'
import { useBrandsList } from '@/hooks/use-brands'
import { UserButton, useUser } from '@clerk/nextjs'
import { ProposalService, Proposal } from '@/services/proposal-service'
import { useProposals } from '@/contexts/proposals-context'
import { 
  CLERK_PERMISSIONS, 
  CLERK_ROLES,
  type ClerkRole,
  type ClerkPermission 
} from '@/lib/clerk-fga-config'
import { 
  PROPOSAL_STATUS_LABELS,
  type ProposalStatus 
} from '@/types/proposal'
import { ProposalStatusBadge } from '@/components/ui/proposal-status-badge'

import { WorkspaceSwitcher } from '@/components/ui/workspace-switcher'
import {
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  Bell,
  HelpCircle,
  Home,
  Star,
  FileText,
  TrendingUp,
  Megaphone,
  Archive,
  ChevronDown,
  Crown,
  Building2,
  Target,
  Menu,
  BarChart3,
  Heart,
  Zap,
  Gift,
  Sparkles,
  Flame,
  Trophy,
  Rocket,
  Diamond,
  Music,
  Camera,
  Video,
  Palette,
  RefreshCw,
  AlertCircle
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { LanguageToggle } from "@/components/ui/language-toggle"
import { useTranslations } from "@/hooks/use-translations"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface SidebarItem {
  icon: React.ReactNode
  label: string
  href: string
  badge?: string | ProposalStatus
  submenu?: SidebarItem[]
  requiredPermission?: ClerkPermission
  requiredRole?: ClerkRole
  onClick?: () => void
}

// ✅ NOVO: Lista de ícones disponíveis para propostas no sidebar
const AVAILABLE_PROPOSAL_ICONS = [
  { name: 'FileText', icon: FileText },
  { name: 'Star', icon: Star },
  { name: 'Heart', icon: Heart },
  { name: 'Target', icon: Target },
  { name: 'Megaphone', icon: Megaphone },
  { name: 'Zap', icon: Zap },
  { name: 'TrendingUp', icon: TrendingUp },
  { name: 'Gift', icon: Gift },
  { name: 'Sparkles', icon: Sparkles },
  { name: 'Crown', icon: Crown },
  { name: 'Flame', icon: Flame },
  { name: 'Trophy', icon: Trophy },
  { name: 'Rocket', icon: Rocket },
  { name: 'Diamond', icon: Diamond },
  { name: 'Music', icon: Music },
  { name: 'Camera', icon: Camera },
  { name: 'Video', icon: Video },
  { name: 'Palette', icon: Palette },
];

// ✅ NOVO: Função para renderizar ícone da proposta no sidebar
const renderProposalIcon = (iconName: string = 'FileText') => {
  const iconConfig = AVAILABLE_PROPOSAL_ICONS.find(icon => icon.name === iconName);
  if (iconConfig) {
    const IconComponent = iconConfig.icon;
    return <IconComponent className="h-4 w-4" />;
  }
  return <FileText className="h-4 w-4" />;
};

// Função para criar submenu de propostas com marcas
const createPropostasSubmenu = (brands: any[], t: (key: string) => string): SidebarItem[] => {
  const submenuItems: SidebarItem[] = [
    {
      icon: <FileText className="h-4 w-4" />,
      label: t('navigation.all_proposals'),
      href: "/propostas",
    }
  ];

  // Adicionar marcas como subitens
  if (brands && brands.length > 0) {
    brands.forEach(brand => {
      submenuItems.push({
        icon: brand.logo ? (
          <img 
            src={brand.logo} 
            alt={brand.name}
            className="w-4 h-4 rounded object-cover"
          />
        ) : (
          <div className="w-4 h-4 rounded bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center text-xs text-white font-bold">
            {brand.name.charAt(0).toUpperCase()}
          </div>
        ),
        label: brand.name,
        href: `/propostas?brandId=${brand.id}&brandName=${encodeURIComponent(brand.name)}`,
      });
    });
  }

  return submenuItems;
};

// ✅ MELHORADO: Função para criar submenu de influenciadores com propostas
const createInfluencersSubmenu = (user: any, userProposals: Proposal[], proposalsLoading: boolean, proposalsError: string | null): SidebarItem[] => {
  const submenuItems: SidebarItem[] = [];

  // ✅ NOVO: Mostrar estado de carregamento
  if (proposalsLoading) {
    submenuItems.push({
      icon: <RefreshCw className="h-4 w-4 animate-spin" />,
      label: "Carregando propostas...",
      href: user ? `/${user.id}/influencers` : "/influencers",
    });
    return submenuItems;
  }

  // ✅ NOVO: Mostrar erro se houver
  if (proposalsError) {
    submenuItems.push({
      icon: <AlertCircle className="h-4 w-4 text-red-500" />,
      label: "Erro ao carregar propostas",
      href: user ? `/${user.id}/influencers` : "/influencers",
    });
    return submenuItems;
  }

  // Adicionar todas as propostas do usuário como subitens
  if (userProposals && userProposals.length > 0) {
    // ✅ ORDENAR por data de criação (mais recentes primeiro)
    const sortedProposals = [...userProposals].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    sortedProposals.forEach(proposal => {
      const proposalName = proposal.nome || `Proposta ${proposal.id.substring(0, 8)}`;
      
      submenuItems.push({
        icon: renderProposalIcon(proposal.icon),
        label: proposalName,
        href: user ? `/${user.id}/influencers?proposta=${proposal.id}` : `/influencers?proposta=${proposal.id}`,
        badge: proposal.status as ProposalStatus,
      });
    });
  }

  // ✅ MELHORADO: Adicionar link para todas as propostas sempre
 

  return submenuItems;
};

const getSidebarItems = (user: any, userPermissions: { has: (params: { permission: string }) => boolean, hasRole: (params: { role: string }) => boolean }, brands: any[], userProposals: Proposal[] = [], proposalsLoading = false, proposalsError: string | null = null, t: (key: string) => string): SidebarItem[] => {
  // Verificar se o usuário é admin ou manager
  const isAdmin = userPermissions?.hasRole({ role: CLERK_ROLES.ADMIN });
  const isManager = userPermissions?.hasRole({ role: CLERK_ROLES.MANAGER });
  
  // Se for admin, mostrar todos os links
  if (isAdmin) {
    return [
      {
        icon: <Users className="h-4 w-4 stroke-[1.5]" />,
        label: t('navigation.influencers'),
        href: user ? `/${user.id}/influencers` : "/influencers",
        submenu: createInfluencersSubmenu(user, userProposals, proposalsLoading, proposalsError),
        badge: userProposals.length > 0 ? userProposals.length.toString() : undefined,
      },
      {
        icon: <Star className="h-4 w-4 stroke-[1.5]" />,
        label: t('navigation.creators'),
        href: "/creators",
        submenu: [
          {
            icon: <Archive className="h-4 w-4 stroke-[1.5]" />,
            label: t('navigation.lists'),
            href: user ? `/creators/${user.id}/lists` : "/creators/lists",
          },
          {
            icon: <Users className="h-4 w-4 stroke-[1.5]" />,
            label: t('navigation.backlog'),
            href: user ? `/creators/${user.id}/backlog` : "/creators/backlog",
          },
        ],
      },
      {
        icon: <FileText className="h-4 w-4 stroke-[1.5]" />,
        label: t('navigation.proposals'),
        href: "/propostas",
        submenu: createPropostasSubmenu(brands, t),
      },
    ];
  }
  
  // Se for manager, mostrar apenas influenciadores com propostas
  if (isManager) {
    return [
      {
        icon: <Users className="h-4 w-4 stroke-[1.5]" />,
        label: t('navigation.influencers'),
        href: user ? `/${user.id}/influencers` : "/influencers",
        submenu: createInfluencersSubmenu(user, userProposals, proposalsLoading, proposalsError),
        badge: userProposals.length > 0 ? userProposals.length.toString() : undefined,
      },
    ];
  }

  // Para todos os outros roles, mostrar apenas Influenciadores com propostas
  return [
    {
      icon: <Users className="h-4 w-4 stroke-[1.5]" />,
      label: t('navigation.influencers'),
      href: user ? `/${user.id}/influencers` : "/influencers",
      submenu: createInfluencersSubmenu(user, userProposals, proposalsLoading, proposalsError),
      badge: userProposals.length > 0 ? userProposals.length.toString() : undefined,
    },
  ];
};

const getBottomItems = (user: any, userPermissions: { has: (params: { permission: string }) => boolean, hasRole: (params: { role: string }) => boolean }, t: (key: string) => string): SidebarItem[] => {
  const isAdmin = userPermissions?.hasRole({ role: CLERK_ROLES.ADMIN });
  const isManager = userPermissions?.hasRole({ role: CLERK_ROLES.MANAGER });
  
  const items: SidebarItem[] = [
    {
      icon: <Bell className="h-4 w-4 stroke-[1.5]" />,
      label: t('navigation.notifications'),
      href: "/notifications",
      badge: "3",
    },
    {
      icon: <HelpCircle className="h-4 w-4 stroke-[1.5]" />,
      label: t('navigation.help'),
      href: "#",
      onClick: () => {
        // Disparar o tour unificado
        const event = new CustomEvent('start-unified-tour');
        window.dispatchEvent(event);
      },
    },
  ];

  // Adicionar configurações para Admin e Manager
  if (isAdmin || isManager) {
    items.push({
      icon: <Settings className="h-4 w-4 stroke-[1.5]" />,
      label: t('navigation.settings'),
      href: user ? `/${user.id}/settings` : "/settings",
    });
  }
  
  return items;
};

export function SimpleSidebarStatic() {
  const { t } = useTranslations()
  const [isExpanded, setIsExpanded] = useState(true)
  // ✅ MODIFICADO: Inicializar com "Influenciadores" sempre expandido
  const [expandedMenus, setExpandedMenus] = useState<{ [key: string]: boolean }>({
    'Influenciadores': true
  })
  const [currentUrl, setCurrentUrl] = useState('')
  const [forceRender, setForceRender] = useState(0)
  const pathname = usePathname()
  const { currentUser: user, isLoading: loading } = useCustomAuth()
  const { hasPermission } = useFirebaseAuth()
  const { user: clerkUser } = useUser() // Para obter dados completos do usuário
  
  // Carregar marcas para o submenu de propostas
  const { brands } = useBrandsList()
  
  // ⚡ OTIMIZAÇÃO: Usar contexto compartilhado de propostas
  const { 
    proposals: userProposals, 
    loading: proposalsLoading, 
    error: proposalsError, 
    refreshProposals 
  } = useProposals()
  
  // ⚡ OTIMIZAÇÃO: Função de refresh agora vem do contexto compartilhado
  // Toda a lógica de carregamento foi movida para o ProposalsContext
  
  // Atualizar URL atual do lado do cliente
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setCurrentUrl(window.location.pathname + window.location.search)
    }
  }, [pathname])

  // Forçar re-render quando a URL mudar (incluindo query params)
  useEffect(() => {
    const handleUrlChange = () => {
      setForceRender(prev => prev + 1)
    }

    // Interceptar mudanças de rota do Next.js
    window.addEventListener('popstate', handleUrlChange)
    
    // Interceptar pushState e replaceState para capturar navegação programática
    const originalPushState = window.history.pushState
    const originalReplaceState = window.history.replaceState
    
    window.history.pushState = function(...args) {
      originalPushState.apply(this, args)
      setTimeout(handleUrlChange, 0) // Usar setTimeout para garantir que a URL foi atualizada
    }
    
    window.history.replaceState = function(...args) {
      originalReplaceState.apply(this, args)
      setTimeout(handleUrlChange, 0)
    }

    return () => {
      window.removeEventListener('popstate', handleUrlChange)
      window.history.pushState = originalPushState
      window.history.replaceState = originalReplaceState
    }
  }, [])

  const handleToggleSidebar = () => {
    setIsExpanded(!isExpanded)
  }

  const isActive = (href: string) => {
    if (typeof window === 'undefined') return false
    
    if (href === "/dashboard") {
      return pathname === "/" || pathname === "/dashboard"
    }
    
    const currentSearch = window.location.search
    
    // Para /propostas sem parâmetros, só é ativo se não há parâmetros brandId na URL atual
    if (href === "/propostas") {
      return pathname === "/propostas" && !currentSearch.includes('brandId')
    }
    
    // Para links com brandId, verificar se o brandId está na URL atual
    if (href.includes('brandId=')) {
      const urlParams = new URLSearchParams(currentSearch)
      const hrefParams = new URLSearchParams(href.split('?')[1])
      const brandId = hrefParams.get('brandId')
      return pathname === "/propostas" && urlParams.get('brandId') === brandId
    }
    
    // Para outros paths, usar startsWith
    return pathname?.startsWith(href) || false
  }

  const toggleSubmenu = (label: string) => {
    // ✅ MODIFICADO: Impedir que o menu "Influenciadores" seja fechado
    if (label === 'Influenciadores') {
      return; // Não fazer nada, mantém sempre expandido
    }
    
    setExpandedMenus(prev => ({
      ...prev,
      [label]: !prev[label]
    }))
  }

  if (loading) {
    return (
      <div className="w-64 h-full bg-white dark:bg-[#080210] border-r border flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  // Criar objeto de permissões para passar para as funções
  const userPermissions = {
    has: (params: { permission: string }) => hasPermission?.(params.permission) || false,
    hasRole: (params: { role: string }) => {
      // Usar os roles do Clerk diretamente
      if (params.role === CLERK_ROLES.ADMIN) {
        return user?.organizationMemberships?.[0]?.role === 'org:admin';
      }
      if (params.role === CLERK_ROLES.MANAGER) {
        return user?.organizationMemberships?.[0]?.role === 'org:manager';
      }
      if (params.role === CLERK_ROLES.MEMBER) {
        return user?.organizationMemberships?.[0]?.role === 'org:member';
      }
      return false;
    }
  }

  return (
    <motion.div
      initial={{ width: 256 }}
      animate={{ width: isExpanded ? 256 : '3rem' }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="h-full bg-white dark:bg-[#080210] border-r border flex flex-col"
    >
      {/* Header */}
      <div className="p-4 border-b ">
        <div className="flex items-center justify-between">
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-3"
              >
                <Link href="/dashboard" className="flex items-center gap-2">
                  <div className="w-8 h-8 flex items-center justify-center">
                    <img 
                      src="/logo deumatch.svg" 
                      alt="Deu Match" 
                      className="h-8 w-8 object-contain"
                    />
                  </div>
                </Link>
              </motion.div>
            )}
          </AnimatePresence>
          <div className="flex items-center gap-2">
            {/* ✅ NOVO: Botão de refresh das propostas */}
            {isExpanded && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button 
                      onClick={refreshProposals}
                      disabled={proposalsLoading}
                      className="h-8 w-8 p-0 inline-flex items-center justify-center rounded-md text-xs font-medium hover:bg-accent hover:text-accent-foreground disabled:pointer-events-none disabled:opacity-50"
                    >
                      <RefreshCw className={cn("h-4 w-4 stroke-[1.5]", proposalsLoading && "animate-spin")} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Atualizar propostas</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            <button 
              onClick={handleToggleSidebar}
              className="h-8 w-8 p-0 inline-flex items-center justify-center rounded-md text-xs font-medium hover:bg-accent hover:text-accent-foreground"
                          >
                {isExpanded ? (
                  <ChevronLeft className="h-4 w-4 stroke-[1.5]" />
                ) : (
                  <ChevronRight className="h-4 w-4 stroke-[1.5]" />
                )}
              </button>
          </div>
        </div>
      </div>

      {/* Workspace Switcher - Apenas para Admin e Manager */}
      {isExpanded && (userPermissions?.hasRole({ role: CLERK_ROLES.ADMIN }) || userPermissions?.hasRole({ role: CLERK_ROLES.MANAGER })) && (
        <div className="px-3 py-2">
          <WorkspaceSwitcher />
        </div>
      )}

      {/* Navigation Items */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-1 p-2">
          {getSidebarItems(user, userPermissions, brands, userProposals, proposalsLoading, proposalsError, t).map((item, index) => (
            <div key={`${item.href || item.label || 'item'}-${index}`}>
              {item.submenu ? (
                // Item com submenu
                <>
                  {/* ✅ MODIFICADO: Se for "Influenciadores", usar Link ao invés de button */}
                  {item.label === 'Influenciadores' ? (
                    <Link
                      href={item.href}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200",
                        isActive(item.href) || (item.submenu && item.submenu.some(subItem => isActive(subItem.href)))
                          ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400 border border-purple-500/30"
                          : "text-gray-700 dark:text-white hover:text-white hover:bg-[#ff0074]",
                        !isExpanded && "justify-center"
                      )}
                    >
                      <div className="flex-shrink-0">{item.icon}</div>
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.2 }}
                            className="flex-1 flex items-center justify-between"
                          >
                            <span className=" text-xs">{item.label}</span>
                            <div className="flex items-center gap-2">
                              {item.badge && (
                                <Badge className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white text-xs">
                                  {item.badge}
                                </Badge>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Link>
                  ) : (
                    <button
                      onClick={() => toggleSubmenu(item.label)}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200",
                        isActive(item.href) || (item.submenu && item.submenu.some(subItem => isActive(subItem.href)))
                          ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400 border border-purple-500/30"
                          : "text-gray-700 dark:text-white hover:text-white hover:bg-[#ff0074]",
                        !isExpanded && "justify-center"
                      )}
                    >
                      <div className="flex-shrink-0">{item.icon}</div>
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            transition={{ duration: 0.2 }}
                            className="flex-1 flex items-center justify-between"
                          >
                            <span className=" text-xs">{item.label}</span>
                            <div className="flex items-center gap-2">
                              {item.badge && (
                                <Badge className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white text-xs">
                                  {item.badge}
                                </Badge>
                              )}
                              {/* ✅ MODIFICADO: Não mostrar chevron para "Influenciadores" */}
                              {item.label !== 'Influenciadores' && (
                                <ChevronDown 
                                  className={cn(
                                    "h-4 w-4 stroke-[1.5] transition-transform duration-200",
                                    expandedMenus[item.label] && "rotate-180"
                                  )}
                                />
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </button>
                  )}
                  
                  {/* Submenu */}
                  <AnimatePresence>
                    {((expandedMenus[item.label] || item.label === 'Influenciadores') && isExpanded) && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="ml-4 mt-1 space-y-1"
                      >
                        {item.submenu.map((subItem, subIndex) => (
                          <Link
                            key={`sub-${subItem.href || subItem.label || 'subitem'}-${subIndex}`}
                            href={subItem.href}
                            className={cn(
                              "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-xs",
                              isActive(subItem.href)
                                ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400 border border-purple-500/30"
                                : "text-gray-600 dark:text-gray-300 hover:text-white hover:bg-[#ff0074]"
                            )}
                          >
                            <div className="flex-shrink-0">{subItem.icon}</div>
                            <span className="font-medium text-xs flex-1 truncate">{subItem.label}</span>
                            {subItem.badge && (
                              typeof subItem.badge === 'string' && Object.values(PROPOSAL_STATUS_LABELS).includes(subItem.badge) ? (
                                <ProposalStatusBadge 
                                  status={Object.keys(PROPOSAL_STATUS_LABELS).find(key => 
                                    PROPOSAL_STATUS_LABELS[key as ProposalStatus] === subItem.badge
                                  ) as ProposalStatus} 
                                  size="sm"
                                />
                              ) : subItem.badge in PROPOSAL_STATUS_LABELS ? (
                                <ProposalStatusBadge 
                                  status={subItem.badge as ProposalStatus} 
                                  size="sm"
                                />
                              ) : (
                                <Badge className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white text-xs">
                                  {subItem.badge}
                                </Badge>
                              )
                            )}
                          </Link>
                        ))}
                      </motion.div>
                    )}
                                      </AnimatePresence>
                  </>
                ) : (
                // Item sem submenu
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200",
                    isActive(item.href)
                      ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400 border border-purple-500/30"
                      : "text-gray-700 dark:text-white hover:text-white hover:bg-[#ff0074]",
                    !isExpanded && "justify-center"
                  )}
                >
                  <div className="flex-shrink-0">{item.icon}</div>
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ duration: 0.2 }}
                        className="flex-1 flex items-center justify-between"
                      >
                        <span className=" text-xs">{item.label}</span>
                        {item.badge && (
                          <Badge className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Link>
              )}
            </div>
          ))}
        </nav>
      </div>

      {/* Bottom Items */}
      <div className="border-t p-3">
        <nav className="space-y-1">
          {getBottomItems(user, userPermissions, t).map((item, index) => {
            // Se tem onClick, renderizar como button, senão como Link
            const Component = item.onClick ? 'button' : Link;
            const props = item.onClick
              ? { onClick: item.onClick, type: 'button' as const }
              : { href: item.href };

            return (
              <Component
                key={`bottom-${item.href || item.label || 'item'}-${index}`}
                {...props}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 w-full text-left",
                  !item.onClick && isActive(item.href)
                    ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-600 dark:text-purple-400"
                    : "text-gray-700 dark:text-white hover:text-white hover:bg-[#ff0074]",
                  !isExpanded && "justify-center"
                )}
              >
              <div className="flex-shrink-0">{item.icon}</div>
              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                    className="flex-1 flex items-center justify-between"
                  >
                    <span className=" text-xs">{item.label}</span>
                    {item.badge && (
                      <Badge className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white text-xs h-4 w-0 p-2 flex items-center justify-center rounded-full">
                        {item.badge}
                      </Badge>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </Component>
            );
          })}
          
          {/* Theme Toggle */}
          <div className="pt-2 border-t border-gray-200 dark:border-gray-800">
            <AnimatePresence mode="wait">
              {isExpanded ? (
                <motion.div
                  key="expanded"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.2 }}
                  className="space-y-1"
                >
                  <ThemeToggle isExpanded={true} />
                  <LanguageToggle isExpanded={true} />
                </motion.div>
              ) : (
                <motion.div
                  key="collapsed"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.2 }}
                  className="flex flex-col items-center gap-2"
                >
                  <ThemeToggle isExpanded={false} />
                  <LanguageToggle isExpanded={false} />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* User Button */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.2 }}
                className="pt-2"
              >
                <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <UserButton
                    appearance={{
                      elements: {
                        avatarBox: "w-8 h-8",
                        userButtonTrigger: "p-0"
                      }
                    }}
                  />
                  {clerkUser && (
                    <div className="flex flex-col min-w-0 flex-1">
                      <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {clerkUser.fullName || `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim()}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {clerkUser.primaryEmailAddress?.emailAddress}
                      </span>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </nav>
      </div>
    </motion.div>
  )
} 