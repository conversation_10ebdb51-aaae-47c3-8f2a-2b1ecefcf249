'use client';

import React, { useState, useCallback } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Share2, 
  Copy, 
  Link2, 
  Facebook, 
  Twitter, 
  Linkedin,
  MessageCircle,
  Mail,
  Check,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ShareListDialogProps {
  isOpen: boolean;
  onClose: () => void;
  listName: string;
  listId: string;
  listType: string;
  itemCount: number;
}

interface ShareOption {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  action: (url: string, text: string) => void;
}

export function ShareListDialog({ 
  isOpen, 
  onClose, 
  listName, 
  listId, 
  listType,
  itemCount 
}: ShareListDialogProps) {
  const [shareUrl, setShareUrl] = useState('');
  const [isGeneratingUrl, setIsGeneratingUrl] = useState(false);
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});

  // Gerar URL de compartilhamento via API
  const generateShareUrl = useCallback(async () => {
    if (shareUrl) return shareUrl;

    setIsGeneratingUrl(true);
    try {
      console.log('🔄 Gerando URL de compartilhamento para lista:', listId);

      // Chamar API para criar token de compartilhamento
      const response = await fetch(`/api/lists/${listId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao criar compartilhamento');
      }

      const data = await response.json();

      console.log('✅ URL de compartilhamento gerada:', {
        token: data.token,
        itemCount: data.itemCount,
        expiresAt: data.expiresAt
      });

      setShareUrl(data.shareUrl);
      return data.shareUrl;
    } catch (error: any) {
      console.error('❌ Erro ao gerar URL de compartilhamento:', error);

      // Mensagens de erro específicas
      if (error.message.includes('Lista vazia')) {
        toast.error('Lista vazia não pode ser compartilhada');
      } else if (error.message.includes('não contém influenciadores')) {
        toast.error('Lista não contém influenciadores para compartilhar');
      } else if (error.message.includes('não encontrada')) {
        toast.error('Lista não encontrada');
      } else if (error.message.includes('permissão')) {
        toast.error('Sem permissão para compartilhar esta lista');
      } else {
        toast.error('Erro ao gerar link de compartilhamento');
      }

      return '';
    } finally {
      setIsGeneratingUrl(false);
    }
  }, [shareUrl, listId]);

  // Copiar para clipboard com feedback visual
  const copyToClipboard = useCallback(async (text: string, key: string = 'default') => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback para contextos não seguros
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }

      setCopiedStates(prev => ({ ...prev, [key]: true }));
      toast.success('Link copiado para a área de transferência!');
      
      // Reset do estado após 2 segundos
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [key]: false }));
      }, 2000);
    } catch (error) {
      console.error('Erro ao copiar:', error);
      toast.error('Erro ao copiar link');
    }
  }, []);

  // Verificar se Web Share API está disponível
  const canUseWebShare = typeof navigator !== 'undefined' && 'share' in navigator;

  // Compartilhar via Web Share API nativa
  const shareViaWebAPI = useCallback(async () => {
    const url = await generateShareUrl();
    if (!url) return;

    const shareData = {
      title: `Lista: ${listName}`,
      text: `Confira esta lista de ${itemCount} ${listType}: ${listName}`,
      url: url,
    };

    try {
      if (canUseWebShare) {
        await navigator.share(shareData);
        toast.success('Compartilhado com sucesso!');
      }
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        console.error('Erro ao compartilhar:', error);
        toast.error('Erro ao compartilhar');
      }
    }
  }, [generateShareUrl, listName, itemCount, listType, canUseWebShare]);

  // Opções de compartilhamento social
  const shareOptions: ShareOption[] = [
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: <MessageCircle className="h-4 w-4" />,
      color: 'bg-green-500 hover:bg-green-600',
      action: (url, text) => {
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(`${text}\n${url}`)}`;
        window.open(whatsappUrl, '_blank', 'noopener,noreferrer');
      }
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: <Twitter className="h-4 w-4" />,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: (url, text) => {
        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        window.open(twitterUrl, '_blank', 'noopener,noreferrer');
      }
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: <Linkedin className="h-4 w-4" />,
      color: 'bg-blue-700 hover:bg-blue-800',
      action: (url, text) => {
        const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        window.open(linkedinUrl, '_blank', 'noopener,noreferrer');
      }
    },
    {
      id: 'email',
      name: 'Email',
      icon: <Mail className="h-4 w-4" />,
      color: 'bg-gray-600 hover:bg-gray-700',
      action: (url, text) => {
        const subject = encodeURIComponent(`Lista: ${listName}`);
        const body = encodeURIComponent(`${text}\n\n${url}`);
        const emailUrl = `mailto:?subject=${subject}&body=${body}`;
        window.location.href = emailUrl;
      }
    }
  ];

  // Texto de compartilhamento
  const shareText = `Confira esta lista de ${itemCount} ${listType}: ${listName}`;

  // Compartilhar via opção social
  const handleSocialShare = useCallback(async (option: ShareOption) => {
    const url = await generateShareUrl();
    if (!url) return;

    try {
      option.action(url, shareText);
      toast.success(`Compartilhando via ${option.name}...`);
    } catch (error) {
      console.error(`Erro ao compartilhar via ${option.name}:`, error);
      toast.error(`Erro ao compartilhar via ${option.name}`);
    }
  }, [generateShareUrl, shareText]);

  // Inicializar URL quando modal abre
  React.useEffect(() => {
    if (isOpen && !shareUrl) {
      generateShareUrl();
    }
  }, [isOpen, shareUrl, generateShareUrl]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5 text-[#ff0074]" />
            Compartilhar Lista
          </DialogTitle>
          <DialogDescription>
            Compartilhe "{listName}" com outras pessoas
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Web Share API nativa (mobile/PWA) */}
          {canUseWebShare && (
            <>
              <Button
                onClick={shareViaWebAPI}
                className="w-full bg-gradient-to-r from-[#ff0074] to-[#5600ce] hover:from-[#ff0074]/90 hover:to-[#5600ce]/90 text-white"
                disabled={isGeneratingUrl}
              >
                {isGeneratingUrl ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Share2 className="h-4 w-4 mr-2" />
                )}
                Compartilhar
              </Button>
              <Separator />
            </>
          )}

          {/* Link direto */}
          <div className="space-y-2">
            <Label htmlFor="share-url">Link de Compartilhamento</Label>
            <div className="flex gap-2">
              <Input
                id="share-url"
                value={shareUrl || 'Gerando link...'}
                readOnly
                className="flex-1 bg-muted/50"
                disabled={isGeneratingUrl}
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(shareUrl, 'url')}
                disabled={!shareUrl || isGeneratingUrl}
                className={cn(
                  "transition-colors",
                  copiedStates.url && "bg-green-100 border-green-300 text-green-700"
                )}
              >
                {copiedStates.url ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Opções de redes sociais */}
          <div className="space-y-3">
            <Label>Compartilhar em Redes Sociais</Label>
            <div className="grid grid-cols-2 gap-2">
              {shareOptions.map((option) => (
                <Button
                  key={option.id}
                  variant="outline"
                  onClick={() => handleSocialShare(option)}
                  disabled={isGeneratingUrl}
                  className={cn(
                    "justify-start gap-2 text-white border-none",
                    option.color
                  )}
                >
                  {option.icon}
                  {option.name}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
