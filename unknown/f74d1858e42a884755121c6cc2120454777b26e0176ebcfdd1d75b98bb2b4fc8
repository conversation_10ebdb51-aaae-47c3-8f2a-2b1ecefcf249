import React, { useState, useRef, useEffect } from 'react'
import { Eye, Download, Loader2, ImageIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

/**
 * Gera URL de thumbnail otimizada para um screenshot
 */
function getThumbnailUrl(originalUrl: string): string {
  // Se for uma URL do Firebase Storage, extrair o caminho
  if (originalUrl.includes('storage.googleapis.com') || originalUrl.includes('firebasestorage.googleapis.com')) {
    // Extrair o caminho do arquivo da URL do Firebase
    const urlParts = originalUrl.split('/')
    const pathIndex = urlParts.findIndex(part => part === 'o') + 1
    if (pathIndex > 0 && pathIndex < urlParts.length) {
      const encodedPath = urlParts[pathIndex].split('?')[0]
      const decodedPath = decodeURIComponent(encodedPath)
      return `/api/screenshots/thumbnail/${decodedPath}`
    }
  }

  // Se for uma URL relativa da nossa API, converter para thumbnail
  if (originalUrl.startsWith('/api/screenshots/')) {
    const path = originalUrl.replace('/api/screenshots/', '')
    return `/api/screenshots/thumbnail/${path}`
  }

  // Fallback: usar URL original
  return originalUrl
}

interface LazyScreenshotProps {
  src: string
  alt: string
  filename: string
  platform: string
  className?: string
  onView?: (src: string) => void
  onDownload?: (src: string, platform: string, filename: string) => void
  showActions?: boolean
  aspectRatio?: 'square' | 'video' | 'auto'
  useThumbnail?: boolean // Se deve usar thumbnail otimizada por padrão
}

export function LazyScreenshot({
  src,
  alt,
  filename,
  platform,
  className,
  onView,
  onDownload,
  showActions = true,
  aspectRatio = 'video',
  useThumbnail = true
}: LazyScreenshotProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [shouldLoad, setShouldLoad] = useState(false)
  const [currentSrc, setCurrentSrc] = useState<string>('')
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Determinar qual URL usar (thumbnail ou original)
  const thumbnailSrc = useThumbnail ? getThumbnailUrl(src) : src
  const displaySrc = currentSrc || thumbnailSrc

  // Intersection Observer para lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !shouldLoad && !isLoaded) {
            setShouldLoad(true)
          }
        })
      },
      {
        rootMargin: '50px', // Começar a carregar 50px antes de entrar na viewport
        threshold: 0.1
      }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current)
      }
    }
  }, [shouldLoad, isLoaded])

  // Carregar imagem quando necessário
  useEffect(() => {
    if (shouldLoad && !isLoaded && !isLoading) {
      setIsLoading(true)
      setHasError(false)

      const img = new Image()
      img.onload = () => {
        setCurrentSrc(thumbnailSrc)
        setIsLoaded(true)
        setIsLoading(false)
      }
      img.onerror = () => {
        setHasError(true)
        setIsLoading(false)
      }
      img.src = thumbnailSrc
    }
  }, [shouldLoad, isLoaded, isLoading, thumbnailSrc])

  const handleClick = () => {
    if (isLoaded && onView) {
      // Sempre passar a URL original para visualização em tela cheia
      onView(src)
    } else if (!isLoaded && !isLoading) {
      // Forçar carregamento se usuário clicar
      setShouldLoad(true)
    }
  }

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isLoaded && onDownload) {
      onDownload(src, platform, filename)
    }
  }

  const aspectRatioClass = {
    square: 'aspect-square',
    video: 'aspect-video',
    auto: 'aspect-auto'
  }[aspectRatio]

  return (
    <div 
      ref={containerRef}
      className={cn(
        "group relative rounded-md overflow-hidden bg-muted cursor-pointer",
        aspectRatioClass,
        className
      )}
      onClick={handleClick}
    >
      {/* Placeholder/Loading State */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          {isLoading ? (
            <div className="w-full h-full bg-muted animate-pulse" />
          ) : hasError ? (
            <div className="flex flex-col items-center gap-2 text-red-400">
              <ImageIcon className="h-6 w-6" />
              <span className="text-xs">Erro ao carregar</span>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-2">
              <ImageIcon className="h-6 w-6 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Clique para carregar</span>
            </div>
          )}
        </div>
      )}

      {/* Imagem carregada */}
      {isLoaded && (
        <img
          ref={imgRef}
          src={displaySrc}
          alt={alt}
          className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
          loading="lazy"
        />
      )}

      {/* Overlay com ações no hover */}
      {showActions && isLoaded && (
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-1">
          <Button
            size="sm"
            variant="secondary"
            onClick={handleClick}
            className="h-7 w-7 p-0 bg-white/90 hover:bg-white"
            title="Visualizar"
          >
            <Eye className="h-3 w-3" />
          </Button>
          
          {onDownload && (
            <Button
              size="sm"
              variant="secondary"
              onClick={handleDownload}
              className="h-7 w-7 p-0 bg-white/90 hover:bg-white"
              title="Baixar"
            >
              <Download className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}

      {/* Indicador de carregamento no canto */}
      {isLoading && (
        <div className="absolute top-2 right-2">
          <div className="w-4 h-4 bg-white/90 rounded-full animate-pulse" />
        </div>
      )}
    </div>
  )
}
