'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Registra o erro no console ou em um serviço de monitoramento
    console.error(error);
  }, [error]);

  return (
    <html lang="pt-BR">
      <body>
        <div className="flex flex-col items-center justify-center min-h-screen bg-[#080210] p-4">
          <div className="max-w-md text-center">
            <h2 className="text-3xl font-bold text-red-600 mb-4">Erro crítico!</h2>
            <p className="text-gray-300 mb-6">
              Ocorreu um erro crítico na aplicação. Por favor, tente recarregar a página.
            </p>
            <Button 
              onClick={reset}
              className="bg-background hover:bg-background text-white"
            >
              Recarregar aplicação
            </Button>
          </div>
        </div>
      </body>
    </html>
  );
}


