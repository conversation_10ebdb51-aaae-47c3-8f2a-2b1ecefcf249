"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, X, User, Plus } from "lucide-react"
import { toast } from "sonner"
import { Influencer } from '@/types/influencer'
import { useFirebaseAuth } from '@/hooks/use-clerk-auth'

interface AddProfileToListModalProps {
  isOpen: boolean;
  onClose: () => void;
  listId: string;
  onSuccess?: () => void;
}

export function AddProfileToListModal({ isOpen, onClose, listId, onSuccess }: AddProfileToListModalProps) {
  const { firebaseUser, getToken } = useFirebaseAuth();
  const [influencers, setInfluencers] = useState<Influencer[]>([]);
  const [selectedInfluencers, setSelectedInfluencers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [adding, setAdding] = useState(false);

  // Carregar todos os influenciadores
  const loadAllInfluencers = async () => {
    if (!firebaseUser) {
      console.error('❌ Usuário não autenticado');
      toast.error('Usuário não autenticado');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Carregando influenciadores...');
      
      const token = await getToken() || '';
      
      const response = await fetch('/api/influencers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Influenciadores carregados:', {
        total: data.length,
        primeiros3: data.slice(0, 3).map((inf: any) => inf.name || inf.nome)
      });

      setInfluencers(data || []);
    } catch (error) {
      console.error('❌ Erro ao carregar influenciadores:', error);
      toast.error('Erro ao carregar influenciadores');
      setInfluencers([]);
    } finally {
      setLoading(false);
    }
  };

  // Adicionar influenciadores selecionados à lista
  const handleAddInfluencers = async () => {
    if (selectedInfluencers.length === 0) return;

    if (!firebaseUser) {
      console.error('❌ Usuário não autenticado');
      toast.error('Usuário não autenticado');
      return;
    }

    setAdding(true);
    try {
      console.log('➕ Adicionando influenciadores à lista:', {
        listId,
        selectedInfluencers,
        total: selectedInfluencers.length
      });

      const token = await getToken() || '';
      let sucessos = 0;
      let falhas = 0;
      let duplicados = 0;
      const errosDetalhados: Array<{influencerId: string, erro: string, status?: number}> = [];

      // Adicionar cada influenciador individualmente
      for (const influencerId of selectedInfluencers) {
        try {
          console.log(`🔄 Processando influenciador ${influencerId}...`);

          const requestBody = {
            listaId: listId,
            itemId: influencerId,
            tipoItem: 'influenciador'
          };

          console.log(`📤 Enviando dados para API:`, {
            url: `/api/lists/${listId}/items`,
            body: requestBody
          });

          const response = await fetch(`/api/lists/${listId}/items`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(requestBody),
          });

          console.log(`📥 Resposta da API para ${influencerId}:`, {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            headers: Object.fromEntries(response.headers.entries())
          });

          if (response.ok) {
            const successData = await response.json();
            sucessos++;
            console.log(`✅ Influenciador ${influencerId} adicionado com sucesso:`, successData);
          } else {
            // Tentar capturar dados de erro da resposta
            let errorData;
            let errorMessage = `Erro HTTP ${response.status}: ${response.statusText}`;

            try {
              const responseText = await response.text();
              console.log(`📄 Texto bruto da resposta de erro:`, responseText);

              if (responseText) {
                try {
                  errorData = JSON.parse(responseText);
                  errorMessage = errorData.error || errorData.message || errorMessage;

                  // Tratamento específico para diferentes tipos de erro
                  if (response.status === 409) {
                    errorMessage = 'Este influenciador já está na lista';
                    duplicados++;
                  } else if (response.status === 403) {
                    errorMessage = 'Sem permissão para adicionar itens nesta lista';
                  } else if (response.status === 404) {
                    errorMessage = 'Lista não encontrada';
                  }
                } catch (parseError) {
                  console.warn(`⚠️ Não foi possível fazer parse do JSON de erro:`, parseError);
                  errorMessage = responseText || errorMessage;
                }
              }
            } catch (textError) {
              console.warn(`⚠️ Não foi possível ler texto da resposta:`, textError);
            }

            errosDetalhados.push({
              influencerId,
              erro: errorMessage,
              status: response.status
            });

            console.error(`❌ Erro ao adicionar influenciador ${influencerId}:`, {
              status: response.status,
              statusText: response.statusText,
              errorData,
              errorMessage
            });

            falhas++;
          }
        } catch (networkError) {
          const errorMessage = networkError instanceof Error ? networkError.message : 'Erro de rede desconhecido';

          errosDetalhados.push({
            influencerId,
            erro: `Erro de rede: ${errorMessage}`
          });

          console.error(`❌ Erro de rede ao adicionar influenciador ${influencerId}:`, {
            error: networkError,
            message: errorMessage,
            stack: networkError instanceof Error ? networkError.stack : undefined
          });

          falhas++;
        }
      }

      // Log do resumo final
      console.log('📊 Resumo da operação:', {
        total: selectedInfluencers.length,
        sucessos,
        falhas,
        duplicados,
        errosDetalhados
      });

      // Mostrar resultado com informações mais detalhadas
      if (sucessos > 0 && falhas === 0) {
        let mensagem = `${sucessos} influenciador(es) adicionado(s) à lista com sucesso!`;
        if (duplicados > 0) {
          mensagem += ` (${duplicados} já estavam na lista)`;
        }
        toast.success(mensagem);
      } else if (sucessos > 0 && falhas > 0) {
        let mensagem = `${sucessos} adicionado(s) com sucesso`;
        if (duplicados > 0) {
          mensagem += `, ${duplicados} já estavam na lista`;
        }
        const falhasReais = falhas - duplicados;
        if (falhasReais > 0) {
          mensagem += `, ${falhasReais} falharam`;
        }
        toast.warning(`${mensagem}. Verifique o console para detalhes.`);

        // Log detalhado dos erros para debug
        console.group('🔍 Detalhes dos erros:');
        errosDetalhados.forEach(erro => {
          console.error(`Influenciador ${erro.influencerId}:`, erro.erro);
        });
        console.groupEnd();
      } else {
        if (duplicados === selectedInfluencers.length) {
          toast.warning('Todos os influenciadores selecionados já estão na lista');
        } else {
          const primeiroErro = errosDetalhados[0];
          const mensagemErro = primeiroErro ?
            `Falha ao adicionar influenciadores: ${primeiroErro.erro}` :
            'Erro ao adicionar influenciadores à lista';

          toast.error(mensagemErro);
        }

        // Log todos os erros para debug
        console.group('🔍 Todos os erros encontrados:');
        errosDetalhados.forEach(erro => {
          console.error(`Influenciador ${erro.influencerId}:`, erro.erro);
        });
        console.groupEnd();
      }

      // Limpar seleção apenas se houve pelo menos um sucesso
      if (sucessos > 0) {
        setSelectedInfluencers([]);
      }

      // Chamar callback de sucesso se pelo menos um foi adicionado
      if (sucessos > 0 && onSuccess) {
        onSuccess();
      }

      // Fechar modal apenas se todos foram adicionados com sucesso
      if (sucessos > 0 && falhas === 0) {
        onClose();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

      console.error('❌ Erro geral ao adicionar influenciadores:', {
        error,
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        listId,
        selectedInfluencers
      });

      toast.error(`Erro geral: ${errorMessage}`);
    } finally {
      setAdding(false);
    }
  };

  // Limpar estados do modal
  const clearModalStates = () => {
    setSelectedInfluencers([]);
    setSearchTerm('');
    setInfluencers([]);
  };

  // Carregar influenciadores quando modal abrir - com verificações de segurança
  useEffect(() => {
    if (isOpen && firebaseUser && !loading && listId && listId !== '') {
      loadAllInfluencers();
    } else if (!isOpen) {
      clearModalStates();
    }
  }, [isOpen, firebaseUser, listId]); // Adicionado listId nas dependências

  // Filtrar influenciadores baseado na pesquisa
  const filteredInfluencers = influencers.filter(influencer => {
    if (!influencer || !influencer.name) return false;

    const name = influencer.name.toLowerCase();
    const category = (influencer.category || '').toLowerCase();
    const search = searchTerm.toLowerCase();

    return name.includes(search) || category.includes(search);
  });

  // Não renderizar se não há listId válido (após todos os hooks)
  if (!listId || listId === '') {
    return null;
  }

  return (
    <Dialog 
      open={isOpen}
      modal={true}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
          clearModalStates();
        }
      }}
    >
      <DialogContent className="max-w-5xl max-h-[90vh] p-0">
        <DialogHeader className="sr-only">
          <DialogTitle>Adicionar perfis à lista</DialogTitle>
        </DialogHeader>
        
        <div className="bg-[#9810fa] text-white p-6 rounded-t-lg flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            Adicionar perfis à lista
            {influencers.length > 0 && (
              <span className="text-sm font-normal ml-2 opacity-80">
                ({influencers.length} disponíveis)
              </span>
            )}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              onClose();
              clearModalStates();
            }}
            className="text-white hover:bg-white/20"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="p-6 space-y-4">
          {/* Header de Filtros */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Pesquisar"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <select className="h-10 px-3 py-2 border rounded-md text-sm bg-background min-w-[100px]">
                <option>Perfis</option>
                <option>Instagram</option>
                <option>TikTok</option>
                <option>YouTube</option>
                <option>Todos</option>
              </select>

              <select className="h-10 px-3 py-2 border rounded-md text-sm bg-background min-w-[100px]">
                <option>E-mail</option>
                <option>Com e-mail</option>
                <option>Sem e-mail</option>
              </select>

              <select className="h-10 px-3 py-2 border rounded-md text-sm bg-background min-w-[140px]">
                <option>Data adicionada</option>
                <option>Mais recente</option>
                <option>Mais antigo</option>
                <option>Esta semana</option>
                <option>Este mês</option>
              </select>
            </div>
          </div>

          {/* Tabela de Influenciadores */}
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedInfluencers.length === filteredInfluencers.length && filteredInfluencers.length > 0}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedInfluencers(filteredInfluencers.map(inf => inf.id));
                        } else {
                          setSelectedInfluencers([]);
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>Influencer</TableHead>
                  <TableHead>Redes sociais</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Data adicionado</TableHead>
                </TableRow>
              </TableHeader>
            </Table>

            {/* Container rolável apenas para o corpo da tabela */}
            <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800">
              <Table>
                <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2 text-muted-foreground">
                      
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredInfluencers.map((influencer) => {
                    const isSelected = selectedInfluencers.includes(influencer.id);
                    
                    return (
                      <TableRow 
                        key={influencer.id}
                        className={`cursor-pointer hover:bg-muted/30 ${isSelected ? 'bg-blue-50' : ''}`}
                        onClick={() => {
                          setSelectedInfluencers(prev => 
                            prev.includes(influencer.id)
                              ? prev.filter(id => id !== influencer.id)
                              : [...prev, influencer.id]
                          );
                        }}
                      >
                        <TableCell>
                          <Checkbox
                            checked={isSelected}
                            onChange={() => {}}
                          />
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                              {influencer.avatar ? (
                                <img 
                                  src={influencer.avatar} 
                                  alt={influencer.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <User className="h-5 w-5 text-gray-500" />
                              )}
                            </div>
                            <div>
                              <div className="font-medium">{influencer.name}</div>
                              <div className="text-sm text-muted-foreground">@{influencer.name.toLowerCase().replace(' ', '')}</div>
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {influencer.socialNetworks?.instagram && (
                              <div className="flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                                  <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                                </svg>
                                <span className="text-xs text-muted-foreground">
                                  {((influencer.socialNetworks?.instagram?.followers || 0) / 1000).toFixed(0)}k
                                </span>
                              </div>
                            )}
                            {influencer.socialNetworks?.tiktok && (
                              <div className="flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 448 512" className="text-black dark:text-white">
                                  <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                                </svg>
                                <span className="text-xs text-muted-foreground">
                                  {((influencer.socialNetworks?.tiktok?.followers || 0) / 1000).toFixed(0)}k
                                </span>
                              </div>
                            )}
                            {influencer.socialNetworks?.youtube && (
                              <div className="flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                                  <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                                </svg>
                                <span className="text-xs text-muted-foreground">
                                  {((influencer.socialNetworks?.youtube?.followers || 0) / 1000).toFixed(0)}k
                                </span>
                              </div>
                            )}
                            {!influencer.socialNetworks?.instagram && !influencer.socialNetworks?.tiktok && !influencer.socialNetworks?.youtube && (
                              <span className="text-sm text-muted-foreground">-</span>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <span className="text-sm">
                            {influencer.email || '-'}
                          </span>
                        </TableCell>
                        
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {new Date().toLocaleDateString('pt-BR')}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
                
                {!loading && filteredInfluencers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      {searchTerm ? `Nenhum influenciador encontrado para "${searchTerm}"` : 'Nenhum influenciador encontrado'}
                    </TableCell>
                  </TableRow>
                )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Contador de selecionados */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{selectedInfluencers.length} selecionado(s)</span>
            <span>{filteredInfluencers.length} total</span>
          </div>
        </div>

        <div className="flex items-center justify-end gap-3 p-6 border-t bg-muted/30">
          <Button 
            variant="outline" 
            onClick={() => {
              onClose();
              clearModalStates();
            }}
            disabled={adding}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleAddInfluencers}
            className="bg-[#ff0074] hover:bg-[#ff0074]/90 text-white"
            disabled={selectedInfluencers.length === 0 || adding}
          >
            {adding ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Adicionando...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Adicionar ({selectedInfluencers.length})
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 


