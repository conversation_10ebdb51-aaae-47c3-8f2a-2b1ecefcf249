import React, { useState, useMemo } from 'react';
import { useTranslations } from '@/hooks/use-translations';
import { Bar<PERSON>hart<PERSON>, Eye, EyeOff, Filter, RotateCcw } from 'lucide-react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer
} from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from '@/components/ui/chart';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface AgeRangeData {
  platform: string;
  ageRange: string;
  percentage: number;
}

interface AgeRangeBarChartProps {
  audienceAgeRanges?: {
    [platform: string]: Array<{
      ageRange: string;
      percentage: number;
    }>;
  };
  title?: string;
  className?: string;
  showControls?: boolean;
  height?: number;
}

const AgeRangeBarChart: React.FC<AgeRangeBarChartProps> = ({
  audienceAgeRanges,
  title,
  className = "",
  showControls = true,
  height = 300
}) => {
  const { t } = useTranslations();
  const finalTitle = title || t('audience.age_ranges');
  
  // Estados para controles interativos
  const [visiblePlatforms, setVisiblePlatforms] = useState<Set<string>>(new Set());
  const [isInitialized, setIsInitialized] = useState(false);

  // Configuração das cores e labels para cada plataforma
  const platformConfig = {
    instagram: {
      label: "Instagram",
      color: "hsl(330 81% 60%)"
    },
    youtube: {
      label: "YouTube", 
      color: "hsl(0 72% 51%)"
    },
    tiktok: {
      label: "TikTok",
      color: "hsl(215 20% 65%)"
    },
    facebook: {
      label: "Facebook",
      color: "hsl(221 83% 53%)"
    },
    twitch: {
      label: "Twitch",
      color: "hsl(262 83% 58%)"
    },
    kwai: {
      label: "Kwai",
      color: "hsl(32 95% 44%)"
    },
  };

  // Ícones das plataformas (separado da config do chart)
  const platformIcons = {
    instagram: "📸",
    youtube: "📺",
    tiktok: "🎵",
    facebook: "👥",
    twitch: "🎮",
    kwai: "🎬"
  };

  // Processar dados e detectar plataformas com dados
  const { chartData, availablePlatforms, totalDataPoints } = useMemo(() => {
    const allAgeData: AgeRangeData[] = [];
    const platformsWithData = new Set<string>();
    
    if (audienceAgeRanges) {
      Object.keys(audienceAgeRanges).forEach(platform => {
        const platformData = audienceAgeRanges[platform];
        if (platformData && platformData.length > 0) {
          platformData.forEach((ageRange: { ageRange: string; percentage: number }) => {
            if (ageRange.ageRange && ageRange.percentage > 0) {
              allAgeData.push({
                platform: platform.toLowerCase(),
                ageRange: ageRange.ageRange,
                percentage: ageRange.percentage
              });
              platformsWithData.add(platform.toLowerCase());
            }
          });
        }
      });
    }

    // Inicializar plataformas visíveis na primeira renderização
    if (!isInitialized && platformsWithData.size > 0) {
      setVisiblePlatforms(new Set(platformsWithData));
      setIsInitialized(true);
    }
    
    // Agrupar por faixa etária
    const groupedData = allAgeData.reduce((acc, item) => {
      if (!acc[item.ageRange]) {
        acc[item.ageRange] = {};
      }
      acc[item.ageRange][item.platform] = item.percentage;
      return acc;
    }, {} as Record<string, Record<string, number>>);
    
    // Preparar dados para o gráfico
    const processedData = Object.entries(groupedData)
      .map(([ageRange, platformData]) => ({
        ageRange,
        ...platformData
      }))
      .sort((a, b) => {
        const getMinAge = (range: string): number => {
          const match = range.match(/(\d+)/);
          return match ? parseInt(match[1]) : 0;
        };
        return getMinAge(a.ageRange) - getMinAge(b.ageRange);
      });
    
    return {
      chartData: processedData,
      availablePlatforms: Array.from(platformsWithData),
      totalDataPoints: allAgeData.length
    };
  }, [audienceAgeRanges, isInitialized]);

  // Filtrar dados baseado nas plataformas visíveis
  const filteredChartData = useMemo(() => {
    return chartData.map(item => {
      const filteredItem: any = { ageRange: item.ageRange };
      availablePlatforms.forEach(platform => {
        if (visiblePlatforms.has(platform)) {
          filteredItem[platform] = item[platform as keyof typeof item] || 0;
        }
      });
      return filteredItem;
    });
  }, [chartData, availablePlatforms, visiblePlatforms]);

  // Funções de controle
  const togglePlatform = (platform: string) => {
    const newVisible = new Set(visiblePlatforms);
    if (newVisible.has(platform)) {
      newVisible.delete(platform);
    } else {
      newVisible.add(platform);
    }
    setVisiblePlatforms(newVisible);
  };

  const showAllPlatforms = () => {
    setVisiblePlatforms(new Set(availablePlatforms));
  };

  const hideAllPlatforms = () => {
    setVisiblePlatforms(new Set());
  };

  const resetView = () => {
    setVisiblePlatforms(new Set(availablePlatforms));
  };

  // Se não há dados
  if (totalDataPoints === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {finalTitle}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-muted-foreground mb-2">
              <BarChart3 className="h-12 w-12 mx-auto opacity-50" />
            </div>
            <p className="text-sm text-muted-foreground">
              {t('audience.no_age_data')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {finalTitle}
          </CardTitle>
          
          {showControls && availablePlatforms.length > 1 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={resetView}
                className="h-8 gap-1"
              >
                <RotateCcw className="h-3 w-3" />
                Reset
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={visiblePlatforms.size === availablePlatforms.length ? hideAllPlatforms : showAllPlatforms}
                className="h-8 gap-1"
              >
                {visiblePlatforms.size === availablePlatforms.length ? (
                  <>
                    <EyeOff className="h-3 w-3" />
                    Ocultar Todos
                  </>
                ) : (
                  <>
                    <Eye className="h-3 w-3" />
                    Mostrar Todos
                  </>
                )}
              </Button>
            </div>
          )}
        </div>

        {/* Controles de Plataformas */}
        {showControls && availablePlatforms.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {availablePlatforms.map(platform => {
              const config = platformConfig[platform as keyof typeof platformConfig];
              const icon = platformIcons[platform as keyof typeof platformIcons];
              const isVisible = visiblePlatforms.has(platform);
              
              return (
                <Badge
                  key={platform}
                  variant={isVisible ? "default" : "outline"}
                  className={`cursor-pointer transition-all hover:scale-105 ${
                    isVisible 
                      ? 'shadow-sm border-2' 
                      : 'opacity-60 hover:opacity-80'
                  }`}
                  style={{
                    backgroundColor: isVisible ? config?.color : 'transparent',
                    borderColor: config?.color,
                    color: isVisible ? 'white' : config?.color
                  }}
                  onClick={() => togglePlatform(platform)}
                >
                  <span className="mr-1">{icon}</span>
                  {config?.label}
                  {isVisible && <Eye className="h-3 w-3 ml-1" />}
                </Badge>
              );
            })}
          </div>
        )}

        {/* Informações do gráfico */}
        <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
          <span>
            <Filter className="h-3 w-3 inline mr-1" />
            {visiblePlatforms.size} de {availablePlatforms.length} plataformas visíveis
          </span>
          <span>
            <BarChart3 className="h-3 w-3 inline mr-1" />
            {filteredChartData.length} faixas etárias
          </span>
        </div>
      </CardHeader>

      <CardContent>
        <div className="w-full">
          <ChartContainer
            config={platformConfig}
            className="w-full"
          >
            <ResponsiveContainer width="100%" height={height}>
              <BarChart
                data={filteredChartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                barCategoryGap="15%"
                barGap={2}
              >
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="ageRange" 
                  className="text-xs fill-muted-foreground"
                  tick={{ fontSize: 11 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis 
                  className="text-xs fill-muted-foreground"
                  tick={{ fontSize: 11 }}
                  label={{ 
                    value: 'Percentual (%)', 
                    angle: -90, 
                    position: 'insideLeft',
                    style: { textAnchor: 'middle' }
                  }}
                />
                <ChartTooltip 
                  content={<ChartTooltipContent />}
                  formatter={(value: any, name: any) => [
                    `${value}%`, 
                    platformConfig[name as keyof typeof platformConfig]?.label || name
                  ]}
                />
                <ChartLegend content={<ChartLegendContent />} />
                
                {/* Renderizar barras dinamicamente apenas para plataformas visíveis */}
                {availablePlatforms
                  .filter(platform => visiblePlatforms.has(platform))
                  .map(platform => {
                    const config = platformConfig[platform as keyof typeof platformConfig];
                    return (
                      <Bar
                        key={platform}
                        dataKey={platform}
                        fill={config?.color}
                        name={config?.label}
                        radius={[2, 2, 0, 0]}
                        animationDuration={300}
                      />
                    );
                  })}
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default AgeRangeBarChart;

