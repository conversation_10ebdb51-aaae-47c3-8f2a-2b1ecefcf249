# 🔒 Sistema de Filtragem por Roles - Dados Sensíveis

## 📋 Visão Geral

Implementamos um sistema robusto de filtragem de dados baseado em roles que **remove automaticamente informações sensíveis** quando usuários com role `USER` acessam propostas compartilhadas.

## 🎯 Problema Resolvido

**Antes**: Usuários com role `USER` podiam ver dados financeiros, preços, informações de contato e outros dados sensíveis ao acessar propostas compartilhadas.

**Agora**: Sistema filtra automaticamente dados sensíveis, mostrando apenas informações relevantes para cada tipo de usuário.

## 🔐 Matriz de Acesso por Role

### 👑 **ADMIN / MANAGER**
```typescript
✅ Dados Completos:
- Pricing completo (post, story, reel, video)
- Informações financeiras (totalAmount, budgets)
- <PERSON><PERSON> de contato (email, telefone, manager)
- Histórico de negociações
- Termos de pagamento
- Dados fiscais e tributários
```

### 👤 **USER** 
```typescript
✅ Dados Básicos Permitidos:
- Nome e avatar do influenciador
- Seguidores e engajamento
- Tier e categoria
- Localização (cidade)
- Redes sociais (sem pricing)
- Portfolio (sem valores)
- Estatísticas básicas

❌ Dados Removidos:
- Pricing (valores de post, story, reel)
- Informações financeiras
- Dados de contato pessoal
- Histórico de negociações
- Total da proposta
- Termos de pagamento
- Informações fiscais
```

## 🛠️ Implementação Técnica

### **1. Tipos TypeScript**
```typescript
// types/proposal-snapshot.ts

// Para usuários USER - dados básicos
interface InfluencerBasicSnapshot {
  id: string;
  name: string;
  avatar?: string;
  followers?: number;
  engagement?: number;
  // SEM pricing, financials, contactInfo
}

// Para admins/managers - dados completos
interface InfluencerCompleteSnapshot extends InfluencerBasicSnapshot {
  pricing: { post: number; story: number; reel: number };
  financials: { totalEarnings: number; paymentTerms: string };
  contactInfo: { email: string; phone?: string };
}
```

### **2. Serviço de Filtragem**
```typescript
// lib/snapshot-filter-service.ts

export class SnapshotFilterService {
  static filterSnapshotByRole(snapshot: any, userRole: string) {
    if (userRole === 'user') {
      return this.createBasicSnapshot(snapshot); // Dados filtrados
    }
    return this.createCompleteSnapshot(snapshot); // Dados completos
  }
  
  static getFilteredFieldsForRole(userRole: string): string[] {
    if (userRole === 'user') {
      return ['pricing', 'financials', 'contactInfo', 'totalAmount'];
    }
    return [];
  }
}
```

### **3. APIs com Filtragem Automática**

#### **GET /api/proposals/[id]**
```typescript
// Proposta individual
if (user.role === 'user') {
  const filteredProposal = {
    id: proposal.id,
    nome: proposal.nome,
    // ... dados básicos
    influencers: proposal.influencers?.map(inf => ({
      id: inf.id,
      name: inf.name,
      followers: inf.followers
      // SEM email, pricing, etc.
    }))
  };
  return { data: filteredProposal, accessLevel: 'basic' };
}
```

#### **GET /api/proposals**
```typescript
// Lista de propostas
if (user.role === 'user') {
  const filteredProposals = proposals.map(proposal => ({
    // Dados básicos sem valores financeiros
    influencerCount: proposal.influencers?.length || 0,
    serviceCount: proposal.services?.length || 0
    // SEM totalAmount, budgets, etc.
  }));
  return { data: filteredProposals, accessLevel: 'basic' };
}
```

## 🔄 Fluxo de Funcionamento

```mermaid
graph TD
    A[📥 Request API] --> B{🔐 Token válido?}
    B -->|❌| C[401 Unauthorized]
    B -->|✅| D[🔍 Extrair role do token]
    
    D --> E{👤 Role = 'user'?}
    E -->|❌| F[👑 Retornar dados completos]
    E -->|✅| G[🔒 Aplicar filtro]
    
    G --> H[❌ Remover pricing]
    G --> I[❌ Remover financials]
    G --> J[❌ Remover contactInfo]
    G --> K[✅ Manter dados básicos]
    
    H --> L[📤 Response filtrada]
    I --> L
    J --> L
    K --> L
    
    F --> M[📤 Response completa]
```

## 📊 Exemplo Prático

### **Request**: `GET /api/proposals/123`

#### **Resposta para ADMIN**:
```json
{
  "success": true,
  "data": {
    "id": "123",
    "nome": "Campanha Verão 2024",
    "totalAmount": 15000,
    "influencers": [{
      "id": "inf1",
      "name": "João Silva",
      "email": "<EMAIL>",
      "phone": "+5511999999999",
      "pricing": {
        "post": 2500,
        "story": 800,
        "reel": 3000
      }
    }]
  },
  "metadata": {
    "accessLevel": "complete",
    "userRole": "admin",
    "filteredFields": []
  }
}
```

#### **Resposta para USER**:
```json
{
  "success": true,
  "data": {
    "id": "123",
    "nome": "Campanha Verão 2024",
    "influencers": [{
      "id": "inf1",
      "name": "João Silva",
      "followers": 150000,
      "engagement": 4.5,
      "tier": "mid"
    }]
  },
  "metadata": {
    "accessLevel": "basic",
    "userRole": "user",
    "filteredFields": ["pricing", "financials", "contactInfo", "totalAmount"]
  }
}
```

## 🚀 Benefícios

### **🔒 Segurança**
- Dados sensíveis nunca chegam ao cliente USER
- Proteção automática contra vazamentos
- Conformidade com políticas de privacidade

### **🎯 Flexibilidade** 
- Sistema de roles expansível
- Configuração granular por campo
- Fácil adição de novos níveis de acesso

### **📊 Transparência**
- Metadata indica que dados foram filtrados
- Logs de auditoria para tracking
- Verificação fácil do nível de acesso

### **⚡ Performance**
- Filtragem no servidor (não cliente)
- Redução do payload para users
- Caching otimizado por role

## 🔧 Configuração e Manutenção

### **Adicionar Novo Campo Sensível**
```typescript
// Em SnapshotFilterService.getFilteredFieldsForRole()
if (userRole === 'user') {
  return [
    'pricing',
    'financials', 
    'novoFieldSensivel' // ✅ Adicionar aqui
  ];
}
```

### **Criar Novo Nível de Acesso**
```typescript
// Exemplo: role 'viewer' com acesso limitado
if (userRole === 'viewer') {
  return ['pricing', 'contactInfo']; // Oculta só pricing e contato
}
```

### **Auditoria e Logs**
```typescript
console.log('🔒 Dados filtrados:', {
  userRole: user.role,
  proposalId: proposal.id,
  filteredFields: getFilteredFieldsForRole(user.role),
  timestamp: new Date()
});
```

## 📈 Próximos Passos

1. **Implementar cache por role** para otimização
2. **Adicionar logs de auditoria** detalhados
3. **Criar interface de configuração** de permissões
4. **Implementar expiração** de snapshots compartilhados
5. **Adicionar validação** de integridade de dados

---

**✅ Sistema implementado e funcionando!** Usuários com role `USER` agora têm acesso seguro e controlado a propostas compartilhadas, sem exposição de dados sensíveis. 