"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  ColumnOrderState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  type DragEndEvent,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers"
import {
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronsLeft,
  ChevronsRight,
  ChevronsUpDown,
  Filter,
  Search,
  Settings2,
  X,
  GripVertical,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { cn } from "@/lib/utils"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  searchKey?: string
  searchPlaceholder?: string
  enableRowSelection?: boolean
  enableColumnVisibility?: boolean
  enablePagination?: boolean
  enableSorting?: boolean
  enableFiltering?: boolean
  enableColumnOrdering?: boolean
  pageSize?: number
  className?: string
  onRowSelectionChange?: (selectedRows: TData[]) => void
  onRowClick?: (row: TData) => void
  expandedRows?: string[]
  renderExpandedRow?: (row: TData) => React.ReactNode
  renderExpandButton?: (row: TData) => React.ReactNode
  selectionMode?: boolean
  selectedRows?: string[]
  onRowSelect?: (id: string) => void
  columnOrder?: ColumnOrderState
  onColumnOrderChange?: (columnOrder: ColumnOrderState) => void
  toolbarActions?: React.ReactNode
}

// Componente para cabeçalho draggable
function DraggableTableHead({
  header,
  enableSorting,
  enableColumnOrdering = false,
}: {
  header: any
  enableSorting: boolean
  enableColumnOrdering?: boolean
}) {
  const { attributes, isDragging, listeners, setNodeRef, transform } =
    useSortable({
      id: header.column.id,
    })

  const style = {
    opacity: isDragging ? 0.8 : 1,
    transform: CSS.Translate.toString(transform),
    transition: "transform 150ms ease",
    zIndex: isDragging ? 1000 : 1,
    cursor: isDragging ? 'grabbing' : 'grab',
    height: '100%',
    display: 'flex',
    alignItems: 'center'
  }

  // Define a base width for each header type - mesmas larguras das células
  let headerWidth = "auto";
  if (header.column.id === 'select') {
    headerWidth = "40px";
  } else if (header.column.id === 'avatar') {
    headerWidth = "50px"; 
  } else if (header.column.id === 'actions' || header.column.id === 'acoes') {
    headerWidth = "120px";
  } else if (header.index === 0) { // For the name/first meaningful column
    headerWidth = "180px";
  } else {
    // For all other columns
    headerWidth = "120px";
  }

  // Determine se é uma célula de ações para aplicar estilo específico
  const isActionHeader = header.column.id === 'actions' || header.column.id === 'acoes';
  const isTextHeader = header.column.id === 'nome' || header.column.id === 'email' || header.column.id === 'nomeInfluencer' || header.column.id === 'influenciador' || header.column.id === 'name';

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "px-1 sm:px-2 lg:px-4 text-right align-right font-medium text-muted-foreground group text-xs lg:text-sm hover:bg-muted/20 transition-colors overflow-hidden flex items-center",
        header.column.getCanSort() && "cursor-pointer select-none hover:bg-muted/30",
        isTextHeader ? "justify-start" : "justify-end"
      )}
    >
      <div className={cn(
        "flex items-center min-w-0 w-full",
        isTextHeader ? "justify-start" : "justify-end"
      )}>
        {enableColumnOrdering && header.id !== 'select' && (
          <button
            className="flex h-4 w-4 items-center justify-center opacity-0 group-hover:opacity-50 hover:!opacity-100 mr-2 flex-shrink-0 transition-opacity cursor-grab active:cursor-grabbing"
            {...attributes}
            {...listeners}
          >
            <GripVertical className="h-3 w-3" />
          </button>
        )}
        <div 
          className={cn(
            "flex-1 min-w-0 flex items-center",
            isTextHeader ? "justify-start" : "justify-end"
          )}
          onClick={enableSorting ? header.column.getToggleSortingHandler() : undefined}
        >
          {enableSorting && header.column.getCanSort() && (
            <span className="mr-2 flex-shrink-0">
              {{
                asc: <ChevronUp className="h-4 w-4 text-black dark:text-white" />,
                desc: <ChevronDown className="h-4 w-4 text-black dark:text-white" />,
              }[header.column.getIsSorted() as string] ?? <ChevronsUpDown className="h-4 w-4 text-gray-600 dark:text-gray-400" />}
            </span>
          )}
          <span className="truncate">
            {header.isPlaceholder
              ? null
              : flexRender(header.column.columnDef.header, header.getContext())}
          </span>
        </div>
      </div>
    </div>
  )
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchKey,
  searchPlaceholder = "Search...",
  enableRowSelection = false,
  enableColumnVisibility = true,
  enablePagination = true,
  enableSorting = true,
  enableFiltering = true,
  enableColumnOrdering = false,
  pageSize = 10,
  className,
  onRowSelectionChange,
  onRowClick,
  expandedRows = [],
  renderExpandedRow,
  renderExpandButton,
  selectionMode = false,
  selectedRows = [],
  onRowSelect,
  columnOrder,
  onColumnOrderChange,
  toolbarActions,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [globalFilter, setGlobalFilter] = React.useState("")
  const [internalColumnOrder, setInternalColumnOrder] = React.useState<ColumnOrderState>([])

  // Ref para o container da tabela
  const tableContainerRef = React.useRef<HTMLDivElement>(null)
  
  // Estes hooks sempre devem ser chamados, independentemente de condições
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 5,
      },
    }),
    useSensor(KeyboardSensor)
  )

  // Modificador customizado para restringir ao container da tabela
  const restrictToTableContainer = React.useCallback(({ draggingNodeRect, transform }: any) => {
    if (!tableContainerRef.current || !draggingNodeRect) {
      return transform
    }

    const containerRect = tableContainerRef.current.getBoundingClientRect()
    
    return {
      ...transform,
      x: Math.max(
        containerRect.left - draggingNodeRect.left,
        Math.min(
          containerRect.right - draggingNodeRect.right,
          transform.x
        )
      ),
    }
  }, [])

  // Handler para drag end
  function handleDragEnd(event: DragEndEvent) {
    // Restaurar overflow do body
    document.body.style.overflow = '';
    
    const { active, over } = event

    if (active && over && active.id !== over.id) {
      // ✨ COLUNAS SEMPRE FIXAS (não podem ser movidas)
      const fixedColumns = ['select'];
      
      // Não permitir mover colunas fixas
      if (fixedColumns.includes(active.id as string)) {
        return;
      }
      
      // Não permitir mover outras colunas para posições de colunas fixas
      if (fixedColumns.includes(over.id as string)) {
        return;
      }

      const oldIndex = currentColumnOrder.indexOf(active.id as string)
      const newIndex = currentColumnOrder.indexOf(over.id as string)
      
      if (oldIndex !== -1 && newIndex !== -1) {
        const newOrder = [...currentColumnOrder]
        newOrder.splice(oldIndex, 1)
        newOrder.splice(newIndex, 0, active.id as string)
        
        // ✨ GARANTIR que 'select' sempre fique no início
        const finalOrder = newOrder.filter(col => col !== 'select');
        const orderedWithSelect = ['select', ...finalOrder];
        
        if (onColumnOrderChange) {
          onColumnOrderChange(orderedWithSelect)
        } else {
          setInternalColumnOrder(orderedWithSelect)
        }
      }
    }
  }

  // Add row selection column if enabled
  const tableColumns = React.useMemo(() => {
    if (!enableRowSelection) return columns

    const selectionColumn: ColumnDef<TData, TValue> = {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40,
    }

        return [selectionColumn, ...columns]
  }, [columns, enableRowSelection])

  // Usar columnOrder do props ou estado interno, com fallback para tableColumns
  const currentColumnOrder = React.useMemo(() => {
    let finalOrder: string[] = [];
    
    if (columnOrder && columnOrder.length > 0) {
      finalOrder = columnOrder;
    } else if (internalColumnOrder.length > 0) {
      finalOrder = internalColumnOrder;
    } else {
      // Fallback para ordem padrão baseada nas colunas
      finalOrder = tableColumns.map((col) => col.id ?? '').filter(id => id !== '');
    }
    
    // ✨ SEMPRE garantir que 'select' seja a primeira coluna
    const filteredOrder = finalOrder.filter(col => col !== 'select');
    return enableRowSelection ? ['select', ...filteredOrder] : filteredOrder;
  }, [columnOrder, internalColumnOrder, tableColumns, enableRowSelection])

  

  const table = useReactTable({
    data,
    columns: tableColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    getFilteredRowModel: enableFiltering ? getFilteredRowModel() : undefined,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
      columnOrder: currentColumnOrder,
    },
    onColumnOrderChange: (updaterOrValue) => {
      const newOrder = typeof updaterOrValue === 'function' 
        ? updaterOrValue(currentColumnOrder)
        : updaterOrValue
      
      if (onColumnOrderChange) {
        onColumnOrderChange(newOrder)
      } else {
        setInternalColumnOrder(newOrder)
      }
    },
    initialState: {
      pagination: {
        pageSize,
      },
    },
  })

  // Notify parent component of row selection changes
  React.useEffect(() => {
    if (onRowSelectionChange && enableRowSelection) {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map((row) => row.original)
      onRowSelectionChange(selectedRows)
    }
  }, [rowSelection, onRowSelectionChange, enableRowSelection, table])

  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length

  return (
    <div className={cn("h-full flex flex-col rounded-md dark:bg-[#08050f] dark:border-[#1c1627] border-border w-full max-w-full relative", className)} style={{ height: '100%' }}>
      {/* Toolbar */}
      <div className="flex items-center justify-between flex-shrink-0 mb-4">
        <div className="flex flex-1 items-center space-x-2">
          {/* Clear filters */}
          {enableFiltering && (columnFilters.length > 0) && (
            <Button
              variant="ghost"
              onClick={() => {
                setColumnFilters([])
              }}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Toolbar Actions */}
        {toolbarActions && (
          <div className="flex items-center gap-2 ml-4">
            {toolbarActions}
          </div>
        )}
      </div>

      {/* Selected rows indicator */}
      {enableRowSelection && selectedRowsCount > 0 && (
        <div className="flex items-center justify-between bg-muted/30 rounded-md border border-border px-4 py-2 flex-shrink-0 mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">
              {selectedRowsCount} of {table.getFilteredRowModel().rows.length} row(s) selected
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setRowSelection({})}
          >
            Clear selection
          </Button>
        </div>
      )}

      {/* Tabela */}
      <div 
        ref={tableContainerRef}
        className="flex flex-col"
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      >
        <div 
          className="overflow-auto flex-grow"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: 'var(--scrollbar-thumb) var(--scrollbar-track)',
            WebkitOverflowScrolling: 'touch'
          }}
        >
          {/* 🎯 WRAPPING COM DNDCONTEXT PARA HABILITAR DRAG AND DROP */}
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToHorizontalAxis]}
          >
            <table className="w-full min-w-max border-collapse rounded-md relative [&_tr]:!h-[48px] [&_td]:!h-[48px] [&_td]:!py-0 [&_td]:!align-right">
              <thead className="sticky top-0 bg-background border  dark:border-[#1c1627] dark:bg-[#08050f] z-10">
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id} className="border-b dark:border-[#1c1627]">
                    {/* 🔥 CONTEXT SORTABLE PARA AS COLUNAS */}
                    <SortableContext items={currentColumnOrder} strategy={horizontalListSortingStrategy}>
                      {headerGroup.headers.map((header) => {
                        const isInfluenciadorHeader = header.column.id === 'nome' || header.column.id === 'influenciador' || header.column.id === 'nomeInfluencer' || header.column.id === 'name';
                        const isActionHeader = header.column.id === 'actions' || header.column.id === 'acoes';
                        
                        // Definir largura da coluna
                        let colWidth = "auto";
                        if (header.column.id === 'select') colWidth = "40px";
                        if (header.column.id === 'avatar') colWidth = "50px";
                        if (isActionHeader) colWidth = "120px";
                        
                        // Determinar se a coluna é fixa
                        const isFixed = header.column.id === 'select' || isInfluenciadorHeader;
                        
                        return (
                          <th 
                            key={header.id} 
                            className={cn(
                              "h-12 px-1 font-medium text-muted-foreground text-sm relative",
                              isInfluenciadorHeader ? "text-left" : "text-right",
                              isFixed && "sticky left-0 z-20"
                            )}
                            style={{ 
                              width: colWidth,
                              backgroundColor: isFixed ? 'var(--background)' : undefined,
                              boxShadow: isFixed ? '2px 0 4px rgba(0,0,0,0.1)' : undefined
                            }}
                          >
                            {/* 🎯 USAR COMPONENTE DRAGGABLE PARA COLUNAS QUE PODEM SER ARRASTADAS */}
                            {enableColumnOrdering && header.column.id !== 'select' ? (
                              <DraggableTableHead 
                                header={header}
                                enableSorting={enableSorting}
                                enableColumnOrdering={enableColumnOrdering}
                              />
                            ) : (
                              // Para coluna select ou quando drag está desabilitado
                              <div 
                                className={cn(
                                  "px-3 text-right align-right font-medium text-muted-foreground group text-xs lg:text-sm hover:bg-muted/20 transition-colors overflow-hidden flex items-center h-full",
                                  header.column.getCanSort() && "cursor-pointer select-none hover:bg-muted/30",
                                  isInfluenciadorHeader ? "justify-start" : "justify-end"
                                )}
                                onClick={enableSorting && header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined}
                              >
                                <div className={cn(
                                  "flex items-center min-w-0 w-full",
                                  isInfluenciadorHeader ? "justify-start" : "justify-end"
                                )}>
                                  <div 
                                    className={cn(
                                      "flex-1 min-w-0 flex items-center",
                                      isInfluenciadorHeader ? "justify-start" : "justify-end"
                                    )}
                                  >
                                    {enableSorting && header.column.getCanSort() && (
                                      <span className="mr-2 flex-shrink-0">
                                        {{
                                          asc: <ChevronUp className="h-4 w-4 text-black dark:text-white" />,
                                          desc: <ChevronDown className="h-4 w-4 text-black dark:text-white" />,
                                        }[header.column.getIsSorted() as string] ?? <ChevronsUpDown className="h-4 w-4 text-gray-600 dark:text-gray-400" />}
                                      </span>
                                    )}
                                    <span className="truncate">
                                      {header.isPlaceholder
                                        ? null
                                        : flexRender(header.column.columnDef.header, header.getContext())}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </th>
                        );
                      })}
                    </SortableContext>
                  </tr>
                ))}
              </thead>
            <tbody className="h-full flex-1">
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <React.Fragment key={row.id}>
                    <tr 
                      data-state={row.getIsSelected() && "selected"}
                      className="border border-1 transition-colors dark:border-[#1c1627] hover:bg-muted/50 data-[state=selected]:bg-muted rounded-md bg-background dark:bg-[#08050f] mb-2"
                      onClick={() => onRowClick?.(row.original)}
                      style={{ 
                        height: '48px', 
                        minHeight: '48px', 
                        maxHeight: '48px',
                        lineHeight: '48px'
                      }}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isInfluenciadorCell = cell.column.id === 'nome' || cell.column.id === 'influenciador';
                        const isActionCell = cell.column.id === 'actions' || cell.column.id === 'acoes';
                        
                        // Determinar se a célula é fixa
                        const isFixed = cell.column.id === 'select' || isInfluenciadorCell;
                        
                        return (
                          <td
                            key={cell.id}
                            className={cn(
                              "px-4 align-right text-right",
                              isInfluenciadorCell ? "text-left" : "text-right",
                              isFixed && "sticky left-0 z-20"
                            )}
                            style={{ 
                              backgroundColor: isFixed ? 'var(--background)' : undefined,
                              boxShadow: isFixed ? '2px 0 4px rgba(0,0,0,0.1)' : undefined,
                              height: '48px',
                              minHeight: '48px',
                              maxHeight: '48px',
                              padding: '0 16px',
                              verticalAlign: 'middle',
                              lineHeight: '48px'
                            }}
                          >
                            {cell.column.id === 'select' ? (
                              // Célula de seleção
                              <div className="flex justify-center w-full">
                                {selectionMode ? (
                                  <Checkbox
                                    checked={selectedRows.includes((row.original as any).id)}
                                    onCheckedChange={() => onRowSelect?.((row.original as any).id)}
                                    className="h-4 w-4"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                ) : flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </div>
                            ) : cell.column.id === 'avatar' ? (
                              // Célula de avatar
                              <div className="flex justify-center w-full">
                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </div>
                            ) : (
                              // Todas as outras células
                              <div className="w-full break-words text-sm">
                                {renderExpandButton && isInfluenciadorCell && (
                                  <span className="mr-2 inline-flex">
                                    {renderExpandButton(row.original)}
                                  </span>
                                )}
                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                    
                    {expandedRows?.includes((row.original as any).id) && renderExpandedRow && (
                      <tr>
                        <td colSpan={row.getVisibleCells().length} className="p-0">
                          <AnimatePresence>
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.2 }}
                              className="w-full"
                            >
                              <div className="p-0 w-full">
                                {renderExpandedRow(row.original)}
                              </div>
                            </motion.div>
                          </AnimatePresence>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td 
                    colSpan={table.getAllColumns().length} 
                    className="h-24 px-4 text-center whitespace-nowrap"
                  >
                    No results.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          </DndContext>
          
        </div>

         {/* Pagination */}
      {enablePagination && (
          <div className="flex items-center justify-between px-2 bg-background dark:bg-[#08050f] py-2 border dark:border-[#1c1627] flex-shrink-0 sticky bottom-0 left-0 right-0 rounded-lg">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">
                Linhas por página
              </p>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => {
                  table.setPageSize(Number(value))
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={table.getState().pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-6 lg:space-x-8">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">
                  Página {table.getState().pagination.pageIndex + 1} de{" "}
                  {table.getPageCount()}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  className="hidden h-8 w-8 p-0 lg:flex"
                  onClick={() => table.setPageIndex(0)}
                  disabled={!table.getCanPreviousPage()}
                >
                  <span className="sr-only">Go to first page</span>
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  <span className="sr-only">Go to previous page</span>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="h-8 w-8 p-0"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  <span className="sr-only">Go to next page</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="hidden h-8 w-8 p-0 lg:flex"
                  onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                  disabled={!table.getCanNextPage()}
                >
                  <span className="sr-only">Go to last page</span>
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
     
    
    
    </div>
  )
}

// Helper function to create column definitions
export function createSelectColumn<TData>(): ColumnDef<TData> {
  return {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 40,
  }
}

// Helper function to create sortable column
export function createSortableColumn<TData, TValue>(
  accessorKey: string,
  header: string,
  cell?: (value: TValue) => React.ReactNode
): ColumnDef<TData, TValue> {
  return {
    accessorKey,
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium hover:bg-transparent"
        >
          {header}
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: cell ? ({ getValue }) => cell(getValue()) : undefined,
  }
}

// Helper function to create action column
export function createActionColumn<TData>(
  actions: (row: TData) => React.ReactNode
): ColumnDef<TData> {
  return {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => actions(row.original),
    enableSorting: false,
    enableHiding: false,
    size: 100,
  }
}

export type { DataTableProps }

