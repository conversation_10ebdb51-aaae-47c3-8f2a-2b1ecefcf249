import React from 'react'
import { Control, useFormContext } from 'react-hook-form'
import { User, MapPin } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { AvatarUpload } from './avatar-upload'
import { LocationManager } from './location-manager'
import type { InfluencerFormData } from '@/types/influencer-form'

interface PersonalInfoSectionProps {
  control: Control<InfluencerFormData>
}

export function PersonalInfoSection({ control }: PersonalInfoSectionProps) {
  const { register, watch, setValue, formState: { errors } } = useFormContext<InfluencerFormData>()
  
  const name = watch('personalInfo.name')
  const avatar = watch('personalInfo.avatar')
  const verified = watch('personalInfo.verified')

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
          <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Informações Pessoais</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Dados básicos do influenciador
          </p>
        </div>
      </div>

      {/* Avatar e Nome */}
      <div className="flex flex-col md:flex-row gap-6">
        <div className="flex flex-col items-center space-y-3">
          <Avatar className="h-24 w-24 border-2 border-gray-200 dark:border-gray-700">
            <AvatarImage src={avatar} alt={name || "Avatar"} />
            <AvatarFallback className="text-lg">
              {name ? name.split(' ').map(n => n[0]).join('').slice(0, 2) : 'IN'}
            </AvatarFallback>
          </Avatar>
          
          <AvatarUpload 
            currentAvatar={avatar}
            onAvatarChange={(newAvatar) => setValue('personalInfo.avatar', newAvatar)}
          />
        </div>

        <div className="flex-1 space-y-4">
          {/* Nome */}
          <div className="space-y-2">
            <Label htmlFor="name">
              Nome Completo <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              {...register('personalInfo.name')}
              placeholder="Digite o nome completo"
              className={errors.personalInfo?.name ? 'border-red-500' : ''}
            />
            {errors.personalInfo?.name && (
              <p className="text-sm text-red-500">{errors.personalInfo.name.message}</p>
            )}
          </div>

          {/* Dados básicos */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="age">Idade</Label>
              <Input
                id="age"
                type="number"
                min="13"
                max="100"
                {...register('personalInfo.age', { valueAsNumber: true })}
                placeholder="Ex: 25"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="gender">Gênero</Label>
              <Select
                value={watch('personalInfo.gender')}
                onValueChange={(value) => setValue('personalInfo.gender', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Feminino">Feminino</SelectItem>
                  <SelectItem value="Masculino">Masculino</SelectItem>
                  <SelectItem value="Outro">Outro</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2 pt-7">
              <Switch
                id="verified"
                checked={verified}
                onCheckedChange={(checked) => setValue('personalInfo.verified', checked)}
              />
              <Label htmlFor="verified" className="text-sm font-medium">
                Influenciador Exclusivo
              </Label>
            </div>
          </div>
        </div>
      </div>

      {/* Localização */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-gray-600 dark:text-gray-400" />
          <Label className="text-base font-medium">Localização</Label>
        </div>
        
        <LocationManager
          control={control}
          onLocationChange={(location) => {
            setValue('location.city', location.city)
            setValue('location.state', location.state)
            setValue('location.country', location.country)
          }}
        />
      </div>

      {/* Biografia */}
      <div className="space-y-2">
        <Label htmlFor="bio">Biografia / Observações</Label>
        <Textarea
          id="bio"
          {...register('personalInfo.bio')}
          placeholder="Descreva o influenciador, especialidades, experiências..."
          className="min-h-[100px] resize-none"
          maxLength={500}
        />
        <div className="text-xs text-gray-500 text-right">
          {watch('personalInfo.bio')?.length || 0}/500 caracteres
        </div>
      </div>
    </div>
  )
} 

