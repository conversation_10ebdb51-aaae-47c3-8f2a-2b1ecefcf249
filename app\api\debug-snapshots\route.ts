import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const proposalId = searchParams.get('proposalId');
    const shareToken = searchParams.get('shareToken');
    
    if (!proposalId) {
      return NextResponse.json({
        success: false,
        error: 'proposalId é obrigatório'
      }, { status: 400 });
    }

    // MODO DEBUG: Pular autenticação para diagnóstico
    console.log(`🔍 [DEBUG] MODO DEBUG ATIVADO - Pulando autenticação`);
    console.log(`🔍 [DEBUG] Propostas a diagnosticar: ${proposalId}`);

    console.log(`🔍 [DEBUG] Iniciando diagnóstico para proposalId: ${proposalId}`);

    const diagnosticResult: any = {
      proposalId,
      timestamp: new Date().toISOString(),
      steps: [],
      summary: {}
    };

    // STEP 1: Verificar se existem proposal_sharings
    console.log(`🔍 [DEBUG] STEP 1: Verificando proposal_sharings...`);
    const proposalSharingsQuery = db.collection('proposal_sharings')
      .where('proposalId', '==', proposalId);
    
    const sharingsSnapshot = await proposalSharingsQuery.get();
    
    diagnosticResult.steps.push({
      step: 1,
      description: 'Verificar proposal_sharings',
      found: sharingsSnapshot.size,
      isEmpty: sharingsSnapshot.empty,
      details: sharingsSnapshot.docs.map(doc => ({
        shareToken: doc.id,
        data: doc.data()
      }))
    });

    if (sharingsSnapshot.empty) {
      diagnosticResult.summary.issue = 'NENHUM_PROPOSAL_SHARING_ENCONTRADO';
      diagnosticResult.summary.solution = 'Os snapshots precisam ser criados primeiro via API de sharing';
      return NextResponse.json(diagnosticResult);
    }

    // STEP 2: Para cada sharing, verificar snapshots
    console.log(`🔍 [DEBUG] STEP 2: Verificando snapshots em cada sharing...`);
    
    let totalSnapshots = 0;
    const detailedSnapshots: any[] = [];

    for (const sharingDoc of sharingsSnapshot.docs) {
      const shareTokenFound = sharingDoc.id;
      const sharingData = sharingDoc.data();
      
      console.log(`🔍 [DEBUG] Verificando snapshots para token: ${shareTokenFound}`);
      
      // Buscar snapshots deste compartilhamento
      const snapshotsCollection = sharingDoc.ref.collection('snapshots');
      const snapshotsSnapshot = await snapshotsCollection.get();
      
      totalSnapshots += snapshotsSnapshot.size;
      
      for (const snapshotDoc of snapshotsSnapshot.docs) {
        const influencerId = snapshotDoc.id;
        const snapshotData = snapshotDoc.data();
        
        console.log(`🔍 [DEBUG] Analisando snapshot: ${influencerId}`);
        
        // STEP 3: Verificar subcoleções
        const pricingCollection = snapshotDoc.ref.collection('pricing');
        const pricingSnapshot = await pricingCollection.get();
        
        const budgetsCollection = snapshotDoc.ref.collection('budgets');
        const budgetsSnapshot = await budgetsCollection.get();
        
        const demographicsCollection = snapshotDoc.ref.collection('demographics');
        const demographicsSnapshot = await demographicsCollection.get();
        
        const snapshotDetail = {
          influencerId,
          shareToken: shareTokenFound,
          mainData: {
            hasData: !!snapshotData,
            keys: Object.keys(snapshotData || {}),
            name: snapshotData?.name,
            originalInfluencerId: snapshotData?.originalInfluencerId,
            capturedAt: snapshotData?.capturedAt
          },
          subcollections: {
            pricing: {
              count: pricingSnapshot.size,
              docs: pricingSnapshot.docs.map(doc => ({ id: doc.id, data: doc.data() }))
            },
            budgets: {
              count: budgetsSnapshot.size,
              docs: budgetsSnapshot.docs.map(doc => ({ id: doc.id, data: doc.data() }))
            },
            demographics: {
              count: demographicsSnapshot.size,
              docs: demographicsSnapshot.docs.map(doc => ({ id: doc.id, data: doc.data() }))
            }
          }
        };
        
        detailedSnapshots.push(snapshotDetail);
      }
    }

    diagnosticResult.steps.push({
      step: 2,
      description: 'Verificar snapshots em sharings',
      totalSnapshots,
      detailedSnapshots
    });

    // STEP 4: Se um shareToken específico foi fornecido, fazer análise profunda
    if (shareToken) {
      console.log(`🔍 [DEBUG] STEP 4: Análise profunda do token: ${shareToken}`);
      
      const specificSharingRef = db.collection('proposal_sharings').doc(shareToken);
      const specificSharingDoc = await specificSharingRef.get();
      
      if (specificSharingDoc.exists()) {
        const specificSnapshotsCollection = specificSharingRef.collection('snapshots');
        const specificSnapshotsSnapshot = await specificSnapshotsCollection.get();
        
        diagnosticResult.steps.push({
          step: 4,
          description: `Análise profunda do token ${shareToken}`,
          sharingExists: true,
          sharingData: specificSharingDoc.data(),
          snapshotsCount: specificSnapshotsSnapshot.size,
          snapshotsList: specificSnapshotsSnapshot.docs.map(doc => ({
            id: doc.id,
            dataSize: JSON.stringify(doc.data()).length,
            keys: Object.keys(doc.data()),
            hasName: !!doc.data().name
          }))
        });
      } else {
        diagnosticResult.steps.push({
          step: 4,
          description: `Token ${shareToken} não encontrado`,
          sharingExists: false
        });
      }
    }

    // STEP 5: Verificar se existem influenciadores na proposta original
    console.log(`🔍 [DEBUG] STEP 5: Verificando influenciadores na proposta original...`);
    
    const proposalRef = db.collection('proposals').doc(proposalId);
    const proposalDoc = await proposalRef.get();
    
    let originalInfluencers: any[] = [];
    if (proposalDoc.exists()) {
      const proposalData = proposalDoc.data();
      originalInfluencers = proposalData?.influencers || [];
      
      // Verificar subcoleção de influenciadores
      const influencersSubcollection = proposalRef.collection('influencers');
      const influencersSnapshot = await influencersSubcollection.get();
      
      diagnosticResult.steps.push({
        step: 5,
        description: 'Verificar influenciadores na proposta original',
        proposalExists: true,
        influencersInMainDoc: originalInfluencers.length,
        influencersInSubcollection: influencersSnapshot.size,
        proposalData: {
          nome: proposalData?.nome,
          hasSnapshots: proposalData?.hasSnapshots,
          snapshotCreatedAt: proposalData?.snapshotCreatedAt
        }
      });
    } else {
      diagnosticResult.steps.push({
        step: 5,
        description: 'Proposta original não encontrada',
        proposalExists: false
      });
    }

    // SUMMARY
    diagnosticResult.summary = {
      totalSharings: sharingsSnapshot.size,
      totalSnapshots,
      hasSnapshots: totalSnapshots > 0,
      originalInfluencers: originalInfluencers.length,
      analysis: {
        sharingsFound: sharingsSnapshot.size > 0,
        snapshotsFound: totalSnapshots > 0,
        subcollectionsHaveData: detailedSnapshots.some(s => 
          s.subcollections.demographics.count > 0 || 
          s.subcollections.budgets.count > 0
        )
      }
    };

    // Determinar possível causa do problema
    if (sharingsSnapshot.size === 0) {
      diagnosticResult.summary.issue = 'NO_SHARINGS_FOUND';
      diagnosticResult.summary.solution = 'Criar snapshots via POST /api/proposals/{id}/sharing';
    } else if (totalSnapshots === 0) {
      diagnosticResult.summary.issue = 'NO_SNAPSHOTS_IN_SHARINGS';
      diagnosticResult.summary.solution = 'Verificar se o processo de captura de snapshots está funcionando';
    } else if (!detailedSnapshots.some(s => s.subcollections.demographics.count > 0)) {
      diagnosticResult.summary.issue = 'NO_DEMOGRAPHICS_IN_SNAPSHOTS';
      diagnosticResult.summary.solution = 'Verificar se os influenciadores originais têm dados demográficos';
    } else {
      diagnosticResult.summary.issue = 'UNKNOWN';
      diagnosticResult.summary.solution = 'Analisar os detalhes dos steps acima';
    }

    return NextResponse.json(diagnosticResult);

  } catch (error) {
    console.error('❌ [DEBUG] Erro no diagnóstico:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
} 

