import { NextResponse } from 'next/server';
import { getAllInfluencers } from '@/lib/firebase';

// Interface para representar um influenciador com tipagem correta
interface Influencer {
  id: string;
  instagram?: string;
  youtube?: string;
  tiktok?: string;
  instagram_views?: number;
  youtube_views?: number;
  tiktok_views?: number;
  [key: string]: any;
}

export async function GET() {
  try {
    // Buscar todos os influenciadores diretamente do Firebase
    const influencers = await getAllInfluencers() as Influencer[];
    
    // Contagem total de influenciadores
    const totalInfluencers = influencers.length;
    
    // Calcular visualizações totais e seguidores
    let totalViews = 0;
    let totalFollowers = 0;
    
    // Função para converter números formatados como string (ex: "200.000") para number
    const parseFormattedNumber = (value: string | number): number => {
      if (typeof value === 'number') return value;
      if (!value) return 0;
      
      // Remove todos os caracteres não numéricos exceto ponto e vírgula
      const cleaned = value.toString().trim().replace(/[^0-9.,]/g, '');
      
      // Converte para número removendo separadores de milhar
      const normalized = cleaned.replace(/\./g, '').replace(',', '.');
      const num = parseFloat(normalized);
      return isNaN(num) ? 0 : num;
    };

    // Função para converter strings como "1.5M" ou "500K" ou "2.000" para números
    const parseFollowerCount = (followerString: string): number => {
      if (!followerString) return 0;
      
      // Remover espaços e caracteres não numéricos exceto pontos, vírgulas e letras K, M, B
      const cleaned = followerString.trim().replace(/[^0-9.,KMB]/g, '');
      
      // Se tiver K, M ou B, processar como antes
      if (cleaned.match(/[KMB]$/)) {
        const match = cleaned.match(/(\d+(\.\d+)?)([KMB])?/);
        if (!match) return 0;
        
        let value = parseFloat(match[1].replace(',', '.'));
        if (match[3] === 'K') value *= 1000;
        if (match[3] === 'M') value *= 1000000;
        if (match[3] === 'B') value *= 1000000000;
        
        return value;
      }
      
      // Se não tiver K, M ou B, tratar como número com separador de milhar
      return parseFormattedNumber(cleaned);
    };
    
    // Processar cada influenciador
    influencers.forEach((data: Influencer) => {
      // Somar visualizações reais de cada rede social usando parseFormattedNumber
      if (data.instagram_views) {
        totalViews += parseFormattedNumber(data.instagram_views);
      }
      
      if (data.youtube_views) {
        totalViews += parseFormattedNumber(data.youtube_views);
      }
      
      if (data.tiktok_views) {
        totalViews += parseFormattedNumber(data.tiktok_views);
      }
      
      // Calcular total de seguidores
      if (data.instagram) {
        totalFollowers += parseFollowerCount(data.instagram);
      }
      
      if (data.youtube) {
        totalFollowers += parseFollowerCount(data.youtube);
      }
      
      if (data.tiktok) {
        totalFollowers += parseFollowerCount(data.tiktok);
      }
    });
    
    // Retornar os dados formatados
    return NextResponse.json({
      success: true,
      totalInfluencers,
      totalViews: Math.round(totalViews),
      totalFollowers: Math.round(totalFollowers),
    });
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro ao buscar estatísticas'
    }, { status: 500 });
  }
}


