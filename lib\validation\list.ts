import { z } from 'zod';
import { BaseDocumentSchema, ERROR_MESSAGES } from './base';

// Schema para tipos específicos de listas
export const TipoListaSchema = z.enum(['estática', 'dinâmica'] as const);
export const TipoObjetoSchema = z.enum(['influenciadores', 'marcas', 'campanhas', 'conteúdo'] as const);
export const StatusListaSchema = z.enum(['ativa', 'arquivada', 'deletada'] as const);
export const FrequenciaAtualizacaoSchema = z.enum(['manual', 'diaria', 'semanal', 'mensal'] as const);

// Schema para permissões de lista
export const PermissoesListaSchema = z.object({
  visualizar: z.array(z.string()),
  editar: z.array(z.string()),
  gerenciar: z.array(z.string())
});

// Schema para critérios de listas dinâmicas
export const CriterioListaSchema = z.object({
  campo: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(50, ERROR_MESSAGES.MAX_LENGTH(50)),
  
  operador: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(20, ERROR_MESSAGES.MAX_LENGTH(20)),
  
  valor: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
});

// Schema para configuração de atualização
export const ConfiguracaoAtualizacaoSchema = z.object({
  frequencia: FrequenciaAtualizacaoSchema,
  ultimaExecucao: z.date().nullable(),
  proximaExecucao: z.date().nullable(),
  ativa: z.boolean()
});

// Schema para estatísticas da lista
export const EstatisticasListaSchema = z.object({
  totalVisualizacoes: z.number().int().min(0),
  ultimaVisualizacao: z.date().nullable(),
  totalCompartilhamentos: z.number().int().min(0),
  totalExportacoes: z.number().int().min(0)
});

// Schema para configuração de exportação
export const ConfiguracaoExportacaoSchema = z.object({
  formatosPadrao: z.array(z.string()),
  incluirMetadados: z.boolean(),
  incluirEstatisticas: z.boolean()
});

// Schema completo para Lista
export const ListaSchema = BaseDocumentSchema.extend({
  nome: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim(),
  
  descricao: z.string()
    .max(500, ERROR_MESSAGES.MAX_LENGTH(500))
    .trim(),
  
  tipoLista: TipoListaSchema,
  tipoObjeto: TipoObjetoSchema,
  
  criadoPor: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  
  criadoPorNome: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100)),
  
  tamanho: z.number()
    .int()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0))
    .max(100000, ERROR_MESSAGES.MAX_VALUE(100000)),
  
  tags: z.array(z.string().max(30, ERROR_MESSAGES.MAX_LENGTH(30))),
  
  marcasAssociadas: z.array(z.object({
    id: z.string().min(1, 'ID da marca é obrigatório'),
    name: z.string().min(1, 'Nome da marca é obrigatório'),
    logo: z.string().url().optional()
  })),
  
  isPublica: z.boolean(),
  
  compartilhadaCom: z.array(z.string()),
  
  permissoes: PermissoesListaSchema,
  
  criterios: z.array(CriterioListaSchema),
  
  configuracaoAtualizacao: ConfiguracaoAtualizacaoSchema,
  
  estatisticas: EstatisticasListaSchema,
  
  status: StatusListaSchema,
  
  configuracaoExportacao: ConfiguracaoExportacaoSchema
});

// Schema para criação de nova lista
export const CreateListaSchema = z.object({
  nome: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim(),
  
  descricao: z.string()
    .max(500, ERROR_MESSAGES.MAX_LENGTH(500))
    .trim()
    .optional(),
  
  tipoLista: TipoListaSchema,
  tipoObjeto: TipoObjetoSchema,
  
  tags: z.array(z.string().max(30, ERROR_MESSAGES.MAX_LENGTH(30)))
    .max(10, 'Máximo 10 tags permitidas')
    .optional(),
  
  isPublica: z.boolean().optional(),
  
  criterios: z.array(CriterioListaSchema)
    .max(20, 'Máximo 20 critérios permitidos')
    .optional(),
  
  marcasAssociadas: z.array(z.string().min(1, 'ID da marca é obrigatório'))
    .max(50, 'Máximo 50 marcas por lista')
    .optional()
});

// Schema para atualização de lista
export const UpdateListaSchema = z.object({
  nome: z.string()
    .min(2, ERROR_MESSAGES.MIN_LENGTH(2))
    .max(100, ERROR_MESSAGES.MAX_LENGTH(100))
    .trim()
    .optional(),
  
  descricao: z.string()
    .max(500, ERROR_MESSAGES.MAX_LENGTH(500))
    .trim()
    .optional(),
  
  tags: z.array(z.string().max(30, ERROR_MESSAGES.MAX_LENGTH(30)))
    .max(10, 'Máximo 10 tags permitidas')
    .optional(),
  
  isPublica: z.boolean().optional(),
  
  criterios: z.array(CriterioListaSchema)
    .max(20, 'Máximo 20 critérios permitidos')
    .optional(),
  
  status: StatusListaSchema.optional(),
  
  configuracaoAtualizacao: ConfiguracaoAtualizacaoSchema.partial().optional(),
  
  marcasAssociadas: z.array(z.string().min(1, 'ID da marca é obrigatório'))
    .max(50, 'Máximo 50 marcas por lista')
    .optional()
});

// Schema para item de lista
export const ItemListaSchema = BaseDocumentSchema.extend({
  listaId: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  
  itemId: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  
  tipoItem: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(50, ERROR_MESSAGES.MAX_LENGTH(50)),
  
  dataAdicao: z.date(),
  
  adicionadoPor: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  
  posicao: z.number()
    .int()
    .min(0, ERROR_MESSAGES.MIN_VALUE(0)),
  
  itemData: z.object({
    nome: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD),
    foto: z.string().url().optional().nullable()
  }).catchall(z.any()),
  
  status: z.enum(['ativo', 'removido'] as const),
  
  notas: z.string()
    .max(1000, ERROR_MESSAGES.MAX_LENGTH(1000)),
  
  tagsPersonalizadas: z.array(z.string().max(30, ERROR_MESSAGES.MAX_LENGTH(30)))
    .max(10, 'Máximo 10 tags personalizadas')
});

// Schema para adicionar item à lista
export const AdicionarItemListaSchema = z.object({
  itemId: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD),
  
  tipoItem: z.string()
    .min(1, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(50, ERROR_MESSAGES.MAX_LENGTH(50)),
  
  notas: z.string()
    .max(1000, ERROR_MESSAGES.MAX_LENGTH(1000))
    .optional(),
  
  tagsPersonalizadas: z.array(z.string().max(30, ERROR_MESSAGES.MAX_LENGTH(30)))
    .max(10, 'Máximo 10 tags personalizadas')
    .optional()
});

// Schema para filtros de lista
export const ListaFiltersSchema = z.object({
  userId: z.string().min(1).optional(),
  busca: z.string().optional(),
  tipoLista: TipoListaSchema.optional(),
  tipoObjeto: TipoObjetoSchema.optional(),
  status: StatusListaSchema.optional(),
  tags: z.array(z.string()).optional(),
  isPublica: z.boolean().optional(),
  tamanhoMin: z.number().int().min(0).optional(),
  tamanhoMax: z.number().int().min(0).optional(),
  criadoPor: z.string().optional(),
  createdAtFrom: z.date().optional(),
  createdAtTo: z.date().optional(),
  limit: z.number().int().min(1).max(100).optional(),
  offset: z.number().int().min(0).optional(),
  sortBy: z.enum(['nome', 'createdAt', 'updatedAt', 'tamanho', 'ultimaAtualizacao']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// Schema para operações em lote
export const OperacaoLoteListaSchema = z.object({
  listaIds: z.array(z.string().min(1))
    .min(1, 'Pelo menos uma lista deve ser selecionada')
    .max(50, 'Máximo 50 listas por operação'),
  
  operacao: z.enum(['arquivar', 'deletar', 'compartilhar', 'mover'] as const),
  
  parametros: z.record(z.any()).optional()
});



// Tipos TypeScript derivados dos schemas
export type ListaValidation = z.infer<typeof ListaSchema>;
export type CreateListaInput = z.infer<typeof CreateListaSchema>;
export type UpdateListaInput = z.infer<typeof UpdateListaSchema>;
export type ItemListaValidation = z.infer<typeof ItemListaSchema>;
export type AdicionarItemListaInput = z.infer<typeof AdicionarItemListaSchema>;
export type ListaFiltersInput = z.infer<typeof ListaFiltersSchema>;
export type OperacaoLoteListaInput = z.infer<typeof OperacaoLoteListaSchema>;
export type CompartilharListaInput = z.infer<typeof CompartilharListaSchema>;

// Funções de validação customizadas
export function validateListaOwnership(listaData: any, userId: string): boolean {
  if (!listaData || !listaData.criadoPor) {
    return false;
  }
  return listaData.criadoPor === userId;
}

export function validateListaAccess(listaData: any, userId: string): boolean {
  if (!listaData) return false;
  
  // Proprietário sempre tem acesso
  if (listaData.criadoPor === userId) return true;
  
  // Lista pública
  if (listaData.isPublica) return true;
  
  // Usuário com permissão específica
  if (listaData.permissoes) {
    return (
      listaData.permissoes.visualizar?.includes(userId) ||
      listaData.permissoes.editar?.includes(userId) ||
      listaData.permissoes.gerenciar?.includes(userId)
    );
  }
  
  return false;
}

export function validateListaEditPermission(listaData: any, userId: string): boolean {
  if (!listaData || !listaData.criadoPor) return false;
  
  // Proprietário sempre pode editar
  if (listaData.criadoPor === userId) return true;
  
  // Usuários com permissão de editar ou gerenciar
  if (listaData.permissoes) {
    return (
      listaData.permissoes.editar?.includes(userId) ||
      listaData.permissoes.gerenciar?.includes(userId)
    );
  }
  
  return false;
}

export function validateListaUniqueness(
  novaListaNome: string,
  listasExistentes: any[],
  userId: string,
  excludeListaId?: string
): boolean {
  const normalizedName = novaListaNome.trim().toLowerCase();
  
  return !listasExistentes.some(lista =>
    lista.criadoPor === userId &&
    lista.nome.toLowerCase() === normalizedName &&
    lista.id !== excludeListaId &&
    lista.status !== 'deletada'
  );
}

export function validateCriteriosForTipoObjeto(
  criterios: any[],
  tipoObjeto: string
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  const camposValidos: Record<string, string[]> = {
    'influenciadores': ['seguidores', 'engajamento', 'nicho', 'localização', 'idade', 'gênero'],
    'marcas': ['orçamento', 'setor', 'status', 'funcionários', 'faturamento'],
    'campanhas': ['budget', 'status', 'dataFim', 'alcance', 'impressões'],
    'conteúdo': ['formato', 'tamanho', 'dataUpload', 'engajamento', 'visualizações']
  };
  
  const campos = camposValidos[tipoObjeto] || [];
  
  criterios.forEach((criterio, index) => {
    if (!campos.includes(criterio.campo)) {
      errors.push(`Critério ${index + 1}: Campo '${criterio.campo}' não é válido para ${tipoObjeto}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateItemCompatibility(tipoObjeto: string, tipoItem: string): boolean {
  const compatibility: Record<string, string[]> = {
    'influenciadores': ['influenciador', 'creator', 'influencer'],
    'marcas': ['marca', 'brand', 'empresa'],
    'campanhas': ['campanha', 'campaign', 'projeto'],
    'conteúdo': ['post', 'video', 'foto', 'story', 'reel', 'content']
  };
  
  return compatibility[tipoObjeto]?.includes(tipoItem.toLowerCase()) || false;
}

export function sanitizeListaData(data: CreateListaInput): CreateListaInput {
  const sanitized = { ...data };
  
  // Normalizar nome
  sanitized.nome = sanitized.nome.trim();
  
  // Normalizar descrição
  if (sanitized.descricao) {
    sanitized.descricao = sanitized.descricao.trim();
  }
  
  // Normalizar tags
  if (sanitized.tags) {
    sanitized.tags = sanitized.tags
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0)
      .filter((tag, index, arr) => arr.indexOf(tag) === index); // Remove duplicatas
  }
  
  // Definir isPublica padrão
  if (sanitized.isPublica === undefined) {
    sanitized.isPublica = false;
  }
  
  // Validar critérios para listas dinâmicas
  if (sanitized.tipoLista === 'dinâmica' && (!sanitized.criterios || sanitized.criterios.length === 0)) {
    throw new Error('Listas dinâmicas devem ter pelo menos um critério');
  }
  
  if (sanitized.tipoLista === 'estática' && sanitized.criterios && sanitized.criterios.length > 0) {
    sanitized.criterios = []; // Remove critérios de listas estáticas
  }
  
  return sanitized;
}

export function calculateListaCompleteness(lista: any): number {
  let score = 0;
  
  // Pontuação base por campo preenchido
  if (lista.nome) score += 15;
  if (lista.descricao && lista.descricao.length > 10) score += 10;
  if (lista.tags && lista.tags.length > 0) score += 10;
  if (lista.tamanho > 0) score += 20;
  
  // Pontuação por tipo de lista
  if (lista.tipoLista === 'dinâmica' && lista.criterios?.length > 0) {
    score += 15; // Listas dinâmicas são mais valiosas
  }
  
  // Pontuação por compartilhamento
  if (lista.compartilhadaCom && lista.compartilhadaCom.length > 0) {
    score += 10;
  }
  
  // Pontuação por atividade
  if (lista.estatisticas?.totalVisualizacoes > 0) score += 10;
  if (lista.estatisticas?.totalCompartilhamentos > 0) score += 10;
  
  return Math.min(score, 100); // Máximo 100
}

export function generateListaSuggestions(lista: any): string[] {
  const suggestions: string[] = [];
  
  if (!lista.descricao || lista.descricao.length < 10) {
    suggestions.push('Adicione uma descrição mais detalhada à lista');
  }
  
  if (!lista.tags || lista.tags.length === 0) {
    suggestions.push('Adicione tags para facilitar a organização');
  }
  
  if (lista.tamanho === 0) {
    suggestions.push('Adicione itens à sua lista');
  }
  
  if (lista.tipoLista === 'dinâmica' && (!lista.criterios || lista.criterios.length === 0)) {
    suggestions.push('Configure critérios para que a lista seja atualizada automaticamente');
  }
  
  if (!lista.isPublica && lista.compartilhadaCom.length === 0) {
    suggestions.push('Considere compartilhar a lista com outros usuários');
  }
  
  return suggestions;
} 

