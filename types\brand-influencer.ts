import { BaseDocument } from './base';

/**
 * 🤝 TIPOS PARA ASSOCIAÇÕES MARCA-INFLUENCIADOR
 * Representa o relacionamento entre marcas e influenciadores
 */

export interface BrandInfluencer extends BaseDocument {
  // Identificadores
  brandId: string;             // ID da marca
  influencerId: string;        // ID do influenciador
  userId: string;              // ID do usuário proprietário
  
  // Dados da marca (cache para performance)
  brandName: string;           // Nome da marca
  brandLogo?: string;          // Logo da marca
  
  // Dados do influenciador (cache para performance)
  influencerName: string;      // Nome do influenciador
  influencerAvatar?: string;   // Avatar do influenciador
  
  // Metadados
  tags?: string[];             // Tags para organização
  
  // Histórico de colaborações
  collaborationHistory?: {
    campaignId?: string;
    date: Date;
    type: string;             // 'story', 'post', 'video', etc.
    description?: string;
  }[];
  
  // Dados de contrato/acordo
  contractDetails?: {
    type: 'fixed' | 'per_campaign' | 'ongoing';
    startDate?: Date;
    endDate?: Date;
    terms?: string;
  };
}

// Interface para criação de nova associação
export interface CreateBrandInfluencerData {
  brandId: string;
  influencerId: string;
  tags?: string[];
  contractDetails?: {
    type: 'fixed' | 'per_campaign' | 'ongoing';
    startDate?: Date;
    endDate?: Date;
    terms?: string;
  };
}

// Interface para atualização
export interface UpdateBrandInfluencerData {
  tags?: string[];
  contractDetails?: {
    type: 'fixed' | 'per_campaign' | 'ongoing';
    startDate?: Date;
    endDate?: Date;
    terms?: string;
  };
}

// Interface para busca/filtros
export interface BrandInfluencerFilters {
  userId?: string;
  brandId?: string;
  influencerId?: string;
  tags?: string[];
  search?: string;
  createdAtFrom?: Date;
  createdAtTo?: Date;
}

// Interface para resultado de busca
export interface BrandInfluencerSearchResult {
  associations: BrandInfluencer[];
  total: number;
  page: number;
  limit: number;
} 

