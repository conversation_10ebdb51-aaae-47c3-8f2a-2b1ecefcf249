#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Iniciando deploy dos índices do Firestore...');

// Executar firebase deploy --only firestore:indexes
const deployProcess = spawn('firebase', ['deploy', '--only', 'firestore:indexes'], {
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: process.platform === 'win32'
});

deployProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Índices do Firestore foram implantados com sucesso!');
    console.log('\n🎯 Otimizações aplicadas:');
    console.log('   • Índice simples para users.email');
    console.log('   • Índice composto para users.email + isActive');
    console.log('   • Índice composto para users.role + isActive + lastLoginAt');
    console.log('   • Índice composto para users.email + role + lastLoginAt');
    console.log('   • Índices de performance para users.isActive e users.role');
    console.log('\n⚡ O login deve estar significativamente mais rápido agora!');
    console.log('\n📊 Para monitorar a performance:');
    console.log('   • Acesse o Firebase Console');
    console.log('   • Vá para Firestore Database > Uso');
    console.log('   • Monitore as consultas lentas');
  } else {
    console.error('❌ Erro ao implantar índices. Código de saída:', code);
    console.log('\n🔧 Para depurar:');
    console.log('   • Verifique se você está autenticado: firebase login');
    console.log('   • Verifique o projeto ativo: firebase projects:list');
    console.log('   • Tente manualmente: firebase deploy --only firestore:indexes');
  }
});

deployProcess.on('error', (error) => {
  console.error('❌ Erro ao executar o comando:', error.message);
  console.log('\n💡 Certifique-se de que o Firebase CLI está instalado:');
  console.log('   npm install -g firebase-tools');
}); 
