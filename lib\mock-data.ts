import { User, UserSession } from '@/types/user';
import { Brand } from '@/types/brand';

// Usuários simulados para teste
export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Super Ad<PERSON>',
    role: 'super_admin',
    passwordHash: 'admin123', // Em produção, seria um hash real
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastLoginAt: new Date(),
    profileImage: null
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    name: '<PERSON><PERSON> da Empresa',
    role: 'admin',
    passwordHash: 'admin123',
    isActive: true,
    createdAt: new Date('2024-01-02'),
    lastLoginAt: new Date(),
    profileImage: null
  },

  {
    id: 'user-4',
    email: '<EMAIL>',
    name: 'Manager Admin',
    role: 'manager',
    passwordHash: 'manager123',
    isActive: true,
    createdAt: new Date('2024-01-04'),
    lastLoginAt: new Date(),
    profileImage: null
  },
  {
    id: 'user-5',
    email: '<EMAIL>',
    name: 'Viewer Admin',
    role: 'viewer',
    passwordHash: 'viewer123',
    isActive: true,
    createdAt: new Date('2024-01-05'),
    lastLoginAt: new Date(),
    profileImage: null
  }
];

// Marcas simuladas para teste
export const mockBrands: Brand[] = [
  {
    id: 'brand-1',
    userId: 'user-3',
    name: 'Marca Teste',
    logo: null,
    industry: 'Tecnologia',
    website: 'https://marcateste.com',
    contactEmail: '<EMAIL>',
    contactPhone: '(11) 99999-9999',
    contactName: 'João Silva',
    budget: 50000,
    notes: 'Marca focada em produtos tecnológicos',
    isActive: true,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date()
  },
  {
    id: 'brand-2',
    userId: 'user-4',
    name: 'Fashion Brand',
    logo: null,
    industry: 'Moda',
    website: 'https://fashionbrand.com',
    contactEmail: '<EMAIL>',
    contactPhone: '(11) 88888-8888',
    contactName: 'Maria Santos',
    budget: 75000,
    notes: 'Marca de moda feminina',
    isActive: true,
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date()
  }
];

// Sessões simuladas (em produção, seria armazenado no banco)
export const mockSessions: UserSession[] = [];

// Função para simular autenticação
export function authenticateUser(email: string, password: string): User | null {
  const user = mockUsers.find(u => u.email === email && u.passwordHash === password);
  return user || null;
}

// Função para buscar usuário por ID
export function getUserById(id: string): User | null {
  return mockUsers.find(u => u.id === id) || null;
}

// Função para buscar marca por ID do usuário
export function getBrandByUserId(userId: string): Brand | null {
  return mockBrands.find(b => b.userId === userId) || null;
}

// Função para criar sessão simulada
export function createMockSession(userId: string, token: string, expiresAt: Date): UserSession {
  const session: UserSession = {
    id: `session-${Date.now()}`,
    userId,
    token,
    expiresAt,
    createdAt: new Date()
  };
  
  mockSessions.push(session);
  return session;
}

// Função para buscar sessão por token
export function getSessionByToken(token: string): UserSession | null {
  return mockSessions.find(s => s.token === token) || null;
}

// Função para deletar sessão
export function deleteMockSession(token: string): boolean {
  const index = mockSessions.findIndex(s => s.token === token);
  if (index > -1) {
    mockSessions.splice(index, 1);
    return true;
  }
  return false;
}

// Função para limpar sessões expiradas
export function cleanExpiredSessions(): void {
  const now = new Date();
  for (let i = mockSessions.length - 1; i >= 0; i--) {
    if (mockSessions[i].expiresAt < now) {
      mockSessions.splice(i, 1);
    }
  }
}

