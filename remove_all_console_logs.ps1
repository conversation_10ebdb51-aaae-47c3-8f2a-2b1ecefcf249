# Script para remover todos os console.log dos arquivos principais
Write-Host "🧹 Removendo TODOS os console.log que estão gerando saída no navegador..." -ForegroundColor Yellow

$targetFiles = @(
    "components/influencer-grid/index.tsx",
    "hooks/use-influencer-form.ts", 
    "app/[userId]/influencers/page.tsx",
    "contexts/firebase-auth-context.tsx"
)

$totalRemoved = 0

foreach ($file in $targetFiles) {
    if (Test-Path $file) {
        Write-Host "📁 Processando: $file" -ForegroundColor Cyan
        
        # Backup
        $backupPath = "$file.backup-mass-cleanup-" + (Get-Date -Format "yyyyMMdd-HHmmss")
        Copy-Item $file $backupPath
        
        # Ler e processar linha por linha
        $lines = Get-Content $file
        $cleanedLines = @()
        $removedCount = 0
        
        foreach ($line in $lines) {
            # Se a linha contém apenas console.log (com possível indentação), pular
            if ($line -match '^\s*console\.(log|error|warn|info|debug)\s*\([^)]*\);\s*$') {
                $removedCount++
                continue
            }
            
            # Se a linha contém apenas console.log sem ponto e vírgula
            if ($line -match '^\s*console\.(log|error|warn|info|debug)\s*\([^)]*\)\s*$') {
                $removedCount++
                continue
            }
            
            # Manter a linha
            $cleanedLines += $line
        }
        
        # Escrever de volta
        $cleanedLines | Set-Content $file -Encoding UTF8
        
        Write-Host "✅ $($file): $($removedCount) console.log removidos" -ForegroundColor Green
        $totalRemoved += $removedCount
    } else {
        Write-Host "❌ Arquivo não encontrado: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 CONCLUÍDO! Total de console.log removidos: $($totalRemoved)" -ForegroundColor Green
Write-Host "📁 Backups criados com timestamp mass-cleanup" -ForegroundColor Yellow 