'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useAuth, useRouteProtection } from '@/hooks/use-auth-v2';
import { Brand } from '@/types/brand';
import { Campaign } from '@/types/campaign';
import { Proposal } from '@/types/proposal';

/**
 * 🗂️ DATA CONTEXT COM ISOLAMENTO AUTOMÁTICO
 * FASE 6.2: Context provider atualizado para trabalhar com isolamento por usuário
 */

interface DataContextType {
  // Estados dos dados
  brands: Brand[];
  campaigns: Campaign[];
  proposals: Proposal[];
  
  // Estados de carregamento
  loading: boolean;
  brandsLoading: boolean;
  campaignsLoading: boolean;
  proposalsLoading: boolean;
  
  // Estados de erro
  error: string | null;
  brandsError: string | null;
  campaignsError: string | null;
  proposalsError: string | null;
  
  // Funções de controle
  refreshData: () => Promise<void>;
  refreshBrands: () => Promise<void>;
  refreshCampaigns: () => Promise<void>;
  refreshProposals: () => Promise<void>;
  
  // Funções de busca
  getBrandById: (id: string) => Brand | undefined;
  getCampaignById: (id: string) => Campaign | undefined;
  getProposalById: (id: string) => Proposal | undefined;
  getCampaignsByBrand: (brandId: string) => Campaign[];
  getProposalsByBrand: (brandId: string) => Proposal[];
  getProposalsByCampaign: (campaignId: string) => Proposal[];
  
  // Cache e performance
  lastUpdated: Date | null;
  isStale: boolean;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

interface DataProviderProps {
  children: ReactNode;
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableCache?: boolean;
}

export function DataProvider({ 
  children, 
  autoRefresh = true,
  refreshInterval = 60000, // 1 minuto
  enableCache = true
}: DataProviderProps) {
  const { currentUser, firebaseUser } = useAuth();
  const { isAuthenticated } = useRouteProtection();
  
  // Estados dos dados
  const [brands, setBrands] = useState<Brand[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  
  // Estados de carregamento
  const [loading, setLoading] = useState(false);
  const [brandsLoading, setBrandsLoading] = useState(false);
  const [campaignsLoading, setCampaignsLoading] = useState(false);
  const [proposalsLoading, setProposalsLoading] = useState(false);
  
  // Estados de erro
  const [error, setError] = useState<string | null>(null);
  const [brandsError, setBrandsError] = useState<string | null>(null);
  const [campaignsError, setCampaignsError] = useState<string | null>(null);
  const [proposalsError, setProposalsError] = useState<string | null>(null);
  
  // Metadados
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isStale, setIsStale] = useState(false);

  /**
   * Buscar marcas do usuário
   */
  const refreshBrands = useCallback(async () => {
    if (!isAuthenticated || !firebaseUser) {
      setBrands([]);
      setBrandsError(null);
      return;
    }

    try {
      setBrandsLoading(true);
      setBrandsError(null);
      
      console.log('[DATA_CONTEXT] Carregando marcas...');
      
      const response = await fetch('/api/brands', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Resposta de API inválida');
      }
      
      const brandsData = result.data || [];
      setBrands(brandsData);
      
      console.log(`[DATA_CONTEXT] ${brandsData.length} marcas carregadas`);
      
      if (enableCache && firebaseUser) {
        localStorage.setItem(`data_brands_${firebaseUser.uid}`, JSON.stringify({
          data: brandsData,
          timestamp: Date.now()
        }));
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar marcas';
      console.error('[DATA_CONTEXT] Erro ao carregar marcas:', err);
      setBrandsError(errorMessage);
      
      // Tentar carregar do cache
      if (enableCache && firebaseUser) {
        try {
          const cached = localStorage.getItem(`data_brands_${firebaseUser.uid}`);
          if (cached) {
            const { data, timestamp } = JSON.parse(cached);
            if (Date.now() - timestamp < 300000) { // 5 minutos
              setBrands(data);
              console.log('[DATA_CONTEXT] Marcas carregadas do cache');
            }
          }
        } catch (cacheError) {
          console.warn('[DATA_CONTEXT] Erro ao carregar cache de marcas:', cacheError);
        }
      }
    } finally {
      setBrandsLoading(false);
    }
  }, [isAuthenticated, firebaseUser, enableCache]);

  /**
   * Buscar campanhas do usuário
   */
  const refreshCampaigns = useCallback(async () => {
    if (!isAuthenticated || !firebaseUser) {
      setCampaigns([]);
      setCampaignsError(null);
      return;
    }

    try {
      setCampaignsLoading(true);
      setCampaignsError(null);
      
      console.log('[DATA_CONTEXT] Carregando campanhas...');
      
      const response = await fetch('/api/campaigns', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Resposta de API inválida');
      }
      
      const campaignsData = result.data || [];
      setCampaigns(campaignsData);
      
      console.log(`[DATA_CONTEXT] ${campaignsData.length} campanhas carregadas`);
      
      if (enableCache && firebaseUser) {
        localStorage.setItem(`data_campaigns_${firebaseUser.uid}`, JSON.stringify({
          data: campaignsData,
          timestamp: Date.now()
        }));
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar campanhas';
      console.error('[DATA_CONTEXT] Erro ao carregar campanhas:', err);
      setCampaignsError(errorMessage);
      
      // Tentar carregar do cache
      if (enableCache && firebaseUser) {
        try {
          const cached = localStorage.getItem(`data_campaigns_${firebaseUser.uid}`);
          if (cached) {
            const { data, timestamp } = JSON.parse(cached);
            if (Date.now() - timestamp < 300000) { // 5 minutos
              setCampaigns(data);
              console.log('[DATA_CONTEXT] Campanhas carregadas do cache');
            }
          }
        } catch (cacheError) {
          console.warn('[DATA_CONTEXT] Erro ao carregar cache de campanhas:', cacheError);
        }
      }
    } finally {
      setCampaignsLoading(false);
    }
  }, [isAuthenticated, firebaseUser, enableCache]);

  /**
   * Buscar propostas do usuário
   */
  const refreshProposals = useCallback(async () => {
    if (!isAuthenticated || !firebaseUser) {
      setProposals([]);
      setProposalsError(null);
      return;
    }

    try {
      setProposalsLoading(true);
      setProposalsError(null);
      
      console.log('[DATA_CONTEXT] Carregando propostas...');
      
      const response = await fetch('/api/proposals', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await firebaseUser.getIdToken()}`
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Resposta de API inválida');
      }
      
      const proposalsData = result.data || [];
      setProposals(proposalsData);
      
      console.log(`[DATA_CONTEXT] ${proposalsData.length} propostas carregadas`);
      
      if (enableCache && firebaseUser) {
        localStorage.setItem(`data_proposals_${firebaseUser.uid}`, JSON.stringify({
          data: proposalsData,
          timestamp: Date.now()
        }));
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar propostas';
      console.error('[DATA_CONTEXT] Erro ao carregar propostas:', err);
      setProposalsError(errorMessage);
      
      // Tentar carregar do cache
      if (enableCache && firebaseUser) {
        try {
          const cached = localStorage.getItem(`data_proposals_${firebaseUser.uid}`);
          if (cached) {
            const { data, timestamp } = JSON.parse(cached);
            if (Date.now() - timestamp < 300000) { // 5 minutos
              setProposals(data);
              console.log('[DATA_CONTEXT] Propostas carregadas do cache');
            }
          }
        } catch (cacheError) {
          console.warn('[DATA_CONTEXT] Erro ao carregar cache de propostas:', cacheError);
        }
      }
    } finally {
      setProposalsLoading(false);
    }
  }, [isAuthenticated, firebaseUser, enableCache]);

  /**
   * Refresh geral de todos os dados
   */
  const refreshData = useCallback(async () => {
    if (!isAuthenticated || !firebaseUser) {
      setBrands([]);
      setCampaigns([]);
      setProposals([]);
      setError(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setIsStale(false);
      
      console.log('[DATA_CONTEXT] Iniciando refresh completo dos dados...');
      
      // Carregar todos os dados em paralelo para melhor performance
      await Promise.all([
        refreshBrands(),
        refreshCampaigns(),
        refreshProposals()
      ]);
      
      setLastUpdated(new Date());
      console.log('[DATA_CONTEXT] Refresh completo finalizado');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar dados';
      console.error('[DATA_CONTEXT] Erro no refresh geral:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, firebaseUser, refreshBrands, refreshCampaigns, refreshProposals]);

  /**
   * Funções de busca
   */
  const getBrandById = useCallback((id: string): Brand | undefined => {
    return brands.find(brand => brand.id === id);
  }, [brands]);

  const getCampaignById = useCallback((id: string): Campaign | undefined => {
    return campaigns.find(campaign => campaign.id === id);
  }, [campaigns]);

  const getProposalById = useCallback((id: string): Proposal | undefined => {
    return proposals.find(proposal => proposal.id === id);
  }, [proposals]);

  const getCampaignsByBrand = useCallback((brandId: string): Campaign[] => {
    return campaigns.filter(campaign => campaign.brandId === brandId);
  }, [campaigns]);

  const getProposalsByBrand = useCallback((brandId: string): Proposal[] => {
    return proposals.filter(proposal => proposal.brandId === brandId);
  }, [proposals]);

  const getProposalsByCampaign = useCallback((campaignId: string): Proposal[] => {
    return proposals.filter(proposal => proposal.campaignId === campaignId);
  }, [proposals]);

  // Carregar dados iniciais
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Auto-refresh periódico
  useEffect(() => {
    if (!autoRefresh || !isAuthenticated) return;

    const interval = setInterval(() => {
      console.log('[DATA_CONTEXT] Auto-refresh ativado');
      setIsStale(true);
      refreshData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isAuthenticated, refreshData]);

  // Detectar quando os dados ficam obsoletos
  useEffect(() => {
    if (!lastUpdated || !refreshInterval) return;

    const staleTimeout = setTimeout(() => {
      setIsStale(true);
    }, refreshInterval);

    return () => clearTimeout(staleTimeout);
  }, [lastUpdated, refreshInterval]);

  // Limpeza ao fazer logout
  useEffect(() => {
    if (!isAuthenticated) {
      setBrands([]);
      setCampaigns([]);
      setProposals([]);
      setError(null);
      setBrandsError(null);
      setCampaignsError(null);
      setProposalsError(null);
      setLoading(false);
      setBrandsLoading(false);
      setCampaignsLoading(false);
      setProposalsLoading(false);
      setLastUpdated(null);
      setIsStale(false);
      
      // Limpar cache do usuário anterior
      if (enableCache) {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('data_')) {
            localStorage.removeItem(key);
          }
        });
      }
    }
  }, [isAuthenticated, enableCache]);

  return (
    <DataContext.Provider value={{
      // Estados dos dados
      brands,
      campaigns,
      proposals,
      
      // Estados de carregamento
      loading,
      brandsLoading,
      campaignsLoading,
      proposalsLoading,
      
      // Estados de erro
      error,
      brandsError,
      campaignsError,
      proposalsError,
      
      // Funções de controle
      refreshData,
      refreshBrands,
      refreshCampaigns,
      refreshProposals,
      
      // Funções de busca
      getBrandById,
      getCampaignById,
      getProposalById,
      getCampaignsByBrand,
      getProposalsByBrand,
      getProposalsByCampaign,
      
      // Cache e performance
      lastUpdated,
      isStale
    }}>
      {children}
    </DataContext.Provider>
  );
}

/**
 * Hook para usar o Data Context
 */
export function useData(): DataContextType {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData deve ser usado dentro de um DataProvider');
  }
  return context;
}

/**
 * Hook simplificado para brands
 */
export function useDataBrands(): {
  brands: Brand[];
  loading: boolean;
  error: string | null;
  refreshBrands: () => Promise<void>;
  getBrandById: (id: string) => Brand | undefined;
} {
  const { brands, brandsLoading, brandsError, refreshBrands, getBrandById } = useData();
  return {
    brands,
    loading: brandsLoading,
    error: brandsError,
    refreshBrands,
    getBrandById
  };
}

/**
 * Hook simplificado para campaigns
 */
export function useDataCampaigns(): {
  campaigns: Campaign[];
  loading: boolean;
  error: string | null;
  refreshCampaigns: () => Promise<void>;
  getCampaignById: (id: string) => Campaign | undefined;
  getCampaignsByBrand: (brandId: string) => Campaign[];
} {
  const { campaigns, campaignsLoading, campaignsError, refreshCampaigns, getCampaignById, getCampaignsByBrand } = useData();
  return {
    campaigns,
    loading: campaignsLoading,
    error: campaignsError,
    refreshCampaigns,
    getCampaignById,
    getCampaignsByBrand
  };
}

/**
 * Hook simplificado para proposals
 */
export function useDataProposals(): {
  proposals: Proposal[];
  loading: boolean;
  error: string | null;
  refreshProposals: () => Promise<void>;
  getProposalById: (id: string) => Proposal | undefined;
  getProposalsByBrand: (brandId: string) => Proposal[];
  getProposalsByCampaign: (campaignId: string) => Proposal[];
} {
  const { proposals, proposalsLoading, proposalsError, refreshProposals, getProposalById, getProposalsByBrand, getProposalsByCampaign } = useData();
  return {
    proposals,
    loading: proposalsLoading,
    error: proposalsError,
    refreshProposals,
    getProposalById,
    getProposalsByBrand,
    getProposalsByCampaign
  };
} 

