import { Badge } from "@/components/ui/badge"
import { 
  PROPOSAL_STATUS_LABELS,
  type ProposalStatus 
} from '@/types/proposal'
import { cn } from "@/lib/utils"

interface ProposalStatusBadgeProps {
  status: ProposalStatus
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

// ✅ Gradientes personalizados para cada status - Tom claro > cor com texto escuro
const getStatusGradientClasses = (status: ProposalStatus): string => {
  const gradientColors: Record<ProposalStatus, string> = {
    draft: 'bg-gradient-to-r from-gray-100 to-gray-250 text-gray-800 border-gray-200',
    pending: 'bg-gradient-to-r from-yellow-100 to-yellow-300 text-yellow-900 border-yellow-300',
    sent: 'bg-gradient-to-r from-blue-100 to-blue-300 text-blue-900 border-blue-300',
    viewed: 'bg-gradient-to-r from-purple-100 to-purple-300 text-purple-900 border-purple-300',
    under_review: 'bg-gradient-to-r from-yellow-100 to-yellow-300 text-yellow-900 border-yellow-300',
    negotiating: 'bg-gradient-to-r from-orange-100 to-orange-300 text-orange-900 border-orange-300',
    approved: 'bg-gradient-to-r from-green-100 to-green-300 text-green-900 border-green-300',
    accepted: 'bg-gradient-to-r from-emerald-100 to-emerald-300 text-emerald-900 border-emerald-300',
    rejected: 'bg-gradient-to-r from-red-100 to-red-300 text-red-900 border-red-300',
    expired: 'bg-gradient-to-r from-gray-100 to-gray-250 text-gray-700 border-gray-200',
    cancelled: 'bg-gradient-to-r from-slate-100 to-slate-250 text-slate-700 border-slate-200'
  }
  return gradientColors[status] || gradientColors.draft
}

// ✅ Classes de tamanho
const getSizeClasses = (size: 'sm' | 'md' | 'lg'): string => {
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  }
  return sizeClasses[size]
}

export function ProposalStatusBadge({ 
  status, 
  className,
  size = 'sm'
}: ProposalStatusBadgeProps) {
  const statusLabel = PROPOSAL_STATUS_LABELS[status] || status
  const gradientClasses = getStatusGradientClasses(status)
  const sizeClasses = getSizeClasses(size)
  
  return (
    <Badge 
      className={cn(
        'border-0 font-medium shadow-sm',
        gradientClasses,
        sizeClasses,
        className
      )}
    >
      {statusLabel}
    </Badge>
  )
} 