'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, X, Building2, Image, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/use-auth-v2';

interface BrandLogoUploadProps {
  value?: string; // URL atual do logo
  file?: File | null; // Arquivo selecionado (modo local)
  onChange: (logoUrl: string | null) => void;
  onFileChange?: (file: File | null) => void; // Callback para arquivo local
  disabled?: boolean;
  maxSizeMB?: number;
  acceptedTypes?: string[];
  className?: string;
  mode?: 'upload' | 'local'; // Modo: upload imediato ou seleção local
}

export function BrandLogoUpload({
  value,
  file,
  onChange,
  onFileChange,
  disabled = false,
  maxSizeMB = 5,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  className,
  mode = 'local' // Por padrão, modo local
}: BrandLogoUploadProps) {
  const { firebaseUser } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Verificar se está logado (só necessário para upload imediato)
  const isAuthenticatedAndReady = mode === 'local' || !!firebaseUser;
  const isDisabled = disabled || uploading || !isAuthenticatedAndReady;

  // Criar preview URL quando arquivo muda
  useEffect(() => {
    if (file && mode === 'local') {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      
      // Cleanup da URL anterior
      return () => {
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
        }
      };
    } else {
      setPreviewUrl(null);
    }
  }, [file, mode]);

  // Cleanup ao desmontar componente
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, []);

  const validateFile = useCallback((file: File): string | null => {
    // Verificar tipo de arquivo
    if (!acceptedTypes.includes(file.type)) {
      return `Tipo de arquivo não suportado. Use: ${acceptedTypes.join(', ')}`;
    }

    // Verificar tamanho
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `Arquivo muito grande. Máximo: ${maxSizeMB}MB`;
    }

    return null;
  }, [acceptedTypes, maxSizeMB]);

  const uploadFile = useCallback(async (file: File) => {
    try {
      setUploading(true);

      // Verificar autenticação
      if (!firebaseUser) {
        throw new Error('Usuário não autenticado');
      }

      // Validar arquivo
      const validationError = validateFile(file);
      if (validationError) {
        toast.error(validationError);
        return;
      }

      // Obter token de autenticação
      const token = await firebaseUser.getIdToken();

      // Preparar FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'brand-logo');

      console.log('[BRAND_LOGO_UPLOAD] Iniciando upload para usuário:', firebaseUser.uid);

      // Upload via API com token de autenticação
      const response = await fetch('/api/uploads/brands/logos', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Erro no upload');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Falha no upload');
      }

      // Notificar sucesso e atualizar valor
      onChange(result.url);
      toast.success('Logo enviado com sucesso!');

      console.log('[BRAND_LOGO_UPLOAD] Upload realizado com sucesso:', result.url);

    } catch (error) {
      console.error('Erro no upload:', error);
      let errorMessage = 'Erro no upload';
      
      if (error instanceof Error) {
        if (error.message.includes('Usuário não autenticado')) {
          errorMessage = 'Você precisa estar logado para enviar imagens';
        } else if (error.message.includes('file too large')) {
          errorMessage = 'Arquivo muito grande. Máximo 5MB';
        } else if (error.message.includes('invalid format')) {
          errorMessage = 'Formato de arquivo não suportado';
        } else {
          errorMessage = error.message;
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  }, [validateFile, onChange, firebaseUser]);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const selectedFile = files[0];
    
    // Validar arquivo antes de processar
    const validationError = validateFile(selectedFile);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    if (mode === 'local') {
      // Modo local: apenas notificar sobre o arquivo selecionado
      onFileChange?.(selectedFile);
      toast.success('Arquivo selecionado! Será enviado quando criar a marca.');
    } else {
      // Modo upload: fazer upload imediato
      uploadFile(selectedFile);
    }
  }, [mode, onFileChange, uploadFile, validateFile]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (isDisabled) return;

    const files = e.dataTransfer.files;
    handleFileSelect(files);
  }, [isDisabled, handleFileSelect]);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDisabled) {
      setDragActive(true);
    }
  }, [isDisabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const openFileDialog = useCallback(() => {
    if (!disabled && !uploading && isAuthenticatedAndReady) {
      fileInputRef.current?.click();
    }
  }, [disabled, uploading, isAuthenticatedAndReady]);

  const removeLogo = useCallback(() => {
    if (!disabled && !uploading && isAuthenticatedAndReady) {
      if (mode === 'local') {
        onFileChange?.(null);
        // Limpar preview
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
          setPreviewUrl(null);
        }
      } else {
        onChange(null);
      }
      toast.success('Logo removido');
    }
  }, [disabled, uploading, isAuthenticatedAndReady, mode, onFileChange, onChange, previewUrl]);

  return (
    <div className={cn("space-y-3", className)}>
      {/* Input file escondido */}
              <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
        disabled={isDisabled}
      />

      {/* Preview do logo atual ou arquivo selecionado */}
      {(value || previewUrl) && (
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="relative">
              <img
                src={previewUrl || value}
                alt="Logo da marca"
                className="w-16 h-16 rounded-lg object-cover bg-muted"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">
                {previewUrl ? 'Arquivo selecionado' : 'Logo atual'}
              </p>
              <p className="text-xs text-muted-foreground">
                {previewUrl 
                  ? `${file?.name || 'Arquivo'} (${((file?.size || 0) / 1024 / 1024).toFixed(1)}MB)`
                  : 'Clique abaixo para alterar ou arrastar nova imagem'
                }
              </p>
              {previewUrl && mode === 'local' && (
                <p className="text-xs text-[#ff0074] font-medium mt-1">
                  📎 Será enviado quando criar a marca
                </p>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={removeLogo}
              disabled={isDisabled}
              className="text-destructive hover:text-destructive"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      )}

      {/* Área de upload */}
      <Card
        className={cn(
          "border-2 border-dashed transition-colors duration-200 cursor-pointer",
          dragActive && "border-[#ff0074] bg-[#ff0074]/5",
          !dragActive && "border-muted-foreground/20 hover:border-[#ff0074]/50",
          isDisabled && "opacity-50 cursor-not-allowed",
          uploading && "cursor-wait"
        )}
        onDrop={handleDrop}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onClick={openFileDialog}
      >
        <div className="p-8 text-center space-y-4">
          {uploading ? (
            <div className="space-y-3">
              <Loader2 className="h-12 w-12 mx-auto text-[#ff0074] animate-spin" />
              <div>
                <p className="text-sm font-medium">Enviando logo...</p>
                <p className="text-xs text-muted-foreground">
                  Aguarde enquanto processamos sua imagem
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {dragActive ? (
                <Upload className="h-12 w-12 mx-auto text-[#ff0074]" />
              ) : (
                <Building2 className="h-12 w-12 mx-auto text-muted-foreground" />
              )}
              <div>
                <p className="text-sm font-medium">
                  {dragActive 
                    ? 'Solte a imagem aqui' 
                    : (value || previewUrl)
                      ? 'Clique ou arraste para alterar logo'
                      : 'Clique ou arraste uma imagem'
                  }
                </p>
                <p className="text-xs text-muted-foreground">
                  PNG, JPG, WEBP até {maxSizeMB}MB
                  {mode === 'local' && ' • Será enviado ao criar marca'}
                </p>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Informações adicionais */}
      <div className="text-xs text-muted-foreground space-y-1">
        {!isAuthenticatedAndReady && mode === 'upload' ? (
          <div className="p-3 rounded-md bg-amber-50 border border-amber-200 dark:bg-amber-900/20 dark:border-amber-800">
            <p className="text-amber-700 dark:text-amber-300 font-medium">
              ⚠️ Você precisa estar logado para enviar imagens
            </p>
          </div>
        ) : (
          <>
            <p>• Recomendado: imagens quadradas (1:1) ou horizontais (16:9)</p>
            <p>• Resolução mínima: 200x200px para melhor qualidade</p>
            {mode === 'local' ? (
              <>
                <p>• A imagem será enviada automaticamente ao criar a marca</p>
                <p>• Você pode alterar a seleção a qualquer momento antes de criar</p>
              </>
            ) : (
              <p>• A imagem será redimensionada automaticamente se necessário</p>
            )}
          </>
        )}
      </div>
    </div>
  );
} 


