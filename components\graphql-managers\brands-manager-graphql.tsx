'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Edit, Trash, X } from 'lucide-react'
import { useBrandsGraphQL, type BrandPrices, type Brand } from '@/hooks/use-brands-graphql'

interface BrandsManagerGraphQLProps {
  influencerId?: string
  defaultPrices?: BrandPrices
  readOnly?: boolean
}

const BrandsManagerGraphQL: React.FC<BrandsManagerGraphQLProps> = ({ 
  influencerId, 
  defaultPrices = {}, 
  readOnly = false 
}) => {
  const [showForm, setShowForm] = useState(false)
  const [brandName, setBrandName] = useState('')
  const [editingBrandId, setEditingBrandId] = useState<string | null>(null)
  const [prices, setPrices] = useState<BrandPrices>({
    instagramStory: { name: 'Stories', price: 0 },
    instagramReel: { name: 'Re<PERSON>', price: 0 },
    tiktokVideo: { name: 'Víde<PERSON>', price: 0 },
    youtubeInsertion: { name: 'Inserção', price: 0 },
    youtubeDedicated: { name: 'Dedicado', price: 0 },
    youtubeShorts: { name: 'Shorts', price: 0 },
    ...defaultPrices
  })

  // Hook GraphQL para gerenciar brands
  const {
    brands,
    isLoading,
    hasError,
    createBrand,
    updateBrand,
    deleteBrand,
    brandsError
  } = useBrandsGraphQL(influencerId)

  /** Reinicia formulário */
  const resetForm = () => {
    setBrandName('')
    setPrices({
      instagramStory: { name: 'Stories', price: 0 },
      instagramReel: { name: 'Reels', price: 0 },
      tiktokVideo: { name: 'Vídeo', price: 0 },
      youtubeInsertion: { name: 'Inserção', price: 0 },
      youtubeDedicated: { name: 'Dedicado', price: 0 },
      youtubeShorts: { name: 'Shorts', price: 0 },
      ...defaultPrices
    })
    setEditingBrandId(null)
    setShowForm(false)
  }

  /** Salva ou atualiza marca */
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!brandName.trim()) return

    try {
      if (editingBrandId) {
        await updateBrand(editingBrandId, {
          name: brandName.trim(),
          prices
        })
      } else {
        await createBrand({
          name: brandName.trim(),
          prices,
          influencerId,
          userId: '' // Será preenchido pelo hook
        })
      }
      resetForm()
    } catch (error) {
      console.error('Erro ao salvar marca:', error)
    }
  }

  /** Exclui marca */
  const handleDelete = async (id: string) => {
    if (!confirm('Deseja excluir esta marca?')) return
    
    try {
      await deleteBrand(id)
    } catch (error) {
      console.error('Erro ao excluir marca:', error)
    }
  }

  /** Preenche formulário para edição */
  const startEdit = (brand: Brand) => {
    setEditingBrandId(brand.id)
    setBrandName(brand.name)
    setPrices({
      instagramStory: { 
        name: brand.prices?.instagramStory?.name || 'Stories', 
        price: brand.prices?.instagramStory?.price || 0 
      },
      instagramReel: { 
        name: brand.prices?.instagramReel?.name || 'Reels', 
        price: brand.prices?.instagramReel?.price || 0 
      },
      tiktokVideo: { 
        name: brand.prices?.tiktokVideo?.name || 'Vídeo', 
        price: brand.prices?.tiktokVideo?.price || 0 
      },
      youtubeInsertion: { 
        name: brand.prices?.youtubeInsertion?.name || 'Inserção', 
        price: brand.prices?.youtubeInsertion?.price || 0 
      },
      youtubeDedicated: { 
        name: brand.prices?.youtubeDedicated?.name || 'Dedicado', 
        price: brand.prices?.youtubeDedicated?.price || 0 
      },
      youtubeShorts: { 
        name: brand.prices?.youtubeShorts?.name || 'Shorts', 
        price: brand.prices?.youtubeShorts?.price || 0 
      }
    })
    setShowForm(true)
  }

  /** Atualiza campo de preços */
  const updatePrice = (field: keyof BrandPrices, value: string) => {
    setPrices(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        price: Number(value) || 0
      }
    }))
  }

  if (hasError) {
    return (
      <div className="text-red-500 text-sm">
        Erro ao carregar marcas: {brandsError?.message}
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <img 
            src="/loader-dm.webp" 
            alt="Carregando marcas..."
            className="w-16 h-16 object-contain"
          />
        </div>
      ) : brands.length === 0 ? (
        <p className="text-xs text-muted-foreground">Nenhuma marca cadastrada.</p>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Marca</TableHead>
              <TableHead>Story</TableHead>
              <TableHead>Reel</TableHead>
              <TableHead>TikTok</TableHead>
              <TableHead>YouTube Insc.</TableHead>
              <TableHead>Dedicated</TableHead>
              <TableHead>Shorts</TableHead>
              {!readOnly && <TableHead className="w-px" />}
            </TableRow>
          </TableHeader>
          <TableBody>
            {brands.map(brand => (
              <TableRow key={brand.id} className="text-xs">
                <TableCell className="font-medium">{brand.name}</TableCell>
                <TableCell>R$ {brand.prices?.instagramStory?.price ?? 0}</TableCell>
                <TableCell>R$ {brand.prices?.instagramReel?.price ?? 0}</TableCell>
                <TableCell>R$ {brand.prices?.tiktokVideo?.price ?? 0}</TableCell>
                <TableCell>R$ {brand.prices?.youtubeInsertion?.price ?? 0}</TableCell>
                <TableCell>R$ {brand.prices?.youtubeDedicated?.price ?? 0}</TableCell>
                <TableCell>R$ {brand.prices?.youtubeShorts?.price ?? 0}</TableCell>
                {!readOnly && (
                  <TableCell className="flex gap-2">
                    <Button variant="ghost" size="icon" onClick={() => startEdit(brand)}>
                      <Edit size={14} />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDelete(brand.id)}>
                      <Trash size={14} />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {!readOnly && (
        showForm ? (
          <form onSubmit={handleSave} className="space-y-2 border rounded-lg p-4 bg-muted/10">
            <div className="flex items-center gap-2">
              <Input
                placeholder="Nome da marca"
                value={brandName}
                onChange={e => setBrandName(e.target.value)}
                className="flex-1"
              />
              <Button type="button" variant="ghost" onClick={resetForm}>
                <X size={16} />
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
              <Input
                type="number"
                placeholder="Story Insta"
                value={prices.instagramStory?.price || 0}
                onChange={e => updatePrice('instagramStory', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Reel Insta"
                value={prices.instagramReel?.price || 0}
                onChange={e => updatePrice('instagramReel', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Vídeo TikTok"
                value={prices.tiktokVideo?.price || 0}
                onChange={e => updatePrice('tiktokVideo', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Inserção YouTube"
                value={prices.youtubeInsertion?.price || 0}
                onChange={e => updatePrice('youtubeInsertion', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Dedicado YouTube"
                value={prices.youtubeDedicated?.price || 0}
                onChange={e => updatePrice('youtubeDedicated', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Shorts YouTube"
                value={prices.youtubeShorts?.price || 0}
                onChange={e => updatePrice('youtubeShorts', e.target.value)}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {editingBrandId ? 'Atualizar' : 'Adicionar'} Marca
            </Button>
          </form>
        ) : (
          <Button 
            variant="outline" 
            onClick={() => setShowForm(true)}
            className="w-full"
            disabled={isLoading}
          >
            + Adicionar Marca
          </Button>
        )
      )}
    </div>
  )
}

export default BrandsManagerGraphQL 

