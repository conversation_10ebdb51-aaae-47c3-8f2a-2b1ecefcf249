"use client"

import * as React from "react"
import { ColumnDef } from "@tanstack/react-table"
import {
  MoreHorizontal,
  Eye,
  MessageCircle,
  Mail,
  Phone,
  ExternalLink,
  Star,
  TrendingUp,
  Users,
  Heart,
  Play,
  Camera,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DataTable, createSortableColumn, createActionColumn } from "@/components/ui/data-table"
import { cn } from "@/lib/utils"

// Influencer data type
export interface Influencer {
  id: string
  name: string
  username: string
  avatar?: string
  city: string
  state: string
  category: string
  gender: "Masculino" | "Feminino" | "Outro"
  divulgaTrader: boolean
  socialNetworks: string[]
  instagram: {
    followers: number
    engagement: number
    likes: number
    storyPrice: number
    reelPrice: number
    storyViews: number
    reelViews: number
  }
  youtube: {
    subscribers: number
    views: number
    dedicatedPrice: number
    shortsPrice: number
    dedicatedViews: number
    shortsViews: number
  }
  tiktok: {
    followers: number
    likes: number
    videoPrice: number
    videoViews: number
  }
  responsible: string
  agency: string
  whatsapp: string
  email: string
  status: "Ativo" | "Inativo" | "Pendente" | "Bloqueado"
  notes?: string
}

// Format number helper
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// Format currency helper
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value)
}

// Status badge component
const StatusBadge = ({ status }: { status: Influencer['status'] }) => {
  const variants = {
    Ativo: "bg-green-100 text-green-800 border-green-200",
    Inativo: "bg-gray-100 text-gray-800 border-gray-200",
    Pendente: "bg-yellow-100 text-yellow-800 border-yellow-200",
    Bloqueado: "bg-red-100 text-red-800 border-red-200",
  }

  return (
    <Badge
      variant="outline"
      className={cn("text-xs font-medium", variants[status])}
    >
      {status}
    </Badge>
  )
}

// Social media metrics component
const SocialMetrics = ({ 
  platform, 
  followers, 
  engagement, 
  price, 
  views, 
  icon: Icon 
}: {
  platform: string
  followers: number
  engagement?: number
  price: number
  views: number
  icon: React.ElementType
}) => (
  <div className="space-y-1 p-2 bg-gray-50 rounded-md border">
    <div className="flex items-center gap-1 text-xs font-medium text-gray-600">
      <Icon className="h-3 w-3" />
      {platform}
    </div>
    <div className="space-y-0.5 text-xs">
      <div className="flex items-center gap-1">
        <Users className="h-3 w-3 text-blue-500" />
        <span>{formatNumber(followers)}</span>
      </div>
      {engagement && (
        <div className="flex items-center gap-1">
          <Heart className="h-3 w-3 text-red-500" />
          <span>{engagement.toFixed(1)}%</span>
        </div>
      )}
      <div className="flex items-center gap-1">
        <Eye className="h-3 w-3 text-green-500" />
        <span>{formatNumber(views)}</span>
      </div>
      <div className="font-medium text-gray-900">
        {formatCurrency(price)}
      </div>
    </div>
  </div>
)

// Define columns for the influencer table
export const influencerColumns: ColumnDef<Influencer>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="h-auto p-0 font-medium hover:bg-transparent"
      >
        Influencer
        <TrendingUp className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const influencer = row.original
      return (
        <div className="flex items-center space-x-3 min-w-[200px]">
          <Avatar className="h-10 w-10">
            <AvatarImage src={influencer.avatar} alt={influencer.name} />
            <AvatarFallback>
              {influencer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-gray-900">{influencer.name}</div>
            <div className="text-sm text-gray-500">@{influencer.username}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: "location",
    header: "Localização",
    cell: ({ row }) => {
      const { city, state } = row.original
      return (
        <div className="min-w-[120px]">
          <div className="text-sm font-medium">{city}</div>
          <div className="text-xs text-gray-500">{state}</div>
        </div>
      )
    },
  },
  createSortableColumn<Influencer, string>("category", "Categoria"),
  {
    accessorKey: "divulgaTrader",
    header: "Trader",
    cell: ({ row }) => (
      <Badge variant={row.original.divulgaTrader ? "default" : "secondary"}>
        {row.original.divulgaTrader ? "Sim" : "Não"}
      </Badge>
    ),
  },
  createSortableColumn<Influencer, string>("gender", "Gênero"),
  {
    accessorKey: "socialNetworks",
    header: "Redes Sociais",
    cell: ({ row }) => {
      const networks = row.original.socialNetworks
      return (
        <div className="flex flex-wrap gap-1 min-w-[100px]">
          {networks.map((network) => (
            <Badge key={network} variant="outline" className="text-xs">
              {network}
            </Badge>
          ))}
        </div>
      )
    },
  },
  {
    accessorKey: "instagram",
    header: "Instagram",
    cell: ({ row }) => {
      const { instagram } = row.original
      return (
        <div className="min-w-[140px]">
          <SocialMetrics
            platform="Instagram"
            followers={instagram.followers}
            engagement={instagram.engagement}
            price={instagram.storyPrice}
            views={instagram.storyViews}
            icon={Camera}
          />
        </div>
      )
    },
  },
  {
    accessorKey: "youtube",
    header: "YouTube",
    cell: ({ row }) => {
      const { youtube } = row.original
      return (
        <div className="min-w-[140px]">
          <SocialMetrics
            platform="YouTube"
            followers={youtube.subscribers}
            price={youtube.dedicatedPrice}
            views={youtube.dedicatedViews}
            icon={Play}
          />
        </div>
      )
    },
  },
  {
    accessorKey: "tiktok",
    header: "TikTok",
    cell: ({ row }) => {
      const { tiktok } = row.original
      return (
        <div className="min-w-[140px]">
          <SocialMetrics
            platform="TikTok"
            followers={tiktok.followers}
            price={tiktok.videoPrice}
            views={tiktok.videoViews}
            icon={Star}
          />
        </div>
      )
    },
  },
  {
    accessorKey: "responsible",
    header: "Responsável",
    cell: ({ row }) => (
      <div className="min-w-[120px] text-sm">{row.original.responsible}</div>
    ),
  },
  {
    accessorKey: "agency",
    header: "Agência",
    cell: ({ row }) => (
      <div className="min-w-[100px] text-sm">{row.original.agency}</div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <StatusBadge status={row.original.status} />,
  },
  createActionColumn<Influencer>((influencer) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Ações</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(influencer.id)}
        >
          Copiar ID
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <Eye className="mr-2 h-4 w-4" />
          Ver perfil
        </DropdownMenuItem>
        <DropdownMenuItem>
          <MessageCircle className="mr-2 h-4 w-4" />
          WhatsApp
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Mail className="mr-2 h-4 w-4" />
          Enviar email
        </DropdownMenuItem>
        <DropdownMenuItem>
          <ExternalLink className="mr-2 h-4 w-4" />
          Ver redes sociais
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )),
]

// Main component
interface InfluencerDataTableProps {
  data: Influencer[]
  onRowSelectionChange?: (selectedRows: Influencer[]) => void
}

export function InfluencerDataTable({ 
  data, 
  onRowSelectionChange 
}: InfluencerDataTableProps) {
  return (
    <DataTable
      columns={influencerColumns}
      data={data}
      searchKey="name"
      searchPlaceholder="Buscar influenciadores..."
      enableRowSelection={true}
      enableColumnVisibility={true}
      enablePagination={true}
      enableSorting={true}
      enableFiltering={true}
      pageSize={20}
      onRowSelectionChange={onRowSelectionChange}
      className="w-full"
    />
  )
}

// Sample data for testing
export const sampleInfluencers: Influencer[] = [
  {
    id: "1",
    name: "Ana Silva",
    username: "anasilva",
    avatar: "/avatars/ana.jpg",
    city: "São Paulo",
    state: "SP",
    category: "Lifestyle",
    gender: "Feminino",
    divulgaTrader: true,
    socialNetworks: ["Instagram", "YouTube", "TikTok"],
    instagram: {
      followers: 150000,
      engagement: 4.2,
      likes: 8500,
      storyPrice: 2500,
      reelPrice: 4000,
      storyViews: 45000,
      reelViews: 120000,
    },
    youtube: {
      subscribers: 85000,
      views: 2500000,
      dedicatedPrice: 8000,
      shortsPrice: 3000,
      dedicatedViews: 180000,
      shortsViews: 95000,
    },
    tiktok: {
      followers: 220000,
      likes: 1200000,
      videoPrice: 3500,
      videoViews: 850000,
    },
    responsible: "João Santos",
    agency: "Influencer Agency",
    whatsapp: "+5511999999999",
    email: "<EMAIL>",
    status: "Ativo",
    notes: "Influenciadora de lifestyle com boa performance",
  },
  {
    id: "2",
    name: "Carlos Mendes",
    username: "carlosmendes",
    city: "Rio de Janeiro",
    state: "RJ",
    category: "Fitness",
    gender: "Masculino",
    divulgaTrader: false,
    socialNetworks: ["Instagram", "YouTube"],
    instagram: {
      followers: 95000,
      engagement: 5.8,
      likes: 12000,
      storyPrice: 1800,
      reelPrice: 3200,
      storyViews: 28000,
      reelViews: 85000,
    },
    youtube: {
      subscribers: 45000,
      views: 1200000,
      dedicatedPrice: 5500,
      shortsPrice: 2200,
      dedicatedViews: 95000,
      shortsViews: 42000,
    },
    tiktok: {
      followers: 0,
      likes: 0,
      videoPrice: 0,
      videoViews: 0,
    },
    responsible: "Maria Costa",
    agency: "Fitness Creators",
    whatsapp: "+5521888888888",
    email: "<EMAIL>",
    status: "Ativo",
  },
  {
    id: "3",
    name: "Beatriz Oliveira",
    username: "biaoliveira",
    city: "Belo Horizonte",
    state: "MG",
    category: "Beleza",
    gender: "Feminino",
    divulgaTrader: true,
    socialNetworks: ["Instagram", "TikTok"],
    instagram: {
      followers: 320000,
      engagement: 3.9,
      likes: 18500,
      storyPrice: 4200,
      reelPrice: 6800,
      storyViews: 95000,
      reelViews: 280000,
    },
    youtube: {
      subscribers: 0,
      views: 0,
      dedicatedPrice: 0,
      shortsPrice: 0,
      dedicatedViews: 0,
      shortsViews: 0,
    },
    tiktok: {
      followers: 450000,
      likes: 2800000,
      videoPrice: 5200,
      videoViews: 1200000,
    },
    responsible: "Pedro Lima",
    agency: "Beauty Influence",
    whatsapp: "+5531777777777",
    email: "<EMAIL>",
    status: "Pendente",
  },
]

