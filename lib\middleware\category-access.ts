/**
 * 🔒 MIDDLEWARE UNIVERSAL DE ACESSO A CATEGORIAS
 * Middleware compartilhado entre GraphQL e REST APIs
 * Garante isolamento de dados e validações de segurança consistentes
 */

import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { CategoryService } from '@/services/category-service-unified';

// ===== TIPOS =====

export interface CategoryAccessContext {
  userId: string;
  isAuthenticated: boolean;
  userRole?: string;
}

export type CategoryHandler<T = any> = (
  req: NextRequest | any, 
  userId: string, 
  context: CategoryAccessContext
) => Promise<T>;

export type GraphQLCategoryHandler<T = any> = (
  parent: any,
  args: any,
  context: CategoryAccessContext
) => Promise<T>;

// ===== MIDDLEWARE PARA REST APIs =====

/**
 * 🔒 MIDDLEWARE PARA ROTAS REST DE CATEGORIAS
 * Valida autenticação e fornece contexto seguro
 */
export function withCategoryAccess<T>(handler: CategoryHandler<T>) {
  return async (req: NextRequest): Promise<T> => {
    try {
      console.log(`🔒 [SECURITY] withCategoryAccess iniciado para ${req.method} ${req.url}`);
      
      // 🚨 VALIDAÇÃO DE AUTENTICAÇÃO
      const { userId: clerkUserId } = await auth();
      
      if (!clerkUserId) {
        console.warn('⚠️ [SECURITY] Tentativa de acesso sem autenticação');
        throw new Error('Usuário não autenticado');
      }

      // 🔒 CRIAR CONTEXTO SEGURO
      const context: CategoryAccessContext = {
        userId: clerkUserId,
        isAuthenticated: true,
        userRole: 'user' // Pode ser expandido futuramente
      };

      console.log(`✅ [SECURITY] Usuário autenticado: ${clerkUserId}`);
      
      // 🎯 EXECUTAR HANDLER COM CONTEXTO SEGURO
      return await handler(req, clerkUserId, context);

    } catch (error) {
      console.error('❌ [SECURITY] Erro em withCategoryAccess:', error);
      throw error;
    }
  };
}

// ===== MIDDLEWARE PARA GRAPHQL RESOLVERS =====

/**
 * 🔒 MIDDLEWARE PARA RESOLVERS GRAPHQL DE CATEGORIAS
 * Valida userId e fornece contexto seguro
 */
export function withGraphQLCategoryAccess<T>(handler: GraphQLCategoryHandler<T>) {
  return async (parent: any, args: any, graphqlContext: any): Promise<T> => {
    try {
      console.log(`🔒 [SECURITY] withGraphQLCategoryAccess iniciado para resolver`);
      
      // 🚨 VALIDAÇÃO DE USERID NOS ARGUMENTOS
      const { userId } = args;
      
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.warn('⚠️ [SECURITY] Resolver GraphQL chamado sem userId válido');
        throw new Error('UserId é obrigatório para operações de categoria');
      }

      // 🔒 CRIAR CONTEXTO SEGURO
      const context: CategoryAccessContext = {
        userId: userId,
        isAuthenticated: true,
        userRole: 'user' // Pode ser expandido futuramente
      };

      console.log(`✅ [SECURITY] Resolver GraphQL autorizado para userId: ${userId}`);
      
      // 🎯 EXECUTAR HANDLER COM CONTEXTO SEGURO
      return await handler(parent, args, context);

    } catch (error) {
      console.error('❌ [SECURITY] Erro em withGraphQLCategoryAccess:', error);
      throw error;
    }
  };
}

// ===== VALIDADORES ESPECÍFICOS =====

/**
 * 🔒 VALIDAR ACESSO A CATEGORIA ESPECÍFICA
 * Verifica se o usuário tem permissão para acessar uma categoria
 */
export async function validateCategoryAccess(
  categoryId: string, 
  userId: string, 
  operation: 'read' | 'write' | 'delete' = 'read'
): Promise<boolean> {
  try {
    console.log(`🔒 [SECURITY] Validando acesso ${operation} à categoria ${categoryId} para userId: ${userId}`);
    
    // Categoria especial "all" é sempre acessível para leitura
    if (categoryId === 'all' && operation === 'read') {
      return true;
    }

    // Buscar categoria usando o serviço unificado
    const category = await CategoryService.getCategoryById(categoryId, userId);
    
    if (!category) {
      console.warn(`⚠️ [SECURITY] Categoria ${categoryId} não encontrada ou sem acesso`);
      return false;
    }

    // Para operações de escrita/delete, só permitir categorias do próprio usuário
    if ((operation === 'write' || operation === 'delete')) {
      const isUserCategory = category.userId === userId;
      
      if (!isUserCategory) {
        console.warn(`⚠️ [SECURITY] Tentativa de ${operation} em categoria que não pertence ao usuário`);
        return false;
      }
    }

    console.log(`✅ [SECURITY] Acesso ${operation} autorizado para categoria ${categoryId}`);
    return true;

  } catch (error) {
    console.error('❌ [SECURITY] Erro em validateCategoryAccess:', error);
    return false;
  }
}

/**
 * 🔒 VALIDAR DADOS DE ENTRADA PARA CATEGORIA
 * Sanitiza e valida dados antes de operações
 */
export function validateCategoryInput(input: any, operation: 'create' | 'update'): any {
  try {
    console.log(`🔒 [SECURITY] Validando input para operação ${operation}`);
    
    const sanitized: any = {};

    // Validar nome (obrigatório para create)
    if (operation === 'create' && (!input.name || typeof input.name !== 'string')) {
      throw new Error('Nome da categoria é obrigatório');
    }
    
    if (input.name !== undefined) {
      if (typeof input.name !== 'string') {
        throw new Error('Nome deve ser uma string');
      }
      
      const trimmedName = input.name.trim();
      if (trimmedName.length === 0) {
        throw new Error('Nome não pode estar vazio');
      }
      
      if (trimmedName.length > 100) {
        throw new Error('Nome não pode ter mais de 100 caracteres');
      }
      
      sanitized.name = trimmedName;
    }

    // Validar slug (opcional)
    if (input.slug !== undefined) {
      if (typeof input.slug !== 'string') {
        throw new Error('Slug deve ser uma string');
      }
      
      const trimmedSlug = input.slug.trim();
      if (trimmedSlug.length > 100) {
        throw new Error('Slug não pode ter mais de 100 caracteres');
      }
      
      // Validar formato do slug
      if (trimmedSlug && !/^[a-z0-9-]+$/.test(trimmedSlug)) {
        throw new Error('Slug deve conter apenas letras minúsculas, números e hífens');
      }
      
      sanitized.slug = trimmedSlug || undefined;
    }

    // Validar descrição (opcional)
    if (input.description !== undefined) {
      if (input.description !== null && typeof input.description !== 'string') {
        throw new Error('Descrição deve ser uma string ou null');
      }
      
      if (input.description && input.description.length > 500) {
        throw new Error('Descrição não pode ter mais de 500 caracteres');
      }
      
      sanitized.description = input.description?.trim() || null;
    }

    // Validar cor (opcional)
    if (input.color !== undefined) {
      if (input.color !== null && typeof input.color !== 'string') {
        throw new Error('Cor deve ser uma string ou null');
      }
      
      // Validar formato de cor hex (opcional)
      if (input.color && !/^#[0-9A-Fa-f]{6}$/.test(input.color)) {
        console.warn(`⚠️ [SECURITY] Cor fornecida não está no formato hex válido: ${input.color}`);
      }
      
      sanitized.color = input.color || null;
    }

    // Validar isActive (opcional)
    if (input.isActive !== undefined) {
      if (typeof input.isActive !== 'boolean') {
        throw new Error('isActive deve ser um boolean');
      }
      
      sanitized.isActive = input.isActive;
    }

    console.log(`✅ [SECURITY] Input validado com sucesso para operação ${operation}`);
    return sanitized;

  } catch (error) {
    console.error('❌ [SECURITY] Erro em validateCategoryInput:', error);
    throw error;
  }
}

/**
 * 🔒 LOG DE AUDITORIA PARA OPERAÇÕES DE CATEGORIA
 * Registra operações sensíveis para auditoria
 */
export function logCategoryOperation(
  operation: string,
  categoryId: string | null,
  userId: string,
  success: boolean,
  details?: any
): void {
  const timestamp = new Date().toISOString();
  const logLevel = success ? '✅' : '❌';
  
  console.log(
    `${logLevel} [SECURITY AUDIT] ${timestamp} - ${operation} | ` +
    `CategoryId: ${categoryId || 'N/A'} | UserId: ${userId} | ` +
    `Success: ${success} | Details: ${JSON.stringify(details || {})}`
  );
}
