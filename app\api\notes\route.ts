import { NextResponse } from 'next/server';
import { 
  getNotesByInfluencerId, 
  getNoteById, 
  addNote, 
  updateNote, 
  deleteNote,
  getAllNotes 
} from '@/lib/firebase-notes';

export const dynamic = 'force-dynamic';

// Rota GET para buscar todas as anotações, as de um influenciador específico ou uma anotação específica
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  const influencerId = searchParams.get('influencerId');
  const all = searchParams.get('all');

  try {
    if (id) {
      // Buscar uma anotação específica pelo ID
      const note = await getNoteById(id);
      
      if (!note) {
        return NextResponse.json(
          { error: 'Anotação não encontrada' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(note);
    } else if (influencerId) {
      try {
        // Buscar todas as anotações de um influenciador
        const notes = await getNotesByInfluencerId(influencerId);
        return NextResponse.json(notes);
      } catch (error) {
        // Se ocorrer erro com o índice do Firestore, retornar array vazio
        console.error(`Erro ao buscar anotações para o influenciador ${influencerId}:`, error);
        // Retornamos um array vazio em vez de erro para não quebrar a UI
        return NextResponse.json([]);
      }
    } else if (all === 'true') {
      // Buscar todas as anotações do sistema
      try {
        const notes = await getAllNotes();
        return NextResponse.json(notes);
      } catch (error) {
        console.error('Erro ao buscar todas as anotações:', error);
        return NextResponse.json([]);
      }
    } else {
      // É necessário fornecer um ID de influenciador ou o parâmetro all=true
      return NextResponse.json(
        { error: 'É necessário fornecer um ID de influenciador ou o parâmetro all=true' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Erro ao buscar anotações:', error);
    // Retornamos um array vazio em vez de erro para não quebrar a UI
    return NextResponse.json([]);
  }
}

// Rota POST para adicionar uma nova anotação
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Validar dados
    if (!data.influencerId || !data.title || !data.content) {
      return NextResponse.json(
        { error: 'ID do influenciador, título e conteúdo são obrigatórios' },
        { status: 400 }
      );
    }
    
    // Adicionar anotação
    const noteId = await addNote(
      data.influencerId,
      {
        title: data.title,
        content: data.content,
        type: data.type || 'geral',
        influencerId: data.influencerId
      },
      data.createdBy
    );
    
    return NextResponse.json({ 
      success: true, 
      id: noteId,
      message: 'Anotação criada com sucesso'
    }, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar anotação:', error);
    return NextResponse.json(
      { error: 'Erro ao criar anotação' },
      { status: 500 }
    );
  }
}

// Rota PUT para atualizar uma anotação existente
export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID da anotação é obrigatório' },
        { status: 400 }
      );
    }
    
    const data = await request.json();
    
    // Verificar se a anotação existe
    const note = await getNoteById(id);
    if (!note) {
      return NextResponse.json(
        { error: 'Anotação não encontrada' },
        { status: 404 }
      );
    }
    
    // Atualizar anotação
    await updateNote(id, {
      title: data.title,
      content: data.content,
      type: data.type
    });
    
    return NextResponse.json({ 
      success: true,
      message: 'Anotação atualizada com sucesso'
    });
  } catch (error) {
    console.error('Erro ao atualizar anotação:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar anotação' },
      { status: 500 }
    );
  }
}

// Rota DELETE para excluir uma anotação
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID da anotação é obrigatório' },
        { status: 400 }
      );
    }
    
    // Excluir anotação
    await deleteNote(id);
    
    return NextResponse.json({ 
      success: true,
      message: 'Anotação excluída com sucesso'
    });
  } catch (error) {
    console.error('Erro ao excluir anotação:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir anotação' },
      { status: 500 }
    );
  }
}


