'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  Upload, 
  Database, 
  AlertTriangle, 
  RefreshCw,
  Cloud,
  HardDrive
} from 'lucide-react';

interface AvatarStatus {
  total: number;
  localAvatars: number;
  firebaseAvatars: number;
  noAvatars: number;
  needsMigration: boolean;
}

interface MigrationResult {
  id: string;
  name: string;
  status: 'success' | 'error';
  newUrl?: string;
  error?: string;
}

interface MigrationResponse {
  success: boolean;
  message: string;
  migrated: number;
  errors: number;
  results: MigrationResult[];
}

export function AvatarMigrationPanel() {
  const [status, setStatus] = useState<AvatarStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResults, setMigrationResults] = useState<MigrationResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Carregar status inicial
  useEffect(() => {
    loadStatus();
  }, []);

  const loadStatus = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/migrate-avatars');
      if (!response.ok) {
        throw new Error('Erro ao carregar status dos avatares');
      }
      
      const data = await response.json();
      setStatus(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const startMigration = async () => {
    setIsMigrating(true);
    setError(null);
    setMigrationResults(null);
    
    try {
      const response = await fetch('/api/admin/migrate-avatars', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro na migração dos avatares');
      }
      
      const data = await response.json();
      setMigrationResults(data);
      
      // Recarregar status após migração
      await loadStatus();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsMigrating(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Carregando Status dos Avatares
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Verificando status dos avatares...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status dos Avatares */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Status dos Avatares
          </CardTitle>
          <CardDescription>
            Visualize a distribuição atual dos avatares dos influenciadores
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {status && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{status.total}</div>
                <div className="text-sm text-muted-foreground">Total de Influenciadores</div>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="flex items-center justify-center gap-1 text-2xl font-bold text-orange-600">
                  <HardDrive className="h-5 w-5" />
                  {status.localAvatars}
                </div>
                <div className="text-sm text-muted-foreground">Avatares Locais</div>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="flex items-center justify-center gap-1 text-2xl font-bold text-green-600">
                  <Cloud className="h-5 w-5" />
                  {status.firebaseAvatars}
                </div>
                <div className="text-sm text-muted-foreground">Avatares no Firebase</div>
              </div>
              
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-gray-600">{status.noAvatars}</div>
                <div className="text-sm text-muted-foreground">Sem Avatar</div>
              </div>
            </div>
          )}
          
          <div className="flex items-center justify-between mt-6">
            <Button 
              onClick={loadStatus} 
              variant="outline" 
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Atualizar Status
            </Button>
            
            {status?.needsMigration && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                Migração Necessária
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Painel de Migração */}
      {status?.needsMigration && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Migração para Firebase Storage
            </CardTitle>
            <CardDescription>
              Migre os avatares armazenados localmente para o Firebase Storage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Esta operação irá transferir {status.localAvatars} avatares do armazenamento local 
                para o Firebase Storage. O processo pode levar alguns minutos.
              </AlertDescription>
            </Alert>
            
            <div className="flex items-center gap-4">
              <Button 
                onClick={startMigration}
                disabled={isMigrating}
                className="flex items-center gap-2"
              >
                {isMigrating ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4" />
                )}
                {isMigrating ? 'Migrando...' : 'Iniciar Migração'}
              </Button>
              
              {isMigrating && (
                <div className="flex-1">
                  <div className="text-sm text-muted-foreground mb-2">Migração em andamento...</div>
                  <Progress value={undefined} className="w-full" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resultados da Migração */}
      {migrationResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {migrationResults.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              Resultados da Migração
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert>
                <AlertDescription>{migrationResults.message}</AlertDescription>
              </Alert>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 border rounded-lg bg-green-50 dark:bg-green-950">
                  <div className="text-2xl font-bold text-green-600">{migrationResults.migrated}</div>
                  <div className="text-sm text-muted-foreground">Migrados com Sucesso</div>
                </div>
                
                <div className="text-center p-4 border rounded-lg bg-red-50 dark:bg-red-950">
                  <div className="text-2xl font-bold text-red-600">{migrationResults.errors}</div>
                  <div className="text-sm text-muted-foreground">Erros</div>
                </div>
              </div>
              
              {migrationResults.results.length > 0 && (
                <div>
                  <Separator className="my-4" />
                  <h4 className="font-medium mb-3">Detalhes da Migração</h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {migrationResults.results.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          {result.status === 'success' ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span className="text-sm">{result.name}</span>
                        </div>
                        
                        <div className="text-right">
                          {result.status === 'success' ? (
                            <Badge variant="default">Sucesso</Badge>
                          ) : (
                            <Badge variant="destructive">Erro</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

