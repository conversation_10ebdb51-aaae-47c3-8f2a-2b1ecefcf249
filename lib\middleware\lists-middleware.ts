import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation, withOwnershipValidation } from '@/lib/middleware/user-isolation';
import { validateRequestBody, validateQueryParams } from '@/lib/middleware/validation';
import { ListService } from '@/services/list-service';
import { 
  Lista, 
  CriarListaData, 
  AtualizarListaData, 
  AdicionarItemListaData,
  FiltrosLista 
} from '@/types/list';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';
import {
  CreateListaSchema,
  UpdateListaSchema,
  AdicionarItemListaSchema,
  ListaFiltersSchema,
  validateCriteriosForTipoObjeto,
  validateItemCompatibility,
  sanitizeListaData
} from '@/lib/validation/list';

/**
 * 📝 MIDDLEWARE ESPECÍFICO PARA LISTAS
 * Implementa validações e segurança para operações com listas
 */

/**
 * Middleware para criação de listas
 */
export function withCreateListValidation(
  handler: (req: NextRequest, userId: string, context: any, data: CriarListaData) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: any) => {
    try {
      // Validar body da requisição
      const validation = await validateRequestBody(CreateListaSchema)(req);
      
      if (!validation.success) {
        return NextResponse.json(
          { 
            error: validation.error,
            errors: validation.errors 
          },
          { status: 400 }
        );
      }

      // Sanitizar e validar dados
      const sanitizedData = sanitizeListaData(validation.data!);

      // Validar regras de negócio específicas para critérios
      if (sanitizedData.criterios && sanitizedData.criterios.length > 0) {
        const criteriosValidation = validateCriteriosForTipoObjeto(sanitizedData.criterios, sanitizedData.tipoObjeto);
        if (!criteriosValidation.isValid) {
          return NextResponse.json(
            { 
              error: 'Critérios inválidos',
              errors: criteriosValidation.errors 
            },
            { status: 400 }
          );
        }
      }

      // Log da tentativa de criação
      IsolationUtils.logIsolationEvent(
        'create',
        'lists',
        'new',
        userId,
        { 
          nome: sanitizedData.nome,
          tipoLista: sanitizedData.tipoLista,
          tipoObjeto: sanitizedData.tipoObjeto
        }
      );

      return await handler(req, userId, context, sanitizedData);

    } catch (error: any) {
      console.error('[LIST_MIDDLEWARE_CREATE]', { userId, error });
      return NextResponse.json(
        { error: 'Erro interno na validação de criação' },
        { status: 500 }
      );
    }
  });
}

/**
 * Middleware para busca de lista por ID com validação de ownership
 */
export function withListOwnershipValidation(
  handler: (req: NextRequest, userId: string, context: any, lista: Lista) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: any) => {
    try {
      // Extrair ID da lista da URL
      const url = new URL(req.url);
      const listaId = url.pathname.split('/').slice(-1)[0];
      
      if (!listaId) {
        return NextResponse.json(
          { error: 'ID da lista é obrigatório' },
          { status: 400 }
        );
      }

      // Buscar e validar ownership da lista
      const lista = await ListService.buscarListaPorId(listaId, userId);
      
      if (!lista) {
        return NextResponse.json(
          { error: 'Lista não encontrada' },
          { status: 404 }
        );
      }

      return await handler(req, userId, context, lista);

    } catch (error: any) {
      if (error instanceof IsolationError) {
        return NextResponse.json(
          { 
            error: error.message,
            code: error.code 
          },
          { status: 403 }
        );
      }

      console.error('[LIST_MIDDLEWARE_OWNERSHIP]', { userId, error });
      return NextResponse.json(
        { error: 'Erro interno na validação de ownership' },
        { status: 500 }
      );
    }
  });
}

/**
 * Middleware para atualização de listas
 */
export function withUpdateListValidation(
  handler: (req: NextRequest, userId: string, context: any, listaId: string, data: AtualizarListaData) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: any) => {
    try {
      // Extrair ID da lista da URL
      const url = new URL(req.url);
      const listaId = url.pathname.split('/').pop();
      
      if (!listaId) {
        return NextResponse.json(
          { error: 'ID da lista é obrigatório' },
          { status: 400 }
        );
      }

      // Validar ownership da lista
      const lista = await ListService.buscarListaPorId(listaId, userId);
      if (!lista) {
        return NextResponse.json(
          { error: 'Lista não encontrada' },
          { status: 404 }
        );
      }

      // Validar body da requisição
      const validation = await validateRequestBody(UpdateListaSchema)(req);
      
      if (!validation.success) {
        return NextResponse.json(
          { 
            error: validation.error,
            errors: validation.errors 
          },
          { status: 400 }
        );
      }

      // Validar critérios se fornecidos
      if (validation.data!.criterios && validation.data!.criterios.length > 0) {
        const criteriosValidation = validateCriteriosForTipoObjeto(validation.data!.criterios, lista.tipoObjeto);
        if (!criteriosValidation.isValid) {
          return NextResponse.json(
            { 
              error: 'Critérios inválidos',
              errors: criteriosValidation.errors 
            },
            { status: 400 }
          );
        }
      }

      // Log da tentativa de atualização
      IsolationUtils.logIsolationEvent(
        'update',
        'lists',
        listaId,
        userId,
        { 
          camposAlterados: Object.keys(validation.data!),
          nomeAtual: lista.nome
        }
      );

      return await handler(req, userId, context, listaId, validation.data!);

    } catch (error: any) {
      if (error instanceof IsolationError) {
        return NextResponse.json(
          { 
            error: error.message,
            code: error.code 
          },
          { status: 403 }
        );
      }

      console.error('[LIST_MIDDLEWARE_UPDATE]', { userId, error });
      return NextResponse.json(
        { error: 'Erro interno na validação de atualização' },
        { status: 500 }
      );
    }
  });
}

/**
 * Middleware para busca de listas do usuário com filtros
 */
export function withListFiltersValidation(
  handler: (req: NextRequest, userId: string, context: any, filtros: FiltrosLista) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: any) => {
    try {
      // Validar query parameters
      const validation = validateQueryParams(ListaFiltersSchema)(req);
      
      if (!validation.success) {
        return NextResponse.json(
          { 
            error: validation.error,
            errors: validation.errors 
          },
          { status: 400 }
        );
      }

      const filtros = validation.data! as FiltrosLista;

      // Log da busca
      IsolationUtils.logIsolationEvent(
        'read',
        'lists',
        'search',
        userId,
        { 
          filtros,
          userAgent: req.headers.get('user-agent')
        }
      );

      return await handler(req, userId, context, filtros);

    } catch (error: any) {
      console.error('[LIST_MIDDLEWARE_FILTERS]', { userId, error });
      return NextResponse.json(
        { error: 'Erro interno na validação de filtros' },
        { status: 500 }
      );
    }
  });
}

/**
 * Middleware para adição de itens à lista
 */
export function withAddItemToListValidation(
  handler: (req: NextRequest, userId: string, context: any, listaId: string, data: AdicionarItemListaData) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: any) => {
    try {
      // Extrair ID da lista da URL
      const url = new URL(req.url);
      const pathParts = url.pathname.split('/');
      const listaId = pathParts[pathParts.indexOf('lists') + 1];
      
      if (!listaId) {
        return NextResponse.json(
          { error: 'ID da lista é obrigatório' },
          { status: 400 }
        );
      }

      // Validar ownership da lista
      const lista = await ListService.buscarListaPorId(listaId, userId);
      if (!lista) {
        return NextResponse.json(
          { error: 'Lista não encontrada' },
          { status: 404 }
        );
      }

      // Validar body da requisição
      const validation = await validateRequestBody(AdicionarItemListaSchema)(req);
      
      if (!validation.success) {
        return NextResponse.json(
          { 
            error: validation.error,
            errors: validation.errors 
          },
          { status: 400 }
        );
      }

      const itemData: AdicionarItemListaData = {
        listaId,
        ...validation.data!
      };

      // Validar se o tipo do item é compatível com o tipo da lista
      if (!validateItemCompatibility(lista.tipoObjeto, itemData.tipoItem)) {
        return NextResponse.json(
          { 
            error: `Tipo de item '${itemData.tipoItem}' não é compatível com listas de '${lista.tipoObjeto}'`
          },
          { status: 400 }
        );
      }

      // Log da tentativa de adição
      IsolationUtils.logIsolationEvent(
        'create',
        'list_items',
        itemData.itemId,
        userId,
        { 
          listaId,
          listaName: lista.nome,
          tipoItem: itemData.tipoItem
        }
      );

      return await handler(req, userId, context, listaId, itemData);

    } catch (error: any) {
      if (error instanceof IsolationError) {
        return NextResponse.json(
          { 
            error: error.message,
            code: error.code 
          },
          { status: 403 }
        );
      }

      console.error('[LIST_MIDDLEWARE_ADD_ITEM]', { userId, error });
      return NextResponse.json(
        { error: 'Erro interno na validação de adição de item' },
        { status: 500 }
      );
    }
  });
}

/**
 * Middleware para operações em lote
 */
export function withBatchListOperationValidation(
  handler: (req: NextRequest, userId: string, context: any, data: any) => Promise<NextResponse>
) {
  return withUserIsolation(async (req: NextRequest, userId: string, context: any) => {
    try {
      const body = await req.json();
      
      if (!body.listaIds || !Array.isArray(body.listaIds) || body.listaIds.length === 0) {
        return NextResponse.json(
          { error: 'Lista de IDs é obrigatória' },
          { status: 400 }
        );
      }

      if (!body.operacao || !['arquivar', 'deletar', 'compartilhar'].includes(body.operacao)) {
        return NextResponse.json(
          { error: 'Operação inválida' },
          { status: 400 }
        );
      }

      // Validar se o usuário tem permissão para todas as listas
      const validationPromises = body.listaIds.map(async (listaId: string) => {
        try {
          const lista = await ListService.buscarListaPorId(listaId, userId);
          return lista ? { id: listaId, valid: true } : { id: listaId, valid: false, error: 'Lista não encontrada' };
        } catch (error: any) {
          return { id: listaId, valid: false, error: error.message };
        }
      });

      const validationResults = await Promise.all(validationPromises);
      const invalidLists = validationResults.filter(result => !result.valid);

      if (invalidLists.length > 0) {
        return NextResponse.json(
          { 
            error: 'Algumas listas não podem ser processadas',
            invalidLists 
          },
          { status: 403 }
        );
      }

      // Log da operação em lote
      IsolationUtils.logIsolationEvent(
        'update',
        'lists',
        'batch_operation',
        userId,
        { 
          operacao: body.operacao,
          totalListas: body.listaIds.length,
          listaIds: body.listaIds
        }
      );

      return await handler(req, userId, context, body);

    } catch (error: any) {
      console.error('[LIST_MIDDLEWARE_BATCH]', { userId, error });
      return NextResponse.json(
        { error: 'Erro interno na validação de operação em lote' },
        { status: 500 }
      );
    }
  });
} 

