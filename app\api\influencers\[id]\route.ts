import { NextRequest, NextResponse } from 'next/server';
import { getInfluencerById } from '@/lib/firebase';

/**
 * GET - Buscar um influenciador específico pelo ID
 * Params: id (na URL)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    // Buscar influenciador pelo ID
    const influencer = await getInfluencerById(id);
    
    if (!influencer) {
      return NextResponse.json(
        { error: 'Influenciador não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json(influencer);
    
  } catch (error) {
    console.error('Erro ao buscar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}