// ATENÇÃO: Este arquivo deve ser usado APENAS em componentes do servidor
// (Server Components ou API Routes)

import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// import { getAuth } from 'firebase-admin/auth';

const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicialize o Firebase apenas se não estiver já inicializado
const firebaseApp = getApps().length === 0 
  ? initializeApp({
      credential: cert(serviceAccount),
      storageBucket: 'deumatch-demo.firebasestorage.app'
    })
  : getApps()[0];

// Exporte o Firestore para uso em toda a aplicação
export const db = getFirestore(firebaseApp);
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// export const adminAuth = getAuth(firebaseApp);
export { FieldValue };

// Funções de Firebase Storage
export async function initializeFirebase() {
  // Função para garantir que o Firebase está inicializado
  // Já é inicializado acima, então retorna apenas true
  return true;
}

export async function getFirebaseStorage() {
  return getStorage(firebaseApp);
}

export async function createStorageRef(storage: any, filePath: string) {
  return storage.bucket().file(filePath);
}

// Coleções principais
export const influencersCollection = db.collection('influencers');
export const usersCollection = db.collection('users');

// Funções auxiliares para CRUD de influenciadores

// Buscar todos os influenciadores
export async function getAllInfluencers() {
  try {
    const snapshot = await influencersCollection.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Erro ao buscar influenciadores:', error);
    return [];
  }
}

// Buscar um influenciador por ID
export async function getInfluencerById(id: string) {
  try {
    const doc = await influencersCollection.doc(id).get();
    if (!doc.exists) return null;
    return { id: doc.id, ...doc.data() };
  } catch (error) {
    console.error(`Erro ao buscar influenciador com ID ${id}:`, error);
    return null;
  }
}

// Adicionar um novo influenciador
export async function addInfluencer(influencerData: any) {
  try {
    const docRef = await influencersCollection.add(influencerData);
    return { id: docRef.id, ...influencerData };
  } catch (error) {
    console.error('Erro ao adicionar influenciador:', error);
    throw error;
  }
}

// Atualizar um influenciador existente
export async function updateInfluencer(id: string, influencerData: any) {
  try {
    await influencersCollection.doc(id).update(influencerData);
    return { id, ...influencerData };
  } catch (error) {
    console.error(`Erro ao atualizar influenciador com ID ${id}:`, error);
    throw error;
  }
}

// Remover um influenciador
export async function deleteInfluencer(id: string) {
  try {
    await influencersCollection.doc(id).delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir influenciador com ID ${id}:`, error);
    throw error;
  }
}

// Funções auxiliares para CRUD de usuários

// Buscar um usuário por ID
export async function getUserById(id: string) {
  try {
    const doc = await usersCollection.doc(id).get();
    if (!doc.exists) return null;
    return { id: doc.id, ...doc.data() };
  } catch (error) {
    console.error(`Erro ao buscar usuário com ID ${id}:`, error);
    return null;
  }
}

// Atualizar avatar do usuário
export async function updateUserAvatar(userId: string, avatarUrl: string) {
  try {
    await usersCollection.doc(userId).update({
      profileImage: avatarUrl,
      updatedAt: FieldValue.serverTimestamp()
    });
    
    console.log(`Avatar do usuário ${userId} atualizado com sucesso: ${avatarUrl}`);
    return true;
  } catch (error) {
    console.error(`Erro ao atualizar avatar do usuário ${userId}:`, error);
    throw error;
  }
}

// Atualizar dados do usuário
export async function updateUser(userId: string, userData: any) {
  try {
    await usersCollection.doc(userId).update({
      ...userData,
      updatedAt: FieldValue.serverTimestamp()
    });
    
    return { id: userId, ...userData };
  } catch (error) {
    console.error(`Erro ao atualizar usuário com ID ${userId}:`, error);
    throw error;
  }
}


