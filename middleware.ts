// @ts-ignore
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Configuração de idiomas
const supportedLocales = ['pt', 'en', 'es'];
const defaultLocale = 'pt';

// ⚡ OTIMIZAÇÃO: Cache de detecção de locale em memória
const localeCache = new Map<string, string>();

// Função otimizada para extrair idioma da URL
function getLocaleFromPathname(pathname: string): string | null {
  if (localeCache.has(pathname)) {
    return localeCache.get(pathname) || null;
  }
  
  const segments = pathname.split('/');
  const firstSegment = segments[1];
  
  const locale = supportedLocales.includes(firstSegment) ? firstSegment : null;
  localeCache.set(pathname, locale || '');
  
  return locale;
}

// ⚡ OTIMIZAÇÃO: Cache de detecção de idioma do navegador
const browserLocaleCache = new Map<string, string>();

// Função otimizada para detectar idioma do navegador
function getLocaleFromHeaders(request: NextRequest): string {
  const acceptLanguage = request.headers.get('accept-language') || '';
  
  if (browserLocaleCache.has(acceptLanguage)) {
    return browserLocaleCache.get(acceptLanguage) || defaultLocale;
  }
  
  for (const locale of supportedLocales) {
    if (acceptLanguage.toLowerCase().includes(locale)) {
      browserLocaleCache.set(acceptLanguage, locale);
      return locale;
    }
  }
  
  browserLocaleCache.set(acceptLanguage, defaultLocale);
  return defaultLocale;
}

// Função para remover idioma da URL
function removeLocaleFromPathname(pathname: string): string {
  const locale = getLocaleFromPathname(pathname);
  if (locale) {
    return pathname.replace(`/${locale}`, '') || '/';
  }
  return pathname;
}

// Rotas públicas que não precisam de autenticação
const publicRoutes = [
  '/',
  '/sign-in',
  '/sign-up',
  '/api/webhooks',
  '/_next',
  '/favicon.ico',
  '/logo.png',
  '/images',
  '/api/graphql',
  '/shared',
  '/public'
];

// ⚡ OTIMIZAÇÃO: Rotas que podem fazer preload de dados
const preloadRoutes = [
  '/dashboard',
  '/influencers',
  '/propostas',
  '/campanhas'
];

// Rotas protegidas
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/auth-redirect',
  '/admin(.*)',
  '/settings(.*)',
  '/onboarding',
  '/(.+)/(influencers|lists|propostas)(.*)',
  '/creators(.*)',
  '/negociacoes(.*)',
  '/campanhas(.*)',
  '/marcas(.*)',
  '/orcamento(.*)'
]);

// Rotas que requerem permissões de admin
const isAdminRoute = createRouteMatcher([
  '/admin(.*)',
  '/(.+)/(settings)/(brands|categories)(.*)',
  '/propostas(.*)',
]);

// Rotas que requerem permissões de manager
const isManagerRoute = createRouteMatcher([
  '/(.+)/(settings|campanhas)(.*)',
  '/orcamento(.*)',
]);

// Rotas que requerem permissões de influencer
const isInfluencerRoute = createRouteMatcher([
  '/(.+)/(lists)(.*)',
  '/creators(.*)',
]);

export default clerkMiddleware((auth: any, req: NextRequest) => {
  const { pathname, searchParams } = req.nextUrl;

  // ⚡ OTIMIZAÇÃO: Evitar processamento desnecessário para assets estáticos
  if (
    pathname.startsWith('/_next/static') ||
    pathname.startsWith('/favicon') ||
    pathname.includes('.js') ||
    pathname.includes('.css') ||
    pathname.includes('.map')
  ) {
    return NextResponse.next();
  }

  // ETAPA 1: Gerenciar idiomas na URL de forma otimizada
  if (
    !pathname.startsWith('/api') &&
    !pathname.startsWith('/_next') &&
    !pathname.includes('.') &&
    !pathname.startsWith('/static')
  ) {
    const currentLocale = getLocaleFromPathname(pathname);
    
    // ⚡ OTIMIZAÇÃO: Reduzir redirects desnecessários
    if (!currentLocale && pathname !== '/') {
      const detectedLocale = getLocaleFromHeaders(req);
      
      // 🔥 OTIMIZAÇÃO: Redirect direto para auth-redirect se é uma rota protegida
      const pathnameWithoutLocale = removeLocaleFromPathname(pathname);
      const modifiedReq = {
        ...req,
        nextUrl: { ...req.nextUrl, pathname: pathnameWithoutLocale }
      };
      
      if (isProtectedRoute(modifiedReq as any)) {
        const { userId } = auth();
        if (userId) {
          // Se usuário já está autenticado, ir direto para a rota de destino
          const newUrl = new URL(`/${detectedLocale}${pathname}`, req.url);
          searchParams.forEach((value, key) => {
            newUrl.searchParams.set(key, value);
          });
          return NextResponse.redirect(newUrl);
        }
      }
      
      const newUrl = new URL(`/${detectedLocale}${pathname}`, req.url);
      searchParams.forEach((value, key) => {
        newUrl.searchParams.set(key, value);
      });
      return NextResponse.redirect(newUrl);
    }
  }

  // ETAPA 2: Lógica de autenticação do Clerk otimizada
  const { userId, has } = auth();
  
  // Remover idioma da URL para verificação de rotas
  const pathnameWithoutLocale = removeLocaleFromPathname(pathname);
  const currentLocale = getLocaleFromPathname(pathname) || defaultLocale;

  // Criar uma cópia da request para verificação de rotas com pathname modificado
  const modifiedReq = {
    ...req,
    nextUrl: {
      ...req.nextUrl,
      pathname: pathnameWithoutLocale
    }
  };

  // ⚡ OTIMIZAÇÃO: Detectar tokens problemáticos mais cedo
  if (searchParams.has('__clerk_db_jwt') || 
      searchParams.has('__clerk_invitation_token') || 
      searchParams.has('invitation_token')) {
    if (userId) {
      // 🔥 OTIMIZAÇÃO: Ir direto para o destino final se possível
      const targetPath = searchParams.get('redirect_url') || 
                        searchParams.get('return_to') ||
                        `/${currentLocale}/${userId}/influencers`;
      const newUrl = new URL(targetPath, req.url);
      return NextResponse.redirect(newUrl);
    } else {
      const newUrl = new URL(`/${currentLocale}/auth-redirect`, req.url);
      return NextResponse.redirect(newUrl);
    }
  }

  // ⚡ OTIMIZAÇÃO: Permitir rotas públicas com headers otimizados
  if (publicRoutes.some(route => pathnameWithoutLocale.startsWith(route))) {
    const response = NextResponse.rewrite(new URL(pathnameWithoutLocale + req.nextUrl.search, req.url));
    response.headers.set('x-locale', currentLocale);
    
    // 🚀 PRELOAD: Adicionar headers de preload para rotas críticas
    if (preloadRoutes.some(route => pathnameWithoutLocale.includes(route))) {
      response.headers.set('Link', '</api/proposals>; rel=prefetch');
    }
    
    return response;
  }

  // ⚡ OTIMIZAÇÃO: Verificação de autenticação mais eficiente
  if (!userId) {
    // 🔥 OTIMIZAÇÃO: Cache do redirect URL para evitar loops
    const redirectUrl = encodeURIComponent(req.url);
    const newUrl = new URL(`/${currentLocale}/sign-in?redirect_url=${redirectUrl}`, req.url);
    return NextResponse.redirect(newUrl);
  }

  // Verificar permissões específicas de forma otimizada
  if (isAdminRoute(modifiedReq as any)) {
    if (!has?.({ role: 'org:admin' })) {
      const newUrl = new URL(`/${currentLocale}/dashboard`, req.url);
      return NextResponse.redirect(newUrl);
    }
  }

  if (isManagerRoute(modifiedReq as any)) {
    if (!has?.({ permission: 'org:campaigns:edit' }) && 
        !has?.({ role: 'org:admin' }) && 
        !has?.({ role: 'org:manager' })) {
      const newUrl = new URL(`/${currentLocale}/dashboard`, req.url);
      return NextResponse.redirect(newUrl);
    }
  }

  if (isInfluencerRoute(modifiedReq as any)) {
    if (!has?.({ permission: 'org:influencers:view' }) && 
        !has?.({ role: 'org:admin' })) {
      const newUrl = new URL(`/${currentLocale}/dashboard`, req.url);
      return NextResponse.redirect(newUrl);
    }
  }

  // ⚡ OTIMIZAÇÃO: Permitir acesso com headers de performance
  if (isProtectedRoute(modifiedReq as any)) {
    const response = NextResponse.rewrite(new URL(pathnameWithoutLocale + req.nextUrl.search, req.url));
    response.headers.set('x-locale', currentLocale);
    response.headers.set('x-user-id', userId);
    
    // 🚀 PRELOAD: Precarregar dados críticos para rotas de influencers
    if (pathnameWithoutLocale.includes('/influencers')) {
      response.headers.set('Link', '</api/proposals>; rel=prefetch, </api/brands>; rel=prefetch');
    }
    
    return response;
  }

  // Fallback otimizado
  if (pathnameWithoutLocale.includes('/influencers') && pathnameWithoutLocale.split('/').length >= 3) {
    if (userId) { 
      const response = NextResponse.rewrite(new URL(pathnameWithoutLocale + req.nextUrl.search, req.url));
      response.headers.set('x-locale', currentLocale);
      response.headers.set('x-user-id', userId);
      response.headers.set('Link', '</api/proposals>; rel=prefetch');
      return response;
    } else {
      const newUrl = new URL(`/${currentLocale}/sign-in`, req.url);
      return NextResponse.redirect(newUrl);
    }
  }

  // Permitir outras rotas com headers otimizados
  const response = NextResponse.rewrite(new URL(pathnameWithoutLocale + req.nextUrl.search, req.url));
  response.headers.set('x-locale', currentLocale);
  if (userId) {
    response.headers.set('x-user-id', userId);
  }
  return response;
});

export const config = {
  matcher: [
    // ⚡ OTIMIZAÇÃO: Matcher mais específico para reduzir processamento
    '/((?!_next/static|_next/image|favicon.ico|logo.png|.*\\.js|.*\\.css|.*\\.map).*)',
    // API routes
    '/(api|trpc)(.*)',
  ]
};

