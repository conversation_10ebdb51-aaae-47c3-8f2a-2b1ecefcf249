import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageSquare, Paperclip } from 'lucide-react';

interface Campaign {
  id: string;
  title: string;
  influencer: string;
  brand: string;
  value: number;
  deadline: string;
  priority: 'alta' | 'media' | 'baixa';
  status: 'planejamento' | 'execucao' | 'revisao' | 'concluido';
  progress: number;
  tags: string[];
  note?: string;
  cardColor: string;
  avatars: string[];
  comments: number;
  attachments: number;
}

interface SortableCampaignItemProps {
  campaign: Campaign;
}

export function SortableCampaignItem({ campaign }: SortableCampaignItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: campaign.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`${campaign.cardColor} rounded-2xl p-4 cursor-pointer hover:shadow-md transition-all duration-200 ${
        isDragging ? 'shadow-lg rotate-1 scale-105 opacity-50' : ''
      }`}
    >
      {/* Tags */}
      <div className="flex flex-wrap gap-1 mb-3">
        {campaign.tags.map((tag, tagIndex) => (
          <Badge 
            key={tagIndex} 
            variant="secondary" 
            className="text-xs px-2 py-0.5 bg-white/80 text-gray-600 border-0 rounded-full"
          >
            {tag}
          </Badge>
        ))}
      </div>

      {/* Title */}
      <h4 className="font-medium text-gray-900 text-sm leading-tight mb-3">
        {campaign.title}
      </h4>

      {/* Note */}
      {campaign.note && (
        <p className="text-xs text-gray-600 mb-3">
          {campaign.note}
        </p>
      )}

      {/* Progress */}
      {campaign.progress > 0 && (
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-600 font-medium">Progress</span>
            <span className="text-xs text-gray-600 font-medium">{campaign.progress}%</span>
          </div>
          <div className="flex gap-1">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full ${
                  i < (campaign.progress / 100) * 10
                    ? campaign.progress === 100 ? 'bg-green-500' : 'bg-orange-500'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
      )}

      {/* Bottom section */}
      <div className="flex items-center justify-between pt-2">
        {/* Avatars */}
        <div className="flex -space-x-2">
          {campaign.avatars.map((avatar, avatarIndex) => (
            <Avatar key={avatarIndex} className="w-6 h-6 border-2 border-white">
              <AvatarImage src={avatar} />
              <AvatarFallback className="text-xs">
                {campaign.influencer.charAt(0)}
              </AvatarFallback>
            </Avatar>
          ))}
        </div>

        {/* Stats */}
        <div className="flex items-center gap-3 text-xs text-gray-600">
          {campaign.comments > 0 && (
            <div className="flex items-center gap-1">
              <MessageSquare className="w-3 h-3" />
              <span>{campaign.comments}</span>
            </div>
          )}
          {campaign.attachments > 0 && (
            <div className="flex items-center gap-1">
              <Paperclip className="w-3 h-3" />
              <span>{campaign.attachments}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

