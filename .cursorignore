# .cursorignore - Configuração para AI Features do Cursor
# O .env está EXPLICITAMENTE INCLUÍDO (!) para ativar as AI features

# ARQUIVOS IMPORTANTES PARA AI FEATURES (mantém ativos)
!.env
!.env.local
!.env.production
!.env.development
!.env.example

# Dependencies e build artifacts
node_modules/
.next/
.vercel/
dist/
build/
out/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Cache e temp files
.cache/
*.tsbuildinfo
.turbo/
.swc/

# Test coverage
coverage/
.nyc_output/

# Database dumps
*.db
*.sqlite
*.sqlite3

# Large binary files
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso

# IDE files (exceto .cursor/)
.vscode/
.idea/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.backup
*.bak
*.tmp

# Large media files
*.mp4
*.avi
*.mov
*.mkv
*.webm
*.mp3
*.wav
*.flac

# Documentation que não precisa de context
docs/temp/
temp/

# NOTA IMPORTANTE:
# .env é INTENCIONALMENTE MANTIDO ATIVO para AI features
# .env.local, .env.production também são mantidos ativos
# Isso permite que o Cursor AI tenha contexto completo das configurações 