import React, { useState, useEffect } from 'react';
import { Tag } from '@/types/influencer';
import { PlusCircle, X, Edit, Save, Trash } from 'lucide-react';

interface TagsManagerProps {
  influencerId?: string;
  selectedTags?: Tag[];
  onTagsChange?: (tags: Tag[]) => void;
  readOnly?: boolean;
}

const TagsManager: React.FC<TagsManagerProps> = ({
  influencerId,
  selectedTags = [],
  onTagsChange,
  readOnly = false
}) => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showTagForm, setShowTagForm] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#3B82F6'); // Azul padrão
  const [editingTagId, setEditingTagId] = useState<string | null>(null);

  // 🔥 CORREÇÃO: Carregar etiquetas apenas uma vez e usar cache
  useEffect(() => {
    let isMounted = true;
    
    const fetchTags = async () => {
      // Verificar cache primeiro
      const cacheKey = 'tags_cache';
      const cacheTimestamp = 'tags_cache_timestamp';
      const cached = sessionStorage.getItem(cacheKey);
      const timestamp = sessionStorage.getItem(cacheTimestamp);
      const now = Date.now();
      
      // Cache válido por 5 minutos
      if (cached && timestamp && (now - parseInt(timestamp)) < 300000) {
        if (isMounted) {
          setTags(JSON.parse(cached));
          setIsLoading(false);
        }
        return;
      }
      
      setIsLoading(true);
      try {
        const response = await fetch('/api/tags');
        if (response.ok && isMounted) {
          const data = await response.json();
          setTags(data);
          
          // Salvar no cache
          sessionStorage.setItem(cacheKey, JSON.stringify(data));
          sessionStorage.setItem(cacheTimestamp, now.toString());
        } else if (isMounted) {
          console.error('Erro ao carregar etiquetas');
        }
      } catch (error) {
        if (isMounted) {
        console.error('Erro ao carregar etiquetas:', error);
        }
      } finally {
        if (isMounted) {
        setIsLoading(false);
        }
      }
    };

    fetchTags();
    
    return () => {
      isMounted = false;
    };
  }, []); // 🔥 Apenas na primeira montagem

  // Configurar IDs de etiquetas selecionadas
  useEffect(() => {
    if (selectedTags && selectedTags.length > 0) {
      setSelectedTagIds(selectedTags.map(tag => tag.id));
    } else {
      setSelectedTagIds([]);
    }
  }, [selectedTags]);

  // Função para alternar seleção de etiqueta
  const toggleTag = async (tagId: string) => {
    if (readOnly) return;

    const isSelected = selectedTagIds.includes(tagId);
    let newSelectedIds: string[];

    if (isSelected) {
      // Remover etiqueta
      newSelectedIds = selectedTagIds.filter(id => id !== tagId);
      
      if (influencerId) {
        try {
          await fetch(`/api/tags?id=${tagId}&influencerId=${influencerId}`, {
            method: 'DELETE'
          });
        } catch (error) {
          console.error('Erro ao remover etiqueta do influenciador:', error);
        }
      }
    } else {
      // Adicionar etiqueta
      newSelectedIds = [...selectedTagIds, tagId];
      
      if (influencerId) {
        try {
          await fetch('/api/tags', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id: tagId,
              influencerId
            })
          });
        } catch (error) {
          console.error('Erro ao adicionar etiqueta ao influenciador:', error);
        }
      }
    }

    setSelectedTagIds(newSelectedIds);
    
    // Notificar mudança
    if (onTagsChange) {
      const selectedTagObjects = tags.filter(tag => newSelectedIds.includes(tag.id));
      onTagsChange(selectedTagObjects);
    }
  };

  // Função para adicionar nova etiqueta
  const handleAddTag = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newTagName.trim() || !newTagColor) return;
    
    try {
      const response = await fetch('/api/tags', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: newTagName.trim(),
          color: newTagColor,
          influencerId // Se fornecido, já associa ao influenciador
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Adicionar nova etiqueta à lista
        const newTag: Tag = {
          id: data.id,
          name: newTagName.trim(),
          color: newTagColor,
          createdAt: new Date()
        };
        
        setTags([...tags, newTag]);
        
        // Se for para associar ao influenciador, atualizar selecionados
        if (influencerId) {
          setSelectedTagIds([...selectedTagIds, data.id]);
          
          if (onTagsChange) {
            onTagsChange([...selectedTags, newTag]);
          }
        }
        
        // Limpar formulário
        setNewTagName('');
        setNewTagColor('#3B82F6');
        setShowTagForm(false);
      } else {
        console.error('Erro ao criar etiqueta');
      }
    } catch (error) {
      console.error('Erro ao criar etiqueta:', error);
    }
  };

  // Função para atualizar uma etiqueta
  const handleUpdateTag = async (tagId: string) => {
    if (!newTagName.trim() || !newTagColor) return;
    
    try {
      const response = await fetch(`/api/tags?id=${tagId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: newTagName.trim(),
          color: newTagColor
        })
      });
      
      if (response.ok) {
        // Atualizar etiqueta na lista
        const updatedTags = tags.map(tag => 
          tag.id === tagId 
            ? { ...tag, name: newTagName.trim(), color: newTagColor }
            : tag
        );
        
        setTags(updatedTags);
        
        // Atualizar etiquetas selecionadas se necessário
        if (onTagsChange && selectedTagIds.includes(tagId)) {
          const updatedSelectedTags = updatedTags.filter(tag => 
            selectedTagIds.includes(tag.id)
          );
          onTagsChange(updatedSelectedTags);
        }
        
        // Limpar formulário
        setNewTagName('');
        setNewTagColor('#3B82F6');
        setEditingTagId(null);
      } else {
        console.error('Erro ao atualizar etiqueta');
      }
    } catch (error) {
      console.error('Erro ao atualizar etiqueta:', error);
    }
  };

  // Função para excluir uma etiqueta
  const handleDeleteTag = async (tagId: string) => {
    try {
      const response = await fetch(`/api/tags?id=${tagId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        // Remover etiqueta da lista
        const updatedTags = tags.filter(tag => tag.id !== tagId);
        setTags(updatedTags);
        
        // Remover dos selecionados se necessário
        if (selectedTagIds.includes(tagId)) {
          const newSelectedIds = selectedTagIds.filter(id => id !== tagId);
          setSelectedTagIds(newSelectedIds);
          
          if (onTagsChange) {
            const updatedSelectedTags = updatedTags.filter(tag => 
              newSelectedIds.includes(tag.id)
            );
            onTagsChange(updatedSelectedTags);
          }
        }
      } else {
        console.error('Erro ao excluir etiqueta');
      }
    } catch (error) {
      console.error('Erro ao excluir etiqueta:', error);
    }
  };

  // Função para iniciar edição de etiqueta
  const startEditingTag = (tag: Tag) => {
    setEditingTagId(tag.id);
    setNewTagName(tag.name);
    setNewTagColor(tag.color);
    setShowTagForm(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-2 min-h-10">
        {isLoading ? (
          <div className="text-sm text-gray-500">Carregando etiquetas...</div>
        ) : tags.length === 0 ? (
          <div className="text-sm text-gray-500">Nenhuma etiqueta disponível</div>
        ) : (
          tags.map(tag => (
            <div 
              key={tag.id}
              className={`
                flex items-center gap-1 px-2 py-1 rounded-full text-sm
                ${selectedTagIds.includes(tag.id) 
                  ? 'border-2 border-opacity-100' 
                  : 'border border-opacity-50 opacity-70'}
                transition-all duration-200
              `}
              style={{ 
                backgroundColor: `${tag.color}20`, // Cor com transparência
                borderColor: tag.color,
                color: tag.color
              }}
              onClick={() => !readOnly && toggleTag(tag.id)}
            >
              <span>{tag.name}</span>
              
              {!readOnly && (
                <div className="flex items-center ml-1">
                  <button
                    type="button"
                    className="p-0.5 rounded-full hover:bg-white hover:bg-opacity-20"
                    onClick={(e) => {
                      e.stopPropagation();
                      startEditingTag(tag);
                    }}
                  >
                    <Edit size={12} />
                  </button>
                  <button
                    type="button"
                    className="p-0.5 rounded-full hover:bg-white hover:bg-opacity-20"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTag(tag.id);
                    }}
                  >
                    <Trash size={12} />
                  </button>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {!readOnly && (
        <div>
          {showTagForm ? (
            <form onSubmit={editingTagId ? () => handleUpdateTag(editingTagId) : handleAddTag} className="space-y-3">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newTagName}
                  onChange={(e) => setNewTagName(e.target.value)}
                  placeholder="Nome da etiqueta"
                  className="flex-1 px-3 py-2 border rounded-md text-sm"
                  required
                />
                <input
                  type="color"
                  value={newTagColor}
                  onChange={(e) => setNewTagColor(e.target.value)}
                  className="w-10 h-10 border rounded-md cursor-pointer"
                />
              </div>
              
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="flex items-center gap-1 px-3 py-2 bg-blue-500 text-white rounded-md text-sm"
                >
                  {editingTagId ? <Save size={16} /> : <PlusCircle size={16} />}
                  {editingTagId ? 'Salvar' : 'Adicionar'}
                </button>
                <button
                  type="button"
                  className="flex items-center gap-1 px-3 py-2 bg-gray-200 rounded-md text-sm"
                  onClick={() => {
                    setShowTagForm(false);
                    setEditingTagId(null);
                    setNewTagName('');
                    setNewTagColor('#3B82F6');
                  }}
                >
                  <X size={16} />
                  Cancelar
                </button>
              </div>
            </form>
          ) : (
            <button
              type="button"
              className="flex items-center gap-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors w-full justify-center dark:bg-transparent border border-gray/5"
              onClick={() => setShowTagForm(true)}
            >
              <PlusCircle size={16} />
              Adicionar etiqueta
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default TagsManager;


