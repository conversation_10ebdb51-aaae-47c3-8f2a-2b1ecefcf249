// Teste simples para verificar se o query de categorias está funcionando
const { ApolloClient, InMemoryCache, gql, createHttpLink } = require('@apollo/client');

const client = new ApolloClient({
  link: createHttpLink({
    uri: 'http://localhost:3000/api/graphql',
  }),
  cache: new InMemoryCache(),
});

const GET_CATEGORIES = gql`
  query GetCategories($userId: ID) {
    categories(userId: $userId) {
      id
      name
      slug
      description
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`;

async function testCategoriesQuery() {
  try {
    console.log('🧪 Testando query de categorias...');
    
    // Teste 1: Sem userId (categorias globais)
    const result1 = await client.query({
      query: GET_CATEGORIES,
      variables: { userId: null }
    });
    
    console.log('✅ Teste 1 - Categorias globais:', result1.data.categories);
    
    // Teste 2: Com userId específico (substitua por um userId real)
    const testUserId = 'user_2example123'; // Substitua por um userId real do seu sistema
    const result2 = await client.query({
      query: GET_CATEGORIES,
      variables: { userId: testUserId }
    });
    
    console.log('✅ Teste 2 - Categorias do usuário:', result2.data.categories);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  testCategoriesQuery();
}

module.exports = { testCategoriesQuery };