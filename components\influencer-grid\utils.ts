// Funções utilitárias para manipulação de marcas e influenciadores
import { Brand, Influencer, globalBrands } from './types';

// Função para obter marca pelo ID
export const getBrandById = (brandId: string) => {
  return globalBrands.find(brand => brand.id === brandId);
};

// Função para obter todas as marcas de um influenciador
export const getInfluencerBrands = (influencer: Influencer): Brand[] => {
  if (!influencer.financials?.brandHistory) return [];
  
  const brandIds = [
    ...(influencer.financials.brandHistory.instagram || []),
    ...(influencer.financials.brandHistory.tiktok || []),
    ...(influencer.financials.brandHistory.youtube || [])
  ];
  
  // Remover duplicatas
  return [...new Set(brandIds)].map(id => getBrandById(id)).filter(Boolean) as Brand[];
};

export function getAllBrandsFromInfluencer(influencer: any): string[] {
  if (!influencer.financials?.brandHistory) return [];
  
  return [
    ...(influencer.financials.brandHistory.instagram || []),
    ...(influencer.financials.brandHistory.tiktok || []),
    ...(influencer.financials.brandHistory.youtube || [])
  ];
}


