/**
 * 🎯 CONFIGURAÇÃO FGA - CLERK COMO SISTEMA DE AUTORIZAÇÃO
 * 
 * ⚠️ MIGRAÇÃO: Abandonando WorkOS FGA, usando Clerk como FGA completo
 * Sistema baseado em organizações com roles e permissões granulares
 */

// ========================
// DECLARAÇÃO DE TIPOS GLOBAIS CLERK
// ========================
declare global {
  interface ClerkAuthorization {
    permission: 
      | 'org:campaigns:create'
      | 'org:campaigns:edit' 
      | 'org:campaigns:delete'
      | 'org:campaigns:view'
      | 'org:proposals:create'
      | 'org:proposals:edit'
      | 'org:proposals:delete'
      | 'org:proposals:approve'
      | 'org:proposals:view'
      | 'org:influencers:create'
      | 'org:influencers:edit'
      | 'org:influencers:delete'
      | 'org:influencers:view'
      | 'org:brands:create'
      | 'org:brands:edit'
      | 'org:brands:delete'
      | 'org:brands:view'
      | 'org:financials:view'
      | 'org:financials:edit'
      | 'org:financials:approve'
      | 'org:team_settings:manage'
      | 'org:team_settings:read'
      | 'org:analytics:view'
      | 'org:users:manage';
    
    role: 
      | 'org:admin'
      | 'org:manager' 
      | 'org:member'
      | 'org:influencer'
      | 'org:brand_manager'
      | 'org:viewer';
  }
}

// ========================
// CONFIGURAÇÃO DE ROLES
// ========================
export const CLERK_ROLES = {
  ADMIN: 'org:admin',
  MANAGER: 'org:manager',
  MEMBER: 'org:member',
  INFLUENCER: 'org:influencer',
  BRAND_MANAGER: 'org:brand_manager',
  VIEWER: 'org:viewer'
} as const;

// ========================
// CONFIGURAÇÃO DE PERMISSÕES
// ========================
export const CLERK_PERMISSIONS = {
  // Campanhas
  CAMPAIGNS_CREATE: 'org:campaigns:create',
  CAMPAIGNS_EDIT: 'org:campaigns:edit',
  CAMPAIGNS_DELETE: 'org:campaigns:delete',
  CAMPAIGNS_VIEW: 'org:campaigns:view',
  
  // Propostas
  PROPOSALS_CREATE: 'org:proposals:create',
  PROPOSALS_EDIT: 'org:proposals:edit',
  PROPOSALS_DELETE: 'org:proposals:delete',
  PROPOSALS_APPROVE: 'org:proposals:approve',
  PROPOSALS_VIEW: 'org:proposals:view',
  
  // Influenciadores
  INFLUENCERS_CREATE: 'org:influencers:create',
  INFLUENCERS_EDIT: 'org:influencers:edit',
  INFLUENCERS_DELETE: 'org:influencers:delete',
  INFLUENCERS_VIEW: 'org:influencers:view',
  
  // Marcas
  BRANDS_CREATE: 'org:brands:create',
  BRANDS_EDIT: 'org:brands:edit',
  BRANDS_DELETE: 'org:brands:delete',
  BRANDS_VIEW: 'org:brands:view',
  
  // Financeiro
  FINANCIALS_VIEW: 'org:financials:view',
  FINANCIALS_EDIT: 'org:financials:edit',
  FINANCIALS_APPROVE: 'org:financials:approve',
  
  // Configurações
  TEAM_SETTINGS_MANAGE: 'org:team_settings:manage',
  TEAM_SETTINGS_READ: 'org:team_settings:read',
  
  // Analytics
  ANALYTICS_VIEW: 'org:analytics:view',
  
  // Administração
  USERS_MANAGE: 'org:users:manage'
} as const;

// ========================
// MAPEAMENTO ROLE → PERMISSÕES
// ========================
export const ROLE_PERMISSIONS_MAP = {
  [CLERK_ROLES.ADMIN]: [
    // Acesso total a todas as permissões
    ...Object.values(CLERK_PERMISSIONS)
  ],
  
  [CLERK_ROLES.MANAGER]: [
    // Gerenciamento de campanhas e propostas
    CLERK_PERMISSIONS.CAMPAIGNS_CREATE,
    CLERK_PERMISSIONS.CAMPAIGNS_EDIT,
    CLERK_PERMISSIONS.CAMPAIGNS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_CREATE,
    CLERK_PERMISSIONS.PROPOSALS_EDIT,
    CLERK_PERMISSIONS.PROPOSALS_APPROVE,
    CLERK_PERMISSIONS.PROPOSALS_VIEW,
    CLERK_PERMISSIONS.INFLUENCERS_VIEW,
    CLERK_PERMISSIONS.INFLUENCERS_EDIT,
    CLERK_PERMISSIONS.BRANDS_VIEW,
    CLERK_PERMISSIONS.BRANDS_EDIT,
    CLERK_PERMISSIONS.FINANCIALS_VIEW,
    CLERK_PERMISSIONS.ANALYTICS_VIEW,
    CLERK_PERMISSIONS.TEAM_SETTINGS_READ
  ],
  
  [CLERK_ROLES.BRAND_MANAGER]: [
    // Foco em marcas e campanhas
    CLERK_PERMISSIONS.CAMPAIGNS_CREATE,
    CLERK_PERMISSIONS.CAMPAIGNS_EDIT,
    CLERK_PERMISSIONS.CAMPAIGNS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_EDIT,
    CLERK_PERMISSIONS.INFLUENCERS_VIEW,
    CLERK_PERMISSIONS.BRANDS_VIEW,
    CLERK_PERMISSIONS.BRANDS_EDIT,
    CLERK_PERMISSIONS.ANALYTICS_VIEW
  ],
  
  [CLERK_ROLES.MEMBER]: [
    // Acesso básico
    CLERK_PERMISSIONS.CAMPAIGNS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_VIEW,
    CLERK_PERMISSIONS.INFLUENCERS_VIEW,
    CLERK_PERMISSIONS.BRANDS_VIEW,
    CLERK_PERMISSIONS.ANALYTICS_VIEW
  ],
  
  [CLERK_ROLES.INFLUENCER]: [
    // Apenas visualização de campanhas e gerenciamento de propostas próprias
    CLERK_PERMISSIONS.CAMPAIGNS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_EDIT, // Apenas próprias propostas
    CLERK_PERMISSIONS.INFLUENCERS_VIEW
  ],
  
  [CLERK_ROLES.VIEWER]: [
    // Apenas visualização
    CLERK_PERMISSIONS.CAMPAIGNS_VIEW,
    CLERK_PERMISSIONS.PROPOSALS_VIEW,
    CLERK_PERMISSIONS.INFLUENCERS_VIEW,
    CLERK_PERMISSIONS.BRANDS_VIEW
  ]
} as const;

// ========================
// MAPEAMENTO USERTYPE → ROLE
// ========================
export const USER_TYPE_TO_CLERK_ROLE = {
  'agency': CLERK_ROLES.ADMIN,
  'influencer': CLERK_ROLES.INFLUENCER,
  'manager': CLERK_ROLES.MANAGER,
  'brand': CLERK_ROLES.BRAND_MANAGER,
  'viewer': CLERK_ROLES.VIEWER
} as const;

// ========================
// UTILITÁRIOS DE AUTORIZAÇÃO
// ========================

/**
 * Verificar se um role tem uma permissão específica
 */
export function roleHasPermission(
  role: keyof typeof ROLE_PERMISSIONS_MAP, 
  permission: string
): boolean {
  const permissions = ROLE_PERMISSIONS_MAP[role] || [];
  return permissions.includes(permission as any);
}

/**
 * Obter todas as permissões de um role
 */
export function getRolePermissions(role: string): readonly string[] {
  return ROLE_PERMISSIONS_MAP[role as keyof typeof ROLE_PERMISSIONS_MAP] || [];
}

/**
 * Verificar se é um role válido do Clerk
 */
export function isValidClerkRole(role: string): boolean {
  return Object.values(CLERK_ROLES).includes(role as any);
}

/**
 * Mapear userType do onboarding para role do Clerk
 */
export function mapUserTypeToClerkRole(userType: string): string {
  return USER_TYPE_TO_CLERK_ROLE[userType as keyof typeof USER_TYPE_TO_CLERK_ROLE] || CLERK_ROLES.MEMBER;
}

// ========================
// CONFIGURAÇÕES DE ORGANIZAÇÃO
// ========================
export const ORGANIZATION_CONFIG = {
  // Role padrão para novos membros
  DEFAULT_CREATOR_ROLE: CLERK_ROLES.ADMIN,
  
  // Máximo de membros por organização
  MAX_MEMBERS: 100,
  
  // Configurações de convite
  INVITE_SETTINGS: {
    requireApproval: true,
    defaultRole: CLERK_ROLES.MEMBER
  }
} as const;

/**
 * 🔒 LOGS DE SEGURANÇA
 */
export function logClerkSecurityEvent(
  event: string, 
  userId: string, 
  organizationId?: string, 
  details?: any
) {
  const timestamp = new Date().toISOString();
  console.log(`🔒 [CLERK-FGA] ${timestamp} - ${event}`, {
    userId,
    organizationId,
    details,
    source: 'clerk-fga'
  });
}

export type ClerkRole = typeof CLERK_ROLES[keyof typeof CLERK_ROLES];
export type ClerkPermission = typeof CLERK_PERMISSIONS[keyof typeof CLERK_PERMISSIONS]; 

