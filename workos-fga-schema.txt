version 0.3

type user

type organization
  relation admin [user]
  relation manager [user]
  relation member [user]
  
  inherit manager if
    relation admin
  
  inherit member if
    relation manager

type influencer
  relation organization [organization]
  relation owner [user]
  relation editor [user]
  relation viewer [user]
  
  inherit editor if
    relation owner
  
  inherit viewer if
    any_of
      relation editor
      relation admin on organization [organization]
      relation manager on organization [organization]
      relation member on organization [organization]

type campaign
  relation organization [organization]
  relation owner [user]
  relation editor [user]
  relation viewer [user]
  
  inherit editor if
    relation owner
  
  inherit viewer if
    any_of
      relation editor
      relation admin on organization [organization]
      relation manager on organization [organization]

type budget
  relation organization [organization]
  relation owner [user]
  relation editor [user]
  relation viewer [user]
  
  inherit editor if
    relation owner
  
  inherit viewer if
    any_of
      relation editor
      relation admin on organization [organization]
      relation manager on organization [organization]

type report
  relation organization [organization]
  relation owner [user]
  relation viewer [user]
  
  inherit viewer if
    any_of
      relation admin on organization [organization]
      relation manager on organization [organization]

type list
  relation organization [organization]
  relation owner [user]
  relation editor [user]
  relation viewer [user]
  
  inherit editor if
    relation owner
  
  inherit viewer if
    any_of
      relation editor
      relation admin on organization [organization]
      relation manager on organization [organization]
      relation member on organization [organization] 