/* Estilos de barra de rolagem personalizados - Página Global */

/* Estilo para todas as barras de rolagem - Modo Claro */
::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
}

/* Barra de rolagem (trilha) */
::-webkit-scrollbar-track {
  background: #f3f4f6 !important;
  border-radius: 3px !important;
}

/* Controle deslizante da barra de rolagem (thumb) */
::-webkit-scrollbar-thumb {
  background-color: #9c9c9c !important; /* Cor roxa para o modo claro */
  border-radius: 3px !important;
  border: 1px solid #f3f4f6 !important;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #8b8b8b !important; /* Tom mais escuro no hover */
}

/* Thumb da barra de rolagem no modo escuro */
.dark ::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
}

.dark ::-webkit-scrollbar-track {
  background: #d1d1d1 !important;
  border-radius: 3px !important;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: #d1d1d1 !important; /* Cor especificada para o modo escuro */
  border-radius: 3px !important;
  border: 1px solid #d1d1d1 !important;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: #d1d1d1 !important; /* Tom mais claro no hover */
}

/* Cantos da barra de rolagem */
::-webkit-scrollbar-corner {
  background: #d1d1d1 !important;
}

.dark ::-webkit-scrollbar-corner {
  background: #acacac !important;
}

/* Firefox - Página Global */
* {
  scrollbar-color: #9810fa #ffffff !important;
  scrollbar-width: thin !important;
}

.dark * {
  scrollbar-color: #ff0074 #ffffff00 !important;
  scrollbar-width: thin !important;
}

