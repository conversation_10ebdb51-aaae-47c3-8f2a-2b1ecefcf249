'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth-v2';
import { isAdmin } from '@/lib/security-config';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader } from '@/components/ui/loader';
import { UserAvatarUpload } from '@/components/user-avatar-upload';
import { User, Mail, Calendar, Shield, Building2, Settings } from 'lucide-react';

export default function UserSettingsPage() {
  const params = useParams();
  const { currentUser, firebaseUser, isLoading, isInitialized } = useAuth();
  const [userAvatarUrl, setUserAvatarUrl] = useState<string | null>(null);
  
  const userId = params?.userId as string;
  const isOwnProfile = currentUser?.id === userId;
  const canAccess = isOwnProfile || (currentUser && isAdmin(currentUser.role));

  // Função para atualizar o avatar do usuário
  const handleAvatarUpdate = (newAvatarUrl: string) => {
    setUserAvatarUrl(newAvatarUrl);
  };

  if (!isInitialized || isLoading) {
    return <Loader isLoading={true} message="" showLogo={true} />;
  }

  if (!canAccess || !currentUser) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            Você não tem permissão para acessar este perfil.
          </p>
        </div>
      </div>
    );
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin': return 'Agência';
      case 'manager': return 'Gerente';
      case 'user': return 'Usuário';
      case 'viewer': return 'Visualizador';
      default: return 'Usuário';
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin': return 'destructive';
      case 'manager': return 'default';
      case 'user': return 'secondary';
      case 'viewer': return 'outline';
      default: return 'secondary';
    }
  };

 
} 