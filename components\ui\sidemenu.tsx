'use client';

import React, { useState } from 'react';
// Removido import Link - navegação interna apenas
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

import { 
  Search, 
  BarChart3, 
  Megaphone, 
  CheckSquare, 
  Settings, 
  Briefcase, 
  Archive,
  Hash,
  Plus,
  ChevronDown,
  ChevronRight,
  Folder,
  FolderOpen,
  File,
  Code,
  Database,
  Globe,
  Smartphone,
  FileText,
  Layers,
  Users,
  Building2,
      Tag,
    Bell,
    HelpCircle
} from 'lucide-react';

interface SideMenuProps {
  className?: string;
  brandId?: string;
  onSectionChange?: (section: string) => void;
  activeSection?: string;
  createdGroups?: Array<{id: string, name: string, description: string, influencers: string[]}>;
}

interface TreeNode {
  id: string;
  name: string;
  type: 'project' | 'subproject' | 'file' | 'folder' | 'archive';
  isActive?: boolean;
  isExpanded?: boolean;
  children?: TreeNode[];
  icon?: React.ReactNode;
}

const workspaceData: TreeNode[] = [
  {
    id: 'grupos',
    name: 'Grupos de Influenciadores',
    type: 'folder',
    isExpanded: true,
    children: [
      {
        id: 'grupo-lifestyle',
        name: 'Lifestyle & Moda',
        type: 'project',
        isActive: false,
        isExpanded: false,
        children: [
          { id: 'lifestyle-micro', name: 'Micro Influenciadores', type: 'subproject' },
          { id: 'lifestyle-macro', name: 'Macro Influenciadores', type: 'subproject' }
        ]
      },
      {
        id: 'grupo-tech',
        name: 'Tecnologia',
        type: 'project',
        children: [
          { id: 'tech-gaming', name: 'Gaming', type: 'subproject' },
          { id: 'tech-reviews', name: 'Reviews', type: 'subproject' }
        ]
      },
      {
        id: 'grupo-fitness',
        name: 'Fitness & Saúde',
        type: 'project',
        children: [
          { id: 'fitness-treino', name: 'Treino', type: 'subproject' },
          { id: 'fitness-nutricao', name: 'Nutrição', type: 'subproject' }
        ]
      }
    ]
  },
  {
    id: 'campanhas',
    name: 'Campanhas Ativas',
    type: 'folder',
    isExpanded: false,
    children: [
      {
        id: 'campanha-verao',
        name: 'Campanha Verão 2024',
        type: 'project',
        children: [
          { id: 'verao-instagram', name: 'Instagram Posts', type: 'subproject' },
          { id: 'verao-stories', name: 'Stories', type: 'subproject' }
        ]
      },
      {
        id: 'campanha-black',
        name: 'Black Friday',
        type: 'project',
        children: [
          { id: 'black-promocoes', name: 'Promoções', type: 'subproject' }
        ]
      }
    ]
  },
  {
    id: 'arquivos',
    name: 'Arquivos',
    type: 'folder',
    isExpanded: false,
    children: [
      {
        id: 'templates',
        name: 'Templates',
        type: 'archive',
        children: [
          { id: 'templates-contratos', name: 'Contratos', type: 'folder' },
          { id: 'templates-briefings', name: 'Briefings', type: 'folder' }
        ]
      }
    ]
  }
];

// Função para obter o ícone baseado no tipo do nó
const getNodeIcon = (node: TreeNode, isExpanded: boolean = false) => {
  switch (node.type) {
    case 'project':
      return <Hash className="h-4 w-4 text-blue-500" />;
    case 'subproject':
      return <Layers className="h-3 w-3 text-purple-500" />;
    case 'folder':
      return isExpanded ? 
        <FolderOpen className="h-3 w-3 text-yellow-600" /> : 
        <Folder className="h-3 w-3 text-yellow-600" />;
    case 'file':
      return <FileText className="h-3 w-3 text-gray-500" />;
    case 'archive':
      return <Archive className="h-3 w-3 text-gray-400" />;
    default:
      return <File className="h-3 w-3 text-gray-500" />;
  }
};

// Componente TreeNode customizado com linhas de conexão
interface TreeNodeProps {
  node: TreeNode;
  level: number;
  isLast: boolean;
  parentLines: boolean[];
  onToggle: (nodeId: string) => void;
  expandedNodes: Set<string>;
}

const TreeNodeComponent: React.FC<TreeNodeProps> = ({ 
  node, 
  level, 
  isLast, 
  parentLines, 
  onToggle, 
  expandedNodes 
}) => {
  const isExpanded = expandedNodes.has(node.id);
  const hasChildren = node.children && node.children.length > 0;
  
  return (
    <div className="tree-node">
      <div className="flex items-center relative">
        {/* Linhas de conexão */}
        {level > 0 && (
          <div className="absolute left-0 flex">
            {parentLines.map((showLine, index) => (
              <div
                key={index}
                className={cn(
                  "w-4 flex justify-center",
                  showLine && "border-l border-border/30 dark:border-gray-700/50"
                )}
              >
                {index === parentLines.length - 1 && (
                  <div className={cn(
                    "absolute top-0 w-4 h-6 border-l border-b border-border/30 dark:border-gray-700/50",
                    isLast && "h-3"
                  )} />
                )}
              </div>
            ))}
          </div>
        )}
        
        {/* Conteúdo do nó */}
        <div 
          className={cn(
            "flex items-center gap-2 py-1 px-2 rounded-md cursor-pointer transition-colors hover:bg-muted/50",
            level > 0 && "ml-4",
            node.isActive && "bg-muted/70 text-foreground font-medium"
          )}
          style={{ marginLeft: level * 16 }}
          onClick={() => hasChildren && onToggle(node.id)}
        >
          {/* Seta de expansão */}
          {hasChildren && (
            <div className="w-4 h-4 flex items-center justify-center">
              {isExpanded ? (
                <ChevronDown className="h-3 w-3 text-muted-foreground" />
              ) : (
                <ChevronRight className="h-3 w-3 text-muted-foreground" />
              )}
            </div>
          )}
          
          {/* Ícone do tipo */}
          {getNodeIcon(node, isExpanded)}
          
          {/* Nome do nó */}
          <span className={cn(
            "text-sm",
            node.type === 'project' && "font-medium",
            node.type === 'folder' && "font-medium text-foreground",
            node.isActive && "font-semibold"
          )}>
            {node.name}
          </span>
          
          {/* Indicador de ativo */}
          {node.isActive && (
            <div className="ml-auto h-2 w-2 bg-green-500 rounded-full" />
          )}
        </div>
      </div>
      
      {/* Filhos */}
      {hasChildren && isExpanded && (
        <div className="tree-children">
          {node.children!.map((child, index) => {
            const isChildLast = index === node.children!.length - 1;
            const newParentLines = [...parentLines, !isChildLast];
            
            return (
              <TreeNodeComponent
                key={child.id}
                node={child}
                level={level + 1}
                isLast={isChildLast}
                parentLines={newParentLines}
                onToggle={onToggle}
                expandedNodes={expandedNodes}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export function SideMenu({ className, brandId, onSectionChange, activeSection = 'influencers', createdGroups = [] }: SideMenuProps) {
  // Atualizar workspaceData com grupos criados dinamicamente
  const workspaceDataWithGroups: TreeNode[] = [
    {
      id: 'grupos',
      name: 'Grupos de Influenciadores',
      type: 'folder',
      isExpanded: true,
      children: [
        ...createdGroups.map(group => ({
          id: group.id,
          name: group.name,
          type: 'project' as const,
          isActive: false,
          isExpanded: false,
          children: [
            { id: `${group.id}-details`, name: `${group.influencers.length} Influenciadores`, type: 'subproject' as const },
            { id: `${group.id}-description`, name: group.description || 'Sem descrição', type: 'file' as const }
          ]
        })),
        // Grupos de exemplo se não houver grupos criados
        ...(createdGroups.length === 0 ? [
          {
            id: 'grupo-lifestyle',
            name: 'Lifestyle & Moda',
            type: 'project' as const,
            isActive: false,
            isExpanded: false,
            children: [
              { id: 'lifestyle-micro', name: 'Micro Influenciadores', type: 'subproject' as const },
              { id: 'lifestyle-macro', name: 'Macro Influenciadores', type: 'subproject' as const }
            ]
          },
          {
            id: 'grupo-tech',
            name: 'Tecnologia',
            type: 'project' as const,
            children: [
              { id: 'tech-gaming', name: 'Gaming', type: 'subproject' as const },
              { id: 'tech-reviews', name: 'Reviews', type: 'subproject' as const }
            ]
          },
          {
            id: 'grupo-fitness',
            name: 'Fitness & Saúde',
            type: 'project' as const,
            children: [
              { id: 'fitness-treino', name: 'Treino', type: 'subproject' as const },
              { id: 'fitness-nutricao', name: 'Nutrição', type: 'subproject' as const }
            ]
          }
        ] : [])
      ]
    },
    ...workspaceData.slice(1) // Manter outras seções (campanhas, arquivos)
  ];

  const [searchValue, setSearchValue] = useState('');
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(
    new Set(['projects', 'wanderly', 'wanderly-frontend'])
  );
  
  const handleToggle = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };



  return (
    <div className={cn(
      "w-60 h-full bg-background border-r border-border/50 flex flex-col",
      "dark:bg-[#080210] dark:border",
      className
    )}>
      {/* Search Bar */}
      <div className="p-4 border-b border-border/50 dark:border-gray-800/50">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="pl-10 h-10 bg-muted/30 border-border/50 focus:border-primary/50 transition-colors"
          />
        </div>
      </div>

      {/* Navigation Menu */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-2">
         

       

          {/* Campanhas */}
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start h-10 px-3 text-sm font-medium transition-all duration-200",
              activeSection === 'campaigns' 
                ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-gray-900 dark:text-white border border-purple-500/30" 
                : "text-gray-700 dark:text-gray-300 hover:text-white/100 hover:bg-[#ff0074]"
            )}
            onClick={() => onSectionChange?.('campaigns')}
          >
            <Megaphone className="h-4 w-4 mr-3" />
            Campanhas
          </Button>

         

         

       
          {/* Propostas */}
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start h-10 px-3 text-sm font-medium transition-all duration-200",
              activeSection === 'proposals' 
                ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-gray-900 dark:text-white border border-purple-500/30" 
                : "text-gray-700 dark:text-gray-300 hover:text-white/100 hover:bg-[#ff0074]"
            )}
            onClick={() => onSectionChange?.('proposals')}
          >
            <FileText className="h-4 w-4 mr-3" />
            Propostas
          </Button>

          {/* Teste Workspace */}
          <Button
            variant="ghost"
            asChild
            className={cn(
              "w-full justify-start h-10 px-3 text-sm font-medium transition-all duration-200",
              "border-l-2 border-[#ff0074] bg-[#ff0074]/10 text-[#ff0074] hover:bg-[#ff0074] hover:text-white"
            )}
          >
            <a href="/test-workspace">
              <Building2 className="h-4 w-4 mr-3" />
              🧪 Teste Workspace
            </a>
          </Button>

          

         
        </div>

        {/* Separator */}
        <div className="mx-4 border-t border-border/30 dark:border-gray-800/50" />

      
      </div>
    </div>
  );
}


