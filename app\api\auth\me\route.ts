import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';

// Forçar renderização dinâmica para esta rota
export const dynamic = 'force-dynamic';

// 🆕 MÉTODO POST OTIMIZADO: Buscar dados de outro usuário pelo userId
export async function POST(request: NextRequest) {
  try {
    const { userId: currentUserId } = await auth();

    if (!currentUserId) {
      return NextResponse.json(
        { error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { userId: targetUserId } = body;

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    // ⚡ CACHE: Headers para cache agressivo (dados de usuário raramente mudam)
    const headers = new Headers();
    headers.set('Cache-Control', 'private, max-age=300'); // Cache por 5 minutos

    try {
      // Buscar dados do usuário específico no Clerk
      const client = await clerkClient();
      const user = await client.users.getUser(targetUserId);
      
      if (!user) {
        return NextResponse.json(
          { error: 'Usuário não encontrado' },
          { status: 404, headers }
        );
      }

      // ⚡ OTIMIZADO: Buscar role na organização de forma mais eficiente
      let organizationRole = 'member';
      try {
        const userMemberships = await client.users.getOrganizationMembershipList({ 
          userId: targetUserId,
          limit: 1 // Só precisamos da primeira organização
        });
        
        if (userMemberships.data.length > 0) {
          organizationRole = userMemberships.data[0].role || 'member';
        }
      } catch (orgError) {
        console.warn('⚠️ [USER_DATA] Erro ao buscar organizações:', orgError);
      }

      // ⚡ OTIMIZADO: Tradução mais eficiente de roles
      const roleTranslations: Record<string, string> = {
        'org:admin': 'Admin',
        'org:member': 'Membro',
        'org:manager': 'Manager',
        'admin': 'Admin',
        'member': 'Membro',
        'manager': 'Manager'
      };

      const translatedRole = roleTranslations[organizationRole] || organizationRole;

      // ⚡ OTIMIZADO: Montar dados do usuário de forma mais eficiente
      const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      const userData = {
        userId: user.id,
        userName: fullName || user.username || `Usuário ${user.id.substring(0, 8)}`,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        imageUrl: user.imageUrl || '', 
        profileImageUrl: user.imageUrl || '', // Alias para compatibilidade
        role: translatedRole,
        originalRole: organizationRole,
        emailAddress: user.emailAddresses?.[0]?.emailAddress || '' // Para fallback de nome
      };

      return NextResponse.json(userData, { status: 200, headers });

    } catch (clerkError) {
      console.error('❌ [USER_DATA] Erro do Clerk:', clerkError);
      
      // ⚡ FALLBACK: Retornar dados básicos em caso de erro
      const fallbackData = {
        userId: targetUserId,
        userName: `Usuário ${targetUserId.substring(0, 8)}`,
        firstName: '',
        lastName: '',
        imageUrl: '',
        profileImageUrl: '',
        role: 'Membro',
        originalRole: 'member',
        emailAddress: ''
      };

      return NextResponse.json(fallbackData, { status: 200, headers });
    }

  } catch (error) {
    console.error('❌ [USER_DATA] Erro interno:', error);
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        userName: 'Manager',
        role: 'manager'
      },
      { status: 500 }
    );
  }
}

