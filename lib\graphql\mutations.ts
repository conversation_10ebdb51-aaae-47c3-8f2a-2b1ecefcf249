import { gql } from '@apollo/client'

// ===== QUERIES PARA BRANDS =====

export const GET_BRANDS_QUERY = gql`
  query GetBrands($userId: ID!, $influencerId: ID) {
    brands(userId: $userId, influencerId: $influencerId) {
      id
      name
      prices {
        instagramStory {
          name
          price
        }
        instagramReel {
          name
          price
        }
        tiktokVideo {
          name
          price
        }
        youtubeInsertion {
          name
          price
        }
        youtubeDedicated {
          name
          price
        }
        youtubeShorts {
          name
          price
        }
      }
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const GET_BRAND_QUERY = gql`
  query GetBrand($id: ID!, $userId: ID!) {
    brand(id: $id, userId: $userId) {
      id
      name
      prices {
        instagramStory {
          name
          price
        }
        instagramReel {
          name
          price
        }
        tiktokVideo {
          name
          price
        }
        youtubeInsertion {
          name
          price
        }
        youtubeDedicated {
          name
          price
        }
        youtubeShorts {
          name
          price
        }
      }
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

// ===== MUTATIONS PARA BRANDS =====

export const CREATE_BRAND_MUTATION = gql`
  mutation CreateBrand($input: CreateBrandInput!) {
    createBrand(input: $input) {
      id
      name
      prices {
        instagramStory {
          name
          price
        }
        instagramReel {
          name
          price
        }
        tiktokVideo {
          name
          price
        }
        youtubeInsertion {
          name
          price
        }
        youtubeDedicated {
          name
          price
        }
        youtubeShorts {
          name
          price
        }
      }
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_BRAND_MUTATION = gql`
  mutation UpdateBrand($id: ID!, $input: UpdateBrandInput!) {
    updateBrand(id: $id, input: $input) {
      id
      name
      prices {
        instagramStory {
          name
          price
        }
        instagramReel {
          name
          price
        }
        tiktokVideo {
          name
          price
        }
        youtubeInsertion {
          name
          price
        }
        youtubeDedicated {
          name
          price
        }
        youtubeShorts {
          name
          price
        }
      }
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const DELETE_BRAND_MUTATION = gql`
  mutation DeleteBrand($id: ID!) {
    deleteBrand(id: $id)
  }
`

// ===== QUERIES PARA CATEGORIES =====

export const GET_CATEGORIES_QUERY = gql`
  query GetCategories($userId: ID) {
    categories(userId: $userId) {
      id
      name
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`

export const GET_USER_CATEGORIES_QUERY = gql`
  query GetUserCategories($userId: ID!) {
    userCategories(userId: $userId) {
      id
      name
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`

export const GET_CATEGORY_QUERY = gql`
  query GetCategory($id: ID!, $userId: ID!) {
    category(id: $id, userId: $userId) {
      id
      name
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`

// ===== MUTATIONS PARA CATEGORIES =====

export const CREATE_CATEGORY_MUTATION = gql`
  mutation CreateCategory($input: CreateCategoryInput!) {
    createCategory(input: $input) {
      id
      name
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_CATEGORY_MUTATION = gql`
  mutation UpdateCategory($id: ID!, $input: UpdateCategoryInput!) {
    updateCategory(id: $id, input: $input) {
      id
      name
      userId
      isActive
      createdAt
      updatedAt
    }
  }
`

export const DELETE_CATEGORY_MUTATION = gql`
  mutation DeleteCategory($id: ID!, $userId: ID!) {
    deleteCategory(id: $id, userId: $userId)
  }
`

// ===== QUERIES PARA NOTES =====

export const GET_NOTES_QUERY = gql`
  query GetNotes($influencerId: ID!, $userId: ID!) {
    notes(influencerId: $influencerId, userId: $userId) {
      id
      title
      content
      type
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const GET_ALL_NOTES_QUERY = gql`
  query GetAllNotes($userId: ID!) {
    allNotes(userId: $userId) {
      id
      title
      content
      type
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const GET_NOTE_QUERY = gql`
  query GetNote($id: ID!, $userId: ID!) {
    note(id: $id, userId: $userId) {
      id
      title
      content
      type
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

// ===== MUTATIONS PARA NOTES =====

export const CREATE_NOTE_MUTATION = gql`
  mutation CreateNote($input: CreateNoteInput!) {
    createNote(input: $input) {
      id
      title
      content
      type
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const UPDATE_NOTE_MUTATION = gql`
  mutation UpdateNote($id: ID!, $input: UpdateNoteInput!) {
    updateNote(id: $id, input: $input) {
      id
      title
      content
      type
      influencerId
      userId
      createdAt
      updatedAt
    }
  }
`

export const DELETE_NOTE_MUTATION = gql`
  mutation DeleteNote($id: ID!, $userId: ID!) {
    deleteNote(id: $id, userId: $userId)
  }
`

// ===== QUERIES E MUTATIONS EXISTENTES (SCREENSHOTS) =====

// Mutation para upload de screenshot
export const UPLOAD_SCREENSHOT_MUTATION = gql`
  mutation UploadScreenshot($input: CreateScreenshotInput!) {
    uploadScreenshot(input: $input) {
      id
      influencerId
      platform
      url
      filename
      size
      contentType
      uploadedAt
      uploadedBy
    }
  }
`

// Mutation para upload de avatar
export const UPLOAD_AVATAR_MUTATION = gql`
  mutation UploadAvatar($input: UploadAvatarInput!) {
    uploadAvatar(input: $input) {
      id
      userId
      avatarUrl
      filename
      size
      contentType
      uploadedAt
      success
      message
    }
  }
`

// Mutation para deletar screenshot
export const DELETE_SCREENSHOT_MUTATION = gql`
  mutation DeleteScreenshot($id: ID!, $influencerId: ID!, $platform: String!) {
    deleteScreenshot(id: $id, influencerId: $influencerId, platform: $platform)
  }
`

// Query para buscar screenshots de um influenciador
export const GET_INFLUENCER_SCREENSHOTS_QUERY = gql`
  query GetInfluencerScreenshots($influencerId: ID!, $platform: String) {
    influencerScreenshots(influencerId: $influencerId, platform: $platform) {
      id
      influencerId
      platform
      url
      filename
      size
      contentType
      uploadedAt
      uploadedBy
    }
  }
`

// Mutation para upload em lote de screenshots - OTIMIZADA
export const UPLOAD_SCREENSHOTS_BATCH_MUTATION = gql`
  mutation UploadScreenshotsBatch($input: UploadScreenshotsBatchInput!) {
    uploadScreenshotsBatch(input: $input) {
      success
      totalUploaded
      results {
        id
        influencerId
        platform
        url
        filename
        size
        contentType
        uploadedAt
        uploadedBy
      }
      errors {
        platform
        filename
        error
      }
    }
  }
` 

