import { db } from '@/lib/firebase-admin';
import { backupCollection } from './backup-firestore';

/**
 * 🔄 SCRIPT DE MIGRAÇÃO DE BRANDS
 * FASE 5.2: Migração para adicionar userId às marcas existentes
 */

// Interface para dados de migração
interface MigrationResult {
  collection: string;
  totalDocuments: number;
  migratedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: Array<{ id: string; error: string }>;
  duration: number;
}

// Configurações de migração
const MIGRATION_CONFIG = {
  batchSize: 100,
  dryRun: false,
  backupBeforeMigration: true,
  defaultUserId: null as string | null,
  adminFallback: true
};

/**
 * Migrar coleção de brands para incluir userId
 */
export async function migrateBrands(options: {
  dryRun?: boolean;
  backupFirst?: boolean;
  defaultUserId?: string;
  batchSize?: number;
} = {}): Promise<MigrationResult> {
  console.log('🔄 Iniciando migração de brands...');
  
  const config = { ...MIGRATION_CONFIG, ...options };
  const startTime = Date.now();
  
  const result: MigrationResult = {
    collection: 'brands',
    totalDocuments: 0,
    migratedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: [],
    duration: 0
  };
  
  try {
    // 1. Fazer backup se solicitado
    if (config.backupFirst) {
      console.log('📦 Fazendo backup antes da migração...');
      await backupCollection('brands');
      console.log('✅ Backup concluído');
    }
    
    // 2. Obter todos os documentos
    const snapshot = await db.collection('brands').get();
    result.totalDocuments = snapshot.size;
    
    console.log(`📊 Encontrados ${result.totalDocuments} documentos de brands`);
    
    if (result.totalDocuments === 0) {
      console.log('⚠️ Nenhum documento encontrado para migrar');
      return result;
    }
    
    // 3. Processar em lotes
    const docs = snapshot.docs;
    const batches = [];
    
    for (let i = 0; i < docs.length; i += config.batchSize) {
      batches.push(docs.slice(i, i + config.batchSize));
    }
    
    console.log(`🔄 Processando ${batches.length} lotes de até ${config.batchSize} documentos`);
    
    // 4. Migrar cada lote
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`📝 Processando lote ${batchIndex + 1}/${batches.length}`);
      
      const firebaseBatch = db.batch();
      let batchHasUpdates = false;
      
      for (const doc of batch) {
        try {
          const data = doc.data();
          
          // Pular se já tem userId
          if (data.userId) {
            result.skippedCount++;
            console.log(`⏭️ Pulando ${doc.id} - já possui userId`);
            continue;
          }
          
          // Determinar userId para esta brand
          const userId = await determineUserIdForBrand(data, config);
          
          if (!userId) {
            result.errorCount++;
            result.errors.push({
              id: doc.id,
              error: 'Não foi possível determinar userId'
            });
            console.error(`❌ Erro: ${doc.id} - Não foi possível determinar userId`);
            continue;
          }
          
          // Preparar dados para atualização
          const updateData = {
            userId,
            updatedAt: new Date(),
            migratedAt: new Date(),
            _migrationMetadata: {
              phase: 'fase-5-userId-migration',
              timestamp: new Date().toISOString(),
              originalData: {
                contactEmail: data.contactEmail || null,
                industry: data.industry || null,
                name: data.name || null
              }
            }
          };
          
          if (config.dryRun) {
            console.log(`🔍 [DRY RUN] ${doc.id} seria atualizado com userId: ${userId}`);
            result.migratedCount++;
          } else {
            firebaseBatch.update(doc.ref, updateData);
            batchHasUpdates = true;
            result.migratedCount++;
            console.log(`✅ ${doc.id} será atualizado com userId: ${userId}`);
          }
          
        } catch (error) {
          result.errorCount++;
          const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
          result.errors.push({ id: doc.id, error: errorMsg });
          console.error(`❌ Erro ao processar ${doc.id}:`, error);
        }
      }
      
      // Executar batch se há atualizações
      if (batchHasUpdates && !config.dryRun) {
        try {
          await firebaseBatch.commit();
          console.log(`✅ Lote ${batchIndex + 1} commitado com sucesso`);
        } catch (error) {
          console.error(`❌ Erro ao commitar lote ${batchIndex + 1}:`, error);
          // Contar erros do lote inteiro
          for (const doc of batch) {
            if (!doc.data().userId) {
              result.errorCount++;
              result.migratedCount--;
              result.errors.push({
                id: doc.id,
                error: `Erro no batch commit: ${error.message}`
              });
            }
          }
        }
      }
    }
    
    result.duration = Date.now() - startTime;
    
    // 5. Relatório final
    console.log('\n📊 RELATÓRIO DE MIGRAÇÃO DE BRANDS');
    console.log('=====================================');
    console.log(`📁 Coleção: ${result.collection}`);
    console.log(`📊 Total de documentos: ${result.totalDocuments}`);
    console.log(`✅ Migrados: ${result.migratedCount}`);
    console.log(`⏭️ Pulados: ${result.skippedCount}`);
    console.log(`❌ Erros: ${result.errorCount}`);
    console.log(`⏱️ Duração: ${formatDuration(result.duration)}`);
    console.log(`🔧 Modo: ${config.dryRun ? 'DRY RUN' : 'EXECUÇÃO REAL'}`);
    
    if (result.errors.length > 0) {
      console.log('\n❌ Erros encontrados:');
      result.errors.forEach(error => {
        console.log(`  - ${error.id}: ${error.error}`);
      });
    }
    
    if (config.dryRun) {
      console.log('\n🔍 Esta foi uma execução em modo DRY RUN. Nenhuma alteração foi feita.');
      console.log('📝 Execute novamente sem --dry-run para aplicar as mudanças.');
    } else {
      console.log('\n✅ Migração de brands concluída!');
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ Erro fatal na migração de brands:', error);
    result.duration = Date.now() - startTime;
    throw error;
  }
}

/**
 * Determinar userId baseado nos dados da brand
 */
async function determineUserIdForBrand(
  brandData: any, 
  config: typeof MIGRATION_CONFIG
): Promise<string | null> {
  try {
    // Estratégia 1: Usar contactEmail se disponível
    if (brandData.contactEmail) {
      console.log(`🔍 Buscando usuário por email: ${brandData.contactEmail}`);
      
      const userSnapshot = await db.collection('users')
        .where('email', '==', brandData.contactEmail)
        .limit(1)
        .get();
      
      if (!userSnapshot.empty) {
        const userId = userSnapshot.docs[0].id;
        console.log(`✅ Usuário encontrado por email: ${userId}`);
        return userId;
      }
    }
    
    // Estratégia 2: Usar userId padrão se fornecido
    if (config.defaultUserId) {
      console.log(`🎯 Usando userId padrão: ${config.defaultUserId}`);
      return config.defaultUserId;
    }
    
    // Estratégia 3: Buscar campos alternativos
    if (brandData.ownerId) {
      console.log(`🔍 Verificando ownerId: ${brandData.ownerId}`);
      const userDoc = await db.collection('users').doc(brandData.ownerId).get();
      if (userDoc.exists) {
        console.log(`✅ Usuário encontrado por ownerId: ${brandData.ownerId}`);
        return brandData.ownerId;
      }
    }
    
    // Estratégia 4: Procurar por nome similar em campanhas
    if (brandData.name) {
      console.log(`🔍 Buscando campanhas relacionadas à marca: ${brandData.name}`);
      
      const campaignsSnapshot = await db.collection('campaigns')
        .where('brandName', '==', brandData.name)
        .limit(1)
        .get();
      
      if (!campaignsSnapshot.empty) {
        const campaign = campaignsSnapshot.docs[0].data();
        if (campaign.userId) {
          console.log(`✅ UserId encontrado via campanha relacionada: ${campaign.userId}`);
          return campaign.userId;
        }
      }
    }
    
    // Estratégia 5: Fallback para primeiro admin (se habilitado)
    if (config.adminFallback) {
      console.log('🔍 Buscando usuário admin como fallback...');
      
      const adminSnapshot = await db.collection('users')
        .where('role', '==', 'admin')
        .limit(1)
        .get();
      
      if (!adminSnapshot.empty) {
        const adminUserId = adminSnapshot.docs[0].id;
        console.log(`⚠️ Usando admin como fallback: ${adminUserId}`);
        return adminUserId;
      }
      
      // Se não há admin, buscar qualquer usuário
      const anyUserSnapshot = await db.collection('users').limit(1).get();
      if (!anyUserSnapshot.empty) {
        const anyUserId = anyUserSnapshot.docs[0].id;
        console.log(`⚠️ Usando primeiro usuário disponível: ${anyUserId}`);
        return anyUserId;
      }
    }
    
    console.log('❌ Nenhuma estratégia funcionou para determinar userId');
    return null;
    
  } catch (error) {
    console.error('❌ Erro ao determinar userId:', error);
    return null;
  }
}

/**
 * Validar migração de brands
 */
export async function validateBrandsMigration(): Promise<{
  total: number;
  withUserId: number;
  withoutUserId: number;
  migrationComplete: boolean;
  issues: Array<{ id: string; issue: string }>;
}> {
  console.log('🔍 Validando migração de brands...');
  
  const snapshot = await db.collection('brands').get();
  const total = snapshot.size;
  const withUserId = snapshot.docs.filter(doc => doc.data().userId).length;
  const withoutUserId = total - withUserId;
  const issues: Array<{ id: string; issue: string }> = [];
  
  // Verificar qualidade dos dados migrados
  for (const doc of snapshot.docs) {
    const data = doc.data();
    
    if (!data.userId) {
      issues.push({ id: doc.id, issue: 'Missing userId' });
    } else {
      // Verificar se userId é válido
      const userDoc = await db.collection('users').doc(data.userId).get();
      if (!userDoc.exists) {
        issues.push({ id: doc.id, issue: `Invalid userId: ${data.userId}` });
      }
    }
  }
  
  const result = {
    total,
    withUserId,
    withoutUserId,
    migrationComplete: withoutUserId === 0 && issues.length === 0,
    issues
  };
  
  console.log(`📊 Validação: ${withUserId}/${total} brands com userId válido`);
  if (issues.length > 0) {
    console.log(`⚠️ ${issues.length} problemas encontrados`);
  }
  
  return result;
}

/**
 * Reverter migração de brands (remover campos adicionados)
 */
export async function revertBrandsMigration(): Promise<void> {
  console.log('🔄 Revertendo migração de brands...');
  
  const snapshot = await db.collection('brands').get();
  const batch = db.batch();
  
  let revertCount = 0;
  
  for (const doc of snapshot.docs) {
    const data = doc.data();
    
    if (data.migratedAt) {
      // Remover campos de migração
      batch.update(doc.ref, {
        userId: null,
        migratedAt: null,
        _migrationMetadata: null
      });
      revertCount++;
    }
  }
  
  if (revertCount > 0) {
    await batch.commit();
    console.log(`✅ Migração revertida para ${revertCount} brands`);
  } else {
    console.log('⚠️ Nenhuma brand com dados de migração encontrada');
  }
}

// Utilitários
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
}

// Função principal para execução via linha de comando
export async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const noBackup = args.includes('--no-backup');
  const defaultUserId = args.find(arg => arg.startsWith('--default-user='))?.split('=')[1];
  
  try {
    console.log('🚀 Iniciando migração de brands...\n');
    
    const result = await migrateBrands({
      dryRun,
      backupFirst: !noBackup,
      defaultUserId
    });
    
    // Validar se não for dry run
    if (!dryRun) {
      console.log('\n🔍 Validando migração...');
      const validation = await validateBrandsMigration();
      
      if (validation.migrationComplete) {
        console.log('✅ Migração validada com sucesso!');
      } else {
        console.log('⚠️ Migração parcialmente concluída. Verificar issues.');
      }
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Erro na migração:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 

