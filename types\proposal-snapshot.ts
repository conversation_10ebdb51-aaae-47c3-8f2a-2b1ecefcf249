// 📸 TIPOS PARA SNAPSHOT DE DADOS EM PROPOSTAS

import { ProposalStatus, ProposalPriority } from './proposal';

export interface InfluencerSnapshot {
  // IDs para referência
  influencerId: string;
  
  // 📸 DADOS BÁSICOS (sempre incluídos)
  nome: string;
  username: string;
  idade: number;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  localizacao: {
    cidade: string;
    estado: string;
    pais: string;
  };
  categoria: string;
  verificado: boolean;
  
  // 📊 REDES SOCIAIS (snapshot no momento da proposta)
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
      verificado: boolean;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
      verificado: boolean;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
      verificado: boolean;
    };
  };
  
  // 💰 PRICING (valores no momento da proposta)
  precosSnapshot: {
    instagramStory?: { price: number; currency: string };
    instagramReel?: { price: number; currency: string };
    youtubeDedicated?: { price: number; currency: string };
    youtubeShorts?: { price: number; currency: string };
    tiktokVideo?: { price: number; currency: string };
  };
  
  // 📊 DADOS DEMOGRÁFICOS BÁSICOS (se disponíveis)
  audiencia?: {
    genero?: {
      feminino: number;
      masculino: number;
      outros: number;
    };
    faixaEtaria?: {
      faixa: string;
      percentual: number;
    }[];
  };
  
  // 🏷️ METADADOS DO SNAPSHOT
  snapshotMetadata: {
    dataCaptura: Date;
    versaoSchema: string;
    fonte: 'manual' | 'api' | 'import';
    responsavelCaptura: string; // userId que criou a proposta
  };
}

// Removida definição antiga - substituída pelas versões com filtragem por role

// 🔄 INTERFACE PARA ATUALIZAÇÕES DE SNAPSHOT
export interface SnapshotUpdateRequest {
  proposalId: string;
  influencerIds: string[]; // IDs para atualizar
  forceUpdate?: boolean; // Forçar atualização mesmo se recente
  updatedBy: string; // userId responsável
}

// 📊 INTERFACE PARA COMPARAÇÃO DE DADOS
export interface SnapshotComparison {
  influencerId: string;
  campo: string;
  valorSnapshot: any;
  valorAtual: any;
  diferenca: number | string;
  significativa: boolean; // Se a diferença é relevante (ex: >10% seguidores)
}

// Removida primeira definição - mantida apenas a versão completa com socialPlatforms

// Redes sociais básicas (sem dados de pricing)
export interface SocialPlatformBasicSnapshot {
  platform: 'instagram' | 'tiktok' | 'youtube' | 'twitter' | 'linkedin';
  username: string;
  url: string;
  followers: number;
  engagement: number;
  verified: boolean;
  isActive: boolean;
}

// Redes sociais completas (com pricing - só para admins/managers)
export interface SocialPlatformCompleteSnapshot extends SocialPlatformBasicSnapshot {
  pricing: {
    post: number;
    story: number;
    reel: number;
    video?: number;
  };
  historicalRates?: {
    date: string;
    post: number;
    story: number;
    reel: number;
  }[];
  negotiatedRates?: {
    serviceType: string;
    originalPrice: number;
    negotiatedPrice: number;
    discount: number;
  }[];
}

// Snapshot básico do influenciador (para role USER)
export interface InfluencerBasicSnapshot {
  id: string;
  name: string;
  avatar?: string;
  bio?: string;
  followers?: number;
  engagement?: number;
  tier?: string;
  category?: string;
  location?: string;
  verified?: boolean;
  socialPlatforms: SocialPlatformBasicSnapshot[];
  // Dados de portfolio sem pricing
  portfolio?: {
    mediaUrl: string;
    platform: string;
    description: string;
    date: string;
  }[];
  stats?: {
    avgLikes: number;
    avgComments: number;
    avgShares: number;
    postFrequency: string;
  };
  capturedAt: Date;
  snapshotVersion: string;
}

// Snapshot completo do influenciador (para admins/managers)
export interface InfluencerCompleteSnapshot extends Omit<InfluencerBasicSnapshot, 'socialPlatforms'> {
  socialPlatforms: SocialPlatformCompleteSnapshot[];
  // Dados financeiros completos
  financials?: {
    totalEarnings: number;
    avgProjectValue: number;
    paymentTerms: string;
    preferredCurrency: string;
    taxInfo?: string;
  };
  // Histórico de negociações
  negotiationHistory?: {
    projectId: string;
    originalRate: number;
    finalRate: number;
    discount: number;
    date: string;
  }[];
  // Dados sensíveis adicionais
  contactInfo?: {
    email: string;
    phone?: string;
    manager?: {
      name: string;
      email: string;
      phone?: string;
    };
  };
}

// Proposta com snapshot básico (para role USER)
export interface PropostaComSnapshotBasico {
  id: string;
  nome: string;
  descricao?: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected';
  priority: 'low' | 'medium' | 'high';
  dataEnvio: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Dados do snapshot filtrados
  influencersSnapshot: InfluencerBasicSnapshot[];
  
  // Dados da marca (básicos)
  brand?: {
    id: string;
    name: string;
    logo?: string;
    category: string;
  };
  
  // Serviços sem pricing
  services?: {
    serviceId: string;
    serviceName: string;
    description: string;
    quantity: number;
    // pricing removido para users
  }[];
  
  // Metadados do snapshot
  snapshotMetadata: {
    createdAt: Date;
    createdBy: string;
    version: string;
    accessLevel: 'basic'; // sempre 'basic' para users
    filteredFields: string[]; // lista dos campos removidos
  };
}

// Proposta com snapshot completo (para admins/managers)
export interface PropostaComSnapshotCompleto extends Omit<PropostaComSnapshotBasico, 'influencersSnapshot' | 'services' | 'snapshotMetadata'> {
  influencersSnapshot: InfluencerCompleteSnapshot[];
  
  // Serviços completos com pricing
  services?: {
    serviceId: string;
    serviceName: string;
    description: string;
    quantity: number;
    pricing: {
      originalPrice: number;
      customPrice?: number;
      discount?: number;
    };
    totalValue: number;
  }[];
  
  // Dados financeiros completos
  financial?: {
    totalAmount: number;
    currency: string;
    paymentTerms: string;
    invoiceData?: any;
  };
  
  // Dados de negociação
  negotiation?: {
    history: any[];
    currentOffer?: any;
    counterOffers?: any[];
  };
  
  // Metadados do snapshot completo
  snapshotMetadata: {
    createdAt: Date;
    createdBy: string;
    version: string;
    accessLevel: 'complete';
    includesFinancials: boolean;
    includesPricing: boolean;
  };
}

// Union type para diferentes tipos de snapshot
export type PropostaComSnapshot = PropostaComSnapshotBasico | PropostaComSnapshotCompleto;

// Enum para níveis de acesso
export enum SnapshotAccessLevel {
  BASIC = 'basic',      // Para role USER
  COMPLETE = 'complete' // Para admins/managers
}

// Interface para configuração de filtragem
export interface SnapshotFilterConfig {
  accessLevel: SnapshotAccessLevel;
  userRole: string;
  includeFinancials: boolean;
  includePricing: boolean;
  includeContactInfo: boolean;
  includeNegotiationHistory: boolean;
} 

