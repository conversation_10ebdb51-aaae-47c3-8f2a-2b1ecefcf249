import { NextResponse } from "next/server";
import { getBrandById, updateBrand, deleteBrand } from "@/lib/firebase";

// Obter uma marca específica
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: brandId } = await params;
    const brand = await getBrandById(brandId);
    
    if (!brand) {
      return NextResponse.json(
        { error: "Marca não encontrada" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(brand);
  } catch (error) {
    console.error("Erro ao buscar marca:", error);
    return NextResponse.json(
      { error: "Erro ao buscar marca" },
      { status: 500 }
    );
  }
}

// Atualizar uma marca
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: brandId } = await params;
    const data = await request.json();
    
    await updateBrand(brandId, data);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao atualizar marca:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar marca" },
      { status: 500 }
    );
  }
}

// Excluir uma marca
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: brandId } = await params;
    
    await deleteBrand(brandId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao excluir marca:", error);
    return NextResponse.json(
      { error: "Erro ao excluir marca" },
      { status: 500 }
    );
  }
}
