import { collection, doc, addDoc, updateDoc, deleteDoc, getDocs, getDoc, query, where, orderBy, serverTimestamp } from 'firebase/firestore'
import { clientDb } from '@/lib/firebase-client'

export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  parentId?: string
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface CategoryCreateData {
  name: string
  slug: string
  description?: string
  color?: string
  parentId?: string
}

export interface CategoryUpdateData extends Partial<CategoryCreateData> {
  id: string
}

export class CategoryService {
  private readonly collectionName = 'categories'

  /**
   * Criar uma nova categoria
   */
  async createCategory(userId: string, data: CategoryCreateData): Promise<Category> {
    try {
      // Verificar se o slug já existe para este usuário
      const existingCategory = await this.getCategoryBySlug(userId, data.slug)
      if (existingCategory) {
        throw new Error('Já existe uma categoria com este slug')
      }

      const docRef = await addDoc(collection(clientDb, this.collectionName), {
        ...data,
        userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // Buscar o documento criado para retornar com ID
      const createdDoc = await getDoc(docRef)
      return {
        id: createdDoc.id,
        ...createdDoc.data(),
        createdAt: createdDoc.data()?.createdAt?.toDate() || new Date(),
        updatedAt: createdDoc.data()?.updatedAt?.toDate() || new Date(),
      } as Category
    } catch (error) {
      console.error('Erro ao criar categoria:', error)
      throw error
    }
  }

  /**
   * Buscar todas as categorias de um usuário
   */
  async getCategoriesByUser(userId: string): Promise<Category[]> {
    try {
      const q = query(
        collection(clientDb, this.collectionName),
        where('userId', '==', userId),
        orderBy('name', 'asc')
      )
      
      const querySnapshot = await getDocs(q)
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      })) as Category[]
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
      throw error
    }
  }

  /**
   * Buscar categoria por ID
   */
  async getCategoryById(userId: string, categoryId: string): Promise<Category | null> {
    try {
      const docRef = doc(clientDb, this.collectionName, categoryId)
      const docSnap = await getDoc(docRef)
      
      if (!docSnap.exists()) {
        return null
      }

      const categoryData = docSnap.data()
      
      // Verificar se a categoria pertence ao usuário
      if (categoryData.userId !== userId) {
        throw new Error('Categoria não encontrada ou acesso negado')
      }

      return {
        id: docSnap.id,
        ...categoryData,
        createdAt: categoryData.createdAt?.toDate() || new Date(),
        updatedAt: categoryData.updatedAt?.toDate() || new Date(),
      } as Category
    } catch (error) {
      console.error('Erro ao buscar categoria:', error)
      throw error
    }
  }

  /**
   * Buscar categoria por slug
   */
  async getCategoryBySlug(userId: string, slug: string): Promise<Category | null> {
    try {
      const q = query(
        collection(clientDb, this.collectionName),
        where('userId', '==', userId),
        where('slug', '==', slug)
      )
      
      const querySnapshot = await getDocs(q)
      
      if (querySnapshot.empty) {
        return null
      }

      const doc = querySnapshot.docs[0]
      return {
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      } as Category
    } catch (error) {
      console.error('Erro ao buscar categoria por slug:', error)
      throw error
    }
  }

  /**
   * Atualizar uma categoria
   */
  async updateCategory(userId: string, data: CategoryUpdateData): Promise<Category> {
    try {
      const { id, ...updateData } = data

      // Verificar se a categoria existe e pertence ao usuário
      const existingCategory = await this.getCategoryById(userId, id)
      if (!existingCategory) {
        throw new Error('Categoria não encontrada ou acesso negado')
      }

      // Se o slug mudou, verificar se não conflita com outro
      if (updateData.slug && updateData.slug !== existingCategory.slug) {
        const conflictCategory = await this.getCategoryBySlug(userId, updateData.slug)
        if (conflictCategory && conflictCategory.id !== id) {
          throw new Error('Já existe uma categoria com este slug')
        }
      }

      const docRef = doc(clientDb, this.collectionName, id)
      await updateDoc(docRef, {
        ...updateData,
        updatedAt: serverTimestamp(),
      })

      // Buscar o documento atualizado
      return await this.getCategoryById(userId, id) as Category
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error)
      throw error
    }
  }

  /**
   * Excluir uma categoria
   */
  async deleteCategory(userId: string, categoryId: string): Promise<void> {
    try {
      // Verificar se a categoria existe e pertence ao usuário
      const existingCategory = await this.getCategoryById(userId, categoryId)
      if (!existingCategory) {
        throw new Error('Categoria não encontrada ou acesso negado')
      }

      // TODO: Verificar se existem influenciadores usando esta categoria
      // e decidir como lidar com eles (remover categoria ou impedir exclusão)

      const docRef = doc(clientDb, this.collectionName, categoryId)
      await deleteDoc(docRef)
    } catch (error) {
      console.error('Erro ao excluir categoria:', error)
      throw error
    }
  }

  /**
   * Gerar slug automaticamente a partir do nome
   */
  generateSlug(name: string): string {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim()
  }

  /**
   * Validar dados de categoria
   */
  validateCategoryData(data: CategoryCreateData): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Nome deve ter pelo menos 2 caracteres')
    }

    if (!data.slug || data.slug.trim().length < 2) {
      errors.push('Slug deve ter pelo menos 2 caracteres')
    }

    if (data.slug && !/^[a-z0-9-]+$/.test(data.slug)) {
      errors.push('Slug deve conter apenas letras minúsculas, números e hífens')
    }

    if (data.name && data.name.length > 50) {
      errors.push('Nome não pode ter mais de 50 caracteres')
    }

    if (data.description && data.description.length > 200) {
      errors.push('Descrição não pode ter mais de 200 caracteres')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Instância singleton do serviço
export const categoryService = new CategoryService() 

