import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { BrandInfluencerService } from '@/services/brand-influencer-service';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * DELETE /api/brand-influencers/[id] - Deletar associação específica
 */
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      try {
        const { id } = await params;

        console.log('[API_BRAND_INFLUENCERS_DELETE] Deletando associação:', { 
          associationId: id,
          userId 
        });

        // Deletar associação
        await BrandInfluencerService.deleteAssociation(id, userId);

        console.log('[API_BRAND_INFLUENCERS_DELETE] Associação deletada:', id);

        return NextResponse.json(
          { message: 'Associação removida com sucesso' }, 
          { status: 200 }
        );

      } catch (error: any) {
        console.error('[API_BRAND_INFLUENCERS_DELETE] Erro:', error);
        
        return NextResponse.json(
          { 
            error: 'Erro interno do servidor',
            message: error.message 
          },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * GET /api/brand-influencers/[id] - Buscar associação específica
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      try {
        const { id } = await params;

        console.log('[API_BRAND_INFLUENCERS_GET] Buscando associação:', { 
          associationId: id,
          userId 
        });

        // Buscar associação específica
        const associations = await BrandInfluencerService.getAssociations(userId, {});
        const association = associations.associations.find(assoc => assoc.id === id);

        if (!association) {
          return NextResponse.json(
            { error: 'Associação não encontrada' },
            { status: 404 }
          );
        }

        console.log('[API_BRAND_INFLUENCERS_GET] Associação encontrada:', association.id);

        return NextResponse.json(association);

      } catch (error: any) {
        console.error('[API_BRAND_INFLUENCERS_GET] Erro:', error);
        
        return NextResponse.json(
          { 
            error: 'Erro interno do servidor',
            message: error.message 
          },
          { status: 500 }
        );
      }
    }
  )(req);
} 