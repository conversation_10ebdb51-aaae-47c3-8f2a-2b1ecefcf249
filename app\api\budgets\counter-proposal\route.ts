import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [POST] Recebendo contraproposta (nova estrutura)');
    
    const body = await request.json();
    console.log('📦 Dados recebidos:', body);

    const {
      budgetId,
      influencerId,
      influencerName,
      userId,
      originalAmount,
      counterAmount,
      counterCurrency = 'BRL',
      note,
      serviceType,
      proposalId, // 🆕 Incluir proposalId para identificar estrutura
      type = 'manager', // 🆕 Tipo da contraproposta (manager, collaborator, etc)
      quantity = 1 // 🆕 Quantidade do serviço na contraproposta
    } = body;

    // Validações básicas
    if (!budgetId || !influencerId || !userId || !counterAmount) {
      console.error('❌ Campos obrigatórios faltando:', { budgetId, influencerId, userId, counterAmount });
      return NextResponse.json(
        { error: 'Campos obrigatórios: budgetId, influencerId, userId, counterAmount' },
        { status: 400 }
      );
    }

    // 🆕 Criar contraproposta para adicionar ao array
    const counterProposal = {
      id: `counter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // ID único
      proposedAmount: Number(counterAmount),
      originalAmount: Number(originalAmount),
      currency: counterCurrency,
      notes: note || '',
      proposedBy: userId,
      proposedAt: new Date().toISOString(),
      status: 'pending',
      type: type, // 🆕 Adicionar tipo da contraproposta
      quantity: Number(quantity) // 🆕 Quantidade do serviço
    };

    console.log('💾 Adicionando contraproposta ao orçamento:', counterProposal);

    // 🆕 Estrutura condicional: proposta ou independente
    let budgetRef;
    if (proposalId) {
      // Estrutura hierárquica: proposals/{proposalId}/influencers/{influencerId}/budgets/{budgetId}
      budgetRef = db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .doc(budgetId);
    } else {
      // Estrutura independente: influencers/{influencerId}/budgets/{budgetId}
      budgetRef = db
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .doc(budgetId);
    }

    // Buscar o documento atual para pegar as contrapropostas existentes
    const budgetDoc = await budgetRef.get();
    
    if (!budgetDoc.exists) {
      throw new Error('Orçamento não encontrado');
    }

    const budgetData = budgetDoc.data();
    const existingCounterProposals = budgetData?.counterProposals || [];

    // Adicionar nova contraproposta ao array
    const updatedCounterProposals = [...existingCounterProposals, counterProposal];

    // Atualizar o documento com a nova contraproposta
    await budgetRef.update({
      counterProposals: updatedCounterProposals,
      status: 'negotiating', // Status indicando que está em negociação
      updatedAt: new Date().toISOString()
    });

    console.log('✅ Contraproposta adicionada ao orçamento com sucesso');

    return NextResponse.json({
      success: true,
      counterProposal,
      message: 'Contraproposta adicionada com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao adicionar contraproposta:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const budgetId = searchParams.get('budgetId');
    const userId = searchParams.get('userId');
    const influencerId = searchParams.get('influencerId');

    console.log('🔍 [GET] Buscando contrapropostas:', { budgetId, userId, influencerId });

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    // Fazer query simples apenas por userId para evitar índices compostos
    const snapshot = await db.collection('counterProposals')
      .where('userId', '==', userId)
      .get();
    
    let counterProposals = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Filtrar no lado do cliente para evitar índices compostos
    if (budgetId) {
      counterProposals = counterProposals.filter((cp: any) => cp.budgetId === budgetId);
    }

    if (influencerId) {
      counterProposals = counterProposals.filter((cp: any) => cp.influencerId === influencerId);
    }

    // Ordenar no lado do cliente
    counterProposals.sort((a: any, b: any) => {
      const dateA = new Date(a.createdAt || '').getTime();
      const dateB = new Date(b.createdAt || '').getTime();
      return dateB - dateA; // desc
    });

    console.log(`✅ Encontradas ${counterProposals.length} contrapropostas`);

    return NextResponse.json(counterProposals);

  } catch (error) {
    console.error('❌ Erro ao buscar contrapropostas:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, response, ...updateData } = body;

    console.log('🔄 [PUT] Atualizando contraproposta:', { id, status, updateData });

    if (!id) {
      return NextResponse.json(
        { error: 'ID da contraproposta é obrigatório' },
        { status: 400 }
      );
    }

    const updateFields = {
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    if (status) {
      updateFields.status = status;
    }

    if (response) {
      updateFields.response = response;
      updateFields.respondedAt = new Date().toISOString();
    }

    await db.collection('counterProposals').doc(id).update(updateFields);

    console.log('✅ Contraproposta atualizada com sucesso');

    return NextResponse.json({ success: true, id });

  } catch (error) {
    console.error('❌ Erro ao atualizar contraproposta:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    console.log('🗑️ [DELETE] Removendo contraproposta:', { id });

    if (!id) {
      return NextResponse.json(
        { error: 'ID da contraproposta é obrigatório' },
        { status: 400 }
      );
    }

    await db.collection('counterProposals').doc(id).delete();

    console.log('✅ Contraproposta removida com sucesso');

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ Erro ao remover contraproposta:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
} 

