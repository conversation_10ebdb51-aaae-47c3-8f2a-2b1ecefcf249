import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { PLATFORM_CONFIG } from '@/types/influencer-form'
import type { InfluencerFormData } from '@/types/influencer-form'

interface PricingPartnershipsSectionProps {
  // Interface vazia - não precisa mais de activePlatforms
}

const CONTENT_TYPES = {
  instagram: [
    { key: 'story', label: 'Stories' },
    { key: 'reel', label: 'Reels' },
  ],
  youtube: [
    { key: 'shorts', label: 'Shorts' },
    { key: 'dedicated', label: 'Vídeo' },
    { key: 'insertion', label: 'Inserção' },
  ],
  tiktok: [
    { key: 'video', label: 'Vídeo' },
  ],
  facebook: [
    { key: 'post', label: 'Post' },
  ],
  twitch: [
    { key: 'stream', label: 'Stream' },
  ],
  kwai: [
    { key: 'video', label: 'Vídeo' },
  ],
} as const

export function PricingPartnershipsSection({}: PricingPartnershipsSectionProps) {
  const { register, watch } = useFormContext<InfluencerFormData>()
  
  // Obter plataformas com dados
  const platforms = watch('platforms') || {}
  const activePlatforms = Object.keys(platforms).filter(key => {
    const platform = (platforms as any)[key]
    return platform && platform.followers > 0
  })

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Preços</h2>

      {activePlatforms.length > 0 ? (
        <div className="space-y-4">
          {activePlatforms.map((platformKey) => {
            const platformConfig = PLATFORM_CONFIG[platformKey as keyof typeof PLATFORM_CONFIG]
            const contentTypes = CONTENT_TYPES[platformKey as keyof typeof CONTENT_TYPES] || []

            return (
              <Card key={platformKey}>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: platformConfig?.color || '#666' }}
                    />
                    {platformConfig?.name || platformKey}
                  </CardTitle>
                </CardHeader>

                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    {contentTypes.map((contentType) => (
                      <div key={contentType.key}>
                        <Label className="text-xs">{contentType.label}</Label>
                        <div className="relative">
                          <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">R$</span>
                          <Input
                            type="number"
                            placeholder="0"
                            className="text-sm pl-7"
                            {...register(`platforms.${platformKey}.pricing.${contentType.key}` as any, {
                              valueAsNumber: true
                            })}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : (
        <p className="text-sm text-muted-foreground text-center py-8">
          Adicione plataformas para configurar preços
        </p>
      )}
    </div>
  )
} 

