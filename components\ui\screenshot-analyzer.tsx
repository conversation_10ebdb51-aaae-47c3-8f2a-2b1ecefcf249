"use client"

import React, { useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Upload, Loader2, CheckCircle2, AlertCircle, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { mistralAIService, type ExtractedDemographics } from '@/lib/mistral-ai-service'
import { toast } from '@/hooks/use-toast'

interface ScreenshotAnalyzerProps {
  onDataExtracted: (data: ExtractedDemographics) => void
  maxFiles?: number
}

interface FileItem {
  id: string
  file: File
  preview: string
  status: 'pending' | 'analyzing' | 'done' | 'error'
  error?: string
}

export function ScreenshotAnalyzer({ onDataExtracted, maxFiles }: ScreenshotAnalyzerProps) {
  const [files, setFiles] = useState<FileItem[]>([])
  const [analyzing, setAnalyzing] = useState(false)

  const processFile = async (fileItem: FileItem) => {
    setFiles(prev => prev.map(f => f.id === fileItem.id ? { ...f, status: 'analyzing' } : f))
    
    try {
      const data = await mistralAIService.extractDemographicsFromScreenshot(fileItem.file)
      
      if (data?.platform) {
        setFiles(prev => prev.map(f => f.id === fileItem.id ? { ...f, status: 'done' } : f))
        onDataExtracted(data)
        toast({ title: "Screenshot analisado!", description: `${data.platform} • ${data.followers} seguidores` })
      } else {
        throw new Error('Dados inválidos')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Erro na análise'
      setFiles(prev => prev.map(f => f.id === fileItem.id ? { ...f, status: 'error', error: errorMsg } : f))
    }
  }

  const addFiles = async (newFiles: File[]) => {
    const fileItems: FileItem[] = newFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 15),
      file,
      preview: URL.createObjectURL(file),
      status: 'pending'
    }))
    
    setFiles(prev => [...prev, ...fileItems])
    
    // Processar arquivos automaticamente
    setAnalyzing(true)
    for (const item of fileItems) {
      await processFile(item)
    }
    setAnalyzing(false)
  }

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id)
      if (file?.preview) URL.revokeObjectURL(file.preview)
      return prev.filter(f => f.id !== id)
    })
  }

  const clearAll = () => {
    files.forEach(f => URL.revokeObjectURL(f.preview))
    setFiles([])
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },
    maxFiles: maxFiles ? maxFiles - files.length : undefined,
    disabled: analyzing || (maxFiles ? files.length >= maxFiles : false),
    onDrop: addFiles
  })

  const progress = files.length > 0 ? (files.filter(f => f.status === 'done' || f.status === 'error').length / files.length) * 100 : 0

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-base">
          <span>Análise de Screenshots</span>
          {files.length > 0 && (
            <span className="text-sm text-muted-foreground">
              {maxFiles ? `${files.length}/${maxFiles}` : `${files.length} arquivo${files.length !== 1 ? 's' : ''}`}
            </span>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Upload */}
        {(!maxFiles || files.length < maxFiles) && (
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
              isDragActive ? "border-[#ff0074] bg-[#ff0074]/5" : "border-border hover:border-[#5600ce]",
              analyzing && "opacity-50 cursor-not-allowed"
            )}
          >
            <input {...getInputProps()} />
            <Upload className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm font-medium">{isDragActive ? 'Solte aqui' : 'Carregar Screenshots'}</p>
            <p className="text-xs text-muted-foreground">PNG, JPG, WebP</p>
          </div>
        )}

        {/* Progress */}
        {analyzing && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-[#5600ce]/20 rounded animate-pulse" />
              <span className="text-sm">Analisando...</span>
            </div>
            <Progress value={progress} className="h-1" />
          </div>
        )}

        {/* Files */}
        {files.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Arquivos carregados</span>
              <Button variant="outline" size="sm" onClick={clearAll}>
                Limpar tudo
              </Button>
            </div>
            
            <div className="space-y-2">
              {files.map((file) => (
                <div key={file.id} className="flex items-center gap-3 p-2 bg-muted rounded-lg">
                  <div className="flex-1 flex items-center gap-2">
                    <img
                      src={file.preview}
                      alt={file.file.name}
                      className="h-8 w-8 rounded object-cover"
                    />
                    <div className="flex-1">
                      <p className="text-sm font-medium truncate">{file.file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(file.file.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {file.status === 'pending' && (
                      <span className="text-xs text-muted-foreground">Pendente</span>
                    )}
                    {file.status === 'analyzing' && (
                      <div className="h-4 w-4 bg-[#5600ce]/20 rounded animate-pulse" />
                    )}
                    {file.status === 'done' && (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    )}
                    {file.status === 'error' && (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 


