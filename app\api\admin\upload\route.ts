import { NextResponse } from 'next/server';
import { uploadImage, uploadInfluencerAvatar } from '@/lib/firebase-storage';

// Função para tratar upload de imagens usando Firebase Storage
export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'general';
    
    if (!file) {
      return NextResponse.json(
        { error: 'Nenhum arquivo enviado' },
        { status: 400 }
      );
    }
    
    // Validar tipo de arquivo (apenas imagens)
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Apenas arquivos de imagem são permitidos' },
        { status: 400 }
      );
    }
    
    // Validar tamanho do arquivo (máximo 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Arquivo muito grande. Máximo permitido: 5MB' },
        { status: 400 }
      );
    }
    
    console.log(`Fazendo upload do arquivo ${file.name} para a pasta ${folder}`);
    
    // Se for upload de avatar de influenciador, usar função específica
    let publicUrl: string;
    if (folder === 'influencers') {
      const influencerId = formData.get('influencerId') as string;
      if (influencerId) {
        publicUrl = await uploadInfluencerAvatar(file, influencerId);
      } else {
        // Fallback para função genérica se não houver ID do influenciador
        publicUrl = await uploadImage(file, 'influencers/avatars');
      }
    } else {
      // Usar função genérica para outros tipos de upload
      publicUrl = await uploadImage(file, folder);
    }
    
    console.log(`Upload concluído. URL: ${publicUrl}`);
    
    return NextResponse.json({ url: publicUrl });
  } catch (error) {
    console.error('Erro no upload da imagem:', error);
    return NextResponse.json(
      { error: 'Falha ao fazer upload da imagem' },
      { status: 500 }
    );
  }
}


