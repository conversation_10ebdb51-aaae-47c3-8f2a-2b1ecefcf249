// Interfaces e tipos para o sistema de influenciadores

export interface Brand {
  id: string;
  name: string;
  industry: string;
  logo: string;
  createdAt: string;
  updatedAt: string;
}

export interface Influencer {
  id: number | string;
  name: string;
  location: string;
  avatar: string;
  backgroundImage: string;
  expertise: string;
  expertiseCount: number;
  rating: number;
  earned: string;
  rate: string;
  instagram: string;
  youtube: string;
  tiktok: string;
  category: string;
  bio: string;
  age: number;
  totalFollowers: string;
  mainCategories?: string[];
  mainCategoriesData?: { id: string; name: string }[];
  categories?: string[];
  categoryName?: string;
  isVerified?: boolean;
  gradient: string;
  whatsapp?: string;
  createdAt?: string;
  updatedAt?: string;
  verified?: boolean;
  mainNetwork?: string;
  financialData?: {
    id?: string;
    email?: string;
    brandHistory?: {
      instagram?: string[];
      tiktok?: string[];
      youtube?: string[];
    };
    prices?: {
      youtubeShorts?: number;
      youtubeDedicated?: number;
      youtubeInsertion?: number;
      tiktokVideo?: number;
      instagramReel?: number;
      instagramStory?: number;
      [key: string]: number | undefined;
    };
  };
  socialNetworks?: {
    instagram?: {
      username?: string;
      followers: number;
    };
    youtube?: {
      username?: string;
      followers: number;
    };
    tiktok?: {
      username?: string;
      followers: number;
    };
    facebook?: {
      username?: string;
      followers: number;
    };
    twitch?: {
      username?: string;
      followers: number;
    };
    kwai?: {
      username?: string;
      followers: number;
    };
  };
}

// Gradientes para os cards
export const gradients = [
  "bg-gradient-to-r from-blue-400 via-purple-500 to-orange-400",
  "bg-gradient-to-r from-pink-400 via-red-500 to-yellow-400",
  "bg-gradient-to-r from-green-400 via-blue-500 to-purple-600",
  "bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600",
  "bg-gradient-to-r from-purple-500 via-indigo-500 to-blue-500",
  "bg-gradient-to-r from-red-500 via-pink-500 to-purple-500",
  "bg-gradient-to-r from-blue-500 via-teal-500 to-green-500",
  "bg-gradient-to-r from-orange-500 via-amber-500 to-yellow-500",
];

// Estado global das marcas para compartilhar entre componentes
export let globalBrands: Brand[] = [
  {
    id: "6dydwpg366xKtZRg0pF1",
    name: "Amazon",
    industry: "Ecommerce",
    logo: "/uploads/brands/87056c4e-5788-484e-bc1c-faf4fb63c459.png",
    createdAt: "2025-05-31T06:24:27.796Z",
    updatedAt: "2025-05-31T06:24:27.796Z"
  },
  {
    id: "f81lH6YniCB4RuRMFrHd",
    name: "Amazon 2",
    industry: "Amazon 2",
    logo: "/uploads/brands/d0bc3e69-9d16-4b70-8f92-f47353f83da8.svg",
    createdAt: "2025-06-01T03:11:02.662Z",
    updatedAt: "2025-06-01T03:11:02.662Z"
  }
];


