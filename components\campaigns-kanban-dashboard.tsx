'use client';

import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus, MoreHorizontal, Calendar, User, DollarSign, ChevronLeft, ChevronRight, MessageSquare, Paperclip, Tag, ChevronDown, FileText, Clock, ArrowLeft, Download, Instagram, Music, Youtube, Users, AlertTriangle } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { motion, AnimatePresence } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { InfluencerAvatar } from '@/components/ui/influencer-avatar';
import { Progress } from '@/components/ui/progress';
import { Note } from '@/types/influencer';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/use-auth-v2';

interface WorkflowStage {
  id: string
  label: string
  status: "active" | "completed" | "inactive"
  count?: number
}

interface WorkflowPipelineProps {
  stages: WorkflowStage[]
  className?: string
}

function WorkflowPipeline({ stages, className }: WorkflowPipelineProps) {
  return (
    <div className={cn("flex items-center justify-between w-full", className)}>
      {stages.map((stage, index) => (
        <div key={stage.id} className="flex items-center flex-1">
          {/* Stage */}
          <div
            className={cn(
              "relative flex items-center justify-center px-4 py-2 text-sm font-medium rounded-l-full transition-all duration-200",
              "min-w-[120px] h-10",
              {
                "bg-teal-400 text-white": stage.status === "active" || stage.status === "completed",
                "bg-muted text-muted-foreground": stage.status === "inactive",
              },
              // Remove right rounded corner if not the last item
              index < stages.length - 1 ? "rounded-r-none" : "rounded-r-full",
            )}
          >
            <span className="truncate">{stage.label}</span>
            {stage.count !== undefined && (
              <span className="ml-2 bg-white/20 rounded-full px-2 py-0.5 text-xs">{stage.count}</span>
            )}
          </div>

          {/* Arrow connector */}
          {index < stages.length - 1 && (
            <div
              className={cn("h-0 w-0 border-y-[20px] border-y-transparent", "border-l-[12px]", {
                "border-l-teal-400": stages[index + 1].status === "active" || stages[index + 1].status === "completed",
                "border-l-muted": stages[index + 1].status === "inactive",
              })}
            />
          )}
        </div>
      ))}
    </div>
  )
}

interface Campaign {
  id: string;
  title: string;
  brand: string;
  influencer: string;
  influencerId?: string;
  budget: number;
  deadline: string;
  description: string;
  status: 'em-analise' | 'aprovado' | 'reprovado' | 'proposta-recusada' | 'observacoes';
  priority: 'alta' | 'media' | 'baixa';
  createdAt: string;
  tags: string[];
  progress: number;
  comments: number;
  attachments: number;
  avatars: string[];
  cardColor: string;
  note?: string;
}

interface BudgetProposal {
  id: string;
  campaignId: string;
  // Campos da Agência
  startDate: string;
  influencerName: string;
  channelLink: string;
  chargedPrice: number;
  // Campos da Marca
  pricePerVideo: number;
  videoCount: number;
  finalPrice: number;
  status: 'Aprovado' | 'Reprovado' | 'Em analise' | 'Proposta Recusada';
  observations: string;
  // Contraproposta
  counterProposal?: {
    price: number;
    deliverables: {
      stories: number;
      videos: number;
    };
    notes: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface Column {
  id: string;
  title: string;
  campaigns: Campaign[];
  color: string;
}

const initialColumns: Column[] = [
  {
    id: 'em-analise',
    title: 'Em analise',
    campaigns: [
      {
        id: '1',
        title: 'Search inspirations for upcoming project',
        brand: 'Fashion Brand',
        influencer: 'Ana Silva',
        budget: 5000,
        deadline: '2024-02-15',
        description: 'They like our balance project Miss',
        status: 'em-analise',
        priority: 'alta',
        createdAt: '2024-01-10',
        tags: ['#website', '#client'],
        progress: 40,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: '',
        note: 'They like our balance project Miss'
      },
      {
        id: '2',
        title: 'Ginko mobile app design',
        brand: 'TechCorp',
        influencer: 'João Santos',
        budget: 8000,
        deadline: '2024-02-20',
        description: 'Create user flow, Make wireframe, Design onboarding screens, Make prototype',
        status: 'em-analise',
        priority: 'media',
        createdAt: '2024-01-12',
        tags: ['#mobile app', '#client'],
        progress: 15,
        comments: 7,
        attachments: 2,
        avatars: ['/placeholder-user.jpg'],
        cardColor: '',
        note: 'We have a meeting 2:34 AM'
      },
      {
        id: '3',
        title: 'Make user flow of akua mobile banking app',
        brand: 'FinTech',
        influencer: 'Maria Costa',
        budget: 3000,
        deadline: '2024-02-10',
        description: 'Banking app user flow design',
        status: 'em-analise',
        priority: 'alta',
        createdAt: '2024-01-08',
        tags: ['#mobileapp', '#client'],
        progress: 30,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      }
    ],
    color: 'bg-muted/50 border-muted'
  },
  {
    id: 'aprovado',
    title: 'Aprovado',
    campaigns: [
      {
        id: '4',
        title: 'Wehlu product task and the task process pages',
        brand: 'SportsBrand',
        influencer: 'Maria Costa',
        budget: 3000,
        deadline: '2024-02-10',
        description: 'Have to finish this before weekend',
        status: 'aprovado',
        priority: 'alta',
        createdAt: '2024-01-08',
        tags: ['#dribbble shot', '#product'],
        progress: 90,
        comments: 6,
        attachments: 1,
        avatars: ['/placeholder-user.jpg'],
        cardColor: '',
        note: 'Have to finish this before weekend'
      },
      {
        id: '5',
        title: 'Design CRM shop product page responsive website',
        brand: 'E-commerce',
        influencer: 'Pedro Lima',
        budget: 2000,
        deadline: '2024-02-05',
        description: 'CRM responsive design',
        status: 'aprovado',
        priority: 'baixa',
        createdAt: '2024-01-05',
        tags: ['#products', '#client'],
        progress: 40,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      }
    ],
    color: 'bg-muted/50 border-muted'
  },
  {
    id: 'reprovado',
    title: 'Reprovado',
    campaigns: [
      {
        id: '6',
        title: 'Orypto product landing page create in webflow',
        brand: 'Crypto',
        influencer: 'Ana Silva',
        budget: 4500,
        deadline: '2024-02-25',
        description: 'Crypto landing page',
        status: 'reprovado',
        priority: 'media',
        createdAt: '2024-01-15',
        tags: ['#development', '#client'],
        progress: 0,
        comments: 12,
        attachments: 0,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      },
      {
        id: '7',
        title: 'Natverk video platform web app design and develop',
        brand: 'Media',
        influencer: 'João Santos',
        budget: 6000,
        deadline: '2024-03-01',
        description: 'Video platform design',
        status: 'reprovado',
        priority: 'alta',
        createdAt: '2024-01-20',
        tags: ['#product', '#client'],
        progress: 0,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      },
      {
        id: '8',
        title: 'Redesign grab website landing and login pages',
        brand: 'Transport',
        influencer: 'Carla Oliveira',
        budget: 3500,
        deadline: '2024-02-28',
        description: 'Website redesign project',
        status: 'reprovado',
        priority: 'media',
        createdAt: '2024-01-18',
        tags: ['#website', '#client'],
        progress: 0,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: '',
        note: 'We have a meeting 3:12 AM'
      },
      {
        id: '9',
        title: 'Create Odyah app prototype for Get notification in figma',
        brand: 'Mobile',
        influencer: 'Lucas Silva',
        budget: 2800,
        deadline: '2024-03-05',
        description: 'App prototype creation',
        status: 'reprovado',
        priority: 'baixa',
        createdAt: '2024-01-22',
        tags: ['#mobileapp', '#client'],
        progress: 0,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      }
    ],
    color: 'bg-muted/50 border-muted'
  },
  {
    id: 'proposta-recusada',
    title: 'Proposta Recusada',
    campaigns: [
      {
        id: '10',
        title: 'Affitto product full service',
        brand: 'Real Estate',
        influencer: 'Marina Costa',
        budget: 7500,
        deadline: '2024-01-30',
        description: 'Branding, Mobile app design & development, Landing page design & development, Dashboard design & development, Marketing',
        status: 'proposta-recusada',
        priority: 'alta',
        createdAt: '2024-01-01',
        tags: ['#mobile app', '#client'],
        progress: 100,
        comments: 7,
        attachments: 2,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      },
      {
        id: '11',
        title: 'Design Moli app product page redesign',
        brand: 'E-commerce',
        influencer: 'Rafael Santos',
        budget: 4200,
        deadline: '2024-01-25',
        description: 'Product page redesign',
        status: 'proposta-recusada',
        priority: 'media',
        createdAt: '2024-01-05',
        tags: ['#products', '#client'],
        progress: 100,
        comments: 12,
        attachments: 8,
        avatars: ['/placeholder-user.jpg'],
        cardColor: ''
      }
    ],
    color: 'bg-muted/50 border-muted'
  },
  {
    id: 'observacoes',
    title: 'Observações',
    campaigns: [
      {
        id: '12',
        title: 'Campanha com observações especiais',
        brand: 'Beauty Brand',
        influencer: 'Camila Rodrigues',
        budget: 3200,
        deadline: '2024-02-12',
        description: 'Campanha que requer acompanhamento especial e observações detalhadas',
        status: 'observacoes' as any,
        priority: 'media',
        createdAt: '2024-01-25',
        tags: ['#beauty', '#observacao'],
        progress: 25,
        comments: 5,
        attachments: 3,
        avatars: ['/placeholder-user.jpg'],
        cardColor: '',
        note: 'Necessário acompanhamento especial devido a requisitos específicos da marca'
      }
    ],
    color: 'bg-yellow-50 border-yellow-200'
  }
];

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'alta':
      return 'bg-red-500';
    case 'media':
      return 'bg-yellow-500';
    case 'baixa':
      return 'bg-green-500';
    default:
      return 'bg-muted-foreground';
  }
};



// Função para obter cores de fundo baseado no status
const getCardBackgroundColor = (status: string) => {
  // Retorna fundo branco com sombra sutil na parte inferior para todos os cards
  return 'bg-white border shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1)] dark:bg-background dark:shadow-[0_4px_6px_-1px_rgba(0,0,0,0.3)]';
};

// Função para obter cores baseado no orçamento
const getBudgetIndicatorColor = (budget: number) => {
  if (budget >= 10000) {
    return 'bg-gradient-to-br from-purple-400 to-purple-600 shadow-purple-200 shadow-sm'; // Alto valor
  } else if (budget >= 5000) {
    return 'bg-gradient-to-br from-blue-400 to-blue-600 shadow-blue-200 shadow-sm'; // Médio valor
  } else if (budget >= 2000) {
    return 'bg-gradient-to-br from-green-400 to-green-600 shadow-green-200 shadow-sm'; // Baixo valor
  } else {
    return 'bg-gradient-to-br from-gray-300 to-gray-500 shadow-gray-200 shadow-sm'; // Muito baixo
  }
};

// Função para obter badge de prioridade
const getPriorityBadge = (priority: string) => {
  switch (priority) {
    case 'alta':
      return 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border-red-300 shadow-red-100 shadow-sm';
    case 'media':
      return 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border-yellow-300 shadow-yellow-100 shadow-sm';
    case 'baixa':
      return 'bg-gradient-to-r from-green-100 to-green-200 text-green-800 border-green-300 shadow-green-100 shadow-sm';
    default:
      return 'bg-gradient-to-r from-muted/50 to-muted text-muted-foreground border-muted shadow-muted/50 shadow-sm';
  }
};

// Função para verificar se o deadline está próximo (urgente)
const isDeadlineUrgent = (deadline: string) => {
  const deadlineDate = new Date(deadline);
  const today = new Date();
  const diffTime = deadlineDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 3 && diffDays >= 0; // Próximos 3 dias
};

// Função para obter classe de animação para deadlines urgentes
const getUrgentDeadlineClass = (deadline: string) => {
  return isDeadlineUrgent(deadline) ? 'animate-pulse' : '';
};

// Função para obter cor do progresso
const getProgressColor = (progress: number) => {
  if (progress >= 80) {
    return 'bg-green-500';
  } else if (progress >= 50) {
    return 'bg-blue-500';
  } else if (progress >= 25) {
    return 'bg-yellow-500';
  } else {
    return 'bg-red-500';
  }
};

// Função para obter cor do badge de status
const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case 'aprovado':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'reprovado':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'em-analise':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'proposta-recusada':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'observacoes':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    default:
      return 'bg-muted text-muted-foreground border-muted';
  }
};

// Função para obter label do status
const getStatusLabel = (status: string) => {
  switch (status) {
    case 'em-analise':
      return 'Em analise';
    case 'aprovado':
      return 'Aprovado';
    case 'reprovado':
      return 'Reprovado';
    case 'proposta-recusada':
      return 'Proposta Recusada';
    case 'observacoes':
      return 'Observações';
    default:
      return status;
  }
};

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('pt-BR');
};

// Função para converter campanhas do Firebase para o formato do Kanban
const convertFirebaseCampaignsToKanban = (firebaseCampaigns: any[], influencersList: any[] = []): Column[] => {
  const columns: Column[] = [
    {
      id: 'em-analise',
      title: 'Em analise',
      campaigns: [],
      color: 'bg-muted/50 border-muted'
    },
    {
      id: 'aprovado',
      title: 'Aprovado',
      campaigns: [],
      color: 'bg-muted/50 border-muted'
    },
    {
      id: 'reprovado',
      title: 'Reprovado',
      campaigns: [],
      color: 'bg-muted/50 border-muted'
    },
    {
      id: 'proposta-recusada',
      title: 'Proposta Recusada',
      campaigns: [],
      color: 'bg-muted/50 border-muted'
    },
    {
      id: 'observacoes',
      title: 'Observações',
      campaigns: [],
      color: 'bg-yellow-50 border-yellow-200'
    }
  ];

  firebaseCampaigns.forEach((fbCampaign) => {
    // Buscar dados do influenciador na lista carregada
    let avatarPath = '/placeholder-user.jpg';
    console.log('🔍 Processando campanha:', fbCampaign.name);
    console.log('📋 Lista de influenciadores disponível:', influencersList.length, 'influenciadores');
    
    if (fbCampaign.influencers && fbCampaign.influencers.length > 0) {
      const influencerId = fbCampaign.influencers[0].influencerId;
      console.log('🎯 ID do influenciador da campanha:', influencerId);
      
      if (influencerId) {
        const influencerData = influencersList.find(inf => inf && inf.id === influencerId);
        console.log('👤 Dados do influenciador encontrados:', !!influencerData);
        
        if (influencerData) {
          console.log('🖼️ Avatar do influenciador:', influencerData.avatar);
          if (influencerData.avatar) {
            avatarPath = influencerData.avatar;
          }
        }
      }
      // Fallback: usar avatar diretamente da campanha se existir
      else if (fbCampaign.influencers[0].avatar) {
        console.log('🔄 Usando avatar da campanha como fallback:', fbCampaign.influencers[0].avatar);
        avatarPath = fbCampaign.influencers[0].avatar;
      }
    }
    
    console.log('✅ Avatar final definido:', avatarPath);

    const campaign: Campaign = {
      id: fbCampaign.id,
      title: fbCampaign.name || 'Campanha sem título',
      brand: fbCampaign.brandName || 'Marca não informada',
      influencer: fbCampaign.influencers && fbCampaign.influencers.length > 0 
        ? fbCampaign.influencers[0].name || 'Nome não informado'
        : 'Nenhum influenciador atribuído',
      influencerId: fbCampaign.influencers && fbCampaign.influencers.length > 0 
        ? fbCampaign.influencers[0].influencerId
        : undefined,
      budget: fbCampaign.budget || 0,
      deadline: fbCampaign.endDate || new Date().toISOString(),
      description: fbCampaign.description || fbCampaign.notes || '',
      status: (fbCampaign.status as Campaign['status']) || 'em-analise',
      priority: fbCampaign.priority || 'media',
      createdAt: fbCampaign.createdAt || new Date().toISOString(),
      tags: fbCampaign.tags || ['#campanha'],
      progress: fbCampaign.progress || 0,
      comments: 0,
      attachments: 0,
      avatars: [avatarPath],
      cardColor: '',
      note: fbCampaign.notes || ''
    };

    const columnIndex = columns.findIndex(col => col.id === campaign.status);
    if (columnIndex !== -1) {
      columns[columnIndex].campaigns.push(campaign);
    } else {
      // Se o status não for encontrado, adicionar em 'em-analise'
      columns[0].campaigns.push(campaign);
    }
  });

  return columns;
};

// Dados de exemplo para propostas de orçamento
const initialBudgetProposals: BudgetProposal[] = [
  {
    id: '1',
    campaignId: '1',
    startDate: '2024-01-15',
    influencerName: 'Ana Silva',
    channelLink: 'https://instagram.com/anasilva',
    chargedPrice: 5000,
    pricePerVideo: 2500,
    videoCount: 2,
    finalPrice: 5000,
    status: 'Em analise',
    observations: 'Aguardando aprovação da marca para início da campanha.',
    counterProposal: {
      price: 4500,
      deliverables: {
        stories: 5,
        videos: 2
      },
      notes: 'Proposta ajustada com stories adicionais'
    },
    createdAt: '2024-01-10',
    updatedAt: '2024-01-15'
  },
  {
    id: '2',
    campaignId: '4',
    startDate: '2024-01-20',
    influencerName: 'Maria Costa',
    channelLink: 'https://youtube.com/mariacosta',
    chargedPrice: 3000,
    pricePerVideo: 1500,
    videoCount: 2,
    finalPrice: 3000,
    status: 'Aprovado',
    observations: 'Campanha aprovada. Início previsto para próxima semana.',
    createdAt: '2024-01-18',
    updatedAt: '2024-01-20'
  }
];

interface CampaignsKanbanDashboardProps {
  brand?: any;
  brandId?: string;
  onClose?: () => void;
}

export function CampaignsKanbanDashboard({ brand, brandId, onClose }: CampaignsKanbanDashboardProps = {}) {
  const { currentUser } = useAuth();
  const [columns, setColumns] = useState<Column[]>(initialColumns);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(new Date());
  const [brands, setBrands] = useState<any[]>([]);
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [influencers, setInfluencers] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [filters, setFilters] = useState<any[]>([]);
  const [recentNotes, setRecentNotes] = useState<Note[]>([]);
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);
  const [newNoteTitle, setNewNoteTitle] = useState('');
  const [newNoteContent, setNewNoteContent] = useState('');
  const [newNoteType, setNewNoteType] = useState('geral');
  const [newNoteInfluencerId, setNewNoteInfluencerId] = useState('');
  const [budgetProposals, setBudgetProposals] = useState<BudgetProposal[]>(initialBudgetProposals);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [savingStatus, setSavingStatus] = useState<{[key: string]: 'saving' | 'saved' | 'error'}>({});
  const [isDragging, setIsDragging] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<string | null>(brandId || null);
  const [selectedCampaignFilter, setSelectedCampaignFilter] = useState<string | null>(null);
  const [allCampaigns, setAllCampaigns] = useState<any[]>([]);
  
  // Estados para o modal de criação de campanha
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    brand: '',
    influencer: '',
    budget: '',
    startDate: '',
    endDate: '',
    description: '',
    status: 'em-analise'
  });
  const [isCreating, setIsCreating] = useState(false);
  const [selectedSocialNetwork, setSelectedSocialNetwork] = useState<'all' | 'instagram' | 'tiktok' | 'youtube'>('all');

  // Pipeline stages data
  const pipelineStages: WorkflowStage[] = [
    { id: 'new-leads', label: 'New Leads', status: 'completed' },
    { id: 'request-received', label: 'Request Rec...', status: 'completed' },
    { id: 'in-draft', label: 'In Draft', status: 'completed' },
    { id: 'proposal-sent', label: 'Proposal Sent', status: 'active' },
    { id: 'approved', label: 'Approved', status: 'inactive' },
    { id: 'rejected', label: 'Rejected', status: 'inactive' },
  ];
  const [loading, setLoading] = useState(true);

  // Carregar dados do Firebase
  useEffect(() => {
    const loadFirebaseData = async () => {
      setLoading(true);
      try {
        // Carregar marcas
        const brandsResponse = await fetch('/api/brands');
        if (brandsResponse.ok) {
          const brandsData = await brandsResponse.json();
          // A API retorna { brands: [...] }, então precisamos acessar a propriedade brands
          const brandsArray = brandsData.brands || [];
          setBrands(Array.isArray(brandsArray) ? brandsArray : []);
          console.log('Debug - Marcas carregadas:', brandsArray);
        } else {
          console.error('Erro ao carregar marcas - Status:', brandsResponse.status);
          setBrands([]);
        }

        // Carregar influenciadores primeiro
        const influencersResponse = await fetch('/api/influencers');
        let influencersData = [];
        if (influencersResponse.ok) {
          influencersData = await influencersResponse.json();
          setInfluencers(influencersData);
        }

        // Carregar todas as campanhas (sem filtro inicial)
        const campaignsResponse = await fetch('/api/campaigns');
        if (campaignsResponse.ok) {
          const campaignsData = await campaignsResponse.json();
          setAllCampaigns(campaignsData);
          setCampaigns(campaignsData);
          
          // Converter campanhas do Firebase para o formato do Kanban
          const convertedColumns = convertFirebaseCampaignsToKanban(campaignsData, influencersData);
          setColumns(convertedColumns);
        }

        // Carregar categorias
        const categoriesResponse = await fetch('/api/categories');
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategories(categoriesData);
        }

        // Carregar filtros
        const filtersResponse = await fetch('/api/filters');
        if (filtersResponse.ok) {
          const filtersData = await filtersResponse.json();
          setFilters(filtersData);
        }
      } catch (error) {
        console.error('Erro ao carregar dados do Firebase:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFirebaseData();
  }, []);

  // Filtrar campanhas quando marca ou campanha específica for selecionada
  useEffect(() => {
    let filteredCampaigns = allCampaigns;

    // Debug: Log dos dados para entender a estrutura
    console.log('Debug - Todas as campanhas:', allCampaigns);
    console.log('Debug - Marca selecionada:', selectedBrand);
    console.log('Debug - Marcas disponíveis:', brands);

    // Filtrar por marca selecionada
    if (selectedBrand && selectedBrand !== 'all-brands') {
      // Encontrar a marca selecionada para obter tanto o nome quanto o ID
      const selectedBrandData = brands.find(brand => brand.name === selectedBrand || brand.id === selectedBrand);
      
      filteredCampaigns = filteredCampaigns.filter(campaign => {
        // Verificar múltiplos campos possíveis para a marca
        const matchesClient = campaign.client === selectedBrand || campaign.client === selectedBrandData?.id;
        const matchesBrand = campaign.brand === selectedBrand || campaign.brand === selectedBrandData?.id;
        const matchesBrandName = campaign.brandName === selectedBrand || campaign.brandName === selectedBrandData?.id;
        const matchesBrandId = campaign.brandId === selectedBrand || campaign.brandId === selectedBrandData?.id;
        
        const matches = matchesClient || matchesBrand || matchesBrandName || matchesBrandId;
        
        // Debug: Log para cada campanha
        console.log(`Debug - Campanha "${campaign.name || campaign.title}": client=${campaign.client}, brand=${campaign.brand}, brandName=${campaign.brandName}, brandId=${campaign.brandId}, matches=${matches}`);
        
        return matches;
      });
    }

    // Filtrar por campanha específica
    if (selectedCampaignFilter && selectedCampaignFilter !== 'all-campaigns') {
      filteredCampaigns = filteredCampaigns.filter(campaign => 
        campaign.id === selectedCampaignFilter
      );
    }

    console.log('Debug - Campanhas filtradas:', filteredCampaigns);
    setCampaigns(filteredCampaigns);
    
    // Converter campanhas filtradas para o formato do Kanban
    const convertedColumns = convertFirebaseCampaignsToKanban(filteredCampaigns, influencers);
    console.log('Debug - Colunas convertidas:', convertedColumns);
    setColumns(convertedColumns);
  }, [selectedBrand, selectedCampaignFilter, allCampaigns, brands, influencers]);

  // Carregar anotações recentes
  useEffect(() => {
    const loadRecentNotes = async () => {
      try {
        const response = await fetch('/api/notes?all=true');
        if (response.ok) {
          const notesData = await response.json();
          // Ordenar por data de criação e pegar as 5 mais recentes
          const sortedNotes = notesData.sort((a: Note, b: Note) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          ).slice(0, 5);
          setRecentNotes(sortedNotes);
        }
      } catch (error) {
        console.error('Erro ao carregar anotações:', error);
      }
    };

    loadRecentNotes();
  }, []);

  // Função para adicionar nova anotação
  const handleAddNote = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newNoteTitle.trim() || !newNoteContent.trim()) return;
    
    try {
      const response = await fetch('/api/notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          influencerId: newNoteInfluencerId.trim() || 'geral',
          title: newNoteTitle.trim(),
          content: newNoteContent.trim(),
          type: newNoteType
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Adicionar nova anotação à lista
        const newNote: Note = {
          id: data.id,
          title: newNoteTitle.trim(),
          content: newNoteContent.trim(),
          type: newNoteType,
          influencerId: newNoteInfluencerId || undefined,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'current-user'
        };
        
        setRecentNotes(prev => [newNote, ...prev.slice(0, 4)]);
        
        // Limpar formulário
        setNewNoteTitle('');
        setNewNoteContent('');
        setNewNoteType('geral');
        setNewNoteInfluencerId('');
        setShowAddNoteModal(false);
      }
    } catch (error) {
      console.error('Erro ao adicionar anotação:', error);
    }
  };

  // Gerar datas para a timeline (próximas 2 semanas)
  const generateTimelineDates = () => {
    const dates = [];
    const startDate = new Date(currentWeekStart);
    startDate.setDate(startDate.getDate() - startDate.getDay()); // Começar na segunda-feira
    
    for (let i = 0; i < 14; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  const timelineDates = generateTimelineDates();

  // Filtrar campanhas por data selecionada
  const getFilteredColumns = () => {
    if (!selectedDate) return columns;
    
    return columns.map(column => ({
      ...column,
      campaigns: column.campaigns.filter(campaign => {
        const campaignDate = new Date(campaign.deadline).toDateString();
        const filterDate = new Date(selectedDate).toDateString();
        return campaignDate === filterDate;
      })
    }));
  };

  const filteredColumns = getFilteredColumns();

  // Contar campanhas por data
  const getCampaignCountForDate = (date: Date) => {
    const dateString = date.toDateString();
    return columns.reduce((total, column) => {
      return total + column.campaigns.filter(campaign => {
        return new Date(campaign.deadline).toDateString() === dateString;
      }).length;
    }, 0);
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentWeekStart);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentWeekStart(newDate);
  };

  // Obter campanhas únicas por marca selecionada
  const getCampaignsByBrand = (brandName: string) => {
    return allCampaigns.filter(campaign => 
      campaign.client === brandName || campaign.brand === brandName
    );
  };

  // Obter marca selecionada
  const getSelectedBrandData = () => {
    if (!selectedBrand) return null;
    return brands.find(brand => brand.name === selectedBrand || brand.id === selectedBrand);
  };

  // Resetar filtros
  const resetFilters = () => {
    setSelectedBrand('all-brands');
    setSelectedCampaignFilter('all-campaigns');
  };

  // Função para filtrar influenciadores por rede social
  const getFilteredInfluencers = () => {
    if (selectedSocialNetwork === 'all') {
      return influencers;
    }

    return influencers.filter(influencer => {
      switch (selectedSocialNetwork) {
        case 'instagram':
          return influencer.socialNetworks?.instagram && 
                 influencer.socialNetworks.instagram.followers > 0;
        case 'tiktok':
          return influencer.socialNetworks?.tiktok && 
                 influencer.socialNetworks.tiktok.followers > 0;
        case 'youtube':
          return influencer.socialNetworks?.youtube && 
                 influencer.socialNetworks.youtube.followers > 0;
        default:
          return true;
      }
    }).sort((a, b) => {
      // Ordenar por número de seguidores na rede social selecionada
      let followersA = 0;
      let followersB = 0;
      
      if (selectedSocialNetwork === 'instagram') {
        followersA = a.socialNetworks?.instagram?.followers || 0;
        followersB = b.socialNetworks?.instagram?.followers || 0;
      } else if (selectedSocialNetwork === 'tiktok') {
        followersA = a.socialNetworks?.tiktok?.followers || 0;
        followersB = b.socialNetworks?.tiktok?.followers || 0;
      } else if (selectedSocialNetwork === 'youtube') {
        followersA = a.socialNetworks?.youtube?.followers || 0;
        followersB = b.socialNetworks?.youtube?.followers || 0;
      }
      
      return followersB - followersA; // Ordem decrescente
    });
  };

  // Função para formatar número de seguidores
  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Função para obter ícone da rede social
  const getSocialNetworkIcon = (network: string) => {
    switch (network) {
      case 'instagram':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
          </svg>
        );
      case 'tiktok':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 448 512">
            <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
          </svg>
        );
      case 'youtube':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
          </svg>
        );
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  // Função para criar nova campanha
  const handleCreateCampaign = async () => {
    if (!newCampaign.name.trim() || !newCampaign.brand || !newCampaign.influencer) {
      alert('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    setIsCreating(true);
    
    try {
      // Encontrar dados da marca selecionada
      const selectedBrandData = brands.find(brand => brand.name === newCampaign.brand || brand.id === newCampaign.brand);
      
      // Encontrar dados do influenciador selecionado
      const selectedInfluencerData = influencers.find(inf => inf.name === newCampaign.influencer || inf.id === newCampaign.influencer);
      
      const campaignData = {
        name: newCampaign.name.trim(),
        description: newCampaign.description.trim(),
        brandId: selectedBrandData?.id || newCampaign.brand,
        brandName: selectedBrandData?.name || newCampaign.brand,
        budget: parseFloat(newCampaign.budget) || 0,
        startDate: newCampaign.startDate,
        endDate: newCampaign.endDate,
        status: newCampaign.status,
        priority: 'medium',
        influencers: [{
          id: selectedInfluencerData?.id || newCampaign.influencer,
          name: selectedInfluencerData?.name || newCampaign.influencer,
          role: 'primary'
        }],
        objectives: '',
        targetAudience: '',
        deliverables: '',
        notes: '',
        tags: []
      };

      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      });

      if (response.ok) {
        const createdCampaign = await response.json();
        
        // Atualizar lista de campanhas
        setAllCampaigns(prev => [...prev, createdCampaign]);
        setCampaigns(prev => [...prev, createdCampaign]);
        
        // Atualizar colunas do Kanban
        const updatedCampaigns = [...allCampaigns, createdCampaign];
        const convertedColumns = convertFirebaseCampaignsToKanban(updatedCampaigns, influencers);
        setColumns(convertedColumns);
        
        // Resetar formulário e fechar modal
        setSelectedSocialNetwork('all');
        setNewCampaign({
          name: '',
          brand: '',
          influencer: '',
          budget: '',
          startDate: '',
          endDate: '',
          description: '',
          status: 'em-analise'
        });
        setIsCreateModalOpen(false);
        
        alert('Campanha criada com sucesso!');
      } else {
        throw new Error('Erro ao criar campanha');
      }
    } catch (error) {
      console.error('Erro ao criar campanha:', error);
      alert('Erro ao criar campanha. Tente novamente.');
    } finally {
      setIsCreating(false);
    }
  };

  const onDragStart = () => {
    setIsDragging(true);
  };

  const onDragEnd = async (result: DropResult) => {
    setIsDragging(false);
    const { destination, source, draggableId } = result;

    if (!destination) {
      return;
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const sourceColumn = columns.find(col => col.id === source.droppableId);
    const destColumn = columns.find(col => col.id === destination.droppableId);

    if (!sourceColumn || !destColumn) {
      return;
    }

    if (sourceColumn === destColumn) {
      // Reordenar dentro da mesma coluna
      const newCampaigns = Array.from(sourceColumn.campaigns);
      const [removed] = newCampaigns.splice(source.index, 1);
      newCampaigns.splice(destination.index, 0, removed);

      const newColumns = columns.map(col => {
        if (col.id === sourceColumn.id) {
          return {
            ...col,
            campaigns: newCampaigns
          };
        }
        return col;
      });

      setColumns(newColumns);
    } else {
      // Mover entre colunas diferentes
      const sourceCampaigns = Array.from(sourceColumn.campaigns);
      const destCampaigns = Array.from(destColumn.campaigns);
      const [removed] = sourceCampaigns.splice(source.index, 1);
      
      // Atualizar o status da campanha
      const updatedCampaign = {
        ...removed,
        status: destColumn.id as Campaign['status']
      };
      
      destCampaigns.splice(destination.index, 0, updatedCampaign);

      const newColumns = columns.map(col => {
        if (col.id === sourceColumn.id) {
          return {
            ...col,
            campaigns: sourceCampaigns
          };
        }
        if (col.id === destColumn.id) {
          return {
            ...col,
            campaigns: destCampaigns
          };
        }
        return col;
      });

      setColumns(newColumns);

      // Persistir mudança no banco de dados
      try {
        // Indicar que está salvando
        setSavingStatus(prev => ({ ...prev, [updatedCampaign.id]: 'saving' }));

        // Verificar conectividade antes de fazer a requisição
        if (!navigator.onLine) {
          throw new Error('Sem conexão com a internet');
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos timeout

        const response = await fetch(`/api/campaigns?id=${updatedCampaign.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: updatedCampaign.status,
            updatedAt: new Date().toISOString()
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Erro desconhecido');
          throw new Error(`Erro ${response.status}: ${errorText}`);
        }

        // Indicar sucesso
        setSavingStatus(prev => ({ ...prev, [updatedCampaign.id]: 'saved' }));
        
        // Remover indicador após 2 segundos
        setTimeout(() => {
          setSavingStatus(prev => ({ ...prev, [updatedCampaign.id]: undefined }));
        }, 2000);

        console.log('Status da campanha atualizado com sucesso no banco de dados');
      } catch (error) {
        console.error('Erro ao salvar mudança no banco:', error);
        
        // Indicar erro
        setSavingStatus(prev => ({ ...prev, [updatedCampaign.id]: 'error' }));
        
        // Reverter mudança local em caso de erro
        const revertedColumns = columns.map(col => {
          if (col.id === sourceColumn.id) {
            return {
              ...col,
              campaigns: [...sourceColumn.campaigns]
            };
          }
          if (col.id === destColumn.id) {
            return {
              ...col,
              campaigns: [...destColumn.campaigns]
            };
          }
          return col;
        });
        
        setColumns(revertedColumns);
        
        // Mostrar mensagem de erro específica para o usuário
        let errorMessage = 'Erro ao salvar mudança. Tente novamente.';
        
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            errorMessage = 'Tempo limite excedido. Verifique sua conexão e tente novamente.';
          } else if (error.message.includes('Sem conexão')) {
            errorMessage = 'Sem conexão com a internet. Verifique sua conexão e tente novamente.';
          } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';
          }
        }
        
        alert(errorMessage);
        
        // Remover indicador de erro após 3 segundos
        setTimeout(() => {
          setSavingStatus(prev => ({ ...prev, [updatedCampaign.id]: undefined }));
        }, 3000);
      }
    }
  };

  const getTotalCampaigns = () => {
    return columns.reduce((total, column) => total + column.campaigns.length, 0);
  };

  const getTotalBudget = () => {
    return columns.reduce((total, column) => {
      return total + column.campaigns.reduce((colTotal, campaign) => colTotal + campaign.budget, 0);
    }, 0);
  };

  // Loading state
  if (loading) {
    return (
      <div className="p-6 bg-white min-h-screen flex items-center justify-center dark:bg-background">
        <div className="text-center">
          <img 
              src="/loader-dm.webp" 
              alt="Carregando..."
              className="w-32 h-32 object-contain mx-auto mb-4"
            />
        
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white dark:bg-black min-h-screen">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          
          {/* Texto de Boas-vindas e Seletor de Marca */}
          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-4">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Bem-vindo,</span>
            <span className="text-lg font-semibold text-foreground">
                   {/* Usar dados do usuário autenticado */}
                   {(() => {
                     const name = currentUser?.name || currentUser?.email || 'Usuário';
                     return name.charAt(0).toUpperCase() + name.slice(1);
                   })()}
                 </span>
              </div>
              
              {/* Botão de Voltar quando há filtros ativos */}
              {((selectedBrand && selectedBrand !== 'all-brands') || (selectedCampaignFilter && selectedCampaignFilter !== 'all-campaigns')) && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={resetFilters}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Voltar
                </Button>
              )}
            </div>
            
            {/* Filtros de Marca */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
               
                <div className="flex items-center gap-2 flex-wrap">
                  {/* Botão Todas as Marcas */}
                  <Button
                    variant={selectedBrand === null || selectedBrand === 'all-brands' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      setSelectedBrand(null);
                      setSelectedCampaignFilter(null);
                    }}
                    className="h-8 px-3 text-xs font-medium transition-all duration-200"
                  >
                    Todas
                  </Button>
                  
                  {/* Marcas do Banco de Dados */}
                  {(Array.isArray(brands) ? brands : []).slice(0, 5).map((brand) => {
                    const isSelected = selectedBrand === brand.name;
                    
                    return (
                      <Button
                        key={brand.id}
                        variant={isSelected ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                          setSelectedBrand(brand.name);
                          setSelectedCampaignFilter(null);
                        }}
                        className={`h-8 px-3 py-2 text-xs font-medium transition-all duration-200 flex items-center gap-2 ${
                          isSelected 
                            ? 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600' 
                            : 'hover:bg-muted/50 dark:hover:bg-muted/20'
                        }`}
                      >
                        <div 
                          className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0"
                          style={{
                            backgroundColor: brand.logoBackgroundColor || '#1f2937'
                          }}
                        >
                          <img 
                            src={brand.logo || '/placeholder-logo.svg'} 
                            alt={brand.name}
                            className="max-w-full max-h-full object-contain rounded-full p-0.5"
                          />
                        </div>
                        {brand.name}
                      </Button>
                    );
                  })}
                  
                  {/* Dropdown para Outras Marcas */}
                  {brands.length > 5 && (
                    <Select 
                      value={selectedBrand && !(Array.isArray(brands) ? brands : []).slice(0, 5).some(mainBrand => 
                        selectedBrand === mainBrand.name
                      ) ? selectedBrand : ''} 
                      onValueChange={(value) => {
                        setSelectedBrand(value || null);
                        setSelectedCampaignFilter(null);
                      }}
                    >
                      <SelectTrigger className="w-[120px] h-8 text-xs">
                        <SelectValue placeholder="Outras" />
                      </SelectTrigger>
                      <SelectContent>
                        {brands
                          .slice(5)
                          .map((brand) => (
                            <SelectItem key={brand.id} value={brand.name}>
                              <div className="flex items-center gap-2 py-1">
                                <div 
                                  className="w-5 h-5 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0"
                                  style={{
                                    backgroundColor: brand.logoBackgroundColor || '#1f2937'
                                  }}
                                >
                                  <img 
                                    src={brand.logo || '/placeholder-logo.svg'} 
                                    alt={brand.name}
                                    className="max-w-full max-h-full object-contain rounded-full p-0.5"
                                  />
                                </div>
                                {brand.name}
                              </div>
                            </SelectItem>
                          ))
                        }
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
              
              {/* Seletor de Campanha (quando marca está selecionada) */}
              {selectedBrand && selectedBrand !== 'all-brands' && (
                <div className="flex items-center gap-2">
                  <Label htmlFor="campaign-select" className="text-sm font-medium text-muted-foreground">
                    Campanha:
                  </Label>
                  <Select 
                    value={selectedCampaignFilter || ''} 
                    onValueChange={(value) => setSelectedCampaignFilter(value || null)}
                  >
                    <SelectTrigger className="w-[250px]">
                      <SelectValue placeholder="Todas as campanhas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-campaigns">Todas as campanhas</SelectItem>
                      {getCampaignsByBrand(selectedBrand).map((campaign) => (
                        <SelectItem key={campaign.id} value={campaign.id}>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${
                              campaign.status === 'aprovado' ? 'bg-green-500' :
                              campaign.status === 'reprovado' ? 'bg-red-500' :
                              campaign.status === 'em-analise' ? 'bg-blue-500' :
                              campaign.status === 'proposta-recusada' ? 'bg-orange-500' :
                              'bg-yellow-500'
                            }`}></div>
                            <span className="truncate">{campaign.name || campaign.title || 'Campanha sem título'}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            
            {/* Informações da marca selecionada */}
            {selectedBrand && selectedBrand !== 'all-brands' && (
              <div className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-12 h-12 rounded-full flex items-center justify-center"
                    style={{
                      backgroundColor: getSelectedBrandData()?.logoBackgroundColor || '#1f2937'
                    }}
                  >
                    <img 
                      src={getSelectedBrandData()?.logo || '/placeholder-logo.svg'} 
                      alt={selectedBrand}
                      className="max-w-full max-h-full object-contain p-1 rounded-full"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-foreground">{selectedBrand}</p>
                  <p className="text-xs text-muted-foreground">
                      {getCampaignsByBrand(selectedBrand).length} campanhas no histórico
                    </p>
                  </div>
                </div>
                
                {selectedCampaignFilter && (
                  <div className="flex items-center gap-2 px-3 py-1 bg-white rounded-full border">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-xs font-medium text-muted-foreground">
                      Campanha específica selecionada
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Logos das Marcas e Botões de Navegação */}
          <div className="flex items-center gap-4">
            {/* Logos das Marcas */}
            <div className="flex items-center">
              {(Array.isArray(brands) ? brands : []).slice(0, 5).map((brand, index) => (
                <div 
                  key={brand.id} 
                  className="relative" 
                  style={{ marginLeft: index > 0 ? '-15px' : '0' }}
                >
                  <div 
                    className={`w-11 h-11 rounded-full flex items-center justify-center border border-white shadow-lg hover:scale-110 hover:z-10 transition-all duration-200 cursor-pointer relative ${
                      selectedBrand === brand.name ? 'ring-2 ring-blue-500' : ''
                    }`}
                    title={brand.name}
                    style={{ 
                      zIndex: (Array.isArray(brands) ? brands.length : 0) - index,
                      backgroundColor: brand.logoBackgroundColor || '#1f2937'
                    }}
                    onClick={() => {
                      if (selectedBrand === brand.name) {
                        resetFilters();
                      } else {
                        setSelectedBrand(brand.name);
                        setSelectedCampaignFilter(null);
                      }
                    }}
                  >
                    <div className="w-10 h-10 flex items-center justify-center rounded-full overflow-hidden">
                      <img 
                        src={brand.logo || '/placeholder-logo.svg'} 
                        alt={brand.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  {index === 4 && brands.length > 5 && (
                    <div className="absolute -top-1 -right-1 bg-muted-foreground text-white text-xs rounded-full w-5 h-5 flex items-center justify-center z-20">
                      +{brands.length - 5}
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            {/* Botões de Navegação */}
            <div className="flex items-center gap-2">
              <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                <DialogTrigger asChild>
                  <Button 
                    variant="default" 
                    size="sm" 
                    className="bg-[#9810fa] text-white hover:bg-[#ff0074] flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Nova Campanha
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Criar Nova Campanha</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="campaign-name">Nome da Campanha</Label>
                        <Input 
                          id="campaign-name" 
                          placeholder="Digite o nome da campanha"
                          value={newCampaign.name}
                          onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="campaign-brand">Marca</Label>
                        <Select value={newCampaign.brand} onValueChange={(value) => setNewCampaign(prev => ({ ...prev, brand: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a marca" />
                          </SelectTrigger>
                          <SelectContent>
                            {brands.map((brand) => (
                              <SelectItem key={brand.id} value={brand.name}>
                                <div className="flex items-center gap-2">
                                  <div 
                                    className="w-4 h-4 rounded-full flex items-center justify-center relative overflow-hidden"
                                    style={{
                                      backgroundColor: brand.logoBackgroundColor || '#1f2937'
                                    }}
                                  >
                                    <img 
                                      src={brand.logo || '/placeholder-logo.svg'} 
                                      alt={brand.name}
                                      className="max-w-full max-h-full object-contain rounded-full p-0.5"
                                    />
                                  </div>
                                  {brand.name}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <Label>Filtrar por Rede Social</Label>
                        <div className="flex gap-2 mt-2">
                          <Button
                            type="button"
                            variant={selectedSocialNetwork === 'all' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setSelectedSocialNetwork('all')}
                            className="flex items-center gap-1"
                          >
                            <Users className="w-4 h-4" /> Todos
                          </Button>
                          <Button
                            type="button"
                            variant={selectedSocialNetwork === 'instagram' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setSelectedSocialNetwork('instagram')}
                            className="flex items-center gap-1"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                            </svg>
                            Instagram
                          </Button>
                          <Button
                            type="button"
                            variant={selectedSocialNetwork === 'tiktok' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setSelectedSocialNetwork('tiktok')}
                            className="flex items-center gap-1"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 448 512">
                              <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                            </svg>
                            TikTok
                          </Button>
                          <Button
                            type="button"
                            variant={selectedSocialNetwork === 'youtube' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setSelectedSocialNetwork('youtube')}
                            className="flex items-center gap-1"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                            </svg>
                            YouTube
                          </Button>
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor="campaign-influencer">Influenciador</Label>
                        <Select value={newCampaign.influencer} onValueChange={(value) => setNewCampaign(prev => ({ ...prev, influencer: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o influenciador" />
                          </SelectTrigger>
                          <SelectContent className="max-h-60">
                            {getFilteredInfluencers().map((influencer) => {
                              const socialData = selectedSocialNetwork !== 'all' 
                                ? influencer.socialNetworks?.[selectedSocialNetwork]
                                : null;
                              
                              return (
                                <SelectItem key={influencer.id} value={influencer.name}>
                                  <div className="flex items-center justify-between w-full">
                                    <div className="flex items-center gap-2">
                                      {influencer.id ? (
                                        <InfluencerAvatar
                                          influencerId={influencer.id}
                                          influencerName={influencer.name}
                                          avatarPath={influencer.avatar}
                                          avatarUrl={influencer.avatar}
                                          className="w-5 h-5"
                                        />
                                      ) : (
                                        <Avatar className="w-5 h-5">
                                          <AvatarImage src={influencer.avatar} />
                                          <AvatarFallback className="text-xs">
                                            {influencer.name.charAt(0)}
                                          </AvatarFallback>
                                        </Avatar>
                                      )}
                                      <span>{influencer.name}</span>
                                    </div>
                                    {socialData && (
                                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                        <span>{getSocialNetworkIcon(selectedSocialNetwork)}</span>
                                        <span>{formatFollowers(socialData.followers)}</span>
                                      </div>
                                    )}
                                  </div>
                                </SelectItem>
                              );
                            })}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="campaign-budget">Orçamento (R$)</Label>
                        <Input 
                          id="campaign-budget" 
                          type="number"
                          placeholder="0,00"
                          value={newCampaign.budget}
                          onChange={(e) => setNewCampaign(prev => ({ ...prev, budget: e.target.value }))}
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="campaign-start">Data de Início</Label>
                        <Input 
                          id="campaign-start" 
                          type="date"
                          value={newCampaign.startDate}
                          onChange={(e) => setNewCampaign(prev => ({ ...prev, startDate: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="campaign-end">Data de Término</Label>
                        <Input 
                          id="campaign-end" 
                          type="date"
                          value={newCampaign.endDate}
                          onChange={(e) => setNewCampaign(prev => ({ ...prev, endDate: e.target.value }))}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="campaign-description">Descrição</Label>
                      <Textarea 
                        id="campaign-description" 
                        placeholder="Descreva os objetivos e detalhes da campanha"
                        rows={3}
                        value={newCampaign.description}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, description: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="campaign-status">Status Inicial</Label>
                      <Select value={newCampaign.status} onValueChange={(value) => setNewCampaign(prev => ({ ...prev, status: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="em-analise">EM ANÁLISE</SelectItem>
                          <SelectItem value="aprovado">APROVADO</SelectItem>
                          <SelectItem value="reprovado">REPROVADO</SelectItem>
                          <SelectItem value="proposta-recusada">PROPOSTA RECUSADA</SelectItem>
                          <SelectItem value="observacoes">OBSERVAÇÕES</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex justify-end gap-2 pt-4">
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setIsCreateModalOpen(false);
                          setSelectedSocialNetwork('all');
                          setNewCampaign({
                            name: '',
                            brand: '',
                            influencer: '',
                            budget: '',
                            startDate: '',
                            endDate: '',
                            description: '',
                            status: 'em-analise'
                          });
                        }}
                      >
                        Cancelar
                      </Button>
                      <Button 
                        className="bg-[#9810fa] hover:bg-[#ff0074]"
                        onClick={handleCreateCampaign}
                        disabled={isCreating}
                      >
                        {isCreating ? 'Criando...' : 'Criar Campanha'}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              
              <Button 
                variant="default" 
                size="sm" 
                className="bg-black text-white hover:bg-muted dark:bg-white dark:text-black"
              >
                Kanban
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-foreground border-muted hover:bg-muted/50"
              >
                Marcas
              </Button>
            </div>
          </div>
        </div>
        
        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {selectedBrand && selectedBrand !== 'all-brands' ? `Campanhas - ${selectedBrand}` : 'Total de Campanhas'}
                  </p>
                  <p className="text-2xl font-bold text-foreground">{getTotalCampaigns()}</p>
                  {selectedBrand && selectedBrand !== 'all-brands' && (
                    <p className="text-xs text-muted-foreground mt-1">
                      de {getCampaignsByBrand(selectedBrand).length} no histórico
                    </p>
                  )}
                </div>
                <div className="p-2 bg-muted/50 rounded-lg">
                  <Calendar className="w-5 h-5 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {selectedBrand && selectedBrand !== 'all-brands' ? `Orçamento - ${selectedBrand}` : 'Orçamento Total'}
                  </p>
                  <p className="text-2xl font-bold text-foreground">{formatCurrency(getTotalBudget())}</p>
                  {selectedBrand && selectedBrand !== 'all-brands' && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {selectedCampaignFilter && selectedCampaignFilter !== 'all-campaigns' ? 'campanha específica' : 'todas as campanhas'}
                    </p>
                  )}
                </div>
                <div className="p-2 bg-muted/50 rounded-lg">
                  <DollarSign className="w-5 h-5 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Aprovadas</p>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {columns.find(col => col.id === 'aprovado')?.campaigns.length || 0}
                  </p>
                  {selectedBrand && selectedBrand !== 'all-brands' && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {((columns.find(col => col.id === 'aprovado')?.campaigns.length || 0) / getTotalCampaigns() * 100).toFixed(0)}% do total
                    </p>
                  )}
                </div>
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <User className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Em analise</p>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {columns.find(col => col.id === 'em-analise')?.campaigns.length || 0}
                  </p>
                  {selectedBrand && selectedBrand !== 'all-brands' && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {((columns.find(col => col.id === 'em-analise')?.campaigns.length || 0) / getTotalCampaigns() * 100).toFixed(0)}% do total
                    </p>
                  )}
                </div>
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


      </div>

      <div className="flex gap-6">
        {/* Kanban Board */}
        <div className="flex-1">
      {/* Linha de Progresso dos Status */}
          {/* <div className="mb-6">
            <div className="relative">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 relative">
                {filteredColumns.map((column, index) => {
                  const totalValue = column.campaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
                  const campaignCount = column.campaigns.length;
                  
                  return (
                    <div key={column.id} className="flex flex-col items-center relative">
                      <div className="text-center mb-4">
                        <div className="text-sm font-medium text-foreground mb-1">{column.title.toUpperCase()}</div>
              <div className="text-lg font-bold text-foreground">${totalValue.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">{campaignCount} leads</div>
                      </div>
                      
                      {index < filteredColumns.length - 1 && (
                        <div className="absolute top-[calc(100%-8px)] left-1/2 w-full h-1 bg-gradient-to-r from-[#9810fa] via-[#ff0074] to-[#ff0074] z-0" />
                      )}
                      
                      <div className="relative z-10">
                        <div className="w-4 h-4 bg-[#ff0074] rounded-full border-2 border-white shadow-md" />
                      </div>
                    </div>
                  );
                })}
              </div>
              
              <div className="absolute bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-[#9810fa] via-[#ff0074] to-white z-0"></div>
            </div>
          </div> */}

          {/* Timeline de Datas - DESATIVADA */}
          {/* <div className=" rounded-lg mb-6 p-6">
            <div className="flex items-center justify-center mb-4">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateWeek('prev')}
                  className="h-8 w-8 p-0 bg-white"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateWeek('next')}
                  className="h-8 w-8 p-0 bg-white"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                {selectedDate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedDate(null)}
                    className="text-xs bg-white"
                  >
                    Limpar Filtro
                  </Button>
                )}
              </div>
            </div>
            
            <div className="flex justify-center">
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {timelineDates.map((date, index) => {
                  const isToday = date.toDateString() === new Date().toDateString();
                  const isSelected = selectedDate === date.toISOString().split('T')[0];
                  const campaignCount = getCampaignCountForDate(date);
                  const isWeekend = date.getDay() === 0 || date.getDay() === 6;
                  
                  return (
                    <button
                      key={index}
                      onClick={() => {
                        const dateString = date.toISOString().split('T')[0];
                        setSelectedDate(isSelected ? null : dateString);
                      }}
                      className={`
                        flex-shrink-0 p-3 rounded-lg transition-all duration-200 min-w-[70px]
                        ${
                          isSelected
                            ? 'bg-foreground text-background shadow-lg'
                : isToday
                ? 'bg-muted text-foreground'
                : 'bg-background text-muted-foreground hover:bg-muted/50'
                        }
                        ${
                          isWeekend && !isSelected && !isToday
                            ? 'opacity-60'
                            : ''
                        }
                      `}
                    >
                      <div className="text-center">
                        <div className={`text-xs font-medium mb-1 ${
                          isSelected ? 'text-muted-foreground' : 'text-muted-foreground/60'
                        }`}>
                          {date.toLocaleDateString('pt-BR', { weekday: 'short' }).toUpperCase()}
                        </div>
                        <div className={`text-lg font-bold ${
                          isSelected ? 'text-background' : isToday ? 'text-foreground' : 'text-muted-foreground'
                        }`}>
                          {date.getDate()}
                        </div>
                        {campaignCount > 0 && (
                          <div className={`mt-1 w-5 h-5 rounded-full text-xs flex items-center justify-center font-medium mx-auto ${
                            isSelected
                              ? 'bg-background text-foreground'
                : 'bg-foreground text-background'
                          }`}>
                            {campaignCount}
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
            
            {selectedDate && (
              <div className="mt-4 p-3 bg-white rounded-lg text-center">
                <p className="text-sm text-muted-foreground">
                  Mostrando campanhas para <span className="font-medium">
                    {new Date(selectedDate).toLocaleDateString('pt-BR', { 
                      weekday: 'long', 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </span>
                </p>
              </div>
            )}
          </div> */}
          
          <DragDropContext onDragStart={onDragStart} onDragEnd={onDragEnd}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {filteredColumns.map((column) => {
            const totalValue = column.campaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
            const campaignCount = column.campaigns.length;
            
            return (
            <div key={column.id} className="bg-[#fcfcfc]  rounded-xl  dark:bg-[#fcfcfc0f]">
              <div className="p-4 ">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-muted-foreground"></div>
          <h3 className="font-medium text-muted-foreground text-sm">{column.title.toUpperCase()}</h3>
                    <span className="text-xs text-muted-foreground">({column.campaigns.length})</span>
                    {((selectedBrand && selectedBrand !== 'all-brands') || (selectedCampaignFilter && selectedCampaignFilter !== 'all-campaigns')) && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" title="Filtros ativos"></div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <Plus className="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Editar coluna</DropdownMenuItem>
                        <DropdownMenuItem>Adicionar card</DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          Excluir coluna
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                
                {/* Valor total e quantidade */}
                <div className="px-4 pb-2">
                  <div className="flex items-center gap-3">
                    <div className="text-xl font-semibold text-foreground">R$ {totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
                    <div className="text-xs text-muted-foreground">{campaignCount} campanhas</div>
                  </div>
                </div>
              </div>
              
              <Droppable droppableId={column.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`p-4 min-h-[600px] space-y-3 ${
                      snapshot.isDraggingOver ? 'bg-gray-50' : ''
                    }`}
                  >
                    {column.campaigns.map((campaign, index) => (
                      <Draggable
                        key={campaign.id}
                        draggableId={campaign.id}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            onClick={() => setSelectedCampaign(campaign)}
                            className={`rounded-2xl  p-4 cursor-pointer hover:shadow-md transition-all duration-200 ${
                              getCardBackgroundColor(campaign.status)
                            } ${
                              snapshot.isDragging ? 'shadow-lg rotate-1 scale-105' : ''
                            } ${
                              selectedCampaign?.id === campaign.id ? 'ring-2 ring-purple-500 border-purple-300' : ''
                            }`}
                          >
                            {/* Header com Prioridade, Orçamento e Status */}
                            <div className="flex flex-wrap items-center justify-between gap-2 mb-3">
                              <div className="flex flex-wrap items-center gap-2">
                              
                                {isDeadlineUrgent(campaign.deadline) && (
                                  <Badge className="text-xs px-2 py-1 rounded-full font-medium bg-red-500 text-white border-red-600 animate-pulse">
                                    URGENTE
                                  </Badge>
                                )}
                                {/* Orçamento integrado com os labels */}
                                <div className="flex flex-wrap items-center gap-1">
                                  <div className={`w-3 h-3 rounded-full ${getBudgetIndicatorColor(campaign.budget)}`} title={`Orçamento: ${formatCurrency(campaign.budget)}`}></div>
                                  <span className="text-xs font-medium text-muted-foreground break-words">{formatCurrency(campaign.budget)}</span>
                                </div>
                              </div>
                              {/* Indicadores de salvamento */}
                              {savingStatus[campaign.id] && (
                                <div className="flex items-center gap-1">
                                  {savingStatus[campaign.id] === 'saving' && (
                                    <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" title="Salvando..."></div>
                                  )}
                                  {savingStatus[campaign.id] === 'saved' && (
                                    <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center" title="Salvo com sucesso">
                                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                  )}
                                  {savingStatus[campaign.id] === 'error' && (
                                    <div className="w-3 h-3 bg-red-500 rounded-full flex items-center justify-center" title="Erro ao salvar">
                                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>

                            {/* Tags */}
                            <div className="flex flex-wrap gap-1 mb-3">
                              {campaign.tags.map((tag, tagIndex) => (
                                <Badge 
                                  key={tagIndex} 
                                  variant="secondary" 
                                  className="text-xs px-2 py-0.5 bg-white/80 text-gray-600 border-0 rounded-full"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>

                            {/* Title */}
                            <h4 className={`font-medium text-muted-foreground dark:text-white text-sm leading-tight mb-3  ${
                              isDeadlineUrgent(campaign.deadline) ? 'text-red-700' : ''
                            }`}>
                              {campaign.title}
                            </h4>

                            {/* Deadline Info */}
                            <div className={`flex items-center gap-1 mb-3 text-xs ${
                              isDeadlineUrgent(campaign.deadline) 
                                ? 'text-red-600 font-medium' 
                                : 'text-gray-500'
                            } ${getUrgentDeadlineClass(campaign.deadline)}`}>
                              <Calendar className="w-3 h-3" />
                              <span>Prazo: {formatDate(campaign.deadline)}</span>
                              {isDeadlineUrgent(campaign.deadline) && (
                                <AlertTriangle className="ml-1 w-3 h-3 text-black dark:text-white" />
                              )}
                            </div>

                            {/* Note */}
                            {campaign.note && (
                              <p className="text-xs text-gray-600 mb-3">
                                {campaign.note}
                              </p>
                            )}

                            {/* Progress */}
                            {campaign.progress > 0 && (
                              <div className="mb-3">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-xs text-gray-600 font-medium">Progresso</span>
                                  <span className={`text-xs font-medium ${
                                    campaign.progress >= 80 ? 'text-green-600' :
                                    campaign.progress >= 50 ? 'text-blue-600' :
                                    campaign.progress >= 25 ? 'text-yellow-600' : 'text-red-600'
                                  }`}>{campaign.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                                  <div 
                                    className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(campaign.progress)}`}
                                    style={{ width: `${campaign.progress}%` }}
                                  ></div>
                                </div>
                                <div className="flex gap-1">
                                  {Array.from({ length: 10 }, (_, i) => (
                                    <div
                                      key={i}
                                      className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                        i < (campaign.progress / 100) * 10
                                          ? getProgressColor(campaign.progress)
                                          : 'bg-gray-300'
                                      }`}
                                    />
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Bottom section */}
                            <div className="space-y-3 pt-2">
                              {/* Avatars */}
                              <div className="flex items-center justify-between">
                                <div className="flex -space-x-2">
                                  {campaign.influencerId ? (
                                    <InfluencerAvatar
                                      influencerId={campaign.influencerId}
                                      influencerName={campaign.influencer}
                                      avatarPath={campaign.avatars[0]}
                                      avatarUrl={campaign.avatars[0]}
                                      className="w-6 h-6 border-2 border-white"
                                    />
                                  ) : (
                                    <Avatar className="w-6 h-6 border-2 border-white">
                                      <AvatarImage src={campaign.avatars[0]} />
                                      <AvatarFallback className="text-xs">
                                        {campaign.influencer.charAt(0)}
                                      </AvatarFallback>
                                    </Avatar>
                                  )}
                                </div>

                                {/* Stats */}
                                <div className="flex items-center gap-3 text-xs text-gray-600">
                                  {campaign.comments > 0 && (
                                    <div className="flex items-center gap-1">
                                      <MessageSquare className="w-3 h-3" />
                                      <span>{campaign.comments}</span>
                                    </div>
                                  )}
                                  {campaign.attachments > 0 && (
                                    <div className="flex items-center gap-1">
                                      <Paperclip className="w-3 h-3" />
                                      <span>{campaign.attachments}</span>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Action Icons Footer */}
                              <div className="flex items-center gap-4">
                                {[
                                  { icon: Calendar, title: "Schedule event" },
                                  { icon: FileText, title: "Task list" },
                                  { icon: Paperclip, title: "Documents" },
                                  { 
                                    icon: () => (
                                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                    ),
                                    title: "Image gallery"
                                  }
                                ].map((action, index) => (
                                  <button 
                                    key={index}
                                    className="flex items-center justify-center w-8 h-8 rounded-lg   transition-colors duration-200 group"
                                    title={action.title}
                                  >
                                    {typeof action.icon === 'function' ? (
                                      <action.icon />
                                    ) : (
                                      <action.icon className="w-4 h-4 text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200" />
                                    )}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          );
          })}
            </div>
          </DragDropContext>
        </div>

        {/* Painel Lateral Direito */}
        <div className="w-[34rem] bg-white rounded-xl border  p-6 dark:bg-black">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-muted-foreground mb-2">Painel de Informações</h3>
            <p className="text-sm text-gray-600 dark:text-muted-foreground">Informações adicionais e ferramentas</p>
          </div>
          
          {/* Conteúdo do painel - pode ser expandido conforme necessário */}
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-xl dark:bg-black">
              <h4 className="text-sm font-medium text-muted-foreground dark:text-muted-foreground mb-2">Resumo Rápido</h4>
              <div className="space-y-2 text-sm text-muted-foreground dark:text-muted-foreground">
                <div className="flex justify-between">
                  <span>Total de Campanhas:</span>
                  <span className="font-medium">{getTotalCampaigns()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Orçamento Total:</span>
                  <span className="font-medium">{formatCurrency(getTotalBudget())}</span>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-xl dark:bg-black">
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Ações Rápidas</h4>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Plus className="w-4 h-4 mr-2" />
                  Nova Campanha
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Download className="w-4 h-4 mr-2" />
                  Exportar Dados
                </Button>
              </div>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-xl dark:bg-black">
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Status das Colunas</h4>
              <div className="space-y-2">
                {columns.map((column) => (
                  <div key={column.id} className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">{column.title.toUpperCase()}:</span>
                    <Badge variant="secondary" className="text-xs">
                      {column.campaigns.length}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Detalhes da Campanha */}
      <Dialog open={!!selectedCampaign} onOpenChange={(open) => !open && setSelectedCampaign(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-black border border-gray-300 dark:border-gray-700">
          {selectedCampaign && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl font-bold text-black dark:text-white flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-gray-400 dark:bg-gray-600"></div>
                  {selectedCampaign.title}
                  <Badge className="ml-2 bg-gray-800 dark:bg-background text-white dark:text-white border ">
                    {selectedCampaign.priority.toUpperCase()}
                  </Badge>
                </DialogTitle>
              </DialogHeader>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                {/* Coluna Esquerda - Informações Principais */}
                <div className="space-y-6">
                  {/* Informações Básicas */}
                  <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-4 flex items-center gap-2">
                      <FileText className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      Informações Básicas
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Marca:</span>
                        <span className="text-sm text-black dark:text-white font-medium">{selectedCampaign.brand}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Influenciador:</span>
                        <div className="flex items-center gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarImage src={selectedCampaign.avatars[0]} />
                            <AvatarFallback className="text-xs">
                              {selectedCampaign.influencer.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-black dark:text-white font-medium">{selectedCampaign.influencer}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
                        <Badge className="bg-gray-700 dark:bg-gray-300 text-white dark:text-white border-gray-500 dark:border-gray-500">
                          {getStatusLabel(selectedCampaign.status)}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Criado em:</span>
                        <span className="text-sm text-black dark:text-white">{formatDate(selectedCampaign.createdAt)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Orçamento e Prazo */}
                  <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-4 flex items-center gap-2">
                      <DollarSign className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      Orçamento e Prazo
                    </h3>
                    <div className="space-y-3">
                      <div className="flex flex-wrap justify-between items-center gap-2">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Orçamento:</span>
                        <span className="text-lg font-bold text-black dark:text-white break-words">{formatCurrency(selectedCampaign.budget)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Prazo:</span>
                        <span className={`text-sm font-medium ${
                          isDeadlineUrgent(selectedCampaign.deadline) ? 'text-gray-800 dark:text-gray-200' : 'text-black dark:text-white'
                        }`}>
                          {formatDate(selectedCampaign.deadline)}
                          {isDeadlineUrgent(selectedCampaign.deadline) && (
                            <span className="ml-2 text-black dark:text-white flex items-center gap-1">
                              <AlertTriangle className="w-3 h-3" />
                              URGENTE
                            </span>
                          )}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Progresso */}
                  {selectedCampaign.progress > 0 && (
                    <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-4 flex items-center gap-2">
                        <Clock className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        Progresso da Campanha
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Progresso:</span>
                          <span className="text-lg font-bold text-black dark:text-white">{selectedCampaign.progress}%</span>
                        </div>
                        <Progress value={selectedCampaign.progress} className="h-3 bg-gray-200 dark:bg-gray-800" />
                        <div className="flex gap-1">
                          {Array.from({ length: 10 }, (_, i) => (
                            <div
                              key={i}
                              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                                i < (selectedCampaign.progress / 100) * 10
                                  ? 'bg-gray-700 dark:bg-gray-300'
                                  : 'bg-gray-300 dark:bg-gray-700'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Coluna Direita - Detalhes Adicionais */}
                <div className="space-y-6">
                  {/* Descrição */}
                  <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-4">Descrição</h3>
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {selectedCampaign.description || 'Nenhuma descrição disponível.'}
                    </p>
                  </div>

                  {/* Nota */}
                  {selectedCampaign.note && (
                    <div className="bg-gray-200 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-xl p-4">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-2">Observações</h3>
                      <p className="text-sm text-gray-700 dark:text-gray-300">{selectedCampaign.note}</p>
                    </div>
                  )}

                  {/* Tags */}
                  {selectedCampaign.tags.length > 0 && (
                    <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-4 flex items-center gap-2">
                        <Tag className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        Tags
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedCampaign.tags.map((tag, index) => (
                          <Badge key={index} className="bg-gray-700 dark:bg-gray-300 text-white dark:text-white border border-gray-600 dark:border-gray-400">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Estatísticas */}
                  <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-4">Estatísticas</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-white dark:bg-black rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <MessageSquare className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Comentários</span>
                        </div>
                        <span className="text-2xl font-bold text-black dark:text-white">{selectedCampaign.comments}</span>
                      </div>
                      <div className="text-center p-3 bg-white dark:bg-black rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <Paperclip className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Anexos</span>
                        </div>
                        <span className="text-2xl font-bold text-black dark:text-white">{selectedCampaign.attachments}</span>
                      </div>
                    </div>
                  </div>

                  {/* Equipe */}
                  <div className="bg-gray-100 dark:bg-[#080210] rounded-xl p-4 border border-gray-200 dark:border-gray-800">
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-4 flex items-center gap-2">
                      <Users className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      Equipe
                    </h3>
                    <div className="flex items-center gap-3">
                      {selectedCampaign.avatars.map((avatar, index) => (
                        <Avatar key={index} className="w-10 h-10 border-2 border-gray-300 dark:border-gray-600 shadow-md">
                          <AvatarImage src={avatar} />
                          <AvatarFallback className="text-sm font-medium bg-gray-200 dark:bg-gray-700 text-black dark:text-white">
                            {selectedCampaign.influencer.charAt(index)}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                      <div className="ml-2">
                        <p className="text-sm font-medium text-black dark:text-white">{selectedCampaign.influencer}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Influenciador Principal</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Ações do Modal */}
              <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-300 dark:border-gray-700">
                <Button variant="outline" onClick={() => setSelectedCampaign(null)} className="border-gray-400 dark:border-gray-600 text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800">
                  Fechar
                </Button>
                <Button className="bg-black dark:bg-white text-white dark:text-white hover:bg-gray-800 dark:hover:bg-gray-200">
                  Editar Campanha
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}



