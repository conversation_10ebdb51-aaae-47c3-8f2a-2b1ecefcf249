// Interface base para todos os documentos com isolamento multi-tenancy
export interface BaseDocument {
  id: string;
  userId: string;          // 🆕 Campo obrigatório para isolamento
  createdAt: Date;
  updatedAt: Date;
}

// Interface para documentos que precisam rastrear quem criou/atualizou
export interface UserOwnedDocument extends BaseDocument {
  createdBy: string;       // ID do usuário que criou
  updatedBy: string;       // ID do usuário que atualizou
}

// Interface para documentos que podem ser compartilhados (futuro)
export interface ShareableDocument extends BaseDocument {
  isShared: boolean;
  sharedWith?: string[];   // IDs de usuários com acesso
  sharePermissions?: 'read' | 'write' | 'admin';
}

// Tipo utilitário para criar dados sem campos de sistema
export type CreateData<T extends BaseDocument> = Omit<T, 'id' | 'userId' | 'createdAt' | 'updatedAt'>;

// Tipo utilitário para atualizar dados
export type UpdateData<T extends BaseDocument> = Partial<Omit<T, 'id' | 'userId' | 'createdAt'>> & {
  updatedAt?: Date;
};

// Enum para status comum de documentos
export enum DocumentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived'
}

// Interface para metadados comuns
export interface DocumentMetadata {
  version: number;
  tags?: string[];
  notes?: string;
  lastModifiedBy?: string;
  isArchived?: boolean;
} 

