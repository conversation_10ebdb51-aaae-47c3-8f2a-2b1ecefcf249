
import { useState, useEffect, useMemo, useRef } from "react"
import { useStatsGraphQL } from "@/hooks/use-graphql-influencers"
import { useTranslations } from "@/hooks/use-translations"
import { FilterBar } from "./filter-bar"
import { Toolbar } from "./toolbar"
import { UnifiedFilterToolbar } from "./unified-filter-toolbar"

interface AnimatedStatsProps {
  influencersCount: number;
  userId?: string;
  // Props para FilterBar
  searchTerm?: string;
  selectedCategory?: string;
  onSearchChange?: (value: string) => void;
  onCategoryChange?: (category: string) => void;
  // Props para Toolbar
  viewMode?: "grid" | "list";
  selectionMode?: boolean;
  selectedCount?: number;
  onViewModeChange?: (mode: "grid" | "list") => void;
  onAddInfluencer?: () => void;
  onToggleSelectionMode?: () => void;
  onDeleteSelected?: () => void;
  onDuplicateSelected?: () => void;
  onBrandFilterChange?: (selectedBrands: string[]) => void;
  verifiedOnly?: boolean;
  onVerifiedOnlyChange?: (value: boolean) => void;
  onSendToBrand?: (brandId: string, brandName: string) => void;
}

export function AnimatedStats({ 
  influencersCount, 
  userId,
  searchTerm,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  // Props da Toolbar
  viewMode,
  selectionMode,
  selectedCount,
  onViewModeChange,
  onAddInfluencer,
  onToggleSelectionMode,
  onDeleteSelected,
  onDuplicateSelected,
  onBrandFilterChange,
  verifiedOnly,
  onVerifiedOnlyChange,
  onSendToBrand
}: AnimatedStatsProps) {
  const { t } = useTranslations();
  const effectiveUserId = userId || 'system';
  const { stats, loading: isLoading, error, refetch } = useStatsGraphQL(effectiveUserId);
  const hasRefetched = useRef(false);

  // 🔥 CORREÇÃO: Detectar filtros ativos de forma mais robusta
  const hasActiveFilters = useMemo(() => {
    const hasSearch = searchTerm && searchTerm.trim().length > 0;
    const hasCategory = selectedCategory && selectedCategory !== 'all' && selectedCategory !== '';
    const hasVerified = verifiedOnly === true;
    
    return hasSearch || hasCategory || hasVerified;
  }, [searchTerm, selectedCategory, verifiedOnly]);

  // 🔥 CORREÇÃO: Lógica clara para escolher qual estatística mostrar
  const displayStats = useMemo(() => {
    // Se há filtros ativos E temos uma contagem válida do grid filtrado, mostrar isso
    if (hasActiveFilters && typeof influencersCount === 'number' && influencersCount >= 0) {
      return {
        totalInfluencers: influencersCount,
        totalViews: stats?.totalViews || 0, // Mantém views/followers totais do sistema
        totalFollowers: stats?.totalFollowers || 0,
        isFiltered: true,
        sourceType: 'filtered'
      };
    }
    
    // Senão, mostrar dados totais do GraphQL
    return {
      totalInfluencers: stats?.totalInfluencers || 0,
      totalViews: stats?.totalViews || 0,
      totalFollowers: stats?.totalFollowers || 0,
      isFiltered: false,
      sourceType: 'total'
    };
  }, [hasActiveFilters, influencersCount, stats]);

  // 🔥 CORREÇÃO: Remover useEffect de debug excessivo e otimizar refetch
  useEffect(() => {
    if (!isLoading && !hasActiveFilters && !hasRefetched.current && (!stats || stats.totalInfluencers === 0)) {
      console.log('🔄 [AnimatedStats] Carregando estatísticas iniciais...');
      refetch?.();
      hasRefetched.current = true;
    }
  }, [isLoading, hasActiveFilters, stats, refetch]);

  // Formatador para exibir números com separação de milhares
  const formatNumber = (num: number): string => {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0';
    }
    return num.toLocaleString('pt-BR');
  };

  return (
    <div className="sticky top-0 z-5 w-full bg-background backdrop-blur-sm border-b border-border/40 ">
      <div className="px-2 py-4 space-y-4">
        
        {/* Container para os insights estáticos */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="hidden md:flex items-center gap-6 text-foreground dark:text-white mr-auto">
            
            {/* Estatística de Influencers */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-influencers)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-influencers" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8zm0-2a2 2 0 1 1 0-4 2 2 0 0 1 0 4z" />
                <path d="M12 12c-5.5 0-10 3.5-10 8v1h20v-1c0-4.5-4.5-8-10-8zm0 2c4.5 0 8 2.5 8 6H4c0-3.5 3.5-6 8-6z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalInfluencers)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">
                {displayStats.isFiltered ? t('common.found').toUpperCase() : t('navigation.influencers').toUpperCase()}
              </span>
              {displayStats.isFiltered && (
                <span className="text-xs text-muted-foreground ml-1">(filtrados)</span>
              )}
            </div>
            
            <div className="text-muted-foreground dark:text-white">|</div>
            
            {/* Estatística de Views */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-views)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-views" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalViews)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">{t('panels.contact.total_views').toUpperCase()}</span>
            </div>
            
            <div className="text-muted-foreground dark:text-white">|</div>
            
            {/* Estatística de Seguidores */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-followers)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-followers" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalFollowers)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">{t('panels.contact.total_followers').toUpperCase()}</span>
            </div>

            {/* Indicadores de estado melhorados */}
            {isLoading && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-4 h-4 border-2 border-muted-foreground/30 border-t-muted-foreground rounded-full animate-spin"></div>
                <span className="text-xs">Carregando...</span>
              </div>
            )}

            {error && (
              <div className="flex items-center gap-2 text-destructive">
                <span className="text-xs">Erro ao carregar dados</span>
                <button 
                  onClick={() => refetch?.()} 
                  className="text-xs underline hover:no-underline"
                >
                  Tentar novamente
                </button>
              </div>
            )}

            
            
          </div>
        </div>
        
        {/* Filtros integrados */}
        {/* Barra unificada de filtros e toolbar */}
        {(onSearchChange && onCategoryChange) && (onViewModeChange && onAddInfluencer) && (
          <UnifiedFilterToolbar
            // FilterBar props
            searchTerm={searchTerm || ''}
            selectedCategory={selectedCategory || 'all'}
            onSearchChange={onSearchChange}
            onCategoryChange={onCategoryChange}
            onBrandFilterChange={onBrandFilterChange}
            // Toolbar props
            viewMode={viewMode || "grid"}
            selectionMode={selectionMode || false}
            selectedCount={selectedCount || 0}
            onViewModeChange={onViewModeChange}
            onAddInfluencer={onAddInfluencer}
            onToggleSelectionMode={onToggleSelectionMode || (() => {})}
            onDeleteSelected={onDeleteSelected || (() => {})}
            onDuplicateSelected={onDuplicateSelected || (() => {})}
            verifiedOnly={verifiedOnly || false}
            onVerifiedOnlyChange={onVerifiedOnlyChange || (() => {})}
            onSendToBrand={onSendToBrand}
          />
        )}
        
      </div>
    </div>
  );
}


