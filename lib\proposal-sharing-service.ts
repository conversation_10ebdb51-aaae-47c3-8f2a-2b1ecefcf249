import { db } from './firebase';
import { 
  ProposalCollaborator, 
  ProposalInvite, 
  ShareProposalRequest,
  PermissionLevel,
  UserRole 
} from '@/types/proposal-sharing';

export class ProposalSharingService {
  
  // 📨 Criar convite para proposta
  static async createInvite(request: ShareProposalRequest, inviterData: {
    name: string;
    email: string;
  }): Promise<{ inviteId: string; token: string }> {
    try {
      // Gerar token único para o convite
      const token = this.generateInviteToken();
      
      // Data de expiração (7 dias)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);
      
      const invite: Omit<ProposalInvite, 'id'> = {
        proposalId: request.proposalId,
        proposalName: `Proposta ${request.proposalId.substring(0, 8)}`,
        inviterName: inviterData.name,
        inviterEmail: inviterData.email,
        inviteeEmail: request.inviteeEmail,
        inviteeName: request.inviteeName,
        userRole: request.userRole,
        permissionLevel: request.permissionLevel,
        brandId: request.brandId,
        brandName: request.brandName,
        token,
        expiresAt,
        createdAt: new Date(),
        status: 'pending',
        message: request.message
      };
      
      // Filtrar valores undefined antes de enviar ao Firestore
      const cleanInvite = Object.fromEntries(
        Object.entries(invite).filter(([_, value]) => value !== undefined)
      );
      
      const docRef = await db.collection('proposal_invites').add(cleanInvite);
      
      console.log('✅ Convite criado:', docRef.id);
      
      // Enviar email de convite apenas se não for apenas para gerar link
      if (!(request as any).onlyGenerateLink) {
        await this.sendInviteEmail(invite, docRef.id);
      } else {
        console.log('🔗 Service: Link gerado sem envio de email');
      }
      
      return { inviteId: docRef.id, token };
    } catch (error) {
      console.error('❌ Erro ao criar convite:', error);
      throw error;
    }
  }
  
  // 📧 Enviar email de convite (placeholder)
  private static async sendInviteEmail(invite: Omit<ProposalInvite, 'id'>, inviteId: string): Promise<void> {
    try {
      console.log('📧 Email de convite enviado para:', invite.inviteeEmail);
      console.log('🔗 Link do convite:', `/convites/aceitar?token=${invite.token}`);
    } catch (error) {
      console.error('❌ Erro ao enviar email:', error);
    }
  }
  
  // 🎫 Gerar token único para convite
  private static generateInviteToken(): string {
    return btoa(Date.now().toString() + Math.random().toString(36)).replace(/[^a-zA-Z0-9]/g, '');
  }
  
  // ✅ Aceitar convite
  static async acceptInvite(token: string, userData: {
    userId: string;
    userName: string;
    userEmail: string;
    avatar?: string;
  }): Promise<string> {
    try {
      // Buscar convite pelo token
      const inviteSnapshot = await db.collection('proposal_invites')
        .where('token', '==', token)
        .where('status', '==', 'pending')
        .get();
      
      if (inviteSnapshot.empty) {
        throw new Error('Convite não encontrado ou já utilizado');
      }
      
      const inviteDoc = inviteSnapshot.docs[0];
      const invite = { id: inviteDoc.id, ...inviteDoc.data() } as ProposalInvite;
      
      // Verificar se convite não expirou
      const now = new Date();
      const expiresAt = invite.expiresAt instanceof Date ? 
        invite.expiresAt : 
        new Date(invite.expiresAt);
      
      if (now > expiresAt) {
        throw new Error('Convite expirado');
      }
      
      // Criar colaborador
      const collaborator: Omit<ProposalCollaborator, 'id'> = {
        userId: userData.userId,
        userEmail: userData.userEmail,
        userName: userData.userName,
        userRole: invite.userRole,
        permissionLevel: invite.permissionLevel,
        invitedBy: invite.inviterEmail,
        invitedAt: invite.createdAt instanceof Date ? 
          invite.createdAt : 
          new Date(invite.createdAt),
        joinedAt: new Date(),
        status: 'accepted',
        brandId: invite.brandId,
        brandName: invite.brandName,
        avatar: userData.avatar
      };
      
      // Filtrar valores undefined antes de enviar ao Firestore
      const cleanCollaborator = Object.fromEntries(
        Object.entries(collaborator).filter(([_, value]) => value !== undefined)
      );
      
      // Adicionar colaborador à proposta
      const collaboratorRef = await db.collection('proposals')
        .doc(invite.proposalId)
        .collection('collaborators')
        .add(cleanCollaborator);
      
      // Marcar convite como aceito
      await db.collection('proposal_invites')
        .doc(inviteDoc.id)
        .update({ status: 'accepted' });
      
      console.log('✅ Convite aceito:', collaboratorRef.id);
      return invite.proposalId;
      
    } catch (error) {
      console.error('❌ Erro ao aceitar convite:', error);
      throw error;
    }
  }
  
  // 👥 Listar colaboradores de uma proposta
  static async getProposalCollaborators(proposalId: string): Promise<ProposalCollaborator[]> {
    try {
      console.log('🔍 Service: Buscando colaboradores para proposal:', proposalId);
      
      const snapshot = await db.collection('proposals')
        .doc(proposalId)
        .collection('collaborators')
        .where('status', '==', 'accepted')
        .orderBy('joinedAt', 'desc')
        .get();
      
      console.log('📊 Service: Documentos de colaboradores encontrados:', snapshot.docs.length);
      
      const collaborators = snapshot.docs.map(doc => {
        const data = doc.data();
        console.log('📄 Service: Dados do colaborador:', doc.id, data);
        
        return {
          id: doc.id,
          ...data,
          invitedAt: data.invitedAt instanceof Date ? data.invitedAt : new Date(data.invitedAt),
          joinedAt: data.joinedAt instanceof Date ? data.joinedAt : new Date(data.joinedAt)
        } as ProposalCollaborator;
      });
      
      console.log('✅ Service: Colaboradores processados:', collaborators.length);
      return collaborators;
    } catch (error) {
      console.error('❌ Service: Erro ao listar colaboradores:', error);
      return [];
    }
  }
  
  // 📨 Listar convites pendentes de uma proposta
  static async getPendingInvites(proposalId: string): Promise<ProposalInvite[]> {
    try {
      console.log('🔍 Service: Buscando convites pendentes para proposal:', proposalId);
      
      const snapshot = await db.collection('proposal_invites')
        .where('proposalId', '==', proposalId)
        .where('status', '==', 'pending')
        .orderBy('createdAt', 'desc')
        .get();
      
      console.log('📨 Service: Documentos de convites encontrados:', snapshot.docs.length);
      
      const invites = snapshot.docs.map(doc => {
        const data = doc.data();
        console.log('📄 Service: Dados do convite:', doc.id, data);
        
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt instanceof Date ? data.createdAt : new Date(data.createdAt),
          expiresAt: data.expiresAt instanceof Date ? data.expiresAt : new Date(data.expiresAt)
        } as ProposalInvite;
      });
      
      console.log('✅ Service: Convites processados:', invites.length);
      return invites;
    } catch (error) {
      console.error('❌ Service: Erro ao listar convites:', error);
      return [];
    }
  }
  
  // 🔄 Atualizar permissões de colaborador
  static async updateCollaboratorPermissions(
    proposalId: string, 
    collaboratorId: string, 
    newPermissionLevel: PermissionLevel
  ): Promise<void> {
    try {
      await db.collection('proposals')
        .doc(proposalId)
        .collection('collaborators')
        .doc(collaboratorId)
        .update({ permissionLevel: newPermissionLevel });
      
      console.log('✅ Permissões atualizadas para colaborador:', collaboratorId);
    } catch (error) {
      console.error('❌ Erro ao atualizar permissões:', error);
      throw error;
    }
  }
  
  // 🗑️ Remover colaborador
  static async removeCollaborator(proposalId: string, collaboratorId: string): Promise<void> {
    try {
      await db.collection('proposals')
        .doc(proposalId)
        .collection('collaborators')
        .doc(collaboratorId)
        .update({ status: 'revoked' });
      
      console.log('✅ Colaborador removido:', collaboratorId);
    } catch (error) {
      console.error('❌ Erro ao remover colaborador:', error);
      throw error;
    }
  }
  
  // ❌ Cancelar convite
  static async cancelInvite(inviteId: string): Promise<void> {
    try {
      await db.collection('proposal_invites')
        .doc(inviteId)
        .update({ status: 'revoked' });
      
      console.log('✅ Convite cancelado:', inviteId);
    } catch (error) {
      console.error('❌ Erro ao cancelar convite:', error);
      throw error;
    }
  }
  
  // 🔍 Verificar permissões de usuário em proposta
  static async getUserPermissions(proposalId: string, userId: string): Promise<{
    permissionLevel: PermissionLevel | null;
    collaborator: ProposalCollaborator | null;
  }> {
    try {
      const snapshot = await db.collection('proposals')
        .doc(proposalId)
        .collection('collaborators')
        .where('userId', '==', userId)
        .where('status', '==', 'accepted')
        .get();
      
      if (snapshot.empty) {
        return { permissionLevel: null, collaborator: null };
      }
      
      const collaboratorDoc = snapshot.docs[0];
      const collaborator = {
        id: collaboratorDoc.id,
        ...collaboratorDoc.data()
      } as ProposalCollaborator;
      
      return { 
        permissionLevel: collaborator.permissionLevel, 
        collaborator 
      };
    } catch (error) {
      console.error('❌ Erro ao verificar permissões:', error);
      return { permissionLevel: null, collaborator: null };
    }
  }
  
  // Chat removido do projeto
} 

