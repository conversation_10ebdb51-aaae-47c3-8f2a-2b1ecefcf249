import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { uploadBrandLogo, compressAndOptimizeImage } from '@/lib/firebase-storage';

export const dynamic = 'force-dynamic';

/**
 * 📸 POST /api/uploads/brands/logos - Upload de logo de marca
 * Faz upload da imagem para Firebase Storage com otimização automática
 */
export const POST = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    // Verificar se é uma requisição multipart/form-data
    const contentType = req.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json({
        error: 'Conteúdo deve ser multipart/form-data'
      }, { status: 400 });
    }

    // Extrair dados do FormData
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string;

    // Validações básicas
    if (!file) {
      return NextResponse.json({
        error: 'Arquivo de imagem é obrigatório'
      }, { status: 400 });
    }

    if (type !== 'brand-logo') {
      return NextResponse.json({
        error: 'Tipo de upload inválido'
      }, { status: 400 });
    }

    // Validar arquivo
    const validationError = validateImageFile(file);
    if (validationError) {
      return NextResponse.json({
        error: validationError
      }, { status: 400 });
    }

    // Converter File para Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Otimizar e comprimir imagem
    const optimizedBuffer = await compressAndOptimizeImage(buffer, {
      maxWidth: 512,
      maxHeight: 512,
      quality: 0.9,
      format: 'webp' // Converter para WebP para melhor compressão
    });

    // Upload para Firebase Storage
    const logoUrl = await uploadBrandLogo(optimizedBuffer, userId, {
      originalName: file.name,
      mimeType: 'image/webp',
      size: optimizedBuffer.length
    });

    return NextResponse.json({
      success: true,
      url: logoUrl,
      message: 'Logo enviado com sucesso',
      metadata: {
        originalSize: file.size,
        optimizedSize: optimizedBuffer.length,
        compressionRatio: ((file.size - optimizedBuffer.length) / file.size * 100).toFixed(1) + '%'
      }
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error('[BRAND_LOGO_UPLOAD]', { userId, error: errorMessage });

    // Tratamento específico de erros
    if (errorMessage.includes('file too large')) {
      return NextResponse.json({
        error: 'Arquivo muito grande. Máximo 5MB'
      }, { status: 413 });
    }

    if (errorMessage.includes('invalid format')) {
      return NextResponse.json({
        error: 'Formato de imagem inválido. Use PNG, JPG ou WEBP'
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Erro interno no upload da imagem',
      details: errorMessage
    }, { status: 500 });
  }
});

/**
 * Validar arquivo de imagem
 */
function validateImageFile(file: File): string | null {
  // Verificar tamanho (máximo 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return 'Arquivo muito grande. Máximo 5MB';
  }

  // Verificar tipo de arquivo
  const allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    return 'Formato não suportado. Use PNG, JPG ou WEBP';
  }

  // Verificar extensão do arquivo para segurança extra
  const fileName = file.name.toLowerCase();
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
  
  if (!hasValidExtension) {
    return 'Extensão de arquivo inválida';
  }

  return null;
} 

