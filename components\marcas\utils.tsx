import { Badge } from "@/components/ui/badge";
import { Influencer, BrandInfluencer } from "@/types";

// Função para formatar números grandes
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// Função para formatar moeda
export const formatCurrency = (value: number): string => {
  if (value >= 1000000) {
    return `R$ ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `R$ ${(value / 1000).toFixed(1)}K`;
  }
  return `R$ ${value.toFixed(0)}`;
};

// Função para formatar moeda de proposta
export const formatProposalCurrency = (value: number, currency: string = 'BRL'): string => {
  const symbols: { [key: string]: string } = {
    'BRL': 'R$',
    'USD': '$',
    'EUR': '€'
  };
  
  const symbol = symbols[currency] || 'R$';
  
  if (value >= 1000000) {
    return `${symbol} ${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `${symbol} ${(value / 1000).toFixed(1)}K`;
  }
  return `${symbol} ${value.toFixed(0)}`;
};

// Função para renderizar badge de status
export const renderStatusBadge = (status: string): JSX.Element => {
  const statusConfig = {
    'sent': { label: 'Enviado', variant: 'secondary' as const },
    'viewed': { label: 'Visualizado', variant: 'outline' as const },
    'interested': { label: 'Interessado', variant: 'default' as const },
    'proposal_sent': { label: 'Proposta Enviada', variant: 'default' as const },
    'rejected': { label: 'Rejeitado', variant: 'destructive' as const },
  };
  
  const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'outline' as const };
  
  return (
    <Badge variant={config.variant} className="text-xs">
      {config.label}
    </Badge>
  );
};

// Função para obter serviços disponíveis
export const getAvailableServices = (influencer: Influencer) => {
  const services = [];
  
  // Instagram
  if (influencer.redesSociais?.instagram) {
    if (influencer.dadosFinanceiros?.precos?.instagramStory) {
      services.push({
        id: 'instagramStory',
        name: influencer.dadosFinanceiros.precos.instagramStory.name || 'Stories',
        platform: 'instagram',
        type: 'story',
        price: influencer.dadosFinanceiros.precos.instagramStory.price || 0
      });
    }
    if (influencer.dadosFinanceiros?.precos?.instagramReel) {
      services.push({
        id: 'instagramReel',
        name: influencer.dadosFinanceiros.precos.instagramReel.name || 'Reels',
        platform: 'instagram',
        type: 'reel',
        price: influencer.dadosFinanceiros.precos.instagramReel.price || 0
      });
    }
  }
  
  // YouTube
  if (influencer.redesSociais?.youtube) {
    if (influencer.dadosFinanceiros?.precos?.youtubeInsertion) {
      services.push({
        id: 'youtubeInsertion',
        name: influencer.dadosFinanceiros.precos.youtubeInsertion.name || 'Inserção',
        platform: 'youtube',
        type: 'insertion',
        price: influencer.dadosFinanceiros.precos.youtubeInsertion.price || 0
      });
    }
    if (influencer.dadosFinanceiros?.precos?.youtubeDedicated) {
      services.push({
        id: 'youtubeDedicated',
        name: influencer.dadosFinanceiros.precos.youtubeDedicated.name || 'Dedicado',
        platform: 'youtube',
        type: 'dedicated',
        price: influencer.dadosFinanceiros.precos.youtubeDedicated.price || 0
      });
    }
    if (influencer.dadosFinanceiros?.precos?.youtubeShorts) {
      services.push({
        id: 'youtubeShorts',
        name: influencer.dadosFinanceiros.precos.youtubeShorts.name || 'Shorts',
        platform: 'youtube',
        type: 'shorts',
        price: influencer.dadosFinanceiros.precos.youtubeShorts.price || 0
      });
    }
  }
  
  // TikTok
  if (influencer.redesSociais?.tiktok) {
    if (influencer.dadosFinanceiros?.precos?.tiktokVideo) {
      services.push({
        id: 'tiktokVideo',
        name: influencer.dadosFinanceiros.precos.tiktokVideo.name || 'Vídeo',
        platform: 'tiktok',
        type: 'video',
        price: influencer.dadosFinanceiros.precos.tiktokVideo.price || 0
      });
    }
  }
  
  return services;
};

// Função para obter ícone de serviço
export const getServiceIcon = (platform: string, type: string) => {
  if (platform === 'instagram') {
    if (type === 'story') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
        </svg>
      );
    }
  } else if (platform === 'youtube') {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
      </svg>
    );
  } else if (platform === 'tiktok') {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 448 512">
        <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
      </svg>
    );
  }
  
  return null;
};

