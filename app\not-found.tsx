'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Logo/Ícone */}
        <div className="flex justify-center">
          <div className="w-24 h-24 bg-gradient-to-r from-[#ff0074] to-[#9810fa] rounded-2xl flex items-center justify-center">
            <span className="text-white text-4xl font-bold">404</span>
          </div>
        </div>

        {/* Título e Descrição */}
        <div className="space-y-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            P<PERSON>gina não encontrada
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            Ops! A página que você está procurando não existe ou foi movida.
          </p>
        </div>

        {/* Botões de Ação */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            asChild
            className="bg-[#ff0074] hover:bg-[#d10037] text-white"
          >
            <Link href="/" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Ir para Início
            </Link>
          </Button>
          
          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        {/* Links Úteis */}
        <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Links úteis:
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              href="/login" 
              className="text-[#9810fa] hover:text-[#4a00b0] font-medium"
            >
              Login
            </Link>
            <Link 
              href="/dashboard" 
              className="text-[#9810fa] hover:text-[#4a00b0] font-medium"
            >
              Home
            </Link>
            <Link 
              href="/propostas" 
              className="text-[#9810fa] hover:text-[#4a00b0] font-medium"
            >
              Propostas
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 


