// Script para inicializar a estrutura do banco de dados no Firebase
const admin = require('firebase-admin');
const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicializar o Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

// Função principal para inicializar o banco de dados
async function initializeDatabase() {
  console.log('Iniciando configuração do banco de dados...');

  try {
    // Criar as coleções (mesmo vazias)
    const batch = db.batch();
    
    // Documento temporário para 'influencers'
    const influencersRef = db.collection('influencers').doc('_temp');
    batch.set(influencersRef, { _temp: true });
    
    // Documento temporário para 'influencer_financials'
    const financialsRef = db.collection('influencer_financials').doc('_temp');
    batch.set(financialsRef, { _temp: true });
    
    // Documento temporário para 'campaigns'
    const campaignsRef = db.collection('campaigns').doc('_temp');
    batch.set(campaignsRef, { _temp: true });
    
    // Documento temporário para 'categories'
    const categoriesRef = db.collection('categories').doc('_temp');
    batch.set(categoriesRef, { _temp: true });
    
    // Documento temporário para 'brands'
    const brandsRef = db.collection('brands').doc('_temp');
    batch.set(brandsRef, { _temp: true });
    
    // Executar o batch
    await batch.commit();
    console.log('Coleções criadas com sucesso!');
    
    // Remover os documentos temporários após a criação das coleções
    const deletePromises = [
      influencersRef.delete(),
      financialsRef.delete(),
      campaignsRef.delete(),
      categoriesRef.delete(),
      brandsRef.delete()
    ];
    
    await Promise.all(deletePromises);
    console.log('Documentos temporários removidos!');
    
    // Criar alguns dados de exemplo
    await createSampleData();

    console.log('Banco de dados inicializado com sucesso!');
  } catch (error) {
    console.error('Erro ao inicializar banco de dados:', error);
  }
}

// Função para criar alguns dados de exemplo
async function createSampleData() {
  try {
    console.log('Criando dados de exemplo...');
    
    // Configuração básica
    console.log('Configurando ambiente...');
    const now = new Date();
    
    // Categorias serão adicionadas manualmente
    console.log('Configuração do ambiente concluída.');
    const categoryRefs = [];
    console.log('Categorias serão adicionadas manualmente pelo usuário.');
    
    // Subcategorias serão adicionadas manualmente
    console.log('Subcategorias serão adicionadas manualmente pelo usuário.');
    
    // Criar marcas de exemplo
    console.log('Criando marcas de exemplo...');
    const brandsData = [
      {
        name: 'Samsung',
        industry: 'Tecnologia',
        logo: '/uploads/brands/samsung-logo.png',
        website: 'https://www.samsung.com',
        contactEmail: '<EMAIL>',
        budget: 500000
      },
      {
        name: 'Nike',
        industry: 'Moda',
        logo: '/uploads/brands/nike-logo.png',
        website: 'https://www.nike.com.br',
        contactEmail: '<EMAIL>',
        budget: 400000
      },
      {
        name: 'Coca-Cola',
        industry: 'Bebidas',
        logo: '/uploads/brands/cocacola-logo.png',
        website: 'https://www.cocacola.com.br',
        contactEmail: '<EMAIL>',
        budget: 600000
      },
      {
        name: 'iFood',
        industry: 'Alimentação',
        logo: '/uploads/brands/ifood-logo.png',
        website: 'https://www.ifood.com.br',
        contactEmail: '<EMAIL>',
        budget: 350000
      },
      {
        name: 'Netflix',
        industry: 'Entretenimento',
        logo: '/uploads/brands/netflix-logo.png',
        website: 'https://www.netflix.com',
        contactEmail: '<EMAIL>',
        budget: 700000
      },
      {
        name: 'Amazon',
        industry: 'E-commerce',
        logo: '/uploads/brands/amazon-logo.png',
        website: 'https://www.amazon.com.br',
        contactEmail: '<EMAIL>',
        budget: 500000
      }
    ];
    
    // Adicionar marcas ao banco
    const brandPromises = brandsData.map(brand => {
      return db.collection('brands').add({
        ...brand,
        createdAt: now,
        updatedAt: now
      });
    });
    
    const brandRefs = await Promise.all(brandPromises);
    console.log(`${brandRefs.length} marcas criadas com sucesso!`);
    
    
    // Exemplo de um influenciador
    const influencerRef = db.collection('influencers').doc();
    
    const influencerData = {
      name: "Rafael Chocolate",
      avatar: "/uploads/avatars/772d07ae-7410-4d7d-ae89-fecc7e13476d.png",
      backgroundImage: "/uploads/backgrounds/b0deab61-f97b-4868-956c-456908e2d8f.svg",
      country: "Brasil",
      state: "RJ",
      city: "Rio de Janeiro",
      age: 35,
      gender: "M",
      bio: "Comediante e criador de conteúdo de humor. Especialista em vídeos virais e esquetes cômicas que engajam milhões de pessoas.",
      category: "Humor",
      mainCategories: ["Humor", "Entretenimento", "Blog Pessoal"],
      totalFollowers: "6.857M",
      engagementRate: 4.2,
      audienceGender: {
        male: 45,
        female: 52,
        other: 3
      },
      socialNetworks: {
        instagram: {
          username: "rafalchocolate",
          followers: 902000,
          avgViews: 1200000,
          reelsViews: 1500000
        },
        tiktok: {
          username: "rafalchocolate",
          followers: 606000,
          avgViews: 756000
        },
        youtube: {
          username: "RafaelChocolateOficial",
          followers: 5350000,
          avgViews: 1600000
        }
      },
      createdAt: now,
      updatedAt: now
    };
    
    await influencerRef.set(influencerData);
    console.log(`Influenciador de exemplo criado com ID: ${influencerRef.id}`);
    
    // Exemplo de dados financeiros para o influenciador
    const financialRef = db.collection('influencer_financials').doc();
    
    const financialData = {
      influencerId: influencerRef.id,
      responsibleName: "Carlos Silva",
      agencyName: "Talentos Agency",
      email: "<EMAIL>",
      whatsapp: "+5521999998888",
      instagramStoriesViews: 900000,
      prices: {
        instagramStory: 5000,
        instagramReel: 12000,
        tiktokVideo: 8000,
        youtubeInsertion: 15000,
        youtubeDedicated: 25000,
        youtubeShorts: 10000
      },
      brandHistory: {
        instagram: ["Nike", "Coca-Cola", "Samsung"],
        tiktok: ["iFood", "Shopee", "Uber"],
        youtube: ["Amazon Prime", "Netflix", "Raid Shadow Legends"]
      },
      additionalData: {
        contentType: ["Comédia", "Vlogs", "Reações"],
        promotesTraders: false,
        responsibleRecruiter: "Maria Oliveira",
        socialMediaScreenshots: [
          "/uploads/screenshots/insta-rafael-2025.jpg",
          "/uploads/screenshots/tiktok-rafael-2025.jpg"
        ],
        notes: "Prefere projetos de longo prazo. Disponível para viagens internacionais."
      },
      createdAt: now,
      updatedAt: now
    };
    
    await financialRef.set(financialData);
    console.log(`Dados financeiros de exemplo criados com ID: ${financialRef.id}`);
    
    // Exemplo de uma campanha
    const campaignRef = db.collection('campaigns').doc();
    
    const campaignData = {
      name: "Lançamento Smartphone Galaxy Z",
      clientName: "Samsung Brasil",
      budget: 250000,
      startDate: new Date(2025, 5, 15), // 15 de junho de 2025
      endDate: new Date(2025, 7, 15),   // 15 de agosto de 2025
      status: 'active',
      influencers: [
        {
          influencerId: influencerRef.id,
          financialId: financialRef.id,
          negotiatedPrice: 20000,
          deliverables: ["1 Reels", "2 Stories", "1 Vídeo YouTube"],
          status: 'confirmed',
          paymentStatus: 'partial',
          notes: "Primeiro pagamento já realizado."
        }
      ],
      notes: "Campanha focada em humor para o lançamento do novo smartphone dobrável.",
      createdAt: now,
      updatedAt: now
    };
    
    await campaignRef.set(campaignData);
    console.log(`Campanha de exemplo criada com ID: ${campaignRef.id}`);
    
    console.log('Dados de exemplo criados com sucesso!');
    
  } catch (error) {
    console.error('Erro ao criar dados de exemplo:', error);
  }
}

// Executar a função de inicialização
initializeDatabase()
  .then(() => {
    console.log('Script concluído com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Erro ao executar script:', error);
    process.exit(1);
  });

