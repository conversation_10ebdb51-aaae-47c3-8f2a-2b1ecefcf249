import { db } from '@/lib/firebase-admin';

/**
 * 🔍 SCRIPT DE VALIDAÇÃO DE MIGRAÇÃO
 * FASE 5.4: Validação completa e detalhada da migração de dados
 */

// Interface para resultado de validação por coleção
interface CollectionValidationResult {
  collection: string;
  total: number;
  withUserId: number;
  withoutUserId: number;
  migrationComplete: boolean;
  issues: Array<{
    id: string;
    issue: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    details?: any;
  }>;
  performance: {
    validationTime: number;
    documentsPerSecond: number;
  };
  metadata: {
    lastMigrationDate?: string;
    migrationPhase?: string;
    migrationSource?: string;
  };
}

// Interface para resultado geral da validação
interface ValidationSummary {
  timestamp: string;
  collections: CollectionValidationResult[];
  summary: {
    totalCollections: number;
    totalDocuments: number;
    totalWithUserId: number;
    totalWithoutUserId: number;
    fullyMigrated: number;
    partiallyMigrated: number;
    notMigrated: number;
    overallSuccess: boolean;
    migrationCompleteness: number;
  };
  issues: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    total: number;
  };
  recommendations: string[];
  performance: {
    totalValidationTime: number;
    averageTimePerCollection: number;
  };
}

/**
 * Validar migração completa do sistema
 */
export async function validateMigration(options: {
  collections?: string[];
  detailed?: boolean;
  checkRelationships?: boolean;
  includeOrphaned?: boolean;
} = {}): Promise<ValidationSummary> {
  console.log('🔍 Iniciando validação completa da migração...');
  
  const config = {
    collections: ['brands', 'campaigns', 'influencers', 'groups', 'notes', 'tags', 'proposals'],
    detailed: true,
    checkRelationships: true,
    includeOrphaned: true,
    ...options
  };
  
  const startTime = Date.now();
  const results: CollectionValidationResult[] = [];
  const recommendations: string[] = [];
  
  // Validar cada coleção
  for (const collectionName of config.collections) {
    console.log(`\n📊 Validando coleção: ${collectionName}`);
    
    try {
      const result = await validateCollection(collectionName, config);
      results.push(result);
      
      console.log(`  ✅ ${result.withUserId}/${result.total} documentos com userId`);
      console.log(`  ⏱️ Tempo: ${result.performance.validationTime}ms`);
      
      if (result.issues.length > 0) {
        console.log(`  ⚠️ ${result.issues.length} problemas encontrados`);
      }
      
    } catch (error) {
      console.error(`  ❌ Erro ao validar ${collectionName}:`, error);
      
      results.push({
        collection: collectionName,
        total: 0,
        withUserId: 0,
        withoutUserId: 0,
        migrationComplete: false,
        issues: [{
          id: 'validation_error',
          issue: `Erro na validação: ${error.message}`,
          severity: 'critical'
        }],
        performance: { validationTime: 0, documentsPerSecond: 0 },
        metadata: {}
      });
    }
  }
  
  // Validações de relacionamentos
  if (config.checkRelationships) {
    console.log('\n🔗 Validando relacionamentos entre coleções...');
    const relationshipIssues = await validateRelationships();
    
    // Adicionar issues de relacionamento aos resultados
    relationshipIssues.forEach(issue => {
      const collectionResult = results.find(r => r.collection === issue.collection);
      if (collectionResult) {
        collectionResult.issues.push({
          id: issue.id,
          issue: issue.description,
          severity: issue.severity,
          details: issue.details
        });
      }
    });
  }
  
  // Calcular estatísticas gerais
  const totalCollections = results.length;
  const totalDocuments = results.reduce((sum, r) => sum + r.total, 0);
  const totalWithUserId = results.reduce((sum, r) => sum + r.withUserId, 0);
  const totalWithoutUserId = results.reduce((sum, r) => sum + r.withoutUserId, 0);
  const fullyMigrated = results.filter(r => r.migrationComplete).length;
  const partiallyMigrated = results.filter(r => r.withUserId > 0 && !r.migrationComplete).length;
  const notMigrated = results.filter(r => r.withUserId === 0).length;
  const migrationCompleteness = totalDocuments > 0 ? (totalWithUserId / totalDocuments) * 100 : 0;
  
  // Contar issues por severidade
  const allIssues = results.flatMap(r => r.issues);
  const issuesBySeverity = {
    critical: allIssues.filter(i => i.severity === 'critical').length,
    high: allIssues.filter(i => i.severity === 'high').length,
    medium: allIssues.filter(i => i.severity === 'medium').length,
    low: allIssues.filter(i => i.severity === 'low').length,
    total: allIssues.length
  };
  
  // Gerar recomendações
  recommendations.push(...generateRecommendations(results, issuesBySeverity, migrationCompleteness));
  
  // Calcular performance
  const totalValidationTime = Date.now() - startTime;
  const averageTimePerCollection = totalValidationTime / totalCollections;
  
  const summary: ValidationSummary = {
    timestamp: new Date().toISOString(),
    collections: results,
    summary: {
      totalCollections,
      totalDocuments,
      totalWithUserId,
      totalWithoutUserId,
      fullyMigrated,
      partiallyMigrated,
      notMigrated,
      overallSuccess: fullyMigrated === totalCollections && issuesBySeverity.critical === 0,
      migrationCompleteness
    },
    issues: issuesBySeverity,
    recommendations,
    performance: {
      totalValidationTime,
      averageTimePerCollection
    }
  };
  
  // Imprimir relatório
  printValidationReport(summary);
  
  return summary;
}

/**
 * Validar uma coleção específica
 */
async function validateCollection(
  collectionName: string, 
  config: any
): Promise<CollectionValidationResult> {
  const startTime = Date.now();
  
  const snapshot = await db.collection(collectionName).get();
  const total = snapshot.size;
  const issues: any[] = [];
  let withUserId = 0;
  let migrationMetadata: any = {};
  
  // Analisar cada documento
  for (const doc of snapshot.docs) {
    const data = doc.data();
    
    // Verificar presença de userId
    if (data.userId) {
      withUserId++;
      
      // Validação detalhada do userId
      if (config.detailed) {
        const userValidation = await validateUserId(data.userId, doc.id, collectionName);
        if (userValidation.issue) {
          issues.push(userValidation.issue);
        }
      }
      
      // Extrair metadados de migração
      if (data._migrationMetadata) {
        migrationMetadata = data._migrationMetadata;
      }
      
    } else {
      issues.push({
        id: doc.id,
        issue: 'Documento sem userId',
        severity: 'high' as const
      });
    }
    
    // Validações específicas por coleção
    const collectionSpecificIssues = await validateCollectionSpecificRules(collectionName, doc.id, data);
    issues.push(...collectionSpecificIssues);
  }
  
  const withoutUserId = total - withUserId;
  const migrationComplete = withoutUserId === 0 && issues.filter(i => i.severity === 'critical' || i.severity === 'high').length === 0;
  
  const validationTime = Date.now() - startTime;
  const documentsPerSecond = total > 0 ? Math.round((total / validationTime) * 1000) : 0;
  
  return {
    collection: collectionName,
    total,
    withUserId,
    withoutUserId,
    migrationComplete,
    issues,
    performance: {
      validationTime,
      documentsPerSecond
    },
    metadata: migrationMetadata
  };
}

/**
 * Validar se userId é válido
 */
async function validateUserId(
  userId: string, 
  documentId: string, 
  collection: string
): Promise<{ valid: boolean; issue?: any }> {
  try {
    // Verificar se é string válida
    if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
      return {
        valid: false,
        issue: {
          id: documentId,
          issue: 'UserId vazio ou inválido',
          severity: 'high' as const,
          details: { userId, collection }
        }
      };
    }
    
    // Verificar se usuário existe
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      return {
        valid: false,
        issue: {
          id: documentId,
          issue: `UserId não encontrado na coleção users: ${userId}`,
          severity: 'critical' as const,
          details: { userId, collection }
        }
      };
    }
    
    // Verificar se usuário está ativo
    const userData = userDoc.data();
    if (userData?.status === 'inactive' || userData?.deleted === true) {
      return {
        valid: false,
        issue: {
          id: documentId,
          issue: `UserId referencia usuário inativo: ${userId}`,
          severity: 'medium' as const,
          details: { userId, collection, userStatus: userData?.status }
        }
      };
    }
    
    return { valid: true };
    
  } catch (error) {
    return {
      valid: false,
      issue: {
        id: documentId,
        issue: `Erro ao validar userId: ${error.message}`,
        severity: 'high' as const,
        details: { userId, collection, error: error.message }
      }
    };
  }
}

/**
 * Validações específicas por coleção
 */
async function validateCollectionSpecificRules(
  collection: string, 
  documentId: string, 
  data: any
): Promise<any[]> {
  const issues: any[] = [];
  
  switch (collection) {
    case 'campaigns':
      // Validar relacionamento com brand
      if (data.brandId) {
        const brandDoc = await db.collection('brands').doc(data.brandId).get();
        
        if (!brandDoc.exists) {
          issues.push({
            id: documentId,
            issue: `Brand referenciada não existe: ${data.brandId}`,
            severity: 'high' as const,
            details: { brandId: data.brandId, collection }
          });
        } else {
          const brandData = brandDoc.data();
          
          // Verificar se brand tem userId
          if (!brandData?.userId) {
            issues.push({
              id: documentId,
              issue: `Brand referenciada não possui userId: ${data.brandId}`,
              severity: 'high' as const,
              details: { brandId: data.brandId, collection }
            });
          } else if (brandData.userId !== data.userId) {
            issues.push({
              id: documentId,
              issue: `UserIds não coincidem entre campaign e brand`,
              severity: 'critical' as const,
              details: { 
                campaignUserId: data.userId, 
                brandUserId: brandData.userId,
                brandId: data.brandId,
                collection 
              }
            });
          }
        }
      } else {
        issues.push({
          id: documentId,
          issue: 'Campaign sem brandId',
          severity: 'medium' as const,
          details: { collection }
        });
      }
      break;
      
    case 'brand_influencers':
      // Validar relacionamentos duplos
      if (data.brandId && data.influencerId) {
        const [brandDoc, influencerDoc] = await Promise.all([
          db.collection('brands').doc(data.brandId).get(),
          db.collection('influencers').doc(data.influencerId).get()
        ]);
        
        if (!brandDoc.exists) {
          issues.push({
            id: documentId,
            issue: `Brand não encontrada: ${data.brandId}`,
            severity: 'high' as const
          });
        }
        
        if (!influencerDoc.exists) {
          issues.push({
            id: documentId,
            issue: `Influencer não encontrado: ${data.influencerId}`,
            severity: 'high' as const
          });
        }
        
        // Verificar se todos têm o mesmo userId
        const brandUserId = brandDoc.data()?.userId;
        const influencerUserId = influencerDoc.data()?.userId;
        
        if (brandUserId !== data.userId || influencerUserId !== data.userId) {
          issues.push({
            id: documentId,
            issue: 'UserIds inconsistentes no relacionamento brand-influencer',
            severity: 'critical' as const,
            details: {
              relationshipUserId: data.userId,
              brandUserId,
              influencerUserId
            }
          });
        }
      }
      break;
      
    case 'proposals':
      // Validar relacionamentos com campaigns
      if (data.campaignId) {
        const campaignDoc = await db.collection('campaigns').doc(data.campaignId).get();
        
        if (!campaignDoc.exists) {
          issues.push({
            id: documentId,
            issue: `Campaign referenciada não existe: ${data.campaignId}`,
            severity: 'high' as const
          });
        } else {
          const campaignData = campaignDoc.data();
          if (campaignData?.userId !== data.userId) {
            issues.push({
              id: documentId,
              issue: 'UserIds não coincidem entre proposal e campaign',
              severity: 'critical' as const,
              details: {
                proposalUserId: data.userId,
                campaignUserId: campaignData.userId,
                campaignId: data.campaignId
              }
            });
          }
        }
      }
      break;
  }
  
  return issues;
}

/**
 * Validar relacionamentos entre coleções
 */
async function validateRelationships(): Promise<Array<{
  collection: string;
  id: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: any;
}>> {
  const issues: any[] = [];
  
  console.log('🔗 Verificando relacionamentos campaign-brand...');
  
  // Validar campaigns -> brands
  const campaignsSnapshot = await db.collection('campaigns').get();
  for (const campaignDoc of campaignsSnapshot.docs) {
    const campaign = campaignDoc.data();
    
    if (campaign.brandId && campaign.userId) {
      try {
        const brandDoc = await db.collection('brands').doc(campaign.brandId).get();
        
        if (!brandDoc.exists) {
          issues.push({
            collection: 'campaigns',
            id: campaignDoc.id,
            description: 'Brand referenciada não existe',
            severity: 'high' as const,
            details: { brandId: campaign.brandId }
          });
        } else {
          const brand = brandDoc.data();
          if (brand?.userId !== campaign.userId) {
            issues.push({
              collection: 'campaigns',
              id: campaignDoc.id,
              description: 'UserIds divergentes entre campaign e brand',
              severity: 'critical' as const,
              details: {
                campaignUserId: campaign.userId,
                brandUserId: brand.userId,
                brandId: campaign.brandId
              }
            });
          }
        }
      } catch (error) {
        issues.push({
          collection: 'campaigns',
          id: campaignDoc.id,
          description: `Erro ao validar relacionamento: ${error.message}`,
          severity: 'medium' as const,
          details: { error: error.message }
        });
      }
    }
  }
  
  console.log(`  📊 ${campaignsSnapshot.size} campaigns verificadas`);
  
  return issues;
}

/**
 * Gerar recomendações baseadas nos resultados
 */
function generateRecommendations(
  results: CollectionValidationResult[], 
  issues: any, 
  completeness: number
): string[] {
  const recommendations: string[] = [];
  
  // Recomendações baseadas na completude da migração
  if (completeness < 50) {
    recommendations.push('🚨 CRÍTICO: Menos de 50% dos documentos foram migrados. Execute novamente o script de migração.');
  } else if (completeness < 90) {
    recommendations.push('⚠️ ATENÇÃO: Migração incompleta. Verifique os documentos sem userId e execute correções.');
  } else if (completeness < 100) {
    recommendations.push('🔧 AJUSTE: Migração quase completa. Execute script de correção para os documentos restantes.');
  }
  
  // Recomendações baseadas em issues críticos
  if (issues.critical > 0) {
    recommendations.push('🚨 Issues críticos encontrados. Resolva imediatamente antes de prosseguir.');
  }
  
  if (issues.high > 10) {
    recommendations.push('⚠️ Muitos issues de alta severidade. Considere executar script de correção automática.');
  }
  
  // Recomendações específicas por coleção
  const problematicCollections = results.filter(r => !r.migrationComplete);
  if (problematicCollections.length > 0) {
    recommendations.push(`🔧 Coleções problemáticas: ${problematicCollections.map(c => c.collection).join(', ')}`);
  }
  
  // Recomendações de performance
  const slowCollections = results.filter(r => r.performance.documentsPerSecond < 10);
  if (slowCollections.length > 0) {
    recommendations.push('⚡ Performance baixa detectada. Considere otimizar consultas ou aumentar recursos.');
  }
  
  // Recomendação final
  if (completeness === 100 && issues.critical === 0 && issues.high === 0) {
    recommendations.push('🎉 Migração validada com sucesso! Sistema pronto para uso.');
  }
  
  return recommendations;
}

/**
 * Imprimir relatório de validação
 */
function printValidationReport(summary: ValidationSummary): void {
  console.log('\n📋 RELATÓRIO DE VALIDAÇÃO DA MIGRAÇÃO');
  console.log('=====================================');
  console.log(`⏰ Data/Hora: ${new Date(summary.timestamp).toLocaleString('pt-BR')}`);
  console.log(`⏱️ Tempo total: ${summary.performance.totalValidationTime}ms`);
  
  console.log('\n📊 RESUMO GERAL:');
  console.log(`📁 Coleções analisadas: ${summary.summary.totalCollections}`);
  console.log(`📄 Total de documentos: ${summary.summary.totalDocuments}`);
  console.log(`✅ Com userId: ${summary.summary.totalWithUserId}`);
  console.log(`❌ Sem userId: ${summary.summary.totalWithoutUserId}`);
  console.log(`📈 Completude: ${summary.summary.migrationCompleteness.toFixed(1)}%`);
  
  console.log('\n🏆 STATUS POR COLEÇÃO:');
  summary.collections.forEach(col => {
    const icon = col.migrationComplete ? '✅' : '⚠️';
    const percentage = col.total > 0 ? ((col.withUserId / col.total) * 100).toFixed(1) : '0.0';
    console.log(`${icon} ${col.collection}: ${col.withUserId}/${col.total} (${percentage}%)`);
    
    if (col.issues.length > 0) {
      const criticalIssues = col.issues.filter(i => i.severity === 'critical').length;
      const highIssues = col.issues.filter(i => i.severity === 'high').length;
      console.log(`   🚨 Critical: ${criticalIssues} | ⚠️ High: ${highIssues} | Total: ${col.issues.length}`);
    }
  });
  
  console.log('\n🚨 ISSUES POR SEVERIDADE:');
  console.log(`🚨 Critical: ${summary.issues.critical}`);
  console.log(`⚠️ High: ${summary.issues.high}`);
  console.log(`🔸 Medium: ${summary.issues.medium}`);
  console.log(`🔹 Low: ${summary.issues.low}`);
  console.log(`📊 Total: ${summary.issues.total}`);
  
  if (summary.recommendations.length > 0) {
    console.log('\n💡 RECOMENDAÇÕES:');
    summary.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });
  }
  
  console.log('\n🎯 STATUS FINAL:');
  if (summary.summary.overallSuccess) {
    console.log('✅ MIGRAÇÃO VALIDADA COM SUCESSO!');
  } else {
    console.log('❌ MIGRAÇÃO REQUER ATENÇÃO');
  }
}

/**
 * Função principal para execução via linha de comando
 */
export async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const collectionsArg = args.find(arg => arg.startsWith('--collections='));
  const detailed = args.includes('--detailed');
  const noRelationships = args.includes('--no-relationships');
  const jsonOutput = args.includes('--json');
  
  const collections = collectionsArg ? collectionsArg.split('=')[1].split(',') : undefined;
  
  try {
    console.log('🔍 Iniciando validação da migração...\n');
    
    const result = await validateMigration({
      collections,
      detailed,
      checkRelationships: !noRelationships,
      includeOrphaned: true
    });
    
    if (jsonOutput) {
      console.log('\n📄 Resultado em JSON:');
      console.log(JSON.stringify(result, null, 2));
    }
    
    // Código de saída baseado no resultado
    const hasErrors = result.issues.critical > 0 || result.summary.migrationCompleteness < 100;
    process.exit(hasErrors ? 1 : 0);
    
  } catch (error) {
    console.error('❌ Erro na validação:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 

