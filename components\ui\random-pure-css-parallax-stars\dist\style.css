body {
  width: 100vw;
  height: 100vh;
  margin: 0;
  overflow: hidden;
  background-image: radial-gradient(center, ellipse cover, #27364f 0%, #112 50%, #112 100%);
  background: -moz-radial-gradient(center, ellipse cover, #27364f 0%, #112 50%, #112 100%);
  background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%, #27364f), color-stop(50%, #112), color-stop(100%, #112));
  background: -webkit-radial-gradient(center, ellipse cover, #27364f 0%, #112 50%, #112 100%);
  background: -o-radial-gradient(center, ellipse cover, #27364f 0%, #112 50%, #112 100%);
  background: -ms-radial-gradient(center, ellipse cover, #27364f 0%, #112 50%, #112 100%);
  filter: 'progid:DXImageTransform.Microsoft.gradient( startColorstr=' #27364f ', endColorstr=' #112 ',GradientType=1 )';
  background-position: 50% 0%;
  background-size: 150vmax 150vmax;
  background-repeat: no-repeat;
  background-color: #112;
}
.stars .container {
  position: absolute;
  animation: stars linear infinite;
}
.stars .container .star {
  animation: twinkle linear infinite;
  border-radius: 100%;
  transform: translateZ(0);
}
.stars .container:nth-child(0) {
  width: 1px;
  height: 1px;
  left: 15vw;
  animation-delay: -984.6s;
  animation-duration: 267.2s;
}
.stars .container:nth-child(0) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44s;
  animation-duration: 27.1s;
  background: rgba(246,195,106,0.8);
}
.stars .container:nth-child(1) {
  width: 2px;
  height: 2px;
  left: 25vw;
  animation-delay: -997.4s;
  animation-duration: 153.7s;
}
.stars .container:nth-child(1) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.6s;
  animation-duration: 32s;
  background: #e7caeb;
}
.stars .container:nth-child(2) {
  width: 3px;
  height: 3px;
  left: 51.5vw;
  animation-delay: -996.4666666666667s;
  animation-duration: 20.666666666666668s;
}
.stars .container:nth-child(2) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.8s;
  animation-duration: 41.5s;
  background: #cbdc64;
}
.stars .container:nth-child(3) {
  width: 2px;
  height: 2px;
  left: 11.5vw;
  animation-delay: -994.5s;
  animation-duration: 89s;
}
.stars .container:nth-child(3) .star {
  width: inherit;
  height: inherit;
  animation-delay: -47.9s;
  animation-duration: 28.1s;
  background: rgba(227,166,231,0.7);
}
.stars .container:nth-child(4) {
  width: 2px;
  height: 2px;
  left: 25.5vw;
  animation-delay: -992.1s;
  animation-duration: 109.9s;
}
.stars .container:nth-child(4) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.7s;
  animation-duration: 18.8s;
  background: #dbfaa6;
}
.stars .container:nth-child(5) {
  width: 2px;
  height: 2px;
  left: 15.5vw;
  animation-delay: -997s;
  animation-duration: 23.2s;
}
.stars .container:nth-child(5) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.4s;
  animation-duration: 19.5s;
  background: rgba(212,198,225,0.7);
}
.stars .container:nth-child(6) {
  width: 1px;
  height: 1px;
  left: 65vw;
  animation-delay: -992.4s;
  animation-duration: 385.2s;
}
.stars .container:nth-child(6) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.2s;
  animation-duration: 13.9s;
  background: rgba(214,164,206,0.8);
}
.stars .container:nth-child(7) {
  width: 1px;
  height: 1px;
  left: 10.5vw;
  animation-delay: -993.8s;
  animation-duration: 204.8s;
}
.stars .container:nth-child(7) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.8s;
  animation-duration: 7.4s;
  background: #f8e8a7;
}
.stars .container:nth-child(8) {
  width: 2px;
  height: 2px;
  left: 13vw;
  animation-delay: -994.1s;
  animation-duration: 33.7s;
}
.stars .container:nth-child(8) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.4s;
  animation-duration: 45.6s;
  background: rgba(229,208,154,0.8);
}
.stars .container:nth-child(9) {
  width: 2px;
  height: 2px;
  left: 47vw;
  animation-delay: -997.1s;
  animation-duration: 120.5s;
}
.stars .container:nth-child(9) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.9s;
  animation-duration: 48s;
  background: rgba(209,163,157,0.8);
}
.stars .container:nth-child(10) {
  width: 3px;
  height: 3px;
  left: 75.5vw;
  animation-delay: -999.1333333333333s;
  animation-duration: 131.46666666666667s;
}
.stars .container:nth-child(10) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.3s;
  animation-duration: 41.5s;
  background: #f8f168;
}
.stars .container:nth-child(11) {
  width: 2px;
  height: 2px;
  left: 66.5vw;
  animation-delay: -999.4s;
  animation-duration: 171.9s;
}
.stars .container:nth-child(11) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.2s;
  animation-duration: 14.9s;
  background: rgba(243,172,168,0.9);
}
.stars .container:nth-child(12) {
  width: 1px;
  height: 1px;
  left: 96.5vw;
  animation-delay: -986.2s;
  animation-duration: 373.8s;
}
.stars .container:nth-child(12) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.7s;
  animation-duration: 39.8s;
  background: rgba(238,164,203,0.7);
}
.stars .container:nth-child(13) {
  width: 3px;
  height: 3px;
  left: 5vw;
  animation-delay: -999.6666666666666s;
  animation-duration: 110.39999999999999s;
}
.stars .container:nth-child(13) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.7s;
  animation-duration: 31.4s;
  background: rgba(241,186,107,0.9);
}
.stars .container:nth-child(14) {
  width: 2px;
  height: 2px;
  left: 51vw;
  animation-delay: -996.5s;
  animation-duration: 138.7s;
}
.stars .container:nth-child(14) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.6s;
  animation-duration: 13s;
  background: #e2e777;
}
.stars .container:nth-child(15) {
  width: 1px;
  height: 1px;
  left: 80.5vw;
  animation-delay: -994s;
  animation-duration: 327.2s;
}
.stars .container:nth-child(15) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.8s;
  animation-duration: 24.3s;
  background: rgba(209,159,136,0.8);
}
.stars .container:nth-child(16) {
  width: 3px;
  height: 3px;
  left: 48vw;
  animation-delay: -996.5333333333333s;
  animation-duration: 66.93333333333334s;
}
.stars .container:nth-child(16) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.7s;
  animation-duration: 21.4s;
  background: #d9b793;
}
.stars .container:nth-child(17) {
  width: 2px;
  height: 2px;
  left: 81.5vw;
  animation-delay: -992.4s;
  animation-duration: 87.8s;
}
.stars .container:nth-child(17) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42s;
  animation-duration: 28s;
  background: rgba(214,215,164,0.7);
}
.stars .container:nth-child(18) {
  width: 3px;
  height: 3px;
  left: 78vw;
  animation-delay: -998.8666666666667s;
  animation-duration: 105.93333333333334s;
}
.stars .container:nth-child(18) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.5s;
  animation-duration: 48.4s;
  background: rgba(226,238,231,0.8);
}
.stars .container:nth-child(19) {
  width: 1px;
  height: 1px;
  left: 23.5vw;
  animation-delay: -999.8s;
  animation-duration: 195.6s;
}
.stars .container:nth-child(19) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.2s;
  animation-duration: 10.1s;
  background: rgba(233,180,215,0.8);
}
.stars .container:nth-child(20) {
  width: 3px;
  height: 3px;
  left: 54.5vw;
  animation-delay: -996.4s;
  animation-duration: 119.46666666666665s;
}
.stars .container:nth-child(20) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42s;
  animation-duration: 27.1s;
  background: #efbe6e;
}
.stars .container:nth-child(21) {
  width: 3px;
  height: 3px;
  left: 91.5vw;
  animation-delay: -998.4s;
  animation-duration: 110.06666666666666s;
}
.stars .container:nth-child(21) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.7s;
  animation-duration: 37.3s;
  background: #ecd365;
}
.stars .container:nth-child(22) {
  width: 2px;
  height: 2px;
  left: 41vw;
  animation-delay: -991.3s;
  animation-duration: 167.8s;
}
.stars .container:nth-child(22) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.3s;
  animation-duration: 40.9s;
  background: rgba(207,203,209,0.9);
}
.stars .container:nth-child(23) {
  width: 2px;
  height: 2px;
  left: 42.5vw;
  animation-delay: -990.9s;
  animation-duration: 79.4s;
}
.stars .container:nth-child(23) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.9s;
  animation-duration: 39s;
  background: rgba(219,239,224,0.9);
}
.stars .container:nth-child(24) {
  width: 3px;
  height: 3px;
  left: 19vw;
  animation-delay: -997.6666666666666s;
  animation-duration: 90.13333333333333s;
}
.stars .container:nth-child(24) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.8s;
  animation-duration: 16.1s;
  background: rgba(229,244,196,0.8);
}
.stars .container:nth-child(25) {
  width: 2px;
  height: 2px;
  left: 99.5vw;
  animation-delay: -993.3s;
  animation-duration: 81.5s;
}
.stars .container:nth-child(25) .star {
  width: inherit;
  height: inherit;
  animation-delay: -47.5s;
  animation-duration: 45s;
  background: #d99a77;
}
.stars .container:nth-child(26) {
  width: 2px;
  height: 2px;
  left: 81vw;
  animation-delay: -997.2s;
  animation-duration: 20.7s;
}
.stars .container:nth-child(26) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.6s;
  animation-duration: 6.9s;
  background: rgba(242,152,223,0.9);
}
.stars .container:nth-child(27) {
  width: 1px;
  height: 1px;
  left: 12vw;
  animation-delay: -991s;
  animation-duration: 154.2s;
}
.stars .container:nth-child(27) .star {
  width: inherit;
  height: inherit;
  animation-delay: -47.2s;
  animation-duration: 20.7s;
  background: rgba(251,214,115,0.8);
}
.stars .container:nth-child(28) {
  width: 2px;
  height: 2px;
  left: 30.5vw;
  animation-delay: -998s;
  animation-duration: 59s;
}
.stars .container:nth-child(28) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.5s;
  animation-duration: 31.6s;
  background: rgba(206,238,254,0.9);
}
.stars .container:nth-child(29) {
  width: 3px;
  height: 3px;
  left: 52.5vw;
  animation-delay: -994.0666666666667s;
  animation-duration: 49.199999999999996s;
}
.stars .container:nth-child(29) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.1s;
  animation-duration: 14.4s;
  background: rgba(254,253,150,0.9);
}
.stars .container:nth-child(30) {
  width: 1px;
  height: 1px;
  left: 67.5vw;
  animation-delay: -996.2s;
  animation-duration: 325.8s;
}
.stars .container:nth-child(30) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.6s;
  animation-duration: 11.2s;
  background: rgba(213,165,250,0.7);
}
.stars .container:nth-child(31) {
  width: 1px;
  height: 1px;
  left: 99.5vw;
  animation-delay: -996.8s;
  animation-duration: 387.4s;
}
.stars .container:nth-child(31) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.5s;
  animation-duration: 33.5s;
  background: rgba(207,201,204,0.7);
}
.stars .container:nth-child(32) {
  width: 1px;
  height: 1px;
  left: 54vw;
  animation-delay: -991s;
  animation-duration: 140.2s;
}
.stars .container:nth-child(32) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.8s;
  animation-duration: 29.5s;
  background: rgba(254,204,209,0.7);
}
.stars .container:nth-child(33) {
  width: 1px;
  height: 1px;
  left: 74.5vw;
  animation-delay: -994s;
  animation-duration: 45.6s;
}
.stars .container:nth-child(33) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41s;
  animation-duration: 35.9s;
  background: rgba(255,160,145,0.8);
}
.stars .container:nth-child(34) {
  width: 3px;
  height: 3px;
  left: 64.5vw;
  animation-delay: -998.7333333333333s;
  animation-duration: 92.86666666666667s;
}
.stars .container:nth-child(34) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40s;
  animation-duration: 48.5s;
  background: rgba(245,227,239,0.8);
}
.stars .container:nth-child(35) {
  width: 2px;
  height: 2px;
  left: 30.5vw;
  animation-delay: -990.4s;
  animation-duration: 147s;
}
.stars .container:nth-child(35) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.7s;
  animation-duration: 17.4s;
  background: #eb9a6c;
}
.stars .container:nth-child(36) {
  width: 1px;
  height: 1px;
  left: 78vw;
  animation-delay: -997.2s;
  animation-duration: 294.4s;
}
.stars .container:nth-child(36) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.1s;
  animation-duration: 8.5s;
  background: rgba(201,243,140,0.8);
}
.stars .container:nth-child(37) {
  width: 2px;
  height: 2px;
  left: 63.5vw;
  animation-delay: -993.3s;
  animation-duration: 157.7s;
}
.stars .container:nth-child(37) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.7s;
  animation-duration: 24.3s;
  background: rgba(252,166,208,0.9);
}
.stars .container:nth-child(38) {
  width: 3px;
  height: 3px;
  left: 48vw;
  animation-delay: -994.6s;
  animation-duration: 111.66666666666667s;
}
.stars .container:nth-child(38) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.7s;
  animation-duration: 26.3s;
  background: #e2f778;
}
.stars .container:nth-child(39) {
  width: 2px;
  height: 2px;
  left: 31vw;
  animation-delay: -995.1s;
  animation-duration: 103.8s;
}
.stars .container:nth-child(39) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.2s;
  animation-duration: 36.5s;
  background: #e9aeb0;
}
.stars .container:nth-child(40) {
  width: 2px;
  height: 2px;
  left: 85vw;
  animation-delay: -999.3s;
  animation-duration: 26s;
}
.stars .container:nth-child(40) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.4s;
  animation-duration: 39.5s;
  background: rgba(235,187,157,0.9);
}
.stars .container:nth-child(41) {
  width: 1px;
  height: 1px;
  left: 5.5vw;
  animation-delay: -990.6s;
  animation-duration: 265.8s;
}
.stars .container:nth-child(41) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.8s;
  animation-duration: 10.5s;
  background: rgba(241,181,119,0.8);
}
.stars .container:nth-child(42) {
  width: 3px;
  height: 3px;
  left: 10vw;
  animation-delay: -999.9333333333333s;
  animation-duration: 127s;
}
.stars .container:nth-child(42) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.6s;
  animation-duration: 45.2s;
  background: #d6c0b1;
}
.stars .container:nth-child(43) {
  width: 1px;
  height: 1px;
  left: 98.5vw;
  animation-delay: -988.8s;
  animation-duration: 337.8s;
}
.stars .container:nth-child(43) .star {
  width: inherit;
  height: inherit;
  animation-delay: -47.9s;
  animation-duration: 25.7s;
  background: rgba(200,191,189,0.7);
}
.stars .container:nth-child(44) {
  width: 3px;
  height: 3px;
  left: 86vw;
  animation-delay: -994.8s;
  animation-duration: 28.066666666666666s;
}
.stars .container:nth-child(44) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.3s;
  animation-duration: 17.5s;
  background: rgba(212,240,148,0.8);
}
.stars .container:nth-child(45) {
  width: 3px;
  height: 3px;
  left: 97vw;
  animation-delay: -999.6s;
  animation-duration: 66.2s;
}
.stars .container:nth-child(45) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.3s;
  animation-duration: 43.9s;
  background: rgba(240,252,186,0.9);
}
.stars .container:nth-child(46) {
  width: 1px;
  height: 1px;
  left: 52vw;
  animation-delay: -994s;
  animation-duration: 245s;
}
.stars .container:nth-child(46) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.1s;
  animation-duration: 39.7s;
  background: rgba(209,207,158,0.9);
}
.stars .container:nth-child(47) {
  width: 2px;
  height: 2px;
  left: 72vw;
  animation-delay: -996.4s;
  animation-duration: 86.4s;
}
.stars .container:nth-child(47) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.5s;
  animation-duration: 5.4s;
  background: rgba(230,159,131,0.9);
}
.stars .container:nth-child(48) {
  width: 2px;
  height: 2px;
  left: 17vw;
  animation-delay: -993.8s;
  animation-duration: 175.6s;
}
.stars .container:nth-child(48) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.3s;
  animation-duration: 17.3s;
  background: rgba(229,208,168,0.7);
}
.stars .container:nth-child(49) {
  width: 2px;
  height: 2px;
  left: 26.5vw;
  animation-delay: -992.4s;
  animation-duration: 59s;
}
.stars .container:nth-child(49) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.4s;
  animation-duration: 38s;
  background: rgba(247,228,170,0.7);
}
.stars .container:nth-child(50) {
  width: 3px;
  height: 3px;
  left: 7vw;
  animation-delay: -997.4666666666667s;
  animation-duration: 21.400000000000002s;
}
.stars .container:nth-child(50) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40s;
  animation-duration: 41.6s;
  background: rgba(214,199,141,0.9);
}
.stars .container:nth-child(51) {
  width: 2px;
  height: 2px;
  left: 11.5vw;
  animation-delay: -995.5s;
  animation-duration: 121.4s;
}
.stars .container:nth-child(51) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.4s;
  animation-duration: 19.2s;
  background: rgba(224,180,226,0.9);
}
.stars .container:nth-child(52) {
  width: 2px;
  height: 2px;
  left: 1vw;
  animation-delay: -996.8s;
  animation-duration: 157.4s;
}
.stars .container:nth-child(52) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.3s;
  animation-duration: 16.5s;
  background: rgba(201,214,207,0.9);
}
.stars .container:nth-child(53) {
  width: 2px;
  height: 2px;
  left: 62.5vw;
  animation-delay: -994.2s;
  animation-duration: 68.1s;
}
.stars .container:nth-child(53) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45s;
  animation-duration: 6s;
  background: rgba(221,163,222,0.7);
}
.stars .container:nth-child(54) {
  width: 1px;
  height: 1px;
  left: 43.5vw;
  animation-delay: -995.8s;
  animation-duration: 300.2s;
}
.stars .container:nth-child(54) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.9s;
  animation-duration: 23.6s;
  background: #c9b1cc;
}
.stars .container:nth-child(55) {
  width: 2px;
  height: 2px;
  left: 36.5vw;
  animation-delay: -990.4s;
  animation-duration: 61.4s;
}
.stars .container:nth-child(55) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.3s;
  animation-duration: 41.4s;
  background: rgba(230,173,240,0.7);
}
.stars .container:nth-child(56) {
  width: 3px;
  height: 3px;
  left: 1vw;
  animation-delay: -999.6s;
  animation-duration: 48.6s;
}
.stars .container:nth-child(56) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.6s;
  animation-duration: 21s;
  background: rgba(238,194,212,0.9);
}
.stars .container:nth-child(57) {
  width: 3px;
  height: 3px;
  left: 68.5vw;
  animation-delay: -999.4s;
  animation-duration: 45.800000000000004s;
}
.stars .container:nth-child(57) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.3s;
  animation-duration: 33.7s;
  background: rgba(216,214,209,0.7);
}
.stars .container:nth-child(58) {
  width: 3px;
  height: 3px;
  left: 54.5vw;
  animation-delay: -996.0666666666667s;
  animation-duration: 49.26666666666667s;
}
.stars .container:nth-child(58) .star {
  width: inherit;
  height: inherit;
  animation-delay: -47.5s;
  animation-duration: 6.9s;
  background: #d5bc78;
}
.stars .container:nth-child(59) {
  width: 1px;
  height: 1px;
  left: 13.5vw;
  animation-delay: -983.8s;
  animation-duration: 241.4s;
}
.stars .container:nth-child(59) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.2s;
  animation-duration: 37.8s;
  background: rgba(243,254,209,0.7);
}
.stars .container:nth-child(60) {
  width: 1px;
  height: 1px;
  left: 74.5vw;
  animation-delay: -989.8s;
  animation-duration: 166.6s;
}
.stars .container:nth-child(60) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48s;
  animation-duration: 23.1s;
  background: #ccd1a6;
}
.stars .container:nth-child(61) {
  width: 1px;
  height: 1px;
  left: 49.5vw;
  animation-delay: -993.2s;
  animation-duration: 132.8s;
}
.stars .container:nth-child(61) .star {
  width: inherit;
  height: inherit;
  animation-delay: -47.3s;
  animation-duration: 47.2s;
  background: rgba(229,205,161,0.8);
}
.stars .container:nth-child(62) {
  width: 1px;
  height: 1px;
  left: 96.5vw;
  animation-delay: -996.8s;
  animation-duration: 195.8s;
}
.stars .container:nth-child(62) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.2s;
  animation-duration: 34.4s;
  background: rgba(248,190,113,0.8);
}
.stars .container:nth-child(63) {
  width: 1px;
  height: 1px;
  left: 73vw;
  animation-delay: -994.2s;
  animation-duration: 58s;
}
.stars .container:nth-child(63) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.1s;
  animation-duration: 24.1s;
  background: #dabae3;
}
.stars .container:nth-child(64) {
  width: 1px;
  height: 1px;
  left: 52.5vw;
  animation-delay: -990s;
  animation-duration: 229.4s;
}
.stars .container:nth-child(64) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.9s;
  animation-duration: 28.9s;
  background: rgba(241,168,164,0.7);
}
.stars .container:nth-child(65) {
  width: 1px;
  height: 1px;
  left: 81vw;
  animation-delay: -997.4s;
  animation-duration: 168s;
}
.stars .container:nth-child(65) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.6s;
  animation-duration: 17.9s;
  background: #f7cfe7;
}
.stars .container:nth-child(66) {
  width: 3px;
  height: 3px;
  left: 68vw;
  animation-delay: -995.8s;
  animation-duration: 130.13333333333333s;
}
.stars .container:nth-child(66) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.9s;
  animation-duration: 33.1s;
  background: rgba(214,247,124,0.9);
}
.stars .container:nth-child(67) {
  width: 1px;
  height: 1px;
  left: 52vw;
  animation-delay: -980.4s;
  animation-duration: 105.2s;
}
.stars .container:nth-child(67) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.3s;
  animation-duration: 34.5s;
  background: rgba(230,250,170,0.9);
}
.stars .container:nth-child(68) {
  width: 3px;
  height: 3px;
  left: 22.5vw;
  animation-delay: -999.7333333333333s;
  animation-duration: 44.666666666666664s;
}
.stars .container:nth-child(68) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.1s;
  animation-duration: 13.5s;
  background: rgba(242,231,132,0.7);
}
.stars .container:nth-child(69) {
  width: 1px;
  height: 1px;
  left: 50.5vw;
  animation-delay: -984.8s;
  animation-duration: 374.6s;
}
.stars .container:nth-child(69) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.2s;
  animation-duration: 34.1s;
  background: rgba(223,198,188,0.7);
}
.stars .container:nth-child(70) {
  width: 1px;
  height: 1px;
  left: 70vw;
  animation-delay: -991s;
  animation-duration: 160.6s;
}
.stars .container:nth-child(70) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.2s;
  animation-duration: 15.3s;
  background: #c8f3b9;
}
.stars .container:nth-child(71) {
  width: 3px;
  height: 3px;
  left: 32.5vw;
  animation-delay: -994.0666666666667s;
  animation-duration: 74.93333333333334s;
}
.stars .container:nth-child(71) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.6s;
  animation-duration: 11.3s;
  background: rgba(209,190,132,0.9);
}
.stars .container:nth-child(72) {
  width: 2px;
  height: 2px;
  left: 15vw;
  animation-delay: -991.2s;
  animation-duration: 24.4s;
}
.stars .container:nth-child(72) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.3s;
  animation-duration: 40.3s;
  background: rgba(217,157,149,0.8);
}
.stars .container:nth-child(73) {
  width: 2px;
  height: 2px;
  left: 73.5vw;
  animation-delay: -993.4s;
  animation-duration: 78.1s;
}
.stars .container:nth-child(73) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.9s;
  animation-duration: 21.7s;
  background: rgba(248,162,156,0.8);
}
.stars .container:nth-child(74) {
  width: 1px;
  height: 1px;
  left: 60.5vw;
  animation-delay: -985.8s;
  animation-duration: 304s;
}
.stars .container:nth-child(74) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.6s;
  animation-duration: 43.7s;
  background: rgba(241,245,209,0.8);
}
.stars .container:nth-child(75) {
  width: 2px;
  height: 2px;
  left: 5vw;
  animation-delay: -990.6s;
  animation-duration: 165.7s;
}
.stars .container:nth-child(75) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.3s;
  animation-duration: 48s;
  background: rgba(204,204,117,0.8);
}
.stars .container:nth-child(76) {
  width: 2px;
  height: 2px;
  left: 83vw;
  animation-delay: -996.1s;
  animation-duration: 92.6s;
}
.stars .container:nth-child(76) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.1s;
  animation-duration: 41.6s;
  background: rgba(226,156,212,0.8);
}
.stars .container:nth-child(77) {
  width: 1px;
  height: 1px;
  left: 96.5vw;
  animation-delay: -993.8s;
  animation-duration: 89.4s;
}
.stars .container:nth-child(77) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.1s;
  animation-duration: 43.6s;
  background: rgba(221,203,238,0.9);
}
.stars .container:nth-child(78) {
  width: 2px;
  height: 2px;
  left: 46.5vw;
  animation-delay: -999.1s;
  animation-duration: 104.3s;
}
.stars .container:nth-child(78) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.7s;
  animation-duration: 11.7s;
  background: #e8adf4;
}
.stars .container:nth-child(79) {
  width: 2px;
  height: 2px;
  left: 85vw;
  animation-delay: -992.8s;
  animation-duration: 163.8s;
}
.stars .container:nth-child(79) .star {
  width: inherit;
  height: inherit;
  animation-delay: -43.3s;
  animation-duration: 11.8s;
  background: #deaab2;
}
.stars .container:nth-child(80) {
  width: 1px;
  height: 1px;
  left: 37vw;
  animation-delay: -991.6s;
  animation-duration: 82s;
}
.stars .container:nth-child(80) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.1s;
  animation-duration: 36.5s;
  background: rgba(231,225,147,0.7);
}
.stars .container:nth-child(81) {
  width: 2px;
  height: 2px;
  left: 98.5vw;
  animation-delay: -993s;
  animation-duration: 22.7s;
}
.stars .container:nth-child(81) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46s;
  animation-duration: 8.6s;
  background: rgba(205,200,214,0.8);
}
.stars .container:nth-child(82) {
  width: 2px;
  height: 2px;
  left: 34vw;
  animation-delay: -995.1s;
  animation-duration: 115.8s;
}
.stars .container:nth-child(82) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.5s;
  animation-duration: 24.3s;
  background: rgba(231,184,112,0.8);
}
.stars .container:nth-child(83) {
  width: 3px;
  height: 3px;
  left: 8vw;
  animation-delay: -998.2s;
  animation-duration: 113.86666666666667s;
}
.stars .container:nth-child(83) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40s;
  animation-duration: 32.8s;
  background: rgba(249,194,195,0.8);
}
.stars .container:nth-child(84) {
  width: 2px;
  height: 2px;
  left: 61.5vw;
  animation-delay: -996s;
  animation-duration: 161.2s;
}
.stars .container:nth-child(84) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44s;
  animation-duration: 22.7s;
  background: rgba(250,153,202,0.8);
}
.stars .container:nth-child(85) {
  width: 3px;
  height: 3px;
  left: 2vw;
  animation-delay: -994.4666666666667s;
  animation-duration: 60.26666666666667s;
}
.stars .container:nth-child(85) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.7s;
  animation-duration: 46.5s;
  background: rgba(202,201,183,0.9);
}
.stars .container:nth-child(86) {
  width: 1px;
  height: 1px;
  left: 69vw;
  animation-delay: -999s;
  animation-duration: 102.4s;
}
.stars .container:nth-child(86) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.2s;
  animation-duration: 38.4s;
  background: rgba(220,197,180,0.8);
}
.stars .container:nth-child(87) {
  width: 3px;
  height: 3px;
  left: 98vw;
  animation-delay: -998.8s;
  animation-duration: 98.8s;
}
.stars .container:nth-child(87) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41s;
  animation-duration: 7.5s;
  background: rgba(212,222,209,0.9);
}
.stars .container:nth-child(88) {
  width: 2px;
  height: 2px;
  left: 60vw;
  animation-delay: -997.1s;
  animation-duration: 140.4s;
}
.stars .container:nth-child(88) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.3s;
  animation-duration: 16.8s;
  background: rgba(235,216,254,0.9);
}
.stars .container:nth-child(89) {
  width: 2px;
  height: 2px;
  left: 29.5vw;
  animation-delay: -990.9s;
  animation-duration: 29.5s;
}
.stars .container:nth-child(89) .star {
  width: inherit;
  height: inherit;
  animation-delay: -48.6s;
  animation-duration: 5.5s;
  background: #dfbae8;
}
.stars .container:nth-child(90) {
  width: 2px;
  height: 2px;
  left: 26vw;
  animation-delay: -991.6s;
  animation-duration: 197s;
}
.stars .container:nth-child(90) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.7s;
  animation-duration: 10.2s;
  background: rgba(231,186,215,0.9);
}
.stars .container:nth-child(91) {
  width: 3px;
  height: 3px;
  left: 35vw;
  animation-delay: -993.8666666666667s;
  animation-duration: 48.333333333333336s;
}
.stars .container:nth-child(91) .star {
  width: inherit;
  height: inherit;
  animation-delay: -44.1s;
  animation-duration: 13.6s;
  background: rgba(229,254,161,0.7);
}
.stars .container:nth-child(92) {
  width: 3px;
  height: 3px;
  left: 15vw;
  animation-delay: -995.8666666666667s;
  animation-duration: 47.4s;
}
.stars .container:nth-child(92) .star {
  width: inherit;
  height: inherit;
  animation-delay: -42.3s;
  animation-duration: 20.5s;
  background: rgba(237,234,123,0.9);
}
.stars .container:nth-child(93) {
  width: 1px;
  height: 1px;
  left: 38.5vw;
  animation-delay: -999s;
  animation-duration: 179.4s;
}
.stars .container:nth-child(93) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.3s;
  animation-duration: 33.7s;
  background: #eefbf2;
}
.stars .container:nth-child(94) {
  width: 2px;
  height: 2px;
  left: 73vw;
  animation-delay: -998.4s;
  animation-duration: 61.7s;
}
.stars .container:nth-child(94) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.1s;
  animation-duration: 6.9s;
  background: rgba(207,165,161,0.7);
}
.stars .container:nth-child(95) {
  width: 1px;
  height: 1px;
  left: 62.5vw;
  animation-delay: -993.8s;
  animation-duration: 314.4s;
}
.stars .container:nth-child(95) .star {
  width: inherit;
  height: inherit;
  animation-delay: -40.9s;
  animation-duration: 43s;
  background: rgba(210,168,100,0.9);
}
.stars .container:nth-child(96) {
  width: 1px;
  height: 1px;
  left: 87.5vw;
  animation-delay: -991s;
  animation-duration: 176.4s;
}
.stars .container:nth-child(96) .star {
  width: inherit;
  height: inherit;
  animation-delay: -41.7s;
  animation-duration: 18.2s;
  background: rgba(216,195,130,0.8);
}
.stars .container:nth-child(97) {
  width: 1px;
  height: 1px;
  left: 79vw;
  animation-delay: -998s;
  animation-duration: 155.6s;
}
.stars .container:nth-child(97) .star {
  width: inherit;
  height: inherit;
  animation-delay: -49.9s;
  animation-duration: 28.7s;
  background: rgba(218,220,200,0.8);
}
.stars .container:nth-child(98) {
  width: 3px;
  height: 3px;
  left: 0vw;
  animation-delay: -993.7333333333333s;
  animation-duration: 75.06666666666666s;
}
.stars .container:nth-child(98) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.7s;
  animation-duration: 43s;
  background: rgba(202,191,106,0.8);
}
.stars .container:nth-child(99) {
  width: 2px;
  height: 2px;
  left: 87vw;
  animation-delay: -994.7s;
  animation-duration: 85.5s;
}
.stars .container:nth-child(99) .star {
  width: inherit;
  height: inherit;
  animation-delay: -46.4s;
  animation-duration: 12.6s;
  background: #e69bc9;
}
.stars .container:nth-child(100) {
  width: 3px;
  height: 3px;
  left: 78vw;
  animation-delay: -995s;
  animation-duration: 79.66666666666667s;
}
.stars .container:nth-child(100) .star {
  width: inherit;
  height: inherit;
  animation-delay: -45.8s;
  animation-duration: 45.4s;
  background: rgba(242,163,107,0.7);
}
@-moz-keyframes stars {
  0% {
    transform: translateY(110vh) translateZ(0);
  }
  100% {
    transform: translateY(-10vh) translateZ(0);
  }
}
@-webkit-keyframes stars {
  0% {
    transform: translateY(110vh) translateZ(0);
  }
  100% {
    transform: translateY(-10vh) translateZ(0);
  }
}
@-o-keyframes stars {
  0% {
    transform: translateY(110vh) translateZ(0);
  }
  100% {
    transform: translateY(-10vh) translateZ(0);
  }
}
@keyframes stars {
  0% {
    transform: translateY(110vh) translateZ(0);
  }
  100% {
    transform: translateY(-10vh) translateZ(0);
  }
}
@-moz-keyframes twinkle {
  0%, 80%, 100% {
    opacity: 0.7;
    box-shadow: 0 0 0 #fff, 0 0 0 #fff;
  }
  95% {
    opacity: 1;
    box-shadow: 0 0 2px #fff, 0 0 4px #fff;
  }
}
@-webkit-keyframes twinkle {
  0%, 80%, 100% {
    opacity: 0.7;
    box-shadow: 0 0 0 #fff, 0 0 0 #fff;
  }
  95% {
    opacity: 1;
    box-shadow: 0 0 2px #fff, 0 0 4px #fff;
  }
}
@-o-keyframes twinkle {
  0%, 80%, 100% {
    opacity: 0.7;
    box-shadow: 0 0 0 #fff, 0 0 0 #fff;
  }
  95% {
    opacity: 1;
    box-shadow: 0 0 2px #fff, 0 0 4px #fff;
  }
}
@keyframes twinkle {
  0%, 80%, 100% {
    opacity: 0.7;
    box-shadow: 0 0 0 #fff, 0 0 0 #fff;
  }
  95% {
    opacity: 1;
    box-shadow: 0 0 2px #fff, 0 0 4px #fff;
  }
}
