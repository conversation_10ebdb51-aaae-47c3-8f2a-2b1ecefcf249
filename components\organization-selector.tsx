'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Building } from 'lucide-react';

interface OrganizationSelectorProps {
  onSelect?: (organizationId: string) => void;
  className?: string;
}

export function OrganizationSelector({ onSelect, className }: OrganizationSelectorProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Selecionar Organização</h2>
          <p className="text-muted-foreground">
            Funcionalidade de organização será implementada em breve
          </p>
        </div>
      </div>

      <div className="text-center py-8">
        <Building className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold mb-2">Sistema de Organização em Desenvolvimento</h3>
        <p className="text-muted-foreground mb-4">
          Esta funcionalidade será implementada em uma próxima versão
        </p>
      </div>
    </div>
  );
} 

