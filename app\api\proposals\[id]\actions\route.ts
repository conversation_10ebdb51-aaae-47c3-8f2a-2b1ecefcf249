import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { action } = body;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Proposal ID is required'
        },
        { status: 400 }
      );
    }

    if (!action) {
      return NextResponse.json(
        {
          success: false,
          error: 'Action is required'
        },
        { status: 400 }
      );
    }

    let result;
    let message;

    switch (action) {
      case 'send':
        result = await ProposalService.sendProposal(id);
        message = 'Proposal sent successfully';
        break;

      case 'view':
        const { userId } = body;
        if (!userId) {
          return NextResponse.json(
            {
              success: false,
              error: 'User ID is required for view action'
            },
            { status: 400 }
          );
        }
        result = await ProposalService.markAsViewed(id, userId);
        message = 'Proposal marked as viewed';
        break;

      case 'accept':
        const { acceptedBy } = body;
        if (!acceptedBy) {
          return NextResponse.json(
            {
              success: false,
              error: 'acceptedBy is required for accept action'
            },
            { status: 400 }
          );
        }
        result = await ProposalService.acceptProposal(id, acceptedBy);
        message = 'Proposal accepted successfully';
        break;

      case 'reject':
        const { rejectedBy, reason } = body;
        if (!rejectedBy) {
          return NextResponse.json(
            {
              success: false,
              error: 'rejectedBy is required for reject action'
            },
            { status: 400 }
          );
        }
        result = await ProposalService.rejectProposal(id, rejectedBy, reason);
        message = 'Proposal rejected successfully';
        break;

      case 'counter':
        const { userId: counterUserId, userType, changes, counterMessage } = body;
        if (!counterUserId || !userType || !changes) {
          return NextResponse.json(
            {
              success: false,
              error: 'userId, userType, and changes are required for counter action'
            },
            { status: 400 }
          );
        }
        result = await ProposalService.createCounterProposal(
          id,
          counterUserId,
          userType,
          changes,
          counterMessage
        );
        message = 'Counter proposal created successfully';
        break;

      case 'message':
        const { senderId, senderType, messageText, attachments } = body;
        if (!senderId || !senderType || !messageText) {
          return NextResponse.json(
            {
              success: false,
              error: 'senderId, senderType, and messageText are required for message action'
            },
            { status: 400 }
          );
        }
        result = await ProposalService.addMessage(
          id,
          senderId,
          senderType,
          messageText,
          attachments
        );
        message = 'Message added successfully';
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            error: `Unknown action: ${action}`
          },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      success: true,
      message,
      data: result
    });
  } catch (error) {
    console.error('Error performing proposal action:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to perform proposal action'
      },
      { status: 500 }
    );
  }
}