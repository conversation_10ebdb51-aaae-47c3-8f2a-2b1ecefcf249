@tailwind base;
@tailwind components;
@tailwind utilities;

/* Importar estilos de scrollbar customizada */
@import '../styles/custom-scrollbar.css';

/* Override para garantir tamanhos de fonte customizados */
.text-xs {
  font-size: 0.6875rem !important; /* 11px */
  line-height: 1rem !important; /* 16px */
}

.text-sm {
  font-size: 0.8125rem !important; /* 13px */
  line-height: 1.25rem !important; /* 20px */
}

/* Responsivo: Em telas xl, text-sm fica com tamanho xs */
@media (min-width: 1280px) {
  .text-sm {
    font-size: 0.6875rem !important; /* 11px - mesmo tamanho do xs */
    line-height: 1rem !important; /* 16px - mesmo line-height do xs */
  }
}

body {
  font-family: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

/* Garantir que emojis de bandeiras sejam exibidos corretamente */
.country-flag-emoji {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "Twemoji Mozilla", Arial, sans-serif !important;
  font-size: 1rem !important;
  font-style: normal !important;
  font-weight: normal !important;
  line-height: 1 !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-variant-emoji: emoji !important;
  unicode-bidi: isolate !important;
  word-spacing: normal !important;
  letter-spacing: normal !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 0 0% 60%;
    --chart-2: 0 0% 40%;
    --chart-3: 0 0% 20%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 270 41% 3%;
    --foreground: 0 0% 98%;
    --card: 270 41% 3%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 270 28% 12%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 0 0% 60%;
    --chart-2: 0 0% 40%;
    --chart-3: 0 0% 20%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html, body {
    @apply text-foreground;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  body:not([data-login-page="true"]) {
    @apply bg-background;
  }
  
  body {
    margin: 0;
    padding: 0;
  }
  
  /* Prevenir overflow horizontal em todos os elementos */
 
  
  /* Container principal da página */
  #__next {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  /* Estilos para autofill - adapta aos temas */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px hsl(var(--input)) inset !important;
    -webkit-text-fill-color: hsl(var(--foreground)) !important;
    transition: background-color 5000s ease-in-out 0s;
  }

  /* Estilos para react-international-phone */
  .react-international-phone-input-container {
    font-family: inherit !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
  }

  .react-international-phone-input {
    flex: 1 !important;
    min-width: 0 !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    color: inherit !important;
    font-size: 14px !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .react-international-phone-country-selector {
    border: none !important;
    background: transparent !important;
    padding: 0 8px 0 0 !important;
    margin: 0 !important;
    outline: none !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
  }

  .react-international-phone-country-selector:hover {
    background-color: hsl(var(--muted)) !important;
    border-radius: 4px !important;
  }

  .react-international-phone-country-selector-arrow {
    width: 0 !important;
    height: 0 !important;
    border-left: 3px solid transparent !important;
    border-right: 3px solid transparent !important;
    border-top: 4px solid hsl(var(--foreground)) !important;
    opacity: 0.6 !important;
    margin-left: 4px !important;
  }

  .react-international-phone-flag-emoji {
    font-size: 16px !important;
    line-height: 1 !important;
  }

  /* Customização do dropdown de países */
  .react-international-phone-dropdown {
    background-color: hsl(var(--popover)) !important;
    border: 1px solid hsl(var(--border)) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;
    z-index: 50 !important;
  }

  .react-international-phone-dropdown-item {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--foreground)) !important;
    padding: 8px 12px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  .react-international-phone-dropdown-item:hover {
    background-color: hsl(var(--muted)) !important;
  }

  /* Dark mode adjustments */
  @media (prefers-color-scheme: dark) {
    .react-international-phone-dropdown {
      background-color: hsl(var(--popover)) !important;
      border-color: hsl(var(--border)) !important;
    }
    
    .react-international-phone-dropdown-item {
      background-color: hsl(var(--popover)) !important;
      color: hsl(var(--foreground)) !important;
    }
  }

  /* Ajustes para o tema escuro usando data-theme */
  [data-theme="dark"] .react-international-phone-dropdown {
    background-color: hsl(var(--popover)) !important;
    border-color: hsl(var(--border)) !important;
  }
  
  [data-theme="dark"] .react-international-phone-dropdown-item {
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--foreground)) !important;
  }
}

/* Animações customizadas para o gradient glow */
@keyframes border-flow {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.3;
  }
  25% {
    background-position: 100% 50%;
    opacity: 0.6;
  }
  50% {
    background-position: 200% 50%;
    opacity: 0.4;
  }
  75% {
    background-position: 300% 50%;
    opacity: 0.7;
  }
}

@keyframes blur-animation {
  to {
    filter: blur(3vmin);
    transform: scale(1.05);
  }
}

/* Faster gradient animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@layer utilities {
  .animate-border-flow {
    background-size: 200% 200%;
    animation: border-flow 4s ease-in-out infinite;
  }
  
  .animate-blur-glow {
    --size: 10px;
    position: absolute;
    top: calc(var(--size) / -2);
    left: calc(var(--size) / -2);
    width: calc(100% + var(--size));
    height: calc(100% + var(--size));
    background: radial-gradient(circle at 0 0, hsl(340, 100%, 50%), transparent),
      radial-gradient(circle at 100% 0, #6500ff, transparent),
      radial-gradient(circle at 0 100%, #ff0056, transparent),
      radial-gradient(circle at 100% 100%, #6500ff, transparent);
    filter: blur(8vmin);
    transform: scale(1.05);
    animation: blur-animation 3s ease-in-out infinite alternate;
  }

  .animate-gradient-shift {
    background: linear-gradient(45deg, #ff6b6b00, #4ecdc500, #4a1c80, #96e6a100);
    background-size: 200% 200%;
    animation: gradient-shift 10.5s ease-in-out infinite;
  }
}

/* Scrollbar personalizada para DataTable - Modo Claro */
.scrollbar-custom::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #f3f4f6 !important;
  border-radius: 3px !important;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d1d5db 0%, #9ca3af 100%) !important;
  border-radius: 3px !important;
  border: 1px solid #f3f4f6 !important;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%) !important;
}

/* Scrollbar personalizada para DataTable - Modo Escuro */
.dark .scrollbar-custom::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
}

.dark .scrollbar-custom::-webkit-scrollbar-track {
  background: #1f1f23 !important;
  border-radius: 3px !important;
}

.dark .scrollbar-custom::-webkit-scrollbar-thumb {
  background: #08050f !important; /* Cor específica solicitada */
  border-radius: 3px !important;
  border: 1px solid #1f1f23 !important;
}

.dark .scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: #120b1a !important; /* Tom mais claro no hover */
}

.dark .scrollbar-custom::-webkit-scrollbar-corner {
  background: #1f1f23 !important;
}

/* Firefox - scrollbar customizada */
.scrollbar-custom {
  scrollbar-width: thin !important;
  scrollbar-color: #18fa10 #f3f4f6 !important;
}

.dark .scrollbar-custom {
  scrollbar-width: thin !important;
  scrollbar-color: #ffffff #747474 !important;
}

/* Força a cor específica para o modo escuro */
[data-theme="dark"] .scrollbar-custom::-webkit-scrollbar-thumb,
.dark .scrollbar-custom::-webkit-scrollbar-thumb {
  background: #08050f !important;
  background-color: #08050f !important;
}

[data-theme="dark"] .scrollbar-custom::-webkit-scrollbar-thumb:hover,
.dark .scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: #120b1a !important;
  background-color: #120b1a !important;
}

/* Estilos inline para maior especificidade */
.scrollbar-custom[style*="overflow"] {
  scrollbar-color: #d1d5db #f3f4f6 !important;
}

.dark .scrollbar-custom[style*="overflow"] {
  scrollbar-color: #ff0074 #18fa10 !important;
}

/* Prevenir overflow horizontal em containers específicos */
@layer utilities {
  .container-safe {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  /* Grids responsivos seguros */
  .grid-safe {
    display: grid;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  /* Flex containers seguros */
  .flex-safe {
    display: flex;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
  
  /* Tabelas responsivas */
  .table-container {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Prevenção de overflow em elementos inline */
  .text-safe {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
  }
  
  /* Prevenir overflow em qualquer elemento */
  .no-overflow {
    max-width: 100% !important;
    overflow-x: hidden !important;
  }
  
  /* Para páginas completas */
  .page-container {
    width: 100vw;
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
  }
  

}


