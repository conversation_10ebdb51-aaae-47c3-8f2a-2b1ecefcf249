"use client";

import { useState, useEffect } from 'react';
import { useUser, useOrganization } from '@clerk/nextjs';
import { useProposalAccess } from '@/hooks/use-proposal-access';
import { useModalCleanup } from '@/hooks/use-modal-cleanup';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Users, 
  UserPlus, 
  Mail, 
  Shield, 
  Eye, 
  Pencil, 
  Crown,
  Trash2,
  Search,
  Copy
} from 'lucide-react';

export type ProposalRole = 'a' | 'e' | 'v'; // ag<PERSON><PERSON>, editor, viewer

interface ProposalInviteManagerProps {
  proposalId?: string;
  proposalName?: string;
  currentUserRole?: ProposalRole;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface OrgMember {
  id: string;
  userId: string;
  identifier: string;
  emailAddress: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  role: string;
  hasProposalAccess?: boolean;
  proposalRole?: ProposalRole;
}

export function ProposalInviteManager({ 
  proposalId, 
  proposalName, 
  currentUserRole = 'a',
  open: externalOpen,
  onOpenChange: externalOnOpenChange
}: ProposalInviteManagerProps) {
  const { user: currentUser } = useUser();
  const { organization, memberships, isLoaded: orgIsLoaded, invitations } = useOrganization({
    memberships: {
      infinite: true,
      keepPreviousData: true,
    }
  });
  const { hasAccess, addProposalAccess, removeProposalAccess, updateProposalRole } = useProposalAccess();
  const { toast } = useToast();
  const { onModalClose } = useModalCleanup();
  
  const [orgMembers, setOrgMembers] = useState<OrgMember[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // Função para obter nome do role
  const getRoleName = (role: ProposalRole) => {
    switch (role) {
      case 'a': return 'Agência';
      case 'e': return 'Editor';
      case 'v': return 'Viewer';
      default: return 'Sem acesso';
    }
  };

  // Função para obter cor do role
  const getRoleColor = (role: ProposalRole) => {
    switch (role) {
      case 'a': return 'bg-red-100 text-red-800 border-red-200';
      case 'e': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'v': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Função para obter ícone do role
  const getRoleIcon = (role: ProposalRole) => {
    switch (role) {
      case 'a': return <Crown className="h-3 w-3" />;
      case 'e': return <Pencil className="h-3 w-3" />;
      case 'v': return <Eye className="h-3 w-3" />;
      default: return null;
    }
  };

  // Função para limpeza completa do modal
  const cleanupModal = () => {
    // Limpar dados do componente
    setOrgMembers([]);
    setSearchTerm('');
    setLoading(false);

    // Aguardar um pouco para garantir que a animação de fechamento termine
    setTimeout(() => {
      // Limpar estilos do body
      document.body.style.pointerEvents = '';
      document.body.style.overflow = '';
      document.body.classList.remove('overflow-hidden');

      // Remover overlays órfãos específicos do Radix Dialog
      const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
      overlays.forEach(overlay => {
        const dialogContent = overlay.parentElement?.querySelector('[data-radix-dialog-content]');
        if (!dialogContent || dialogContent.getAttribute('data-state') !== 'open') {
          overlay.remove();
        }
      });

      // Remover portais vazios
      const portals = document.querySelectorAll('[data-radix-portal]');
      portals.forEach(portal => {
        if (!portal.hasChildNodes() || !portal.querySelector('[data-state="open"]')) {
          portal.remove();
        }
      });

      // Forçar reflow para garantir que mudanças sejam aplicadas
      document.body.offsetHeight;
    }, 350);
  };

  // Determinar o estado atual do dialog (controlado externamente ou interno)
  const isDialogOpen = externalOpen !== undefined ? externalOpen : isOpen;
  const setDialogOpen = (open: boolean) => {
    if (externalOnOpenChange) {
      externalOnOpenChange(open);
    } else {
      setIsOpen(open);
    }

    // Se está fechando o modal, executar limpeza
    if (!open) {
      cleanupModal();
      onModalClose(); // Usar hook de limpeza
    }
  };

  // Limpar dados quando dialog for fechado
  useEffect(() => {
    if (!isDialogOpen) {
      setOrgMembers([]);
      setSearchTerm('');
      setLoading(false);

      // Garantir limpeza completa do overlay após fechamento
      setTimeout(() => {
        // Limpar estilos do body que podem estar persistindo
        document.body.style.pointerEvents = '';
        document.body.style.overflow = '';
        document.body.classList.remove('overflow-hidden');

        // Remover qualquer overlay órfão do modal "Gerenciar Acesso"
        const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
        overlays.forEach(overlay => {
          const parentDialog = overlay.closest('[data-radix-dialog-content]');
          if (!parentDialog || parentDialog.getAttribute('data-state') !== 'open') {
            overlay.remove();
          }
        });

        // Remover elementos de portal órfãos
        const portals = document.querySelectorAll('[data-radix-portal]');
        portals.forEach(portal => {
          if (!portal.querySelector('[data-state="open"]')) {
            portal.remove();
          }
        });
      }, 300); // Aguardar animação de fechamento
    }
  }, [isDialogOpen]);

  // Carregar membros da organização
  useEffect(() => {
    if (isDialogOpen && orgIsLoaded && organization && memberships && proposalId && !loading) {
      console.log('🔄 [INVITE_MANAGER] useEffect executado:', {
        isDialogOpen,
        orgIsLoaded,
        hasOrganization: !!organization,
        hasMemberships: !!memberships,
        membershipsDataLength: memberships?.data?.length,
        proposalId,
        currentMembersLength: orgMembers.length
      });
      
      // Só carregar se ainda não temos membros
      if (orgMembers.length === 0) {
        loadOrgMembers();
      }
    }
  }, [isDialogOpen, orgIsLoaded, organization?.id, memberships?.data?.length, memberships?.hasNextPage, proposalId]); // ✅ Usar IDs estáveis e aguardar Clerk carregar

  const loadOrgMembers = async () => {
    console.log('🚀 [INVITE_MANAGER] Iniciando loadOrgMembers...', {
      hasMemberships: !!memberships,
      membershipsData: memberships?.data?.length,
      membershipsCount: memberships?.count,
      organizationId: organization?.id,
      organizationMembersCount: organization?.membersCount,
      proposalId
    });
    
    setLoading(true);
    try {
      // Carregar TODOS os memberships se necessário
      while (memberships && memberships.hasNextPage && !memberships.isLoading) {
        console.log('📥 [INVITE_MANAGER] Carregando próxima página de memberships...', {
          currentCount: memberships.data?.length,
          hasNextPage: memberships.hasNextPage,
          totalExpected: organization?.membersCount
        });
        
        await memberships.fetchNext();
        
        // Aguardar um pouco para o Clerk processar
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Evitar loop infinito
        if (memberships.data && memberships.data.length >= (organization?.membersCount || 0)) {
          console.log('✅ [INVITE_MANAGER] Todos os membros carregados!');
          break;
        }
      }

      // Verificar se há memberships válidos
      if (!memberships?.data || memberships.data.length === 0) {
        console.warn('⚠️ [INVITE_MANAGER] Nenhum membership encontrado', {
          memberships: memberships,
          hasData: !!memberships?.data,
          dataLength: memberships?.data?.length,
          organizationMembersCount: organization?.membersCount,
          hasNextPage: memberships?.hasNextPage,
          isLoading: memberships?.isLoading
        });
        
        // Se a organização tem membros mas não conseguimos carregar, mostrar aviso
        if (organization?.membersCount && organization.membersCount > 0) {
          toast({
            title: "Aviso",
            description: `A organização tem ${organization.membersCount} membro(s), mas não foi possível carregá-los. Verifique as permissões.`,
            variant: "default"
          });
        }
        
        setOrgMembers([]);
        return;
      }

      console.log('📋 [INVITE_MANAGER] Memberships encontrados:', {
        total: memberships.data.length,
        hasNextPage: memberships.hasNextPage,
        isLoading: memberships.isLoading,
        memberships: memberships.data.map(m => ({
          id: m.id,
          role: m.role,
          hasPublicUserData: !!m.publicUserData,
          userId: m.publicUserData?.userId,
          identifier: m.publicUserData?.identifier,
          firstName: m.publicUserData?.firstName,
          lastName: m.publicUserData?.lastName
        }))
      });

      // Converter memberships para formato local
      const members: OrgMember[] = memberships.data
        .filter(membership => {
          const hasValidData = membership.publicUserData?.userId;
          if (!hasValidData) {
            console.warn('⚠️ [INVITE_MANAGER] Membro sem userId válido:', membership);
          }
          return hasValidData;
        })
        .map(membership => ({
          id: membership.id,
          userId: membership.publicUserData!.userId!, // Usar ! pois já filtramos acima
          identifier: membership.publicUserData!.identifier,
          emailAddress: membership.publicUserData!.identifier,
          firstName: membership.publicUserData!.firstName || '',
          lastName: membership.publicUserData!.lastName || '',
          imageUrl: membership.publicUserData!.imageUrl || '',
          role: membership.role,
          hasProposalAccess: false,
          proposalRole: undefined
        }));

      console.log('👥 [INVITE_MANAGER] Membros processados:', {
        total: members.length,
        organizationExpectedCount: organization?.membersCount,
        members: members.map(m => ({
          userId: m.userId,
          name: `${m.firstName} ${m.lastName}`.trim(),
          email: m.emailAddress,
          role: m.role
        }))
      });

      // Verificar quais membros já têm acesso à proposta
      const membersWithAccess = await Promise.all(
        members.map(async (member) => {
          try {
            // Buscar metadata do usuário para verificar acesso
            const hasAccess = await checkUserProposalAccess(member.userId);
            return {
              ...member,
              hasProposalAccess: !!hasAccess,
              proposalRole: hasAccess || undefined
            };
          } catch (error) {
            console.error('Erro ao verificar acesso do usuário:', member.userId, error);
            return member;
          }
        })
      );

      setOrgMembers(membersWithAccess);
      console.log('✅ [INVITE_MANAGER] Membros carregados:', membersWithAccess);

    } catch (error) {
      console.error('❌ [INVITE_MANAGER] Erro ao carregar membros:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os membros da organização.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Função para verificar acesso à proposta
  const checkUserProposalAccess = async (userId: string): Promise<ProposalRole | null> => {
    if (!proposalId) return null;
    
    try {
      // Para o usuário atual, usar o hook diretamente
      if (userId === currentUser?.id) {
        const accessInfo = hasAccess(proposalId);
        return accessInfo?.role || null;
      }
      
      // Para outros usuários, usar a API para verificar acesso
      if (currentUserRole === 'a') {
        const response = await fetch(`/api/proposals/${proposalId}/access/check?userId=${userId}`);
        
        if (!response.ok) {
          console.warn(`Não foi possível verificar acesso para ${userId}:`, response.status);
          return null;
        }
        
        const data = await response.json();
        return data.hasAccess ? data.role : null;
      }
      
      // Se não é admin, assumir que não tem acesso para outros usuários
      return null;
    } catch (error) {
      console.error('Erro ao verificar acesso:', error);
      return null;
    }
  };

  // Função para adicionar usuário à proposta
  const addUserToProposal = async (userId: string, role: ProposalRole) => {
    if (currentUserRole !== 'a') {
      toast({
        title: "Sem permissão",
        description: "Apenas admins podem adicionar usuários à proposta.",
        variant: "destructive"
      });
      return;
    }

    if (!proposalId) {
      toast({
        title: "Erro",
        description: "ID da proposta não encontrado.",
        variant: "destructive"
      });
      return;
    }

    try {
      console.log(`🚀 [INVITE_MANAGER] Adicionando usuário ${userId} à proposta ${proposalId} com role ${role}`);
      
      // Chamar API para adicionar acesso
      const response = await fetch(`/api/proposals/${proposalId}/access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: userId,
          role: role
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao adicionar usuário');
      }

      console.log(`✅ [INVITE_MANAGER] Usuário adicionado com sucesso:`, data);
      
      // Atualizar estado local
      setOrgMembers(members => 
        members.map(member => 
          member.userId === userId 
            ? { ...member, hasProposalAccess: true, proposalRole: role }
            : member
        )
      );
      
      toast({
        title: "Sucesso",
        description: `Usuário adicionado à proposta como ${getRoleName(role)}.`,
        variant: "default"
      });

    } catch (error: any) {
      console.error('❌ [INVITE_MANAGER] Erro ao adicionar usuário:', error);
      toast({
        title: "Erro",
        description: error.message || "Não foi possível adicionar o usuário à proposta.",
        variant: "destructive"
      });
    }
  };

  // Função para remover usuário da proposta
  const removeUserFromProposal = async (userId: string) => {
    if (currentUserRole !== 'a') {
      toast({
        title: "Sem permissão",
        description: "Apenas admins podem remover usuários da proposta.",
        variant: "destructive"
      });
      return;
    }

    if (!proposalId) return;

    try {
      console.log(`🚀 [INVITE_MANAGER] Removendo usuário ${userId} da proposta ${proposalId}`);
      
      // Chamar API para remover acesso
      const response = await fetch(`/api/proposals/${proposalId}/access`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: userId
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao remover usuário');
      }

      console.log(`✅ [INVITE_MANAGER] Usuário removido com sucesso:`, data);
      
      // Atualizar estado local
      setOrgMembers(members => 
        members.map(member => 
          member.userId === userId 
            ? { ...member, hasProposalAccess: false, proposalRole: undefined }
            : member
        )
      );

      toast({
        title: "Sucesso",
        description: "Usuário removido da proposta com sucesso.",
        variant: "default"
      });

    } catch (error: any) {
      console.error('❌ [INVITE_MANAGER] Erro ao remover usuário:', error);
      toast({
        title: "Erro",
        description: error.message || "Não foi possível remover o usuário da proposta.",
        variant: "destructive"
      });
    }
  };

  // Função para alterar role do usuário
  const changeUserRole = async (userId: string, newRole: ProposalRole) => {
    if (currentUserRole !== 'a') {
      toast({
        title: "Sem permissão",
        description: "Apenas agências podem alterar roles de usuários.",
        variant: "destructive"
      });
      return;
    }

    if (!proposalId) return;

    try {
      console.log(`🚀 [INVITE_MANAGER] Alterando role do usuário ${userId} na proposta ${proposalId} para ${newRole}`);
      
      // Chamar API para alterar role
      const response = await fetch(`/api/proposals/${proposalId}/access`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: userId,
          newRole: newRole
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao alterar role');
      }

      console.log(`✅ [INVITE_MANAGER] Role alterado com sucesso:`, data);
      
      // Atualizar estado local
      setOrgMembers(members => 
        members.map(member => 
          member.userId === userId 
            ? { ...member, proposalRole: newRole }
            : member
        )
      );

      toast({
        title: "Sucesso",
        description: `Role alterado para ${getRoleName(newRole)} com sucesso.`,
        variant: "default"
      });

    } catch (error: any) {
      console.error('❌ [INVITE_MANAGER] Erro ao alterar role:', error);
      toast({
        title: "Erro",
        description: error.message || "Não foi possível alterar o role do usuário.",
        variant: "destructive"
      });
    }
  };

  // Filtrar membros baseado na busca
  const filteredMembers = orgMembers.filter(member => 
    member.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.emailAddress.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Separar membros com e sem acesso
  const membersWithAccess = filteredMembers.filter(m => m.hasProposalAccess);
  const membersWithoutAccess = filteredMembers.filter(m => !m.hasProposalAccess);

  // Se não há proposalId, não renderizar o componente
  if (!proposalId || !proposalName) {
    return null;
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setDialogOpen}>
      {/* Só renderizar DialogTrigger se não estiver sendo controlado externamente */}
      {externalOpen === undefined && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Users className="h-4 w-4" />
            Gerenciar Acesso
          </Button>
        </DialogTrigger>
      )}
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-[#ff0074]" />
            Gerenciar Acesso - {proposalName}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto space-y-6">
          {/* Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar membros por nome ou email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff0074]"></div>
              <span className="ml-2">Carregando membros...</span>
            </div>
          ) : !orgIsLoaded ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff0074]"></div>
              <span className="ml-2">Carregando organização...</span>
            </div>
          ) : (
            <>
              {/* Membros com acesso */}
              {membersWithAccess.length > 0 && (
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-3 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Com Acesso ({membersWithAccess.length})
                  </h3>
                  <div className="space-y-2">
                    {membersWithAccess.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.imageUrl} />
                            <AvatarFallback>
                              {(member.firstName?.charAt(0) || member.emailAddress.charAt(0)).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-sm">
                              {member.firstName} {member.lastName}
                            </p>
                            <p className="text-xs text-muted-foreground">{member.emailAddress}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {member.proposalRole && (
                            <>
                              <Badge variant="outline" className={getRoleColor(member.proposalRole)}>
                                {getRoleIcon(member.proposalRole)}
                                <span className="ml-1">{getRoleName(member.proposalRole)}</span>
                              </Badge>

                              {currentUserRole === 'a' && member.userId !== currentUser?.id && (
                                <>
                                  <Select
                                    value={member.proposalRole}
                                    onValueChange={(value: ProposalRole) => changeUserRole(member.userId, value)}
                                  >
                                    <SelectTrigger className="w-24 h-8">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="a">Agência</SelectItem>
                                      <SelectItem value="e">Editor</SelectItem>
                                      <SelectItem value="v">Viewer</SelectItem>
                                    </SelectContent>
                                  </Select>

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeUserFromProposal(member.userId)}
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Separador */}
              {membersWithAccess.length > 0 && membersWithoutAccess.length > 0 && (
                <Separator />
              )}

              {/* Membros sem acesso */}
              {membersWithoutAccess.length > 0 && (
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-3 flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    Adicionar Membros ({membersWithoutAccess.length})
                  </h3>
                  <div className="space-y-2">
                    {membersWithoutAccess.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/20 transition-colors">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.imageUrl} />
                            <AvatarFallback>
                              {(member.firstName?.charAt(0) || member.emailAddress.charAt(0)).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-sm">
                              {member.firstName} {member.lastName}
                            </p>
                            <p className="text-xs text-muted-foreground">{member.emailAddress}</p>
                          </div>
                        </div>
                        
                        {currentUserRole === 'a' && (
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => addUserToProposal(member.userId, 'v')}
                              className="text-xs"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Viewer
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => addUserToProposal(member.userId, 'e')}
                              className="text-xs"
                            >
                              <Pencil className="h-3 w-3 mr-1" />
                              Editor
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => addUserToProposal(member.userId, 'a')}
                              className="text-xs"
                            >
                              <Crown className="h-3 w-3 mr-1" />
                              Agência
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {filteredMembers.length === 0 && !loading && orgIsLoaded && (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Nenhum membro encontrado</p>
                  {searchTerm && (
                    <p className="text-sm">Tente uma busca diferente</p>
                  )}
                  {!searchTerm && orgMembers.length === 0 && (
                    <div className="mt-2">
                      <p className="text-sm">Esta organização não possui membros ou</p>
                      <p className="text-sm">você não tem permissão para visualizá-los.</p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 