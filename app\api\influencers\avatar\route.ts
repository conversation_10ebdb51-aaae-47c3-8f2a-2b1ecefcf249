import { NextRequest, NextResponse } from 'next/server';
import { getInfluencerAvatarUrl, uploadInfluencerAvatar } from '@/lib/firebase-storage';
import { updateInfluencer } from '@/lib/firebase';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const influencerId = searchParams.get('influencerId');
    const avatarPath = searchParams.get('avatarPath');

    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    const avatarUrl = await getInfluencerAvatarUrl(influencerId, avatarPath || undefined);
    
    return NextResponse.json({ avatarUrl });
  } catch (error) {
    console.error('Erro ao buscar URL do avatar:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST: Upload de avatar para Firebase Storage
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [API] Iniciando upload de avatar de influenciador');
    
    const formData = await request.formData();
    const file = formData.get('avatar') as File;
    const influencerId = formData.get('influencerId') as string;

    // Validar dados
    if (!file) {
      return NextResponse.json(
        { error: 'Arquivo de avatar é obrigatório' },
        { status: 400 }
      );
    }

    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    // Validar tipo de arquivo
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Arquivo deve ser uma imagem' },
        { status: 400 }
      );
    }

    // Validar tamanho do arquivo (máximo 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Arquivo muito grande. Máximo 5MB.' },
        { status: 400 }
      );
    }

    console.log('📤 [API] Fazendo upload para Firebase Storage...');
    
    // Upload para Firebase Storage
    const avatarUrl = await uploadInfluencerAvatar(file, influencerId);
    
    console.log('✅ [API] Upload realizado:', avatarUrl);
    
    // Só atualizar o Firestore se não for um ID temporário
    if (!influencerId.startsWith('temp_')) {
      console.log('📝 [API] Atualizando influenciador no Firestore...');
      
      try {
        await updateInfluencer(influencerId, { avatar: avatarUrl });
        console.log('✅ [API] Avatar atualizado no banco de dados');
      } catch (error) {
        console.warn('⚠️ [API] Não foi possível atualizar o Firestore (documento pode não existir ainda):', error);
        // Não falhar a operação - o upload foi bem-sucedido
      }
    } else {
      console.log('ℹ️ [API] ID temporário detectado - avatar salvo apenas no Storage (será vinculado ao criar o influenciador)');
    }

    return NextResponse.json({ 
      success: true, 
      avatarUrl,
      message: 'Avatar enviado com sucesso!' 
    });

  } catch (error) {
    console.error('❌ [API] Erro ao fazer upload do avatar:', error);
    return NextResponse.json(
      { 
        error: 'Erro ao fazer upload do avatar',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

