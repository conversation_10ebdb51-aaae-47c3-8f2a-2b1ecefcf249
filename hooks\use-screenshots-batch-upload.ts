import { useState, useCallback } from 'react'
import { useToast } from '@/components/ui/use-toast'

interface PendingFile {
  file: File
  platform: string
  preview: string
  id: string
}

interface UploadResult {
  url: string
  filename: string
  platform: string
  size: number
  contentType: string
}

interface UploadError {
  platform: string
  filename: string
  error: string
}

interface BatchUploadResult {
  success: boolean
  totalUploaded: number
  totalFiles: number
  results: UploadResult[]
  errors: UploadError[]
}

/**
 * Hook para upload em lote de screenshots usando GraphQL otimizado
 * Implementa as melhores práticas do Next.js com Context7
 * 
 * Exemplo de uso:
 * ```tsx
 * const { uploadScreenshots, uploading, progress } = useScreenshotsBatchUpload()
 * 
 * const handleUpload = async () => {
 *   const pendingFiles = [
 *     { file: file1, platform: 'instagram', preview: '...', id: '1' },
 *     { file: file2, platform: 'tiktok', preview: '...', id: '2' }
 *   ]
 *   
 *   const result = await uploadScreenshots('influencer-id', pendingFiles)
 *   if (result.success) {
 *     console.log('Upload concluído via GraphQL!', result)
 *   }
 * }
 * ```
 */
export function useScreenshotsBatchUpload() {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const { toast } = useToast()

  const uploadScreenshots = useCallback(
    async (influencerId: string, pendingFiles: PendingFile[]): Promise<any> => {
      if (!influencerId || !pendingFiles || pendingFiles.length === 0) {
        throw new Error('ID do influenciador e arquivos são obrigatórios')
      }

      setUploading(true)
      setProgress(0)

      try {
        console.log(`📸 [GRAPHQL-BATCH] Iniciando upload GraphQL de ${pendingFiles.length} screenshots`)

        // Converter arquivos para base64 para o GraphQL
        const screenshots = await Promise.all(
          pendingFiles.map(async (pendingFile) => {
            return new Promise<any>((resolve, reject) => {
              const reader = new FileReader()
              reader.onload = () => {
                resolve({
                  platform: pendingFile.platform,
                  filename: pendingFile.file.name,
                  fileData: reader.result as string,
                  contentType: pendingFile.file.type,
                  size: pendingFile.file.size
                })
              }
              reader.onerror = reject
              reader.readAsDataURL(pendingFile.file)
            })
          })
        )

        setProgress(30) // Conversão concluída

        console.log(`📸 [GRAPHQL-BATCH] Enviando ${screenshots.length} arquivos via GraphQL mutation`)

        // Fazer mutation GraphQL
        const response = await fetch('/api/graphql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: `
              mutation UploadScreenshotsBatch($input: UploadScreenshotsBatchInput!) {
                uploadScreenshotsBatch(input: $input) {
                  success
                  totalUploaded
                  results {
                    id
                    influencerId
                    platform
                    url
                    filename
                    size
                    contentType
                    uploadedAt
                    uploadedBy
                  }
                  errors {
                    platform
                    filename
                    error
                  }
                }
              }
            `,
            variables: {
              input: {
                influencerId,
                screenshots
              }
            }
          })
        })

        setProgress(90) // GraphQL processado

        if (!response.ok) {
          throw new Error(`Erro HTTP: ${response.status}`)
        }

        const result = await response.json()

        if (result.errors) {
          throw new Error(result.errors[0]?.message || 'Erro GraphQL desconhecido')
        }

        setProgress(100)

        const batchResult = result.data?.uploadScreenshotsBatch

        console.log(`✅ [GRAPHQL-BATCH] Upload GraphQL concluído:`, batchResult)

        // Exibir toast com resultado
        if (batchResult.errors && batchResult.errors.length > 0) {
          toast({
            title: "Upload parcialmente concluído",
            description: `${batchResult.totalUploaded} de ${screenshots.length} screenshot(s) enviado(s). ${batchResult.errors.length} falhou(aram).`,
            variant: "default"
          })
        } else {
          toast({
            title: "Screenshots enviados",
            description: `${batchResult.totalUploaded} screenshot(s) enviado(s) com sucesso`
          })
        }

        return batchResult

      } catch (error) {
        console.error('❌ [GRAPHQL-BATCH] Erro no upload GraphQL:', error)
        
        toast({
          title: "Erro no upload GraphQL",
          description: error instanceof Error ? error.message : 'Erro desconhecido',
          variant: "destructive"
        })

        throw error
      } finally {
        setUploading(false)
        setProgress(0)
      }
    },
    [toast]
  )

  return {
    uploadScreenshots,
    uploading,
    progress
  }
} 