# 📅 FASE 5: <PERSON><PERSON><PERSON> de Dados (1-2 dias)

## 🎯 Objetivo
Migrar dados existentes adicionando `userId` onde necessário, mantendo integridade.

## 📝 Tarefas Principais

### 5.1 Backup e Preparação

#### Script de Backup
```typescript
// scripts/backup-firestore.ts
import { db } from '@/lib/firebase';

export async function backupCollection(collectionName: string) {
  console.log(`📦 Fazendo backup da coleção: ${collectionName}`);
  
  const snapshot = await db.collection(collectionName).get();
  const data = snapshot.docs.map(doc => ({
    id: doc.id,
    data: doc.data()
  }));
  
  // Salvar backup em arquivo JSON
  const backupData = {
    collection: collectionName,
    timestamp: new Date().toISOString(),
    count: data.length,
    documents: data
  };
  
  const fs = require('fs');
  const filename = `backup-${collectionName}-${Date.now()}.json`;
  fs.writeFileSync(`./backups/${filename}`, JSON.stringify(backupData, null, 2));
  
  console.log(`✅ Backup salvo: ${filename} (${data.length} documentos)`);
  return filename;
}
```

### 5.2 Scripts de Migração

#### Migração de Brands
```typescript
// scripts/migrate-brands.ts
export async function migrateBrands() {
  console.log('🔄 Iniciando migração de brands...');
  
  const snapshot = await db.collection('brands').get();
  const batch = db.batch();
  let migratedCount = 0;
  
  for (const doc of snapshot.docs) {
    const data = doc.data();
    
    // Pular se já tem userId
    if (data.userId) {
      continue;
    }
    
    // Determinar userId baseado em lógica de negócio
    const userId = await determineUserIdForBrand(data);
    
    if (userId) {
      batch.update(doc.ref, {
        userId,
        updatedAt: new Date(),
        migratedAt: new Date()
      });
      migratedCount++;
    }
  }
  
  await batch.commit();
  console.log(`✅ Migração de brands concluída: ${migratedCount} documentos`);
}

async function determineUserIdForBrand(brandData: any): Promise<string | null> {
  // Lógica para determinar o userId:
  // 1. Se tem email de contato, buscar usuário com esse email
  // 2. Se tem ID específico em outros campos
  // 3. Atribuir a um usuário admin padrão como fallback
  
  if (brandData.contactEmail) {
    const userSnapshot = await db.collection('users')
      .where('email', '==', brandData.contactEmail)
      .limit(1)
      .get();
    
    if (!userSnapshot.empty) {
      return userSnapshot.docs[0].id;
    }
  }
  
  // Fallback: atribuir ao primeiro admin
  const adminSnapshot = await db.collection('users')
    .where('role', '==', 'admin')
    .limit(1)
    .get();
    
  if (!adminSnapshot.empty) {
    return adminSnapshot.docs[0].id;
  }
  
  return null;
}
```

#### Migração de Campaigns
```typescript
// scripts/migrate-campaigns.ts
export async function migrateCampaigns() {
  console.log('🔄 Iniciando migração de campaigns...');
  
  const snapshot = await db.collection('campaigns').get();
  const batch = db.batch();
  let migratedCount = 0;
  
  for (const doc of snapshot.docs) {
    const data = doc.data();
    
    if (data.userId) continue;
    
    // Determinar userId através da brand
    let userId = null;
    
    if (data.brandId) {
      const brandDoc = await db.collection('brands').doc(data.brandId).get();
      if (brandDoc.exists && brandDoc.data()?.userId) {
        userId = brandDoc.data()?.userId;
      }
    }
    
    // Fallback: mesmo método das brands
    if (!userId) {
      userId = await determineUserIdForCampaign(data);
    }
    
    if (userId) {
      batch.update(doc.ref, {
        userId,
        updatedAt: new Date(),
        migratedAt: new Date()
      });
      migratedCount++;
    }
  }
  
  await batch.commit();
  console.log(`✅ Migração de campaigns concluída: ${migratedCount} documentos`);
}
```

### 5.3 Script Principal de Migração

#### Orquestrador da Migração
```typescript
// scripts/migrate-all.ts
import { backupCollection } from './backup-firestore';
import { migrateBrands } from './migrate-brands';
import { migrateCampaigns } from './migrate-campaigns';
import { migrateInfluencers } from './migrate-influencers';

async function runMigration() {
  try {
    console.log('🚀 Iniciando migração completa...');
    
    // 1. Fazer backup de todas as coleções
    const collections = ['brands', 'campaigns', 'influencers', 'groups', 'notes'];
    const backupFiles = [];
    
    for (const collection of collections) {
      const backupFile = await backupCollection(collection);
      backupFiles.push(backupFile);
    }
    
    console.log('📦 Backups concluídos:', backupFiles);
    
    // 2. Executar migrações na ordem correta
    await migrateBrands();
    await migrateCampaigns();
    await migrateInfluencers();
    await migrateGroups();
    await migrateNotes();
    
    // 3. Validar integridade
    await validateMigration();
    
    console.log('✅ Migração completa concluída com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro na migração:', error);
    console.log('📋 Execute o rollback se necessário');
    throw error;
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  runMigration().catch(console.error);
}
```

### 5.4 Validação da Migração

#### Script de Validação
```typescript
// scripts/validate-migration.ts
export async function validateMigration() {
  console.log('🔍 Validando migração...');
  
  const collections = ['brands', 'campaigns', 'influencers', 'groups', 'notes'];
  const results = [];
  
  for (const collectionName of collections) {
    const snapshot = await db.collection(collectionName).get();
    const total = snapshot.size;
    const withUserId = snapshot.docs.filter(doc => doc.data().userId).length;
    const withoutUserId = total - withUserId;
    
    results.push({
      collection: collectionName,
      total,
      withUserId,
      withoutUserId,
      migrationComplete: withoutUserId === 0
    });
    
    console.log(`📊 ${collectionName}: ${withUserId}/${total} com userId`);
  }
  
  const allComplete = results.every(r => r.migrationComplete);
  
  if (allComplete) {
    console.log('✅ Migração validada: todos os documentos têm userId');
  } else {
    console.log('⚠️ Migração incompleta: alguns documentos ainda sem userId');
    console.table(results);
  }
  
  return results;
}
```

### 5.5 Script de Rollback

#### Rollback de Segurança
```typescript
// scripts/rollback-migration.ts
export async function rollbackMigration(backupFiles: string[]) {
  console.log('🔄 Iniciando rollback da migração...');
  
  for (const backupFile of backupFiles) {
    console.log(`📤 Restaurando backup: ${backupFile}`);
    
    const fs = require('fs');
    const backupData = JSON.parse(fs.readFileSync(`./backups/${backupFile}`, 'utf8'));
    
    const batch = db.batch();
    
    for (const doc of backupData.documents) {
      const docRef = db.collection(backupData.collection).doc(doc.id);
      batch.set(docRef, doc.data);
    }
    
    await batch.commit();
    console.log(`✅ Backup restaurado: ${backupData.collection}`);
  }
  
  console.log('✅ Rollback concluído');
}
```

## 📊 Deliverables

### 1. Scripts de Backup
- [x] `backup-firestore.ts` - Backup automático
- [x] Armazenamento seguro dos backups
- [x] Validação da integridade dos backups

### 2. Scripts de Migração
- [x] `migrate-brands.ts` - Migração de marcas
- [x] `migrate-campaigns.ts` - Migração de campanhas
- [ ] `migrate-influencers.ts` - Migração de influenciadores
- [ ] `migrate-groups.ts` - Migração de grupos
- [ ] `migrate-notes.ts` - Migração de notas

### 3. Validação e Rollback
- [x] `validate-migration.ts` - Validação da migração
- [x] `rollback-migration.ts` - Rollback de segurança
- [x] Relatórios de integridade

### 4. Orquestração
- [x] `migrate-all.ts` - Script principal
- [x] Ordem correta de execução
- [x] Tratamento de erros

## ✅ Critérios de Aceitação

- [ ] Backup completo de todos os dados
- [ ] Migração preserva integridade referencial
- [ ] 100% dos documentos têm userId válido
- [ ] Relacionamentos mantidos (campaigns → brands)
- [ ] Script de rollback testado e funcionando
- [ ] Relatório de migração detalhado
- [ ] Zero perda de dados

## 🚀 Próxima Fase

Seguir para [Fase 6: Frontend e Hooks](./fase-6-frontend-hooks.md) 