import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

// GET - Buscar contrapropostas de colaboradores para um orçamento específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; budgetId: string }> }
) {
  try {
    const { id: proposalId, budgetId } = await params;
    const { searchParams } = new URL(request.url);
    const influencerId = searchParams.get('influencerId');

    console.log('🔍 [GET] Buscando contrapropostas de colaboradores:', { 
      proposalId, 
      budgetId, 
      influencerId 
    });

    if (!proposalId || !budgetId) {
      return NextResponse.json(
        { success: false, error: 'proposalId e budgetId são obrigatórios' },
        { status: 400 }
      );
    }

    // Buscar contrapropostas de colaboradores na coleção separada
    let query = db.collection('collaboratorCounterProposals')
      .where('proposalId', '==', proposalId)
      .where('budgetId', '==', budgetId);

    // Filtrar por influenciador se especificado
    if (influencerId) {
      query = query.where('influencerId', '==', influencerId);
    }

    const snapshot = await query.get();
    
    const counterProposals = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Ordenar por data de criação (mais recente primeiro)
    counterProposals.sort((a: any, b: any) => {
      const dateA = new Date(a.createdAt?.toDate?.() || a.createdAt || 0).getTime();
      const dateB = new Date(b.createdAt?.toDate?.() || b.createdAt || 0).getTime();
      return dateB - dateA;
    });

    console.log(`✅ Encontradas ${counterProposals.length} contrapropostas de colaboradores`);

    return NextResponse.json({
      success: true,
      data: counterProposals
    });

  } catch (error) {
    console.error('❌ Erro ao buscar contrapropostas de colaboradores:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

// PUT - Atualizar status de uma contraproposta de colaborador (aceitar/rejeitar)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; budgetId: string }> }
) {
  try {
    const { id: proposalId, budgetId } = await params;
    const body = await request.json();
    const { 
      counterProposalId, 
      status, 
      reviewNote, 
      influencerId, 
      reviewedBy 
    } = body;

    console.log('🔄 [PUT] Atualizando contraproposta de colaborador:', { 
      proposalId, 
      budgetId, 
      counterProposalId, 
      status 
    });

    if (!counterProposalId || !status) {
      return NextResponse.json(
        { success: false, error: 'counterProposalId e status são obrigatórios' },
        { status: 400 }
      );
    }

    if (!['accepted', 'rejected'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Status deve ser "accepted" ou "rejected"' },
        { status: 400 }
      );
    }

    // Atualizar a contraproposta na coleção
    const updateData: any = {
      status,
      updatedAt: new Date(),
      reviewedAt: new Date(),
      reviewedBy: reviewedBy || 'current_user'
    };

    if (reviewNote) {
      updateData.reviewNote = reviewNote;
    }

    await db.collection('collaboratorCounterProposals')
      .doc(counterProposalId)
      .update(updateData);

    // Se aceita, atualizar o orçamento original com o novo valor
    if (status === 'accepted') {
      console.log('💰 Aplicando valor aceito ao orçamento original...');
      
      // Buscar a contraproposta para obter o valor proposto
      const counterProposalDoc = await db.collection('collaboratorCounterProposals')
        .doc(counterProposalId)
        .get();
      
      if (counterProposalDoc.exists) {
        const counterProposalData = counterProposalDoc.data();
        const proposedAmount = counterProposalData?.proposedAmount;
        
        if (proposedAmount) {
          // Atualizar o orçamento na estrutura hierárquica
          const budgetRef = db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .doc(influencerId)
            .collection('budgets')
            .doc(budgetId);
          
          await budgetRef.update({
            amount: proposedAmount,
            updatedAt: new Date(),
            lastAcceptedCounterProposal: {
              id: counterProposalId,
              amount: proposedAmount,
              acceptedAt: new Date(),
              acceptedBy: reviewedBy || 'current_user'
            }
          });
          
          console.log('✅ Orçamento atualizado com valor da contraproposta aceita');
        }
      }
    }

    console.log('✅ Contraproposta de colaborador atualizada com sucesso');

    return NextResponse.json({
      success: true,
      message: status === 'accepted' ? 'Valor inicial aceito com sucesso' : 'Contraproposta rejeitada com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar contraproposta de colaborador:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
