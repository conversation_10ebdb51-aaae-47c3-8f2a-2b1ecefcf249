import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const { id: proposalId } = await params;
    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    // Verificar se o usuário atual tem permissão (deve ser admin da proposta)
    const clerk = await clerkClient();
    const currentUser = await clerk.users.getUser(userId);
    const currentUserProposalAccess = (currentUser.unsafeMetadata as any)?.p?.[proposalId];
    
    if (currentUserProposalAccess !== 'a') {
      return NextResponse.json(
        { error: 'Apenas admins podem verificar acesso à proposta' },
        { status: 403 }
      );
    }

    // Obter usuário alvo
    const targetUser = await clerk.users.getUser(targetUserId);
    
    // Verificar acesso à proposta
    const proposalAccess = (targetUser.unsafeMetadata as any)?.p?.[proposalId];

    return NextResponse.json({
      userId: targetUserId,
      proposalId,
      hasAccess: !!proposalAccess,
      role: proposalAccess || null,
      user: {
        firstName: targetUser.firstName,
        lastName: targetUser.lastName,
        emailAddress: targetUser.emailAddresses[0]?.emailAddress,
        imageUrl: targetUser.imageUrl
      }
    });

  } catch (error) {
    console.error('❌ [PROPOSAL_ACCESS_CHECK] Erro ao verificar acesso:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
