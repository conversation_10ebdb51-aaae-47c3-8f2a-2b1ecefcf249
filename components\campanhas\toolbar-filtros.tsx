import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckSquare, Users, FileText, BarChart3, User, Trash2, Search, Calendar, Filter, MapPin, TrendingUp, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';

interface FilterState {
  busca: string;
  pais: string;
  categoria: string;
  genero: string;
  verificado: boolean | null;
  divulgaTrader: boolean | null;
  minSeguidores: number;
  maxSeguidores: number;
}

interface ProposalFilters {
  dataEnvio: string;
  nomeGrupo: string;
  status: string;
}

interface ToolbarFiltrosProps {
  selectionMode: boolean;
  selectedInfluencers: string[];
  viewMode: 'list' | 'card';
  activeTab: 'all' | 'sent' | 'proposals';
  filters: FilterState;
  proposalFilters: ProposalFilters;
  categorias: string[];
  paises: string[];
  generos: string[];
  filteredInfluencers: any[];
  toggleSelectionMode: () => void;
  setGroupInfluencers: (ids: string[]) => void;
  setShowGroupDialog: (show: boolean) => void;
  setProposalInfluencer: (influencer: any) => void;
  setProposalServices: (services: any[]) => void;
  setProposalMessage: (message: string) => void;
  setShowProposalSheet: (show: boolean) => void;
  setViewMode: (mode: 'list' | 'card') => void;
  deleteSelectedFromBrand: () => void;
  setSelectedInfluencers: (ids: string[]) => void;
  setFilters: React.Dispatch<React.SetStateAction<FilterState>>;
  setProposalFilters: React.Dispatch<React.SetStateAction<ProposalFilters>>;
}

export function ToolbarFiltros({
  selectionMode,
  selectedInfluencers,
  viewMode,
  activeTab,
  filters,
  proposalFilters,
  categorias,
  paises,
  generos,
  filteredInfluencers,
  toggleSelectionMode,
  setGroupInfluencers,
  setShowGroupDialog,
  setProposalInfluencer,
  setProposalServices,
  setProposalMessage,
  setShowProposalSheet,
  setViewMode,
  deleteSelectedFromBrand,
  setSelectedInfluencers,
  setFilters,
  setProposalFilters
}: ToolbarFiltrosProps) {
  return (
    <div className="bg-card/50 backdrop-blur-sm rounded-xl p-6 mb-6">
      {/* Primeira Linha - Controles Principais */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
        {/* Controles de Ação */}
        <div className="flex flex-wrap items-center gap-3">
          <Button
            variant={selectionMode ? "default" : "outline"}
            size="sm"
            className={cn(
              "transition-all duration-200 hover:scale-105",
              selectionMode && "bg-[#ff0074] hover:bg-[#ff0074]/90 text-white shadow-md"
            )}
            onClick={toggleSelectionMode}
          >
            <CheckSquare className="h-4 w-4 mr-2" />
            {selectionMode ? 'Cancelar Seleção' : 'Selecionar Múltiplos'}
          </Button>
          
          {/* Botão de Criar Grupo */}
          <Button
            variant="default"
            size="sm"
            className="bg-[#ff0074] hover:bg-[#ff0074]/90 text-white transition-all duration-200 hover:scale-105 shadow-md"
            onClick={() => {
              if (selectedInfluencers.length === 0) {
                toast.error('Selecione pelo menos um influenciador para criar um grupo');
                return;
              }
              setGroupInfluencers(selectedInfluencers);
              setShowGroupDialog(true);
            }}
            disabled={selectedInfluencers.length === 0}
          >
            <Users className="h-4 w-4 mr-2" />
            Criar Grupo ({selectedInfluencers.length})
          </Button>
          
          {/* Botão de Criar Proposta */}
          <Button
            variant="default"
            size="sm"
            className="bg-[#9810fa] hover:bg-[#ff0074]/90 text-white transition-all duration-200 hover:scale-105 shadow-md"
            onClick={() => {
              if (selectedInfluencers.length === 0) {
                toast.error('Selecione pelo menos um influenciador para criar uma proposta');
                return;
              }
              const firstSelected = filteredInfluencers.find(inf => selectedInfluencers.includes(inf.id));
              if (firstSelected) {
                setProposalInfluencer(firstSelected);
                setProposalServices([]);
                setProposalMessage('');
                setShowProposalSheet(true);
              }
            }}
            disabled={selectedInfluencers.length === 0}
          >
            <FileText className="h-4 w-4 mr-2" />
            Criar Proposta Individual
          </Button>
          
          {/* Botões de alternância de visualização */}
          <div className="flex gap-0 bg-muted/50 rounded-lg p-1 border">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('list')}
              className={cn(
                "h-8 px-3 rounded-md transition-all duration-200",
                viewMode === 'list' 
                  ? "bg-[#ff0074] hover:bg-[#ff0074]/90 text-white shadow-sm" 
                  : "hover:bg-background/80"
              )}
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('card')}
              className={cn(
                "h-8 px-3 rounded-md transition-all duration-200",
                viewMode === 'card' 
                  ? "bg-[#ff0074] hover:bg-[#ff0074]/90 text-white shadow-sm" 
                  : "hover:bg-background/80"
              )}
            >
              <User className="h-4 w-4" />
            </Button>
          </div>
          
          {selectionMode && selectedInfluencers.length > 0 && (
            <div className="flex items-center gap-2 animate-in slide-in-from-left-5 duration-300">
              <Button
                variant="destructive"
                size="sm"
                onClick={deleteSelectedFromBrand}
                disabled={activeTab !== 'sent'}
                className="transition-all duration-200 hover:scale-105"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remover da Marca ({selectedInfluencers.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedInfluencers([])}
                className="transition-all duration-200 hover:scale-105"
              >
                Limpar Seleção
              </Button>
            </div>
          )}
        </div>

        {/* Badge de Seleção */}
        {selectionMode && (
          <Badge 
            variant="secondary" 
            className="bg-[#ff0074]/10 text-[#ff0074] border-[#ff0074]/20 animate-in slide-in-from-right-5 duration-300"
          >
            {selectedInfluencers.length} selecionado(s)
          </Badge>
        )}
      </div>

      {/* Segunda Linha - Filtros */}
      <div className="space-y-4">
        {/* Busca Principal */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar por nome, cidade ou categoria..."
            value={filters.busca}
            onChange={(e) => setFilters(prev => ({ ...prev, busca: e.target.value }))}
            className="pl-10 h-10 bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
          />
        </div>

        {/* Grid de Filtros */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3">
          {/* Filtros condicionais baseados na aba ativa */}
          {activeTab === 'proposals' ? (
            <>
              {/* Filtro por Data de Envio - Propostas */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Data de Envio
                </label>
                <Input
                  type="date"
                  value={proposalFilters.dataEnvio}
                  onChange={(e) => setProposalFilters(prev => ({ ...prev, dataEnvio: e.target.value }))}
                  className="h-9 text-sm bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
                />
              </div>

              {/* Filtro por Nome do Grupo - Propostas */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  Nome do Grupo
                </label>
                <Input
                  placeholder="Buscar por grupo..."
                  value={proposalFilters.nomeGrupo}
                  onChange={(e) => setProposalFilters(prev => ({ ...prev, nomeGrupo: e.target.value }))}
                  className="h-9 text-sm bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
                />
              </div>

              {/* Filtro por Status - Propostas */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <Filter className="h-3 w-3" />
                  Status
                </label>
                <Select
                  value={proposalFilters.status}
                  onValueChange={(value) => setProposalFilters(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger className="h-9 text-sm bg-background/50 border-border/50 hover:border-[#ff0074]/50 transition-colors">
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="pending">Pendentes</SelectItem>
                    <SelectItem value="approved">Aprovadas</SelectItem>
                    <SelectItem value="rejected">Rejeitadas</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Botão Limpar Filtros - Propostas */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-transparent">Ações</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setProposalFilters({
                    dataEnvio: '',
                    nomeGrupo: '',
                    status: 'todos'
                  })}
                  className="h-9 w-full text-sm hover:bg-destructive/10 hover:text-destructive hover:border-destructive/50 transition-all duration-200"
                >
                  <X className="h-4 w-4 mr-1" />
                  Limpar
                </Button>
              </div>
            </>
          ) : (
            <>
              {/* Categoria */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <Filter className="h-3 w-3" />
                  Categoria
                </label>
                <Select
                  value={filters.categoria}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, categoria: value }))}
                >
                  <SelectTrigger className="h-9 text-sm bg-background/50 border-border/50 hover:border-[#ff0074]/50 transition-colors">
                    <SelectValue placeholder="Todas" />
                  </SelectTrigger>
                  <SelectContent>
                    {categorias.map(categoria => (
                      <SelectItem key={categoria} value={categoria}>
                        {categoria}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* País */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  País
                </label>
                <Select
                  value={filters.pais}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, pais: value }))}
                >
                  <SelectTrigger className="h-9 text-sm bg-background/50 border-border/50 hover:border-[#ff0074]/50 transition-colors">
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    {paises.map(pais => (
                      <SelectItem key={pais} value={pais}>
                        {pais}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Gênero */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <User className="h-3 w-3" />
                  Gênero
                </label>
                <Select
                  value={filters.genero}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, genero: value }))}
                >
                  <SelectTrigger className="h-9 text-sm bg-background/50 border-border/50 hover:border-[#ff0074]/50 transition-colors">
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    {generos.map(genero => (
                      <SelectItem key={genero} value={genero}>
                        {genero}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Verificado */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <CheckSquare className="h-3 w-3" />
                  Verificado
                </label>
                <Select
                  value={filters.verificado === null ? 'todos' : filters.verificado.toString()}
                  onValueChange={(value) => {
                    if (value === 'todos') {
                      setFilters(prev => ({ ...prev, verificado: null }));
                    } else {
                      setFilters(prev => ({ ...prev, verificado: value === 'true' }));
                    }
                  }}
                >
                  <SelectTrigger className="h-9 text-sm bg-background/50 border-border/50 hover:border-[#ff0074]/50 transition-colors">
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="true">Verificados</SelectItem>
                    <SelectItem value="false">Não Verificados</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Trader */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  Trader
                </label>
                <Select
                  value={filters.divulgaTrader === null ? 'todos' : filters.divulgaTrader.toString()}
                  onValueChange={(value) => {
                    if (value === 'todos') {
                      setFilters(prev => ({ ...prev, divulgaTrader: null }));
                    } else {
                      setFilters(prev => ({ ...prev, divulgaTrader: value === 'true' }));
                    }
                  }}
                >
                  <SelectTrigger className="h-9 text-sm bg-background/50 border-border/50 hover:border-[#ff0074]/50 transition-colors">
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="true">Divulga Trader</SelectItem>
                    <SelectItem value="false">Não Divulga</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Faixa de Seguidores */}
              <div className="space-y-1 col-span-2">
                <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  Seguidores
                </label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.minSeguidores || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, minSeguidores: parseInt(e.target.value) || 0 }))}
                    className="h-9 text-sm bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
                  />
                  <span className="text-muted-foreground text-sm font-medium">até</span>
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.maxSeguidores === 1000000 ? '' : filters.maxSeguidores}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxSeguidores: parseInt(e.target.value) || 1000000 }))}
                    className="h-9 text-sm bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
                  />
                </div>
              </div>

              {/* Botão Limpar Filtros */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-transparent">Ações</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({
                    busca: '',
                    pais: 'Todos',
                    categoria: 'Todas',
                    genero: 'Todos',
                    verificado: null,
                    divulgaTrader: null,
                    minSeguidores: 0,
                    maxSeguidores: 1000000
                  })}
                  className="h-9 w-full text-sm hover:bg-destructive/10 hover:text-destructive hover:border-destructive/50 transition-all duration-200"
                >
                  <X className="h-4 w-4 mr-1" />
                  Limpar
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}


