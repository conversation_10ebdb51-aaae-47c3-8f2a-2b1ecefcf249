'use client';

import { usePathname } from 'next/navigation';
import { SimpleSidebarStatic } from '@/components/ui/simple-sidebar-static';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

// Função para remover prefixo de idioma da URL
function removeLocalePrefix(pathname: string): string {
  const supportedLocales = ['pt', 'en', 'es'];
  const segments = pathname.split('/');
  const firstSegment = segments[1];
  
  if (supportedLocales.includes(firstSegment)) {
    return pathname.replace(`/${firstSegment}`, '') || '/';
  }
  
  return pathname;
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // Verificar se pathname não é null
  if (!pathname) {
    return <div className="min-h-screen w-full">{children}</div>;
  }
  
  // Remover prefixo de idioma para verificação de rotas
  const pathWithoutLocale = removeLocalePrefix(pathname);
  
  // Rotas que não devem mostrar sidebar
  const routesWithoutSidebar = [
    '/',
    '/onboarding',
    '/sign-in',
    '/sign-up',
    '/auth-redirect',
    '/select-organization',
    '/shared'
  ];
  
  // Verificar se é uma rota sem sidebar
  const shouldHideSidebar = routesWithoutSidebar.some(route => {
    if (route === '/') {
      return pathWithoutLocale === '/';
    }
    return pathWithoutLocale.startsWith(route);
  });

  if (shouldHideSidebar) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }

  // Para todas as outras páginas, layout normal com sidebar
  return (
    <div className="h-screen w-full flex">
      <SimpleSidebarStatic />
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
}

