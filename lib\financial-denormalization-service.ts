// SERVIÇO DE DENORMALIZAÇÃO DE DADOS FINANCEIROS
// Gerencia a sincronização de dados básicos financeiros no documento principal

import { InfluencerFinancial } from '../types/influencer-financial';
import { Influencer } from '../types/influencer';
import { influencersCollection } from './firebase';
import { FinancialCacheService } from './firebase-financial-cache';

// Interface para os dados de preços denormalizados
interface DenormalizedPricing {
  hasFinancialData: boolean;
  priceRange: 'low' | 'medium' | 'high' | 'premium';
  avgPrice: number;
  mainService: {
    platform: 'instagram' | 'tiktok' | 'youtube';
    serviceType: string;
    price: number;
  };
  lastPriceUpdate: Date;
  isNegotiable: boolean;
}

// Configuração de faixas de preço (em reais)
const PRICE_RANGES = {
  low: { min: 0, max: 500 },
  medium: { min: 501, max: 2000 },
  high: { min: 2001, max: 5000 },
  premium: { min: 5001, max: Infinity }
};

/**
 * Calcula a faixa de preço baseado no valor médio
 */
function calculatePriceRange(avgPrice: number): 'low' | 'medium' | 'high' | 'premium' {
  if (avgPrice <= PRICE_RANGES.low.max) return 'low';
  if (avgPrice <= PRICE_RANGES.medium.max) return 'medium';
  if (avgPrice <= PRICE_RANGES.high.max) return 'high';
  return 'premium';
}

/**
 * Calcula o preço médio dos serviços
 */
function calculateAveragePrice(financialData: InfluencerFinancial): number {
  const prices = [];
  
  if (financialData.prices.instagramStory.price > 0) {
    prices.push(financialData.prices.instagramStory.price);
  }
  if (financialData.prices.instagramReel.price > 0) {
    prices.push(financialData.prices.instagramReel.price);
  }
  if (financialData.prices.tiktokVideo.price > 0) {
    prices.push(financialData.prices.tiktokVideo.price);
  }
  if (financialData.prices.youtubeDedicated.price > 0) {
    prices.push(financialData.prices.youtubeDedicated.price);
  }
  if (financialData.prices.youtubeShorts.price > 0) {
    prices.push(financialData.prices.youtubeShorts.price);
  }
  if (financialData.prices.youtubeInsertion.price > 0) {
    prices.push(financialData.prices.youtubeInsertion.price);
  }

  return prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0;
}

/**
 * Identifica o serviço principal (maior preço)
 */
function getMainService(financialData: InfluencerFinancial): DenormalizedPricing['mainService'] {
  const services = [
    {
      platform: 'instagram' as const,
      serviceType: 'story',
      price: financialData.prices.instagramStory.price
    },
    {
      platform: 'instagram' as const,
      serviceType: 'reel',
      price: financialData.prices.instagramReel.price
    },
    {
      platform: 'tiktok' as const,
      serviceType: 'video',
      price: financialData.prices.tiktokVideo.price
    },
    {
      platform: 'youtube' as const,
      serviceType: 'dedicated',
      price: financialData.prices.youtubeDedicated.price
    },
    {
      platform: 'youtube' as const,
      serviceType: 'shorts',
      price: financialData.prices.youtubeShorts.price
    },
    {
      platform: 'youtube' as const,
      serviceType: 'insertion',
      price: financialData.prices.youtubeInsertion.price
    }
  ];

  // Ordenar por preço decrescente e pegar o primeiro
  services.sort((a, b) => b.price - a.price);
  
  return services[0].price > 0 ? services[0] : {
    platform: 'instagram',
    serviceType: 'story',
    price: 0
  };
}

/**
 * Converte dados financeiros completos em dados denormalizados
 */
function createDenormalizedPricing(financialData: InfluencerFinancial): DenormalizedPricing {
  const avgPrice = calculateAveragePrice(financialData);
  const priceRange = calculatePriceRange(avgPrice);
  const mainService = getMainService(financialData);

  return {
    hasFinancialData: true,
    priceRange,
    avgPrice,
    mainService,
    lastPriceUpdate: financialData.updatedAt,
    isNegotiable: avgPrice > 0 // Assumir que é negociável se tem preço definido
  };
}

/**
 * Serviço principal de denormalização
 */
export const FinancialDenormalizationService = {
  /**
   * Atualiza dados financeiros denormalizados de um influenciador
   */
  async updateInfluencerPricing(influencerId: string): Promise<boolean> {
    try {
      console.log(`🔄 Atualizando dados financeiros denormalizados para influenciador ${influencerId}`);

      // Buscar dados financeiros (usando cache)
      const financialData = await FinancialCacheService.getFinancialData(influencerId);
      
      if (!financialData) {
        // Se não tem dados financeiros, remover pricing do documento
        await influencersCollection.doc(influencerId).update({
          pricing: null
        });
        console.log(`❌ Removido pricing do influenciador ${influencerId} - sem dados financeiros`);
        return true;
      }

      // Criar dados denormalizados
      const denormalizedPricing = createDenormalizedPricing(financialData);

      // Atualizar documento do influenciador
      await influencersCollection.doc(influencerId).update({
        pricing: denormalizedPricing
      });

      console.log(`✅ Dados financeiros denormalizados atualizados para influenciador ${influencerId}`);
      return true;

    } catch (error) {
      console.error(`❌ Erro ao atualizar pricing denormalizado para ${influencerId}:`, error);
      return false;
    }
  },

  /**
   * Atualiza dados denormalizados para múltiplos influenciadores
   */
  async updateMultipleInfluencersPricing(influencerIds: string[]): Promise<{
    success: string[];
    failed: string[];
  }> {
    console.log(`🔄 Atualizando pricing denormalizado para ${influencerIds.length} influenciadores`);
    
    const results = {
      success: [] as string[],
      failed: [] as string[]
    };

    // Processar em lotes de 10 para não sobrecarregar
    const BATCH_SIZE = 10;
    for (let i = 0; i < influencerIds.length; i += BATCH_SIZE) {
      const batch = influencerIds.slice(i, i + BATCH_SIZE);
      
      const promises = batch.map(async (influencerId) => {
        const success = await this.updateInfluencerPricing(influencerId);
        if (success) {
          results.success.push(influencerId);
        } else {
          results.failed.push(influencerId);
        }
      });

      await Promise.all(promises);
      
      // Pequeno delay entre lotes
      if (i + BATCH_SIZE < influencerIds.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    console.log(`✅ Atualização concluída: ${results.success.length} sucessos, ${results.failed.length} falhas`);
    return results;
  },

  /**
   * Sincroniza dados financeiros quando há alteração nos dados completos
   */
  async syncFinancialData(influencerId: string, financialData: InfluencerFinancial): Promise<boolean> {
    try {
      console.log(`🔄 Sincronizando dados financeiros para influenciador ${influencerId}`);

      // Criar dados denormalizados
      const denormalizedPricing = createDenormalizedPricing(financialData);

      // Atualizar documento do influenciador
      await influencersCollection.doc(influencerId).update({
        pricing: denormalizedPricing
      });

      // Invalidar cache para forçar atualização
      FinancialCacheService.invalidateInfluencer(influencerId);

      console.log(`✅ Dados financeiros sincronizados para influenciador ${influencerId}`);
      return true;

    } catch (error) {
      console.error(`❌ Erro ao sincronizar dados financeiros para ${influencerId}:`, error);
      return false;
    }
  },

  /**
   * Busca influenciadores por faixa de preço
   */
  async getInfluencersByPriceRange(
    priceRange: 'low' | 'medium' | 'high' | 'premium',
    userId: string,
    limit: number = 20
  ): Promise<Influencer[]> {
    try {
      console.log(`🔍 Buscando influenciadores na faixa de preço: ${priceRange}`);

      const snapshot = await influencersCollection
        .where('userId', '==', userId)
        .where('pricing.priceRange', '==', priceRange)
        .where('pricing.hasFinancialData', '==', true)
        .orderBy('pricing.avgPrice', 'desc')
        .limit(limit)
        .get();

      const influencers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Influencer[];

      console.log(`✅ Encontrados ${influencers.length} influenciadores na faixa ${priceRange}`);
      return influencers;

    } catch (error) {
      console.error(`❌ Erro ao buscar influenciadores por faixa de preço:`, error);
      return [];
    }
  },

  /**
   * Busca influenciadores por faixa de preço específica
   */
  async getInfluencersByPriceValues(
    minPrice: number,
    maxPrice: number,
    userId: string,
    limit: number = 20
  ): Promise<Influencer[]> {
    try {
      console.log(`🔍 Buscando influenciadores entre R$ ${minPrice} e R$ ${maxPrice}`);

      const snapshot = await influencersCollection
        .where('userId', '==', userId)
        .where('pricing.hasFinancialData', '==', true)
        .where('pricing.avgPrice', '>=', minPrice)
        .where('pricing.avgPrice', '<=', maxPrice)
        .orderBy('pricing.avgPrice', 'desc')
        .limit(limit)
        .get();

      const influencers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Influencer[];

      console.log(`✅ Encontrados ${influencers.length} influenciadores na faixa de preço específica`);
      return influencers;

    } catch (error) {
      console.error(`❌ Erro ao buscar influenciadores por faixa de preço específica:`, error);
      return [];
    }
  },

  /**
   * Busca influenciadores por plataforma principal
   */
  async getInfluencersByMainPlatform(
    platform: 'instagram' | 'tiktok' | 'youtube',
    userId: string,
    limit: number = 20
  ): Promise<Influencer[]> {
    try {
      console.log(`🔍 Buscando influenciadores com plataforma principal: ${platform}`);

      const snapshot = await influencersCollection
        .where('userId', '==', userId)
        .where('pricing.hasFinancialData', '==', true)
        .where('pricing.mainService.platform', '==', platform)
        .orderBy('pricing.avgPrice', 'desc')
        .limit(limit)
        .get();

      const influencers = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Influencer[];

      console.log(`✅ Encontrados ${influencers.length} influenciadores com plataforma principal ${platform}`);
      return influencers;

    } catch (error) {
      console.error(`❌ Erro ao buscar influenciadores por plataforma principal:`, error);
      return [];
    }
  },

  /**
   * Retorna estatísticas de preços por usuário
   */
  async getPricingStats(userId: string): Promise<{
    totalWithPricing: number;
    byPriceRange: Record<string, number>;
    byPlatform: Record<string, number>;
    avgPrice: number;
    minPrice: number;
    maxPrice: number;
  }> {
    try {
      console.log(`📊 Calculando estatísticas de preços para usuário ${userId}`);

      const snapshot = await influencersCollection
        .where('userId', '==', userId)
        .where('pricing.hasFinancialData', '==', true)
        .get();

      const influencers = snapshot.docs.map(doc => doc.data()) as Influencer[];

      const stats = {
        totalWithPricing: influencers.length,
        byPriceRange: {
          low: 0,
          medium: 0,
          high: 0,
          premium: 0
        },
        byPlatform: {
          instagram: 0,
          tiktok: 0,
          youtube: 0
        },
        avgPrice: 0,
        minPrice: Infinity,
        maxPrice: 0
      };

      let totalPrice = 0;
      for (const influencer of influencers) {
        if (influencer.pricing) {
          // Contar por faixa de preço
          stats.byPriceRange[influencer.pricing.priceRange]++;
          
          // Contar por plataforma
          stats.byPlatform[influencer.pricing.mainService.platform]++;
          
          // Calcular valores
          totalPrice += influencer.pricing.avgPrice;
          stats.minPrice = Math.min(stats.minPrice, influencer.pricing.avgPrice);
          stats.maxPrice = Math.max(stats.maxPrice, influencer.pricing.avgPrice);
        }
      }

      stats.avgPrice = influencers.length > 0 ? totalPrice / influencers.length : 0;
      stats.minPrice = stats.minPrice === Infinity ? 0 : stats.minPrice;

      console.log(`✅ Estatísticas calculadas para ${influencers.length} influenciadores`);
      return stats;

    } catch (error) {
      console.error(`❌ Erro ao calcular estatísticas de preços:`, error);
      return {
        totalWithPricing: 0,
        byPriceRange: { low: 0, medium: 0, high: 0, premium: 0 },
        byPlatform: { instagram: 0, tiktok: 0, youtube: 0 },
        avgPrice: 0,
        minPrice: 0,
        maxPrice: 0
      };
    }
  }
};

export default FinancialDenormalizationService; 

