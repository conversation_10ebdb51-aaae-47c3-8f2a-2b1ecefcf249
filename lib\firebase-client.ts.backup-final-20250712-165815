// Firebase Client Configuration
// lib/firebase-client.ts
// Para uso em Client Components

import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore, connectFirestoreEmulator, enableIndexedDbPersistence, enableNetwork, disableNetwork } from 'firebase/firestore';
import { getStorage, FirebaseStorage, connectStorageEmulator } from 'firebase/storage';
import { getAnalytics } from 'firebase/analytics';

// ✅ CONFIGURAÇÃO: Firebase config com variáveis de ambiente
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "AIzaSyDhSkFQHdDLke6drWlVJzE3Zys6CJx_arQ",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "deumatch-demo.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "deumatch-demo",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "deumatch-demo.firebasestorage.app",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "306980252445",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:306980252445:web:e6e275cde8b9b9668e0ff4",
};

// ✅ VALIDAÇÃO: Verificar configurações obrigatórias
const requiredConfig = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
const missingConfig = requiredConfig.filter(key => !firebaseConfig[key as keyof typeof firebaseConfig]);

if (missingConfig.length > 0) {
  console.error('❌ ERRO FIREBASE: Configurações ausentes:', missingConfig);
  console.error('📋 SOLUÇÃO:');
  console.error('1. Crie um arquivo .env.local na raiz do projeto');
  console.error('2. Adicione as variáveis de ambiente do Firebase:');
  console.error('   NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDhSkFQHdDLke6drWlVJzE3Zys6CJx_arQ');
  console.error('   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=deumatch-demo.firebaseapp.com');
  console.error('   NEXT_PUBLIC_FIREBASE_PROJECT_ID=deumatch-demo');
  console.error('   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=deumatch-demo.firebasestorage.app');
  console.error('   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=306980252445');
  console.error('   NEXT_PUBLIC_FIREBASE_APP_ID=1:306980252445:web:e6e275cde8b9b9668e0ff4');
  console.error('3. Reinicie o servidor: npm run dev');
  console.error('📖 Consulte firebase-config-example.md para mais detalhes');
  
  // ✅ CORREÇÃO: Não tentar inicializar Firebase com configurações inválidas
  throw new Error('❌ Firebase não pode ser inicializado: variáveis de ambiente ausentes. Consulte firebase-config-example.md');
}

// ✅ DEBUG: Log configurações carregadas (sem expor chaves sensíveis)
console.log('🔥 Firebase Config:', {
  apiKey: firebaseConfig.apiKey ? '✅ Configurado' : '❌ Ausente',
  authDomain: firebaseConfig.authDomain || '❌ Ausente',
  projectId: firebaseConfig.projectId || '❌ Ausente',
  storageBucket: firebaseConfig.storageBucket || '❌ Ausente',
  messagingSenderId: firebaseConfig.messagingSenderId || '❌ Ausente',
  appId: firebaseConfig.appId ? '✅ Configurado' : '❌ Ausente',
});

// ✅ INICIALIZAÇÃO: Firebase App
let app: FirebaseApp;
try {
  app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
  console.log('✅ Firebase inicializado com sucesso para o projeto:', firebaseConfig.projectId);
} catch (error) {
  console.error('❌ ERRO CRÍTICO Firebase:', error);
  
  if (error instanceof Error) {
    if (error.message.includes('auth/invalid-api-key')) {
      console.error('🔑 CHAVE API INVÁLIDA:');
      console.error('   - Verifique se NEXT_PUBLIC_FIREBASE_API_KEY está correto');
      console.error('   - Confirme no Firebase Console > Project Settings');
    } else if (error.message.includes('auth/invalid-app-credential')) {
      console.error('🚫 CREDENCIAIS INVÁLIDAS:');
      console.error('   - Verifique todas as variáveis NEXT_PUBLIC_FIREBASE_*');
    } else if (error.message.includes('network')) {
      console.error('🌐 ERRO DE REDE:');
      console.error('   - Verifique sua conexão com a internet');
      console.error('   - Verifique se o Firebase está acessível');
    }
  }
  
  console.error('📖 Consulte firebase-config-example.md para configuração correta');
  throw new Error('❌ Firebase falhou ao inicializar. Verifique a configuração.');
}

// ✅ SERVIÇOS: Inicializar serviços Firebase
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// const auth: Auth = getAuth(app);
const clientDb: Firestore = getFirestore(app);
const storage: FirebaseStorage = getStorage(app);

// ✅ PERSISTÊNCIA: Configurar offline para Firestore usando a nova API
if (typeof window !== 'undefined') {
  // Usar a nova configuração de cache do Firestore
  import('firebase/firestore').then(({ initializeFirestore, persistentLocalCache, persistentMultipleTabManager }) => {
    try {
      // Tentar habilitar cache persistente
      const firestoreWithCache = initializeFirestore(app, {
        localCache: persistentLocalCache({
          tabManager: persistentMultipleTabManager()
        })
      });
      console.log('✅ Persistência offline habilitada (nova API)');
    } catch (error: any) {
      if (error.code === 'failed-precondition') {
        console.warn('⚠️ Persistência offline: Múltiplas abas abertas');
      } else if (error.code === 'unimplemented') {
        console.warn('⚠️ Persistência offline não suportada neste navegador');
      } else {
        console.warn('⚠️ Cache persistente não disponível, usando cache em memória:', error);
        // Fallback para cache em memória
        import('firebase/firestore').then(({ memoryLocalCache }) => {
          try {
            initializeFirestore(app, {
              localCache: memoryLocalCache()
            });
            console.log('✅ Cache em memória habilitado');
          } catch (fallbackError) {
            console.warn('⚠️ Erro ao configurar cache:', fallbackError);
          }
        });
      }
    }
  }).catch(() => {
    // Fallback para método antigo se a nova API não estiver disponível
    enableIndexedDbPersistence(clientDb, {
      forceOwnership: false
    }).then(() => {
      console.log('✅ Persistência offline habilitada (método legado)');
    }).catch((error) => {
      console.warn('⚠️ Persistência offline não disponível:', error);
    });
  });

  // ✅ CONECTIVIDADE: Monitorar estado da conexão
  window.addEventListener('online', async () => {
    console.log('🌐 Conexão restaurada');
    try {
      await enableNetwork(clientDb);
      console.log('✅ Firestore reconectado');
    } catch (error) {
      console.warn('⚠️ Erro ao reconectar Firestore:', error);
    }
  });

  window.addEventListener('offline', async () => {
    console.warn('⚠️ Conexão perdida - entrando em modo offline');
    try {
      await disableNetwork(clientDb);
      console.log('📱 Firestore em modo offline');
    } catch (error) {
      console.warn('⚠️ Erro ao desabilitar rede Firestore:', error);
    }
  });
}

// ✅ DESENVOLVIMENTO: Conectar emuladores se configurado
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const useEmulators = process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true';
 
}

// ✅ ANALYTICS: Inicializar apenas se suportado
let analytics: any;
if (typeof window !== 'undefined') {
  import('firebase/analytics').then(async ({ isSupported }) => {
    try {
      const analyticsSupported = await isSupported();
      if (analyticsSupported) {
        analytics = getAnalytics(app);
        console.log('📊 Firebase Analytics inicializado');
      }
    } catch (error) {
      console.warn('⚠️ Firebase Analytics não disponível:', error);
    }
  }).catch(() => {
    // Analytics não é crítico, ignorar erro
  });
}

// ✅ CORREÇÃO: Adicionar monitoramento de erros de CSP para Firebase Analytics
if (typeof window !== 'undefined') {
  // Detectar violações de CSP específicas do Firebase
  window.addEventListener('securitypolicyviolation', (e) => {
    if (e.blockedURI?.includes('firebase.googleapis.com')) {
      console.error('❌ CSP bloqueou requisição do Firebase:', {
        blockedURI: e.blockedURI,
        violatedDirective: e.violatedDirective,
        originalPolicy: e.originalPolicy
      });
      
      // Tentar uma reinicialização alternativa do Firebase Analytics
      console.log('🔄 Tentando reinicializar Firebase Analytics...');
      
      // Aguardar um pouco antes de tentar novamente
      setTimeout(() => {
                 try {
           if ((window as any).gtag && typeof (window as any).gtag === 'function') {
             console.log('📊 Firebase Analytics disponível via gtag');
           }
        } catch (retryError) {
          console.warn('⚠️ Não foi possível reinicializar Firebase Analytics:', retryError);
        }
      }, 2000);
    }
  });
  
  // Detectar erros gerais do Firebase
  window.addEventListener('error', (e) => {
    if (e.error?.message?.includes('firebase') || e.error?.message?.includes('Firebase')) {
      console.error('❌ Erro do Firebase detectado:', {
        message: e.error.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno
      });
    }
  });
  
  // Detectar erros de promises rejeitadas do Firebase
  window.addEventListener('unhandledrejection', (e) => {
    if (e.reason?.message?.includes('firebase') || e.reason?.message?.includes('Firebase')) {
      console.error('❌ Promise rejeitada do Firebase:', e.reason);
      // Não impedir o comportamento padrão para manter compatibilidade
    }
  });
}

// ✅ UTILITÁRIOS: Funções auxiliares
export const testFirebaseConnection = async (): Promise<boolean> => {
  try {
    const { doc, getDoc } = await import('firebase/firestore');
    const testDoc = doc(clientDb, 'test', 'connection');
    await getDoc(testDoc);
    console.log('✅ Teste de conexão Firebase: SUCESSO');
    return true;
  } catch (error) {
    console.warn('⚠️ Teste de conexão Firebase: FALHOU', error);
    return false;
  }
};

// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
export const getTokenWithRetry = async (forceRefresh = false): Promise<string | null> => {
  console.warn('getTokenWithRetry está desabilitado - use Clerk Auth');
  return null;
};

// ✅ EXPORTAÇÕES: Centralizadas e tipadas
export { app };
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// export { auth };
export { clientDb };
export { storage };
export { analytics };

// ✅ COMPATIBILIDADE: Exports alternativos
export { clientDb as firestore };
export { clientDb as db };
export default app;


