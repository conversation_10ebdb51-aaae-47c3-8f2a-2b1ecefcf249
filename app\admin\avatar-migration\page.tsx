import { Metadata } from 'next';
import { AvatarMigrationPanel } from '@/components/avatar-migration-panel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Database, Shield, Info } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export const metadata: Metadata = {
  title: 'Migração de Avatares | Admin - DeuMatch',
  description: 'Painel administrativo para migração de avatares do armazenamento local para o Firebase Storage',
};

export default function AvatarMigrationPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      {/* Cabeçalho */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Database className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Migração de Avatares</h1>
            <p className="text-muted-foreground">
              Gerencie a migração dos avatares dos influenciadores para o Firebase Storage
            </p>
          </div>
        </div>
      </div>

      {/* Informações Importantes */}
      <div className="mb-8 space-y-4">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Sobre esta migração:</strong> Os avatares dos influenciadores estão atualmente 
            armazenados localmente no servidor. Esta ferramenta permite migrar esses arquivos 
            para o Firebase Storage, proporcionando melhor performance, escalabilidade e confiabilidade.
          </AlertDescription>
        </Alert>
        
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <strong>Segurança:</strong> Durante a migração, os arquivos originais são mantidos 
            até que a operação seja concluída com sucesso. Apenas administradores podem executar esta operação.
          </AlertDescription>
        </Alert>
      </div>

      {/* Benefícios da Migração */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Benefícios da Migração para Firebase Storage</CardTitle>
          <CardDescription>
            Vantagens de migrar do armazenamento local para o Firebase Storage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-green-600 dark:text-green-400">Vantagens Técnicas</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>CDN Global:</strong> Entrega mais rápida de imagens em qualquer localização</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Escalabilidade:</strong> Suporte automático para crescimento do volume de dados</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Backup Automático:</strong> Redundância e proteção contra perda de dados</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Otimização:</strong> Compressão e otimização automática de imagens</span>
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-semibold text-blue-600 dark:text-blue-400">Vantagens Operacionais</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Redução de Carga:</strong> Menos processamento no servidor principal</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Monitoramento:</strong> Métricas detalhadas de uso e performance</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Segurança:</strong> Controle de acesso granular e criptografia</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>Manutenção:</strong> Redução da complexidade de backup e manutenção</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Painel Principal de Migração */}
      <AvatarMigrationPanel />

      {/* Informações Técnicas */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Informações Técnicas</CardTitle>
          <CardDescription>
            Detalhes sobre o processo de migração
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-semibold mb-3">Processo de Migração</h4>
              <ol className="space-y-2 list-decimal list-inside text-muted-foreground">
                <li>Verificação dos avatares armazenados localmente</li>
                <li>Upload seguro para o Firebase Storage</li>
                <li>Configuração de permissões públicas</li>
                <li>Atualização dos registros no Firestore</li>
                <li>Validação da integridade dos dados</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-semibold mb-3">Configurações</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li><strong>Bucket:</strong> deumatch-385dd.appspot.com</li>
                <li><strong>Pasta de Destino:</strong> influencers/avatars/</li>
                <li><strong>Formato Suportado:</strong> JPG, PNG, WebP</li>
                <li><strong>Tamanho Máximo:</strong> 5MB por arquivo</li>
                <li><strong>Compressão:</strong> Automática pelo Firebase</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

