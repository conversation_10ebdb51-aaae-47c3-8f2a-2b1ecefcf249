'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/firebase-auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building2, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Eye, 
  MessageSquare, 
  Calendar,
  BarChart3,
  Settings,
  LogOut,
  Shield,
  Crown,
  UserCheck
} from 'lucide-react';
import { ProposalService } from '@/services/proposal-service';
import { CampaignService } from '@/services/campaign-service';
import { Proposal } from '@/types/proposal';
import { Campaign } from '@/types/campaign';
import { toast } from '@/hooks/use-toast';

interface DashboardStats {
  totalProposals: number;
  activeProposals: number;
  acceptedProposals: number;
  totalCampaigns: number;
  activeCampaigns: number;
  totalBudget: number;
  avgResponseTime: number;
}

export function BrandDashboard() {
  const { user, logout, hasPermission } = useAuth();
  const canViewAll = hasPermission('view_all');
  const canEdit = hasPermission('edit_all');
  const canManageUsers = hasPermission('manage_users');
  
  const [stats, setStats] = useState<DashboardStats>({
    totalProposals: 0,
    activeProposals: 0,
    acceptedProposals: 0,
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalBudget: 0,
    avgResponseTime: 0
  });
  const [recentProposals, setRecentProposals] = useState<Proposal[]>([]);
  const [recentCampaigns, setRecentCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Carregar propostas do usuário
      const proposalsResult = await ProposalService.getProposals({}, 50);
      
      const brandProposals = proposalsResult.proposals;
      setRecentProposals(brandProposals.slice(0, 5));
      
      // Carregar campanhas do usuário
      const campaigns = await CampaignService.getCampaigns({}, 50);
      
      const brandCampaigns = campaigns;
      setRecentCampaigns(brandCampaigns.slice(0, 5));
      
      // Calcular estatísticas
      const newStats: DashboardStats = {
        totalProposals: brandProposals.length,
        activeProposals: brandProposals.filter(p => 
          ['sent', 'viewed', 'negotiating'].includes(p.status)
        ).length,
        acceptedProposals: brandProposals.filter(p => p.status === 'accepted').length,
        totalCampaigns: brandCampaigns.length,
        activeCampaigns: brandCampaigns.filter(c => c.status === 'active').length,
        totalBudget: brandCampaigns.reduce((sum, c) => sum + (c.budget || 0), 0),
        avgResponseTime: 0 // Calcular se necessário
      };
      
      setStats(newStats);
    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
      toast({
        title: "Erro ao carregar dados",
        description: "Não foi possível carregar os dados do dashboard.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'manager': return <Shield className="h-4 w-4 text-blue-500" />;
      case 'viewer': return <UserCheck className="h-4 w-4 text-green-500" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin': return 'default';
      case 'manager': return 'secondary';
      case 'viewer': return 'outline';
      default: return 'outline';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'text-green-500';
      case 'sent': return 'text-blue-500';
      case 'negotiating': return 'text-yellow-500';
      case 'rejected': return 'text-red-500';
      case 'active': return 'text-green-500';
      case 'completed': return 'text-blue-500';
      case 'paused': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header com informações da marca e usuário */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-3">
            <div 
              className="w-12 h-12 rounded-lg flex items-center justify-center"
              style={{
                backgroundColor: '#8b5cf6'
              }}
            >
              <Building2 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Dashboard</h1>
              <p className="text-muted-foreground">Painel de controle</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="font-medium">{user.name}</p>
            <div className="flex items-center gap-2">
              {getRoleIcon(user.role)}
              <Badge variant={getRoleBadgeVariant(user.role)}>
                {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
              </Badge>
            </div>
          </div>
          
          <Button variant="outline" size="sm" onClick={logout}>
            <LogOut className="h-4 w-4 mr-2" />
            Sair
          </Button>
        </div>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Propostas</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProposals}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeProposals} ativas
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Propostas Aceitas</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.acceptedProposals}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalProposals > 0 ? 
                `${((stats.acceptedProposals / stats.totalProposals) * 100).toFixed(1)}% taxa de aceitação` :
                'Nenhuma proposta ainda'
              }
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Campanhas Ativas</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeCampaigns}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalCampaigns} total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orçamento Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              R$ {stats.totalBudget.toLocaleString('pt-BR')}
            </div>
            <p className="text-xs text-muted-foreground">
              Em campanhas ativas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs com conteúdo detalhado */}
      <Tabs defaultValue="proposals" className="space-y-4">
        <TabsList>
          <TabsTrigger value="proposals">Propostas Recentes</TabsTrigger>
          <TabsTrigger value="campaigns">Campanhas Recentes</TabsTrigger>
          {canViewAll && <TabsTrigger value="analytics">Analytics</TabsTrigger>}
        </TabsList>
        
        <TabsContent value="proposals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Propostas Recentes</CardTitle>
              <CardDescription>
                Últimas propostas da marca {currentBrand.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentProposals.length > 0 ? (
                <div className="space-y-3">
                  {recentProposals.map((proposal) => (
                    <div key={proposal.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">Proposta - {proposal.grupo || 'Sem grupo'}</p>
                        <p className="text-sm text-muted-foreground">
                          R$ {proposal.totalAmount.toLocaleString('pt-BR')}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(proposal.status)}>
                          {proposal.status}
                        </Badge>
                        <p className="text-xs text-muted-foreground mt-1">
                          {new Date(proposal.createdAt).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  Nenhuma proposta encontrada para esta marca.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Campanhas Recentes</CardTitle>
              <CardDescription>
                Últimas campanhas da marca {currentBrand.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentCampaigns.length > 0 ? (
                <div className="space-y-3">
                  {recentCampaigns.map((campaign) => (
                    <div key={campaign.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{campaign.name}</p>
                        <p className="text-sm text-muted-foreground">
                          R$ {campaign.budget?.toLocaleString('pt-BR') || '0'}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(campaign.status)}>
                          {campaign.status}
                        </Badge>
                        <p className="text-xs text-muted-foreground mt-1">
                          {new Date(campaign.createdAt).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  Nenhuma campanha encontrada para esta marca.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {canViewAll && (
          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Analytics da Marca</CardTitle>
                <CardDescription>
                  Métricas e insights detalhados
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Taxa de Conversão</h4>
                    <p className="text-2xl font-bold">
                      {stats.totalProposals > 0 ? 
                        `${((stats.acceptedProposals / stats.totalProposals) * 100).toFixed(1)}%` :
                        '0%'
                      }
                    </p>
                  </div>
                  
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Orçamento Médio</h4>
                    <p className="text-2xl font-bold">
                      R$ {stats.totalCampaigns > 0 ? 
                        (stats.totalBudget / stats.totalCampaigns).toLocaleString('pt-BR') :
                        '0'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

