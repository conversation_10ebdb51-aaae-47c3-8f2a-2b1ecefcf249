# Requirements Document

## Introduction

The proposals table at `/propostas/[id]` is currently missing important view metrics fields for influencers. These fields (`instagramReelsViews`, `instagramStoriesViews`, `tiktokVideoViews`, `youtubeShortsViews`, `youtubeLongFormViews`) are defined in the influencer types and used in other parts of the application, but are not being displayed in the proposals table. This creates an incomplete view of influencer performance data for users reviewing proposals.

## Requirements

### Requirement 1

**User Story:** As a brand manager reviewing a proposal, I want to see Instagram Reels view metrics for each influencer, so that I can evaluate their Reels performance when making collaboration decisions.

#### Acceptance Criteria

1. WHEN viewing the proposals table THEN the system SHALL display the `instagramReelsViews` field for each influencer
2. WHEN the `instagramReelsViews` value is available THEN the system SHALL format it as a readable number with appropriate separators
3. WHEN the `instagramReelsViews` value is null or undefined THEN the system SHALL display "N/A" or a similar placeholder

### Requirement 2

**User Story:** As a brand manager reviewing a proposal, I want to see Instagram Stories view metrics for each influencer, so that I can evaluate their Stories performance when making collaboration decisions.

#### Acceptance Criteria

1. WHEN viewing the proposals table THEN the system SHALL display the `instagramStoriesViews` field for each influencer
2. WHEN the `instagramStoriesViews` value is available THEN the system SHALL format it as a readable number with appropriate separators
3. WHEN the `instagramStoriesViews` value is null or undefined THEN the system SHALL display "N/A" or a similar placeholder

### Requirement 3

**User Story:** As a brand manager reviewing a proposal, I want to see TikTok video view metrics for each influencer, so that I can evaluate their TikTok performance when making collaboration decisions.

#### Acceptance Criteria

1. WHEN viewing the proposals table THEN the system SHALL display the `tiktokVideoViews` field for each influencer
2. WHEN the `tiktokVideoViews` value is available THEN the system SHALL format it as a readable number with appropriate separators
3. WHEN the `tiktokVideoViews` value is null or undefined THEN the system SHALL display "N/A" or a similar placeholder

### Requirement 4

**User Story:** As a brand manager reviewing a proposal, I want to see YouTube Shorts view metrics for each influencer, so that I can evaluate their YouTube Shorts performance when making collaboration decisions.

#### Acceptance Criteria

1. WHEN viewing the proposals table THEN the system SHALL display the `youtubeShortsViews` field for each influencer
2. WHEN the `youtubeShortsViews` value is available THEN the system SHALL format it as a readable number with appropriate separators
3. WHEN the `youtubeShortsViews` value is null or undefined THEN the system SHALL display "N/A" or a similar placeholder

### Requirement 5

**User Story:** As a brand manager reviewing a proposal, I want to see YouTube long-form video view metrics for each influencer, so that I can evaluate their YouTube long-form content performance when making collaboration decisions.

#### Acceptance Criteria

1. WHEN viewing the proposals table THEN the system SHALL display the `youtubeLongFormViews` field for each influencer
2. WHEN the `youtubeLongFormViews` value is available THEN the system SHALL format it as a readable number with appropriate separators
3. WHEN the `youtubeLongFormViews` value is null or undefined THEN the system SHALL display "N/A" or a similar placeholder

### Requirement 6

**User Story:** As a user, I want the new view fields to be properly integrated with the existing column visibility controls, so that I can show or hide these columns based on my preferences.

#### Acceptance Criteria

1. WHEN accessing column visibility settings THEN the system SHALL include the new view fields as toggleable options
2. WHEN toggling view field columns THEN the system SHALL save the preference and maintain it across sessions
3. WHEN the columns are hidden THEN the system SHALL not display them in the table but keep the data available for future display