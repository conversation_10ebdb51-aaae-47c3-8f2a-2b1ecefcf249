import { useState, useCallback, useMemo } from 'react';
import { ColumnOrderState } from "@tanstack/react-table";

export interface UseDataTableOptions {
  pageSize?: number;
  enableColumnOrdering?: boolean;
  enableRowSelection?: boolean;
  enableSorting?: boolean;
  enableFiltering?: boolean;
}

export function useDataTable<T>(options: UseDataTableOptions = {}) {
  const {
    pageSize = 10,
    enableColumnOrdering = false,
    enableRowSelection = false,
    enableSorting = true,
    enableFiltering = true
  } = options;

  // Estados do DataTable
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [columnOrder, setColumnOrder] = useState<ColumnOrderState>([]);

  // Handlers memoizados com verificações de segurança
  const handleRowSelectionChange = useCallback((selectedRows: T[]) => {
    if (!Array.isArray(selectedRows)) {
      console.warn('handleRowSelectionChange: selectedRows não é um array', selectedRows);
      return;
    }

    const newSelectedItems = selectedRows
      .filter((row: any) => row && row.id) // Filtrar apenas itens válidos
      .map((row: any) => row.id);

    setSelectedItems(prev => {
      // Evitar atualizações desnecessárias se os arrays são iguais
      if (prev.length === newSelectedItems.length &&
          prev.every((id, index) => id === newSelectedItems[index])) {
        return prev;
      }
      return newSelectedItems;
    });
  }, []);

  const handleColumnOrderChange = useCallback((newOrder: ColumnOrderState) => {
    if (!Array.isArray(newOrder)) {
      console.warn('handleColumnOrderChange: newOrder não é um array', newOrder);
      return;
    }

    setColumnOrder(prev => {
      // Evitar atualizações desnecessárias
      if (JSON.stringify(prev) === JSON.stringify(newOrder)) {
        return prev;
      }
      return newOrder;
    });
  }, []);

  const handleSelectAll = useCallback((items: T[], checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map((item: any) => item.id));
    } else {
      setSelectedItems([]);
    }
  }, []);

  const handleSelectItem = useCallback((itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  }, []);

  const resetSelection = useCallback(() => {
    setSelectedItems([]);
  }, []);

  const resetFilters = useCallback(() => {
    setSearchTerm('');
  }, []);

  // Props para o DataTable
  const dataTableProps = useMemo(() => ({
    enableRowSelection,
    enableColumnVisibility: true,
    enablePagination: true,
    enableSorting,
    enableFiltering,
    enableColumnOrdering,
    pageSize,
    onRowSelectionChange: handleRowSelectionChange,
    columnOrder,
    onColumnOrderChange: handleColumnOrderChange,
  }), [
    enableRowSelection,
    enableSorting,
    enableFiltering,
    enableColumnOrdering,
    pageSize,
    handleRowSelectionChange,
    columnOrder,
    handleColumnOrderChange
  ]);

  return {
    // Estados
    searchTerm,
    setSearchTerm,
    selectedItems,
    setSelectedItems,
    columnOrder,
    setColumnOrder,

    // Handlers
    handleRowSelectionChange,
    handleColumnOrderChange,
    handleSelectAll,
    handleSelectItem,
    resetSelection,
    resetFilters,

    // Props para DataTable
    dataTableProps,

    // Utilidades
    selectedCount: selectedItems.length,
    hasSelection: selectedItems.length > 0,
  };
} 

