const admin = require('firebase-admin');
const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

async function verificarListas() {
  try {
    console.log('🔍 Verificando coleção de listas...');
    
    const snapshot = await db.collection('lists').get();
    console.log('📊 Total de listas na coleção:', snapshot.size);
    
    if (snapshot.size > 0) {
      console.log('\n📋 Listas encontradas:');
      snapshot.docs.forEach(doc => {
        const data = doc.data();
        console.log(`- ID: ${doc.id}`);
        console.log(`  Nome: ${data.nome || 'Sem nome'}`);
        console.log(`  Criada por: ${data.criadoPor || 'Não definido'}`);
        console.log(`  Status: ${data.status || 'Não definido'}`);
        console.log(`  Tipo: ${data.tipoLista || 'Não definido'}`);
        console.log('---');
      });
    }
    
    // Verificar se existem para o usuário específico
    const userId = 'HXzK8iQe2zeoLlgFEGKYglRovFw2';
    console.log(`\n🔍 Buscando listas do usuário: ${userId}`);
    
    const userQuery = await db.collection('lists')
      .where('criadoPor', '==', userId)
      .get();
    console.log(`📊 Listas do usuário encontradas: ${userQuery.size}`);
    
    if (userQuery.size > 0) {
      userQuery.docs.forEach(doc => {
        const data = doc.data();
        console.log(`- ${data.nome} (${data.tipoLista})`);
      });
    }
    
    console.log('\n✅ Verificação concluída');
    
  } catch (error) {
    console.error('❌ Erro ao verificar listas:', error);
  }
}

async function criarListaTeste() {
  try {
    const userId = 'HXzK8iQe2zeoLlgFEGKYglRovFw2';
    console.log('\n🆕 Criando lista de teste...');
    
    const listaData = {
      nome: 'Lista de Teste',
      descricao: 'Lista criada automaticamente para testes',
      tipoLista: 'estática',
      tipoObjeto: 'influenciadores',
      criadoPor: userId,
      criadoPorNome: 'Usuário de Teste',
      tamanho: 0,
      tags: ['teste'],
      isPublica: false,
      compartilhadaCom: [],
      permissoes: {
        visualizar: [],
        editar: [],
        gerenciar: []
      },
      criterios: [],
      configuracaoAtualizacao: {
        frequencia: 'manual',
        ultimaExecucao: null,
        proximaExecucao: null,
        ativa: false
      },
      estatisticas: {
        totalVisualizacoes: 0,
        ultimaVisualizacao: null,
        totalCompartilhamentos: 0,
        totalExportacoes: 0
      },
      status: 'ativa',
      configuracaoExportacao: {
        formatosPadrao: ['csv', 'xlsx'],
        incluirMetadados: true,
        incluirEstatisticas: false
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
      ultimaAtualizacao: new Date()
    };
    
    const docRef = await db.collection('lists').add(listaData);
    console.log(`✅ Lista de teste criada com ID: ${docRef.id}`);
    
  } catch (error) {
    console.error('❌ Erro ao criar lista de teste:', error);
  }
}

async function main() {
  await verificarListas();
  
  // Se não há listas para o usuário, criar uma de teste
  const userId = 'HXzK8iQe2zeoLlgFEGKYglRovFw2';
  const userQuery = await db.collection('lists')
    .where('criadoPor', '==', userId)
    .get();
  
  if (userQuery.size === 0) {
    console.log('\n⚠️  Nenhuma lista encontrada para o usuário. Criando lista de teste...');
    await criarListaTeste();
    await verificarListas();
  }
  
  process.exit(0);
}

main(); 
