"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";

export function NavBar() {
  const pathname = usePathname();
  
  return (
    <div className="relative w-full bg-gradient-to-r from-black/80 to-black/70 backdrop-blur-lg border-b border-white/10 z-50">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2">
            <motion.div
              className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg w-10 h-10 flex items-center justify-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 15 }}
            >
              <span className="text-white font-bold text-xl">IM</span>
            </motion.div>
            <span className="text-white font-bold text-xl hidden sm:block">InfluMarketing</span>
          </Link>

          {/* Navigation */}
          <NavigationMenu className="hidden md:block">
            <NavigationMenuList className="flex space-x-1">
              <NavigationMenuItem>
                <Link href="/" legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "bg-transparent text-white",
                      pathname === "/" && "bg-white/10 text-white"
                    )}
                  >
                    Início
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              
              <NavigationMenuItem>
                <NavigationMenuTrigger className="bg-transparent text-white hover:bg-white/10">
                  Influenciadores
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-3 p-6 w-[400px] bg-black/80 backdrop-blur-lg border border-white/10 rounded-xl">
                    <li className="row-span-3">
                      <NavigationMenuLink asChild>
                        <a
                          className="flex flex-col h-full p-6 no-underline rounded-md bg-gradient-to-br from-purple-900/50 via-purple-800/30 to-fuchsia-900/40 border border-white/5 text-white"
                          href="/influencers"
                        >
                          <div className="mb-2 mt-4 text-lg font-medium">
                            Nossos Influenciadores
                          </div>
                          <p className="text-sm leading-tight text-white/70">
                            Encontre os influenciadores perfeitos para a sua marca ou produto. 
                            Filtre por categoria, alcance e muito mais.
                          </p>
                        </a>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <Link href="/categories/lifestyle" legacyBehavior passHref>
                        <NavigationMenuLink className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-white/10 hover:text-white focus:bg-white/10 focus:text-white">
                          <div className="text-sm font-medium leading-none text-white">Lifestyle</div>
                          <p className="line-clamp-2 text-sm leading-snug text-white/70">
                            Moda, beleza, viagens e bem-estar
                          </p>
                        </NavigationMenuLink>
                      </Link>
                    </li>
                    <li>
                      <Link href="/categories/tech" legacyBehavior passHref>
                        <NavigationMenuLink className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-white/10 hover:text-white focus:bg-white/10 focus:text-white">
                          <div className="text-sm font-medium leading-none text-white">Tecnologia</div>
                          <p className="line-clamp-2 text-sm leading-snug text-white/70">
                            Gadgets, reviews, apps e jogos
                          </p>
                        </NavigationMenuLink>
                      </Link>
                    </li>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              
              <NavigationMenuItem>
                <Link href="/admin/influencers/listar" legacyBehavior passHref>
                  <NavigationMenuLink className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent text-white",
                    pathname?.includes("/admin") && "bg-white/10 text-white"
                  )}>
                    Admin
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              
              <NavigationMenuItem>
                <Link href="/contact" legacyBehavior passHref>
                  <NavigationMenuLink className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent text-white",
                    pathname === "/contact" && "bg-white/10 text-white"
                  )}>
                    Contato
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          {/* Auth buttons / User menu */}
          <div className="flex items-center gap-2">
            <div className="hidden sm:block">
              <Button
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10 hover:text-white"
              >
                Entrar
              </Button>
            </div>
            <Button
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-0 hover:opacity-90"
            >
              Cadastrar
            </Button>
            
            {/* User avatar (mostrado quando logado) */}
            {/* 
            <Avatar className="cursor-pointer border-2 border-purple-500">
              <AvatarImage src="/placeholder.svg" alt="Usuário" />
              <AvatarFallback className="bg-gradient-to-r from-purple-700 to-pink-700 text-white">
                US
              </AvatarFallback>
            </Avatar> 
            */}
          </div>
        </div>
      </div>
    </div>
  );
}


