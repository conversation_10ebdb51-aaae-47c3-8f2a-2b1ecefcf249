import React from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';
import { formatters, StatusBadge, TableAvatar } from '@/lib/table-utils';

export interface Influenciador {
  id: string;
  nome: string;
  usuario?: string;
  pseudonimo?: string;
  email?: string;
  telefone?: string;
  whatsapp?: string;
  
  // Localização
  pais?: string;
  estado?: string;
  cidade?: string;
  localizacao?: string;
  
  // Demografia
  idade?: number;
  genero?: 'male' | 'female' | 'other' | 'not_specified';
  
  // Métricas
  seguidores: number;
  engajamento: number;
  rating?: number;
  
  // Categorização
  nicho?: string;
  categorias?: string[];
  categoriasprincipais?: string[];
  tiposConteudo?: string[];
  especialidades?: string[];
  
  // Status e verificação
  foto?: string;
  status?: 'ativo' | 'inativo' | 'pendente';
  verificado?: boolean;
  disponivel?: boolean;
  
  // Profissional
  promoteTraders?: boolean;
  nomeResponsavel?: string;
  nomeAgencia?: string;
  
  // Bio e visual
  bio?: string;
  imagemFundo?: string;
  gradiente?: string;
  
  // Audiência
  generoAudiencia?: {
    male: number;
    female: number;
    other: number;
  };
  idiomasConteudo?: string[];
  
  // Redes sociais
  redesSociais?: any;
  instagram?: any;
  tiktok?: any;
  youtube?: any;
  twitter?: any;
  facebook?: any;
  outrasRedes?: any[];
  
  // Tags e notas
  tags?: string[];
  notas?: string[];
  
  // Origem e lista
  origem?: string;
  listItemId?: string;
  dataAdicao?: Date;
  posicao?: number;
  
  // Dados completos
  dadosCompletos?: any;
  
  // Novos campos adicionais
  contrato?: 'ativo' | 'inativo' | 'pendente';
  valorMedio?: number;
  ultimaInteracao?: Date;
  frequenciaPostagem?: 'alta' | 'media' | 'baixa';
  qualidadeConteudo?: number; // 1-5
  relacionamentoMarca?: 'excelente' | 'bom' | 'regular' | 'ruim';
  observacoes?: string;
  
  // Campos da API real
  totalFollowers?: number;
  engagementRate?: number;
  location?: string;
  country?: string;
  age?: number;
  category?: string;
  isVerified?: boolean;
  isAvailable?: boolean;
  responsibleName?: string;
  agencyName?: string;
  socialNetworks?: {
    instagram?: {
      username?: string;
      followers?: number;
      engagementRate?: number;
      avgViews?: number;
    };
    youtube?: {
      username?: string;
      followers?: number;
      subscribers?: number;
      engagementRate?: number;
      avgViews?: number;
    };
    tiktok?: {
      username?: string;
      followers?: number;
      engagementRate?: number;
      avgViews?: number;
    };
  };
  mainNetwork?: string;
  audienceAgeRanges?: {
    [platform: string]: { range: string; percentage: number }[];
  };
  financials?: any;
  financialData?: any;
}

export interface InfluencerColumnActions {
  onEdit?: (influencer: Influenciador) => void;
  onDelete?: (influencer: Influenciador) => void;
  onView?: (influencer: Influenciador) => void;
  onRemoveFromList?: (influencer: Influenciador) => void;
}

export function createInfluencerColumns(
  actions: InfluencerColumnActions = {},
  options: {
    showActions?: boolean;
    showPseudonimo?: boolean;
    showOrigem?: boolean;
    isListView?: boolean;
  } = {}
): ColumnDef<Influenciador>[] {
  const { 
    showActions = true, 
    showPseudonimo = true,
    showOrigem = true,
    isListView = false
  } = options;
  
  const { onEdit, onDelete, onView, onRemoveFromList } = actions;

  const columns: ColumnDef<Influenciador>[] = [
    {
      accessorKey: 'nome',
      id: 'nome',
      header: 'NOME DO PERFIL',
      meta: {
        align: 'left'
      },
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex items-center gap-3 justify-start text-left">
            <TableAvatar 
              src={influencer.foto} 
              name={influencer.nome}
              size="sm"
            />
            <div className="text-left min-w-0 flex-1">
              <div className="font-medium text-left truncate">{influencer.nome}</div>
              {influencer.usuario && (
                <div className="text-sm text-muted-foreground text-left truncate">
                  @{influencer.usuario.replace('@', '')}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
  ];



  if (showOrigem) {
    columns.push({
      id: 'origem',
      header: 'ORIGEM',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm text-muted-foreground">
            {influencer.origem || 'Instagram'}
          </span>
        );
      },
    });
  }

  columns.push(
    {
      accessorKey: 'email',
      id: 'email',
      header: 'EMAIL',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm text-muted-foreground">
            {influencer.email || 'N/A'}
          </span>
        );
      },
    },
    {
      accessorKey: 'localizacao',
      id: 'localizacao',
      header: 'LOCALIZAÇÃO',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm">
            {influencer.localizacao || 'N/A'}
          </span>
        );
      },
    },
    {
      accessorKey: 'idade',
      id: 'idade',
      header: 'IDADE',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm">
            {influencer.idade ? `${influencer.idade} anos` : 'N/A'}
          </span>
        );
      },
    },
    {
      accessorKey: 'genero',
      id: 'genero',
      header: 'GÊNERO',
      cell: ({ row }) => {
        const influencer = row.original;
        const generoMap = {
          male: 'Masculino',
          female: 'Feminino',
          other: 'Outro',
          not_specified: 'Não informado'
        };
        return (
          <span className="text-sm">
            {influencer.genero ? generoMap[influencer.genero] || influencer.genero : 'N/A'}
          </span>
        );
      },
    },
    {
      accessorKey: 'seguidores',
      id: 'seguidores',
      header: 'SEGUIDORES',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="font-medium">
            {formatters.number(influencer.seguidores)}
          </span>
        );
      },
    },
    {
      accessorKey: 'engajamento',
      id: 'engajamento',
      header: 'ENGAJAMENTO',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-[#ff0074] font-medium">
            {formatters.percentage(influencer.engajamento)}
          </span>
        );
      },
    },
    {
      accessorKey: 'rating',
      id: 'rating',
      header: 'RATING',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex items-center gap-1">
            <span className="text-sm font-medium">
              {influencer.rating ? influencer.rating.toFixed(1) : 'N/A'}
            </span>
            {influencer.rating && (
              <span className="text-yellow-500">★</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'nicho',
      id: 'nicho',
      header: 'CATEGORIA',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm bg-muted px-2 py-1 rounded">
            {influencer.nicho || 'N/A'}
          </span>
        );
      },
    },
    {
      id: 'redes_sociais',
      header: 'REDES SOCIAIS',
      cell: ({ row }) => {
        const influencer = row.original;
        const redes = [];
        
        if (influencer.instagram?.followers) {
          redes.push(
            <div key="instagram" className="flex items-center gap-1 text-xs">
              <span>📷</span>
              <span>{formatters.number(influencer.instagram.followers)}</span>
            </div>
          );
        }
        
        if (influencer.tiktok?.followers) {
          redes.push(
            <div key="tiktok" className="flex items-center gap-1 text-xs">
              <span>🎵</span>
              <span>{formatters.number(influencer.tiktok.followers)}</span>
            </div>
          );
        }
        
        if (influencer.youtube?.followers) {
          redes.push(
            <div key="youtube" className="flex items-center gap-1 text-xs">
              <span>📺</span>
              <span>{formatters.number(influencer.youtube.followers)}</span>
            </div>
          );
        }
        
        return (
          <div className="flex flex-col gap-1">
            {redes.length > 0 ? redes : <span className="text-xs text-muted-foreground">N/A</span>}
          </div>
        );
      },
    },
    {
      id: 'verificado',
      header: 'VERIFICADO',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex items-center gap-1">
            {influencer.verificado ? (
              <span className="text-blue-500">✓</span>
            ) : (
              <span className="text-muted-foreground">—</span>
            )}
          </div>
        );
      },
    },
    {
      id: 'disponivel',
      header: 'DISPONÍVEL',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex items-center gap-1">
            {influencer.disponivel ? (
              <span className="text-green-500 text-xs">Sim</span>
            ) : (
              <span className="text-red-500 text-xs">Não</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'nomeAgencia',
      id: 'agencia',
      header: 'AGÊNCIA',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm">
            {influencer.nomeAgencia || 'N/A'}
          </span>
        );
      },
    }
  );

  // Sempre mostrar status
  columns.push({
    accessorKey: 'status',
    id: 'status',
    header: 'STATUS',
    cell: ({ row }) => {
      const influencer = row.original;
      const status = influencer.status || 'ativo';
      return <StatusBadge status={status} type="influencer" />;
    },
  });

  if (showActions) {
    columns.push({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(influencer)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(influencer)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
              )}
              {isListView && onRemoveFromList && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onRemoveFromList(influencer)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remover da Lista
                </DropdownMenuItem>
              )}
              {!isListView && onDelete && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onDelete(influencer)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 48,
    });
  }

  return columns;
} 



