import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/lib/firebase';

interface Brand {
  id: string;
  name: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  industry?: string;
  status: 'active' | 'inactive';
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const params = await context.params;
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId || clerkUserId !== params.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Buscar marcas do usuário na coleção 'brands'
    const brandsRef = db.collection('brands');
    const querySnapshot = await brandsRef
      .where('userId', '==', params.userId)
      .orderBy('createdAt', 'desc')
      .get();
    
    const userBrands: Brand[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      userBrands.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate().toISOString() : data.updatedAt
      } as Brand);
    });
    
    return NextResponse.json(userBrands);
  } catch (error) {
    console.error('Erro ao buscar marcas:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const params = await context.params;
    const { userId: clerkUserId } = await auth();
    
    if (!clerkUserId || clerkUserId !== params.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, website, email, phone, industry, logo } = body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json({ error: 'Nome da marca é obrigatório' }, { status: 400 });
    }

    // Verificar se já existe uma marca com o mesmo nome para este usuário
    const brandsRef = db.collection('brands');
    const existingBrand = await brandsRef
      .where('userId', '==', params.userId)
      .where('name', '==', name.trim())
      .get();

    if (!existingBrand.empty) {
      return NextResponse.json({ 
        error: 'Já existe uma marca com este nome em sua conta' 
      }, { status: 409 });
    }

    // Preparar dados da marca, filtrando campos undefined/vazios
    const brandData: any = {
      name: name.trim(),
      userId: params.userId,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Adicionar campos opcionais apenas se tiverem valores válidos
    if (description && description.trim()) {
      brandData.description = description.trim();
    }
    if (website && website.trim()) {
      brandData.website = website.trim();
    }
    if (email && email.trim()) {
      brandData.email = email.trim();
    }
    if (phone && phone.trim()) {
      brandData.phone = phone.trim();
    }
    if (industry && industry.trim()) {
      brandData.industry = industry.trim();
    }
    if (logo && logo.trim()) {
      brandData.logo = logo.trim();
    }

    // Criar nova marca no Firestore
    const newBrandRef = await brandsRef.add(brandData);
    const newBrandDoc = await newBrandRef.get();
    
    const brand = {
      id: newBrandRef.id,
      ...newBrandDoc.data()
    };

    return NextResponse.json(brand);
  } catch (error) {
    console.error('Erro ao criar marca:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}