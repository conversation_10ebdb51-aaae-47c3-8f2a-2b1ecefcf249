import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './use-auth-v2';
import { BrandInfluencer } from '@/types/brand-influencer';

/**
 * 🤝 HOOK PARA INFLUENCIADORES DAS MARCAS DO USUÁRIO
 * Gerencia influenciadores associados às marcas através da tabela brand_influencers
 */

interface InfluencerWithBrandData {
  id: string;
  nome: string;
  verificado?: boolean;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  email?: string;
  avatar?: string;
  country?: string;
  age?: number;
  gender?: string;
  category?: string;
  isVerified?: boolean;
  location?: string;
  phone?: string;
  mainNetwork?: string;
  promotesTraders?: boolean;
  responsibleName?: string;
  agencyName?: string;
  engagementRate?: number;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  // Dados da associação com a marca
  brandInfo: {
    brandId: string;
    brandName: string;
    brandLogo?: string;
    associationDate: Date;
    tags?: string[];
  };
  // Dados financeiros
  dadosFinanceiros?: {
    precos?: {
      instagramStory?: { price: number; name?: string };
      instagramReel?: { price: number; name?: string };
      youtubeInsertion?: { price: number; name?: string };
      youtubeDedicated?: { price: number; name?: string };
      youtubeShorts?: { price: number; name?: string };
      tiktokVideo?: { price: number; name?: string };
    };
    responsavel?: string;
    agencia?: string;
    whatsappFinanceiro?: string;
    emailFinanceiro?: string;
  };
}

interface UseBrandInfluencersOptions {
  autoLoad?: boolean;
  refreshInterval?: number;
  brandId?: string; // Filtrar por marca específica
}

interface UseBrandInfluencersResult {
  influencers: InfluencerWithBrandData[];
  brands: Array<{ id: string; name: string; logo?: string; count: number }>;
  loading: boolean;
  error: string | null;
  totalCount: number;
  refresh: () => Promise<void>;
  addToList: (listId: string, influencerIds: string[]) => Promise<boolean>;
  filterByBrand: (brandId: string | null) => void;
  selectedBrandId: string | null;
}

// Cache para evitar requisições desnecessárias
const cache = new Map<string, { data: InfluencerWithBrandData[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

export function useBrandInfluencers(options: UseBrandInfluencersOptions = {}): UseBrandInfluencersResult {
  const { currentUser, firebaseUser } = useAuth();
  const [influencers, setInfluencers] = useState<InfluencerWithBrandData[]>([]);
  const [brands, setBrands] = useState<Array<{ id: string; name: string; logo?: string; count: number }>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBrandId, setSelectedBrandId] = useState<string | null>(options.brandId || null);

  const {
    autoLoad = true,
    refreshInterval = 300000, // 5 minutos
  } = options;

  /**
   * Buscar associações marca-influenciador do usuário
   */
  const loadBrandInfluencers = useCallback(async () => {
    if (!firebaseUser) {
      setInfluencers([]);
      setBrands([]);
      setLoading(false);
      return;
    }

    const cacheKey = `brand_influencers_${firebaseUser.uid}_${selectedBrandId || 'all'}`;
    
    // Verificar cache
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      setInfluencers(cached.data);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = await firebaseUser.getIdToken();
      
      // Construir URL com filtros
      let url = '/api/brand-influencers';
      const params = new URLSearchParams();
      
      if (selectedBrandId) {
        params.append('brandId', selectedBrandId);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      console.log('[USE_BRAND_INFLUENCERS] Buscando associações:', url);

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.associations || !Array.isArray(data.associations)) {
        throw new Error('Estrutura de resposta inválida');
      }

      console.log('[USE_BRAND_INFLUENCERS] Associações encontradas:', data.associations.length);

      // Buscar dados detalhados dos influenciadores
      const influencerIds = data.associations.map((assoc: BrandInfluencer) => assoc.influencerId);
      const influencersData = await loadInfluencersData(influencerIds, token);

      // Combinar dados das associações com dados dos influenciadores
      const combinedData: InfluencerWithBrandData[] = data.associations
        .map((association: BrandInfluencer) => {
          const influencerData = influencersData[association.influencerId];
          
          if (!influencerData) {
            console.warn(`Influenciador ${association.influencerId} não encontrado`);
            return null;
          }

          return {
            ...influencerData,
            brandInfo: {
              brandId: association.brandId,
              brandName: association.brandName,
              brandLogo: association.brandLogo,
              associationDate: new Date(association.createdAt || Date.now()),
              tags: association.tags || []
            }
          };
        })
        .filter(Boolean);

      // Calcular estatísticas das marcas
      const brandStats = new Map<string, { name: string; logo?: string; count: number }>();
      
      data.associations.forEach((assoc: BrandInfluencer) => {
        const existing = brandStats.get(assoc.brandId);
        brandStats.set(assoc.brandId, {
          name: assoc.brandName,
          logo: assoc.brandLogo,
          count: (existing?.count || 0) + 1
        });
      });

      const brandsArray = Array.from(brandStats.entries()).map(([id, data]) => ({
        id,
        ...data
      }));

      setInfluencers(combinedData);
      setBrands(brandsArray);

      // Salvar no cache
      cache.set(cacheKey, { data: combinedData, timestamp: Date.now() });

      console.log(`[USE_BRAND_INFLUENCERS] ${combinedData.length} influenciadores carregados de ${brandsArray.length} marcas`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      console.error('[USE_BRAND_INFLUENCERS] Erro:', err);
      setError(errorMessage);
      setInfluencers([]);
      setBrands([]);
    } finally {
      setLoading(false);
    }
  }, [firebaseUser, selectedBrandId]);

  /**
   * Buscar dados detalhados dos influenciadores
   */
  const loadInfluencersData = async (influencerIds: string[], token: string) => {
    if (influencerIds.length === 0) return {};

    try {
      // Firestore "where in" suporta até 10 itens por query
      const batchSize = 10;
      const batches: string[][] = [];
      
      for (let i = 0; i < influencerIds.length; i += batchSize) {
        batches.push(influencerIds.slice(i, i + batchSize));
      }
      
      const influencersData: { [key: string]: any } = {};
      
      // Fazer queries em paralelo para cada batch
      const batchPromises = batches.map(async (batch) => {
        const idsParam = batch.join(',');
        const response = await fetch(`/api/influencers?ids=${idsParam}&includeFinancials=true`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          console.warn(`Erro ao carregar batch de influenciadores: ${batch.join(', ')}`);
          return;
        }
        
        const data = await response.json();
        if (data.success && Array.isArray(data.influencers)) {
          data.influencers.forEach((inf: any) => {
            if (inf.id) {
              influencersData[inf.id] = {
                id: inf.id,
                nome: inf.nome || inf.name,
                verificado: inf.verified,
                verified: inf.verified,
                categoria: inf.categoria,
                pais: inf.pais,
                cidade: inf.cidade,
                estado: inf.estado,
                idade: inf.idade,
                divulgaTrader: inf.divulgaTrader,
                genero: inf.genero,
                whatsapp: inf.whatsapp || inf.dadosFinanceiros?.whatsappFinanceiro,
                avatar: inf.avatar,
                email: inf.dadosFinanceiros?.emailFinanceiro || inf.email,
                dadosFinanceiros: inf.dadosFinanceiros,
                // Novos campos da API
                country: inf.country,
                age: inf.age,
                gender: inf.gender,
                category: inf.category,
                isVerified: inf.isVerified,
                location: inf.location,
                phone: inf.phone,
                mainNetwork: inf.mainNetwork,
                promotesTraders: inf.promotesTraders,
                responsibleName: inf.responsibleName,
                agencyName: inf.agencyName,
                engagementRate: inf.engagementRate,
                redesSociais: {
                  instagram: inf.socialNetworks?.instagram || inf.redesSociais?.instagram ? {
                    username: inf.socialNetworks?.instagram?.username || inf.redesSociais?.instagram?.username || '',
                    seguidores: inf.socialNetworks?.instagram?.followers || inf.redesSociais?.instagram?.seguidores || 0,
                    engajamento: inf.socialNetworks?.instagram?.avgViews || inf.redesSociais?.instagram?.engajamento || 0
                  } : undefined,
                  tiktok: inf.socialNetworks?.tiktok || inf.redesSociais?.tiktok ? {
                    username: inf.socialNetworks?.tiktok?.username || inf.redesSociais?.tiktok?.username || '',
                    seguidores: inf.socialNetworks?.tiktok?.followers || inf.redesSociais?.tiktok?.seguidores || 0,
                    curtidas: inf.socialNetworks?.tiktok?.avgViews || inf.redesSociais?.tiktok?.curtidas || 0
                  } : undefined,
                  youtube: inf.socialNetworks?.youtube || inf.redesSociais?.youtube ? {
                    username: inf.socialNetworks?.youtube?.username || inf.redesSociais?.youtube?.username || '',
                    seguidores: inf.socialNetworks?.youtube?.followers || inf.redesSociais?.youtube?.seguidores || 0,
                    visualizacoes: inf.socialNetworks?.youtube?.avgViews || inf.redesSociais?.youtube?.visualizacoes || 0
                  } : undefined
                }
              };
            }
          });
        }
      });
      
      await Promise.all(batchPromises);
      return influencersData;
      
    } catch (error) {
      console.error('Erro ao carregar dados dos influenciadores:', error);
      return {};
    }
  };

  /**
   * Adicionar influenciadores a uma lista
   */
  const addToList = useCallback(async (listId: string, influencerIds: string[]): Promise<boolean> => {
    if (!firebaseUser || influencerIds.length === 0) {
      return false;
    }

    try {
      const token = await firebaseUser.getIdToken();

      // Adicionar cada influenciador à lista
      const promises = influencerIds.map(async (influencerId) => {
        const response = await fetch(`/api/lists/${listId}/items`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            listaId: listId,
            itemId: influencerId,
            tipoItem: 'influenciador'
          }),
        });

        return response.ok;
      });

      const results = await Promise.all(promises);
      const successCount = results.filter(Boolean).length;

      console.log(`[USE_BRAND_INFLUENCERS] ${successCount}/${influencerIds.length} influenciadores adicionados à lista`);

      return successCount > 0;

    } catch (error) {
      console.error('[USE_BRAND_INFLUENCERS] Erro ao adicionar à lista:', error);
      return false;
    }
  }, [firebaseUser]);

  /**
   * Filtrar por marca
   */
  const filterByBrand = useCallback((brandId: string | null) => {
    setSelectedBrandId(brandId);
    // Limpar cache para forçar reload com novo filtro
    cache.clear();
  }, []);

  /**
   * Refresh manual
   */
  const refresh = useCallback(async () => {
    // Limpar cache para forçar reload
    cache.clear();
    await loadBrandInfluencers();
  }, [loadBrandInfluencers]);

  // Carregar dados automaticamente
  useEffect(() => {
    if (autoLoad && firebaseUser) {
      loadBrandInfluencers();
    }
  }, [autoLoad, firebaseUser, loadBrandInfluencers]);

  // Auto-refresh periódico
  useEffect(() => {
    if (!autoLoad || !firebaseUser || refreshInterval <= 0) return;

    const interval = setInterval(() => {
      if (!loading) {
        loadBrandInfluencers();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoLoad, firebaseUser, refreshInterval, loading, loadBrandInfluencers]);

  // Limpar dados ao fazer logout
  useEffect(() => {
    if (!firebaseUser) {
      setInfluencers([]);
      setBrands([]);
      setError(null);
      setLoading(false);
      setSelectedBrandId(null);
    }
  }, [firebaseUser]);

  return {
    influencers,
    brands,
    loading,
    error,
    totalCount: influencers.length,
    refresh,
    addToList,
    filterByBrand,
    selectedBrandId
  };
} 

