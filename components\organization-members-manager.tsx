'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Label } from './ui/label';
import { Separator } from './ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from './ui/alert-dialog';
import { UserPlus, Users, Mail, Shield, Crown, User, Trash2, Edit } from 'lucide-react';

interface Member {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'admin' | 'manager' | 'member';
  allRoles?: string[];
  joinedAt: string;
  status: string;
}

interface MemberStats {
  totalMembers: number;
  adminCount: number;
  managerCount: number;
  memberCount: number;
}

interface OrganizationMembersManagerProps {
  organizationId: string;
  organizationName: string;
  currentUserRole: string;
}

export function OrganizationMembersManager({ 
  organizationId, 
  organizationName, 
  currentUserRole 
}: OrganizationMembersManagerProps) {
  const [members, setMembers] = useState<Member[]>([]);
  const [stats, setStats] = useState<MemberStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'manager' | 'member'>('member');
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);

  const isAdmin = currentUserRole === 'admin';
  const canManageMembers = ['admin', 'manager'].includes(currentUserRole);

  // Carregar membros
  const loadMembers = async () => {
    if (!canManageMembers) return;
    
    setLoading(true);
    try {
      // TODO: Implementar carregamento de membros via Clerk
      console.log('🔍 Carregamento de membros temporariamente desabilitado');
      setMembers([]);
      setStats({
        totalMembers: 0,
        adminCount: 0,
        managerCount: 0,
        memberCount: 0
      });
    } catch (error) {
      console.error('❌ Erro ao carregar membros:', error);
    } finally {
      setLoading(false);
    }
  };

  // Convidar usuário
  const handleInvite = async () => {
    if (!inviteEmail.trim()) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/organizations/invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: inviteEmail.trim(),
          role: inviteRole,
          organizationId
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Usuário convidado:', data.invitation);
        setInviteEmail('');
        setInviteDialogOpen(false);
        await loadMembers(); // Recarregar lista
      } else {
        console.error('❌ Erro ao convidar usuário:', data.error);
        alert(data.error);
      }
    } catch (error) {
      console.error('❌ Erro ao convidar usuário:', error);
      alert('Erro interno do servidor');
    } finally {
      setLoading(false);
    }
  };

  // Alterar role de membro
  const handleChangeRole = async (memberId: string, newRole: 'admin' | 'manager' | 'member') => {
    setLoading(true);
    try {
      // TODO: Implementar alteração de role via Clerk
      console.log('🔍 Alteração de role temporariamente desabilitada', { memberId, newRole });
      alert('Funcionalidade temporariamente indisponível');
    } catch (error) {
      console.error('❌ Erro ao alterar role:', error);
      alert('Erro interno do servidor');
    } finally {
      setLoading(false);
    }
  };

  // Remover membro
  const handleRemoveMember = async (memberId: string) => {
    setLoading(true);
    try {
      // TODO: Implementar remoção de membro via Clerk
      console.log('🔍 Remoção de membro temporariamente desabilitada', { memberId });
      alert('Funcionalidade temporariamente indisponível');
    } catch (error) {
      console.error('❌ Erro ao remover membro:', error);
      alert('Erro interno do servidor');
    } finally {
      setLoading(false);
    }
  };

  // Carregar membros na montagem
  useEffect(() => {
    loadMembers();
  }, [organizationId]);

  // Função para obter ícone do role
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Crown className="w-4 h-4" />;
      case 'manager': return <Shield className="w-4 h-4" />;
      case 'member': return <User className="w-4 h-4" />;
      default: return <User className="w-4 h-4" />;
    }
  };

  // Função para obter cor do role
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-[#ff0074] text-white';
      case 'manager': return 'bg-[#9810fa] text-white';
      case 'member': return 'bg-gray-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  // Função para obter nome do role
  const getRoleName = (role: string) => {
    switch (role) {
      case 'admin': return 'Agência';
      case 'manager': return 'Manager';
      case 'member': return 'Membro';
      default: return role;
    }
  };

  if (!canManageMembers) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Membros da Organização
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Você não tem permissão para visualizar os membros desta organização.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com estatísticas */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Membros - {organizationName}
              </CardTitle>
              {stats && (
                <p className="text-muted-foreground">
                  {stats.totalMembers} membros • {stats.adminCount} admins • {stats.managerCount} managers
                </p>
              )}
            </div>
            
            {isAdmin && (
              <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#ff0074] hover:bg-[#ff0074]/90 text-white">
                    <UserPlus className="w-4 h-4 mr-2" />
                    Convidar Membro
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Convidar Novo Membro</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={inviteEmail}
                        onChange={(e) => setInviteEmail(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="role">Role</Label>
                      <Select value={inviteRole} onValueChange={(value: 'manager' | 'member') => setInviteRole(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="manager">Manager</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        onClick={handleInvite} 
                        disabled={loading || !inviteEmail.trim()}
                        className="bg-[#ff0074] hover:bg-[#ff0074]/90 text-white"
                      >
                        <Mail className="w-4 h-4 mr-2" />
                        Enviar Convite
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => setInviteDialogOpen(false)}
                      >
                        Cancelar
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Lista de membros */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Membros</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-center text-muted-foreground">Carregando membros...</p>
          ) : members.length === 0 ? (
            <p className="text-center text-muted-foreground">Nenhum membro encontrado.</p>
          ) : (
            <div className="space-y-4">
              {members.map((member) => (
                <div 
                  key={member.id} 
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-[#ff0074]/10 rounded-full flex items-center justify-center">
                      <span className="text-[#ff0074] font-semibold">
                        {(member.firstName?.[0] || member.email[0]).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">
                        {member.firstName && member.lastName 
                          ? `${member.firstName} ${member.lastName}` 
                          : member.email}
                      </p>
                      <p className="text-sm text-muted-foreground">{member.email}</p>
                      <p className="text-xs text-muted-foreground">
                        Membro desde {new Date(member.joinedAt).toLocaleDateString('pt-BR')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Badge className={getRoleColor(member.role)}>
                      {getRoleIcon(member.role)}
                      <span className="ml-1">{getRoleName(member.role)}</span>
                    </Badge>
                    
                    {isAdmin && (
                      <div className="flex gap-2">
                        <Select 
                          value={member.role} 
                          onValueChange={(value: 'admin' | 'manager' | 'member') => 
                            handleChangeRole(member.id, value)
                          }
                        >
                          <SelectTrigger className="w-[120px]">
                            <Edit className="w-4 h-4" />
                          </SelectTrigger>
                          <SelectContent>
                                                            <SelectItem value="admin">Agência</SelectItem>
                            <SelectItem value="manager">Manager</SelectItem>
                            <SelectItem value="member">Member</SelectItem>
                          </SelectContent>
                        </Select>
                        
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Remover membro?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Tem certeza que deseja remover {member.email} da organização? 
                                Esta ação não pode ser desfeita.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => handleRemoveMember(member.id)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Remover
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 


