import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db";

export async function GET() {
  try {
    // Buscar todos os filtros salvos
    const filters = await db.filter.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(filters);
  } catch (error) {
    console.error("Erro ao buscar filtros:", error);
    return NextResponse.json(
      { error: "Erro ao buscar filtros" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Obter dados do corpo da requisição
    const data = await request.json();

    // Validar dados mínimos
    if (!data.name) {
      return NextResponse.json(
        { error: "Nome do filtro é obrigatório" },
        { status: 400 }
      );
    }

    // Salvar filtro no banco de dados
    const filter = await db.filter.create({
      data: {
        name: data.name,
        location: data.location || "",
        minFollowers: data.minFollowers || 0,
        maxFollowers: data.maxFollowers || 10000000,
        minRating: data.minRating || 0,
        verifiedOnly: data.verifiedOnly || false,
        availableOnly: data.availableOnly || false,
        platforms: data.platforms || {},
        userId: "system", // Sistema genérico
      },
    });

    return NextResponse.json(filter);
  } catch (error) {
    console.error("Erro ao salvar filtro:", error);
    return NextResponse.json(
      { error: "Erro ao salvar filtro" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {

    // Obter ID do filtro da URL
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "ID do filtro não fornecido" },
        { status: 400 }
      );
    }

    // Verificar se o filtro pertence ao usuário atual
    const filter = await db.filter.findUnique({
      where: { id },
    });

    if (!filter || filter.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Filtro não encontrado ou acesso negado" },
        { status: 404 }
      );
    }

    // Deletar o filtro
    await db.filter.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao excluir filtro:", error);
    return NextResponse.json(
      { error: "Erro ao excluir filtro" },
      { status: 500 }
    );
  }
}


