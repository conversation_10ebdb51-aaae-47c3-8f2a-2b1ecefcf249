'use client';

import React, { useState, useEffect, Suspense } from 'react';

// Força renderização dinâmica para evitar problemas de SSG
export const dynamic = 'force-dynamic';
import { useSearchParams, usePathname } from 'next/navigation';
import { SideMenu } from '@/components/ui/sidemenu';
import { toast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';



function MarcasLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const brandId = searchParams.get('brandId') || searchParams.get('brand');
  
  const [activeSection, setActiveSection] = useState('influencers');

  // Determinar seção ativa baseada na URL
  useEffect(() => {
    if (pathname.includes('/grupos')) {
      setActiveSection('groups');
    } else if (pathname.includes('/propostas')) {
      setActiveSection('proposals');
    } else if (pathname.includes('/campanhas')) {
      setActiveSection('campaigns');
    } else if (pathname.includes('/dashboard')) {
      setActiveSection('influencers');
    } else {
      setActiveSection('influencers');
    }
  }, [pathname]);



  // Função para navegar entre seções
  const handleSectionChange = (section: string) => {
    const currentUrl = new URL(window.location.href);
    const brandId = currentUrl.searchParams.get('brandId') || currentUrl.searchParams.get('brand');
    const brandName = currentUrl.searchParams.get('brandName');
    
    let targetUrl = '';
    const params = new URLSearchParams();
    
    if (brandId) params.set('brandId', brandId);
    if (brandName) params.set('brandName', brandName);
    
    switch (section) {
      case 'influencers':
        targetUrl = '/campanhas/dashboard';
        break;
      case 'groups':
        targetUrl = '/campanhas/grupos';
        break;
      case 'proposals':
        targetUrl = '/propostas';
        break;
      case 'campaigns':
        targetUrl = '/campanhas/campanhas';
        break;
      case 'overview':
        targetUrl = '/campanhas/overview';
        break;
      case 'brands':
        targetUrl = '/campanhas/marcas';
        break;
      case 'tags':
        targetUrl = '/campanhas/tags';
        break;
      case 'notes':
        targetUrl = '/campanhas/notes';
        break;
      case 'settings':
        targetUrl = '/campanhas/configuracoes';
        break;
      default:
        targetUrl = '/campanhas/dashboard';
    }
    
    if (params.toString()) {
      targetUrl += '?' + params.toString();
    }
    
    window.location.href = targetUrl;
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Side Menu */}
      <SideMenu 
        className="flex-shrink-0"
        onSectionChange={handleSectionChange}
        activeSection={activeSection}
      />
      
      {/* Main Content */}
      <div className="flex-1 bg-muted/30 overflow-hidden">
        {children}
      </div>
    </div>
  );
}

export default function MarcasLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
          <p className="text-muted-foreground">Carregando layout...</p>
        </div>
      </div>
    }>
      <MarcasLayoutContent>
        {children}
      </MarcasLayoutContent>
    </Suspense>
  );
} 


