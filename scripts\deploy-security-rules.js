#!/usr/bin/env node

/**
 * 🚀 SCRIPT DE DEPLOY PARA FIREBASE SECURITY RULES
 * 
 * Este script faz o deploy seguro das regras de segurança do Firestore
 * FASE 3: Deploy automatizado com validações e backup
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Configurações do deploy
const CONFIG = {
  projectId: process.env.FIREBASE_PROJECT_ID || 'deumatch-demo',
  rulesFile: 'firestore.rules',
  backupDir: 'backups/firestore-rules',
  deployTimeout: 120000, // 2 minutos
  colors: {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
  }
};

// Interface readline para input do usuário
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Funções auxiliares
function log(message, color = 'reset') {
  console.log(`${CONFIG.colors[color]}${message}${CONFIG.colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logStep(step, message) {
  log(`${CONFIG.colors.bold}[${step}]${CONFIG.colors.reset} ${message}`, 'cyan');
}

// Fazer pergunta ao usuário
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim().toLowerCase());
    });
  });
}

// Executar comando Firebase CLI
function runFirebaseCommand(args, timeout = CONFIG.deployTimeout) {
  return new Promise((resolve, reject) => {
    logInfo(`Executando: firebase ${args.join(' ')}`);
    
    const firebase = spawn('firebase', args, {
      stdio: 'inherit',
      shell: true
    });

    firebase.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Firebase CLI falhou com código: ${code}`));
      }
    });

    firebase.on('error', (error) => {
      reject(new Error(`Erro ao executar Firebase CLI: ${error.message}`));
    });

    // Timeout
    setTimeout(() => {
      firebase.kill();
      reject(new Error('Firebase CLI timeout'));
    }, timeout);
  });
}

// Verificar pré-requisitos
async function checkPrerequisites() {
  logStep('1/7', 'Verificando pré-requisitos...');

  // Verificar se Firebase CLI está instalado
  try {
    await runFirebaseCommand(['--version'], 5000);
    logSuccess('Firebase CLI está instalado');
  } catch (error) {
    logError('Firebase CLI não encontrado. Instale com: npm install -g firebase-tools');
    return false;
  }

  // Verificar se está logado
  try {
    await runFirebaseCommand(['projects:list'], 10000);
    logSuccess('Usuário autenticado no Firebase');
  } catch (error) {
    logError('Não autenticado. Execute: firebase login');
    return false;
  }

  // Verificar arquivo de regras
  if (!fs.existsSync(CONFIG.rulesFile)) {
    logError(`Arquivo de regras não encontrado: ${CONFIG.rulesFile}`);
    return false;
  }
  logSuccess(`Arquivo de regras encontrado: ${CONFIG.rulesFile}`);

  // Verificar firebase.json
  if (!fs.existsSync('firebase.json')) {
    logError('Arquivo firebase.json não encontrado');
    return false;
  }
  logSuccess('Arquivo firebase.json encontrado');

  return true;
}

// Fazer backup das regras atuais
async function backupCurrentRules() {
  logStep('2/7', 'Fazendo backup das regras atuais...');

  try {
    // Criar diretório de backup se não existir
    if (!fs.existsSync(CONFIG.backupDir)) {
      fs.mkdirSync(CONFIG.backupDir, { recursive: true });
    }

    // Obter regras atuais do Firebase
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(CONFIG.backupDir, `firestore-rules-${timestamp}.rules`);

    try {
      await runFirebaseCommand(['firestore:rules:get', '--project', CONFIG.projectId, '--output', backupFile]);
      logSuccess(`Backup salvo em: ${backupFile}`);
      return backupFile;
    } catch (error) {
      logWarning('Não foi possível fazer backup das regras atuais (pode ser o primeiro deploy)');
      return null;
    }

  } catch (error) {
    logError(`Erro ao fazer backup: ${error.message}`);
    throw error;
  }
}

// Validar sintaxe das novas regras
async function validateRules() {
  logStep('3/7', 'Validando sintaxe das regras...');

  try {
    const rulesContent = fs.readFileSync(CONFIG.rulesFile, 'utf8');

    // Verificações básicas de sintaxe
    const checks = [
      {
        name: 'Versão das regras',
        test: () => rulesContent.includes("rules_version = '2'"),
        critical: true
      },
      {
        name: 'Estrutura do service',
        test: () => rulesContent.includes('service cloud.firestore'),
        critical: true
      },
      {
        name: 'Match statements',
        test: () => rulesContent.includes('match /databases/{database}/documents'),
        critical: true
      },
      {
        name: 'Funções de segurança',
        test: () => rulesContent.includes('function isAuthenticated()'),
        critical: false
      },
      {
        name: 'Regra de negação padrão',
        test: () => rulesContent.includes('allow read, write: if false'),
        critical: false
      }
    ];

    let hasErrors = false;

    for (const check of checks) {
      if (check.test()) {
        logSuccess(`   ${check.name}`);
      } else {
        if (check.critical) {
          logError(`   ${check.name} - CRÍTICO`);
          hasErrors = true;
        } else {
          logWarning(`   ${check.name} - Recomendado`);
        }
      }
    }

    if (hasErrors) {
      throw new Error('Erros críticos de sintaxe encontrados');
    }

    logSuccess('Validação de sintaxe concluída');
    return true;

  } catch (error) {
    logError(`Erro na validação: ${error.message}`);
    throw error;
  }
}

// Executar testes de segurança
async function runSecurityTests() {
  logStep('4/7', 'Executando testes de segurança...');

  try {
    // Verificar se script de teste existe
    const testScript = 'scripts/test-security-rules.js';
    
    if (fs.existsSync(testScript)) {
      const { runSecurityTests, validateRulesStructure } = require('../' + testScript);
      
      // Validar estrutura
      const structureValid = validateRulesStructure();
      if (!structureValid) {
        throw new Error('Estrutura das regras inválida');
      }

      // Executar testes
      const testsPass = await runSecurityTests();
      if (!testsPass) {
        throw new Error('Testes de segurança falharam');
      }

      logSuccess('Todos os testes de segurança passaram');
    } else {
      logWarning('Script de testes não encontrado, pulando testes automáticos');
    }

    return true;

  } catch (error) {
    logError(`Erro nos testes: ${error.message}`);
    throw error;
  }
}

// Mostrar resumo das mudanças
function showChangesSummary() {
  logStep('5/7', 'Resumo das mudanças:');

  const rulesContent = fs.readFileSync(CONFIG.rulesFile, 'utf8');
  
  // Contar regras por coleção
  const collections = [
    'users', 'brands', 'campaigns', 'influencers', 
    'notes', 'tags', 'groups', 'proposals', 
    'filters', 'categories', 'brand_influencers'
  ];

  const rulesCount = collections.reduce((count, collection) => {
    if (rulesContent.includes(`match /${collection}/`)) {
      return count + 1;
    }
    return count;
  }, 0);

  log(`   📋 Coleções com regras: ${rulesCount}/${collections.length}`);
  log(`   🔒 Funções de segurança: ${(rulesContent.match(/function /g) || []).length}`);
  log(`   📏 Tamanho do arquivo: ${Math.round(rulesContent.length / 1024)} KB`);
  
  // Verificar regras específicas
  if (rulesContent.includes('canAccessDocument()')) {
    logSuccess('   ✓ Isolamento por usuário implementado');
  }
  
  if (rulesContent.includes('brandBelongsToUser(')) {
    logSuccess('   ✓ Validação de relacionamentos implementada');
  }
  
  if (rulesContent.includes('allow read, write: if false')) {
    logSuccess('   ✓ Regra de negação padrão ativa');
  }
}

// Confirmar deploy
async function confirmDeploy() {
  logStep('6/7', 'Confirmação de deploy');
  
  logWarning('⚠️  ATENÇÃO: Este deploy irá substituir as regras de segurança em produção!');
  logInfo(`Projeto: ${CONFIG.projectId}`);
  logInfo(`Arquivo: ${CONFIG.rulesFile}`);
  
  const confirm = await askQuestion('\nDeseja continuar com o deploy? (sim/não): ');
  
  if (confirm !== 'sim' && confirm !== 's' && confirm !== 'yes' && confirm !== 'y') {
    logInfo('Deploy cancelado pelo usuário');
    return false;
  }
  
  return true;
}

// Executar deploy
async function executeDeploy() {
  logStep('7/7', 'Executando deploy...');

  try {
    logInfo('Iniciando deploy das regras de segurança...');
    
    await runFirebaseCommand([
      'deploy',
      '--only', 'firestore:rules',
      '--project', CONFIG.projectId
    ]);

    logSuccess('Deploy concluído com sucesso!');
    return true;

  } catch (error) {
    logError(`Erro durante o deploy: ${error.message}`);
    throw error;
  }
}

// Verificar deploy
async function verifyDeploy() {
  logInfo('Verificando deploy...');

  try {
    // Esperar um pouco para as regras serem aplicadas
    await new Promise(resolve => setTimeout(resolve, 5000));

    logSuccess('Deploy verificado com sucesso!');
    
    logInfo('\n📝 Próximos passos:');
    logInfo('   1. Teste as regras em ambiente de desenvolvimento');
    logInfo('   2. Monitore os logs de segurança no Firebase Console');
    logInfo('   3. Verifique se todas as funcionalidades estão funcionando');
    logInfo('\n📊 Monitoramento:');
    logInfo(`   • Console: https://console.firebase.google.com/project/${CONFIG.projectId}/firestore/rules`);
    logInfo(`   • Logs: https://console.firebase.google.com/project/${CONFIG.projectId}/firestore/usage`);

  } catch (error) {
    logWarning(`Erro na verificação: ${error.message}`);
  }
}

// Função principal
async function main() {
  let backupFile = null;

  try {
    log(`${CONFIG.colors.bold}🚀 DEPLOY DE FIREBASE SECURITY RULES${CONFIG.colors.reset}`, 'cyan');
    log(`Projeto: ${CONFIG.projectId}\n`);

    // 1. Verificar pré-requisitos
    const prerequisitesOk = await checkPrerequisites();
    if (!prerequisitesOk) {
      process.exit(1);
    }

    // 2. Fazer backup
    backupFile = await backupCurrentRules();

    // 3. Validar sintaxe
    await validateRules();

    // 4. Executar testes
    await runSecurityTests();

    // 5. Mostrar resumo
    showChangesSummary();

    // 6. Confirmar deploy
    const confirmed = await confirmDeploy();
    if (!confirmed) {
      process.exit(0);
    }

    // 7. Executar deploy
    await executeDeploy();

    // Verificar deploy
    await verifyDeploy();

    logSuccess('\n🎉 Deploy concluído com sucesso!');

  } catch (error) {
    logError(`\n💥 Deploy falhou: ${error.message}`);
    
    if (backupFile) {
      logInfo(`\n🔄 Para restaurar o backup:`);
      logInfo(`   firebase firestore:rules:release ${backupFile} --project ${CONFIG.projectId}`);
    }
    
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = {
  checkPrerequisites,
  validateRules,
  runSecurityTests,
  executeDeploy
}; 
