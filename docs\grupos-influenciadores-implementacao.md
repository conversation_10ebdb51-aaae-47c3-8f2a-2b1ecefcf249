# Sistema de Grupos de Influenciadores - Plano de Implementação

## Visão Geral
Sistema completo para criação, gerenciamento e envio de grupos de influenciadores para marcas, permitindo visualização organizada de perfis e valores de serviços.

## Arquitetura do Sistema

### Fluxo Principal
1. **Criação de Grupos** → Admin seleciona influenciadores e cria grupos temáticos
2. **Gerenciamento de Grupos** → Página dedicada para visualizar e editar grupos
3. **Envio para Marcas** → Grupos são compartilhados com marcas específicas
4. **Visualização da Marca** → Marcas veem grupos organizados com preços e serviços

---

## FASE 1: Estrutura Base e Navegação

### 1.1 Atualização do SideMenu
**Arquivo:** `components/ui/sidemenu.tsx`

**Implementações:**
- [x] ✅ Adicionar seção "Workspace" no menu
- [x] ✅ Criar subitem "Grupos de Influenciadores" 
- [ ] 🔄 Implementar contador de grupos ativos
- [ ] 🔄 Adicionar indicador visual para grupos com atualizações

**Estrutura de Navegação:**
```
📁 Workspace
  └── 👥 Grupos de Influenciadores (3)
  └── 📊 Relatórios
  └── ⚙️ Configurações
```

### 1.2 Roteamento
**Arquivos a criar:**
- `app/workspace/grupos/page.tsx` - Lista de grupos
- `app/workspace/grupos/[id]/page.tsx` - Detalhes do grupo
- `app/workspace/grupos/[id]/edit/page.tsx` - Edição do grupo

---

## FASE 2: Página Principal de Grupos

### 2.1 Interface da Lista de Grupos
**Arquivo:** `app/workspace/grupos/page.tsx`

**Funcionalidades:**
- [x] ✅ Grid responsivo de cards de grupos
- [ ] 🔄 Filtros por status, data de criação, marca associada
- [ ] 🔄 Busca por nome do grupo ou influenciadores
- [ ] 🔄 Ordenação (mais recentes, alfabética, por quantidade)
- [ ] 🔄 Paginação para grandes volumes

**Layout do Card:**
```tsx
Card de Grupo:
├── Header: Nome + Badge de Status
├── Metrics: Qtd Influenciadores + Alcance Total
├── Preview: Avatares dos 5 primeiros influenciadores
├── Footer: Data de Criação + Ações
└── Actions: Ver Detalhes | Editar | Enviar para Marca
```

### 2.2 Estados e Filtros
**Estados dos Grupos:**
- `draft` - Rascunho (cinza)
- `active` - Ativo (verde)
- `sent` - Enviado para marca (azul)
- `archived` - Arquivado (vermelho)

---

## FASE 3: Página de Detalhes do Grupo

### 3.1 Layout Principal
**Arquivo:** `app/workspace/grupos/[id]/page.tsx`

**Seções:**
1. **Header do Grupo**
   - Nome, descrição, status
   - Métricas agregadas (alcance total, engajamento médio)
   - Botões de ação (Editar, Duplicar, Arquivar, Enviar)

2. **Tabela de Influenciadores**
   - Lista completa com dados detalhados
   - Colunas customizáveis
   - Exportação para Excel/PDF

3. **Painel de Preços**
   - Tabela de serviços e valores
   - Cálculo automático de pacotes
   - Descontos por volume

### 3.2 Dados dos Influenciadores
**Colunas da Tabela:**
```tsx
- Avatar + Nome + Verificação
- Localização (País/Estado/Cidade)
- Categoria Principal
- Instagram (Seguidores + Engajamento)
- YouTube (Seguidores + Visualizações)
- TikTok (Seguidores + Curtidas)
- Preços dos Serviços (Stories, Reels, Posts, Vídeos)
- Status na Marca (se enviado)
- Ações (Ver Perfil, Remover do Grupo)
```

---

## FASE 4: Sistema de Preços e Serviços

### 4.1 Estrutura de Serviços
**Arquivo:** `types/services.ts`

```typescript
interface Service {
  id: string;
  name: string;
  platform: 'instagram' | 'youtube' | 'tiktok' | 'twitter';
  type: 'post' | 'story' | 'reel' | 'video' | 'short' | 'live';
  description: string;
  duration?: number; // para stories/lives
  format?: string; // aspectos técnicos
}

interface InfluencerPrice {
  influencerId: string;
  serviceId: string;
  basePrice: number;
  negotiablePrice?: number;
  discountTiers: {
    quantity: number;
    discountPercent: number;
  }[];
}
```

### 4.2 Calculadora de Preços
**Funcionalidades:**
- [ ] 🔄 Cálculo automático por quantidade de serviços
- [ ] 🔄 Aplicação de descontos progressivos
- [ ] 🔄 Comparação de preços entre influenciadores
- [ ] 🔄 Sugestão de pacotes otimizados

---

## FASE 5: Sistema de Envio para Marcas

### 5.1 Interface de Envio
**Componente:** `components/groups/send-to-brand-dialog.tsx`

**Fluxo:**
1. Seleção da marca de destino
2. Customização da mensagem de apresentação
3. Seleção de serviços a incluir
4. Configuração de preços especiais
5. Agendamento de envio (opcional)

### 5.2 API de Envio
**Arquivo:** `app/api/groups/[id]/send/route.ts`

**Funcionalidades:**
- [ ] 🔄 Validação de dados completos
- [ ] 🔄 Criação de cópia do grupo para a marca
- [ ] 🔄 Notificação automática para a marca
- [ ] 🔄 Log de envios e interações

### 5.3 Histórico de Envios
**Tracking:**
- Data/hora do envio
- Marca de destino
- Status (enviado, visualizado, respondido)
- Interações da marca
- Conversões realizadas

---

## FASE 6: Dashboard para Marcas

### 6.1 Visualização dos Grupos Recebidos
**Arquivo:** `app/brand-dashboard/grupos/page.tsx`

**Funcionalidades:**
- [ ] 🔄 Lista de grupos recebidos
- [ ] 🔄 Filtros por status, data, categoria
- [ ] 🔄 Preview rápido dos influenciadores
- [ ] 🔄 Comparação entre grupos

### 6.2 Detalhes do Grupo para Marca
**Arquivo:** `app/brand-dashboard/grupos/[id]/page.tsx`

**Seções:**
1. **Resumo Executivo**
   - Alcance total estimado
   - Demografia da audiência agregada
   - Investimento total estimado

2. **Perfis dos Influenciadores**
   - Cards detalhados com métricas
   - Dados demográficos da audiência
   - Histórico de campanhas (se disponível)

3. **Calculadora de Campanha**
   - Seleção de serviços desejados
   - Cálculo automático de custos
   - Simulação de resultados

### 6.3 Sistema de Resposta
**Funcionalidades:**
- [ ] 🔄 Marcação de interesse em influenciadores específicos
- [ ] 🔄 Solicitação de orçamentos personalizados
- [ ] 🔄 Agendamento de reuniões
- [ ] 🔄 Chat direto com o admin

---

## FASE 7: Analytics e Relatórios

### 7.1 Dashboard de Performance
**Métricas dos Grupos:**
- Taxa de abertura pelas marcas
- Tempo médio de resposta
- Taxa de conversão em propostas
- ROI dos grupos criados

### 7.2 Relatórios Avançados
**Tipos de Relatório:**
- Performance por categoria de influenciador
- Análise de preços e competitividade
- Tendências de mercado
- Eficácia por tipo de serviço

---

## FASE 8: Otimizações e Recursos Avançados

### 8.1 Sistema de Recomendações
- [ ] 🔄 Sugestão automática de grupos baseada em campanhas anteriores
- [ ] 🔄 Recomendação de influenciadores para completar grupos
- [ ] 🔄 Otimização de preços por mercado

### 8.2 Automações
- [ ] 🔄 Criação automática de grupos por critérios
- [ ] 🔄 Envio agendado de grupos
- [ ] 🔄 Follow-up automático com marcas
- [ ] 🔄 Alertas de oportunidades

### 8.3 Integrações
- [ ] 🔄 Exportação para ferramentas de gestão de campanhas
- [ ] 🔄 Integração com redes sociais para dados em tempo real
- [ ] 🔄 API pública para parceiros

---

## Estrutura de Arquivos Final

```
app/
├── workspace/
│   └── grupos/
│       ├── page.tsx                    # Lista de grupos
│       ├── [id]/
│       │   ├── page.tsx               # Detalhes do grupo
│       │   ├── edit/page.tsx          # Edição do grupo
│       │   └── analytics/page.tsx     # Analytics do grupo
│       └── novo/page.tsx              # Criação de novo grupo
├── brand-dashboard/
│   └── grupos/
│       ├── page.tsx                   # Grupos recebidos pela marca
│       └── [id]/page.tsx              # Detalhes do grupo para marca
└── api/
    └── groups/
        ├── route.ts                   # CRUD básico ✅
        ├── [id]/
        │   ├── route.ts              # Operações específicas
        │   ├── send/route.ts         # Envio para marcas
        │   ├── analytics/route.ts    # Dados de performance
        │   └── duplicate/route.ts    # Duplicação de grupo
        └── recommendations/route.ts   # Sistema de recomendações

components/
├── groups/
│   ├── group-card.tsx               # Card do grupo na listagem
│   ├── group-header.tsx             # Header com info e ações
│   ├── influencers-table.tsx        # Tabela de influenciadores
│   ├── price-calculator.tsx         # Calculadora de preços
│   ├── send-to-brand-dialog.tsx     # Modal de envio
│   ├── group-analytics.tsx          # Componente de analytics
│   └── filters/
│       ├── group-filters.tsx        # Filtros da listagem
│       └── advanced-search.tsx      # Busca avançada
└── brand-dashboard/
    ├── group-preview.tsx            # Preview para marcas
    ├── campaign-calculator.tsx      # Calculadora para marcas
    └── response-system.tsx          # Sistema de resposta

types/
├── group.ts ✅                      # Interfaces básicas
├── services.ts                      # Tipos de serviços
├── pricing.ts                       # Sistema de preços
└── analytics.ts                     # Métricas e relatórios
```

---

## Cronograma Sugerido

### Sprint 1 (1-2 semanas) - Fundação
- [x] ✅ FASE 1: Estrutura base e navegação
- [x] ✅ API básica de grupos
- [ ] 🔄 FASE 2: Página principal de grupos

### Sprint 2 (2-3 semanas) - Core Features  
- [ ] 🔄 FASE 3: Página de detalhes
- [ ] 🔄 FASE 4: Sistema de preços

### Sprint 3 (2-3 semanas) - Integração com Marcas
- [ ] 🔄 FASE 5: Sistema de envio
- [ ] 🔄 FASE 6: Dashboard para marcas

### Sprint 4 (1-2 semanas) - Analytics e Polimento
- [ ] 🔄 FASE 7: Analytics e relatórios
- [ ] 🔄 Testes e refinamentos

### Sprint 5 (2-3 semanas) - Recursos Avançados
- [ ] 🔄 FASE 8: Otimizações e automações

---

## Considerações Técnicas

### Performance
- Virtualização para tabelas grandes
- Cache de dados de influenciadores
- Lazy loading de imagens e componentes
- Otimização de queries no Firestore

### Segurança
- Validação de permissões por usuário
- Criptografia de dados sensíveis
- Audit log de todas as operações
- Rate limiting nas APIs

### UX/UI
- Design system consistente
- Responsividade total
- Dark/Light mode
- Acessibilidade (WCAG 2.1)
- Loading states e error handling

### Escalabilidade
- Arquitetura modular
- APIs RESTful bem documentadas
- Testes automatizados
- CI/CD pipeline
- Monitoramento e logs

---

## Próximos Passos Imediatos

1. **Implementar FASE 2**: Criar a página principal de listagem de grupos
2. **Atualizar SideMenu**: Adicionar navegação para grupos com contador
3. **Criar componentes base**: Group cards, filtros e tabelas
4. **Expandir API**: Adicionar endpoints para listagem e filtros
5. **Implementar sistema de preços**: Base para cálculos de valores

**Prioridade:** Começar pela listagem de grupos para ter uma base sólida antes de implementar funcionalidades mais complexas. 