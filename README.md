# Influencer Dashboard

## Descrição

Sistema de gestão de influenciadores com interface moderna, focado em segurança e escalabilidade.

## Principais Funcionalidades

- **Gestão de Influenciadores**: Interface completa para gerenciar perfis de creators
- **Sistema de Orçamentos**: Criação e gestão de orçamentos hierárquicos por proposta
- **Contrapropostas**: Sistema de negociação entre marcas e influenciadores
- **Dashboard Analytics**: Dados demográficos e métricas de performance
- **Multi-tema**: Suporte completo para modo claro e escuro

## Tecnologias Principais

- **Next.js 14** com App Router
- **TypeScript** para tipagem forte
- **Tailwind CSS v4.0** para estilização
- **Shadcn UI** como base de componentes
- **Firebase** para backend e autenticação
- **Framer Motion** para animações

## Arquitetura

### Estrutura de Dados Hierárquica
```
proposals/{proposalId}/influencers/{influencerId}/budgets/{budgetId}
```

### Componentes Principais
- `InfluencerGrid`: Grade principal de influenciadores
- `BudgetSystem`: Sistema de orçamentos e contrapropostas
- `DemographicsCharts`: Visualizações de dados da audiência

## Segurança

- Validação rigorosa em todas as operações
- Controle de acesso granular por usuário
- Sanitização de dados de entrada
- Auditoria completa de ações sensíveis

## Configuração

1. Configure as variáveis de ambiente no `.env`
2. Execute `npm install` para instalar dependências
3. Execute `npm run dev` para iniciar o servidor de desenvolvimento

## Estado do Projeto

✅ Sistema de orçamentos implementado  
✅ Interface de usuário moderna  
✅ Dados demográficos e analytics  
🔄 Otimizações de performance em andamento 