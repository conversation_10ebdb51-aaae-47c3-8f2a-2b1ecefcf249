import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';
import { db } from '@/lib/firebase-admin';
import { SnapshotFilterService } from '@/lib/snapshot-filter-service';
import { nanoid } from 'nanoid';

// GET - Listar colaboradores e convites pendentes
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    
    // Buscar compartilhamentos ativos da proposta
    const sharings = await ProposalService.getProposalSharings(proposalId, userId);

    return NextResponse.json({
      success: true,
      data: sharings
    });

  } catch (error) {
    console.error('Erro ao buscar compartilhamentos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

/**
 * Busca budgets específicos de um influenciador numa proposta
 */
async function fetchProposalBudgets(proposalId: string, influencerId: string) {
  try {
    console.log(`💰 [SNAPSHOT] Buscando budgets específicos: proposta ${proposalId}, influenciador ${influencerId}`);
    
    // Buscar orçamentos na subcoleção hierárquica: proposals/{proposalId}/influencers/{influencerId}/budgets
    const budgetsSnapshot = await db
      .collection('proposals')
      .doc(proposalId)
      .collection('influencers')
      .doc(influencerId)
      .collection('budgets')
      .get();
    
    if (budgetsSnapshot.empty) {
      console.log(`💰 [SNAPSHOT] Nenhum budget encontrado para proposta ${proposalId}, influenciador ${influencerId}`);
      return [];
    }

    const budgets = budgetsSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        influencerId: influencerId,
        influencerName: data.influencerName || '',
        userId: data.userId || '',
        brandId: data.brandId || '',
        amount: data.amount || 0,
        originalPrice: data.originalPrice || 0,
        currency: data.currency || 'BRL',
        description: data.description || '',
        serviceType: data.serviceType || 'personalizado',
        status: data.status || 'draft',
        hasCounterProposal: data.hasCounterProposal || false,
        createdAt: data.createdAt?.toDate?.() || new Date(data.createdAt || Date.now()),
        updatedAt: data.updatedAt?.toDate?.() || new Date(data.updatedAt || Date.now()),
        createdBy: data.createdBy || data.userId,
        
        // Metadados do snapshot
        capturedAt: new Date(),
        proposalId: proposalId
      };
    });

    console.log(`💰 [SNAPSHOT] ${budgets.length} budgets encontrados para ${influencerId}`);
    return budgets;
    
  } catch (error) {
    console.error(`❌ [SNAPSHOT] Erro ao buscar budgets para ${influencerId}:`, error);
    return [];
  }
}

/**
 * Captura snapshots dos influenciadores com estrutura de subcoleções idêntica a /influencers
 * Nova estrutura: proposal_sharings/{shareToken}/snapshots/{influencerId}/pricing + demographics + budgets
 */
async function captureInfluencerSnapshots(shareToken: string, proposalId: string, influencerIds: string[], userId: string, userRole: string = 'admin') {
  try {
    console.log('📸 [SNAPSHOT] Iniciando captura com estrutura de subcoleções...');
    
    const batch = db.batch();
    const capturedSnapshots: any[] = [];
    
    for (const influencerId of influencerIds) {
      try {
        console.log(`🔍 [SNAPSHOT] Processando influenciador: ${influencerId}`);
        
        // 1. Buscar dados principais do influenciador
        const influencerData = await fetchInfluencerCompleteData(influencerId);
        
        if (!influencerData) {
          console.log(`⚠️ [SNAPSHOT] Dados não encontrados para: ${influencerId}`);
          continue;
        }

        // 2. 💰 BUSCAR BUDGETS ESPECÍFICOS DA PROPOSTA
        const proposalBudgets = await fetchProposalBudgets(proposalId, influencerId);

        // 3. Criar documento principal do snapshot (dados básicos do influenciador)
        const mainSnapshotData = {
          // Dados básicos
          name: influencerData.name,
          email: influencerData.email,
          phone: influencerData.phone,
          whatsapp: influencerData.whatsapp,
          avatar: influencerData.avatar,
          bio: influencerData.bio,
          age: influencerData.age,
          gender: influencerData.gender,
          country: influencerData.country,
          state: influencerData.state,
          city: influencerData.city,
          location: influencerData.location,
          category: influencerData.category,
          categories: influencerData.categories,
          totalFollowers: influencerData.totalFollowers,
          engagementRate: influencerData.engagementRate,
          rating: influencerData.rating,
          isVerified: influencerData.isVerified,
          isAvailable: influencerData.isAvailable,
          status: influencerData.status,
          
          // 🔧 CORREÇÃO: Redes sociais como campos diretos (igual ao GraphQL)
          // Instagram
          instagramUsername: influencerData.instagramUsername || null,
          instagramFollowers: influencerData.instagramFollowers || 0,
          instagramEngagementRate: influencerData.instagramEngagementRate || 0,
          instagramAvgViews: influencerData.instagramAvgViews || 0,
          instagramStoriesViews: influencerData.instagramStoriesViews || 0,
          instagramReelsViews: influencerData.instagramReelsViews || 0,
          
          // TikTok
          tiktokUsername: influencerData.tiktokUsername || null,
          tiktokFollowers: influencerData.tiktokFollowers || 0,
          tiktokEngagementRate: influencerData.tiktokEngagementRate || 0,
          tiktokAvgViews: influencerData.tiktokAvgViews || 0,
          tiktokVideoViews: influencerData.tiktokVideoViews || 0,
          
          // YouTube
          youtubeUsername: influencerData.youtubeUsername || null,
          youtubeFollowers: influencerData.youtubeFollowers || 0,
          youtubeSubscribers: influencerData.youtubeSubscribers || 0,
          youtubeEngagementRate: influencerData.youtubeEngagementRate || 0,
          youtubeAvgViews: influencerData.youtubeAvgViews || 0,
          youtubeShortsViews: influencerData.youtubeShortsViews || 0,
          youtubeLongFormViews: influencerData.youtubeLongFormViews || 0,
          
          // Facebook
          facebookUsername: influencerData.facebookUsername || null,
          facebookFollowers: influencerData.facebookFollowers || 0,
          facebookEngagementRate: influencerData.facebookEngagementRate || 0,
          facebookAvgViews: influencerData.facebookAvgViews || 0,
          facebookViews: influencerData.facebookViews || 0,
          
          // Twitch
          twitchUsername: influencerData.twitchUsername || null,
          twitchFollowers: influencerData.twitchFollowers || 0,
          twitchEngagementRate: influencerData.twitchEngagementRate || 0,
          twitchViews: influencerData.twitchViews || 0,
          
          // Kwai
          kwaiUsername: influencerData.kwaiUsername || null,
          kwaiFollowers: influencerData.kwaiFollowers || 0,
          kwaiEngagementRate: influencerData.kwaiEngagementRate || 0,
          kwaiViews: influencerData.kwaiViews || 0,
          
          // Dados de responsabilidade
          promotesTraders: influencerData.promotesTraders,
          responsibleName: influencerData.responsibleName,
          agencyName: influencerData.agencyName,
          
          // Metadados do snapshot
          originalInfluencerId: influencerId,
          capturedAt: new Date(),
          createdAt: new Date(),
          version: '2.0', // Nova versão com subcoleções
          source: 'graphql',
          
          // 🔒 ISOLAMENTO POR USUÁRIO
          createdBy: userId,
          proposalId: proposalId,
          shareToken: shareToken
        };

        // Salvar documento principal
        const mainSnapshotRef = db.collection('proposal_sharings').doc(shareToken)
          .collection('snapshots').doc(influencerId);
        batch.set(mainSnapshotRef, mainSnapshotData);

        console.log(`✅ [SNAPSHOT] Documento principal criado para: ${influencerData.name}`);

        // 4. 🔒 PRICING NÃO É MAIS SALVO NO SNAPSHOT
        // Pricing deve permanecer privado e acessível apenas via APIs com validação
        if (influencerData.currentPricing) {
          console.log(`🔒 [SNAPSHOT] Pricing encontrado para ${influencerData.name}, mas NÃO será salvo no snapshot por questões de privacidade`);
          console.log(`🔒 [SNAPSHOT] Proprietário (${influencerData.currentPricing.userId}) terá acesso via API direta`);
        }

        // 5. 💰 SALVAR BUDGETS ESPECÍFICOS DA PROPOSTA NA SUBCOLEÇÃO
        if (proposalBudgets.length > 0) {
          for (let i = 0; i < proposalBudgets.length; i++) {
            const budget = proposalBudgets[i];
            const budgetSnapshotRef = mainSnapshotRef.collection('budgets').doc(budget.id || `budget_${i}`);
            
            // Filtrar valores undefined para evitar erro do Firestore
            const budgetData = {
              // Dados do budget preservados
              influencerId: budget.influencerId,
              influencerName: budget.influencerName,
              userId: budget.userId,
              brandId: budget.brandId,
              amount: budget.amount,
              originalPrice: budget.originalPrice,
              currency: budget.currency,
              description: budget.description,
              serviceType: budget.serviceType,
              status: budget.status,
              hasCounterProposal: budget.hasCounterProposal,
              createdAt: budget.createdAt,
              updatedAt: budget.updatedAt,
              createdBy: budget.createdBy,
              
              // Metadados do snapshot
              originalBudgetId: budget.id || '',
              capturedAt: new Date(),
              
              // Isolamento
              snapshotCreatedBy: userId,
              proposalId: proposalId,
              shareToken: shareToken
            };
           
            batch.set(budgetSnapshotRef, budgetData);
            console.log(`💰 [SNAPSHOT] Budget [${budget.serviceType}] capturado para: ${influencerData.name} (R$ ${budget.amount})`);
          }
        } else {
          console.log(`💰 [SNAPSHOT] Nenhum budget específico encontrado para: ${influencerData.name}`);
        }

        // 6. Capturar e salvar DEMOGRAPHICS na subcoleção (se existir)
        if (influencerData.currentDemographics && Array.isArray(influencerData.currentDemographics)) {
          for (let i = 0; i < influencerData.currentDemographics.length; i++) {
            const demographic = influencerData.currentDemographics[i];
            const demographicSnapshotRef = mainSnapshotRef.collection('demographics').doc(`${demographic.platform}_${i}`);
            
            // Filtrar valores undefined para evitar erro do Firestore
            const demographicData = {
              // Dados da demographic preservados
              userId: demographic.userId || userId,
              platform: demographic.platform || 'unknown',
              audienceGender: demographic.audienceGender || { male: 0, female: 0, other: 0 },
              audienceLocations: demographic.audienceLocations || [],
              audienceCities: demographic.audienceCities || [],
              audienceAgeRange: demographic.audienceAgeRange || {},
              captureDate: demographic.captureDate ? new Date(demographic.captureDate) : new Date(),
              isActive: demographic.isActive ?? true,
              source: demographic.source || 'unknown',
              
              // Metadados do snapshot
              originalDemographicId: demographic.id || '',
              capturedAt: new Date(),
              createdAt: demographic.createdAt ? new Date(demographic.createdAt) : new Date(),
              createdBy: demographic.createdBy || userId,
              updatedAt: demographic.updatedAt ? new Date(demographic.updatedAt) : new Date(),
              updatedBy: demographic.updatedBy || userId,
              
              // Isolamento
              snapshotCreatedBy: userId,
              proposalId: proposalId,
              shareToken: shareToken
            };
            
            batch.set(demographicSnapshotRef, demographicData);
            console.log(`📊 [SNAPSHOT] Demographics [${demographic.platform}] capturado para: ${influencerData.name}`);
          }
        }
        
        capturedSnapshots.push({
          influencerId,
          name: influencerData.name,
          dataSize: JSON.stringify(mainSnapshotData).length,
          hasPricing: false, // 🔒 Pricing nunca é incluído no snapshot
          pricingExists: !!influencerData.currentPricing, // Mas registramos se existe
          budgetsCount: proposalBudgets.length, // 💰 NOVO: Contagem de budgets capturados
          budgetsTotal: proposalBudgets.reduce((sum, budget) => sum + (budget.amount || 0), 0), // 💰 NOVO: Total dos budgets
          demographicsCount: influencerData.currentDemographics?.length || 0
        });
        
        console.log(`✅ [SNAPSHOT] Snapshot completo para: ${influencerData.name} (${proposalBudgets.length} budgets incluídos)`);
        
      } catch (error) {
        console.error(`❌ [SNAPSHOT] Erro ao processar influenciador ${influencerId}:`, error);
        // Continuar processando outros influenciadores mesmo se um falhar
      }
    }
    
    // Executar todas as operações em batch
    await batch.commit();
    
    console.log(`🎉 [SNAPSHOT] Captura concluída! ${capturedSnapshots.length} snapshots com subcoleções salvos`);
    return capturedSnapshots;
    
  } catch (error) {
    console.error('❌ [SNAPSHOT] Erro geral na captura de snapshots:', error);
    throw new Error('Falha ao capturar snapshots dos influenciadores');
  }
}

/**
 * Busca dados completos de um influenciador via GraphQL
 */
async function fetchInfluencerCompleteData(influencerId: string) {
  try {
    console.log(`🔍 [SNAPSHOT] Buscando dados completos via GraphQL: ${influencerId}`);
    
    const graphqlUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/graphql`;
    console.log(`🔍 [SNAPSHOT] URL GraphQL: ${graphqlUrl}`);
    
    const response = await fetch(graphqlUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetInfluencerComplete($id: ID!) {
            influencer(id: $id) {
              id
              name
              email
              phone
              whatsapp
              avatar
              bio
              age
              gender
              country
              state
              city
              location
              category
              categories
              totalFollowers
              engagementRate
              rating
              isVerified
              isAvailable
              status
              
              # Redes sociais
              instagramUsername
              instagramFollowers
              instagramEngagementRate
              instagramAvgViews
              instagramStoriesViews
              instagramReelsViews
              
              tiktokUsername
              tiktokFollowers
              tiktokEngagementRate
              tiktokAvgViews
              tiktokVideoViews
              
              youtubeUsername
              youtubeFollowers
              youtubeSubscribers
              youtubeEngagementRate
              youtubeAvgViews
              youtubeShortsViews
              youtubeLongFormViews
              
              facebookUsername
              facebookFollowers
              facebookEngagementRate
              facebookAvgViews
              facebookViews
              
              twitchUsername
              twitchFollowers
              twitchEngagementRate
              twitchViews
              
              kwaiUsername
              kwaiFollowers
              kwaiEngagementRate
              kwaiViews
              
              promotesTraders
              responsibleName
              agencyName
              
              # Pricing completo
              currentPricing {
                id
                services {
                  instagram {
                    story { price currency }
                    reel { price currency }
                  }
                  tiktok {
                    video { price currency }
                  }
                  youtube {
                    insertion { price currency }
                    dedicated { price currency }
                    shorts { price currency }
                  }
                  facebook {
                    post { price currency }
                  }
                  twitch {
                    stream { price currency }
                  }
                  kwai {
                    video { price currency }
                  }
                }
                isActive
                validFrom
                validUntil
                notes
                clientSpecific
                createdAt
                createdBy
                updatedAt
                updatedBy
              }
              
              # Demographics completos
              currentDemographics {
                id
                platform
                audienceGender {
                  male
                  female
                  other
                }
                              audienceLocations {
                country
                  percentage
                }
                audienceCities {
                  city
                  percentage
                }
                audienceAgeRange {
                  range
                  percentage
                }
                captureDate
                isActive
                source
                createdAt
                createdBy
                updatedAt
                updatedBy
              }
              
              createdAt
              updatedAt
            }
          }
        `,
        variables: { id: influencerId }
      })
    });

    console.log(`🔍 [SNAPSHOT] Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ [SNAPSHOT] HTTP error! status: ${response.status}, body: ${errorText}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    console.log(`🔍 [SNAPSHOT] GraphQL response:`, {
      hasErrors: !!result.errors,
      hasData: !!result.data,
      hasInfluencer: !!result.data?.influencer,
      errorCount: result.errors?.length || 0
    });
    
    if (result.errors) {
      console.error('❌ [SNAPSHOT] GraphQL errors:', result.errors);
      return null;
    }

    console.log(`✅ [SNAPSHOT] Dados GraphQL obtidos para ${influencerId}:`, {
      name: result.data?.influencer?.name,
      hasCurrentPricing: !!result.data?.influencer?.currentPricing,
      hasCurrentDemographics: !!result.data?.influencer?.currentDemographics
    });

    return result.data?.influencer;
    
  } catch (error) {
    console.error(`❌ [SNAPSHOT] Erro ao buscar dados do influenciador ${influencerId}:`, error);
    return null;
  }
}

// POST - Criar convite
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    
    // Verificar se a proposta existe e o usuário tem permissão
    const proposal = await ProposalService.getProposalById(proposalId);
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    console.log('🔍 Verificando permissões de compartilhamento:', {
      proposalId,
      userId,
      criadoPor: proposal.criadoPor,
      brandId: proposal.brandId,
      isCreator: proposal.criadoPor === userId,
      isBrandOwner: proposal.brandId === userId
    });

    // Verificar se o usuário é o criador da proposta OU é o dono da marca (brandId)
    if (proposal.criadoPor !== userId && proposal.brandId !== userId) {
      console.log('❌ Permissão negada:', {
        userIsCreator: proposal.criadoPor === userId,
        userIsBrandOwner: proposal.brandId === userId
      });
      return NextResponse.json(
        { success: false, error: 'Sem permissão para compartilhar esta proposta' },
        { status: 403 }
      );
    }

    console.log('✅ Permissão concedida para compartilhamento');

    // ===== 🔥 NOVA FUNCIONALIDADE: CAPTURA DE SNAPSHOTS =====
    
    console.log('📸 [DEBUG] Verificando necessidade de snapshots...');
    console.log('📸 [DEBUG] ProposalId para busca:', proposalId);
    console.log('📸 [DEBUG] Firebase db instance:', !!db);
    
    // 🔧 CORREÇÃO: Buscar influenciadores da subcoleção também
    let allInfluencerIds: string[] = [];
    
    // 1. Influenciadores do documento principal (se existirem)
    if (proposal.influencers && proposal.influencers.length > 0) {
      const mainInfluencerIds = proposal.influencers.map((inf: any) => inf.id || inf);
      allInfluencerIds.push(...mainInfluencerIds);
      console.log('📸 [DEBUG] Influenciadores do documento principal:', mainInfluencerIds);
    } else {
      console.log('📸 [DEBUG] Nenhum influenciador no documento principal');
    }
    
    // 2. Influenciadores da subcoleção (ESTRUTURA CORRIGIDA: proposals/influencers)
    try {
      console.log('📸 [DEBUG] Iniciando busca na subcoleção...');
      const subcollectionRef = db.collection('proposals').doc(proposalId).collection('influencers');
      console.log('📸 [DEBUG] Subcollection ref criada:', subcollectionRef.path);
      
      const subcollectionSnapshot = await subcollectionRef.get();
      console.log('📸 [DEBUG] Snapshot obtido:', {
        empty: subcollectionSnapshot.empty,
        size: subcollectionSnapshot.size,
        exists: subcollectionSnapshot.docs.length
      });
      
      if (!subcollectionSnapshot.empty) {
        const subcollectionIds = subcollectionSnapshot.docs.map((doc: any) => doc.id);
        allInfluencerIds.push(...subcollectionIds);
        console.log('📸 [DEBUG] Influenciadores da subcoleção:', subcollectionIds);
      } else {
        console.log('📸 [DEBUG] Subcoleção está vazia');
      }
    } catch (error) {
      console.error('❌ [SNAPSHOT] Erro detalhado ao buscar subcoleção:', error);
      console.error('❌ [SNAPSHOT] Stack trace:', error instanceof Error ? error.stack : 'No stack');
    }
    
    // 3. Remover duplicatas
    allInfluencerIds = [...new Set(allInfluencerIds)];
    
    console.log('📸 [DEBUG] Total de influenciadores únicos encontrados:', {
      count: allInfluencerIds.length,
      ids: allInfluencerIds
    });
    
    console.log('📸 [DEBUG] Snapshots serão criados na estrutura de compartilhamento');
    
    // ===== GERAR TOKEN ANTES DA CAPTURA DE SNAPSHOTS =====

    // Gerar token único para compartilhamento
    const shareToken = nanoid(32);
    
    // ===== CAPTURA DE SNAPSHOTS (NOVA ESTRUTURA) =====
    
    if (allInfluencerIds.length > 0) {
      console.log('📸 [SNAPSHOT] Iniciando captura de snapshots para compartilhamento...');
      
      try {
        console.log(`📸 [SNAPSHOT] Capturando snapshots de ${allInfluencerIds.length} influenciadores`);
        
        // Capturar snapshots dos influenciadores na nova estrutura
        const capturedSnapshots = await captureInfluencerSnapshots(shareToken, proposalId, allInfluencerIds, userId);
        
        console.log(`✅ [SNAPSHOT] Snapshots capturados com sucesso: ${capturedSnapshots.length} influenciadores`);
        console.log('✅ [SNAPSHOT] Detalhes dos snapshots:', capturedSnapshots);
        
      } catch (snapshotError) {
        console.error('❌ [SNAPSHOT] Erro ao capturar snapshots:', snapshotError);
        console.error('❌ [SNAPSHOT] Stack trace:', snapshotError instanceof Error ? snapshotError.stack : 'No stack');
        // Não impedir o compartilhamento se os snapshots falharem
        console.log('⚠️ [SNAPSHOT] Continuando com compartilhamento sem snapshots');
      }
    } else {
      console.log('⚠️ [SNAPSHOT] Proposta sem influenciadores, pulando captura de snapshots');
    }

    // ===== CONTINUAR COM FLUXO ORIGINAL DE COMPARTILHAMENTO =====
    
    // Obter dados do corpo da requisição
    const body = await request.json();
    const { 
      isPublic = true, 
      requiresPassword = false, 
      password = '', 
      expiresIn = 7, 
      allowDownload = false 
    } = body; // Expira em 7 dias por padrão
    
    // Calcular data de expiração
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresIn);
    
    // Dados do compartilhamento
    const sharingData = {
      token: shareToken,
      proposalId: proposalId,
      createdBy: userId,
      createdAt: new Date(),
      expiresAt: expiresAt,
      isPublic: isPublic,
      requiresPassword: requiresPassword,
      password: requiresPassword ? password : null,
      allowDownload: allowDownload,
      isActive: true,
      accessCount: 0,
      lastAccessed: null
    };

    // Salvar dados de compartilhamento no Firebase
    await ProposalService.createProposalSharing(shareToken, sharingData);

    // Gerar URL de compartilhamento
    // ✅ CORRIGIR PROTOCOLO PARA LOCALHOST EM DESENVOLVIMENTO
    let baseUrl = process.env.NEXT_PUBLIC_APP_URL || request.nextUrl.origin;
    
    // Se for localhost e estiver usando https, mudar para http
    if (baseUrl.includes('localhost') && baseUrl.startsWith('https://')) {
      baseUrl = baseUrl.replace('https://', 'http://');
      console.log('🔧 Corrigido protocolo para desenvolvimento:', baseUrl);
    }
    
    const shareUrl = `${baseUrl}/shared/${shareToken}`;

    return NextResponse.json({
      success: true,
      data: {
        shareUrl,
        token: shareToken,
        expiresAt: expiresAt.toISOString(),
        allowDownload,
        hasSnapshots: allInfluencerIds.length > 0,
        snapshotsCount: allInfluencerIds.length
      }
    });

  } catch (error) {
    console.error('Erro ao criar compartilhamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token é obrigatório' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    
    // Desativar compartilhamento
    await ProposalService.deactivateProposalSharing(token, userId);

    return NextResponse.json({
      success: true,
      message: 'Compartilhamento desativado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao desativar compartilhamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 