"use client"

import { Influencer } from "@/types/brand-dashboard"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import Image from "next/image"

interface InfluencerDetailsDialogProps {
  influencer: Influencer | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function InfluencerDetailsDialog({
  influencer,
  open,
  onOpenChange,
}: InfluencerDetailsDialogProps) {
  if (!influencer) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{influencer.name}</DialogTitle>
          <DialogDescription>
            Detalhes completos do influenciador e suas métricas
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="metrics">Métricas</TabsTrigger>
            <TabsTrigger value="history">Histórico</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">Redes Sociais</h3>
                <div className="space-y-2">
                  {influencer.socialNetworks.map((social) => (
                    <div
                      key={social.network}
                      className="flex items-center justify-between"
                    >
                      <span className={social.isPrimary ? "font-bold" : ""}>
                        {social.network}
                      </span>
                      <a
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        @{social.username}
                      </a>
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">Serviços</h3>
                <div className="space-y-2">
                  {influencer.services.map((service, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <span>{service.type}</span>
                      <div className="text-right">
                        <div>R$ {service.price}</div>
                        {service.counterProposal && (
                          <div className="text-sm text-gray-500">
                            Contraproposta: R$ {service.counterProposal}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="metrics">
            <div className="space-y-4">
              <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">Métricas de Performance</h3>
                {influencer.metrics.screenshots.map((screenshot, index) => (
                  <div key={index} className="relative h-48 mb-4">
                    <Image
                      src={screenshot}
                      alt={`Métrica ${index + 1}`}
                      fill
                      className="object-contain"
                    />
                  </div>
                ))}
                {influencer.metrics.demographicReport && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Relatório Demográfico</h4>
                    <p>{influencer.metrics.demographicReport}</p>
                  </div>
                )}
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history">
            <Card className="p-4">
              <h3 className="text-lg font-semibold mb-4">Histórico de Entregas</h3>
              <div className="space-y-4">
                {influencer.previousDeliveries.map((delivery, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between border-b pb-2"
                  >
                    <div>
                      <div className="font-medium">{delivery.type}</div>
                      <div className="text-sm text-gray-500">{delivery.date}</div>
                    </div>
                    <a
                      href={delivery.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      Ver Publicação
                    </a>
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 

