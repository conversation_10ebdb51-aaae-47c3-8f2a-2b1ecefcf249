"use client"

import { useState, useEffect, useMemo } from 'react'
import { useParams } from 'next/navigation'
import { PlusCircle, <PERSON>cil, Trash2, Tag, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { DataTable } from '@/components/ui/data-table'
import { ColumnDef } from '@tanstack/react-table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { useCategoriesGraphQL, Category } from '@/hooks/use-categories-graphql'

interface CategoryFormData {
  name: string
  slug: string
  description: string
  color: string
}

// Definição das colunas da tabela com ordenação
function createCategoryColumns(
  onEdit: (category: Category) => void,
  onDelete: (category: Category) => void
): ColumnDef<Category>[] {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <div className="flex-1 min-w-0 flex items-center justify-left">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-left justify-left"
          >
            Nome
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const category = row.original
        return (
          <div className="flex items-center gap-2 flex-1 justify-left">
            {category.color && (
              <div
                className="w-3 h-3 rounded-full border"
                style={{ backgroundColor: category.color }}
              />
            )}
            <span className="font-medium">{category.name}</span>
          </div>
        )
      },
    },
    {
      accessorKey: "slug",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium hover:bg-transparent text-left justify-start"
        >
          Slug
        </Button>
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="font-mono text-xs">
          {row.getValue("slug")}
        </Badge>
      ),
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium hover:bg-transparent text-left justify-start"
        >
          Descrição
        </Button>
      ),
      cell: ({ row }) => (
        <span className="text-muted-foreground max-w-xs truncate">
          {row.getValue("description") || '-'}
        </span>
      ),
    },
    {
      id: "actions",
      header: () => <div className="text-right">Ações</div>,
      enableSorting: false,
      cell: ({ row }) => {
        const category = row.original
        return (
          <div className="flex justify-end space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(category)}
              className="h-8 w-8 p-0"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(category)}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]
}

export function UserCategoryManager() {
  const params = useParams()
  const userId = params?.userId as string
  
  // ✅ CORREÇÃO: Usar todas as funções GraphQL
  const {
    categories,
    categoriesLoading: isLoading,
    categoriesError,
    createCategory,
    updateCategory: updateCategoryGraphQL,
    deleteCategory: deleteCategoryGraphQL
  } = useCategoriesGraphQL(true)

  // 🔍 DEBUG: Log para monitorar carregamento das categorias
  console.log('🏷️ [UserCategoryManager] Estado atual:', {
    categoriesCount: categories.length,
    isLoading,
    categoriesError: categoriesError?.message,
    userId
  });

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  
  // Form data
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    slug: '',
    description: '',
    color: '',
  })

  // Função para gerar slug automaticamente a partir do nome
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim()
  }

  // Gerencia a mudança no nome da categoria e atualiza o slug automaticamente
  const handleNameChange = (value: string) => {
    const newFormData = {
      ...formData,
      name: value,
      slug: generateSlug(value)
    }
    setFormData(newFormData)
  }

  // Reinicia os estados do formulário
  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '',
    })
    setSelectedCategory(null)
  }

  // Abre o diálogo de adição
  const openAddDialog = () => {
    resetForm()
    setIsAddDialogOpen(true)
  }

  // Abre o diálogo de edição com os dados da categoria selecionada
  const openEditDialog = (category: Category) => {
    setSelectedCategory(category)
    setFormData({
      name: category.name,
      slug: category.slug || '',
      description: category.description || '',
      color: category.color || '',
    })
    setIsEditDialogOpen(true)
  }

  // Abre o diálogo de exclusão
  const openDeleteDialog = (category: Category) => {
    setSelectedCategory(category)
    setIsDeleteDialogOpen(true)
  }

  // Definir as colunas da tabela
  const columns = useMemo(() => createCategoryColumns(
    openEditDialog,
    openDeleteDialog
  ), [openEditDialog, openDeleteDialog])

  // Definir a ordem específica das colunas
  const columnOrder = useMemo(() => ['name', 'slug', 'description', 'actions'], [])

  // ✅ CORREÇÃO: Usar GraphQL para criar categoria
  const addCategory = async () => {
    if (!formData.name.trim()) {
      toast.error('Nome da categoria é obrigatório')
      return
    }

    if (!userId) {
      toast.error('ID do usuário não encontrado')
      return
    }

    setIsSubmitting(true)
    try {
      await createCategory({
        name: formData.name.trim(),
        userId: userId as string,
        isActive: true
      })

      toast.success('Categoria adicionada com sucesso')
      setIsAddDialogOpen(false)
      resetForm()
      // O cache será atualizado automaticamente pela mutation
    } catch (error: any) {
      console.error('Erro ao adicionar categoria:', error)
      toast.error(error.message || 'Erro ao adicionar categoria')
    } finally {
      setIsSubmitting(false)
    }
  }

  // ✅ CORREÇÃO: Usar GraphQL para atualizar categoria
  const updateCategory = async () => {
    if (!selectedCategory || !formData.name.trim()) {
      toast.error('Nome da categoria é obrigatório')
      return
    }

    setIsSubmitting(true)
    try {
      await updateCategoryGraphQL(selectedCategory.id, {
        name: formData.name.trim(),
        isActive: true
      })

      toast.success('Categoria atualizada com sucesso')
      setIsEditDialogOpen(false)
      resetForm()
      // O cache será atualizado automaticamente pela mutation
    } catch (error: any) {
      console.error('Erro ao atualizar categoria:', error)
      toast.error(error.message || 'Erro ao atualizar categoria')
    } finally {
      setIsSubmitting(false)
    }
  }

  // ✅ CORREÇÃO: Usar GraphQL para deletar categoria
  const deleteCategory = async () => {
    if (!selectedCategory) return

    setIsSubmitting(true)
    try {
      await deleteCategoryGraphQL(selectedCategory.id)

      toast.success('Categoria excluída com sucesso')
      setIsDeleteDialogOpen(false)
      resetForm()
      // O cache será atualizado automaticamente pela mutation
    } catch (error: any) {
      console.error('Erro ao excluir categoria:', error)
      toast.error(error.message || 'Erro ao excluir categoria')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Loader removido - agora controlado globalmente

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Tag className="h-5 w-5 text-[#ff0074]" />
          <h3 className="text-lg font-semibold">Suas Categorias</h3>
        </div>
        <Button 
          onClick={openAddDialog} 
          className="bg-[#ff0074] hover:bg-[#d10037] text-white"
        >
          <PlusCircle className="h-4 w-4 mr-2" />
          Nova Categoria
        </Button>
      </div>

      {categories.length === 0 ? (
        <div className="text-center py-12  rounded-lg border border-dashed">
          <Tag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Nenhuma categoria criada</h3>
          <p className="text-muted-foreground mb-4">
            Comece criando sua primeira categoria para organizar seus influenciadores.
          </p>
          <Button
            onClick={openAddDialog}
            className="bg-[#ff0074] hover:bg-[#d10037] text-white"
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Criar primeira categoria
          </Button>
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={categories}
          searchKey="name"
          searchPlaceholder="Pesquisar categorias..."
          enableSorting={true}
          enableFiltering={true}
          enablePagination={true}
          enableRowSelection={false}
          enableColumnVisibility={false}
          enableColumnOrdering={false}
          pageSize={10}
          className="w-full"
          columnOrder={columnOrder}
        />
      )}

      {/* Diálogo para adicionar categoria */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nova Categoria</DialogTitle>
            <DialogDescription>
              Crie uma nova categoria para organizar seus influenciadores.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Ex: Moda e Beleza"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                placeholder="Ex: moda-e-beleza"
                className="font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground">
                Usado em URLs. Gerado automaticamente se deixado em branco.
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descrição opcional da categoria"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button 
              onClick={addCategory}
              disabled={isSubmitting}
              className="bg-[#ff0074] hover:bg-[#d10037] text-white"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Criando...
                </>
              ) : (
                'Criar categoria'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar categoria */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Categoria</DialogTitle>
            <DialogDescription>
              Atualize os detalhes da categoria.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nome *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Ex: Moda e Beleza"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-slug">Slug</Label>
              <Input
                id="edit-slug"
                value={formData.slug}
                onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                placeholder="Ex: moda-e-beleza"
                className="font-mono text-sm"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Descrição</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descrição opcional da categoria"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsEditDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button 
              onClick={updateCategory}
              disabled={isSubmitting}
              className="bg-[#ff0074] hover:bg-[#d10037] text-white"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                'Salvar alterações'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para excluir categoria */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir Categoria</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir a categoria "{selectedCategory?.name}"? 
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button 
              onClick={deleteCategory}
              disabled={isSubmitting}
              variant="destructive"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Excluindo...
                </>
              ) : (
                'Excluir categoria'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 


