
"use client";

// 🔥 OTIMIZAÇÃO: Mantendo "use client" mas implementando lazy loading estratégico
// para reduzir o impacto inicial no bundle JavaScript

import { useState, useEffect, useRef, useMemo, Suspense } from "react";
import { createPortal } from "react-dom";
import dynamic from "next/dynamic";
import { useSearchPara<PERSON>, useRouter, usePathname } from "next/navigation";
import { Protect, useAuth as useClerkAuth, useUser } from '@clerk/nextjs';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from '@/hooks/use-translations';
import { useProposals } from '@/contexts/proposals-context';
import { OtherSocialNetwork } from "@/types/influencer";
import { Campaign } from "@/types/campaign";
import { CampaignService } from "@/services/campaign-service";

// 🔥 IMPORTAÇÃO DIRETA: Framer Motion (voltando ao normal para evitar erros)
import { motion, AnimatePresence } from "framer-motion";

// 🔥 IMPORTAÇÃO DIRETA: Componentes de gráficos (voltando ao normal para evitar erros)
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line, LabelList, AreaChart, Area } from "recharts";
import AgeRangeBarChart from "@/components/age-range-bar-chart";
import { LocationBarChart } from "@/components/ui/location-bar-chart";
import { AudienceInterests } from "@/components/ui/audience-interests";

// 🔥 WRAPPER PARA SHEETS (removido lazy loading)
function SheetWrapper({ children, ...props }: any) {
  return (
    <Sheet {...props}>
      {children}
    </Sheet>
  );
}

// 🔥 SKELETON OTIMIZADO para InfluencerGrid
function InfluencerGridSkeleton() {
  return (
    <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto min-h-0">
      <div className="p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-muted rounded-full" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-1/2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
import { 
  UserPlus, 
  ChevronDown, 
  CheckCircle, 
  Copy, 
  FileText, 
  Tag, 
  Pencil,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Send,
  Download,
  Trash2,
  Upload,
  File,
  ExternalLink,
  DollarSign,
  BarChart3,
  History,
  Users,
  Building,
  MessageCircle,
  CheckSquare,
  XSquare,
  Info,
  ChevronLeft,
  ChevronRight,
  UserCircle,
  Star,

  Globe,
  TrendingUp,
  TrendingDown,
  Eye,
  Target,
  Clock,
  Receipt,
  Plus,
  MessageSquare,
  X,
  Check,
  Banknote,
  BadgeCheck,
  ArrowRightLeft,
  Sparkles
} from "lucide-react";
// 🔥 OTIMIZAÇÃO: Importações básicas (leves)
import { Toaster, toast } from "sonner";
import { Toaster as ShadcnToaster } from '@/components/ui/toaster';
import { Button } from "@/components/ui/button";
import { Loader, useGlobalLoader } from "@/components/ui/loader";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";

// 🔥 LAZY LOADING: Componentes pesados carregados sob demanda
// 🔥 CORREÇÃO: SidebarMenu é named export
import { SidebarMenu } from "@/components/ui/sidebar-menu";
const TagsManager = dynamic(() => import("@/components/TagsManager"), { ssr: false });
const NotesManager = dynamic(() => import("@/components/NotesManager"), { ssr: false });
const BrandsManager = dynamic(() => import("@/components/BrandsManager"), { ssr: false });
// 🔥 CORREÇÃO: BrandFilter é named export, não default export
import { BrandFilter } from "@/components/brand-filter";
// 🔥 CORREÇÃO: ProfilePanelTourProvider e BudgetSectionAutoTour são named exports
import { ProfilePanelTourProvider, BudgetSectionAutoTour } from "@/components/tour/profile-panel-tour";
const InfluencerCampaignsPanel = dynamic(() => import("@/components/influencer-campaigns-panel"), { ssr: false });
// 🔥 CORREÇÃO: Hooks NUNCA podem ser lazy loaded - importação direta obrigatória
import { useProposalAccess } from "@/hooks/use-proposal-access";
import { useBrands } from "@/hooks/use-brands";

// 🔥 IMPORTAÇÃO DIRETA: Modais e Sheets (voltando ao normal para evitar erros)
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet";

// 🔥 CORREÇÃO: Named exports importados diretamente (não como default)
import { SocialIcon, getPlatformDisplayName } from "@/components/ui/social-icons";
import { SocialScreenshotsSection } from "@/components/ui/social-screenshots-section";

// 🔥 GRAPHQL - Mantido como importação direta pois é necessário para queries
import { useQuery, gql } from '@apollo/client';
import { apolloClient } from '@/lib/apollo-client';
// import { useToast } from '@/hooks/use-toast'; // Removido para evitar conflito com toast do Sonner

// ===== 🆕 QUERIES GRAPHQL PARA ORÇAMENTOS =====
const GET_PROPOSAL_BUDGETS = gql`
  query GetProposalBudgets($proposalId: ID!, $userId: ID!) {
    proposalBudgets(proposalId: $proposalId, userId: $userId) {
      proposalId
      permissions {
        canView
        canEdit
        canCreateCounterProposal
        canManageBudgets
        canApproveCounterProposals
        canViewFinancialData
      }
      budgets {
        id
        serviceType
        amount
        currency
        originalPrice
        proposalId
        influencerId
        brandId
        description
        status
        createdAt
        updatedAt
        expiresAt
      }
      totalCount
      userRole
      errors
      processingTimeMs
    }
  }
`;

const GET_INFLUENCER_BUDGETS_IN_PROPOSAL = gql`
  query GetInfluencerBudgetsInProposal($proposalId: ID!, $influencerId: ID!, $userId: ID!) {
    influencerBudgetsInProposal(proposalId: $proposalId, influencerId: $influencerId, userId: $userId) {
      proposalId
      permissions {
        canView
        canEdit
        canCreateCounterProposal
        canManageBudgets
        canApproveCounterProposals
        canViewFinancialData
      }
      budgets {
        id
        serviceType
        amount
        currency
        originalPrice
        proposalId
        influencerId
        brandId
        description
        status
        createdAt
        updatedAt
        expiresAt
        counterProposals {
          id
          proposedAmount
          originalAmount
          currency
          notes
          proposedBy
          proposedAt
          status
          type
          quantity
        }
      }
      documents {
        id
        name
        url
        type
        size
        uploadedAt
        uploadedBy
        proposalId
        influencerId
      }
      totalCount
      userRole
      errors
      processingTimeMs
    }
  }
`;

const GET_COLLABORATOR_COUNTER_PROPOSALS = gql`
  query GetCollaboratorCounterProposals($proposalId: ID!, $userId: ID!, $status: CollaboratorCounterProposalStatus) {
    collaboratorCounterProposals(proposalId: $proposalId, userId: $userId, status: $status) {
      id
      budgetId
      proposalId
      influencerId
      originalAmount
      proposedAmount
      currency
      notes
      serviceType
      proposedBy {
        userId
        userName
        userEmail
        collaboratorRole
      }
      status
      reviewedBy
      reviewNote
      createdAt
      updatedAt
      reviewedAt
    }
  }
`;

// 🆕 NOVO: Query GraphQL para orçamentos independentes (fora de propostas)
const GET_INDEPENDENT_BUDGETS = gql`
  query GetIndependentBudgets($influencerId: ID!, $userId: ID!, $brandId: ID) {
    independentBudgets(influencerId: $influencerId, userId: $userId, brandId: $brandId) {
      budgets {
        id
        serviceType
        amount
        currency
        originalPrice
        influencerId
        brandId
        description
        status
        createdAt
        updatedAt
        expiresAt
        counterProposals {
          id
          proposedAmount
          originalAmount
          currency
          notes
          proposedBy
          proposedAt
          status
          type
          quantity
        }
      }
      totalCount
      userRole
      errors
      processingTimeMs
    }
  }
`;

// ===== 🆕 HOOKS CUSTOMIZADOS COM CACHE GRAPHQL =====

// Hook para orçamentos de proposta específica - OTIMIZADO
const useBudgetsInProposal = (proposalId: string | null, influencerId: string | null, userId: string | null) => {
  const { data, loading, error, refetch } = useQuery(GET_INFLUENCER_BUDGETS_IN_PROPOSAL, {
    variables: {
      proposalId: proposalId || '',
      influencerId: influencerId || '',
      userId: userId || ''
    },
    skip: !proposalId || !influencerId || !userId,
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: false, // 🔥 OTIMIZAÇÃO: Reduzir re-renders
    returnPartialData: true
  });

  return {
    budgets: data?.influencerBudgetsInProposal?.budgets || [],
    documents: data?.influencerBudgetsInProposal?.documents || [], // 🆕 NOVO: Incluir documentos
    permissions: data?.influencerBudgetsInProposal?.permissions || {},
    userRole: data?.influencerBudgetsInProposal?.userRole || '',
    totalCount: data?.influencerBudgetsInProposal?.totalCount || 0,
    loading,
    error,
    refetch,
    hasData: !!data?.influencerBudgetsInProposal
  };
};

// Hook para orçamentos independentes - OTIMIZADO
const useIndependentBudgets = (influencerId: string | null, userId: string | null, brandId?: string | null) => {
  const { data, loading, error, refetch } = useQuery(GET_INDEPENDENT_BUDGETS, {
    variables: {
      influencerId: influencerId || '',
      userId: userId || '',
      brandId: brandId || undefined
    },
    skip: !influencerId || !userId,
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: false, // 🔥 OTIMIZAÇÃO: Reduzir re-renders
    returnPartialData: true
  });

  return {
    budgets: data?.independentBudgets?.budgets || [],
    totalCount: data?.independentBudgets?.totalCount || 0,
    loading,
    error,
    refetch,
    hasData: !!data?.independentBudgets
  };
};

// Hook para contrapropostas de colaboradores
const useCollaboratorCounterProposals = (proposalId: string | null, userId: string | null, influencerId?: string | null) => {
  const { data, loading, error, refetch } = useQuery(GET_COLLABORATOR_COUNTER_PROPOSALS, {
    variables: {
      proposalId: proposalId || '',
      userId: userId || '',
      status: null // Buscar todas
    },
    skip: !proposalId || !userId,
    fetchPolicy: 'cache-first', // 🔥 CACHE: Usar cache primeiro
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    returnPartialData: true
  });

  // Filtrar por influenciador se especificado
  const filteredProposals = influencerId 
    ? (data?.collaboratorCounterProposals || []).filter((cp: any) => cp.influencerId === influencerId)
    : (data?.collaboratorCounterProposals || []);

  return {
    counterProposals: filteredProposals,
    loading,
    error,
    refetch,
    hasData: !!data?.collaboratorCounterProposals
  };
};

// 🔥 OTIMIZAÇÃO: InfluencerGrid com lazy loading e skeleton otimizado
const InfluencerGridComponent = dynamic(() => import('@/components/influencer-grid'), {
  ssr: false,
  loading: () => <InfluencerGridSkeleton />
});

// Componente de tooltip customizado que usa createPortal
const CustomTooltip = ({ content, position, visible }: { content: string, position: { x: number, y: number }, visible: boolean }) => {
  if (!visible || typeof window === 'undefined') return null;
  
  return createPortal(
    <div 
      className="fixed bg-black/90 text-white text-xs px-4 py-3 rounded-lg shadow-xl max-w-sm break-words pointer-events-none z-[2147483647] backdrop-blur-sm"
      style={{ 
        left: Math.max(10, position.x - 150), // Centralizar com margem mínima
        top: position.y - 60,
        zIndex: 2147483647 // Máximo z-index possível
      }}
    >
      <div className="whitespace-pre-line leading-relaxed">
        {content}
      </div>
    </div>,
    document.body
  );
};

interface PageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function Page({ params }: PageProps) {
  const { t } = useTranslations();
  const { has, isLoaded, isSignedIn } = useClerkAuth();
  const { user: clerkUser } = useUser();
  const { isLoading, showLoader, hideLoader, updateMessage } = useGlobalLoader();
  
  // ⚡ OTIMIZAÇÃO: Usar contexto compartilhado de propostas
  const { proposals: contextProposals, loading: proposalsLoading } = useProposals();
  
  // ⚡ MAPEAMENTO: Converter propostas do contexto para formato esperado
  const userProposals = useMemo(() => {
    return contextProposals.map((proposal: any) => ({
      id: proposal.id,
      name: proposal.nome || 'Proposta sem nome',
      description: proposal.descricao || '',
      value: proposal.totalAmount || 0,
      status: proposal.status || 'draft',
      deadline: proposal.dataEnvio || null,
      brandId: proposal.brandId || '',
      influencerId: proposal.influencers?.[0] || null,
      createdAt: proposal.createdAt || new Date(),
      updatedAt: proposal.updatedAt || new Date(),
      priority: proposal.priority || 'medium',
      grupo: proposal.grupo || null,
      services: proposal.services || [],
      influencers: proposal.influencers || [],
      hasSnapshots: false,
      snapshotCreatedAt: null,
      snapshotsCount: 0
    }));
  }, [contextProposals]);
  
  // const { toast } = useToast(); // Removido para usar toast do Sonner
  const router = useRouter();

  // 🔒 VERIFICAÇÃO DE AUTENTICAÇÃO: Redirecionar para sign-in se não autenticado
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      console.log('🔒 [AUTH] Usuário não autenticado, redirecionando para sign-in');
      router.push('/sign-in');
      return;
    }
  }, [isLoaded, isSignedIn, router]);

  // ✅ REMOVIDO: Hook desnecessário - API principal já faz isolamento por usuário

  // Debug das permissões
  useEffect(() => {
    if (isLoaded && clerkUser) {
      // Permissões verificadas silenciosamente
    }
  }, [isLoaded, clerkUser, has]);
  const [showWelcomeBadge, setShowWelcomeBadge] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  
  // 🔥 NOVO: Hooks para gerenciar parâmetros da URL
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // 🔗 NOVO: Estados para lista compartilhada
  const [isSharedList, setIsSharedList] = useState(false);
  const [sharedListData, setSharedListData] = useState<{
    listName: string;
    listType: string;
    influencerIds: string[];
    token: string;
  } | null>(null);
  const [sharedListError, setSharedListError] = useState<string | null>(null);

  // 🏢 Hook para gerenciar marcas
  const { brands, getBrandById, loading: brandsLoading, refreshBrands } = useBrands();
  
  // Função utilitária para formatar localização filtrando valores inválidos
  const formatLocationDisplay = (influencer: any) => {
    if (!influencer) return t('influencers.location_not_informed');
    
    if (influencer.location && influencer.location.trim() !== "" && influencer.location !== t('influencers.not_informed')) {
      return influencer.location;
    }
    
    const validCity = influencer.city && influencer.city !== t('influencers.not_informed') && influencer.city.trim() !== "" ? influencer.city : null;
    const validState = influencer.state && influencer.state !== t('influencers.not_informed') && influencer.state.trim() !== "" ? influencer.state : null;
    const validCountry = influencer.country && influencer.country !== t('influencers.not_informed') && influencer.country.trim() !== "" ? influencer.country : null;
    
    if (validCity && validState) {
      return `${validCity}, ${validState}`;
    } else if (validCity && validCountry) {
      return `${validCity}, ${validCountry}`;
    } else if (validState && validCountry) {
      return `${validState}, ${validCountry}`;
    } else if (validCountry) {
      return validCountry;
    } else if (validCity) {
      return validCity;
    } else if (validState) {
      return validState;
    }
    return t('influencers.location_not_informed');
  };
  
  // Resolver o parâmetro userId de forma assíncrona
  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = await params;
        setUserId(resolvedParams.userId);
      } catch (error) {
        // Erro ao resolver parâmetros - verificar configuração de rotas
      }
    };

    resolveParams();
  }, [params]);

  // 🔗 NOVO: Detectar e validar parâmetros de lista compartilhada
  useEffect(() => {
    if (!searchParams) return;

    const shared = searchParams.get('shared');
    const ids = searchParams.get('ids');
    const token = searchParams.get('token');

    if (shared === 'true' && ids && token) {
      console.log('🔗 [SHARED_LIST] Detectados parâmetros de lista compartilhada:', {
        ids: ids.split(',').length,
        token: token.substring(0, 8) + '...'
      });

      setIsSharedList(true);

      // Validar token e carregar dados da lista
      validateSharedListToken(token, ids.split(','));
    } else {
      setIsSharedList(false);
      setSharedListData(null);
      setSharedListError(null);
    }
  }, [searchParams]);

  // 🔗 NOVO: Função para validar token de compartilhamento
  const validateSharedListToken = async (token: string, influencerIds: string[]) => {
    try {
      console.log('🔍 [SHARED_LIST] Validando token:', token.substring(0, 8) + '...');

      const response = await fetch(`/api/share-tokens/${token}/validate`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Token inválido');
      }

      const data = await response.json();

      if (!data.valid) {
        throw new Error(data.error || 'Token inválido');
      }

      console.log('✅ [SHARED_LIST] Token válido:', {
        listName: data.data.listName,
        itemCount: data.data.itemCount
      });

      setSharedListData({
        listName: data.data.listName,
        listType: data.data.listType,
        influencerIds: data.data.influencerIds,
        token
      });

      setSharedListError(null);

      // 🎉 Mostrar toast de sucesso para lista compartilhada
      toast.success(
        `Lista "${data.data.listName}" carregada com sucesso!`,
        {
          description: `${data.data.influencerIds.length} influenciadores disponíveis`,
          duration: 5000,
          position: 'top-right'
        }
      );

      // Registrar visualização
      fetch(`/api/share-tokens/${token}/validate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'view_list',
          metadata: { influencerCount: influencerIds.length }
        })
      }).catch(console.warn);

    } catch (error: any) {
      console.error('❌ [SHARED_LIST] Erro ao validar token:', error);

      setSharedListError(error.message);
      setSharedListData(null);

      // Mostrar toast de erro
      toast.error(`Erro ao carregar lista compartilhada: ${error.message}`);
    }
  };

  // Estados adicionais necessários
  const [refreshKey, setRefreshKey] = useState(0);
  const [popularLocations, setPopularLocations] = useState([]);
  const [selectedProposal, setSelectedProposal] = useState<any>(null);
  const [showProposalSelector, setShowProposalSelector] = useState(false);
  
  // Verificar se o usuário tem acesso a esta página (verificação de segurança)
  useEffect(() => {
    if (clerkUser && userId) {
      if (userId !== clerkUser.id) {
        // Redirecionamento por segurança - usuário não autorizado
        window.location.href = `/${clerkUser.id}/influencers`;
      }
    }
  }, [clerkUser, userId]);



  // ⚡ OTIMIZAÇÃO: Propostas agora carregadas via ProposalsContext
  // useEffect removido - dados vêm automaticamente do contexto


  // 🔄 Carregamento inicial simplificado - evita ciclos infinitos
  useEffect(() => {
    if (userId && isLoaded && clerkUser) {
      // Carrega brands primeiro, depois proposals será carregado pelo hook de proposalAccess
      if (brands && Array.isArray(brands) && brands.length === 0 && !brandsLoading) {
        refreshBrands();
      }
    }
  }, [userId, isLoaded, clerkUser]);

  // 🔥 NOVO: Restaurar proposta selecionada da URL
  useEffect(() => {
    if (!searchParams) return;

    const proposalId = searchParams.get('proposta');
    const currentInfluencerId = searchParams.get('influencer');

    if (proposalId && userProposals && Array.isArray(userProposals) && userProposals.length > 0) {
      const foundProposal = userProposals.find(p => p.id === proposalId);
      if (foundProposal && foundProposal.id !== selectedProposal?.id) {
        console.log('🔄 [URL_SYNC] Atualizando proposta selecionada:', foundProposal.name);
        setSelectedProposal(foundProposal);

        // 🔥 CORREÇÃO: Só auto-adicionar primeiro influenciador se não há seleção manual em andamento
        if (!currentInfluencerId && foundProposal.influencers && Array.isArray(foundProposal.influencers) && foundProposal.influencers.length > 0 && !lastSelectedInfluencerRef.current && !isManualSelection) {
          const firstInfluencerId = foundProposal.influencers[0];
          const params = new URLSearchParams(searchParams.toString());
          params.set('influencer', firstInfluencerId);

          const newUrl = `${pathname}?${params.toString()}`;
          console.log('🎯 [AUTO_INFLUENCER] Adicionando primeiro influenciador da proposta à URL:', firstInfluencerId);
          console.log('🔍 [DEBUG] Estado atual - isManualSelection:', isManualSelection, 'lastSelectedInfluencerRef:', lastSelectedInfluencerRef.current);
          router.replace(newUrl);
        } else {
          console.log('🚫 [AUTO_INFLUENCER] Não adicionando influencer automático - currentInfluencerId:', currentInfluencerId, 'isManualSelection:', isManualSelection);
        }
      }
    } else if (!proposalId && selectedProposal) {
      // Se não há proposta na URL mas há uma selecionada, limpar seleção
      console.log('🔄 [URL_SYNC] Limpando proposta selecionada');
      setSelectedProposal(null);
    }
  }, [searchParams, userProposals, selectedProposal, pathname, router]);

  // Controlar carregamento inicial da página
  useEffect(() => {
    if (clerkUser && userId) {
      // Esconder loader após um breve delay para mostrar sucesso
      const timer = setTimeout(() => {
        hideLoader();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [clerkUser, userId]); // Removido updateMessage e hideLoader das deps

  // Controlar exibição temporária do badge de boas-vindas
  useEffect(() => {
    if (clerkUser && clerkUser.emailAddresses?.[0]?.emailAddress) {
      const welcomeKey = `welcome_shown_${clerkUser.emailAddresses[0].emailAddress}`;
      const hasShownBefore = localStorage.getItem(welcomeKey);
      
      if (!hasShownBefore) {
        setShowWelcomeBadge(true);
        localStorage.setItem(welcomeKey, 'true');
        const timer = setTimeout(() => {
          setShowWelcomeBadge(false);
        }, 5000); // Badge desaparece após 5 segundos
        
        return () => clearTimeout(timer);
      }
    } else if (!clerkUser) {
      setShowWelcomeBadge(false);
    }
  }, [clerkUser]);
  // Estado do modal de formulário removido
  const [influencers, setInfluencers] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [minFollowers, setMinFollowers] = useState(0);
  const [maxFollowers, setMaxFollowers] = useState(0); // Resetado para 0
  const [minRating, setMinRating] = useState(0); // Resetado para 0
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [availableOnly, setAvailableOnly] = useState(false);
  const [selectedInfluencer, setSelectedInfluencer] = useState<any>(null);
  const [isManualSelection, setIsManualSelection] = useState(false);
  const [financialInfo, setFinancialInfo] = useState<any>(null);
  const [documents, setDocuments] = useState<{name: string, url: string, type: string}[]>([]);
  const [documentsLoaded, setDocumentsLoaded] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showRelationshipPanel, setShowRelationshipPanel] = useState(true);
  const [showContactPanel, setShowContactPanel] = useState(true);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [showNoteForm, setShowNoteForm] = useState(false);
  
  // Estados para campanhas do influenciador
  const [influencerCampaigns, setInfluencerCampaigns] = useState<Campaign[]>([]);

  // Estados para orçamento personalizado
  const [showBudgetModal, setShowBudgetModal] = useState(false);
  const [budgetAmount, setBudgetAmount] = useState('');
  const [budgetCurrency, setBudgetCurrency] = useState('BRL');
  const [budgetDescription, setBudgetDescription] = useState('');
  const [budgetServiceType, setBudgetServiceType] = useState('');
  const [budgetQuantity, setBudgetQuantity] = useState('1');
  const [savingBudget, setSavingBudget] = useState(false);

  // Estados para orçamentos existentes
  const [existingBudgets, setExistingBudgets] = useState<Record<string, any>>({});
  
  // Estados para tooltip customizado
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [tooltipContent, setTooltipContent] = useState('');
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [editingBudget, setEditingBudget] = useState<any>(null);

  // 🔥 CORREÇÃO: useMemo APÓS definição de todos os estados para garantir ordem consistente dos hooks
  const totalViews = useMemo(() => {
    if (!selectedInfluencer) return '0';
    
    let totalViews = 0;
    
    // 🔥 FUNÇÃO MELHORADA: Converter visualizações com validação
    const addViews = (value: string | number | null | undefined, label: string = '') => {
      if (!value) return 0;
      
      let numericValue = 0;
      if (typeof value === 'string') {
        // Remove pontos, vírgulas e espaços, mantém apenas números
        const cleanValue = value.replace(/[.,\s]/g, '');
        numericValue = parseInt(cleanValue) || 0;
      } else {
        numericValue = Number(value) || 0;
      }
      
      return numericValue;
    };
    
    // 🔍 DETALHAMENTO DO CÁLCULO - SOMA TOTAL DE VISUALIZAÇÕES
    const breakdownViews = {
      instagramStories: addViews(selectedInfluencer?.instagramStoriesViews, 'Instagram Stories'),
      instagramReels: addViews(selectedInfluencer?.instagramReelsViews, 'Instagram Reels'),
      tiktokVideos: addViews(selectedInfluencer?.tiktokVideoViews, 'TikTok Vídeos'),
      youtubeShorts: addViews(selectedInfluencer?.youtubeShortsViews, 'YouTube Shorts'),
      youtubeLongForm: addViews(selectedInfluencer?.youtubeLongFormViews, 'YouTube Longos'),
      facebookStories: addViews(selectedInfluencer?.facebookStoriesViews, 'Facebook Stories'),
      facebookReels: addViews(selectedInfluencer?.facebookReelsViews, 'Facebook Reels'),
      facebookPosts: addViews(selectedInfluencer?.facebookViews, 'Facebook Posts'),
      twitchStreams: addViews(selectedInfluencer?.twitchViews, 'Twitch Streams'),
      kwaiVideos: addViews(selectedInfluencer?.kwaiViews, 'Kwai Vídeos')
    };
    
    // Calcular total
    totalViews = Object.values(breakdownViews).reduce((sum, views) => sum + views, 0);
    
    // Formatação do resultado
    if (totalViews >= 1000000) {
      return `${(totalViews / 1000000).toFixed(1)}M`;
    } else if (totalViews >= 1000) {
      return `${(totalViews / 1000).toFixed(0)}K`;
    }
    return totalViews.toLocaleString();
  }, [selectedInfluencer]);

  const activeContentTypes = useMemo(() => {
    if (!selectedInfluencer) return 0;
    
    let activeTypes = 0;
    
    // 🔥 FUNÇÃO MELHORADA: Verificar se tem visualizações com validação consistente
    const hasViews = (value: string | number | null | undefined) => {
      if (!value) return false;
      
      let numValue = 0;
      if (typeof value === 'string') {
        // Mesma lógica de limpeza usada no cálculo total
        const cleanValue = value.replace(/[.,\s]/g, '');
        numValue = parseInt(cleanValue) || 0;
      } else {
        numValue = Number(value) || 0;
      }
      
      return numValue > 0;
    };
    
    // 🔍 CONTAGEM DE TIPOS ATIVOS - usar mesma ordem do cálculo
    const activeContentTypesArray = [
      { name: 'Instagram Stories', hasData: hasViews(selectedInfluencer?.instagramStoriesViews) },
      { name: 'Instagram Reels', hasData: hasViews(selectedInfluencer?.instagramReelsViews) },
      { name: 'TikTok Vídeos', hasData: hasViews(selectedInfluencer?.tiktokVideoViews) },
      { name: 'YouTube Shorts', hasData: hasViews(selectedInfluencer?.youtubeShortsViews) },
      { name: 'YouTube Longos', hasData: hasViews(selectedInfluencer?.youtubeLongFormViews) },
      { name: 'Facebook Stories', hasData: hasViews(selectedInfluencer?.facebookStoriesViews) },
      { name: 'Facebook Reels', hasData: hasViews(selectedInfluencer?.facebookReelsViews) },
      { name: 'Facebook Posts', hasData: hasViews(selectedInfluencer?.facebookViews) },
      { name: 'Twitch Streams', hasData: hasViews(selectedInfluencer?.twitchViews) },
      { name: 'Kwai Vídeos', hasData: hasViews(selectedInfluencer?.kwaiViews) }
    ];
    
    activeTypes = activeContentTypesArray.filter(type => type.hasData).length;
    
    return activeTypes;
  }, [selectedInfluencer]);

  // 🆕 Estado para controlar a plataforma selecionada nos gráficos unificados
  const [selectedPlatform, setSelectedPlatform] = useState<string>('instagram');

  // 🆕 Effect para definir a primeira plataforma disponível quando um influenciador é selecionado
  useEffect(() => {
    if (selectedInfluencer?.currentDemographics && Array.isArray(selectedInfluencer.currentDemographics) && selectedInfluencer.currentDemographics.length > 0) {
      const platformOrder = ['instagram', 'tiktok', 'youtube', 'facebook', 'twitch', 'kwai'];
      const availablePlatforms = selectedInfluencer.currentDemographics
        .map((d: any) => d.platform)
        .filter((p: string) => platformOrder.includes(p))
        .sort((a: string, b: string) => {
          const indexA = platformOrder.indexOf(a);
          const indexB = platformOrder.indexOf(b);
          return indexA - indexB;
        });
      
      // Se a plataforma atual não está disponível ou se não há plataforma selecionada, selecionar a primeira disponível
      if (availablePlatforms && Array.isArray(availablePlatforms) && availablePlatforms.length > 0 && (!selectedPlatform || !availablePlatforms.includes(selectedPlatform))) {
        setSelectedPlatform(availablePlatforms[0]);
      }
    }
  }, [selectedInfluencer?.currentDemographics, selectedInfluencer?.id]);

  // Estados para quantidades dos serviços
  const [serviceQuantities, setServiceQuantities] = useState<Record<string, number>>({});

  // Estado para status do influencer na proposta
  const [influencerStatus, setInfluencerStatus] = useState<'pendente' | 'aceito' | 'rejeitado' | 'descartado' | null>(null);
  const [isEditingStatus, setIsEditingStatus] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(false);

  // Estados para contrapropostas
  const [showCounterProposalModal, setShowCounterProposalModal] = useState(false);
  const [counterProposalAmount, setCounterProposalAmount] = useState('');
  const [counterProposalCurrency, setCounterProposalCurrency] = useState('BRL');
  const [counterProposalNote, setCounterProposalNote] = useState('');
  const [savingCounterProposal, setSavingCounterProposal] = useState(false);
  const [selectedBudgetForCounter, setSelectedBudgetForCounter] = useState<any>(null);

  // 🔥 NOVO: Estado para controlar o carregamento dos orçamentos
  const [isLoadingBudgets, setIsLoadingBudgets] = useState(false);



  // Estados para o sheet de orçamentos
  const [showBudgetSheet, setShowBudgetSheet] = useState(false);

  // Estado para armazenar marcas do usuário
  const [userBrands, setUserBrands] = useState<any[]>([]);

  // 🔥 NOVO: Estados para recálculo manual
  const [isRecalculating, setIsRecalculating] = useState(false);
  const [recalcResult, setRecalcResult] = useState<string | null>(null);

  // Função para buscar quantidades dos serviços do array counterProposals
  const fetchServiceQuantities = async (influencerId: string) => {
    if (!selectedInfluencer || !userId) {
      return;
    }

    try {
      // Buscar quantidades do existingBudgets que já contém os counterProposals
      const quantitiesMap: Record<string, number> = {};
      
      // Percorrer existingBudgets para encontrar quantidades nos counterProposals
      Object.entries(existingBudgets).forEach(([serviceKey, budget]) => {
        if (budget.counterProposals && budget.counterProposals.length > 0) {
          // Pegar quantidade da contraproposta mais recente aceita ou primeira pendente
          const acceptedProposal = budget.counterProposals.find((cp: any) => cp.status === 'accepted');
          const targetProposal = acceptedProposal || budget.counterProposals[0];
          
          if (targetProposal && targetProposal.quantity) {
            quantitiesMap[serviceKey] = targetProposal.quantity;
          }
        }
      });
      
      setServiceQuantities(quantitiesMap);
    } catch (error) {
      console.error('Erro ao buscar quantidades dos serviços:', error);
      setServiceQuantities({});
    }
  };

  // 🔥 REMOVIDO: useEffect que estava causando loop - a seleção via URL é gerenciada pelo InfluencerGrid

  // Efeito para carregar orçamentos quando proposta E influenciador estão selecionados
  useEffect(() => {
    if (selectedProposal && selectedInfluencer && userId) {
      fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
    } else if (selectedInfluencer && userId) {
      fetchExistingBudgets(selectedInfluencer.id);
    } else {
      // Limpar orçamentos se qualquer um dos pré-requisitos não estiver presente
      setExistingBudgets({});
      setServiceQuantities({});
    }
  }, [selectedProposal, selectedInfluencer, userId]);

  // Efeito para buscar quantidades após existingBudgets ser carregado
  useEffect(() => {
    if (selectedInfluencer && Object.keys(existingBudgets).length > 0) {
      fetchServiceQuantities(selectedInfluencer.id);
    }
  }, [existingBudgets, selectedInfluencer]);

  // 🆕 NOVO: Efeito para recarregar documentos quando proposta ou influenciador mudarem
  useEffect(() => {
    if (selectedInfluencer) {
      fetchDocuments();
    } else {
      setDocuments([]);
    }
  }, [selectedProposal?.id, selectedInfluencer?.id]);

  // 🔥 NOVO: useEffect para sincronizar orçamentos quando proposta ou influenciador mudarem
  useEffect(() => {
    if (selectedInfluencer && userId) {
      if (selectedProposal) {
        fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
        // 🆕 Buscar também o status do influencer na proposta
        fetchInfluencerStatus(selectedProposal.id, selectedInfluencer.id);
      } else {
        fetchExistingBudgets(selectedInfluencer.id);
        // Limpar status quando não há proposta selecionada
        setInfluencerStatus(null);
      }
    } else if (!selectedInfluencer) {
      setExistingBudgets({});
      setInfluencerStatus(null);
      setIsEditingStatus(false);
    }
  }, [selectedProposal, selectedInfluencer, userId]);

  // 🆕 LAZY LOADING: Carregar documentos quando necessário
  useEffect(() => {
    // Carregar documentos apenas quando um influencer está selecionado e ainda não foram carregados
    if (selectedInfluencer?.id && !documentsLoaded) {
      const loadDocuments = async () => {
        try {
          await loadFinancialDataOnDemand(selectedInfluencer.id);
          setDocumentsLoaded(true);
        } catch (error) {
          console.error('❌ Erro ao carregar documentos:', error);
        }
      };

      // Carregar com um pequeno delay para não impactar a performance da seleção
      const timeoutId = setTimeout(loadDocuments, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [selectedInfluencer?.id, documentsLoaded]);

  // Efeito para carregar marcas do usuário
  useEffect(() => {
    if (userId) {
      fetchUserBrands();
    }
  }, [userId]);

  // 🔄 LISTENER PARA EVENTOS DE ATUALIZAÇÃO DE INFLUENCIADORES NO PAINEL DE CONTATO
  useEffect(() => {
    const handleInfluencerUpdate = async (event: CustomEvent) => {
      const { influencerId, action } = event.detail || {};
      
      // Se o influenciador atualizado é o mesmo que está selecionado, atualizar os dados
      if (selectedInfluencer && influencerId === selectedInfluencer.id) {
        console.log('🔄 [PAINEL CONTATO] Atualizando dados do influenciador selecionado:', influencerId);
        
        try {
          // 🔥 OTIMIZADO: Evitar chamada desnecessária à API - usar dados em cache ou lazy loading
          console.log('🔄 [PAINEL CONTATO] Sincronizando dados do influenciador:', influencerId);

          // Se os dados financeiros já foram carregados, recarregar apenas se necessário
          if (documentsLoaded) {
            await loadFinancialDataOnDemand(influencerId);
          }

          // Para dados básicos, manter os existentes (evita chamada desnecessária à API)
          return; // Sair da função para evitar a chamada à API

          const influencerResponse = await fetch(`/api/influencers?id=${influencerId}&includeFinancials=true`);
          
          if (influencerResponse.ok) {
            const influencerData = await influencerResponse.json();
            
            // Manter a estrutura existente mas com dados atualizados
            // 🔄 MAPEAMENTO CORRETO: Garantir compatibilidade entre API e painel
            const updatedInfluencer = {
              ...selectedInfluencer,
              ...influencerData,
              // Preservar propriedades críticas
              id: influencerId,
              // 🔄 MAPEAMENTO DE CAMPOS: API para painel de contato
              name: influencerData.name || influencerData.nome || selectedInfluencer.name,
              whatsapp: influencerData.whatsapp || influencerData.financialData?.whatsapp || selectedInfluencer.whatsapp,
              email: influencerData.email || influencerData.financialData?.email || selectedInfluencer.email,
              age: influencerData.age || influencerData.idade || selectedInfluencer.age,
              gender: influencerData.gender || influencerData.genero || selectedInfluencer.gender,
              city: influencerData.city || influencerData.cidade || selectedInfluencer.city,
              state: influencerData.state || influencerData.estado || selectedInfluencer.state,
              country: influencerData.country || influencerData.pais || selectedInfluencer.country,
              avatar: influencerData.avatar || selectedInfluencer.avatar,
              expertise: influencerData.expertise || influencerData.categoria || selectedInfluencer.expertise
            };
            
            console.log('✅ [PAINEL CONTATO] Dados do influenciador atualizados:', updatedInfluencer);
            setSelectedInfluencer(updatedInfluencer);
            
            // Atualizar também dados financeiros se disponíveis
            const financialData = influencerData.financials || influencerData.financialData;
            if (financialData) {
              setFinancialInfo({
                pricing: updatedInfluencer.pricing,
                hasFinancialData: true,
                ...financialData
              });
              
              // Atualizar documentos
              const documentsArray = financialData.additionalData?.documents || [];
              setDocuments(documentsArray);
            }
          } else {
            console.error('❌ [PAINEL CONTATO] Erro ao buscar dados atualizados:', influencerResponse.status);
          }
        } catch (error) {
          console.error('❌ [PAINEL CONTATO] Erro na atualização automática:', error);
        }
      }
    };

    // Adicionar listener para eventos de atualização de influenciadores
    window.addEventListener('influencer-updated', handleInfluencerUpdate as unknown as EventListener);
    
    // Cleanup: remover listener quando componente for desmontado
    return () => {
      window.removeEventListener('influencer-updated', handleInfluencerUpdate as unknown as EventListener);
    };
  }, [selectedInfluencer]); // Dependência do selectedInfluencer para recriar listener quando mudou





  // Função para buscar marcas do usuário
  const fetchUserBrands = async () => {
    if (!userId || !clerkUser) return;
    
    try {
      const response = await fetch(`/api/brands/user/${userId}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': clerkUser.id
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUserBrands(data.brands || []);
      }
    } catch (error) {
      // Erro ao buscar marcas - verificar configuração de API
    }
  };

  // Lista de localizações populares (pode ser carregada de uma API)
  const popularLocationsList = t('influencers.popular_locations') as string[];
  
  // Função para lidar com adição de novo influenciador removida
  
  // Função para lidar com exclusão de influenciador
  const handleDeleteInfluencer = (id: string) => {
    // Aqui você implementaria a lógica para excluir um influenciador
    // Após excluir, recarregue a lista
    setRefreshKey(prev => prev + 1);
  };
  
  // 🔥 CORREÇÃO: Função otimizada para buscar campanhas
  const fetchInfluencerCampaigns = async (influencerId: string) => {
    if (!influencerId) {
      setInfluencerCampaigns([]);
      return;
    }
    
    try {
      const campaigns = await CampaignService.getCampaignsByInfluencer(influencerId);
      setInfluencerCampaigns(campaigns || []);
    } catch (error) {
      // Erro ao buscar campanhas - verificar conectividade
      setInfluencerCampaigns([]);
    }
  };

  // ⚡ OTIMIZAÇÃO: Agora usando contexto compartilhado de propostas
  // Função removida - dados vêm do ProposalsContext

  // Função para selecionar uma proposta
  const handleSelectProposal = (proposal: any) => {
    setSelectedProposal(proposal);
    setShowProposalSelector(false);
    
    // 🔥 NOVO: Atualizar URL com a proposta selecionada
    const params = new URLSearchParams(searchParams || '');
    if (proposal) {
      // Adicionar o ID da proposta na URL
      params.set('proposta', proposal.id);

      // 🔥 NOVO: Auto-adicionar primeiro influenciador da proposta se não há um na URL
      if (!params.get('influencer') && proposal.influencers && Array.isArray(proposal.influencers) && proposal.influencers.length > 0) {
        const firstInfluencerId = proposal.influencers[0];
        params.set('influencer', firstInfluencerId);
        console.log('🎯 [MANUAL_SELECT] Adicionando primeiro influenciador da proposta à URL:', firstInfluencerId);
      }
    } else {
      // Remover o parâmetro da proposta da URL
      params.delete('proposta');
    }

    // Atualizar a URL sem recarregar a página
    const newUrl = `${pathname}?${params.toString()}`;
    router.replace(newUrl);
    
    // Os orçamentos serão atualizados pelo useEffect que monitora mudanças
  };

  // Função para alternar o seletor de propostas
  const toggleProposalSelector = () => {
    setShowProposalSelector(!showProposalSelector);
    if (!showProposalSelector && (!userProposals || !Array.isArray(userProposals) || userProposals.length === 0)) {
      fetchUserProposals();
    }
  };

  // 🔥 CORREÇÃO: Agora os dados vêm direto da query GraphQL principal
  // Não é mais necessário buscar dados demográficos separadamente

  // 🔥 CORREÇÃO: Referência para prevenir múltiplas chamadas simultâneas
  const lastSelectedInfluencerRef = useRef<string | null>(null);

  // 🆕 FUNÇÃO LAZY: Carregar dados financeiros sob demanda
  const loadFinancialDataOnDemand = async (influencerId: string) => {
    try {
      console.log('📄 [LAZY] Carregando dados financeiros sob demanda para:', influencerId);

      const influencerResponse = await fetch(`/api/influencers?id=${influencerId}&includeFinancials=true`);

      if (influencerResponse.ok) {
        const influencerData = await influencerResponse.json();
        const financialData = influencerData.financials || influencerData.financialData;

        if (financialData) {
          console.log('📄 [LAZY] Dados financeiros carregados:', financialData);

          // Atualizar informações financeiras
          setFinancialInfo(prev => ({
            ...prev,
            hasFinancialData: true,
            ...financialData
          }));

          // Extrair e definir documentos
          const documentsArray = financialData.additionalData?.documents || [];
          console.log('📄 [LAZY] Documentos encontrados:', documentsArray.length);
          setDocuments(documentsArray);

          return financialData;
        }
      } else {
        console.error('❌ [LAZY] Erro ao buscar dados financeiros:', influencerResponse.status);
      }
    } catch (error) {
      console.error('❌ [LAZY] Erro ao carregar dados financeiros:', error);
    }

    return null;
  };
  
  // 🔥 CORREÇÃO: Função otimizada para evitar múltiplas requisições
  const handleSelectInfluencer = async (influencer: any) => {
    console.log('🎯 [SELECT] Iniciando seleção do influencer:', influencer?.name, 'ID:', influencer?.id);

    // 🔥 CORREÇÃO: Verificar se já estamos processando o mesmo influencer
    if (lastSelectedInfluencerRef.current === influencer?.id) {
      console.log('🚫 [SKIP] Influencer já sendo processado:', influencer?.id);
      return;
    }

    // Marcar como seleção manual para evitar interferência de lógicas automáticas
    setIsManualSelection(true);
    lastSelectedInfluencerRef.current = influencer?.id;
    showLoader();

    try {
      // 🔥 CORREÇÃO: Atualizar URL de forma mais controlada
      if (influencer?.id) {
        const currentParams = new URLSearchParams(window.location.search);
        currentParams.set('influencer', influencer.id);
        const newUrl = `${pathname}?${currentParams.toString()}`;

        console.log('🔄 [URL_UPDATE] Atualizando URL para:', newUrl);
        // Usar router.replace para não adicionar ao histórico
        router.replace(newUrl, { scroll: false });
      }

      // Limpar estados anteriores primeiro
      setFinancialInfo(null);
      setDocuments([]);
      setDocumentsLoaded(false); // Reset do estado de documentos carregados
      setInfluencerCampaigns([]);
      setExistingBudgets({}); // Limpar orçamentos anteriores

      // Atualizar influencer selecionado após limpeza
      setSelectedInfluencer(influencer);

      if (!influencer?.id) {
        lastSelectedInfluencerRef.current = null; // Reset da referência
        hideLoader();
        return;
      }

      // 🔥 REMOVIDO: Chamada desnecessária à API para dados financeiros
      // Os dados financeiros serão carregados sob demanda quando necessário
      // Isso elimina a chamada desnecessária: /api/influencers?id=${influencer.id}&includeFinancials=true

      // Usar dados básicos do pricing se disponível
      if (influencer.pricing) {
        setFinancialInfo({
          pricing: influencer.pricing,
          hasFinancialData: influencer.pricing?.hasFinancialData || false
        });
      }
      
      // Buscar campanhas (ainda usando REST por enquanto)
      await fetchInfluencerCampaigns(influencer.id);
      
      // ✅ ORÇAMENTOS: Serão carregados automaticamente pelo useEffect que monitora mudanças
      
      // 🆕 DOCUMENTOS: Para influenciadores independentes (sem proposta), buscar via API
      // Para propostas, os documentos vêm automaticamente via GraphQL
      if (!selectedProposal) {
        await fetchDocuments();
      }
      
      hideLoader();
      lastSelectedInfluencerRef.current = null; // 🔥 CORREÇÃO: Reset da referência após sucesso
      setIsManualSelection(false); // 🔥 CORREÇÃO: Reset do flag de seleção manual
      
    } catch (error) {
      setDocuments([]);
      setInfluencerCampaigns([]);
      setExistingBudgets({});
      hideLoader();
      lastSelectedInfluencerRef.current = null; // 🔥 CORREÇÃO: Reset da referência após erro
      setIsManualSelection(false); // 🔥 CORREÇÃO: Reset do flag de seleção manual
    }
  };
  
  // Função para lidar com upload de documento
  const handleUploadDocument = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0 || !selectedInfluencer) {
      return;
    }
    
    try {
      const file = event.target.files[0];
      
      // Criar FormData para envio do arquivo
      const formData = new FormData();
      formData.append('file', file);
      formData.append('influencerId', selectedInfluencer.id);
      
      // 🆕 ESTRUTURA HIERÁRQUICA: Incluir proposalId se há proposta selecionada
      if (selectedProposal) {
        formData.append('proposalId', selectedProposal.id);
        console.log('📄 [UPLOAD] Enviando documento para proposta:', selectedProposal.name);
      } else {
        console.log('📄 [UPLOAD] Enviando documento independente (sem proposta)');
      }
      
      // Enviar o arquivo para a API atualizada
      const response = await fetch('/api/admin/upload-document', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error('Erro ao fazer upload do documento');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ [UPLOAD] Documento enviado com sucesso na estrutura: ${data.structure}`);
        
        // 🆕 BUSCAR DOCUMENTOS ATUALIZADOS: Usar nova API que considera a estrutura
        await fetchDocuments();
        
        // Mostrar notificação de sucesso
        toast.success('Documento enviado com sucesso!', {
          description: selectedProposal 
            ? `Salvo na proposta: ${selectedProposal.name}` 
            : 'Salvo no perfil do influenciador'
        });
      } else {
        throw new Error(data.message || 'Erro ao processar documento no servidor');
      }
      
      // Limpar o input de arquivo
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('❌ [UPLOAD] Erro no upload:', error);
      toast.error('Erro ao fazer upload do documento', {
        description: error instanceof Error ? error.message : 'Tente novamente'
      });
    }
  };
  
  // 🆕 Função para buscar documentos usando a nova API
  const fetchDocuments = async () => {
    if (!selectedInfluencer) {
      setDocuments([]);
      return;
    }

    try {
      console.log('📄 [FETCH] Buscando documentos:', {
        influencerId: selectedInfluencer.id,
        proposalId: selectedProposal?.id || null
      });

      // Construir URL da API com parâmetros apropriados
      const searchParams = new URLSearchParams({
        influencerId: selectedInfluencer.id
      });

      // Incluir proposalId se há proposta selecionada
      if (selectedProposal) {
        searchParams.append('proposalId', selectedProposal.id);
      }

      const response = await fetch(`/api/admin/get-documents?${searchParams.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Erro na API: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ [FETCH] ${data.total} documentos encontrados na estrutura: ${data.structure}`);
        setDocuments(data.documents || []);
      } else {
        throw new Error(data.error || 'Erro ao buscar documentos');
      }
    } catch (error) {
      console.error('❌ [FETCH] Erro ao buscar documentos:', error);
      setDocuments([]);
    }
  };
  
  // Função para excluir documento
  const handleDeleteDocument = async (index: number) => {
    if (!selectedInfluencer) return;
    
    try {
      const documentToDelete = documents[index];
      
      console.log('📄 [DELETE] Excluindo documento:', {
        name: documentToDelete.name,
        url: documentToDelete.url,
        proposalId: selectedProposal?.id || null
      });
      
      // Preparar dados para a API de exclusão
      const deleteData: {
        influencerId: string;
        documentUrl: string;
        proposalId?: string;
        documentId?: string;
      } = {
        influencerId: selectedInfluencer.id,
        documentUrl: documentToDelete.url
      };

      // 🆕 ESTRUTURA HIERÁRQUICA: Incluir dados da proposta se necessário
      if (selectedProposal) {
        deleteData.proposalId = selectedProposal.id;
        if ((documentToDelete as any).id) {
          deleteData.documentId = (documentToDelete as any).id; // Usar ID do documento se disponível
        }
      }
      
      // Chamar a API atualizada para exclusão de documentos
      const response = await fetch('/api/admin/delete-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(deleteData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao excluir documento');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ [DELETE] Documento excluído da estrutura: ${data.structure}`);
        
        // 🆕 BUSCAR DOCUMENTOS ATUALIZADOS: Usar nova API que considera a estrutura
        await fetchDocuments();
        
        // Mostrar notificação de sucesso
        toast.success('Documento excluído com sucesso!', {
          description: selectedProposal 
            ? `Removido da proposta: ${selectedProposal.name}` 
            : 'Removido do perfil do influenciador'
        });
      } else {
        throw new Error(data.error || 'Erro ao excluir documento no servidor');
      }
    } catch (error) {
      console.error('❌ [DELETE] Erro na exclusão:', error);
      toast.error('Erro ao excluir documento', {
        description: error instanceof Error ? error.message : 'Tente novamente'
      });
      
      // Recarregar documentos em caso de erro para manter sincronia
      await fetchDocuments();
    }
  };

  // Funções para os filtros
  const handleLocationFilter = (location: string) => {
    setSelectedLocation(location);
  };
  
  const handleFollowersRangeChange = (min: number, max: number) => {
    setMinFollowers(min);
    setMaxFollowers(max);
  };
  
  const handleRatingChange = (rating: number) => {
    setMinRating(rating);
  };
  
  const handleVerifiedOnlyChange = (value: boolean) => {
    setVerifiedOnly(value);
  };
  
  const handleAvailableOnlyChange = (value: boolean) => {
    setAvailableOnly(value);
  };
  
  const handleResetFilters = () => {
    setSelectedLocation("");
    setMinFollowers(1000);
    setMaxFollowers(1000000);
    setMinRating(1);
    setVerifiedOnly(false);
    setAvailableOnly(false);
  };
  
  // Função para controlar ambos os painéis
  const togglePanels = () => {
    setShowRelationshipPanel(!showRelationshipPanel);
    setShowContactPanel(!showContactPanel);
  };

  // Função para controlar painel de relacionamento
  const toggleRelationshipPanel = () => {
    setShowRelationshipPanel(!showRelationshipPanel);
  };

  // Função para controlar painel de contato
  const toggleContactPanel = () => {
    setShowContactPanel(!showContactPanel);
  };

  // ✅ FUNÇÃO UNIFICADA: Salvar orçamento via API (sincronizada com /propostas/id)
  const handleSaveBudget = async () => {
    if (!selectedInfluencer || !budgetAmount || !userId) {
      toast({
        title: t('influencers.attention'),
        description: t('influencers.fill_required_fields'),
        variant: "default",
      });
      return;
    }

    // ✅ VALIDAÇÃO: Verificar se há marca válida disponível
    const finalBrandId = selectedProposal?.brandId || (userBrands.length > 0 ? userBrands[0].id : null);
    if (!finalBrandId) {
      toast({
        title: t('influencers.attention'),
        description: t('influencers.select_valid_brand'),
        variant: "default",
      });
      return;
    }

    setSavingBudget(true);
    try {
      // Preparar dados do orçamento
      const budgetData = {
        influencerId: selectedInfluencer.id,
        influencerName: selectedInfluencer.name,
        userId: userId,
        // ✅ CORREÇÃO: Usar marca validada
        brandId: finalBrandId,
        amount: parseFloat(budgetAmount.replace(/[^\d,]/g, '').replace(',', '.')),
        quantity: parseInt(budgetQuantity) || 1,
        currency: budgetCurrency,
        description: budgetDescription || `Orçamento para ${selectedInfluencer.name}`,
        serviceType: budgetServiceType || 'personalizado'
      };

      // Adicionar dados da proposta apenas se estiver selecionada
      if (selectedProposal) {
        Object.assign(budgetData, {
          proposalId: selectedProposal.id,
          proposalName: selectedProposal.name
        });
      }

      let response;
      
      if (editingBudget) {
        // Atualizar orçamento existente na hierarquia
        response = await fetch('/api/budgets', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            budgetId: editingBudget.id,
            ...budgetData
          }),
        });
      } else {
        // Criar novo orçamento
        const newBudgetData = {
          ...budgetData,
          createdAt: new Date().toISOString()
        };
        response = await fetch('/api/budgets', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(newBudgetData),
        });
      }

      if (!response.ok) {
        throw new Error(`Erro ao ${editingBudget ? 'atualizar' : 'salvar'} orçamento`);
      }

      const result = await response.json();
      
      // 🔥 CORREÇÃO: Forçar atualização imediata dos orçamentos
      if (selectedProposal && selectedInfluencer) {
        // Pequeno delay para garantir que o banco foi atualizado
        await new Promise(resolve => setTimeout(resolve, 100));
        await fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
      } else if (selectedInfluencer) {
        // Pequeno delay para garantir que o banco foi atualizado
        await new Promise(resolve => setTimeout(resolve, 100));
        await fetchExistingBudgets(selectedInfluencer.id);
      }
      
      // 🔥 NOVO: Atualizar lista de propostas para mostrar valor total atualizado no dropdown
      await fetchUserProposals();
      
      // Fechar modal e resetar campos
      setShowBudgetModal(false);
      setBudgetAmount('');
      setBudgetCurrency('BRL');
      setBudgetDescription('');
      setBudgetQuantity('1');
      setBudgetServiceType('');
      setEditingBudget(null);
      
      toast({
        title: "Sucesso!",
        description: `Orçamento ${editingBudget ? 'atualizado' : 'salvo'} com sucesso!`,
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Erro!",
        description: `Erro ao ${editingBudget ? 'atualizar' : 'salvar'} orçamento. Tente novamente.`,
        variant: "destructive",
      });
    } finally {
      setSavingBudget(false);
    }
  };

  const handleOpenBudgetModal = (serviceType: string = 'personalizado', serviceName: string = '', existingBudget?: any) => {
    if (!selectedInfluencer) {
      toast({
        title: t('influencers.attention'),
        description: t('influencers.select_influencer_first'),
        variant: "default",
      });
      return;
    }
    setBudgetServiceType(serviceType);
    
    // Se há um orçamento existente, pré-preencher os campos
    if (existingBudget) {
      setBudgetAmount(formatCurrency(String(existingBudget.amount * 100))); // Converter para centavos para o formatador
      setBudgetCurrency(existingBudget.currency || 'BRL');
      setBudgetDescription(existingBudget.description || '');
      setBudgetQuantity(String(existingBudget.quantity || 1));
      setEditingBudget(existingBudget); // Definir orçamento sendo editado
    } else {
      setBudgetAmount('');
      setBudgetCurrency('BRL');
      setBudgetDescription(serviceName ? `Orçamento para ${serviceName}` : '');
      setBudgetQuantity('1');
      setEditingBudget(null); // Novo orçamento
    }
    
    setShowBudgetModal(true);
  };

  const handleCloseBudgetModal = () => {
    setShowBudgetModal(false);
    setBudgetAmount('');
    setBudgetCurrency('BRL');
    setBudgetDescription('');
    setBudgetQuantity('1');
    setBudgetServiceType('');
    setEditingBudget(null);
  };

  const formatCurrency = (value: string) => {
    // Remove caracteres não numéricos e pontos/vírgulas
    const numeric = value.replace(/[^\d]/g, '');
    
    // Converte para número
    const amount = parseInt(numeric) || 0;
    
    // Formata como moeda brasileira
    return amount.toLocaleString('pt-BR');
  };

  const handleBudgetAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCurrency(e.target.value);
    setBudgetAmount(formatted);
  };

  // ✅ FUNÇÃO UNIFICADA: Buscar orçamentos via API (sincronizada com /propostas/id)
  const fetchExistingBudgets = async (influencerId: string, brandId?: string) => {
    if (!influencerId || !userId) {
      return;
    }

    // 🔥 CORREÇÃO: Evitar chamadas simultâneas
    const fetchKey = `${influencerId}_${brandId || 'no-brand'}_${selectedProposal?.id || 'no-proposal'}`;

    // 🔥 NOVO: Ativar estado de loading
    setIsLoadingBudgets(true);

    try {
      // 🆕 MIGRAÇÃO PARA GRAPHQL: Buscar orçamentos usando GraphQL
      let budgetsData = null;
      let collaboratorCounterProposals: any[] = [];
      let managerCounterProposals: any[] = [];
      
      if (selectedProposal) {
      
        // Buscar orçamentos específicos do influenciador na proposta
        try {
          const { data } = await apolloClient.query({
            query: GET_INFLUENCER_BUDGETS_IN_PROPOSAL,
            variables: {
              proposalId: selectedProposal.id,
          influencerId,
          userId
            },
            fetchPolicy: 'network-only' // Sempre buscar dados atualizados
          });

          if (data?.influencerBudgetsInProposal) {
            budgetsData = {
              success: true,
              budgets: data.influencerBudgetsInProposal.budgets || [],
              permissions: data.influencerBudgetsInProposal.permissions,
              userRole: data.influencerBudgetsInProposal.userRole
            };
            
            // 🆕 NOVO: Usar documentos retornados pelo GraphQL
            const documentsFromGraphQL = data.influencerBudgetsInProposal.documents || [];
            if (documentsFromGraphQL.length > 0) {
              console.log(`📄 [GRAPHQL] ${documentsFromGraphQL.length} documentos encontrados via GraphQL`);
              setDocuments(documentsFromGraphQL);
            } else {
              console.log('📄 [GRAPHQL] Nenhum documento encontrado via GraphQL');
              setDocuments([]);
            }
          }
        } catch (error) {
          budgetsData = { success: false, budgets: [] };
        }

        // Buscar contrapropostas de colaboradores e enriquecer com dados do Clerk
        try {
          const { data: collaboratorData } = await apolloClient.query({
            query: GET_COLLABORATOR_COUNTER_PROPOSALS,
            variables: {
              proposalId: selectedProposal.id,
              userId,
              status: null // Buscar todas as contrapropostas
            },
            fetchPolicy: 'network-only'
          });

          if (collaboratorData?.collaboratorCounterProposals) {
            // Filtrar apenas as contrapropostas relacionadas a este influenciador
            const rawCollaboratorCounterProposals = collaboratorData.collaboratorCounterProposals.filter(
              (cp: any) => cp.influencerId === influencerId
            );
            
            // 🎯 Buscar dados completos dos usuários no Clerk para colaboradores
            if (rawCollaboratorCounterProposals.length > 0) {
              // Modificar o formato para ser compatível com a função de enrichment
              const proposalsForEnrichment = rawCollaboratorCounterProposals.map((proposal: any) => ({
                ...proposal,
                proposedBy: proposal.proposedBy?.userId || proposal.proposedBy,
                proposedAt: proposal.createdAt ? new Date(proposal.createdAt) : new Date()
              }));
              
              collaboratorCounterProposals = await enrichCounterProposalsWithUserData(proposalsForEnrichment);
            }
            
            // 🆕 ORDENAR CONTRAPROPOSTAS: Mais antiga primeiro
            collaboratorCounterProposals.sort((a, b) => {
              const dateA = new Date(a.createdAt || 0).getTime();
              const dateB = new Date(b.createdAt || 0).getTime();
              return dateA - dateB; // Mais antiga primeiro
            });
            
          }
        } catch (error) {
          // Erro ao buscar contrapropostas - verificar configuração GraphQL
        }

                // 🆕 Extrair contrapropostas diretamente dos orçamentos e enriquecer com dados do Clerk
        if (budgetsData?.success && budgetsData.budgets) {
          try {
            
            const allCounterProposals: any[] = [];
            
            budgetsData.budgets.forEach((budget: any) => {
              if (budget.counterProposals && Array.isArray(budget.counterProposals)) {
                budget.counterProposals.forEach((cp: any) => {
                  allCounterProposals.push({
                      ...cp,
                      budgetId: budget.id,
                    type: cp.type || 'manager'
                  });
                });
              }
            });
            
            // Filtrar apenas contrapropostas de manager
            const rawManagerCounterProposals = allCounterProposals.filter(cp => cp.type === 'manager' || !cp.type);
            
            // 🎯 Buscar dados completos dos usuários no Clerk para cada contraproposta
            if (rawManagerCounterProposals.length > 0) {
              managerCounterProposals = await enrichCounterProposalsWithUserData(rawManagerCounterProposals);
            }
            

          } catch (error) {
            console.warn('⚠️ [MANAGER] Erro ao extrair contrapropostas:', error);
          }
        }
      } else {
        // Buscando orçamentos independentes para o influenciador
        console.log('📋 [INFLUENCER] Orçamentos independentes desabilitados');
        
        // Para orçamentos independentes, não há contrapropostas de managers
        managerCounterProposals = [];
        
        // Definir dados vazios para orçamentos independentes
        budgetsData = { success: true, budgets: [] };
      }
      
      if (budgetsData) {
        // Verificar se há erro nos dados mesmo com status 200
        if ('error' in budgetsData && budgetsData.error) {
          console.warn('⚠️ [DEBUG] API retornou erro:', budgetsData.error);
          console.warn('⚠️ [DEBUG] Detalhes:', (budgetsData as any).details);
        }
        
        if (budgetsData.success && budgetsData.budgets && Array.isArray(budgetsData.budgets)) {
          // Criar um mapa de serviceType -> orçamento para acesso rápido
          const budgetMap: Record<string, any> = {};
          
          for (const budget of budgetsData.budgets) {
            try {
              // Verificar se o orçamento tem dados válidos
              if (budget && budget.serviceType && budget.id) {
                // 🔥 NOVO: Adicionar contrapropostas de colaboradores relacionadas
                const relatedCollaboratorCounterProposals = collaboratorCounterProposals.filter(
                  (cp: any) => cp.budgetId === budget.id
                );
                
                // 🆕 NOVO: Contrapropostas já vêm do GraphQL no budget.counterProposals
                let existingCounterProposals = budget.counterProposals || [];
                
                // 🎯 Enriquecer contrapropostas do GraphQL com dados do usuário
                if (existingCounterProposals.length > 0) {
                  existingCounterProposals = await enrichCounterProposalsWithUserData(existingCounterProposals);
                }
                
                // 🔥 CORREÇÃO: Usar serviceType diretamente como chave (que já vem no formato correto da API)
                // A API já envia o serviceType no formato correto (ex: "instagram_story", "youtube_video")
                const budgetKey = budget.serviceType;
                

                
                // Combinar todas as contrapropostas (do GraphQL enriquecidas + colaboradores)
                const allCounterProposals = [
                  ...existingCounterProposals, // Contrapropostas enriquecidas do GraphQL
                  ...relatedCollaboratorCounterProposals.map((cp: any) => ({ ...cp, type: 'collaborator' }))
                ];
                
                // Adicionar orçamento ao mapa usando serviceType como chave
                budgetMap[budgetKey] = {
                  ...budget,
                  hasCounterProposal: allCounterProposals.length > 0,
                  counterProposals: allCounterProposals, // Todas as contrapropostas
                  collaboratorCounterProposals: relatedCollaboratorCounterProposals // Para compatibilidade
                };
              }
            } catch (err) {
              console.error('❌ [DEBUG] Erro ao processar orçamento:', err);
            }
          }
          
          // Mapa de orçamentos GraphQL criado e contrapropostas processadas
          setExistingBudgets(budgetMap);
          
          // Estado dos orçamentos atualizado
        } else {
          console.log('⚠️ [DEBUG] Dados de orçamentos inválidos ou vazios');
          setExistingBudgets({});
        }
      } else {
        console.error('❌ [DEBUG] Erro na busca de orçamentos GraphQL');
        setExistingBudgets({});
      }
    } catch (error) {
      console.error('❌ [DEBUG] Erro na função fetchExistingBudgets com GraphQL:', error);
      setExistingBudgets({});
    } finally {
      // 🔥 NOVO: Desativar estado de loading após completar
      setIsLoadingBudgets(false);
    }
  };

  // Função helper para renderizar serviços dinamicamente
  const renderServiceItem = (serviceName: string, serviceData: any, platform: string) => {
    if (!serviceData || !serviceData.price) return null;
    
    const serviceKey = `${platform.toLowerCase()}_${serviceName.toLowerCase()}`;
    const displayName = `${serviceName}`;
    

    
    // Buscar orçamento pela chave do serviço
    const existingBudget = existingBudgets[serviceKey];
    
    return (
      <div key={serviceKey} className="grid grid-cols-4 gap-2 py-1 hover:bg-muted/10 transition-colors">
        {/* Coluna 1: Nome do Serviço */}
        <div className="text-xs font-medium text-muted-foreground">
          {displayName}
        </div>
        
        {/* Coluna 2: Pricing */}
        <div className="text-center">
          <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded">
            R$ {serviceData.price?.toLocaleString('pt-BR') || '0'}
          </span>
        </div>
        
        {/* Coluna 3: Orçamento */}
        <div className="text-center">
            {existingBudget ? (
                <button
                  onClick={() => handleOpenBudgetModal(serviceKey, displayName, existingBudget)}
                  className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded border border-green-200 dark:border-green-800 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                  title={t('influencers.click_to_edit_budget')}
                >
              R$ {existingBudget.amount?.toLocaleString('pt-BR') || '0'}
                </button>
            ) : (
              <button
                onClick={() => handleOpenBudgetModal(serviceKey, displayName)}
                className="text-xs px-2 py-1 text-green-600 hover:text-green-700 hover:bg-green-50 border border-green-200 dark:border-green-800 rounded transition-colors"
              >
              + Orçar
              </button>
            )}
        </div>

        {/* Coluna 4: Contraproposta */}
        <div className="text-center">
            {existingBudget?.hasCounterProposal && existingBudget.counterProposals?.[0] ? (
            <button
              onClick={() => handleOpenCounterProposalModal(existingBudget)}
              className="text-xs px-2 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 rounded border border-amber-200 dark:border-amber-800 hover:bg-amber-200 dark:hover:bg-amber-900/50 transition-colors"
            >
                R$ {existingBudget.counterProposals[0].counterAmount?.toLocaleString('pt-BR') || '0'}
            </button>
          ) : existingBudget ? (
            <button
              onClick={() => handleOpenCounterProposalModal(existingBudget)}
              className="text-xs px-2 py-1 text-amber-600 hover:text-amber-700 hover:bg-amber-50 border border-amber-200 dark:border-amber-800 rounded transition-colors"
              title="Fazer contraproposta"
            >
              + Contra
            </button>
          ) : (
            <span className="text-xs text-muted-foreground">-</span>
          )}
        </div>
      </div>
    );
  };

  // Função helper para renderizar plataforma completa
  const renderPlatformServices = (platformName: string, platformData: any, bgColor: string) => {
    if (!platformData || typeof platformData !== 'object') return null;
    
    const services = Object.entries(platformData).filter(([key, value]) => 
      value && typeof value === 'object' && (value as any).price
    );
    
    if (services.length === 0) return null;
    
    return (
      <div key={platformName} className="space-y-1">
          {services.map(([serviceName, serviceData]) => 
            renderServiceItem(serviceName, serviceData, platformName)
          )}
      </div>
    );
  };

  // Funções para contrapropostas
  const handleOpenCounterProposalModal = (budget: any) => {
    if (!selectedInfluencer) {
      toast({
        title: t('influencers.attention'),
        description: t('influencers.select_influencer_first'),
        variant: "default",
      });
      return;
    }
    setSelectedBudgetForCounter(budget);
    setCounterProposalAmount('');
    setCounterProposalCurrency('BRL');
    setCounterProposalNote('');
    setShowCounterProposalModal(true);
  };

  const handleCloseCounterProposalModal = () => {
    setShowCounterProposalModal(false);
    setCounterProposalAmount('');
    setCounterProposalCurrency('BRL');
    setCounterProposalNote('');
    setSelectedBudgetForCounter(null);
  };

  const handleCounterProposalAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCurrency(e.target.value);
    setCounterProposalAmount(formatted);
  };

  const handleSaveCounterProposal = async () => {
    if (!selectedInfluencer || !counterProposalAmount || !userId || !selectedBudgetForCounter) {
      toast({
        title: t('influencers.attention'),
        description: t('influencers.fill_required_fields'),
        variant: "default",
      });
      return;
    }

    setSavingCounterProposal(true);
    try {
      // 🔥 NOVO: Detectar se é contexto de colaborador em proposta
      if (selectedProposal) {
        console.log('💼 [COLABORADOR] Enviando contraproposta de colaborador para proposta:', selectedProposal.id);
        
        // 🔧 CORRIGIDO: Enviar os campos corretos que a API espera
        const collaboratorCounterProposalData = {
          budgetId: selectedBudgetForCounter.id, // 🆕 Campo obrigatório que estava faltando
          influencerId: selectedInfluencer.id,
          influencerName: selectedInfluencer.name,
          userId: userId,
          originalAmount: selectedBudgetForCounter.amount,
          counterAmount: parseFloat(counterProposalAmount.replace(/[^\d,]/g, '').replace(',', '.')), // 🔧 Nome correto do campo
          counterCurrency: counterProposalCurrency, // 🔧 Nome correto do campo
          note: counterProposalNote || '', // 🔧 Nome correto do campo
          serviceType: selectedBudgetForCounter.serviceType,
          proposalId: selectedProposal.id,
          type: 'collaborator' // Identificar como contraproposta de colaborador
        };

        console.log('📤 [DEBUG] Enviando dados:', collaboratorCounterProposalData);
        
        const response = await fetch('/api/budgets/counter-proposal', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(collaboratorCounterProposalData),
        });

        console.log('📡 [DEBUG] Response status:', response.status);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Erro desconhecido' }));
          console.error('❌ [DEBUG] Erro da API:', errorData);
          throw new Error(`Erro ao salvar contraproposta: ${errorData.error || 'Erro desconhecido'}`);
        }

        const result = await response.json();
        console.log('✅ Contraproposta de colaborador salva:', result);
        
        alert('Contraproposta de colaborador enviada com sucesso!');
      } else {
        // 🔄 NOVA ESTRUTURA: Usar API refatorada para contrapropostas
        console.log('📋 [GERAL] Enviando contraproposta com nova estrutura');
        
        const counterProposalData = {
          budgetId: selectedBudgetForCounter.id,
          influencerId: selectedInfluencer.id,
          influencerName: selectedInfluencer.name,
          userId: userId,
          originalAmount: selectedBudgetForCounter.amount,
          counterAmount: parseFloat(counterProposalAmount.replace(/[^\d,]/g, '').replace(',', '.')),
          counterCurrency: counterProposalCurrency,
          note: counterProposalNote || '',
          serviceType: selectedBudgetForCounter.serviceType,
          proposalId: selectedProposal?.id // 🆕 Incluir proposalId se houver
        };

        const response = await fetch('/api/budgets/counter-proposal', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(counterProposalData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Erro ao salvar contraproposta');
        }

        const result = await response.json();
        console.log('✅ Contraproposta salva com nova estrutura:', result);
        
        toast.success('Contraproposta enviada com sucesso!');
      }
      
      // Atualizar orçamentos locais
      if (selectedProposal) {
        await fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
      } else if (selectedInfluencer) {
        await fetchExistingBudgets(selectedInfluencer.id);
      }
      
      // 🔥 NOVO: Atualizar lista de propostas para mostrar valor total atualizado no dropdown
      console.log('🔄 [COUNTER_PROPOSAL_SAVED] Atualizando lista de propostas após salvar contraproposta...');
      await fetchUserProposals();
      console.log('✅ [COUNTER_PROPOSAL_SAVED] Lista de propostas atualizada');
      
      // Fechar modal e resetar campos
      handleCloseCounterProposalModal();
      
    } catch (error) {
      console.error('❌ Erro ao salvar contraproposta:', error);
      toast.error('Erro ao enviar contraproposta. Tente novamente.');
    } finally {
      setSavingCounterProposal(false);
    }
  };

  // 🔥 NOVA FUNÇÃO: Recálculo manual do total da proposta
  const handleRecalculateProposalTotal = async () => {
    if (!selectedProposal || !userId) {
      alert('Selecione uma proposta primeiro.');
      return;
    }

    setIsRecalculating(true);
    setRecalcResult(null);

    try {
      console.log('🔧 [MANUAL_RECALC] Iniciando recálculo manual para proposta:', selectedProposal.id);
      
      const response = await fetch('/api/proposals/recalculate-totals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          proposalId: selectedProposal.id,
          userId: userId
        }),
      });

      if (!response.ok) {
        throw new Error(`Erro na API: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ [MANUAL_RECALC] Resultado:', result);

      if (result.success) {
        setRecalcResult(result.message);
        
        // Atualizar lista de propostas para mostrar novo valor
        await fetchUserProposals();
        
        // Mostrar feedback de sucesso
        setTimeout(() => {
          setRecalcResult(null);
        }, 5000); // Limpar após 5 segundos
        
      } else {
        throw new Error(result.details || 'Erro no recálculo');
      }

    } catch (error) {
      console.error('❌ [MANUAL_RECALC] Erro:', error);
      setRecalcResult(`Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      
      // Limpar erro após 5 segundos
      setTimeout(() => {
        setRecalcResult(null);
      }, 5000);
    } finally {
      setIsRecalculating(false);
    }
  };

  // 🆕 Função para formatação de tempo relativo (estilo redes sociais)
  const formatRelativeTime = (date: Date | string | null | undefined): string => {
    if (!date) return 'agora';
    
    try {
      const now = new Date();
      let targetDate: Date;
      
      // Converter para Date de forma mais robusta
      if (typeof date === 'string') {
        targetDate = new Date(date);
      } else if (date instanceof Date) {
        targetDate = date;
      } else {
        return 'agora';
      }
      
      // Verificar se a data é válida
      if (isNaN(targetDate.getTime())) {
        console.warn('⚠️ [FORMAT_TIME] Data inválida:', date);
        return 'agora';
      }
      
      // Calcular diferença em milissegundos
      const diffMs = now.getTime() - targetDate.getTime();
      
      // Se a diferença for negativa (data no futuro), retornar 'agora'
      if (diffMs < 0) return 'agora';
      
      // Converter para diferentes unidades
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffWeeks = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 7));
      const diffMonths = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 30));
      const diffYears = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 365));
      
      // Verificar se algum cálculo resultou em NaN
      if (isNaN(diffMinutes) || isNaN(diffHours) || isNaN(diffDays) || 
          isNaN(diffWeeks) || isNaN(diffMonths) || isNaN(diffYears)) {
        return 'agora';
      }
      
      // Retornar formato adequado
      if (diffMinutes < 1) return 'agora';
      if (diffMinutes < 60) return `${diffMinutes}min`;
      if (diffHours < 24) return `${diffHours}h`;
      if (diffDays < 7) return `${diffDays}d`;
      if (diffWeeks < 4) return `${diffWeeks}sem`;
      if (diffMonths < 12) return `${diffMonths}mes`;
      return `${diffYears}ano${diffYears > 1 ? 's' : ''}`;
    } catch (error) {
      return 'agora';
    }
  };

  // 🎯 Nova função para determinar o valor a ser exibido
  const getDisplayValue = (budget: any): number => {
    if (!budget.counterProposals || budget.counterProposals.length === 0) {
      return budget.amount || 0;
    }

    // Verificar se existe contraproposta aceita
    const acceptedProposal = budget.counterProposals.find((cp: any) => cp.status === 'accepted');
    if (acceptedProposal) {
      return acceptedProposal.proposedAmount || 0;
    }

    // Verificar se existe contraproposta pendente (mais recente)
    const pendingProposals = budget.counterProposals
      .filter((cp: any) => cp.status === 'pending')
      .sort((a: any, b: any) => new Date(b.proposedAt).getTime() - new Date(a.proposedAt).getTime());
    
    if (pendingProposals.length > 0) {
      return pendingProposals[0].proposedAmount || 0;
    }

    // Se não há aceitas nem pendentes, volta para o valor original
    return budget.amount || 0;
  };

  // 🎨 Nova função para determinar a cor baseada no status
  const getDisplayColor = (budget: any): string => {
    if (!budget.counterProposals || budget.counterProposals.length === 0) {
      return 'text-muted-foreground'; // Cinza - valor original
    }

    // Verificar se existe contraproposta aceita
    const acceptedProposal = budget.counterProposals.find((cp: any) => cp.status === 'accepted');
    if (acceptedProposal) {
      return 'text-green-600 dark:text-green-400'; // Verde - valor aceito
    }

    // Verificar se existe contraproposta pendente
    const pendingProposals = budget.counterProposals.filter((cp: any) => cp.status === 'pending');
    if (pendingProposals.length > 0) {
      return 'text-yellow-600 dark:text-yellow-400'; // Amarelo - em negociação
    }

    // Se só há rejeitadas, volta para o valor original
    return 'text-muted-foreground'; // Cinza - valor original
  };

  // 🏷️ Nova função para obter o rótulo do status
  const getDisplayLabel = (budget: any): string => {
    if (!budget.counterProposals || budget.counterProposals.length === 0) {
      return t('panels.relationship.initial_value');
    }

    const acceptedProposal = budget.counterProposals.find((cp: any) => cp.status === 'accepted');
    if (acceptedProposal) {
      return t('panels.relationship.accepted_value');
    }

    const pendingProposals = budget.counterProposals.filter((cp: any) => cp.status === 'pending');
    if (pendingProposals.length > 0) {
      return t('panels.relationship.in_negotiation');
    }

    return t('panels.relationship.initial_value');
  };

  // 🆕 Função para calcular o total de valores aprovados
  const calculateApprovedTotal = (): string => {
    if (!existingBudgets || Object.keys(existingBudgets).length === 0) {
      return t('panels.relationship.approved_total', { total: 'R$ 0' });
    }

    const totalApproved = Object.values(existingBudgets).reduce((total: number, budget: any) => {
      // Verificar se o orçamento está aprovado ou tem contraproposta aceita
      const isApproved = budget.status === 'approved';
      const hasAcceptedCounterProposal = budget.counterProposals?.some((cp: any) => cp.status === 'accepted');
      
      if (isApproved || hasAcceptedCounterProposal) {
        // Usar o valor correto (contraproposta aceita ou valor original)
        const finalValue = getDisplayValue(budget);
        return total + (finalValue || 0);
      }
      
      return total;
    }, 0);

    return t('panels.relationship.approved_total', { total: `R$ ${totalApproved.toLocaleString('pt-BR')}` });
  };

  // 🆕 Função para aceitar uma contraproposta específica
  const handleAcceptCounterProposal = async (counterProposal: any, budget: any) => {
    if (!selectedInfluencer || !userId) {
      alert('Dados necessários não disponíveis');
      return;
    }

    try {
      console.log('✅ [ACCEPT_COUNTER] Aceitando contraproposta:', {
        counterProposalId: counterProposal.id,
        proposedAmount: counterProposal.proposedAmount,
        budgetId: budget.id
      });

      // 🔥 CORREÇÃO: Atualizar estado local imediatamente para feedback visual
      const serviceKey = Object.keys(existingBudgets).find(key => existingBudgets[key].id === budget.id);
      if (serviceKey) {
        setExistingBudgets(prev => ({
          ...prev,
          [serviceKey]: {
            ...prev[serviceKey],
            amount: counterProposal.proposedAmount, // Atualizar valor do orçamento
            counterProposals: prev[serviceKey].counterProposals?.map((cp: any) =>
              cp.id === counterProposal.id
                ? { ...cp, status: 'accepted', reviewedAt: new Date(), reviewedBy: userId }
                : cp
            ) || []
          }
        }));
      }

      // 🆕 Nova estrutura: atualizar o status da contraproposta no array
      const response = await fetch('/api/budgets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          influencerId: selectedInfluencer.id,
          budgetId: budget.id,
          proposalId: selectedProposal?.id,
          action: 'accept_counter_proposal',
          counterProposalId: counterProposal.id,
          userId: userId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao aceitar contraproposta');
      }

      // 🔥 GRAPHQL CACHE: Invalidar cache e refetch dados atualizados
      try {
        await apolloClient.refetchQueries({
          include: [GET_INFLUENCER_BUDGETS_IN_PROPOSAL, GET_INDEPENDENT_BUDGETS, GET_COLLABORATOR_COUNTER_PROPOSALS]
        });

        // Recarregar orçamentos para sincronizar com o servidor
        if (selectedProposal) {
          await fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
        } else {
          await fetchExistingBudgets(selectedInfluencer.id);
        }
      } catch (refetchError) {
        console.warn('⚠️ [CACHE_REFRESH] Erro ao atualizar cache:', refetchError);
      }

      console.log('✅ [ACCEPT_COUNTER] Contraproposta aceita com sucesso');

    } catch (error) {
      console.error('❌ [ACCEPT_COUNTER] Erro ao aceitar contraproposta:', error);
      toast.error(`Erro ao aceitar contraproposta: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };

  // 🆕 Função para recusar uma contraproposta específica
  const handleRejectCounterProposal = async (counterProposal: any, budget: any) => {
    if (!selectedInfluencer || !userId) {
      alert('Dados necessários não disponíveis');
      return;
    }

    try {
      console.log('❌ [REJECT_COUNTER] Recusando contraproposta:', {
        counterProposalId: counterProposal.id,
        proposedAmount: counterProposal.proposedAmount,
        budgetId: budget.id
      });

      // 🔥 CORREÇÃO: Atualizar estado local imediatamente para feedback visual
      const serviceKey = Object.keys(existingBudgets).find(key => existingBudgets[key].id === budget.id);
      if (serviceKey) {
        setExistingBudgets(prev => ({
          ...prev,
          [serviceKey]: {
            ...prev[serviceKey],
            counterProposals: prev[serviceKey].counterProposals?.map((cp: any) =>
              cp.id === counterProposal.id
                ? { ...cp, status: 'rejected', reviewedAt: new Date(), reviewedBy: userId }
                : cp
            ) || []
          }
        }));
      }

      // 🆕 Nova estrutura: atualizar o status da contraproposta no array
      const response = await fetch('/api/budgets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          influencerId: selectedInfluencer.id,
          budgetId: budget.id,
          proposalId: selectedProposal?.id,
          action: 'reject_counter_proposal',
          counterProposalId: counterProposal.id,
          userId: userId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao recusar contraproposta');
      }

      // 🔥 GRAPHQL CACHE: Invalidar cache e refetch dados atualizados
      try {
        await apolloClient.refetchQueries({
          include: [GET_INFLUENCER_BUDGETS_IN_PROPOSAL, GET_INDEPENDENT_BUDGETS, GET_COLLABORATOR_COUNTER_PROPOSALS]
        });

        // Recarregar orçamentos para sincronizar com o servidor
        if (selectedProposal) {
          await fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
        } else {
          await fetchExistingBudgets(selectedInfluencer.id);
        }
      } catch (refetchError) {
        console.warn('⚠️ [CACHE_REFRESH] Erro ao atualizar cache:', refetchError);
      }

      console.log('✅ [REJECT_COUNTER] Contraproposta recusada com sucesso');

    } catch (error) {
      console.error('❌ [REJECT_COUNTER] Erro ao recusar contraproposta:', error);
      toast.error(`Erro ao recusar contraproposta: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };



  

  // 🆕 Função para buscar o status do influencer na proposta
  const fetchInfluencerStatus = async (proposalId: string, influencerId: string) => {
    if (!proposalId || !influencerId) {
      setInfluencerStatus(null);
      return;
    }

    try {
      // Logs removidos para limpar console

      // Usar API para buscar o status do influencer na proposta
      const response = await fetch(`/api/proposals/${proposalId}/influencers/${influencerId}/status`);
      
      if (response.ok) {
        const data = await response.json();
        const status = data.status || 'pendente';
        setInfluencerStatus(status);
        
        // Log removido para limpar console
      } else {
        // Log removido para limpar console
        setInfluencerStatus(null);
      }
    } catch (error) {
      console.error('❌ [STATUS] Erro ao buscar status do influencer:', error);
      setInfluencerStatus(null);
    }
  };

  // 🆕 Função para atualizar o status do influencer na proposta
  const updateInfluencerStatus = async (newStatus: 'pendente' | 'aceito' | 'rejeitado' | 'descartado') => {
    if (!selectedProposal || !selectedInfluencer || !userId) {
      console.error('❌ [STATUS UPDATE] Dados necessários não disponíveis');
      return;
    }

    try {
      setUpdatingStatus(true);
      
      console.log('🔄 [STATUS UPDATE] Atualizando status do influencer:', {
        proposalId: selectedProposal.id,
        influencerId: selectedInfluencer.id,
        currentStatus: influencerStatus,
        newStatus
      });

      const response = await fetch(`/api/proposals/${selectedProposal.id}/influencers/${selectedInfluencer.id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        })
      });

      if (response.ok) {
        const result = await response.json();
        setInfluencerStatus(newStatus);
        setIsEditingStatus(false);
        
        console.log('✅ [STATUS UPDATE] Status atualizado com sucesso:', result);
        
        // 🆕 INVALIDAR CACHE GRAPHQL: Força atualização do status no Grid
        try {
          await apolloClient.refetchQueries({
            include: ['GetProposalInfluencersStatus']
          });
          console.log('🔄 [STATUS UPDATE] Cache GraphQL invalidado com sucesso');
        } catch (cacheError) {
          console.warn('⚠️ [STATUS UPDATE] Erro ao invalidar cache GraphQL:', cacheError);
        }
        
        // Mostrar notificação de sucesso
        const statusLabels = {
          'pendente': 'Pendente',
          'aceito': 'Aprovado', 
          'rejeitado': 'Recusado',
          'descartado': 'Descartado'
        };
        
        // Aqui você pode adicionar uma notificação toast se tiver implementada
        console.log(`Status alterado para: ${statusLabels[newStatus]}`);
      } else {
        const errorData = await response.json();
        console.error('❌ [STATUS UPDATE] Erro na resposta da API:', errorData);
        throw new Error(errorData.error || 'Erro ao atualizar status');
      }
    } catch (error) {
      console.error('❌ [STATUS UPDATE] Erro ao atualizar status:', error);
      // Aqui você pode adicionar uma notificação de erro
      alert('Erro ao atualizar status do influencer');
    } finally {
      setUpdatingStatus(false);
    }
  };

  // 🆕 Função para aceitar orçamento (considerando contrapropostas)
  const handleAcceptBudget = async (budget: any, serviceKey: string) => {
    if (!selectedInfluencer || !budget?.id || !userId) {
      alert('Dados do orçamento não encontrados ou usuário não autenticado');
      return;
    }

    try {
      console.log('✅ [ACCEPT] Aceitando valor inicial do orçamento:', { budgetId: budget.id, serviceKey, userId });

      // 🔥 CORREÇÃO: Atualizar estado local imediatamente para feedback visual
      setExistingBudgets(prev => ({
        ...prev,
        [serviceKey]: {
          ...prev[serviceKey],
          counterProposals: [
            ...(prev[serviceKey].counterProposals || []),
            {
              id: `temp-${Date.now()}`, // ID temporário
              proposedAmount: budget.amount,
              originalAmount: budget.amount,
              status: 'accepted',
              proposedAt: new Date(),
              proposedBy: userId,
              proposedByUser: {
                userName: 'Você',
                imageUrl: ''
              },
              notes: 'Valor inicial aceito',
              type: 'initial_acceptance',
              quantity: serviceQuantities[serviceKey] || 1
            }
          ]
        }
      }));

      // 🆕 NOVA ARQUITETURA: Criar entrada no array counterProposals para aceitar valor inicial
      const response = await fetch('/api/budgets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          influencerId: selectedInfluencer.id,
          budgetId: budget.id,
          proposalId: selectedProposal?.id,
          action: 'accept_initial_value', // ação específica para aceitar valor inicial
          userId: userId,
          quantity: serviceQuantities[serviceKey] || 1
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao aceitar orçamento');
      }

      const result = await response.json();
      console.log('✅ Valor inicial aceito:', result);

      // 🔥 GRAPHQL CACHE: Invalidar cache e refetch dados atualizados
      try {
        await apolloClient.refetchQueries({
          include: [GET_INFLUENCER_BUDGETS_IN_PROPOSAL, GET_INDEPENDENT_BUDGETS, GET_COLLABORATOR_COUNTER_PROPOSALS]
        });

        // Recarregar orçamentos para sincronizar com o servidor
        if (selectedProposal) {
          await fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
        } else {
          await fetchExistingBudgets(selectedInfluencer.id);
        }
      } catch (refetchError) {
        console.warn('⚠️ [CACHE_REFRESH] Erro ao atualizar cache:', refetchError);
      }

      toast.success(`Valor inicial aceito com sucesso! (R$ ${budget.amount?.toLocaleString('pt-BR') || '0'})`);

    } catch (error) {
      console.error('❌ Erro ao aceitar valor inicial:', error);
      toast.error(`Erro ao aceitar valor inicial: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  };

  // 🆕 Função para recusar valor inicial do orçamento
  const handleRejectBudget = async (budget: any, serviceKey: string) => {
    if (!selectedInfluencer || !budget?.id || !userId) {
      alert('Dados do orçamento não encontrados ou usuário não autenticado');
      return;
    }

    try {
      console.log('❌ [REJECT] Recusando valor inicial do orçamento:', { budgetId: budget.id, serviceKey, userId });

      // 🔥 CORREÇÃO: Atualizar estado local imediatamente para feedback visual
      setExistingBudgets(prev => ({
        ...prev,
        [serviceKey]: {
          ...prev[serviceKey],
          counterProposals: [
            ...(prev[serviceKey].counterProposals || []),
            {
              id: `temp-${Date.now()}`, // ID temporário
              proposedAmount: budget.amount,
              originalAmount: budget.amount,
              status: 'rejected',
              proposedAt: new Date(),
              proposedBy: userId,
              proposedByUser: {
                userName: 'Você',
                imageUrl: ''
              },
              notes: 'Valor inicial recusado',
              type: 'initial_rejection',
              quantity: serviceQuantities[serviceKey] || 1
            }
          ]
        }
      }));

      // 🆕 NOVA ARQUITETURA: Criar entrada no array counterProposals para rejeitar valor inicial
      const response = await fetch('/api/budgets', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          influencerId: selectedInfluencer.id,
          budgetId: budget.id,
          proposalId: selectedProposal?.id,
          action: 'reject_initial_value', // ação específica para rejeitar valor inicial
          userId: userId,
          quantity: serviceQuantities[serviceKey] || 1
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao recusar orçamento');
      }

      const result = await response.json();
      console.log('✅ Valor inicial recusado:', result);

      // 🔥 GRAPHQL CACHE: Invalidar cache e refetch dados atualizados
      try {
        await apolloClient.refetchQueries({
          include: [GET_INFLUENCER_BUDGETS_IN_PROPOSAL, GET_INDEPENDENT_BUDGETS, GET_COLLABORATOR_COUNTER_PROPOSALS]
        });

        // Recarregar orçamentos para sincronizar com o servidor
        if (selectedProposal) {
          await fetchExistingBudgets(selectedInfluencer.id, selectedProposal.brandId);
        } else {
          await fetchExistingBudgets(selectedInfluencer.id);
        }
      } catch (refetchError) {
        console.warn('⚠️ [CACHE_REFRESH] Erro ao atualizar cache:', refetchError);
      }

      toast.success(`Valor inicial recusado com sucesso!`, {
        description: `R$ ${budget.amount?.toLocaleString('pt-BR') || '0'}`,
        duration: 4000,
      });

    } catch (error) {
      console.error('❌ Erro ao recusar valor inicial:', error);
      toast.error('Erro ao recusar valor inicial', {
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        duration: 5000,
      });
    }
  };

  // 🆕 Função para renderizar o status do influencer com ícones e cores
  const renderInfluencerStatus = (status: string | null) => {
    if (!status) return null;

    const getStatusConfig = (status: string) => {
      switch (status) {
        case 'aceito':
          return {
            icon: CheckCircle,
            color: 'text-green-600 dark:text-green-400',
            bgColor: 'bg-green-100 dark:bg-green-900/30',
            borderColor: 'border-green-200 dark:border-green-800',
            label: 'Aprovado'
          };
        case 'rejeitado':
          return {
            icon: X,
            color: 'text-red-600 dark:text-red-400', 
            bgColor: 'bg-red-100 dark:bg-red-900/30',
            borderColor: 'border-red-200 dark:border-red-800',
            label: 'Recusado'
          };
        case 'descartado':
          return {
            icon: Trash2,
            color: 'text-gray-600 dark:text-gray-400',
            bgColor: 'bg-gray-100 dark:bg-gray-900/30', 
            borderColor: 'border-gray-200 dark:border-gray-800',
            label: 'Descartado'
          };
        default: // 'pendente'
          return {
            icon: Clock,
            color: 'text-yellow-600 dark:text-yellow-400',
            bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
            borderColor: 'border-yellow-200 dark:border-yellow-800',
            label: 'Pendente'
          };
      }
    };

    const config = getStatusConfig(status);
    const IconComponent = config.icon;

    return (
      <div className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-md text-xs font-medium border ${config.bgColor} ${config.borderColor} ${config.color}`}>
        <IconComponent className="h-3 w-3" />
        <span>{config.label}</span>
      </div>
    );
  };

  // 🔒 LOADING: Mostrar loader enquanto verifica autenticação
  if (!isLoaded) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Loader isLoading={true} showLogo={true} />
      </div>
    );
  }

  // 🔒 REDIRECT: Se não autenticado, não renderizar nada (redirecionamento já foi feito)
  if (!isSignedIn) {
    return null;
  }

  // 🆕 FUNÇÃO PARA BUSCAR DADOS DOS USUÁRIOS
  const fetchUserData = async (userId: string): Promise<{ userName: string; imageUrl: string }> => {
    try {
      const response = await fetch(`/api/auth/user-data?userId=${userId}`);
      if (response.ok) {
        const userData = await response.json();
        return {
          userName: userData.firstName && userData.lastName 
            ? `${userData.firstName} ${userData.lastName}`
            : userData.firstName || userData.email || `Usuário ${userId.substring(0, 8)}`,
          imageUrl: userData.imageUrl || ''
        };
      }
    } catch (error) {
      console.error('❌ Erro ao buscar dados do usuário:', error);
    }
    
    return {
      userName: `Usuário ${userId.substring(0, 8)}`,
      imageUrl: ''
    };
  };

  // 🆕 FUNÇÃO PARA ENRIQUECER CONTRAPROPOSTAS COM DADOS DOS USUÁRIOS
  const enrichCounterProposalsWithUserData = async (counterProposals: any[]): Promise<any[]> => {
    if (!counterProposals || counterProposals.length === 0) return [];
    
    const enrichedProposals = await Promise.all(
      counterProposals.map(async (cp: any) => {
        let userData = {
          userName: 'Usuário',
          role: 'member',
          imageUrl: ''
        };

        const userIdToFetch = cp.proposedBy;
        
        if (userIdToFetch) {
          if (userIdToFetch === userId) {
            // Usar dados do usuário atual do Clerk
            userData = {
              userName: clerkUser?.firstName && clerkUser?.lastName 
                ? `${clerkUser.firstName} ${clerkUser.lastName}`
                : clerkUser?.firstName || 'Você',
              role: 'member',
              imageUrl: clerkUser?.imageUrl || ''
            };
          } else {
            // Buscar dados do usuário remoto
            const fetchedUserData = await fetchUserData(userIdToFetch);
            userData = {
              userName: fetchedUserData.userName,
              role: 'member',
              imageUrl: fetchedUserData.imageUrl
            };
          }
        }

        return {
          ...cp,
          proposedByUser: userData,
          proposedAt: cp.proposedAt ? new Date(cp.proposedAt) : new Date()
        };
      })
    );

    return enrichedProposals;
  };



  // Funções para gerenciar tooltip customizado
  const showCustomTooltip = (event: React.MouseEvent, content: string) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({ 
      x: rect.left + rect.width / 2, 
      y: rect.top 
    });
    setTooltipContent(content);
    setTooltipVisible(true);
  };

  const hideCustomTooltip = () => {
    setTooltipVisible(false);
  };



  return (
    <Suspense fallback={<div className="h-screen bg-background animate-pulse" />}>
      <ProfilePanelTourProvider>
        <TooltipProvider delayDuration={200}>


                    {/* Loader Global */}
                    <Loader isLoading={isLoading} showLogo={true} />
      
      <div className="h-screen overflow-hidden flex flex-col lg:flex-row" data-tour="responsive-layout">
        {/* Seção de boas-vindas com informações do usuário */}
      {/* 🔥 ANIMAÇÕES: Framer Motion */}
      <AnimatePresence>
        {showWelcomeBadge && (
          <motion.div
            initial={{ 
              opacity: 0, 
              scale: 0.3, 
              y: -50,
              x: 20,
              rotate: -10
            }}
            animate={{ 
              opacity: 1, 
              scale: 1, 
              y: 0,
              x: 0,
              rotate: 0
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.3, 
              y: -30,
              x: 20,
              rotate: 5
            }}
            transition={{ 
              duration: 0.6, 
              ease: [0.25, 0.46, 0.45, 0.94],
              scale: { 
                type: "spring", 
                stiffness: 400, 
                damping: 30,
                mass: 0.8
              },
              rotate: {
                type: "spring",
                stiffness: 300,
                damping: 20
              }
            }}
            onAnimationStart={() => {
              // Efeito sonoro de popup/tap
              try {
                const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
                
                // Criar oscilador para som de tap
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                const filterNode = audioContext.createBiquadFilter();
                
                // Configurar filtro para som mais suave
                filterNode.type = 'lowpass';
                filterNode.frequency.setValueAtTime(3000, audioContext.currentTime);
                filterNode.Q.setValueAtTime(1, audioContext.currentTime);
                
                // Conectar cadeia de áudio
                oscillator.connect(filterNode);
                filterNode.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                // Frequência aguda para som de tap (C7)
                oscillator.frequency.setValueAtTime(2093, audioContext.currentTime);
                oscillator.type = 'sine';
                
                // Envelope rápido e percussivo
                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.005);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.08);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.08);
                
              } catch (error) {
                // Silenciosamente ignora erros de áudio
                console.debug('Áudio não disponível:', error);
              }
            }}
            className="absolute top-4 right-4 z-50 bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-gray-900 border border-purple-500/30 backdrop-blur-xl backdrop-saturate-200 rounded-2xl px-3 py-2 md:px-5 md:py-4 shadow-2xl shadow-purple-500/25 dark:shadow-purple-400/35 ring-1 ring-purple-500/20"
            data-tour="welcome-message"
          >
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8 border-2 border-purple-500">
                <AvatarImage 
                  src={'/placeholder.svg'} 
                  alt={clerkUser?.firstName || 'Usuário'}
                />
                <AvatarFallback className="bg-gradient-to-r from-purple-700 to-pink-700 text-white text-xs">
                  {(clerkUser?.firstName || 'U').charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="text-xs font-medium text-gray-900 dark:text-white">
                  {t('influencers.welcome_message', { name: clerkUser?.firstName || t('common.user') })}
                </span>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {clerkUser?.emailAddresses?.[0]?.emailAddress}
                </span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>



      {/* Menu lateral de filtros - TEMPORARIAMENTE DESATIVADO */}
      {/* 
      <div data-tour="sidebar-filters">
        <SidebarMenu
            onLocationFilter={handleLocationFilter}
            onFollowersRangeChange={handleFollowersRangeChange}
            onRatingChange={handleRatingChange}
            onVerifiedOnlyChange={handleVerifiedOnlyChange}
            onAvailableOnlyChange={handleAvailableOnlyChange}
            onResetFilters={handleResetFilters}
            selectedLocation={selectedLocation}
            minFollowers={minFollowers}
            maxFollowers={maxFollowers}
            minRating={minRating}
            verifiedOnly={verifiedOnly}
            availableOnly={availableOnly}
            popularLocations={popularLocations}
            userId={userId || undefined}
          />
      </div>
      */}
        
      {/* 🔥 OTIMIZAÇÃO: Grade de influenciadores com Suspense */}
      <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto min-h-0" data-tour="influencers-grid">
        <Suspense fallback={<InfluencerGridSkeleton />}>
          <InfluencerGridComponent
            onRefresh={refreshKey}
            selectedLocation={selectedLocation}
            minFollowers={minFollowers}
            maxFollowers={maxFollowers}
            minRating={minRating}
            verifiedOnly={verifiedOnly}
            availableOnly={availableOnly}
            onSelectInfluencer={handleSelectInfluencer}
            selectedBrands={selectedBrands}
            selectedProposal={selectedProposal}
            selectedInfluencerId={searchParams?.get('influencer')}
            selectedInfluencer={selectedInfluencer}
            // 🔗 NOVO: Props para lista compartilhada
            isSharedList={isSharedList}
            sharedInfluencerIds={sharedListData?.influencerIds || []}
          />
        </Suspense>
      </div>
      
      {/* Container dos painéis laterais */}
      <div className="relative flex flex-row">
       {/* Botão de controle unificado */}
       <div className="absolute -left-4 top-4 z-20 hidden lg:block">
            <button
              onClick={togglePanels}
              className="flex items-center justify-center w-4 h-8 bg-white/20 dark:bg-black/20 backdrop-blur-sm rounded-l border border-r-0 border-border hover:bg-white/30 dark:hover:bg-black/30 transition-colors"
            >
              {showRelationshipPanel ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </button>
          </div>

          {/* Painel de Relacionamento */}
          <div className={`relative transition-all duration-300 ${showRelationshipPanel ? 'w-60' : 'w-0'}`} data-tour="relationship-panel">
            <div className={`h-full backdrop-blur-sm bg-white/20 dark:bg-[#080210] border-l border-border dark:border-[#1c1627] transition-all duration-300 ${showRelationshipPanel ? 'opacity-100' : 'opacity-0'}`}>
              <div className="p-4 space-y-4 h-screen overflow-auto scrollbar-hide">

                {/* Cabeçalho de Relacionamento - Seletor de Propostas */}
                <div className="space-y-2">
                  <div 
                    className="flex justify-between items-center bg-[#5600ce] text-white border pb-2 cursor-pointer hover:bg-[#ff0074] rounded-md p-2 transition-colors"
                    onClick={toggleProposalSelector}
                    data-tour="proposal-selector"
                  >
                  
                    <div className="flex items-center gap-1">
                      {selectedProposal ? (
                        <div className="flex items-center gap-2">
                          {/* Logo da marca selecionada */}
                          {(() => {
                            let selectedBrandData = selectedProposal.brandId ? getBrandById(selectedProposal.brandId) : null;
                            
                            // 🔧 AUTO-FIX: Se brandId for userId, usar primeira marca
                            if (selectedProposal.brandId && !selectedBrandData && selectedProposal.brandId === userId && brands.length > 0) {
                              selectedBrandData = brands[0];
                            }
                            
                            return selectedBrandData?.logo && (
                              <img 
                                src={selectedBrandData.logo} 
                                alt={selectedBrandData.name} 
                                className="w-4 h-4 rounded object-cover"
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            );
                          })()}
                          <span className="text-xs sm:text-sm text-white">
                            {selectedProposal.name}
                            {(() => {
                              let selectedBrandData = selectedProposal.brandId ? getBrandById(selectedProposal.brandId) : null;
                              
                              // 🔧 AUTO-FIX: Se brandId for userId, usar primeira marca
                              if (selectedProposal.brandId && !selectedBrandData && selectedProposal.brandId === userId && brands.length > 0) {
                                selectedBrandData = brands[0];
                              }
                              
                              return selectedBrandData?.name && (
                                <span className="text-xs opacity-80 ml-1 flex items-center gap-1">
                                  • {selectedBrandData.name}
                                  {/* 🔧 Indicador de correção automática */}
                                  {selectedProposal.brandId === userId && (
                                    <span className="text-orange-300" title="Dados corrigidos automaticamente">🔧</span>
                                  )}
                                </span>
                              );
                            })()}
                          </span>
                        </div>
                      ) : (
                        <span className="text-xs sm:text-sm text-white">{t('panels.relationship.view_all_proposals')}</span>
                      )}
                      
                      
                   
                      
                      {/* 🔥 NOVO: Indicador de que a proposta está salva na URL */}
                      {selectedProposal && searchParams && searchParams.get('proposta') === selectedProposal.id && (
                        <div className="w-2 h-2 bg-green-400 rounded-full ml-1" title="Proposta salva na URL" />
                      )}
                      <ChevronDown className={`h-4 w-4 text-white transition-transform ${showProposalSelector ? 'rotate-180' : ''}`} />
                    </div>
                    
                 
                  </div>
                  
                  {/* 🔥 NOVO: Feedback do recálculo */}
                  {recalcResult && (
                    <div className={`mt-2 px-2 py-1 rounded text-xs ${
                      recalcResult.includes('Erro') 
                        ? 'bg-red-100 text-red-700 border border-red-200' 
                        : 'bg-green-100 text-green-700 border border-green-200'
                    }`}>
                      {recalcResult}
                    </div>
                  )}

                                      {/* Dropdown de Seleção de Propostas */}
                  {showProposalSelector && (
                    <div className="bg-background border rounded-md shadow-lg max-h-96 overflow-y-auto">
                      {userProposals && Array.isArray(userProposals) && userProposals.length > 0 ? (
                        <>
                          {/* Opção para deselecionar */}
                          <div
                            className="px-3 py-2 hover:bg-muted/50 cursor-pointer transition-colors border-b"
                            onClick={() => handleSelectProposal(null)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <p className="font-medium text-xs text-muted-foreground">{t('panels.relationship.view_all_proposals')}</p>
                                <p className="text-xs text-muted-foreground">Sem filtro de proposta</p>
                              </div>
                              {!selectedProposal && (
                                <div className="w-2 h-2 rounded-full bg-[#ff0074]"></div>
                              )}
                            </div>
                          </div>
                          
                          {/* Lista de propostas */}
                          {userProposals.map((proposal) => {
                            // ✅ SIMPLIFICADO: Usuário tem acesso completo às suas propostas
                            const userRole = 'owner';
                            const permissions = { canView: true, canEdit: true, canManage: true };
                            
                            // Buscar dados da marca pelo brandId
                            let brandData = proposal.brandId ? getBrandById(proposal.brandId) : null;
                            
                            // 🔧 AUTO-FIX: Se brandId for igual ao userId e não encontrar marca, usar primeira marca disponível
                            if (proposal.brandId && !brandData && proposal.brandId === userId && brands.length > 0) {
                              console.log('🔧 [AUTO-FIX] brandId é userId, usando primeira marca:', {
                                proposalName: proposal.name,
                                originalBrandId: proposal.brandId,
                                fallbackBrand: brands[0].name,
                                fallbackBrandId: brands[0].id
                              });
                              brandData = brands[0]; // Usar primeira marca como fallback
                            }
                            
                           
                            
                            return (
                              <div
                                key={proposal.id}
                                className="px-3 py-2 hover:bg-muted/50 cursor-pointer transition-colors"
                                onClick={() => handleSelectProposal(proposal)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <p className="font-medium text-xs">{proposal.name}</p>
                                      {/* 🔥 NOVO: Indicador de snapshot */}
                                      {proposal.hasSnapshots && (
                                        <Badge 
                                          variant="outline" 
                                          className="text-xs h-5 px-1.5 text-amber-600 border-amber-600 bg-amber-50 dark:bg-amber-900/20"
                                        >
                                          📸 {proposal.snapshotsCount}
                                        </Badge>
                                      )}
                                      
                                      {/* ✅ SIMPLIFICADO: Badge do proprietário */}
                                      <Badge 
                                        variant="outline" 
                                        className="text-xs h-5 px-1.5 text-green-600 border-green-600 bg-green-50 dark:bg-green-900/20"
                                      >
                                        📋 Minha Proposta
                                      </Badge>
                                    </div>
                                    
                                    {/* 🏢 NOVA SEÇÃO: Exibir informaões da marca */}
                                    {proposal.brandId && (
                                      <div className="flex items-center gap-2 mb-1">
                                        {brandsLoading ? (
                                          <div className="flex items-center gap-1">
                                            <div className="w-3 h-3 border border-[#5600ce] border-t-transparent rounded-full animate-spin"></div>
                                            <p className="text-xs text-muted-foreground">Carregando marca...</p>
                                          </div>
                                        ) : brandData ? (
                                          <>
                                            {/* Logo da marca */}
                                            {brandData.logo && (
                                              <img 
                                                src={brandData.logo} 
                                                alt={brandData.name} 
                                                className="w-4 h-4 rounded object-cover"
                                                onError={(e) => {
                                                  e.currentTarget.style.display = 'none';
                                                }}
                                              />
                                            )}
                                            
                                            {/* Nome da marca */}
                                            <div className="flex items-center gap-1">
                                              <p className="text-xs text-black dark:text-white font-medium">
                                                {brandData.name}
                                              </p>
                                              {/* 🔧 Indicador de correção automática */}
                                              {proposal.brandId === userId && (
                                                <span 
                                                  className="text-xs text-orange-500 cursor-help" 
                                                  title="Dados corrigidos automaticamente - brandId era userId"
                                                >
                                                  🔧
                                                </span>
                                              )}
                                            </div>
                                          </>
                                        ) : (
                                          <div className="flex items-center gap-1">
                                          
                                          </div>
                                        )}
                                      </div>
                                    )}
                                    
                                    {proposal.value && (
                                      <p className="text-xs text-muted-foreground">
                                        R$ {proposal.value.toLocaleString('pt-BR')}
                                      </p>
                                    )}
                                    {/* 🔥 NOVO: Data do snapshot */}
                                    {proposal.hasSnapshots && proposal.snapshotCreatedAt && (
                                      <p className="text-xs text-amber-600 dark:text-amber-400">
                                        Dados de {new Date(proposal.snapshotCreatedAt).toLocaleDateString('pt-BR')}
                                      </p>
                                    )}
                                  </div>
                                  {selectedProposal?.id === proposal.id && (
                                    <div className="w-2 h-2 rounded-full bg-[#ff0074]"></div>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </>
                      ) : (
                        <div className="p-3 text-center">
                          <p className="text-sm text-muted-foreground">{t('proposals.no_proposals_found', 'Nenhuma proposta encontrada')}</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {t('proposals.create_to_manage', 'Crie propostas para gerenciar relacionamentos')}
                          </p>
                        </div>
                      )}
                     
                    </div>
                  )}
                </div>
                
                {/* Conteúdo baseado na proposta selecionada */}
                {selectedProposal ? (
                  <>
                   

                   

                 

                   
                  </>
                ) : null}
                
                {/* Perfis */}
                {/*
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">Perfis</p>
                    <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">Editar</Button>
                  </div>
                  
                  {selectedInfluencer?.socialNetworks?.instagram?.username && (
                    <div className="flex items-center space-x-2 p-2 rounded-md border">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full text-black dark:text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.9 3.9 0 0 0-1.417.923A3.9 3.9 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.9 3.9 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.9 3.9 0 0 0-.923-1.417A3.9 3.9 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599s.453.546.598.92c.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.5 2.5 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.5 2.5 0 0 1-.92-.598 2.5 2.5 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233s.008-2.388.046-3.231c.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92s.546-.453.92-.598c.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92m-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217m0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334"/>
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium flex items-center">
                          <a 
                            href={`https://instagram.com/${selectedInfluencer.socialNetworks.instagram.username}`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:underline hover:text-blue-600 transition-colors"
                          >
                            @{selectedInfluencer.socialNetworks.instagram.username}
                          </a>
                          {selectedInfluencer.verified && (
                            <svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.61 189.98" width="16" height="16" className="ml-1">
                              <defs>
                                <linearGradient id="Gradiente_sem_nome_147" data-name="Gradiente sem nome 147" x1="52.12" y1="184.26" x2="115.13" y2="9.61" gradientUnits="userSpaceOnUse">
                                  <stop offset="0" stopColor="#ff0074"/>
                                  <stop offset="1" stopColor="#ff0074"/>
                                </linearGradient>
                              </defs>
                              <g id="Camada_1-2" data-name="Camada 1">
                                <path className="cls-1" fill="url(#Gradiente_sem_nome_147)" d="m155.35,97.66h0c-.82-4.58-2.05-9.01-3.63-13.28-5.77-15.52-16.32-28.72-29.87-37.79,1.72,4.96,2.66,10.29,2.66,15.84,0,4.5-.61,8.86-1.77,12.99-12.01-8.56-19.95-22.48-20.31-38.27-.01-.39-.02-.78-.02-1.16,0-4.5.61-8.86,1.77-12.99,1.02-3.68,2.46-7.18,4.28-10.44,2.62-4.73,6.01-8.97,10-12.56-9.86.78-19.21,3.38-27.71,7.47-3.97,1.91-7.75,4.15-11.32,6.68-8.21,5.82-15.25,13.19-20.7,21.68-7.82,12.18-12.35,26.68-12.35,42.22,0,3.88.28,7.68.83,11.41.71,4.87,1.87,9.59,3.43,14.12-3.55-2.2-6.8-4.85-9.67-7.87-8.22-8.67-13.26-20.39-13.26-33.29,0-4.04.5-7.96,1.43-11.7C14.54,62.52,4.26,79.45,1.06,98.78c-.7,4.2-1.06,8.51-1.06,12.9,0,43.25,35.06,78.3,78.3,78.3,27.69,0,52.03-14.38,65.95-36.08,7.82-12.19,12.35-26.68,12.35-42.23,0-4.78-.43-9.47-1.25-14.02Zm-77.15,77.38c-17.82,0-33.39-9.63-41.79-23.98,12.07,7.61,26.37,12.01,41.69,12.01,12.16,0,23.68-2.77,33.94-7.72,2.79-1.35,5.48-2.85,8.07-4.49-1.03,1.78-2.17,3.48-3.41,5.11-8.84,11.59-22.79,19.07-38.5,19.07Z"/>
                              </g>
                            </svg>
                          )}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {selectedInfluencer.socialNetworks.instagram.followers.toLocaleString()} seguidores
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {selectedInfluencer?.socialNetworks?.youtube?.username && (
                    <div className="flex items-center space-x-2 p-2 rounded-md border">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full text-black dark:text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium flex items-center">
                          <a 
                            href={`https://youtube.com/@${selectedInfluencer.socialNetworks.youtube.username}`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:underline hover:text-blue-600 transition-colors"
                          >
                            @{selectedInfluencer.socialNetworks.youtube.username}
                          </a>
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {selectedInfluencer.socialNetworks.youtube.followers.toLocaleString()} inscritos
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {selectedInfluencer?.socialNetworks?.tiktok?.username && (
                    <div className="flex items-center space-x-2 p-2 rounded-md border">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full text-black dark:text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 448 512">
                          <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium flex items-center">
                          <a 
                            href={`https://tiktok.com/@${selectedInfluencer.socialNetworks.tiktok.username}`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:underline hover:text-blue-600 transition-colors"
                          >
                            @{selectedInfluencer.socialNetworks.tiktok.username}
                          </a>
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {selectedInfluencer.socialNetworks.tiktok.followers.toLocaleString()} seguidores
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {selectedInfluencer?.socialNetworks?.others?.map((network: OtherSocialNetwork, index: number) => {
                    let SocialIcon = ExternalLink;
                    let socialUrl = '#';
                    
                    let socialIcon = <ExternalLink className="h-5 w-5" />;
                    
                    if (network.platform?.toLowerCase().includes('facebook')) {
                      socialIcon = (
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                        </svg>
                      );
                      socialUrl = `https://facebook.com/${network.username}`;
                    } else if (network.platform?.toLowerCase().includes('twitch')) {
                      socialIcon = (
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M3.857 0 1 2.857v10.286h3.429V16l2.857-2.857H9.57L14.714 8V0H3.857zm9.714 7.429-2.285 2.285H9l-2 2v-2H4.429V1.143h9.142v6.286z"/>
                        </svg>
                      );
                      socialUrl = `https://twitch.tv/${network.username}`;
                    } else if (network.platform?.toLowerCase().includes('kwai')) {
                      socialUrl = `https://kwai.com/@${network.username}`;
                    }
                    
                    return (
                      <div key={index} className="flex items-center space-x-2 p-2 rounded-md border">
                        <div className="flex items-center justify-center h-6 w-6 rounded-full text-black dark:text-white">
                          {socialIcon}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium flex items-center">
                            <a 
                              href={socialUrl} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="hover:underline hover:text-blue-600 transition-colors"
                            >
                              @{network.username}
                            </a>
                            <span className="text-xs text-muted-foreground ml-1">({network.platform})</span>
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {network.followers.toLocaleString()} seguidores
                          </p>
                        </div>
                      </div>
                    );
                  })}
                  
                  {!selectedInfluencer && (
                    <div className="flex items-center justify-center p-4 border border-dashed rounded-md">
                      <p className="text-xs text-muted-foreground">Selecione um influenciador para ver seus perfis</p>
                    </div>
                  )}
                  
                  {selectedInfluencer && 
                   !selectedInfluencer.socialNetworks?.instagram?.username && 
                   !selectedInfluencer.socialNetworks?.youtube?.username && 
                   !selectedInfluencer.socialNetworks?.tiktok?.username && 
                   (!selectedInfluencer.socialNetworks?.others || selectedInfluencer.socialNetworks.others.length === 0) && (
                    <div className="flex items-center justify-center p-4 border border-dashed rounded-md">
                      <p className="text-xs text-muted-foreground">Nenhum perfil de rede social cadastrado</p>
                    </div>
                  )}
                </div>
                */}
                
                {/* Emails */}
                {/*
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">Emails</p>
                    <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">Adicionar</Button>
                  </div>
                  
                  {selectedInfluencer?.email ? (
                    <div className="flex items-center justify-between p-2 rounded-md border">
                      <p className="text-sm">{selectedInfluencer.email}</p>
                      <Copy 
                        className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-pointer" 
                        onClick={() => {
                          navigator.clipboard.writeText(selectedInfluencer.email);
                        }}
                      />
                    </div>
                  ) : selectedInfluencer ? (
                    <div className="flex items-center justify-center p-4 border border-dashed rounded-md">
                      <p className="text-xs text-muted-foreground">Nenhum email cadastrado</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center p-4 border border-dashed rounded-md">
                      <p className="text-xs text-muted-foreground">Selecione um influenciador para ver seu email</p>
                    </div>
                  )}
                </div>
                */}
                 {/* 🆕 STATUS DO INFLUENCER NA PROPOSTA */}
                        {selectedProposal && influencerStatus && (
                          <div className="bg-muted/30 dark:bg-[#080210]/60 rounded-lg p-3 border border-dashed dark:border-[#1c1627]">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <UserCircle className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm text-muted-foreground">{t('proposals.status_label')}</span>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                {isEditingStatus ? (
                                  <div className="flex items-center gap-2">
                                    <Select
                                      value={influencerStatus}
                                      onValueChange={(value: 'pendente' | 'aceito' | 'rejeitado' | 'descartado') => {
                                        updateInfluencerStatus(value);
                                      }}
                                      disabled={updatingStatus}
                                    >
                                      <SelectTrigger className="w-32 h-8 text-xs">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="pendente">
                                          <div className="flex items-center gap-2">
                                            <Clock className="h-3 w-3 text-yellow-600" />
                                            <span>Pendente</span>
                                          </div>
                                        </SelectItem>
                                        <SelectItem value="aceito">
                                          <div className="flex items-center gap-2">
                                            <CheckCircle className="h-3 w-3 text-green-600" />
                                            <span>Aprovado</span>
                                          </div>
                                        </SelectItem>
                                        <SelectItem value="rejeitado">
                                          <div className="flex items-center gap-2">
                                            <X className="h-3 w-3 text-red-600" />
                                            <span>{t('proposals.rejected_status')}</span>
                                          </div>
                                        </SelectItem>
                                        <SelectItem value="descartado">
                                          <div className="flex items-center gap-2">
                                            <Trash2 className="h-3 w-3 text-gray-600" />
                                            <span>Descartado</span>
                                          </div>
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                    
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => setIsEditingStatus(false)}
                                      disabled={updatingStatus}
                                      className="h-8 w-8 p-0"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    {renderInfluencerStatus(influencerStatus)}
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => setIsEditingStatus(true)}
                                      className="h-8 w-8 p-0 hover:bg-muted/50"
                                      title="Editar status"
                                    >
                                      <Pencil className="h-3 w-3" />
                                    </Button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
              

                {/* Orçamento de Serviços */}
                <Accordion type="single" collapsible className="w-full" defaultValue="budget-services" data-tour="budget-section">
                  <AccordionItem value="budget-services">
                    <AccordionTrigger className="py-2 text-xs font-medium">
                      <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      <span className="text-xs sm:text-sm">{t('panels.relationship.budget_section')}</span>
                        {Object.keys(existingBudgets).length > 0 && (
                          <span className="bg-[#ff0074] text-white text-xs px-2 py-0.5 rounded-full">
                            {Object.keys(existingBudgets).length}
                          </span>
                        )}

                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 pt-2">
                        {selectedInfluencer ? (
                    <div className="space-y-3 overflow-y-auto">
                      {/* 🎯 SEÇÃO DE ORÇAMENTOS - SEMPRE VISÍVEL quando há orçamentos */}
                      <div className="space-y-4">
                        {/* Insights e botão para abrir sheet de orçamentos - SEMPRE VISÍVEL */}
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                           
                            <div className="flex flex-col">
                              <span className="text-xs font-medium text-foreground" data-tour="approved-total">
                                {calculateApprovedTotal()}
                              </span>
                             
                            </div>
                          </div>
                          <Protect
                            role="org:admin"
                            
                          >
                            <Button 
                              onClick={() => setShowBudgetSheet(true)}
                              size="sm" 
                              className="bg-[#ff0074] hover:bg-[#ff0074]/90 text-white text-xs "
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              <span className="hidden sm:inline">{t('panels.relationship.new_budget')}</span>
                              <span className="sm:hidden">{t('common.new')}</span>
                            </Button>
                          </Protect>
                        </div>

                       

                        {/* 📊 LISTA DE ORÇAMENTOS EXISTENTES - ORGANIZADOS POR PLATAFORMA */}
                        {isLoadingBudgets ? (
                          <div className="flex flex-col items-center justify-center p-8 space-y-4">
                            <div className="relative">
                              <div className="w-12 h-12 border-4 border-[#ff0074]/20 border-t-[#ff0074] rounded-full animate-spin"></div>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <DollarSign className="h-6 w-6 text-[#ff0074]" />
                              </div>
                            </div>
                            <div className="text-center space-y-2">
                              <p className="text-xs font-medium text-foreground">{t('influencers.loading_budgets')}</p>
                              <p className="text-xs text-muted-foreground">
                                {t('influencers.loading_budgets_description')}
                              </p>
                            </div>
                          </div>
                        ) : Object.keys(existingBudgets).length > 0 ? (
                          <div className="space-y-4">
                            {Object.entries((() => {
                              // Agrupar orçamentos por plataforma
                              const budgetsByPlatform: { [platform: string]: Array<[string, any]> } = {};
                              
                              Object.entries(existingBudgets).forEach(([serviceKey, budget]) => {
                                const platform = serviceKey.split('_')[0] || 'outros';
                                if (!budgetsByPlatform[platform]) {
                                  budgetsByPlatform[platform] = [];
                                }
                                budgetsByPlatform[platform].push([serviceKey, budget]);
                              });

                              return budgetsByPlatform;
                            })()).map(([platform, platformBudgets]) => (
                                <div key={platform} className="space-y-2">
                                  {/* Cabeçalho da Plataforma */}
                                  <div className="flex items-center gap-2 pb-2 border-b border-border/50">
                                    <SocialIcon 
                                      platform={platform} 
                                      size={20} 
                                      className="dark:text-white text-black" 
                                    />
                                    <span className="text-sm font-semibold text-foreground">
                                      {getPlatformDisplayName(platform)}
                                    </span>
                                   
                                  </div>

                                  {/* Serviços da Plataforma */}
                                  <div className="space-y-2">
                                    {platformBudgets.map(([serviceKey, budget]) => (
                                      <div key={serviceKey} className="border border-dashed dark:border-[#1c1627] bg-white dark:bg-[#080210] rounded-lg p-4 hover:bg-muted/20 transition-colors">
                                        {/* 🎯 NOVO LAYOUT: Estrutura vertical limpa e organizada */}
                                        <div className="space-y-3">
                                          {/* Seção 1: Nome do Serviço + Detalhes */}
                                          <div className="space-y-2">
                                            {/* Nome do Serviço */}
                                            <div className="text-base font-semibold text-foreground">
                                                {(() => {
                                                  // Criar nome do serviço sem o nome da plataforma
                                                  const [, ...serviceParts] = serviceKey.split('_');
                                                  const serviceName = serviceParts.join(' ');
                                                  
                                                  // Mapear nomes específicos para melhor exibição
                                                  const serviceTypeMapping: { [key: string]: string } = {
                                                    'story': 'Stories',
                                                    'stories': 'Stories',
                                                    'reels': 'Reels',
                                                    'video': 'Vídeo',
                                                    'videos': 'Vídeos',
                                                    'post': 'Post',
                                                    'posts': 'Posts',
                                                    'shorts': 'Shorts',
                                                    'longform': 'Vídeo Longo',
                                                    'long_form': 'Vídeo Longo',
                                                    'live': 'Live',
                                                    'igtv': 'IGTV',
                                                    'feed': 'Feed',
                                                    'carrossel': 'Carrossel',
                                                    'carousel': 'Carrossel',
                                                    'dedicated': 'Dedicado',
                                                    'insertion': 'Inserção'
                                                    
                                                  };
                                                  
                                                  // Obter o nome do serviço formatado
                                                  const serviceDisplayName = serviceTypeMapping[serviceName.toLowerCase()] || 
                                                                            serviceName.charAt(0).toUpperCase() + serviceName.slice(1);
                                                  
                                                  const finalServiceName = serviceDisplayName || budget.description || budget.serviceType || 'Serviço Personalizado';
                                                  
                                                  // 🔧 CORRIGIR: Obter quantidade da contraproposta mais recente
                                                  let quantity = 1;
                                                  
                                                                                                                                                        // Se há contrapropostas, usar a quantidade da mais recente
                                                   if (budget.counterProposals && budget.counterProposals.length > 0) {
                                                     // 🔧 SOLUÇÃO DIRETA: Usar o último item do array (mais recente)
                                                     const latestProposal = budget.counterProposals[budget.counterProposals.length - 1];
                                                     
                                                     // Verificar se há contraproposta aceita
                                                     const acceptedProposal = budget.counterProposals.find(cp => cp.status === 'accepted');
                                                     
                                                     if (acceptedProposal && acceptedProposal.quantity) {
                                                       quantity = acceptedProposal.quantity;
                                                       console.log('🎯 [QUANTITY-NAME] Usando quantidade aceita:', acceptedProposal.quantity);
                                                     } else if (latestProposal && latestProposal.quantity) {
                                                       quantity = latestProposal.quantity;
                                                       console.log('🎯 [QUANTITY-NAME] Usando quantidade mais recente:', latestProposal.quantity, 'timestamp:', latestProposal.proposedAt);
                                                     }
                                                   }
                                                  
                                                  // Fallback para serviceQuantities se não há contrapropostas
                                                  if (quantity === 1 && serviceQuantities[serviceKey]) {
                                                    quantity = serviceQuantities[serviceKey];
                                                  }
                                                  
                                                  // 🔥 CORREÇÃO: Calcular valor unitário para exibir ao lado
                                                  let unitValue = budget.amount || 0;
                                                  
                                                  // Se há contrapropostas, usar o valor da mais recente
                                                  if (budget.counterProposals && budget.counterProposals.length > 0) {
                                                    const latestProposal = budget.counterProposals[budget.counterProposals.length - 1];
                                                    const acceptedProposal = budget.counterProposals.find(cp => cp.status === 'accepted');
                                                    
                                                    if (acceptedProposal) {
                                                      const proposalValue = acceptedProposal.counterAmount !== undefined 
                                                        ? acceptedProposal.counterAmount 
                                                        : acceptedProposal.proposedAmount;
                                                      if (proposalValue !== undefined) {
                                                        unitValue = proposalValue;
                                                      }
                                                    } else if (latestProposal) {
                                                      const proposalValue = latestProposal.counterAmount !== undefined 
                                                        ? latestProposal.counterAmount 
                                                        : latestProposal.proposedAmount;
                                                      if (proposalValue !== undefined) {
                                                        unitValue = proposalValue;
                                                      }
                                                    }
                                                  }
                                                  
                                                                                                     // 🎯 NOVA UI: 3 linhas claras e intuitivas
                                                   const total = unitValue * quantity;
                                                   
                                                   return (
                                                     <div className="text-left space-y-1">
                                                       {/* Linha 1: Nome do Serviço */}
                                                       <div className="font-medium text-xs text-foreground">
                                                         {finalServiceName}
                                                       </div>
                                                       
                                                       {/* Linha 2: Quantidade + Valor Unitário (clicável) */}
                                                       <div className="flex items-center gap-2">
                                                         <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded">
                                                           {quantity}x
                                                         </span>
                                                         <button 
                                                           className="text-xs font-medium text-[#5600ce] hover:text-[#5600ce]/80 hover:underline cursor-pointer"
                                                           onClick={() => {
                                                             // TODO: Abrir modal de contraproposta para valor unitário
                                                             console.log('Contraproposta unitária para:', serviceKey, unitValue);
                                                           }}
                                                           title="Clique para fazer contraproposta no valor unitário"
                                                         >
                                                           R$ {unitValue.toLocaleString('pt-BR')}/cada
                                                         </button>
                                                       </div>
                                                       

                                                     </div>
                                                   );
                                                })()}
                                            </div>
                                            
                                            {/* Seção 2: Status e Valor Total */}
                                            <div className="pt-2 border-t border-dashed border-muted/30">
                                              <div className="text-left">
                                                {(() => {
                                                // Verificar se há contrapropostas pendentes
                                                const hasPendingCounterProposals = budget.counterProposals && 
                                                  budget.counterProposals.some(cp => cp.status === 'pending');
                                                
                                                const hasAcceptedCounterProposals = budget.counterProposals && 
                                                  budget.counterProposals.some(cp => cp.status === 'accepted');
                                                
                                                // 🔥 CALCULAR VALOR TOTAL PARA EXIBIR NO STATUS
                                                let quantity = 1;
                                                let unitValue = budget.amount || 0;
                                                
                                                  if (budget.counterProposals && budget.counterProposals.length > 0) {
                                                    const latestProposal = budget.counterProposals[budget.counterProposals.length - 1];
                                                    const acceptedProposal = budget.counterProposals.find(cp => cp.status === 'accepted');
                                                    
                                                    if (acceptedProposal) {
                                                    if (acceptedProposal.quantity) quantity = acceptedProposal.quantity;
                                                      const proposalValue = acceptedProposal.counterAmount !== undefined 
                                                        ? acceptedProposal.counterAmount 
                                                        : acceptedProposal.proposedAmount;
                                                    if (proposalValue !== undefined) unitValue = proposalValue;
                                                    } else if (latestProposal) {
                                                    if (latestProposal.quantity) quantity = latestProposal.quantity;
                                                      const proposalValue = latestProposal.counterAmount !== undefined 
                                                        ? latestProposal.counterAmount 
                                                        : latestProposal.proposedAmount;
                                                    if (proposalValue !== undefined) unitValue = proposalValue;
                                                  }
                                                }
                                                
                                                if (quantity === 1 && serviceQuantities[serviceKey]) {
                                                  quantity = serviceQuantities[serviceKey];
                                                }
                                                
                                                const totalValue = unitValue * quantity;
                                                
                                                if (hasAcceptedCounterProposals) {
                                                  return (
                                                    <div className="flex items-center justify-between w-full">
                                                      <div className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded text-xs">
                                                        <CheckCircle className="w-3 h-3" />
                                                        Acordo fechado
                                    </div>
                                                      <div className="text-lg font-bold text-green-600">
                                                        R$ {totalValue.toLocaleString('pt-BR')}
                                    </div>
                                                    </div>
                                                  );
                                                } else if (hasPendingCounterProposals) {
                                                  return (
                                                    <div className="flex items-center justify-between w-full">
                                                      <div className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded text-xs">
                                                        <Clock className="w-3 h-3" />
                                                        Em negociação
                                                      </div>
                                                      <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                                                        R$ {totalValue.toLocaleString('pt-BR')}
                                                      </div>
                                                    </div>
                                                  );
                                                } else {
                                                  return (
                                                    <div className="flex items-center justify-between w-full">
                                                      <div className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs">
                                                        <Target className="w-3 h-3" />
                                                        Aguardando resposta
                                                      </div>
                                                      <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                                                        R$ {totalValue.toLocaleString('pt-BR')}
                                                      </div>
                                                    </div>
                                                  );
                                                }
                                              })()}
                                  </div>
                                </div>

                                          {/* Linha 2, Col 2: Ações de Aceitar/Recusar (quando não há contraproposta) ou Valor Principal (Em Negociação) */}
                                          <div className="flex items-center justify-end gap-2">
                                            {/* Se não há contrapropostas, mostrar botões de ação */}
                                            {(!budget.counterProposals || budget.counterProposals.length === 0) ? (
                                              <div className="flex items-center gap-1" data-tour="action-buttons">
                                                <button
                                                  onClick={() => handleAcceptBudget(budget, serviceKey)}
                                                  className="flex items-center justify-center p-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                                                  title={t('panels.relationship.accept_initial_value')}
                                                >
                                                  <Check className="h-3 w-3" />
                                                </button>
                                                
                                                <button
                                                  onClick={() => handleRejectBudget(budget, serviceKey)}
                                                  className="flex items-center justify-center p-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                                                  title={t('panels.relationship.reject_initial_value')}
                                                >
                                                  <X className="h-3 w-3" />
                                                </button>
                                              </div>
                                            ) : (
                                              /* 🔥 REMOVIDO: Valor duplicado - já exibido na seção de status acima */
                                              <div></div>
                                            )}
                                            </div>
                                          </div>
                                        </div>

                              

                                {/* 🆕 NOVA ESTRUTURA: Contrapropostas do Array */}
                                <div className="space-y-1 mb-3">
                                  {budget.counterProposals && budget.counterProposals.length > 0 && (
                                    <div className="space-y-2">
                                    
                                      
                                                                            {/* Lista detalhada das contrapropostas */}
                                      <div className="space-y-1">
                                        {budget.counterProposals
                                          .sort((a: any, b: any) => new Date(b.proposedAt).getTime() - new Date(a.proposedAt).getTime())
                                          .map((counterProposal: any, index: number) => (
                                          <div key={index} className={`p-2 rounded border transition-all ${
                                            counterProposal.status === 'accepted' 
                                              ? 'bg-green-50 dark:bg-green-950/30 shadow-sm' 
                                              : 'border'
                                          }`}>
                                            {/* Layout 2x2 */}
                                            <div className="grid grid-cols-2 gap-2 items-center">
                                              {/* Linha 1, Col 1: Avatar + Nome */}
                                              <div className="flex items-center gap-2">
                                                {counterProposal.proposedByUser?.imageUrl ? (
                                                  <img 
                                                    src={counterProposal.proposedByUser.imageUrl} 
                                                    alt={counterProposal.proposedByUser.userName}
                                                    className="w-6 h-6 rounded-full object-cover"
                                                  />
                                                ) : (
                                                  <div className="w-6 h-6 rounded-full bg-[#5600ce] flex items-center justify-center">
                                                    <span className="text-xs text-white font-medium">
                                                      {counterProposal.proposedByUser?.userName?.charAt(0) || 'U'}
                                                    </span>
                                                  </div>
                                                )}
                                                <div className="text-xs font-medium text-foreground">
                                                  {counterProposal.proposedByUser?.userName || `Usuário ${counterProposal.proposedBy}`}
                                                </div>
                                              </div>
                                              
                                              {/* Linha 1, Col 2: Preço */}
                                              <div className="flex items-center justify-end gap-2 group">
                                                <div className={`text-sm font-bold ${
                                                  counterProposal.status === 'accepted' 
                                                    ? 'text-green-600 dark:text-green-400' 
                                                    : 'text-[#5600ce]'
                                                }`}>
                                                  R$ {counterProposal.proposedAmount?.toLocaleString('pt-BR') || '0'}
                                                </div>
                                                
                                                {/* Ícone de observações */}
                                                {counterProposal.notes && (
                                                  <button 
                                                    className="p-1 hover:bg-[#5600ce]/20 rounded transition-colors"
                                                    onMouseEnter={(e) => showCustomTooltip(e, `Observações: ${counterProposal.notes}\n\nPor: ${counterProposal.proposedByUser?.userName || `Usuário ${counterProposal.proposedBy}`}\nEm: ${formatRelativeTime(counterProposal.proposedAt)}`)}
                                                    onMouseLeave={hideCustomTooltip}
                                                  >
                                                        <MessageSquare className="h-3 w-3 text-[#5600ce]" />
                                                      </button>
                                                )}
                                              </div>
                                              
                                              {/* Linha 2, Col 1: Tempo */}
                                              <div className="text-xs text-muted-foreground">
                                                {formatRelativeTime(counterProposal.proposedAt)}
                                              </div>
                                              
                                              {/* Linha 2, Col 2: Botões + Status */}
                                              <div className="flex items-center justify-end gap-2">
                                                {/* 🔥 BOTÕES À ESQUERDA DO STATUS */}
                                                {counterProposal.status === 'pending' && (
                                                  <div className="flex items-center gap-1 mr-2" data-tour="action-buttons">
                                                    <button
                                                      onClick={() => handleAcceptCounterProposal(counterProposal, budget)}
                                                      className="flex items-center justify-center p-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                                                      title={t('panels.relationship.accept_counter_proposal')}
                                                    >
                                                      <Check className="h-3 w-3" />
                                                    </button>
                                                    
                                                    <button
                                                      onClick={() => handleRejectCounterProposal(counterProposal, budget)}
                                                      className="flex items-center justify-center p-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                                                      title={t('panels.relationship.reject_counter_proposal')}
                                                    >
                                                      <X className="h-3 w-3" />
                                                    </button>
                                                  </div>
                                                )}
                                                
                                                {/* LABELS DE STATUS */}
                                                {counterProposal.status === 'accepted' && (
                                                  <div className="flex items-center gap-1">
                                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                                    <span className="text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded font-medium">
                                                    {t('panels.relationship.accepted')}
                                                  </span>
                                                  </div>
                                                )}
                                                {counterProposal.status === 'rejected' && (
                                                  <span className="text-xs px-1.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded">
                                                    {t('panels.relationship.rejected')}
                                                  </span>
                                                )}
                                                {counterProposal.status === 'pending' && (
                                                  <span className="text-xs px-1.5 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded">
                                                    {t('panels.relationship.pending')}
                                                  </span>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Ações de Orçamento */}
                                <div className="flex gap-2 pt-2 border-t border-dashed border-muted">
                                  {/* 🔒 PROTEÇÃO: Botão Editar - Apenas para usuários com role org:admin */}
                                  <Protect role="org:admin">
                                    <button
                                    onClick={() => handleOpenBudgetModal(serviceKey, budget.description || 'Orçamento', budget)}
                                      className="flex items-center justify-center gap-1 text-xs font-medium px-3 py-2 bg-[#ff0074]/10 dark:bg-[#ff0074]/20 text-[#ff0074] dark:text-[#ff0074] rounded-lg border border-[#ff0074]/30 hover:bg-[#ff0074]/20 dark:hover:bg-[#ff0074]/30 transition-colors flex-1"
                                      title={t('influencers.edit_budget')}
                                  >
                                    <DollarSign className="h-3 w-3" />
                                    <span className="hidden sm:inline">{t('panels.relationship.edit')}</span>
                                    <span className="sm:hidden">{t('common.edit')}</span>
                                  </button>
                                  </Protect>

                                  {selectedProposal && !(budget.counterProposals && budget.counterProposals.some((cp: any) => cp.status === 'accepted')) && (
                                                                  <button
                                  onClick={() => handleOpenCounterProposalModal(budget)}
                                    className="flex items-center justify-center gap-1 text-xs font-medium px-3 py-2 bg-[#5600ce]/10 dark:bg-[#5600ce]/20 text-[#5600ce] dark:text-[#5600ce] border border-[#5600ce]/30 hover:bg-[#5600ce]/20 dark:hover:bg-[#5600ce]/30 rounded-lg transition-colors flex-1"
                                    title={t('panels.relationship.make_counter_proposal')}
                                >
                                  <TrendingUp className="h-3 w-3" />
                                    <span className="hidden sm:inline">{t('panels.relationship.counter_proposal')}</span>
                                    <span className="sm:hidden">{t('common.counter')}</span>
                                </button>
                                  )}
                                </div>
                              </div>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                            </div>
                          ) : (
                            // 🎯 EXEMPLO VISUAL PARA TOUR - Quando não há orçamentos reais
                            <div className="space-y-4">
                              <div className="text-center py-4 border border-dashed rounded-lg bg-muted/20">
                                <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                                <p className="text-sm text-muted-foreground mb-1">{t('panels.relationship.no_budgets_found')}</p>
                                <p className="text-xs text-muted-foreground">
                                  {selectedProposal
                                    ? t('panels.relationship.create_specific_proposal')
                                    : t('panels.relationship.create_budgets_influencer')
                                  }
                                </p>
                              </div>

                             
                            </div>
                          )}
                      </div>

                   

                   
                    </div>
                  ) : (
                    <div className="flex items-center justify-center p-4 border border-dashed rounded-md">
                      <p className="text-xs text-muted-foreground">{t('dashboard.select_influencer_budgets')}</p>
                    </div>
                  )}
                </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
                 
              
                
                {/* Screenshots das Redes Sociais */}
                <Accordion type="single" collapsible className="w-full" data-tour="social-screenshots">
                  <AccordionItem value="social-screenshots">
                    <AccordionTrigger className="py-2 text-xs font-medium">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        <span className="text-xs sm:text-sm">{t('influencers.social_screenshots')}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2 pt-2">
                        {selectedInfluencer ? (
                          <SocialScreenshotsSection 
                            influencer={selectedInfluencer}
                            loadOnExpand={true}
                          />
                        ) : (
                          <div className="flex items-center justify-center p-4 border border-dashed rounded-md">
                            <p className="text-xs text-muted-foreground">Selecione um influenciador para ver os prints</p>
                          </div>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
                  {/* Campanhas */}
                    {/*
                  <Protect role="org:admin">
                    <div data-tour="influencer-campaigns">
                      <InfluencerCampaignsPanel 
                        influencerId={selectedInfluencer?.id || null}
                        influencerName={selectedInfluencer?.name}
                      />
                    </div>
                  </Protect>*/}
                {/* Documentos */}
                <div className="space-y-2" data-tour="documents-section">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                    <p className="text-xs font-medium">{t('dashboard.documents')}</p>
                      {/* 🆕 INDICADOR DE ESTRUTURA: Mostrar onde os documentos são salvos */}
                      {selectedInfluencer && (
                        <div className="flex items-center gap-1">
                          {selectedProposal ? (
                            <Badge variant="outline" className="text-xs h-5 px-1.5 text-[#ff0074] border-[#ff0074] bg-[#ff0074]/10">
                              Proposta
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-xs h-5 px-1.5 text-gray-600 border-gray-300 bg-gray-50 dark:bg-gray-900/20">
                              Perfil
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 px-2 text-xs"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={!selectedInfluencer}
                      title={selectedProposal 
                        ? `Enviar documento para a proposta: ${selectedProposal.name}` 
                        : 'Enviar documento para o perfil do influenciador'
                      }
                    >
                      Enviar
                    </Button>
                    <input 
                      type="file" 
                      ref={fileInputRef} 
                      className="hidden" 
                      onChange={handleUploadDocument}
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png"
                    />
                  </div>
                  
                  {documents.length > 0 ? (
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {documents.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-md border hover:bg-white/5 group">
                          <div className="flex items-center space-x-2">
                            <File className="h-4 w-4 text-blue-400" />
                            <p className="text-sm truncate max-w-[120px]">{doc.name}</p>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => window.open(doc.url, '_blank')}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-6 w-6 text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => handleDeleteDocument(index)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-4 border border-dashed rounded-md">
                      <FileText className="h-8 w-8 text-muted-foreground opacity-50 mb-2" />
                      <p className="text-xs text-muted-foreground text-center">
                        {selectedInfluencer ? t('influencers.no_documents_available') : t('influencers.select_influencer')}
                      </p>
                    </div>
                  )}
                </div>
                
                {/* Etiquetas - Desativado temporariamente
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">Etiquetas</p>
                  </div>
                  {selectedInfluencer ? (
                    <TagsManager 
                      influencerId={selectedInfluencer.id}
                      selectedTags={selectedInfluencer.tags || []}
                      readOnly={false}
                    />
                  ) : (
                    <div className="flex items-center p-2 rounded-md border text-muted-foreground">
                      <Tag className="h-4 w-4 mr-2" />
                      <p className="text-sm">Selecione um influenciador para gerenciar etiquetas</p>
                    </div>
                  )}
                </div>
                */}
                
                {/* Anotações - Desativado temporariamente
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">Anotações</p>
                  </div>
                  {selectedInfluencer ? (
                    <NotesManager 
                      influencerId={selectedInfluencer.id}
                      readOnly={false}
                      showNoteForm={showNoteForm}
                      setShowNoteForm={setShowNoteForm}
                    />
                  ) : (
                    <div className="flex items-center p-2 rounded-md border text-muted-foreground">
                      <Pencil className="h-4 w-4 mr-2" />
                      <p className="text-sm">Selecione um influenciador para gerenciar anotações</p>
                    </div>
                  )}
                </div>
                */}
                
                {/* Listas - Desativado temporariamente
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium">Listas</p>
                    <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">Adicionar</Button>
                  </div>
                  <p className="text-xs text-muted-foreground p-2">Este influenciador ainda não foi salvo em nenhuma lista.</p>
                </div>
                */}
              </div>
            </div>
          </div>

          {/* Painel de Perfil de Contato */}
          <div className={`relative transition-all duration-300 ${showContactPanel ? 'w-80' : 'w-0'}`} data-tour="profile-panel">
            <div className={`h-full backdrop-blur-sm bg-white/20 dark:bg-[#080210] border-l border-border dark:border-[#1c1627] overflow-y-auto scrollbar-hide transition-all duration-300 ${showContactPanel ? 'opacity-100' : 'opacity-0'}`}>
             <Protect role="org:admin"> {/* Ícone "Criado em" no canto superior direito */}
              {selectedInfluencer?.createdAt && (
                <div className="absolute top-3 right-3 z-10">
                  <div className="group relative">
                    <Calendar className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors cursor-pointer" />
                    <div className="absolute top-6 right-0 px-3 py-2 bg-popover border border-border rounded-md shadow-lg text-xs font-medium whitespace-nowrap opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                      {/* Seta do tooltip apontando para cima */}
                      <div className="absolute -top-1 right-2 w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-popover"></div>
                      <div className="text-xs text-muted-foreground mb-1">Criado em</div>
                      <div className="text-sm">
                        {new Date(selectedInfluencer.createdAt).toLocaleDateString('pt-BR', { 
                          year: 'numeric', 
                          month: '2-digit', 
                          day: '2-digit'
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              </Protect>
              <div className="p-3 space-y-6">


                {/* Perfil header */}
                <div className="flex items-start gap-2 md:gap-4">
                  <div className="relative">
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border border-background"></div>
                    <Avatar className="w-12 h-12 sm:w-16 sm:h-16">
                      <AvatarImage
                        src={selectedInfluencer?.avatar || undefined}
                        alt={selectedInfluencer ? selectedInfluencer.name : "Perfil"}
                        className="object-cover object-center"
                      />
                      <AvatarFallback className="bg-muted-foreground/20">
                        {selectedInfluencer && selectedInfluencer.name ? selectedInfluencer.name.split(" ").map((n: string) => n[0]).join("") : "--"}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="flex flex-col flex-1">
                   
                    <h2 className="text-sm sm:text-lg font-bold mb-2">{selectedInfluencer ? selectedInfluencer.name : t('panels.contact.select_influencer')}</h2>
                                          <p className="text-xs sm:text-sm text-muted-foreground mb-1">
                      {/* Idade e Gênero */}
                      {selectedInfluencer?.age && (
                        <span>{t('panels.contact.years_old', { age: selectedInfluencer.age })}</span>
                      )}
                      {selectedInfluencer?.gender && (
                        <span>
                          {selectedInfluencer?.age ? ', ' : ''}
                          {selectedInfluencer.gender === 'female' ? t('panels.contact.female') : selectedInfluencer.gender === 'male' ? t('panels.contact.male') : selectedInfluencer.gender}
                        </span>
                      )}
                      {/* Localização */}
                      {(selectedInfluencer?.age || selectedInfluencer?.gender) && formatLocationDisplay(selectedInfluencer) && ', '}
                      {formatLocationDisplay(selectedInfluencer)}
                    </p>
                                          {/* ✅ Só exibir email se disponível E não for lista compartilhada */}
                      <Protect role="org:admin">
                        {selectedInfluencer?.email && !isSharedList && (
                          <p className="text-xs sm:text-sm text-muted-foreground mb-2">{selectedInfluencer.email}</p>
                        )}
                      </Protect>
                     
                                         <p className="text-xs sm:text-sm text-muted-foreground">{selectedInfluencer ? selectedInfluencer.expertise : t('panels.contact.click_card_details')}</p>
                  </div>
                </div>
                
                
                
                {/* Dados Completos do Influenciador */}
                <div className="space-y-6">
                    <div className="space-y-4">
                      {/* ✅ CAMPOS DINÂMICOS: Só exibir informações administrativas se disponíveis E apenas para org:admin */}
                      <Protect role="org:admin">
                        {(selectedInfluencer?.responsibleName || selectedInfluencer?.agencyName || selectedInfluencer?.responsibleCapturer || selectedInfluencer?.financialData?.additionalData?.responsibleRecruiter || selectedInfluencer?.dadosFinanceiros?.responsibleRecruiter) && (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-xs sm:text-sm">
                            {selectedInfluencer?.responsibleName && (
                              <div className="flex items-center gap-2">
                                <Building className="h-4 w-4 text-muted-foreground" />
                                <span><span className="font-semibold">{t('common.responsible', 'Responsável')}:</span> {selectedInfluencer.responsibleName}</span>
                              </div>
                            )}
                            {selectedInfluencer?.agencyName && (
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-muted-foreground" />
                                <span><span className="font-semibold">{t('common.agency', 'Agência')}:</span> {selectedInfluencer.agencyName}</span>
                              </div>
                            )}
                            {(selectedInfluencer?.responsibleCapturer || selectedInfluencer?.financialData?.additionalData?.responsibleRecruiter || selectedInfluencer?.dadosFinanceiros?.responsibleRecruiter) && (
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-muted-foreground" />
                                <span><span className="font-semibold">{t('common.capturer', 'Captador')}:</span> {selectedInfluencer.responsibleCapturer || selectedInfluencer.financialData?.additionalData?.responsibleRecruiter || selectedInfluencer.dadosFinanceiros?.responsibleRecruiter}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </Protect>   

                  {/* Demografia da Audiência */}
                  <div className="space-y-6">
                    
                    {/* 🆕 ABAS UNIFICADAS: Controle central das plataformas */}
                    {selectedInfluencer?.currentDemographics && Array.isArray(selectedInfluencer.currentDemographics) && selectedInfluencer.currentDemographics.length > 0 && (
                      <div className="mb-6">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-semibold text-xs">{t('panels.contact.audience_demographics', 'Demografia da Audiência')}</h3>
                        </div>
                        
                        {/* Abas das Plataformas */}
                        <div className="flex flex-wrap gap-1 p-1 bg-muted/50 rounded-full">
                          {(() => {
                            const platformOrder = ['instagram', 'tiktok', 'youtube', 'facebook', 'twitch', 'kwai'];
                            const platformNames = {
                              instagram: 'Instagram',
                              tiktok: 'TikTok', 
                              youtube: 'YouTube',
                              facebook: 'Facebook',
                              twitch: 'Twitch',
                              kwai: 'Kwai'
                            };
                            
                            // Cores específicas de cada plataforma
                            const platformColors = {
                              youtube: "#ef4444",
                              instagram: "#ec4899",
                              tiktok: "#374151",
                              facebook: "#3b82f6",
                              twitch: "#8b5cf6",
                              kwai: "#f59e0b"
                            };
                            
                            const availablePlatforms = selectedInfluencer.currentDemographics
                              .map((d: any) => d.platform)
                              .filter((p: string) => platformOrder.includes(p))
                              .sort((a: string, b: string) => {
                                const indexA = platformOrder.indexOf(a);
                                const indexB = platformOrder.indexOf(b);
                                return indexA - indexB;
                              });
                            
                            // Se a plataforma selecionada não está disponível, selecionar a primeira disponível
                            if (!availablePlatforms.includes(selectedPlatform) && availablePlatforms.length > 0) {
                              setSelectedPlatform(availablePlatforms[0]);
                            }
                            
                            return availablePlatforms.map((platform: string) => (
                              <button
                                key={platform}
                                onClick={() => setSelectedPlatform(platform)}
                                className={`px-3 py-2 text-xs font-medium rounded-full transition-all duration-200 ${
                                  selectedPlatform === platform
                                    ? 'text-white shadow-sm transform scale-105'
                                    : 'text-muted-foreground hover:text-foreground hover:bg-white/50'
                                }`}
                                style={{
                                  backgroundColor: selectedPlatform === platform ? platformColors[platform as keyof typeof platformColors] : 'transparent'
                                }}
                              >
                                {platformNames[platform as keyof typeof platformNames] || platform}
                              </button>
                            ));
                          })()}
                        </div>
                      </div>
                    )}
                    
                    <div className="space-y-6">
                                            {/* Gênero por Plataforma */}
                      <div data-tour="gender-chart">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-xs">{t('panels.contact.gender_distribution')}</h4>
                        </div>
                        <div className="space-y-3">
                          {(() => {
                            // Verificar se há dados demográficos
                            if (!selectedInfluencer?.currentDemographics || !Array.isArray(selectedInfluencer.currentDemographics) || selectedInfluencer.currentDemographics.length === 0) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg">
                                  {t('panels.contact.no_demographic_data', 'Nenhum dado demográfico disponível')}
                                </div>
                              );
                            }

                            // Buscar dados da plataforma selecionada
                            const selectedDemographic = selectedInfluencer.currentDemographics.find((d: any) => d.platform === selectedPlatform);
                            
                            if (!selectedDemographic || !selectedDemographic.audienceGender) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                                  Dados de gênero não disponíveis para {selectedPlatform}
                                </div>
                              );
                            }

                            const gender = selectedDemographic.audienceGender;
                            
                            const chartData = [
                              {
                                name: "Feminino",
                                value: gender.female || 0,
                                fill: "#ff0074"
                              },
                              {
                                name: "Masculino", 
                                value: gender.male || 0,
                                fill: "#5600ce"
                              }
                            ];
                            
                            if (gender.other && gender.other > 0) {
                              chartData.push({
                                name: "Outros",
                                value: gender.other,
                                fill: "#6b7280"
                              });
                            }
                            
                            return (
                              <div className="flex items-center">
                                <ChartContainer
                                  config={{
                                    feminino: {
                                      label: "Feminino",
                                      color: "#ec4899",
                                    },
                                    masculino: {
                                      label: "Masculino",
                                      color: "#8b5cf6",
                                    },
                                    outros: {
                                      label: "Outros",
                                      color: "#6b7280",
                                    },
                                  }}
                                  className="h-[150px] w-[250px]"
                                >
                                  <PieChart>
                                    <Pie
                                      data={chartData}
                                      cx="50%"
                                      cy="50%"
                                      innerRadius={45}
                                      outerRadius={60}
                                      paddingAngle={2}
                                      dataKey="value"
                                    >
                                      {chartData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.fill} />
                                      ))}
                                    </Pie>
                                    <ChartTooltip
                                      content={<ChartTooltipContent />}
                                      formatter={(value, name) => [`${value}%`, name]}
                                    />
                                  </PieChart>
                                </ChartContainer>
                                
                                {/* Legenda à direita */}
                                <div className="flex flex-col ml-4 space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <div className="w-3 h-3 rounded-full" style={{backgroundColor: '#ec4899'}}></div>
                                    <span className="text-xs font-medium">Feminino</span>
                                    <span className="text-sm font-bold" style={{color: '#ec4899'}}>{gender.female || 0}%</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <div className="w-3 h-3 rounded-full" style={{backgroundColor: '#8b5cf6'}}></div>
                                    <span className="text-xs font-medium">Masculino</span>
                                    <span className="text-sm font-bold" style={{color: '#8b5cf6'}}>{gender.male || 0}%</span>
                                  </div>
                                  {gender.other && gender.other > 0 && (
                                    <div className="flex items-center space-x-2">
                                      <div className="w-3 h-3 rounded-full" style={{backgroundColor: '#6b7280'}}></div>
                                      <span className="text-xs font-medium">Outros</span>
                                      <span className="text-sm font-bold" style={{color: '#6b7280'}}>{gender.other}%</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            );
                          })()}
                        </div>
                      </div>

                      {/* Faixas Etárias por Rede Social */}
                      <div data-tour="age-range-chart">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-xs">{t('audience.age_ranges')}</h4>
                        </div>
                        <div className="space-y-3">
                          {(() => {
                            // Verificar se há dados demográficos
                            if (!selectedInfluencer?.currentDemographics || !Array.isArray(selectedInfluencer.currentDemographics) || selectedInfluencer.currentDemographics.length === 0) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg">
                                  {t('audience.no_age_data')}
                                </div>
                              );
                            }

                            // Buscar dados da plataforma selecionada
                            const selectedDemographic = selectedInfluencer.currentDemographics.find((d: any) => d.platform === selectedPlatform);
                            
                            if (!selectedDemographic || !selectedDemographic.audienceAgeRange || selectedDemographic.audienceAgeRange.length === 0) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                                  Dados de faixa etária não disponíveis para {selectedPlatform}
                                </div>
                              );
                            }

                            // Preparar dados para o gráfico de barras simples
                            const ageRangeData = selectedDemographic.audienceAgeRange
                              .map((item: any) => ({
                                ageRange: item.range,
                                percentage: item.percentage
                              }))
                              .sort((a, b) => {
                                const getMinAge = (range: string): number => {
                                  const match = range.match(/(\d+)/);
                                  return match ? parseInt(match[1]) : 0;
                                };
                                return getMinAge(a.ageRange) - getMinAge(b.ageRange);
                              });

                            // Definir cor baseada na plataforma selecionada
                            const platformColors: { [key: string]: string } = {
                              youtube: "#ef4444",
                              instagram: "#ec4899",
                              tiktok: "#374151",
                              facebook: "#3b82f6",
                              twitch: "#8b5cf6",
                              kwai: "#f59e0b"
                            };
                            
                            const platformColor = platformColors[selectedPlatform] || "#ff0074";
                            const platformNames: { [key: string]: string } = {
                              youtube: "YouTube",
                              instagram: "Instagram",
                              tiktok: "TikTok",
                              facebook: "Facebook",
                              twitch: "Twitch",
                              kwai: "Kwai"
                            };
                            const platformDisplayName = platformNames[selectedPlatform] || selectedPlatform;

                            return (
                              <ChartContainer
                                config={{
                                  percentage: {
                                    label: "Porcentagem",
                                    color: platformColor,
                                  },
                                }}
                                className="h-[350px] w-full"
                              >
                                <BarChart
                                  data={ageRangeData}
                                  margin={{
                                    top: 20,
                                    right: 30,
                                    left: 20,
                                    bottom: 5,
                                  }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                                  <XAxis
                                    dataKey="ageRange" 
                                    tick={{ fontSize: 12, fontWeight: 500 }}
                                    axisLine={false}
                                    tickLine={false}
                                  />
                                  <YAxis
                                    tick={{ fontSize: 11 }}
                                    axisLine={false}
                                    tickLine={false}
                                    tickFormatter={(value) => `${value}%`}
                                  />
                                  <ChartTooltip
                                    content={({ active, payload, label }) => {
                                      if (active && payload && payload.length) {
                                        return (
                                          <div className="bg-background border rounded-lg shadow-lg p-3">
                                            <p className="font-semibold text-xs">{label} anos • {platformDisplayName}</p>
                                            <p className="text-sm">
                                              <span className="font-medium">Audiência:</span> {payload[0]?.value}%
                                            </p>
                                          </div>
                                        );
                                      }
                                      return null;
                                    }}
                                  />
                                  <Bar
                                    dataKey="percentage" 
                                    fill={platformColor}
                                    radius={[4, 4, 0, 0]}
                                    className="drop-shadow-sm"
                                  >
                                    <LabelList
                                      dataKey="percentage" 
                                      position="top" 
                                      style={{ 
                                        fontSize: '12px', 
                                        fontWeight: '600',
                                        fill: 'text-muted-foreground'
                                      }}
                                      formatter={(value: number) => `${value}%`}
                                    />
                                  </Bar>
                                </BarChart>
                              </ChartContainer>
                            );
                          })()}
                        </div>
                      </div>

                                            {/* Países da Audiência por Rede Social */}
                      <div data-tour="location-countries-chart">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-xs">{t('audience.countries')}</h4>
                        </div>
                        <div className="space-y-3">
                          {(() => {
                            // Verificar se há dados demográficos
                            if (!selectedInfluencer?.currentDemographics || !Array.isArray(selectedInfluencer.currentDemographics) || selectedInfluencer.currentDemographics.length === 0) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg">
                                  {t('audience.no_country_data')}
                                </div>
                              );
                            }

                            // Buscar dados da plataforma selecionada
                            const selectedDemographic = selectedInfluencer.currentDemographics.find((d: any) => d.platform === selectedPlatform);
                            
                            if (!selectedDemographic) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                                  Dados de países não disponíveis para {selectedPlatform}
                                </div>
                              );
                            }

                            // Preparar dados para o gráfico
                            const locationData = (() => {
                              if (!selectedDemographic.audienceLocations || !Array.isArray(selectedDemographic.audienceLocations)) {
                                // Dados de exemplo quando não há dados reais
                                return [
                                  { location: 'Brazil', percentage: 97.44 },
                                  { location: 'United States', percentage: 0.71 },
                                  { location: 'Portugal', percentage: 0.44 },
                                  { location: 'Spain', percentage: 0.15 }
                                ];
                              }
                              
                              return selectedDemographic.audienceLocations
                                .filter((locationData: any) => locationData.country && locationData.percentage > 0)
                                .map((locationData: any) => ({ 
                                  location: locationData.country, 
                                  percentage: locationData.percentage 
                                }));
                            })();

                            return (
                              <LocationBarChart
                                data={locationData}
                                maxItems={6}
                              />
                            );
                          })()}
                        </div>
                      </div>
                        
                                            {/* Cidades da Audiência por Rede Social */}
                      <div data-tour="location-cities-chart">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-xs">{t('audience.cities')}</h4>
                        </div>
                        <div className="space-y-3">
                          {(() => {
                            // Verificar se há dados demográficos
                            if (!selectedInfluencer?.currentDemographics || !Array.isArray(selectedInfluencer.currentDemographics) || selectedInfluencer.currentDemographics.length === 0) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg">
                                  {t('audience.no_city_data')}
                                </div>
                              );
                            }

                            // Buscar dados da plataforma selecionada
                            const selectedDemographic = selectedInfluencer.currentDemographics.find((d: any) => d.platform === selectedPlatform);
                            
                            if (!selectedDemographic) {
                              return (
                                <div className="text-xs text-muted-foreground p-4 bg-muted/20 rounded-lg text-center">
                                  Dados de cidades não disponíveis para {selectedPlatform}
                                </div>
                              );
                            }

                            // Preparar dados para o gráfico
                            const cityData = (() => {
                              if (!selectedDemographic.audienceCities || !Array.isArray(selectedDemographic.audienceCities)) {
                                // Dados de exemplo quando não há dados reais
                                return [
                                  { location: 'Rio de Janeiro', percentage: 50.91 },
                                  { location: 'Brasília', percentage: 1.58 },
                                  { location: 'São Paulo', percentage: 1.55 },
                                  { location: 'Juiz de Fora', percentage: 1.40 },
                                  { location: 'Manaus', percentage: 1.16 }
                                ];
                              }
                              
                              return selectedDemographic.audienceCities
                                .filter((cityData: any) => cityData.city && cityData.percentage > 0)
                                .map((cityData: any) => ({ 
                                  location: cityData.city, 
                                  percentage: cityData.percentage 
                                }));
                            })();

                            return (
                              <LocationBarChart
                                data={cityData}
                                maxItems={6}
                              />
                            );
                          })()}
                        </div>
                      </div>

                      {/* Interesses da Audiência */}
                      <Protect role="org:admin">
                        <div className="p-4 rounded-lg border dark:border-[#1c1627] text-sm" data-tour="interests-chart">
                          <AudienceInterests
                            interests={(() => {
                              // 🔥 NOVA ESTRUTURA: Agregar interesses usando currentDemographics
                              if (!selectedInfluencer?.currentDemographics || !Array.isArray(selectedInfluencer.currentDemographics)) {
                                // Dados de exemplo quando não há dados reais
                                return [
                                  { category: 'Friends, Family & Relationships', percentage: 18.5 },
                                  { category: 'Sports', percentage: 15.2 },
                                  { category: 'Television & Film', percentage: 14.8 },
                                  { category: 'Music', percentage: 12.3 },
                                  { category: 'Restaurants, Food & Grocery', percentage: 11.7 },
                                  { category: 'Fashion & Beauty', percentage: 9.4 },
                                  { category: 'Technology', percentage: 8.1 },
                                  { category: 'Travel', percentage: 6.9 },
                                  { category: 'Gaming', percentage: 3.1 }
                                ];
                              }
                              
                              const interestMap = new Map<string, number>();
                              
                              // Iterar pelos dados demográficos de cada plataforma
                              selectedInfluencer.currentDemographics.forEach((demographic: any) => {
                                if (demographic.audienceInterests && Array.isArray(demographic.audienceInterests)) {
                                  demographic.audienceInterests.forEach((interestData: any) => {
                                    if (interestData.category && interestData.percentage) {
                                      const currentPercentage = interestMap.get(interestData.category) || 0;
                                      interestMap.set(interestData.category, currentPercentage + interestData.percentage);
                                    }
                                  });
                                }
                              });
                              
                              // Converter para array com formato correto
                              return Array.from(interestMap.entries())
                                .map(([category, percentage]) => ({ category, percentage }));
                              })()}
                            title={t('influencers.audience_interests')}
                            maxItems={10}
                          />
                        </div>
                      </Protect>

                      {/* Redes Sociais */}
                  <div className="space-y-4">
                    <Separator />
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <p className="text-xs font-semibold flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" /> 
                          {t('influencers.engagement_rate_by_network')}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-4 w-4 text-muted-foreground hover:text-primary transition-colors cursor-pointer" />
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-sm p-3">
                                <div className="space-y-2 text-sm">
                                  <p className="font-semibold">{t('influencers.how_engagement_rate_calculated')}</p>
                                  <div className="space-y-1 text-xs">
                                    
                                    <p><strong>• Cálculo Automático:</strong> (Visualizações Médias / Seguidores) × 100</p>
                                    <p><strong>• Engagement Geral:</strong> Média aritmética entre todas as plataformas</p>
                                  </div>
                                  <p className="text-xs text-muted-foreground mt-2">
                                    Cada plataforma tem seu próprio engagement rate específico
                                  </p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </p>
                      </div>
                      <ChartContainer
                        data-tour="engagement-chart"
                        config={{
                          youtube: {
                            label: "YouTube",
                            color: "#ef4444",
                          },
                          instagram: {
                            label: "Instagram",
                            color: "#ec4899",
                          },
                          tiktok: {
                            label: "TikTok",
                            color: "#374151",
                          },
                          facebook: {
                            label: "Facebook",
                            color: "#3b82f6",
                          },
                          twitch: {
                            label: "Twitch",
                            color: "#8b5cf6",
                          },
                          kwai: {
                            label: "Kwai",
                            color: "#f59e0b",
                          },
                        }}
                        className="h-[300px] sm:h-[400px] w-full"
                      >
                        <BarChart
                          data={(() => {
const engagementData: { name: string; engagement: number; fill: string }[] = [];
                            // 🔥 NOVA ESTRUTURA: Campos de engagement rate em camelCase
                            const platforms = [
                              { key: 'youtubeEngagementRate', name: 'YouTube', color: '#ef4444' },
                              { key: 'instagramEngagementRate', name: 'Instagram', color: '#ec4899' },
                              { key: 'tiktokEngagementRate', name: 'TikTok', color: '#374151' },
                              { key: 'facebookEngagementRate', name: 'Facebook', color: '#3b82f6' },
                              { key: 'twitchEngagementRate', name: 'Twitch', color: '#8b5cf6' },
                              { key: 'kwaiEngagementRate', name: 'Kwai', color: '#f59e0b' }
                            ];
                            
                            platforms.forEach(platform => {
                              const rate = selectedInfluencer?.[platform.key];
                              if (rate && rate !== '' && !isNaN(parseFloat(rate))) {
                                const numericRate = Math.min(parseFloat(rate), 100);
                                engagementData.push({
                                  name: platform.name,
                                  engagement: parseFloat(numericRate.toFixed(2)),
                                  fill: platform.color
                                });
                              }
                            });
                            
                            return engagementData.sort((a, b) => b.engagement - a.engagement);
                          })()
                          }
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 40,
                          }}
                        >
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis
                            dataKey="name" 
                            tick={{ fontSize: 11, fontWeight: 500 }}
                            axisLine={false}
                            tickLine={false}
                        />
                        <YAxis
                            domain={[0, 100]}
                            tick={{ fontSize: 10 }}
                            axisLine={false}
                            tickLine={false}
                            tickFormatter={(value) => `${value}%`}
                        />
                        <ChartTooltip
                            content={({ active, payload, label }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <div className="bg-background border rounded-lg shadow-lg p-3">
                                    <p className="font-semibold text-xs">{label}</p>
                                    <p className="text-sm">
                                      <span className="font-medium">Engajamento:</span> {(() => {
                                        const numValue = parseFloat((payload[0]?.value as string) || '0');
                                        if (numValue === 0) return '0%';
                                        const formatted = numValue.toFixed(2);
                                        return formatted.endsWith('.00') ? `${numValue.toFixed(0)}%` : `${formatted}%`;
                                      })()}
                                    </p>
                                  </div>
                                );
                              }
                              return null;
                            }}
                        />
                        <Bar
                            dataKey="engagement" 
                            radius={[6, 6, 0, 0]}
                            className="drop-shadow-sm"
                          >
                          <LabelList
                              dataKey="engagement" 
                              position="top" 
                              formatter={(value: any) => {
                                const numValue = parseFloat(value);
                                if (numValue === 0) return '0%';
                                const formatted = numValue.toFixed(2);
                                return formatted.endsWith('.00') ? `${numValue.toFixed(0)}%` : `${formatted}%`;
                              }}
                              className="fill-foreground text-xs font-medium"
                            />
                        </Bar>
                        </BarChart>
                      </ChartContainer>
                      
                      {/* Taxa Geral de Engajamento */}
                      <div className="mt-4 p-4 bg-white dark:bg-[#080210] rounded-lg border">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                            <span className="font-semibold text-xs sm:text-sm text-foreground">{t('influencers.overall_engagement_rate')}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-lg sm:text-xl font-bold text-primary">
                              {(() => {
                                // 🔥 NOVA ESTRUTURA: Campos de engagement rate em camelCase
                                const platforms = [
                                  'youtubeEngagementRate',
                                  'instagramEngagementRate', 
                                  'tiktokEngagementRate',
                                  'facebookEngagementRate',
                                  'twitchEngagementRate',
                                  'kwaiEngagementRate'
                                ];
                                
                                const validRates = platforms
                                  .map(key => selectedInfluencer?.[key])
                                  .filter(rate => rate && rate !== '' && !isNaN(parseFloat(rate)))
                                  .map(rate => parseFloat(rate));
                                
                                if (validRates.length === 0) return '0%';
                                
                                const average = validRates.reduce((sum, rate) => sum + rate, 0) / validRates.length;
                                if (average === 0) return '0%';
                                const formatted = average.toFixed(2);
                                return formatted.endsWith('.00') ? `${average.toFixed(0)}%` : `${formatted}%`;
                              })()
                              }
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {(() => {
                                // 🔥 NOVA ESTRUTURA: Campos de engagement rate em camelCase
                                const platforms = [
                                  'youtubeEngagementRate',
                                  'instagramEngagementRate', 
                                  'tiktokEngagementRate',
                                  'facebookEngagementRate',
                                  'twitchEngagementRate',
                                  'kwaiEngagementRate'
                                ];
                                
                                const validRates = platforms
                                  .map(key => selectedInfluencer?.[key])
                                  .filter(rate => rate && rate !== '' && !isNaN(parseFloat(rate)));
                                
                                return t('influencers.based_on_networks', {
                                  count: validRates.length,
                                  plural: validRates.length !== 1 ? 's' : '',
                                  pluralSocial: validRates.length !== 1 ? 'is' : ''
                                });
                              })()
                              }
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                 </div>
                    </div>
                  </div>

               int
                  </div>
                        {/* Visualizações por Tipo de Conteúdo */}
                        <div className="space-y-4">
                    <Separator />
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <p className="text-xs font-semibold flex items-center gap-2">
                          <Eye className="h-4 w-4" /> 
{t('panels.contact.views_by_content_type', 'Visualizações por Tipo de Conteúdo')}
                        </p>
                        <div className="flex items-center gap-2">
                          <div className="text-xs text-muted-foreground">
{t('common.last_30_days', 'Últimos 30 dias')}
                          </div>
                        </div>
                      </div>

                      <ChartContainer
                        data-tour="content-views-chart"
                        config={{
                          views: {
                            label: t('influencers.views'),
                            color: "var(--chart-2)",
                          },
                          label: {
                            color: "var(--background)",
                          },
                        }}
                        className="h-[300px] sm:h-[400px] w-full"
                      >
                        <BarChart
                          accessibilityLayer
                          data={(() => {
                            const chartData = [];
                            
                            // 🔥 FUNÇÃO CONSISTENTE: Converter visualizações (mesma lógica do total)
                            const convertViews = (value: string | number | null | undefined) => {
                              if (!value) return 0;
                              
                              if (typeof value === 'string') {
                                // Remove pontos, vírgulas e espaços, mantém apenas números
                                const cleanValue = value.replace(/[.,\s]/g, '');
                                return parseInt(cleanValue) || 0;
                              }
                              return Number(value) || 0;
                            };
                            
                            // 🔍 DADOS DO CHART - mesma estrutura do cálculo total
                            const contentTypes = [
                              {
                                name: "Instagram Stories",
                                value: selectedInfluencer?.instagramStoriesViews,
                                color: "#ec4899"
                              },
                              {
                                name: "Instagram Reels", 
                                value: selectedInfluencer?.instagramReelsViews,
                                color: "#d946ef"
                              },
                              {
                                name: "TikTok Vídeos",
                                value: selectedInfluencer?.tiktokVideoViews,
                                color: "#374151"
                              },
                                                            {
                                name: "YouTube Shorts",
                                value: selectedInfluencer?.youtubeShortsViews,
                                color: "#ef4444"
                              },
                              {
                                name: "YouTube Longos",
                                value: selectedInfluencer?.youtubeLongFormViews,
                                color: "#dc2626"
                              },
                              {
                                name: "Facebook Stories",
                                value: selectedInfluencer?.facebookStoriesViews,
                                color: "#1877f2"
                              },
                              {
                                name: "Facebook Reels",
                                value: selectedInfluencer?.facebookReelsViews,
                                color: "#4267b2"
                              },
                              {
                                name: "Facebook Posts",
                                value: selectedInfluencer?.facebookViews,
                                color: "#3b82f6"
                              },
                              {
                                name: "Twitch Streams",
                                value: selectedInfluencer?.twitchViews,
                                color: "#9146ff"
                              },
                              {
                                name: "Kwai Vídeos",
                                value: selectedInfluencer?.kwaiViews,
                                color: "#ff6900"
                              }
                            ];
                            
                            // Processar dados e adicionar ao chart apenas se tiver visualizações
                            contentTypes.forEach(type => {
                              const views = convertViews(type.value);
                              if (views > 0) {
                                chartData.push({
                                  platform: type.name,
                                  views: views,
                                  fill: type.color,
                                });
                              }
                            });

                            
                            // Ordenar por número de visualizações (maior para menor)
                            return chartData.sort((a, b) => b.views - a.views);
                          })()
                          }
                          layout="vertical"
                          maxBarSize={35}
                          margin={{
                            top: 20,
                            right: 50,
                            bottom: 20,
                            left: 20,
                          }}
                        >
                        <CartesianGrid horizontal={false} />
                        <YAxis
                            dataKey="platform"
                            type="category"
                            tickLine={false}
                            tickMargin={8}
                            axisLine={false}
                            width={100}
                            className="text-xs fill-muted-foreground"
                            tick={{ fontSize: 11 }}
                        />
                        <XAxis dataKey="views" type="number" hide />
                        <ChartTooltip
                            cursor={false}
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0]?.payload;
                                return (
                                  <div className="rounded-lg border bg-background p-3 shadow-lg">
                                    <div className="grid gap-2">
                                      <div className="flex flex-col">
                                        <span className="text-[0.70rem] uppercase text-muted-foreground">
                                          {data?.platform}
                                        </span>
                                        <span className="font-bold text-foreground">
                                          {data?.views?.toLocaleString()} visualizações
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                        />
                        <Bar
                            dataKey="views"
                            layout="vertical"
                            radius={4}
                          >
                          <LabelList
                              dataKey="views"
                              position="right"
                              offset={8}
                              className="fill-foreground"
                              fontSize={12}
                              formatter={(value: number) => {
                                if (value >= 1000000) {
                                  return `${(value / 1000000).toFixed(1)}M`;
                                } else if (value >= 1000) {
                                  return `${(value / 1000).toFixed(0)}K`;
                                }
                                return value.toLocaleString();
                              }}
                            />
                        </Bar>
                        </BarChart>
                      </ChartContainer>
                      

                      
                      {/* Total de Visualizações */}
                      <div className="mt-4 p-4 bg-white dark:bg-[#080210] rounded-lg border">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                            <span className="font-semibold text-xs sm:text-sm text-foreground">{t('panels.contact.total_views', 'Total de Visualizações')}:</span>
                          </div>
                          <div className="text-right">
                            <div className="text-lg sm:text-xl font-bold text-primary">
                              {totalViews}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {t('panels.contact.based_on_content_types', { count: activeContentTypes, plural: activeContentTypes !== 1 ? 's' : '' }, `Baseado em ${activeContentTypes} tipo${activeContentTypes !== 1 ? 's' : ''} de conteúdo`)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Modal form removido */}

      {/* Toast notifications */}
      <Toaster position="top-right" richColors />
      


      {/* 🔥 OTIMIZAÇÃO: Sheet de Orçamento Personalizado com Lazy Loading */}
      <Sheet open={showBudgetModal} onOpenChange={setShowBudgetModal}>
        <SheetContent className="w-full sm:w-[500px] md:w-[540px] max-h-screen overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {editingBudget ? 'Editar Orçamento' : 'Orçamento Personalizado'}
            </SheetTitle>
          </SheetHeader>
          
          <div className="space-y-6 py-6">
            {/* Valor do Orçamento */}
            <div className="space-y-3">
              <Label htmlFor="budgetAmount" className="text-base font-medium">Valor</Label>
              <div className="flex gap-3">
                <Select value={budgetCurrency} onValueChange={setBudgetCurrency}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BRL">R$</SelectItem>
                    <SelectItem value="USD">$</SelectItem>
                    <SelectItem value="EUR">€</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  id="budgetAmount"
                  type="text"
                  placeholder="0,00"
                  value={budgetAmount}
                  onChange={handleBudgetAmountChange}
                  className="text-right font-medium text-lg flex-1"
                />
              </div>
            </div>

            {/* Quantidade de Serviços */}
            <div className="space-y-3">
              <Label htmlFor="budgetQuantity" className="text-base font-medium">Quantidade de Serviços</Label>
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                  Qtd:
                </div>
                <Input
                  id="budgetQuantity"
                  type="number"
                  min="1"
                  value={budgetQuantity}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || parseInt(value) >= 1) {
                      setBudgetQuantity(value);
                    }
                  }}
                  placeholder="1"
                  className="pl-16 text-lg font-medium"
                />
              </div>
              <div className="text-xs text-muted-foreground">
                Quantos serviços deste tipo serão entregues
              </div>
            </div>

            {/* Descrição */}
            <div className="space-y-3">
              <Label htmlFor="budgetDescription" className="text-base font-medium">Descrição</Label>
              <Textarea
                id="budgetDescription"
                placeholder={t('influencers.service_details')}
                value={budgetDescription}
                onChange={(e) => setBudgetDescription(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>
          </div>

          {/* Botões de ação */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={handleCloseBudgetModal}
              disabled={savingBudget}
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveBudget}
              disabled={!budgetAmount || savingBudget}
              className="bg-gradient-to-r from-[#ff0074] text-white to-[#5600ce] hover:from-[#ff0074]/90 hover:to-[#5600ce]/90"
            >
              {savingBudget ? (editingBudget ? 'Atualizando...' : 'Salvando...') : (editingBudget ? 'Atualizar Orçamento' : 'Salvar Orçamento')}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Sheet de Todos os Serviços para Orçamento */}
      <Sheet open={showBudgetSheet} onOpenChange={setShowBudgetSheet}>
        <SheetContent className="w-full sm:w-[1600px] md:w-[1700px] max-h-screen overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              Orçar Serviços
            </SheetTitle>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Configure orçamentos para os serviços disponíveis
              </p>
              {selectedProposal ? (
                <div className="flex items-center gap-2 p-2 bg-[#ff0074]/10 border border-[#ff0074]/30 rounded-lg">
                  
                  <span className="text-xs text-[#ff0074]">
                    Os orçamentos aqui ficam sincronizados com a página de propostas
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-2 p-2 bg-gray-100 border border-gray-300 rounded-lg">
                  <Badge variant="outline" className="text-xs bg-gray-100 border-gray-300 text-gray-600">
                    💼 Independentes
                  </Badge>
                  <span className="text-xs text-gray-600">
                    Orçamentos não vinculados a propostas específicas
                  </span>
                </div>
              )}
            </div>
          </SheetHeader>
          
          <div className="space-y-6 py-6">
            {selectedInfluencer?.currentPricing?.services ? (
              <>

                {selectedInfluencer.currentPricing?.services && Object.entries(selectedInfluencer.currentPricing.services).map(([platformName, platformData]) => {
                const platformColors: { [key: string]: string } = {
                  instagram: '#ec4899',
                  tiktok: '#374151',
                  youtube: '#ef4444',
                  facebook: '#3b82f6',
                  twitch: '#8b5cf6',
                  kwai: '#f59e0b',
                  linkedin: '#3b82f6',
                  twitter: '#0ea5e9',
                  snapchat: '#eab308',
                  discord: '#8b5cf6',
                  pinterest: '#ec4899',
                  spotify: '#22c55e'
                };
                
                const platformColor = platformColors[platformName.toLowerCase()] || '#6B7280';
                
                return (
                  <div key={platformName} className="space-y-4">
                    {/* Cabeçalho da Plataforma */}
                    <div className="flex items-center gap-3 pb-2 border-b">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: platformColor }}
                      />
                      <h3 className="font-medium text-lg">
                        {platformName.charAt(0).toUpperCase() + platformName.slice(1)}
                      </h3>
                    </div>
                    
                    {/* Serviços da Plataforma */}
                    <div className="grid gap-3">
                      {platformData ? Object.entries(platformData as any).map(([serviceName, serviceData]: [string, any]) => {
                        if (!serviceData?.price) return null;
                        
                        const serviceKey = `${platformName.toLowerCase()}_${serviceName.toLowerCase()}`;
                        const displayName = serviceName.charAt(0).toUpperCase() + serviceName.slice(1);
                        const existingBudget = existingBudgets[serviceKey];
                        
                        return (
                          <div key={serviceKey} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors">
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium text-xs">{displayName}</h4>
                                {/* 🔒 CONTROLE DE ACESSO: Pricing só para proprietários */}
                                {(() => {
                                  // Verificar se é proprietário do influenciador
                                  const isInfluencerOwner = selectedInfluencer?.userId === userId;
                                  
                                  // Verificar se é proprietário da proposta (se houver)
                                  const isProposalOwner = selectedProposal ? selectedProposal.criadoPor === userId : true;
                                  
                                  // Verificar se tem permissões de admin/editor via Clerk metadata
                                  const hasAdvancedPermissions = selectedProposal && clerkUser?.unsafeMetadata?.p && 
                                    (clerkUser.unsafeMetadata.p as any)?.[selectedProposal.id] && 
                                    ['a', 'e'].includes((clerkUser.unsafeMetadata.p as any)[selectedProposal.id]);
                                  
                                  // Exibir pricing apenas para proprietários ou admin/editor
                                  const canViewPricing = isInfluencerOwner || isProposalOwner || hasAdvancedPermissions;
                                  
                                  return canViewPricing ? (
                                <div className="text-xs text-muted-foreground">
                                  Pricing: R$ {serviceData.price?.toLocaleString('pt-BR')}
                                </div>
                                  ) : (
                                    <div className="text-xs text-muted-foreground">
                                      Valor de referência não disponível
                                    </div>
                                  );
                                })()}
                              </div>
                              
                              {existingBudget && (
                                <div className="flex items-center gap-4 text-xs">
                                  <span className="text-green-600">
                                    Orçamento: R$ {existingBudget.amount?.toLocaleString('pt-BR')}
                                  </span>
                                  {existingBudget.hasCounterProposal && existingBudget.counterProposals?.[0] && (
                                    <span className="text-amber-600">
                                      Contraproposta: R$ {existingBudget.counterProposals[0].counterAmount?.toLocaleString('pt-BR')}
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                            
                            <div className="flex gap-2">
                              <Button
                                variant={existingBudget ? "outline" : "default"}
                                size="sm"
                                onClick={() => {
                                  handleOpenBudgetModal(serviceKey, displayName, existingBudget);
                                  setShowBudgetSheet(false);
                                }}
                                className={existingBudget ? "" : "bg-[#ff0074] hover:bg-[#ff0074]/90 text-white"}
                              >
                                {existingBudget ? 'Editar' : 'Orçar'}
                              </Button>
                              
                              {existingBudget && !(existingBudget.counterProposals && existingBudget.counterProposals.some((cp: any) => cp.status === 'accepted')) && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    handleOpenCounterProposalModal(existingBudget);
                                    setShowBudgetSheet(false);
                                  }}
                                  className="text-purple-600 hover:text-purple-700"
                                >
                                  Contra
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      }) : null}
                    </div>
                  </div>
                );
              })}
              </>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <Receipt className="h-12 w-12 text-muted-foreground opacity-50 mb-3" />
                <p className="text-sm text-muted-foreground mb-2">
                  Nenhum serviço disponível
                </p>
                <p className="text-xs text-muted-foreground">
                  O pricing deste influenciador ainda não foi configurado
                </p>
              </div>
            )}
          </div>

          {/* Resumo e Fechar */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              {Object.keys(existingBudgets).length} orçamento(s) configurado(s)
            </div>
            <Button 
              onClick={() => setShowBudgetSheet(false)}
              variant="outline"
            >
              Fechar
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Sheet de Contraproposta */}
      <Sheet open={showCounterProposalModal} onOpenChange={setShowCounterProposalModal}>
        <SheetContent className="w-full sm:w-[500px] md:w-[540px] max-h-screen overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {selectedProposal ? 'Contraproposta de Colaborador' : 'Contraproposta'}
            </SheetTitle>
            {selectedProposal && (
              <div className="text-sm text-muted-foreground">
                Proposta: <span className="font-medium text-[#ff0074]">{selectedProposal.name}</span>
              </div>
            )}
          </SheetHeader>
          
          <div className="space-y-6 py-6">
            {/* Comparação de valores */}
            {selectedBudgetForCounter && (
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">Valor original</div>
                <div className="text-xl font-semibold">R$ {selectedBudgetForCounter.amount?.toLocaleString('pt-BR') || '0'}</div>
              </div>
            )}

            {/* Valor da Contraproposta */}
            <div className="space-y-3">
              <Label htmlFor="counterProposalAmount" className="text-base font-medium">Novo valor</Label>
              <div className="flex gap-3">
                <Select value={counterProposalCurrency} onValueChange={setCounterProposalCurrency}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BRL">R$</SelectItem>
                    <SelectItem value="USD">$</SelectItem>
                    <SelectItem value="EUR">€</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  id="counterProposalAmount"
                  type="text"
                  placeholder="0,00"
                  value={counterProposalAmount}
                  onChange={handleCounterProposalAmountChange}
                  className="text-right font-medium text-lg flex-1"
                />
              </div>
            </div>

            {/* Nota da Contraproposta */}
            <div className="space-y-3">
              <Label htmlFor="counterProposalNote" className="text-base font-medium">Observações</Label>
              <Textarea
                id="counterProposalNote"
                placeholder="Justificativa da contraproposta..."
                value={counterProposalNote}
                onChange={(e) => setCounterProposalNote(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>
          </div>

          {/* Botões de ação */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={handleCloseCounterProposalModal}
              disabled={savingCounterProposal}
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveCounterProposal}
              disabled={!counterProposalAmount || savingCounterProposal}
              className="bg-gradient-to-r from-[#ff0074] text-white to-[#5600ce] hover:from-[#ff0074]/90 hover:to-[#5600ce]/90"
            >
              {savingCounterProposal ? 'Enviando...' : 'Enviar Contraproposta'}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

    </div>
    <ShadcnToaster />
    
    {/* Tooltip customizado com Portal */}
    <CustomTooltip 
      content={tooltipContent}
      position={tooltipPosition}
      visible={tooltipVisible}
    />
    
        </TooltipProvider>
      </ProfilePanelTourProvider>
    </Suspense>
  );
}

