import { NextRequest, NextResponse } from 'next/server';
import { ListService } from '@/services/list-service';
import { 
  withListOwnershipValidation, 
  withUpdateListValidation 
} from '@/lib/middleware/lists-middleware';
import { Lista, AtualizarListaData } from '@/types/list';

/**
 * 📝 API ROUTES PARA OPERAÇÕES ESPECÍFICAS POR ID DE LISTA
 * Endpoints para operações individuais com listas
 */

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/lists/[id] - Buscar lista específica por ID
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  return withListOwnershipValidation(
    async (req: NextRequest, userId: string, context: any, lista: Lista) => {
      try {
        return NextResponse.json(lista);

      } catch (error: any) {
        console.error('[API_LIST_GET_BY_ID]', { userId, listaId: params.id, error });
        return NextResponse.json(
          { error: 'Erro interno ao buscar lista' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * PUT /api/lists/[id] - Atualizar lista específica
 */
export async function PUT(req: NextRequest, { params }: RouteParams) {
  return withUpdateListValidation(
    async (req: NextRequest, userId: string, context: any, listaId: string, data: AtualizarListaData) => {
      try {
        const listaAtualizada = await ListService.atualizarLista(listaId, data, userId);

        return NextResponse.json(listaAtualizada);

      } catch (error: any) {
        console.error('[API_LIST_PUT]', { userId, listaId, data, error });
        
        if (error?.message?.includes('não encontrada')) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        if (error?.message?.includes('permissão')) {
          return NextResponse.json(
            { error: 'Sem permissão para editar esta lista' },
            { status: 403 }
          );
        }

        return NextResponse.json(
          { error: 'Erro interno ao atualizar lista' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * DELETE /api/lists/[id] - Deletar lista específica
 */
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  return withListOwnershipValidation(
    async (req: NextRequest, userId: string, context: any, lista: Lista) => {
      try {
        await ListService.deletarLista(params.id, userId);

        return NextResponse.json(
          { message: 'Lista deletada com sucesso' },
          { status: 200 }
        );

      } catch (error: any) {
        console.error('[API_LIST_DELETE]', { userId, listaId: params.id, error });
        
        if (error?.message?.includes('não encontrada')) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        if (error?.message?.includes('permissão')) {
          return NextResponse.json(
            { error: 'Sem permissão para deletar esta lista' },
            { status: 403 }
          );
        }

        return NextResponse.json(
          { error: 'Erro interno ao deletar lista' },
          { status: 500 }
        );
      }
    }
  )(req);
} 