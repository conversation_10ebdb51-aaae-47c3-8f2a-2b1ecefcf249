# 📅 FASE 3: Firebase Security Rules (1-2 dias)

## 🎯 Objetivo
Implementar regras de segurança rigorosas no Firestore para isolamento automático de dados por usuário.

## 📝 Tarefas Principais

### 3.1 Funções Utilitárias
```javascript
// firestore.rules - Funções base
function isAuthenticated() {
  return request.auth != null;
}

function isOwner(userId) {
  return isAuthenticated() && request.auth.uid == userId;
}

function hasCorrectUserId() {
  return isAuthenticated() && 
         request.resource.data.userId == request.auth.uid;
}

function canAccessDocument() {
  return isAuthenticated() && 
         resource.data.userId == request.auth.uid;
}
```

### 3.2 Regras por Coleção

#### Users
```javascript
match /users/{userId} {
  allow read, write: if isOwner(userId);
  allow read: if isAdmin();
}
```

#### Brands
```javascript
match /brands/{brandId} {
  allow read: if canAccessDocument();
  allow create: if hasCorrectUserId();
  allow update: if canAccessDocument() && userIdNotChanged();
  allow delete: if canAccessDocument();
}
```

#### Campaigns
```javascript
match /campaigns/{campaignId} {
  allow read: if canAccessDocument();
  allow create: if hasCorrectUserId() && 
                brandBelongsToUser(request.resource.data.brandId);
  allow update: if canAccessDocument() && userIdNotChanged();
  allow delete: if canAccessDocument();
}
```

### 3.3 Validação de Relacionamentos
```javascript
function brandBelongsToUser(brandId) {
  return exists(/databases/$(database)/documents/brands/$(brandId)) &&
         get(/databases/$(database)/documents/brands/$(brandId)).data.userId == request.auth.uid;
}
```

## 📊 Deliverables

- [x] Arquivo firestore.rules completo
- [x] Testes de segurança
- [x] Scripts de deploy
- [x] Documentação de troubleshooting

## ✅ Critérios de Aceitação

- [ ] Isolamento 100% por usuário
- [ ] Validação de relacionamentos funcionando
- [ ] Testes de segurança passando
- [ ] Performance adequada

## 🚀 Próxima Fase

Seguir para [Fase 4: Middleware e APIs](./fase-4-middleware-apis.md) 