import React, { useEffect, useState } from "react";
import { PlusCircle, Save, X, Trash, Edit } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export interface BrandServicePrice {
  name?: string;
  price?: number;
}

interface BrandPrices {
  instagramStory?: BrandServicePrice;
  instagramReel?: BrandServicePrice;
  tiktokVideo?: BrandServicePrice;
  youtubeInsertion?: BrandServicePrice;
  youtubeDedicated?: BrandServicePrice;
  youtubeShorts?: BrandServicePrice;
}

interface Brand {
  id: string;
  name: string;
  prices: BrandPrices;
  createdAt?: Date;
}

interface BrandsManagerProps {
  influencerId?: string;
  defaultPrices?: BrandPrices; // preços padrão do influenciador para preencher formulário
  readOnly?: boolean;
}

const emptyPrices: BrandPrices = {
  instagramStory: { name: "Stories", price: 0 },
  instagramReel: { name: "Reels", price: 0 },
  tiktokVideo: { name: "Vídeo", price: 0 },
  youtubeInsertion: { name: "Inserção", price: 0 },
  youtubeDedicated: { name: "Dedicado", price: 0 },
  youtubeShorts: { name: "Shorts", price: 0 },
};

const BrandsManager: React.FC<BrandsManagerProps> = ({ influencerId, defaultPrices = {}, readOnly = false }) => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);

  const [brandName, setBrandName] = useState("");
  const [prices, setPrices] = useState<BrandPrices>({
    instagramStory: { name: "Stories", price: 0 },
    instagramReel: { name: "Reels", price: 0 },
    tiktokVideo: { name: "Vídeo", price: 0 },
    youtubeInsertion: { name: "Inserção", price: 0 },
    youtubeDedicated: { name: "Dedicado", price: 0 },
    youtubeShorts: { name: "Shorts", price: 0 },
  });

  // Atualizar preços default quando prop mudar (ex.: após carregar financialInfo)
  useEffect(() => {
    if (!editingBrandId && defaultPrices) {
      setPrices({
        instagramStory: { name: "Stories", price: defaultPrices.instagramStory?.price || 0 },
        instagramReel: { name: "Reels", price: defaultPrices.instagramReel?.price || 0 },
        tiktokVideo: { name: "Vídeo", price: defaultPrices.tiktokVideo?.price || 0 },
        youtubeInsertion: { name: "Inserção", price: defaultPrices.youtubeInsertion?.price || 0 },
        youtubeDedicated: { name: "Dedicado", price: defaultPrices.youtubeDedicated?.price || 0 },
        youtubeShorts: { name: "Shorts", price: defaultPrices.youtubeShorts?.price || 0 },
      });
    }
  }, [defaultPrices, editingBrandId]);

  /** 🔥 CORREÇÃO: Carrega marcas com cache e debounce */
  const fetchBrands = async () => {
    if (!influencerId) {
      setBrands([]);
      return;
    }
    
    // Cache por influenciador
    const cacheKey = `brands_${influencerId}`;
    const cacheTimestamp = `brands_timestamp_${influencerId}`;
    const cached = sessionStorage.getItem(cacheKey);
    const timestamp = sessionStorage.getItem(cacheTimestamp);
    const now = Date.now();
    
    // Cache válido por 2 minutos
    if (cached && timestamp && (now - parseInt(timestamp)) < 120000) {
      setBrands(JSON.parse(cached));
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    try {
      const res = await fetch(`/api/brands?influencerId=${influencerId}`);
      if (res.ok) {
        const data = await res.json();
        setBrands(data);
        
        // Salvar no cache
        sessionStorage.setItem(cacheKey, JSON.stringify(data));
        sessionStorage.setItem(cacheTimestamp, now.toString());
      }
    } catch (err) {
      console.error("Erro ao carregar marcas", err);
      setBrands([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!influencerId) {
      setBrands([]);
      return;
    }
    
    // 🔥 Debounce para evitar requisições em rajada
    const timeoutId = setTimeout(() => {
    fetchBrands();
    }, 300);
    
    return () => clearTimeout(timeoutId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [influencerId]);

  /** Reinicia formulário */
  const resetForm = () => {
    setBrandName("");
    setPrices({ ...emptyPrices, ...defaultPrices });
    setEditingBrandId(null);
    setShowForm(false);
  };

  /** Salva ou atualiza marca */
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!brandName.trim()) return;

    const payload = { name: brandName.trim(), prices, influencerId };
    const url = editingBrandId ? `/api/brands?id=${editingBrandId}` : "/api/brands";
    const method = editingBrandId ? "PUT" : "POST";

    try {
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (res.ok) {
        await fetchBrands();
        resetForm();
      }
    } catch (err) {
      console.error("Erro ao salvar marca", err);
    }
  };

  /** Exclui marca */
  const handleDelete = async (id: string) => {
    if (!confirm("Deseja excluir esta marca?")) return;
    try {
      const res = await fetch(`/api/brands?id=${id}&influencerId=${influencerId}`, { method: "DELETE" });
      if (res.ok) fetchBrands();
    } catch (err) {
      console.error("Erro ao excluir marca", err);
    }
  };

  /** Preenche formulário para edição */
  const startEdit = (brand: Brand) => {
    setEditingBrandId(brand.id);
    setBrandName(brand.name);
    setPrices({
      instagramStory: { name: brand.prices?.instagramStory?.name || "Stories", price: brand.prices?.instagramStory?.price || 0 },
      instagramReel: { name: brand.prices?.instagramReel?.name || "Reels", price: brand.prices?.instagramReel?.price || 0 },
      tiktokVideo: { name: brand.prices?.tiktokVideo?.name || "Vídeo", price: brand.prices?.tiktokVideo?.price || 0 },
      youtubeInsertion: { name: brand.prices?.youtubeInsertion?.name || "Inserção", price: brand.prices?.youtubeInsertion?.price || 0 },
      youtubeDedicated: { name: brand.prices?.youtubeDedicated?.name || "Dedicado", price: brand.prices?.youtubeDedicated?.price || 0 },
      youtubeShorts: { name: brand.prices?.youtubeShorts?.name || "Shorts", price: brand.prices?.youtubeShorts?.price || 0 },
    });
    setShowForm(true);
  };

  /** Atualiza campo de preços */
  const updatePrice = (field: keyof BrandPrices, value: string) => {
    setPrices(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        price: Number(value) || 0
      }
    }));
  };

  return (
    <div className="space-y-2">
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <img 
              src="/loader-dm.webp" 
              alt="Carregando marcas..."
              className="w-16 h-16 object-contain"
            />
        </div>
      ) : brands.length === 0 ? (
        <p className="text-xs text-muted-foreground">Nenhuma marca cadastrada.</p>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Marca</TableHead>
              <TableHead>Story</TableHead>
              <TableHead>Reel</TableHead>
              <TableHead>TikTok</TableHead>
              <TableHead>YouTube Insc.</TableHead>
              <TableHead>Dedicated</TableHead>
              <TableHead>Shorts</TableHead>
              {!readOnly && <TableHead className="w-px" />}
            </TableRow>
          </TableHeader>
          <TableBody>
            {brands.map(brand => {
              const p: BrandPrices = typeof (brand as any).prices === "string"
                ? JSON.parse((brand as any).prices || "{}")
                : brand.prices || {};
              return (
                <TableRow key={brand.id} className="text-xs">
                  <TableCell className="font-medium">{brand.name}</TableCell>
                  <TableCell>R$ {p.instagramStory?.price ?? 0}</TableCell>
                  <TableCell>R$ {p.instagramReel?.price ?? 0}</TableCell>
                  <TableCell>R$ {p.tiktokVideo?.price ?? 0}</TableCell>
                  <TableCell>R$ {p.youtubeInsertion?.price ?? 0}</TableCell>
                  <TableCell>R$ {p.youtubeDedicated?.price ?? 0}</TableCell>
                  <TableCell>R$ {p.youtubeShorts?.price ?? 0}</TableCell>
                  {!readOnly && (
                    <TableCell className="flex gap-2">
                      <Button variant="ghost" size="icon" onClick={() => startEdit(brand)}>
                        <Edit size={14} />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDelete(brand.id)}>
                        <Trash size={14} />
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}

      {!readOnly && (
        showForm ? (
          <form onSubmit={handleSave} className="space-y-2 border rounded-lg p-4 bg-muted/10">
            <div className="flex items-center gap-2">
              <Input
                placeholder="Nome da marca"
                value={brandName}
                onChange={e => setBrandName(e.target.value)}
                className="flex-1"
              />
              <Button type="button" variant="ghost" onClick={resetForm}>
                <X size={16} />
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
              <Input
                type="number"
                placeholder="Story Insta"
                value={prices.instagramStory?.price || 0}
                onChange={e => updatePrice("instagramStory", e.target.value)}
              />
              <Input
                type="number"
                placeholder="Reel Insta"
                value={prices.instagramReel?.price || 0}
                onChange={e => updatePrice("instagramReel", e.target.value)}
              />
              <Input
                type="number"
                placeholder="Vídeo TikTok"
                value={prices.tiktokVideo?.price || 0}
                onChange={e => updatePrice("tiktokVideo", e.target.value)}
              />
              <Input
                type="number"
                placeholder="Inserção YouTube"
                value={prices.youtubeInsertion?.price || 0}
                onChange={e => updatePrice("youtubeInsertion", e.target.value)}
              />
              <Input
                type="number"
                placeholder="Vídeo longo YouTube"
                value={prices.youtubeDedicated?.price || 0}
                onChange={e => updatePrice("youtubeDedicated", e.target.value)}
              />
              <Input
                type="number"
                placeholder="Shorts YouTube"
                value={prices.youtubeShorts?.price || 0}
                onChange={e => updatePrice("youtubeShorts", e.target.value)}
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button type="submit" className="gap-1">
                <Save size={14} /> {editingBrandId ? "Salvar" : "Adicionar"}
              </Button>
            </div>
          </form>
        ) : (
          <Button
            type="button"
            className="gap-1"
            onClick={() => {
              resetForm();
              setShowForm(true);
            }}
          >
            <PlusCircle size={16} /> Nova marca
          </Button>
        )
      )}
    </div>
  );
};

export default BrandsManager;


