"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { FormattedInput } from "@/components/ui/formatted-input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DeliveryType, SocialNetwork } from "@/types/brand-dashboard"

const socialNetworks: SocialNetwork[] = [
  "instagram",
  "youtube",
  "tiktok",
  "twitter",
  "facebook",
]

const deliveryTypes: DeliveryType[] = [
  "stories",
  "shorts",
  "long_video",
  "fixed_post",
  "tiktok",
]

const formSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  socialNetworks: z.array(
    z.object({
      network: z.enum(["instagram", "youtube", "tiktok", "twitter", "facebook"]),
      username: z.string().min(1, "Username é obrigatório"),
      isPrimary: z.boolean().default(false),
    })
  ).min(1, "Adicione pelo menos uma rede social"),
  services: z.array(
    z.object({
      type: z.enum(["stories", "shorts", "long_video", "fixed_post", "tiktok"]),
      price: z.number().min(0, "Preço deve ser maior que zero"),
      quantity: z.number().min(1, "Quantidade deve ser pelo menos 1"),
    })
  ).min(1, "Adicione pelo menos um serviço"),
  campaignMonth: z.string().min(1, "Mês da campanha é obrigatório"),
})

type FormValues = z.infer<typeof formSchema>

interface AddInfluencerFormProps {
  onSubmit: (data: FormValues) => void
  onCancel: () => void
}

export function AddInfluencerForm({ onSubmit, onCancel }: AddInfluencerFormProps) {
  const [socialNetworkCount, setSocialNetworkCount] = useState(1)
  const [serviceCount, setServiceCount] = useState(1)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      socialNetworks: [{ network: "instagram", username: "", isPrimary: true }],
      services: [{ type: "stories", price: 0, quantity: 1 }],
      campaignMonth: "",
    },
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome do Influenciador</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Redes Sociais</h3>
            <Button
              type="button"
              variant="outline"
              onClick={() => setSocialNetworkCount((prev) => prev + 1)}
            >
              Adicionar Rede Social
            </Button>
          </div>
          {Array.from({ length: socialNetworkCount }).map((_, index) => (
            <div key={index} className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name={`socialNetworks.${index}.network`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rede Social</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {socialNetworks.map((network) => (
                          <SelectItem key={network} value={network}>
                            {network.charAt(0).toUpperCase() + network.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name={`socialNetworks.${index}.username`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name={`socialNetworks.${index}.isPrimary`}
                render={({ field }) => (
                  <FormItem className="flex items-end space-x-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel>Principal</FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Serviços</h3>
            <Button
              type="button"
              variant="outline"
              onClick={() => setServiceCount((prev) => prev + 1)}
            >
              Adicionar Serviço
            </Button>
          </div>
          {Array.from({ length: serviceCount }).map((_, index) => (
            <div key={index} className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name={`services.${index}.type`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {deliveryTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name={`services.${index}.price`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preço</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-sm text-gray-500 z-10">
                          R$
                        </span>
                        <FormattedInput
                          formatType="currency"
                          value={field.value || ''}
                          onChange={(formattedValue, numericValue) => {
                            field.onChange(numericValue)
                          }}
                          placeholder="0,00"
                          className="pl-8"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name={`services.${index}.quantity`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantidade</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value, 10))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
        </div>

        <FormField
          control={form.control}
          name="campaignMonth"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mês da Campanha</FormLabel>
              <FormControl>
                <Input type="month" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit">Salvar</Button>
        </div>
      </form>
    </Form>
  )
} 

