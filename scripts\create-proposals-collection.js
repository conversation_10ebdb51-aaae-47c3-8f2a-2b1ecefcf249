const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, getDocs } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: "AIzaSyDhSkFQHdDLke6drWlVJzE3Zys6CJx_arQ",
  authDomain: "deumatch-demo.firebaseapp.com",
  projectId: "deumatch-demo",
  storageBucket: "deumatch-demo.firebasestorage.app",
  messagingSenderId: "306980252445",
  appId: "1:306980252445:web:e6e275cde8b9b9668e0ff4"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function createSampleProposals() {
  try {
    console.log('Criando propostas de exemplo...');
    
    const sampleProposals = [
      {
        brandId: 'nike-brasil',
        brandName: 'Nike Brasil',
        influencerId: 'maria-fitness',
        influencerName: '<PERSON>',
        influencerInstagram: '@maria_fitness',
        campaignTitle: 'Lançamento Nike Air Max 2024',
        description: 'Campanha para divulgação do novo tênis Nike Air Max com foco em lifestyle fitness',
        deliverables: [
          '3 posts no feed do Instagram',
          '5 stories promocionais',
          '1 reel de unboxing'
        ],
        budget: 15000,
        currency: 'BRL',
        status: 'pending',
        proposalDate: new Date('2024-01-15'),
        deadline: new Date('2024-02-15'),
        category: 'Fashion & Lifestyle',
        reach: 250000,
        engagement: 4.2,
        demographics: {
          ageRange: '18-35',
          gender: '60% feminino, 40% masculino',
          location: 'Brasil - SP, RJ, MG'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        brandId: 'coca-cola-br',
        brandName: 'Coca-Cola Brasil',
        influencerId: 'joao-lifestyle',
        influencerName: 'João Santos',
        influencerInstagram: '@joao_lifestyle',
        campaignTitle: 'Verão Coca-Cola 2024',
        description: 'Campanha de verão focada em momentos de diversão e refrescância',
        deliverables: [
          '2 posts no feed',
          '3 stories',
          '1 vídeo IGTV de 60 segundos'
        ],
        budget: 25000,
        currency: 'BRL',
        status: 'approved',
        proposalDate: new Date('2024-01-10'),
        deadline: new Date('2024-02-28'),
        category: 'Food & Beverage',
        reach: 500000,
        engagement: 3.8,
        demographics: {
          ageRange: '16-45',
          gender: '55% masculino, 45% feminino',
          location: 'Brasil - Nacional'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        brandId: 'samsung-brasil',
        brandName: 'Samsung Brasil',
        influencerId: 'tech-ana',
        influencerName: 'Ana Tecnologia',
        influencerInstagram: '@tech_ana',
        campaignTitle: 'Galaxy S24 - Revolução da Fotografia',
        description: 'Demonstração das capacidades fotográficas do novo Galaxy S24',
        deliverables: [
          '4 posts no feed com fotos tiradas pelo Galaxy S24',
          '8 stories mostrando recursos',
          '2 reels comparativos'
        ],
        budget: 35000,
        currency: 'BRL',
        status: 'negotiating',
        proposalDate: new Date('2024-01-20'),
        deadline: new Date('2024-03-15'),
        category: 'Technology',
        reach: 180000,
        engagement: 5.1,
        demographics: {
          ageRange: '20-40',
          gender: '45% feminino, 55% masculino',
          location: 'Brasil - Capitais'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        brandId: 'natura-cosmeticos',
        brandName: 'Natura Cosméticos',
        influencerId: 'beleza-carol',
        influencerName: 'Carolina Beauty',
        influencerInstagram: '@beleza_carol',
        campaignTitle: 'Linha Natura Chronos - Cuidados Anti-idade',
        description: 'Divulgação da nova linha anti-idade com foco em sustentabilidade',
        deliverables: [
          '3 posts no feed',
          '6 stories com tutorial',
          '1 live sobre skincare'
        ],
        budget: 20000,
        currency: 'BRL',
        status: 'pending',
        proposalDate: new Date('2024-01-25'),
        deadline: new Date('2024-03-01'),
        category: 'Beauty & Cosmetics',
        reach: 320000,
        engagement: 4.7,
        demographics: {
          ageRange: '25-50',
          gender: '85% feminino, 15% masculino',
          location: 'Brasil - SP, RJ, RS'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        brandId: 'ifood-delivery',
        brandName: 'iFood',
        influencerId: 'chef-rodrigo',
        influencerName: 'Chef Rodrigo',
        influencerInstagram: '@chef_rodrigo',
        campaignTitle: 'iFood Gourmet - Experiências Culinárias',
        description: 'Promoção de restaurantes gourmet disponíveis no iFood',
        deliverables: [
          '5 posts no feed com pratos diferentes',
          '10 stories de unboxing',
          '3 reels de degustação'
        ],
        budget: 18000,
        currency: 'BRL',
        status: 'rejected',
        proposalDate: new Date('2024-01-12'),
        deadline: new Date('2024-02-20'),
        category: 'Food & Delivery',
        reach: 150000,
        engagement: 6.2,
        demographics: {
          ageRange: '22-45',
          gender: '50% feminino, 50% masculino',
          location: 'Brasil - Grandes centros urbanos'
        },
        rejectionReason: 'Conflito com campanha concorrente',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    console.log(`Adicionando ${sampleProposals.length} propostas de exemplo...`);
    
    for (const proposal of sampleProposals) {
      const docRef = await addDoc(collection(db, 'proposals'), proposal);
      console.log(`✅ Proposta criada: ${proposal.campaignTitle} (ID: ${docRef.id})`);
    }
    
    console.log('\n🎉 Todas as propostas de exemplo foram criadas com sucesso!');
    
    // Verificar total de documentos
    const proposalsRef = collection(db, 'proposals');
    const snapshot = await getDocs(proposalsRef);
    console.log(`📊 Total de propostas na coleção: ${snapshot.size}`);
    
  } catch (error) {
    console.error('❌ Erro ao criar propostas de exemplo:', error);
    
    if (error.code === 'permission-denied') {
      console.error('🔒 Erro de permissão. Verifique as regras do Firestore.');
    } else if (error.code === 'unavailable') {
      console.error('🌐 Firestore indisponível. Verifique a conexão com a internet.');
    }
  }
}

createSampleProposals();
