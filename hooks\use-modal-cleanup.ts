"use client"

import { useEffect, useCallback } from 'react';

/**
 * Hook personalizado para gerenciar a limpeza adequada de modais
 * Previne overlays órfãos e camadas invisíveis que bloqueiam interações
 */
export function useModalCleanup() {
  
  /**
   * Função para limpar overlays órfãos e restaurar interações
   */
  const cleanupModalOverlays = useCallback(() => {
    // Limpar estilos do body que podem estar bloqueando interações
    document.body.style.pointerEvents = '';
    document.body.style.overflow = '';
    document.body.classList.remove('overflow-hidden');
    
    // Remover overlays do Radix Dialog que não estão mais ativos
    const dialogOverlays = document.querySelectorAll('[data-radix-dialog-overlay]');
    dialogOverlays.forEach(overlay => {
      const state = overlay.getAttribute('data-state');
      const parentContent = overlay.parentElement?.querySelector('[data-radix-dialog-content]');
      
      // Se o overlay não está aberto ou não tem conteúdo ativo, remover
      if (state !== 'open' || !parentContent || parentContent.getAttribute('data-state') !== 'open') {
        overlay.remove();
      }
    });
    
    // Remover portais vazios ou inativos
    const portals = document.querySelectorAll('[data-radix-portal]');
    portals.forEach(portal => {
      const hasActiveContent = portal.querySelector('[data-state="open"]');
      if (!hasActiveContent || !portal.hasChildNodes()) {
        portal.remove();
      }
    });
    
    // Remover elementos de popper órfãos
    const popperWrappers = document.querySelectorAll('[data-radix-popper-content-wrapper]');
    popperWrappers.forEach(wrapper => {
      if (!wrapper.querySelector('[data-state="open"]')) {
        wrapper.remove();
      }
    });
    
    // Forçar reflow para garantir que as mudanças sejam aplicadas
    document.body.offsetHeight;
  }, []);

  /**
   * Função para executar limpeza com delay para aguardar animações
   */
  const cleanupWithDelay = useCallback((delay: number = 350) => {
    setTimeout(cleanupModalOverlays, delay);
  }, [cleanupModalOverlays]);

  /**
   * Função para ser chamada quando um modal é fechado
   */
  const onModalClose = useCallback(() => {
    cleanupWithDelay();
  }, [cleanupWithDelay]);

  /**
   * Limpeza automática quando o componente é desmontado
   */
  useEffect(() => {
    return () => {
      cleanupModalOverlays();
    };
  }, [cleanupModalOverlays]);

  /**
   * Limpeza periódica para prevenir acúmulo de elementos órfãos
   */
  useEffect(() => {
    const interval = setInterval(() => {
      // Verificar se há overlays órfãos e limpar
      const orphanOverlays = document.querySelectorAll('[data-radix-dialog-overlay]:not([data-state="open"])');
      if (orphanOverlays.length > 0) {
        cleanupModalOverlays();
      }
    }, 5000); // Verificar a cada 5 segundos

    return () => clearInterval(interval);
  }, [cleanupModalOverlays]);

  return {
    cleanupModalOverlays,
    cleanupWithDelay,
    onModalClose
  };
}
