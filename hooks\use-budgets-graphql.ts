/**
 * 🏦 HOOKS GRAPHQL PARA ORÇAMENTOS COM CACHE OTIMIZADO
 * 
 * Hooks personalizados para buscar orçamentos usando GraphQL
 * com configuração de cache Apollo otimizada
 */

import { useQuery, gql } from '@apollo/client';

// ===== QUERIES GRAPHQL =====

// 📋 Orçamentos de propostas específicas
export const GET_PROPOSAL_BUDGETS = gql`
  query GetProposalBudgets($proposalId: ID!, $userId: ID!) {
    proposalBudgets(proposalId: $proposalId, userId: $userId) {
      proposalId
      permissions {
        canView
        canEdit
        canCreateCounterProposal
        canManageBudgets
        canApproveCounterProposals
        canViewFinancialData
      }
      budgets {
        id
        influencerId
        brandId
        amount
        currency
        status
        serviceType
        notes
        createdAt
        updatedAt
        services {
          id
          description
          amount
          quantity
        }
      }
      totalCount
      userRole
      processingTimeMs
    }
  }
`;

// 👤 Orçamentos de influenciador específico em proposta
export const GET_INFLUENCER_BUDGETS_IN_PROPOSAL = gql`
  query GetInfluencerBudgetsInProposal($proposalId: ID!, $influencerId: ID!, $userId: ID!) {
    influencerBudgetsInProposal(proposalId: $proposalId, influencerId: $influencerId, userId: $userId) {
      proposalId
      permissions {
        canView
        canEdit
        canCreateCounterProposal
        canManageBudgets
        canApproveCounterProposals
        canViewFinancialData
      }
      budgets {
        id
        influencerId
        brandId
        amount
        currency
        status
        serviceType
        notes
        createdAt
        updatedAt
        counterProposals {
          id
          proposedAmount
          originalAmount
          currency
          notes
          proposedBy
          proposedAt
          status
          type
          quantity
        }
        services {
          id
          description
          amount
          quantity
        }
      }
      documents {
        id
        name
        url
        type
        size
        uploadedAt
        uploadedBy
        proposalId
        influencerId
      }
      totalCount
      userRole
      processingTimeMs
    }
  }
`;

// 💰 Orçamentos independentes (não vinculados a propostas)
export const GET_INDEPENDENT_BUDGETS = gql`
  query GetIndependentBudgets($userId: ID!, $brandId: ID, $status: BudgetStatus, $serviceType: ServiceType) {
    independentBudgets(userId: $userId, brandId: $brandId, status: $status, serviceType: $serviceType) {
      id
      influencerId
      brandId
      amount
      currency
      status
      serviceType
      notes
      createdAt
      updatedAt
      proposalId
      counterProposals {
        id
        proposedAmount
        originalAmount
        currency
        notes
        proposedBy
        proposedAt
        status
        type
        quantity
      }
      services {
        id
        description
        amount
        quantity
      }
    }
  }
`;

// 💼 Contrapropostas de colaboradores
export const GET_COLLABORATOR_COUNTER_PROPOSALS = gql`
  query GetCollaboratorCounterProposals($proposalId: ID!, $userId: ID!, $status: CollaboratorCounterProposalStatus) {
    collaboratorCounterProposals(proposalId: $proposalId, userId: $userId, status: $status) {
      id
      budgetId
      proposalId
      influencerId
      originalAmount
      proposedAmount
      currency
      notes
      serviceType
      proposedBy {
        userId
        userName
        userEmail
        collaboratorRole
      }
      status
      createdAt
      updatedAt
      reviewedAt
      reviewedBy
      reviewNote
    }
  }
`;

// ===== HOOKS PERSONALIZADOS =====

/**
 * Hook para buscar orçamentos de uma proposta específica
 */
export function useProposalBudgets(proposalId: string, userId: string, options?: {
  skip?: boolean;
  pollInterval?: number;
}) {
  return useQuery(GET_PROPOSAL_BUDGETS, {
    variables: { proposalId, userId },
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    skip: !proposalId || !userId || options?.skip,
    pollInterval: options?.pollInterval,
  });
}

/**
 * Hook para buscar orçamentos de um influenciador específico em uma proposta
 */
export function useInfluencerBudgetsInProposal(
  proposalId: string, 
  influencerId: string, 
  userId: string,
  options?: {
    skip?: boolean;
    pollInterval?: number;
  }
) {
  return useQuery(GET_INFLUENCER_BUDGETS_IN_PROPOSAL, {
    variables: { proposalId, influencerId, userId },
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    skip: !proposalId || !influencerId || !userId || options?.skip,
    pollInterval: options?.pollInterval,
  });
}

/**
 * Hook para buscar orçamentos independentes (não vinculados a propostas)
 */
export function useIndependentBudgets(
  userId: string,
  filters?: {
    brandId?: string;
    status?: string;
    serviceType?: string;
  },
  options?: {
    skip?: boolean;
    pollInterval?: number;
  }
) {
  return useQuery(GET_INDEPENDENT_BUDGETS, {
    variables: { 
      userId, 
      brandId: filters?.brandId,
      status: filters?.status,
      serviceType: filters?.serviceType
    },
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    skip: !userId || options?.skip,
    pollInterval: options?.pollInterval,
  });
}

/**
 * Hook para buscar contrapropostas de colaboradores
 */
export function useCollaboratorCounterProposals(
  proposalId: string,
  userId: string,
  status?: string,
  options?: {
    skip?: boolean;
    pollInterval?: number;
  }
) {
  return useQuery(GET_COLLABORATOR_COUNTER_PROPOSALS, {
    variables: { proposalId, userId, status },
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    skip: !proposalId || !userId || options?.skip,
    pollInterval: options?.pollInterval,
  });
}

// ===== INTERFACES TYPESCRIPT =====

export interface BudgetPermissions {
  canView: boolean;
  canEdit: boolean;
  canCreateCounterProposal: boolean;
  canManageBudgets: boolean;
  canApproveCounterProposals: boolean;
  canViewFinancialData: boolean;
}

// 📊 Interface para resultado de orçamentos de proposta
export interface ProposalBudgetsResult {
  proposalId: string;
  permissions: {
    canView: boolean;
    canEdit: boolean;
    canCreateCounterProposal: boolean;
    canManageBudgets: boolean;
    canApproveCounterProposals: boolean;
    canViewFinancialData: boolean;
  };
  budgets: Budget[];
  documents?: Document[]; // 🆕 NOVO: Documentos do influenciador na proposta
  totalCount: number;
  userRole: string;
  processingTimeMs: number;
}

export interface Budget {
  id: string;
  influencerId: string;
  brandId: string;
  amount: number;
  currency: string;
  status: string;
  serviceType: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  proposalId?: string;
  services: BudgetService[];
}

export interface BudgetService {
  id: string;
  description: string;
  amount: number;
  quantity: number;
}

export interface CollaboratorCounterProposal {
  id: string;
  budgetId: string;
  proposalId: string;
  influencerId: string;
  originalAmount: number;
  proposedAmount: number;
  currency: string;
  notes?: string;
  serviceType: string;
  proposedBy: {
    userId: string;
    userName: string;
    userEmail: string;
    collaboratorRole: string;
  };
  status: string;
  createdAt: string;
  updatedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNote?: string;
} 