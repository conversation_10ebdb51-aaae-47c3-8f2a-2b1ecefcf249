'use client';

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Proposal, ProposalFilters, ProposalStats, ProposalAnalytics } from '@/types/proposal';

interface UseProposalsOptions {
  initialProposals?: Proposal[];
  initialStats?: ProposalStats;
  initialAnalytics?: ProposalAnalytics;
  autoFetch?: boolean;
}

interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

interface UseProposalsReturn {
  // Data
  proposals: Proposal[];
  stats: ProposalStats | null;
  analytics: ProposalAnalytics | null;
  pagination: PaginationInfo;
  
  // State
  loading: boolean;
  refreshing: boolean;
  error: string | null;
  
  // Filters and sorting
  filters: ProposalFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  
  // Actions
  fetchProposals: (newFilters?: ProposalFilters, reset?: boolean) => Promise<void>;
  setFilters: (filters: ProposalFilters) => void;
  setSorting: (field: string, order: 'asc' | 'desc') => void;
  loadMore: () => void;
  refresh: () => Promise<void>;
  createProposal: (data: any) => Promise<string | null>;
  updateProposal: (id: string, data: any) => Promise<boolean>;
  deleteProposal: (id: string) => Promise<boolean>;
  bulkDelete: (ids: string[]) => Promise<boolean>;
  exportProposals: (format?: 'csv' | 'xlsx') => Promise<void>;
}

export const useProposals = ({
  initialProposals = [],
  initialStats,
  initialAnalytics,
  autoFetch = true
}: UseProposalsOptions = {}): UseProposalsReturn => {
  // Estados principais
  const [proposals, setProposals] = useState<Proposal[]>(initialProposals);
  const [stats, setStats] = useState<ProposalStats | null>(initialStats || null);
  const [analytics, setAnalytics] = useState<ProposalAnalytics | null>(initialAnalytics || null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Estados de filtros e ordenação
  const [filters, setFiltersState] = useState<ProposalFilters>({});
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Estados de paginação
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: initialProposals.length,
    limit: 20,
    offset: 0,
    hasMore: false
  });
  
  // Função principal para buscar propostas
  const fetchProposals = useCallback(async (newFilters?: ProposalFilters, reset = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const currentOffset = reset ? 0 : pagination.offset;
      const filtersToUse = newFilters || filters;
      
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: currentOffset.toString(),
        sortBy,
        sortOrder,
        includeAnalytics: 'true'
      });
      
      // Adicionar filtros aos parâmetros
      Object.entries(filtersToUse).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v.toString()));
          } else if (typeof value === 'object') {
            // Para objetos como dateRange
            Object.entries(value).forEach(([subKey, subValue]) => {
              if (subValue) {
                params.append(`${key}.${subKey}`, subValue.toString());
              }
            });
          } else {
            params.append(key, value.toString());
          }
        }
      });
      
      const response = await fetch(`/api/propostas?${params}`);
      const data = await response.json();
      
      if (data.success) {
        if (reset) {
          setProposals(data.propostas);
        } else {
          setProposals(prev => [...prev, ...data.propostas]);
        }
        
        setStats(data.stats);
        setAnalytics(data.analytics);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || 'Erro ao carregar propostas');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.limit, pagination.offset, sortBy, sortOrder]);
  
  // Função para atualizar filtros
  const setFilters = useCallback((newFilters: ProposalFilters) => {
    setFiltersState(newFilters);
    setPagination(prev => ({ ...prev, offset: 0 }));
  }, []);
  
  // Função para atualizar ordenação
  const setSorting = useCallback((field: string, order: 'asc' | 'desc') => {
    setSortBy(field);
    setSortOrder(order);
    setPagination(prev => ({ ...prev, offset: 0 }));
  }, []);
  
  // Função para carregar mais propostas
  const loadMore = useCallback(() => {
    if (pagination.hasMore && !loading) {
      setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }));
    }
  }, [pagination.hasMore, loading]);
  
  // Função para atualizar dados
  const refresh = useCallback(async () => {
    setRefreshing(true);
    setPagination(prev => ({ ...prev, offset: 0 }));
    await fetchProposals(filters, true);
    setRefreshing(false);
    toast.success('Dados atualizados');
  }, [fetchProposals, filters]);
  
  // Função para criar proposta
  const createProposal = useCallback(async (data: any): Promise<string | null> => {
    try {
      const response = await fetch('/api/propostas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success('Proposta criada com sucesso');
        await refresh();
        return result.proposalId;
      } else {
        throw new Error(result.error || 'Erro ao criar proposta');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar proposta';
      toast.error(errorMessage);
      return null;
    }
  }, [refresh]);
  
  // Função para atualizar proposta
  const updateProposal = useCallback(async (id: string, data: any): Promise<boolean> => {
    try {
      const response = await fetch(`/api/propostas/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success('Proposta atualizada com sucesso');
        
        // Atualizar proposta na lista local
        setProposals(prev => 
          prev.map(p => p.id === id ? { ...p, ...data } : p)
        );
        
        return true;
      } else {
        throw new Error(result.error || 'Erro ao atualizar proposta');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar proposta';
      toast.error(errorMessage);
      return false;
    }
  }, []);
  
  // Função para deletar proposta
  const deleteProposal = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/propostas/${id}`, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success('Proposta excluída com sucesso');
        
        // Remover proposta da lista local
        setProposals(prev => prev.filter(p => p.id !== id));
        
        // Atualizar estatísticas
        if (stats) {
          setStats(prev => prev ? { ...prev, total: prev.total - 1 } : null);
        }
        
        return true;
      } else {
        throw new Error(result.error || 'Erro ao excluir proposta');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao excluir proposta';
      toast.error(errorMessage);
      return false;
    }
  }, [stats]);
  
  // Função para deletar múltiplas propostas
  const bulkDelete = useCallback(async (ids: string[]): Promise<boolean> => {
    try {
      const response = await fetch('/api/propostas/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(`${ids.length} proposta${ids.length > 1 ? 's' : ''} excluída${ids.length > 1 ? 's' : ''} com sucesso`);
        
        // Remover propostas da lista local
        setProposals(prev => prev.filter(p => !ids.includes(p.id)));
        
        // Atualizar estatísticas
        if (stats) {
          setStats(prev => prev ? { ...prev, total: prev.total - ids.length } : null);
        }
        
        return true;
      } else {
        throw new Error(result.error || 'Erro ao excluir propostas');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao excluir propostas';
      toast.error(errorMessage);
      return false;
    }
  }, [stats]);
  
  // Função para exportar propostas
  const exportProposals = useCallback(async (format: 'csv' | 'xlsx' = 'csv') => {
    try {
      const params = new URLSearchParams({
        export: 'true',
        format
      });
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
      
      const response = await fetch(`/api/propostas/export?${params}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `propostas-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Dados exportados com sucesso');
      } else {
        throw new Error('Erro ao exportar dados');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao exportar dados';
      toast.error(errorMessage);
    }
  }, [filters]);
  
  // Efeito para buscar dados iniciais
  useEffect(() => {
    if (autoFetch && !initialProposals.length) {
      fetchProposals(filters, true);
    }
  }, []);
  
  // Efeito para carregar mais quando offset muda
  useEffect(() => {
    if (pagination.offset > 0) {
      fetchProposals();
    }
  }, [pagination.offset]);
  
  // Efeito para atualizar quando filtros mudam
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      fetchProposals(filters, true);
    }
  }, [filters]);
  
  // Efeito para atualizar quando ordenação muda
  useEffect(() => {
    if (sortBy && sortOrder) {
      fetchProposals(filters, true);
    }
  }, [sortBy, sortOrder]);
  
  return {
    // Data
    proposals,
    stats,
    analytics,
    pagination,
    
    // State
    loading,
    refreshing,
    error,
    
    // Filters and sorting
    filters,
    sortBy,
    sortOrder,
    
    // Actions
    fetchProposals,
    setFilters,
    setSorting,
    loadMore,
    refresh,
    createProposal,
    updateProposal,
    deleteProposal,
    bulkDelete,
    exportProposals
  };
};

export default useProposals;

