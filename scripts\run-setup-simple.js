/**
 * Script simples para executar a configuração das coleções do Firebase
 * Execute com: node scripts/run-setup-simple.js
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, doc, setDoc, addDoc } = require('firebase/firestore');

// Configuração do Firebase
const firebaseConfig = {
  apiKey: "AIzaSyBKOGnF8Uy8Uy8Uy8Uy8Uy8Uy8Uy8Uy8U",
  authDomain: "deumatch-demo.firebaseapp.com",
  projectId: "deumatch-demo",
  storageBucket: "deumatch-demo.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdefghijklmnop"
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Dados básicos para criar
const basicCategories = [
  {
    id: 'lifestyle',
    name: 'Lifestyle',
    slug: 'lifestyle',
    description: 'Conteúdo sobre estilo de vida, rotina e bem-estar',
    color: '#FF6B6B',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'fashion',
    name: 'Moda',
    slug: 'fashion',
    description: 'Conteúdo sobre moda, tendências e estilo',
    color: '#4ECDC4',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'beauty',
    name: 'Beleza',
    slug: 'beauty',
    description: 'Conteúdo sobre beleza, skincare e makeup',
    color: '#45B7D1',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'fitness',
    name: 'Fitness',
    slug: 'fitness',
    description: 'Conteúdo sobre exercícios, saúde e bem-estar físico',
    color: '#96CEB4',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const basicBrands = [
  {
    id: 'nike',
    name: 'Nike',
    slug: 'nike',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/nike-vector-logo.png',
    website: 'https://www.nike.com.br',
    category: 'fitness',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'loreal',
    name: "L'Oréal",
    slug: 'loreal',
    logo: 'https://logoeps.com/wp-content/uploads/2013/03/loreal-vector-logo.png',
    website: 'https://www.loreal.com.br',
    category: 'beauty',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Função principal
async function setupCollections() {
  console.log('🚀 Iniciando configuração das coleções...');
  
  try {
    // Criar categorias
    console.log('📂 Criando categorias...');
    for (const category of basicCategories) {
      await setDoc(doc(db, 'categories', category.id), category);
      console.log(`✅ Categoria criada: ${category.name}`);
    }
    
    // Criar marcas
    console.log('🏢 Criando marcas...');
    for (const brand of basicBrands) {
      await setDoc(doc(db, 'brands', brand.id), brand);
      console.log(`✅ Marca criada: ${brand.name}`);
    }
    
    // Criar influenciador de exemplo
    console.log('👤 Criando influenciador de exemplo...');
    const exampleInfluencer = {
      personalInfo: {
        name: 'Maria Silva',
        age: 28,
        gender: 'female',
        bio: 'Influenciadora de lifestyle e moda.',
        avatarUrl: '',
        backgroundImageUrl: '',
        verified: false,
        location: {
          city: 'São Paulo',
          state: 'SP',
          country: 'Brasil',
          cep: '01310-100',
          formattedLocation: 'São Paulo, SP, Brasil'
        }
      },
      contactInfo: {
        email: '<EMAIL>',
        whatsapp: '+5511999999999'
      },
      businessInfo: {
        agency: {
          name: 'Agência Digital',
          responsibleName: 'João Santos',
          responsibleCapturer: 'Ana Costa'
        },
        mainCategories: ['lifestyle', 'fashion'],
        promotesTraders: false,
        contentType: ['Fotos', 'Vídeos', 'Stories'],
        brandHistory: {
          instagram: ['nike', 'loreal'],
          tiktok: [],
          youtube: []
        }
      },
      socialNetworks: {
        instagram: {
          username: '@mariasilva',
          followers: 150000,
          avgViews: 25000,
          engagementRate: 4.2,
          storyPrice: 800,
          reelPrice: 1500,
          storiesViews: 45000,
          audienceGender: {
            male: 25,
            female: 70,
            other: 5
          },
          audienceAgeRanges: {
            '13-17': 15,
            '18-24': 35,
            '25-34': 30,
            '35-44': 15,
            '45-54': 5
          },
          audienceLocations: {
            'SP': 40,
            'RJ': 25,
            'MG': 15,
            'Other': 20
          }
        }
      },
      metrics: {
        totalFollowers: 150000,
        overallEngagementRate: 4.2,
        rating: 4.5
      },
      tags: ['lifestyle', 'moda', 'beleza', 'sp'],
      notes: [
        {
          id: 'note1',
          content: 'Influenciadora muito engajada, ótima para campanhas de lifestyle.',
          author: 'João Santos',
          createdAt: new Date(),
          isPrivate: false
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const influencerRef = await addDoc(collection(db, 'influencers'), exampleInfluencer);
    console.log(`✅ Influenciador criado com ID: ${influencerRef.id}`);
    
    // Criar configuração do sistema
    console.log('⚙️ Criando configuração do sistema...');
    const systemConfig = {
      version: '2.0.0',
      structureVersion: 'structured-v1',
      createdAt: new Date(),
      updatedAt: new Date(),
      features: {
        structuredData: true,
        financialSeparation: true,
        audienceAnalytics: true,
        brandHistory: true,
        multiPlatform: true
      },
      collections: {
        influencers: 'influencers',
        financials: 'influencer_financials',
        categories: 'categories',
        brands: 'brands',
        filters: 'filters',
        campaigns: 'campaigns'
      }
    };
    
    await setDoc(doc(db, 'system', 'config'), systemConfig);
    console.log('✅ Configuração do sistema criada!');
    
    console.log('\n🎉 Configuração concluída com sucesso!');
    console.log('\n📊 Resumo:');
    console.log(`   • ${basicCategories.length} categorias criadas`);
    console.log(`   • ${basicBrands.length} marcas criadas`);
    console.log(`   • 1 influenciador de exemplo criado`);
    console.log(`   • Configuração do sistema criada`);
    console.log('\n✅ O sistema está pronto para uso!');
    
    return {
      success: true,
      exampleInfluencerId: influencerRef.id,
      categoriesCount: basicCategories.length,
      brandsCount: basicBrands.length
    };
    
  } catch (error) {
    console.error('❌ Erro durante a configuração:', error);
    throw error;
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  setupCollections()
    .then(result => {
      console.log('\n✅ Script executado com sucesso!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Erro na execução:', error);
      process.exit(1);
    });
}

module.exports = { setupCollections };
