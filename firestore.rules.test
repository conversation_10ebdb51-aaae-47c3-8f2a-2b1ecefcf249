rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // ========================================
    // 🧪 REGRAS TEMPORÁRIAS PARA TESTES
    // ========================================
    
    // Função para verificar se é ambiente de teste
    function isTestEnvironment() {
      // Permitir quando não há autenticação (desenvolvimento local)
      return request.auth == null || 
             // Ou quando é o usuário de teste específico
             request.auth.uid == 'dev-user-123';
    }
    
    // Verificar se usuário está autenticado ou é teste
    function isAuthenticatedOrTest() {
      return request.auth != null || isTestEnvironment();
    }
    
    // Verificar se usuário é proprietário do documento OU é teste
    function isOwnerOrTest(userId) {
      return isTestEnvironment() || 
             (request.auth != null && request.auth.uid == userId);
    }
    
    // Verificar se pode acessar documento baseado no userId OU é teste
    function canAccessDocumentOrTest() {
      return isTestEnvironment() ||
             (request.auth != null && resource.data.userId == request.auth.uid);
    }
    
    // Verificar se userId está correto nos dados da requisição OU é teste
    function hasCorrectUserIdOrTest() {
      return isTestEnvironment() ||
             (request.auth != null && request.resource.data.userId == request.auth.uid);
    }
    
    // ========================================
    // 🔒 REGRAS TEMPORÁRIAS POR COLEÇÃO
    // ========================================
    
    // USUÁRIOS - Acesso para testes
    match /users/{userId} {
      allow read, write: if isOwnerOrTest(userId);
    }
    
    // BRANDS - Permissão ampla para testes
    match /brands/{brandId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // INFLUENCERS - Permissão ampla para testes
    match /influencers/{influencerId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // CAMPAIGNS - Permissão ampla para testes
    match /campaigns/{campaignId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // INFLUENCER FINANCIALS - Permissão ampla para testes
    match /influencer_financials/{financialId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // NOTES - Permissão ampla para testes
    match /notes/{noteId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // TAGS - Permissão ampla para testes
    match /tags/{tagId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // GROUPS - Permissão ampla para testes
    match /groups/{groupId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // PROPOSALS - Permissão ampla para testes
    match /proposals/{proposalId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // FILTERS - Permissão ampla para testes
    match /filters/{filterId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // CATEGORIES - Acesso livre para testes
    match /categories/{categoryId} {
      allow read, write: if isAuthenticatedOrTest();
    }
    
    // BRAND_INFLUENCERS - Permissão ampla para testes
    match /brand_influencers/{relationId} {
      allow read: if isAuthenticatedOrTest();
      allow write: if isAuthenticatedOrTest();
    }
    
    // ========================================
    // ⚠️ ATENÇÃO: ESTAS SÃO REGRAS DE TESTE
    // ========================================
    // Não usar em produção!
    // Reverter para firestore.rules após os testes
    
    // Permitir acesso a qualquer documento em modo de teste
    match /{document=**} {
      allow read, write: if isTestEnvironment();
    }
  }
} 