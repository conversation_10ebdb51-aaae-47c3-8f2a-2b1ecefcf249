import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser } from '@clerk/nextjs';
import { Brand, CreateBrandData, UpdateBrandData } from '@/types/brand';

/**
 * 🏢 HOOK DE BRANDS COM ISOLAMENTO AUTOMÁTICO E RATE LIMITING - CLERK VERSION
 * FASE 6.1: Hook atualizado para trabalhar com isolamento por usuário
 * CORREÇÃO: Implementado debouncing e controle de rate limiting
 * MIGRAÇÃO: Atualizado para usar Clerk Authentication
 */

interface UseBrandsResult {
  brands: Brand[];
  loading: boolean;
  error: string | null;
  createBrand: (data: CreateBrandData) => Promise<Brand>;
  updateBrand: (id: string, data: UpdateBrandData) => Promise<Brand>;
  deleteBrand: (id: string) => Promise<void>;
  refreshBrands: () => Promise<void>;
  getBrandById: (id: string) => Brand | undefined;
}

interface UseBrandsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableCache?: boolean;
  onError?: (error: string) => void;
  onSuccess?: (message: string) => void;
}

// Sistema de rate limiting e debouncing
class RequestManager {
  private static instance: RequestManager;
  private requestCache = new Map<string, { data: any; timestamp: number; expiresAt: number }>();
  private retryDelays = new Map<string, number>();
  private lastRequestTime = new Map<string, number>();
  private pendingRequests = new Map<string, Promise<any>>();
  
  static getInstance(): RequestManager {
    if (!RequestManager.instance) {
      RequestManager.instance = new RequestManager();
    }
    return RequestManager.instance;
  }
  
  // Implementar exponential backoff para HTTP 429
  async executeWithRetry<T>(
    key: string,
    requestFn: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    const baseDelay = 1000; // 1 segundo base
    let attempt = 0;
    
    while (attempt <= maxRetries) {
      try {
        const result = await requestFn();
        // Reset retry delay em caso de sucesso
        this.retryDelays.delete(key);
        return result;
      } catch (error: any) {
        attempt++;
        
        if (error.message?.includes('429') || error.message?.includes('Rate limit')) {
          if (attempt <= maxRetries) {
            const currentDelay = this.retryDelays.get(key) || baseDelay;
            const delay = Math.min(currentDelay * Math.pow(2, attempt - 1), 30000); // Max 30s
            this.retryDelays.set(key, delay);
            
            // Rate limit - tentando novamente
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }
        
        throw error;
      }
    }
    
    throw new Error(`Falha após ${maxRetries + 1} tentativas`);
  }
  
  // Cache inteligente com TTL
  getCachedData<T>(key: string): T | null {
    const cached = this.requestCache.get(key);
    if (cached && Date.now() < cached.expiresAt) {
      return cached.data;
    }
    return null;
  }
  
  setCachedData<T>(key: string, data: T, ttlMs: number = 120000): void { // 2 minutos default
    this.requestCache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttlMs
    });
  }
  
  // Debouncing para evitar requisições duplicadas
  async debouncedRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    debounceMs: number = 500
  ): Promise<T> {
    const now = Date.now();
    const lastRequest = this.lastRequestTime.get(key) || 0;
    
        if (now - lastRequest < debounceMs) {
      await new Promise(resolve => setTimeout(resolve, debounceMs - (now - lastRequest)));
    }

    // Verificar se já existe uma requisição pendente
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }
    
    this.lastRequestTime.set(key, Date.now());
    
    const promise = this.executeWithRetry(key, requestFn).finally(() => {
      this.pendingRequests.delete(key);
    });
    
    this.pendingRequests.set(key, promise);
    return promise;
  }
}

/**
 * Hook principal para gerenciamento de brands - Versão Clerk
 */
export function useBrands(options: UseBrandsOptions = {}): UseBrandsResult {
  const { user, isLoaded } = useUser();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    autoRefresh = true,
    refreshInterval = 300000, // 5 minutos para reduzir rate limiting
    enableCache = true,
    onError,
    onSuccess
  } = options;

  const requestManager = RequestManager.getInstance();

  /**
   * Buscar todas as marcas do usuário com cache e debouncing
   */
  const refreshBrands = useCallback(async () => {
    // Aguardar carregamento da autenticação
    if (!isLoaded) {
      return;
    }

    if (!user) {
      setBrands([]);
      setLoading(false);
      setError(null);
      return;
    }

    const cacheKey = `brands_${user.id}`;
    
    // Verificar cache primeiro
    if (enableCache) {
      const cachedData = requestManager.getCachedData<Brand[]>(cacheKey);
      if (cachedData) {
        setBrands(cachedData);
        setLoading(false);
        setError(null);
        return;
      }
    }

          try {
      setLoading(true);
      setError(null);
      
      const response = await requestManager.debouncedRequest(
        cacheKey,
        async () => {
          const response = await fetch('/api/brands', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-User-ID': user.id
            }
          });
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            
            // Tratamento específico para HTTP 429
            if (response.status === 429) {
              throw new Error('Rate limit atingido. Aguarde alguns minutos antes de tentar novamente.');
            }
            
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
          }
          
          return response.json();
        }
      );
      
      if (!response.success) {
        const errorMsg = response.error || 'Resposta de API inválida';
        throw new Error(errorMsg);
      }
      
      const brandsData = response.data || [];
      
      // Remover duplicatas por ID para evitar keys duplicadas
      const uniqueBrands = brandsData.filter((brand: Brand, index: number, self: Brand[]) => 
        index === self.findIndex(b => b.id === brand.id)
      );
      
      setBrands(uniqueBrands);
      
      // Salvar no cache com TTL estendido
      if (enableCache) {
        requestManager.setCachedData(cacheKey, uniqueBrands, 180000); // 3 minutos
      }
      
      onSuccess?.(`${uniqueBrands.length} marcas carregadas com sucesso`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido ao carregar marcas';
        // Erro silencioso - tratado pela UI
        
      setError(errorMessage);
      onError?.(errorMessage);
      
      // Em caso de erro, tentar carregar dados mais antigos do cache
      if (enableCache && user) {
        const staleData = requestManager.getCachedData<Brand[]>(`brands_${user.id}_stale`);
        if (staleData) {
          setBrands(staleData);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [isLoaded, user, enableCache, onError, requestManager]);

  /**
   * Criar nova marca com tratamento de rate limiting
   */
  const createBrand = useCallback(async (data: CreateBrandData): Promise<Brand> => {
    // Aguardar carregamento da autenticação
    if (!isLoaded) {
      throw new Error('Aguardando carregamento da autenticação...');
    }

    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    try {
      
      const response = await requestManager.executeWithRetry(
        `create_brand_${Date.now()}`,
        async () => {
          const response = await fetch('/api/brands', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-User-ID': user.id
            },
            body: JSON.stringify(data)
          });
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            
            if (response.status === 429) {
              throw new Error('Rate limit atingido. Aguarde alguns minutos antes de tentar novamente.');
            }
            
            throw new Error(errorData.error || `Erro HTTP ${response.status}`);
          }
          
          return response.json();
        }
      );
      
      if (!response.success) {
        throw new Error(response.error || 'Falha ao criar marca');
      }
      
      const newBrand = response.data;
      
      // Atualizar estado e cache
      setBrands(prev => {
        const existingIndex = prev.findIndex(brand => brand.id === newBrand.id);
        let updatedBrands: Brand[];
        
        if (existingIndex >= 0) {
          updatedBrands = prev.map((brand, index) => 
            index === existingIndex ? newBrand : brand
          );
        } else {
          updatedBrands = [newBrand, ...prev];
        }
        
        // Atualizar cache
        if (enableCache && user) {
          requestManager.setCachedData(`brands_${user.id}`, updatedBrands, 180000);
        }
        
        return updatedBrands;
      });
      
      onSuccess?.('Marca criada com sucesso!');
      
      return newBrand;
      
    } catch (err) {
      let errorMessage = 'Erro ao criar marca';
      
      if (err instanceof Error) {
        errorMessage = err.message;
        
        if (err.message.includes('Rate limit')) {
          errorMessage = 'Muitas requisições. Aguarde alguns minutos antes de tentar novamente.';
        } else if (err.message.includes('Dados inválidos')) {
          errorMessage = 'Dados inválidos: verifique se os campos URL (website/logo) são válidos';
        } else if (err.message.includes('URL inválida')) {
          errorMessage = 'URL inválida: verifique se o website e logo são URLs válidas';
        } else if (err.message.includes('já existe')) {
          errorMessage = 'Uma marca com este nome já existe';
        }
      }
      
      onError?.(errorMessage);
      throw new Error(errorMessage);
    }
  }, [isLoaded, user, enableCache, onSuccess, onError, requestManager]);

  /**
   * Buscar marca específica por ID
   */
  const getBrandById = useCallback((id: string): Brand | undefined => {
    return brands.find(brand => brand.id === id);
  }, [brands]);

  // Auto-refresh se habilitado
  useEffect(() => {
    if (autoRefresh && isLoaded && user) {
      refreshBrands();
      
      const interval = setInterval(refreshBrands, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refreshBrands, isLoaded, user]);

  // Funções placeholder para compatibilidade
  const updateBrand = useCallback(async (id: string, data: UpdateBrandData): Promise<Brand> => {
    throw new Error('updateBrand não implementado na versão Clerk');
  }, []);

  const deleteBrand = useCallback(async (id: string): Promise<void> => {
    throw new Error('deleteBrand não implementado na versão Clerk');
  }, []);

  return {
    brands,
    loading,
    error,
    createBrand,
    updateBrand,
    deleteBrand,
    refreshBrands,
    getBrandById
  };
}

/**
 * Hook simplificado para listagem de brands
 */
export function useBrandsList(): Pick<UseBrandsResult, 'brands' | 'loading' | 'error'> {
  const { user, isLoaded } = useUser();
  const { brands, loading, error } = useBrands({
    autoRefresh: true,
    enableCache: true
  });
  
  if (!isLoaded || !user) {
    return { brands: [], loading: true, error: null };
  }
  
  return { brands, loading, error };
}

/**
 * Hook para marca específica
 */
export function useBrand(brandId: string): {
  brand: Brand | null | undefined;
  loading: boolean;
  error: string | null;
  updateBrand: (data: UpdateBrandData) => Promise<Brand>;
  deleteBrand: () => Promise<void>;
} {
  const { brands, loading, error, updateBrand, deleteBrand } = useBrands();
  const brand = brands.find(b => b.id === brandId);
  
  return {
    brand: brand || null,
    loading,
    error,
    updateBrand: (data: UpdateBrandData) => updateBrand(brandId, data),
    deleteBrand: () => deleteBrand(brandId)
  };
} 

