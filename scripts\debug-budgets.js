const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  getDocs, 
  doc, 
  query, 
  where,
  orderBy, 
  deleteDoc,
  updateDoc,
  getDoc
} = require('firebase/firestore');

const firebaseConfig = {
  apiKey: "AIzaSyDhSkFQHdDLke6drWlVJzE3Zys6CJx_arQ",
  authDomain: "deumatch-demo.firebaseapp.com",
  projectId: "deumatch-demo",
  storageBucket: "deumatch-demo.firebasestorage.app",
  messagingSenderId: "306980252445",
  appId: "1:306980252445:web:e6e275cde8b9b9668e0ff4"
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// 🔍 FUNÇÃO: Listar todos os budgets duplicados
async function findDuplicateBudgets() {
  try {
    console.log('🔍 Procurando budgets duplicados...');
    
    // Buscar todas as propostas
    const proposalsSnapshot = await getDocs(collection(db, 'proposals'));
    console.log(`📋 Encontradas ${proposalsSnapshot.size} propostas`);
    
    let totalBudgets = 0;
    let duplicatesFound = 0;
    const duplicatesByProposal = [];
    
    for (const proposalDoc of proposalsSnapshot.docs) {
      const proposalId = proposalDoc.id;
      const proposalData = proposalDoc.data();
      
      console.log(`\n📊 Analisando proposta: ${proposalData.nome || proposalId}`);
      
      // Buscar influenciadores da proposta
      const influencersSnapshot = await getDocs(
        collection(db, 'proposals', proposalId, 'influencers')
      );
      
      let proposalDuplicates = 0;
      const proposalDetails = {
        proposalId,
        proposalName: proposalData.nome || 'Sem nome',
        influencers: []
      };
      
      for (const influencerDoc of influencersSnapshot.docs) {
        const influencerId = influencerDoc.id;
        
        // Buscar budgets do influenciador (ordenados por data)
        const budgetsSnapshot = await getDocs(
          query(
            collection(db, 'proposals', proposalId, 'influencers', influencerId, 'budgets'),
            orderBy('updatedAt', 'desc')
          )
        );
        
        const budgets = budgetsSnapshot.docs.map(doc => ({
          id: doc.id,
          data: doc.data()
        }));
        
        totalBudgets += budgets.length;
        
        // Verificar duplicatas por serviceType
        const serviceTypes = {};
        const duplicates = [];
        
        budgets.forEach(budget => {
          const serviceType = budget.data.serviceType || 'sem-tipo';
          
          if (serviceTypes[serviceType]) {
            // Duplicata encontrada!
            duplicates.push({
              serviceType,
              older: serviceTypes[serviceType],
              newer: budget
            });
            duplicatesFound++;
            proposalDuplicates++;
          } else {
            serviceTypes[serviceType] = budget;
          }
        });
        
        if (duplicates.length > 0) {
          proposalDetails.influencers.push({
            influencerId,
            duplicates
          });
        }
      }
      
      if (proposalDuplicates > 0) {
        duplicatesByProposal.push(proposalDetails);
        console.log(`  ⚠️ ${proposalDuplicates} duplicatas encontradas`);
      } else {
        console.log(`  ✅ Nenhuma duplicata`);
      }
    }
    
    console.log(`\n📊 RESUMO:`);
    console.log(`  Total de budgets: ${totalBudgets}`);
    console.log(`  Duplicatas encontradas: ${duplicatesFound}`);
    console.log(`  Propostas com duplicatas: ${duplicatesByProposal.length}`);
    
    return duplicatesByProposal;
    
  } catch (error) {
    console.error('❌ Erro ao buscar duplicatas:', error);
    return [];
  }
}

// 🧹 FUNÇÃO: Limpar budgets duplicados (manter apenas o mais recente)
async function cleanDuplicateBudgets(dryRun = true) {
  try {
    console.log(`🧹 ${dryRun ? 'SIMULANDO' : 'EXECUTANDO'} limpeza de budgets duplicados...`);
    
    const duplicates = await findDuplicateBudgets();
    
    if (duplicates.length === 0) {
      console.log('✅ Nenhuma duplicata encontrada para limpar');
      return;
    }
    
    let deletedCount = 0;
    
    for (const proposal of duplicates) {
      console.log(`\n🗂️ Limpando proposta: ${proposal.proposalName}`);
      
      for (const influencer of proposal.influencers) {
        console.log(`  👤 Influenciador: ${influencer.influencerId}`);
        
        for (const duplicate of influencer.duplicates) {
          const olderBudget = duplicate.older;
          const newerBudget = duplicate.newer;
          
          console.log(`    🔄 Serviço: ${duplicate.serviceType}`);
          console.log(`      ⏰ Mais antigo: ${olderBudget.id} (R$ ${olderBudget.data.amount}) - ${olderBudget.data.updatedAt?.toDate?.()?.toISOString() || 'sem data'}`);
          console.log(`      🆕 Mais recente: ${newerBudget.id} (R$ ${newerBudget.data.amount}) - ${newerBudget.data.updatedAt?.toDate?.()?.toISOString() || 'sem data'}`);
          
          if (!dryRun) {
            // Deletar o budget mais antigo
            const budgetRef = doc(
              db, 
              'proposals', 
              proposal.proposalId, 
              'influencers', 
              influencer.influencerId, 
              'budgets', 
              olderBudget.id
            );
            
            await deleteDoc(budgetRef);
            console.log(`      ❌ Deletado: ${olderBudget.id}`);
            deletedCount++;
          } else {
            console.log(`      🔍 SERIA DELETADO: ${olderBudget.id}`);
            deletedCount++;
          }
        }
      }
    }
    
    console.log(`\n📊 RESULTADO:`);
    console.log(`  ${dryRun ? 'Simulação' : 'Limpeza'} concluída`);
    console.log(`  ${dryRun ? 'Seriam deletados' : 'Deletados'}: ${deletedCount} budgets duplicados`);
    
    if (dryRun) {
      console.log(`\n💡 Para executar a limpeza real, chame: cleanDuplicateBudgets(false)`);
    }
    
  } catch (error) {
    console.error('❌ Erro durante limpeza:', error);
  }
}

// 📊 FUNÇÃO: Análise detalhada de uma proposta específica
async function analyzeBudgetsForProposal(proposalId) {
  try {
    console.log(`🔍 Analisando budgets da proposta: ${proposalId}`);
    
    const proposalDoc = await getDoc(doc(db, 'proposals', proposalId));
    
    if (!proposalDoc.exists()) {
      console.log('❌ Proposta não encontrada');
      return;
    }
    
    const proposalData = proposalDoc.data();
    console.log(`📋 Proposta: ${proposalData.nome || 'Sem nome'}`);
    console.log(`💰 Total atual: R$ ${(proposalData.totalAmount || 0).toLocaleString('pt-BR')}`);
    
    // Buscar influenciadores
    const influencersSnapshot = await getDocs(
      collection(db, 'proposals', proposalId, 'influencers')
    );
    
    console.log(`👥 Influenciadores: ${influencersSnapshot.size}`);
    
    let calculatedTotal = 0;
    let budgetCount = 0;
    
    for (const influencerDoc of influencersSnapshot.docs) {
      const influencerId = influencerDoc.id;
      console.log(`\n  👤 Influenciador: ${influencerId}`);
      
      // Buscar budgets
      const budgetsSnapshot = await getDocs(
        query(
          collection(db, 'proposals', proposalId, 'influencers', influencerId, 'budgets'),
          orderBy('updatedAt', 'desc')
        )
      );
      
      const uniqueBudgets = new Map();
      
      budgetsSnapshot.docs.forEach(doc => {
        const data = doc.data();
        const serviceType = data.serviceType || 'sem-tipo';
        
        if (!uniqueBudgets.has(serviceType)) {
          uniqueBudgets.set(serviceType, {
            id: doc.id,
            data,
            serviceType
          });
        }
      });
      
      uniqueBudgets.forEach(budget => {
        const amount = budget.data.amount || 0;
        if (amount > 0) {
          calculatedTotal += amount;
          budgetCount++;
          console.log(`    💰 ${budget.serviceType}: R$ ${amount.toLocaleString('pt-BR')} (${budget.id})`);
        }
      });
      
      console.log(`    📊 Total: ${uniqueBudgets.size} budgets únicos, ${budgetsSnapshot.size} total`);
    }
    
    console.log(`\n📊 RESUMO:`);
    console.log(`  Total calculado: R$ ${calculatedTotal.toLocaleString('pt-BR')}`);
    console.log(`  Total no banco: R$ ${(proposalData.totalAmount || 0).toLocaleString('pt-BR')}`);
    console.log(`  Diferença: R$ ${(calculatedTotal - (proposalData.totalAmount || 0)).toLocaleString('pt-BR')}`);
    console.log(`  Budgets únicos: ${budgetCount}`);
    
    return {
      proposalId,
      currentTotal: proposalData.totalAmount || 0,
      calculatedTotal,
      difference: calculatedTotal - (proposalData.totalAmount || 0),
      budgetCount
    };
    
  } catch (error) {
    console.error('❌ Erro na análise:', error);
  }
}

// 🚀 EXECUTAR ANÁLISES
async function main() {
  console.log('🚀 Iniciando análise de budgets...\n');
  
  // 1. Procurar duplicatas
  await findDuplicateBudgets();
  
  // 2. Simular limpeza
  console.log('\n' + '='.repeat(50));
  await cleanDuplicateBudgets(true);
  
  console.log('\n💡 Para executar limpeza real: cleanDuplicateBudgets(false)');
  console.log('💡 Para analisar proposta específica: analyzeBudgetsForProposal("ID_DA_PROPOSTA")');
}

// Exportar funções para uso individual
module.exports = {
  findDuplicateBudgets,
  cleanDuplicateBudgets,
  analyzeBudgetsForProposal
};

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 
