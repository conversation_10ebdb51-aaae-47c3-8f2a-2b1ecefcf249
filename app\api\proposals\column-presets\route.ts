import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';

export async function POST(request: NextRequest) {
  try {
    const { userId, presetName, visibleColumns, columnOrder } = await request.json();

    if (!userId || !presetName || !visibleColumns) {
      return NextResponse.json(
        { error: 'userId, presetName e visibleColumns são obrigatórios' },
        { status: 400 }
      );
    }

    console.log('💾 [COLUMN-PRESETS] Salvando preset:', {
      userId,
      presetName,
      hasColumns: !!visibleColumns
    });

    // Salvar preset no Firestore
    const presetRef = db
      .collection('users')
      .doc(userId)
      .collection('columnPresets')
      .doc();

    const presetData = {
      id: presetRef.id,
      name: presetName,
      visibleColumns,
      columnOrder: columnOrder || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await presetRef.set(presetData);

    return NextResponse.json({ 
      success: true, 
      preset: presetData,
      message: 'Preset salvo com sucesso' 
    });

  } catch (error) {
    console.error('Erro ao salvar preset:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    console.log('📊 [COLUMN-PRESETS] Carregando presets:', { userId });

    // Buscar presets no Firestore
    const presetsRef = db
      .collection('users')
      .doc(userId)
      .collection('columnPresets')
      .orderBy('createdAt', 'desc');

    const snapshot = await presetsRef.get();
    const presets = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json({ 
      success: true, 
      presets,
      message: `${presets.length} presets encontrados`
    });

  } catch (error) {
    console.error('Erro ao carregar presets:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const presetId = searchParams.get('presetId');

    if (!userId || !presetId) {
      return NextResponse.json(
        { error: 'userId e presetId são obrigatórios' },
        { status: 400 }
      );
    }

    console.log('🗑️ [COLUMN-PRESETS] Deletando preset:', { userId, presetId });

    // Deletar preset do Firestore
    await db
      .collection('users')
      .doc(userId)
      .collection('columnPresets')
      .doc(presetId)
      .delete();

    return NextResponse.json({ 
      success: true, 
      message: 'Preset deletado com sucesso' 
    });

  } catch (error) {
    console.error('Erro ao deletar preset:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
