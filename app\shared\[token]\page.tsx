"use client";

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';
import { Loader } from '@/components/ui/loader';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertTriangle, ExternalLink, Info } from 'lucide-react';
import { CollaborationDialog } from '@/components/ui/collaboration-dialog';

interface PageProps {
  params: Promise<{
    token: string;
  }>;
}

export default function SharedProposalPage({ params }: PageProps) {
  const router = useRouter();
  const { currentUser, firebaseUser } = useFirebaseAuth();
  const [token, setToken] = useState<string | null>(null);
  const [status, setStatus] = useState<'loading' | 'waiting-confirmation' | 'success' | 'error' | 'auth-required'>('loading');
  const [message, setMessage] = useState('');
  const [proposalData, setProposalData] = useState<any>(null);
  const [showCollaborationDialog, setShowCollaborationDialog] = useState(false);

  // Resolver parâmetros
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setToken(resolvedParams.token);
    };
    resolveParams();
  }, [params]);

  // Verificar estado inicial quando o token estiver disponível
  useEffect(() => {
    if (!token) return;
    
    // Se não está autenticado, mostrar tela de login
    if (!firebaseUser) {
      setStatus('auth-required');
      setMessage('Você precisa estar logado para acessar propostas compartilhadas.');
      
      // Mostrar o diálogo de colaboração na primeira visita
      const firstVisit = sessionStorage.getItem(`proposal-first-visit-${token}`) !== 'false';
      if (firstVisit) {
        sessionStorage.setItem(`proposal-first-visit-${token}`, 'false');
        setShowCollaborationDialog(true);
      }
      return;
    }

    // Se estiver autenticado, verificar se há colaboração pendente
    const pendingCollaboration = localStorage.getItem('pendingCollaboration');
    
    if (pendingCollaboration === 'true') {
      // Limpar flag de colaboração pendente
      localStorage.removeItem('pendingCollaboration');
      setShowCollaborationDialog(true);
    } else {
      // Mostrar status de espera por confirmação
      setStatus('waiting-confirmation');
      
      // Mostrar o diálogo de colaboração na primeira visita após login
      const firstVisitAfterLogin = sessionStorage.getItem(`proposal-first-visit-after-login-${token}`) !== 'false';
      if (firstVisitAfterLogin) {
        sessionStorage.setItem(`proposal-first-visit-after-login-${token}`, 'false');
        setShowCollaborationDialog(true);
      }
    }
  }, [token, firebaseUser]);

  const processSharedProposal = async () => {
    if (!token || !firebaseUser) return;

    try {
      setStatus('loading');
      setMessage('Processando proposta compartilhada...');

      // Obter token de autenticação
      const idToken = await firebaseUser.getIdToken();

      // Chamar API de compartilhamento
      const response = await fetch(`/api/shared/${token}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${idToken}`,
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao processar compartilhamento');
      }

      if (result.success) {
        setStatus('success');
        setProposalData(result.data.proposal);
        setMessage('Proposta aceita com sucesso! Redirecionando...');

        // Redirecionar para a página da proposta após 2 segundos
        setTimeout(() => {
          if (result.data?.proposal?.id) {
            router.push(`/propostas/${result.data.proposal.id}`);
          } else if (currentUser?.id) {
            router.push(`/influencers/${currentUser.id}`);
          } else {
            router.push('/influencers');
          }
        }, 2000);
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }

    } catch (error) {
      console.error('Erro ao processar proposta compartilhada:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Erro ao processar compartilhamento');
    }
  };

  const handleLogin = () => {
    // Salvar o token para depois do login e indicar que há colaboração pendente
    localStorage.setItem('pendingSharedToken', token || '');
    localStorage.setItem('pendingCollaboration', 'true');
    
    // Redirecionar para login com parâmetro de redirecionamento
    const redirectUrl = encodeURIComponent(`/shared/${token}`);
    router.push(`/login?redirect=${redirectUrl}`);
  };

  const handleCollaborationConfirmed = () => {
    setShowCollaborationDialog(false);
    processSharedProposal();
  };

  const handleRetry = () => {
    // Mostrar diálogo novamente
    setShowCollaborationDialog(true);
  };

  const handleGoToInfluencers = () => {
    if (currentUser?.id) {
      router.push(`/influencers/${currentUser.id}`);
    } else {
      router.push('/influencers');
    }
  };

  // Loading
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loader isLoading={true} message={message} showLogo={true} />
      </div>
    );
  }

  // Esperando confirmação
  if (status === 'waiting-confirmation') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center gap-4 text-center">
              <Info className="h-16 w-16 text-blue-500" />
              <h1 className="text-2xl font-bold text-foreground">Proposta Compartilhada</h1>
              <p className="text-muted-foreground">
                Você foi convidado para colaborar com uma proposta compartilhada. 
                Clique no botão abaixo para ver detalhes e aceitar a colaboração.
              </p>
              
              <Button 
                onClick={() => setShowCollaborationDialog(true)}
                className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90"
              >
                Ver Detalhes da Proposta
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Diálogo de Colaboração */}
        <CollaborationDialog
          open={showCollaborationDialog}
          onClose={() => setShowCollaborationDialog(false)}
          proposalToken={token || ''}
          proposalName="esta proposta compartilhada"
          onConfirm={handleCollaborationConfirmed}
        />
      </div>
    );
  }

  // Sucesso
  if (status === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center gap-4 text-center">
              <CheckCircle className="h-16 w-16 text-green-500" />
              <h1 className="text-2xl font-bold text-foreground">Sucesso!</h1>
              <p className="text-muted-foreground">{message}</p>
              
              {proposalData && (
                <div className="w-full p-4 bg-muted/20 rounded-lg">
                  <h3 className="font-semibold mb-2">Proposta Aceita:</h3>
                  <p className="text-sm">{proposalData.name || proposalData.nome || 'Proposta sem nome'}</p>
                </div>
              )}

              <Button 
                onClick={handleGoToInfluencers}
                className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Ir para Influencers
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Autenticação necessária
  if (status === 'auth-required') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center gap-4 text-center">
              <AlertTriangle className="h-16 w-16 text-yellow-500" />
              <h1 className="text-2xl font-bold text-foreground">Login Necessário</h1>
              <p className="text-muted-foreground">{message}</p>
              
              <Button 
                onClick={handleLogin}
                className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90"
              >
                Fazer Login
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Diálogo de Colaboração */}
        <CollaborationDialog
          open={showCollaborationDialog}
          onClose={() => setShowCollaborationDialog(false)}
          proposalToken={token || ''}
          proposalName="esta proposta compartilhada"
          onConfirm={handleLogin}
        />
      </div>
    );
  }

  // Erro
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="max-w-md w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center gap-4 text-center">
            <XCircle className="h-16 w-16 text-red-500" />
            <h1 className="text-2xl font-bold text-foreground">Erro</h1>
            <p className="text-muted-foreground">{message}</p>
            
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={handleRetry}
              >
                Tentar Novamente
              </Button>
              <Button 
                onClick={handleGoToInfluencers}
                className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90"
              >
                Ir para Influencers
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Diálogo de Colaboração */}
      <CollaborationDialog
        open={showCollaborationDialog}
        onClose={() => setShowCollaborationDialog(false)}
        proposalToken={token || ''}
        proposalName="esta proposta compartilhada"
        onConfirm={handleCollaborationConfirmed}
      />
    </div>
  );
} 