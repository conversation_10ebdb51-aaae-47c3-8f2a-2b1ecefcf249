// Tipos para o sistema de propostas

export type ProposalStatus = 
  | 'draft'
  | 'pending'
  | 'sent'
  | 'viewed'
  | 'under_review'
  | 'negotiating'
  | 'approved'
  | 'accepted'
  | 'rejected'
  | 'expired'
  | 'cancelled';

export type ProposalPriority = 'low' | 'medium' | 'high';

export type ProposalStage = 
  | 'draft'           // Rascunho
  | 'ready_to_send'   // Pronta para envio
  | 'sent'            // Enviada
  | 'viewed'          // Visualizada pelo influenciador
  | 'under_review'    // Em análise
  | 'negotiating'     // Em negociação
  | 'approved'        // Aprovada
  | 'rejected'        // Rejeitada
  | 'expired'         // Expirada
  | 'cancelled';      // Cancelada

export type InfluencerTier = 'micro' | 'macro' | 'mega';

// Filtros para propostas - Fase 1 (removido - usando definição mais completa abaixo)

export type DeliverableType = 
  | 'post'
  | 'story'
  | 'reel'
  | 'video'
  | 'live'
  | 'article'
  | 'review'
  | 'other';

export interface Service {
  id?: string;
  type: DeliverableType;
  platform: string;
  quantity: number;
  description: string;
  deadline: Date;
  amount: number;
}

export interface ProposalMessage {
  id: string;
  senderId: string;
  senderType: 'brand' | 'influencer';
  message: string;
  timestamp: Date;
  attachments?: string[];
}



export interface InfluencerInProposal {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  tier?: InfluencerTier;
  followers?: number;
  engagement?: number;
}

export interface Proposal {
  id: string;
  status: ProposalStatus;
  priority: ProposalPriority;
  stage: ProposalStage;
  
  // Relacionamentos
  brandId: string;
  brandName: string;
  influencers: InfluencerInProposal[];
  campaignId?: string;
  campaignName?: string;
  
  // Financeiro
  totalAmount: number;
  currency: string;
  
  // Orçamentos detalhados
  budgets?: {
    [profileId: string]: {
      [service: string]: {
        budgetedPrice: number;
        originalPrice: number;
        updatedAt: Date;
        updatedBy: string;
        guestProposals?: Array<{
          id: string;
          value: number;
          proposedBy: string;
          proposedAt: Date;
          status: 'pending' | 'accepted' | 'rejected';
          comments?: string;
          guestInfo: {
            name: string;
            email: string;
            role: string;
            permissionLevel: string;
          };
        }>;
      };
    };
  };
  
  // Serviços
  services: Service[];
  message?: string;
  
  // Data de envio
  dataEnvio: string;
  
  // Grupo
  grupo: string;
  
  // Workflow e responsabilidade
  assignedTo: string; // ID do responsável
  createdBy: string; // ID de quem criou
  tags: string[];
  
  // Datas
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  responseDeadline?: Date;
  lastInteractionAt?: Date;
  
  // Métricas
  viewCount: number;
  responseTime?: number; // em horas
  negotiationRounds: number;
}

export interface ProposalFilters {
  userId?: string;
  influencerId?: string;
  campaignId?: string;
  status?: ProposalStatus[];
  priority?: ProposalPriority[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  amountRange?: {
    min: number;
    max: number;
  };
}

export interface ProposalStats {
  total: number;
  byStatus: Record<ProposalStatus, number>;
  averageResponseTime: number; // em horas
  averageAmount: number;
  totalValue: number;
}

export interface ProposalTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  services: Omit<Service, 'id' | 'deadline'>[];
  defaultAmount?: number;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  usageCount: number;
}

export interface CreateProposalData {
  brandId: string;
  influencers: InfluencerInProposal[];
  priority: ProposalPriority;
  totalAmount: number;
  services: Omit<Service, 'id'>[];
  dataEnvio: string;
  grupo: string;
}

export interface UpdateProposalData {
  priority?: ProposalPriority;
  totalAmount?: number;
  services?: Service[];
  dataEnvio?: string;
  grupo?: string;
}

// Utilitários para status
export const PROPOSAL_STATUS_LABELS: Record<ProposalStatus, string> = {
  draft: 'Rascunho',
  pending: 'Pendente',
  sent: 'Enviada',
  viewed: 'Visualizada',
  under_review: 'Em Análise',
  negotiating: 'Negociando',
  approved: 'Aprovada',
  accepted: 'Aceita',
  rejected: 'Rejeitada',
  expired: 'Expirada',
  cancelled: 'Cancelada'
};

// Configuração dos estágios do pipeline - Fase 1
export const PIPELINE_STAGES = {
  draft: { name: 'Rascunho', color: 'gray', order: 1 },
  ready_to_send: { name: 'Pronta p/ Envio', color: 'blue', order: 2 },
  sent: { name: 'Enviada', color: 'yellow', order: 3 },
  viewed: { name: 'Visualizada', color: 'orange', order: 4 },
  under_review: { name: 'Em Análise', color: 'purple', order: 5 },
  negotiating: { name: 'Negociando', color: 'indigo', order: 6 },
  approved: { name: 'Aprovada', color: 'green', order: 7 },
  rejected: { name: 'Rejeitada', color: 'red', order: 8 },
  expired: { name: 'Expirada', color: 'gray', order: 9 },
  cancelled: { name: 'Cancelada', color: 'gray', order: 10 }
} as const;

// Interfaces para analytics - Fase 1
export interface BrandPerformance {
  brandId: string;
  brandName: string;
  totalProposals: number;
  acceptedProposals: number;
  rejectedProposals: number;
  acceptanceRate: number;
  totalRevenue: number;
  averageResponseTime: number;
}

export interface InfluencerPerformance {
  influencerId: string;
  influencerName: string;
  totalProposals: number;
  acceptedProposals: number;
  rejectedProposals: number;
  acceptanceRate: number;
  totalRevenue: number;
  averageResponseTime: number;
  relationshipScore: number;
}

export interface MonthlyData {
  month: string;
  year: number;
  count: number;
  value?: number;
}

export interface ProposalAnalytics {
  // Métricas gerais
  totalProposals: number;
  acceptanceRate: number;
  averageResponseTime: number;
  totalRevenue: number;
  
  // Por período
  proposalsByMonth: MonthlyData[];
  revenueByMonth: MonthlyData[];
  
  // Por marca
  performanceByBrand: BrandPerformance[];
  
  // Por influenciador
  topInfluencers: InfluencerPerformance[];
  
  // Tendências
  trends: {
    acceptanceRateTrend: number;
    responseTimeTrend: number;
    revenueTrend: number;
  };
}

export const PROPOSAL_PRIORITY_LABELS: Record<ProposalPriority, string> = {
  low: 'Baixa',
  medium: 'Média',
  high: 'Alta'
};

export const SERVICE_TYPE_LABELS: Record<DeliverableType, string> = {
  post: 'Post',
  story: 'Story',
  reel: 'Reel',
  video: 'Vídeo',
  live: 'Live',
  article: 'Artigo',
  review: 'Review',
  other: 'Outro'
};

// Utilitários para filtros e classificações - Fase 1
export const getInfluencerTier = (followers: number): InfluencerTier => {
  if (followers < 100000) return 'micro';
  if (followers < 1000000) return 'macro';
  return 'mega';
};

export const getStatusColor = (status: ProposalStatus): string => {
  const colors = {
    draft: 'gray',
    pending: 'yellow',
    sent: 'blue',
    viewed: 'orange',
    under_review: 'purple',
    negotiating: 'indigo',
    approved: 'green',
    accepted: 'green',
    rejected: 'red',
    expired: 'gray',
    cancelled: 'gray'
  };
  return colors[status] || 'gray';
};

export const getPriorityColor = (priority: ProposalPriority): string => {
  const colors = {
    low: 'green',
    medium: 'yellow',
    high: 'red'
  };
  return colors[priority];
};

export const formatCurrency = (amount: number, currency: string = 'BRL'): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

export const calculateResponseTime = (sentAt: Date, respondedAt: Date): number => {
  const diffMs = respondedAt.getTime() - sentAt.getTime();
  return Math.round(diffMs / (1000 * 60 * 60)); // em horas
};

// Função para determinar o próximo estágio no pipeline
export const getNextStage = (currentStage: ProposalStage): ProposalStage | null => {
  const stageOrder = Object.entries(PIPELINE_STAGES)
    .sort(([, a], [, b]) => a.order - b.order)
    .map(([stage]) => stage as ProposalStage);
  
  const currentIndex = stageOrder.indexOf(currentStage);
  if (currentIndex === -1 || currentIndex === stageOrder.length - 1) {
    return null;
  }
  
  return stageOrder[currentIndex + 1];
};

// Função para validar transições de estágio
export const canTransitionTo = (from: ProposalStage, to: ProposalStage): boolean => {
  const validTransitions: Record<ProposalStage, ProposalStage[]> = {
    draft: ['ready_to_send', 'cancelled'],
    ready_to_send: ['sent', 'draft', 'cancelled'],
    sent: ['viewed', 'expired', 'cancelled'],
    viewed: ['under_review', 'rejected', 'expired'],
    under_review: ['negotiating', 'approved', 'rejected'],
    negotiating: ['approved', 'rejected', 'under_review'],
    approved: ['cancelled'], // Raramente cancelada após aprovação
    rejected: ['negotiating'], // Pode reabrir negociação
    expired: ['sent'], // Pode reenviar
    cancelled: ['draft'] // Pode recriar
  };
  
  return validTransitions[from]?.includes(to) || false;
};

// Funções utilitárias para UI
export function getProposalStatusColor(status: ProposalStatus): string {
  const colors: Record<ProposalStatus, string> = {
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    sent: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    viewed: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    under_review: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    negotiating: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    approved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    accepted: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    expired: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400',
    cancelled: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
  };
  return colors[status];
}

export function getProposalPriorityColor(priority: ProposalPriority): string {
  const colors: Record<ProposalPriority, string> = {
    low: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  };
  return colors[priority];
}

export function calculateProposalTotal(services: Service[]): number {
  return services.reduce((total, service) => total + service.amount, 0);
}

