// TESTE DA QUERY GRAPHQL CORRIGIDA COM CAMPO BUDGETS
// Verificar se os orçamentos estão sendo retornados nas queries

const testCorrectedGraphQLQuery = `
  query GetInfluencersWithBudgets($userId: ID!, $filters: InfluencerFilters, $pagination: PaginationInput) {
    influencers(userId: $userId, filters: $filters, pagination: $pagination) {
      nodes {
        id
        name
        avatar
        totalFollowers
        engagementRate
        rating
        isVerified
        isAvailable
        status
        category
        country
        state
        city
        
        # Pricing estruturas
        pricing {
          hasFinancialData
          priceRange
          avgPrice
          isNegotiable
        }
        
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }
        
        # Demographics
        currentDemographics {
          id
          platform
          audienceGender {
            male
            female
            other
          }
                audienceLocations {
        country
            percentage
          }
          audienceCities {
            city
            percentage
          }
          audienceAgeRange {
            range
            percentage
          }
          isActive
          source
        }

        # 🎯 CAMPO PRINCIPAL: Orçamentos organizados por plataforma
        budgets {
          instagram {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          tiktok {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          youtube {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          facebook {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          twitch {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          kwai {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          personalizado {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
        }
        
        createdAt
        updatedAt
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

// Fazer a requisição GraphQL
async function testGraphQLWithBudgets() {
  try {
    console.log('🚀 [TESTE] Executando query GraphQL CORRIGIDA com campo budgets...');
    
    const response = await fetch('http://localhost:3000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: testCorrectedGraphQLQuery,
        variables: {
          userId: "qSrQ5VmzuZQjgCP8Qs07eRE4aul1",
          filters: {},
          pagination: {
            limit: 5,
            offset: 0
          }
        }
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ [TESTE] Erros GraphQL encontrados:', result.errors);
      return;
    }

    console.log('✅ [TESTE] Query executada com sucesso!');
    console.log('📊 [TESTE] Dados retornados:', {
      totalCount: result.data?.influencers?.totalCount,
      nodesCount: result.data?.influencers?.nodes?.length
    });

    // Analisar cada influencer
    result.data?.influencers?.nodes?.forEach((influencer, index) => {
      console.log(`\n👤 [TESTE] Influencer ${index + 1}: ${influencer.name}`);
      console.log(`🆔 [TESTE] ID: ${influencer.id}`);
      
      // Verificar pricing
      console.log(`💰 [TESTE] Pricing denormalizado:`, {
        hasFinancialData: influencer.pricing?.hasFinancialData,
        priceRange: influencer.pricing?.priceRange,
        avgPrice: influencer.pricing?.avgPrice
      });
      
      console.log(`💰 [TESTE] Current Pricing:`, influencer.currentPricing ? 'Presente' : 'Ausente');
      
      // 🎯 VERIFICAR ORÇAMENTOS (CAMPO PRINCIPAL)
      if (influencer.budgets) {
        console.log(`🎯 [TESTE] ORÇAMENTOS ENCONTRADOS!`);
        
        Object.entries(influencer.budgets).forEach(([platform, budgets]) => {
          if (budgets && Array.isArray(budgets) && budgets.length > 0) {
            console.log(`  📱 [TESTE] ${platform}: ${budgets.length} orçamento(s)`);
            budgets.forEach((budget, budgetIndex) => {
              console.log(`    💵 [TESTE] Orçamento ${budgetIndex + 1}:`, {
                id: budget.id,
                amount: budget.amount,
                currency: budget.currency,
                serviceType: budget.serviceType,
                status: budget.status
              });
            });
          } else {
            console.log(`  📱 [TESTE] ${platform}: Sem orçamentos`);
          }
        });
      } else {
        console.log(`❌ [TESTE] Campo 'budgets' não encontrado ou nulo`);
      }
      
      // Verificar demographics
      console.log(`📊 [TESTE] Demographics: ${influencer.currentDemographics?.length || 0} plataforma(s)`);
    });

    // 🎯 RESUMO DOS ORÇAMENTOS
    console.log('\n📋 [TESTE] === RESUMO DOS ORÇAMENTOS ===');
    let totalBudgets = 0;
    let budgetsByPlatform = {};
    
    result.data?.influencers?.nodes?.forEach(influencer => {
      if (influencer.budgets) {
        Object.entries(influencer.budgets).forEach(([platform, budgets]) => {
          if (budgets && Array.isArray(budgets)) {
            const count = budgets.length;
            totalBudgets += count;
            budgetsByPlatform[platform] = (budgetsByPlatform[platform] || 0) + count;
          }
        });
      }
    });
    
    console.log(`🎯 [TESTE] Total de orçamentos encontrados: ${totalBudgets}`);
    console.log(`🎯 [TESTE] Por plataforma:`, budgetsByPlatform);
    
    if (totalBudgets > 0) {
      console.log('🎉 [TESTE] SUCESSO! Os orçamentos estão sendo retornados corretamente!');
    } else {
      console.log('⚠️ [TESTE] ATENÇÃO: Nenhum orçamento encontrado. Verifique os dados no Firestore.');
    }

  } catch (error) {
    console.error('❌ [TESTE] Erro na execução:', error);
  }
}

// Executar o teste
testGraphQLWithBudgets(); 
