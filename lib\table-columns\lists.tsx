import React from 'react';
import { ColumnDef } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MoreHorizontal, Edit, Trash2, Eye, Share, Download, HelpCircle, Building2 } from 'lucide-react';
import { formatters, StatusBadge, typeIcons } from '@/lib/table-utils';
import { cn } from '@/lib/utils';
import { Lista } from '@/types/list';

export interface ListColumnActions {
  onEdit?: (lista: Lista) => void;
  onDelete?: (lista: Lista) => void;
  onView?: (lista: Lista) => void;
  onShare?: (lista: Lista) => void;
  onExport?: (lista: Lista) => void;
  onNameClick?: (lista: Lista) => void;
}

export function createListColumns(
  actions: ListColumnActions = {},
  options: {
    showActions?: boolean;
    enableClickableNames?: boolean;
    showCriterios?: boolean;
  } = {}
): ColumnDef<Lista>[] {
  const { 
    showActions = true, 
    enableClickableNames = false,
    showCriterios = true 
  } = options;
  
  const { onEdit, onDelete, onView, onShare, onExport, onNameClick } = actions;

  const columns: ColumnDef<Lista>[] = [
    {
      accessorKey: 'nome',
      id: 'nome',
      header: 'NOME DA LISTA',
      cell: ({ row }) => {
        const lista = row.original;
        
        const content = (
          <div className="flex flex-col">
            <div className={cn(
              "font-medium text-left",
              enableClickableNames && "hover:text-[#ff0074] cursor-pointer"
            )}>
              {lista.nome}
            </div>
            {showCriterios && lista.tipoLista === 'dinâmica' && lista.criterios && lista.criterios.length > 0 && (
              <div className="mt-1">
                <span className="text-xs text-muted-foreground">
                  Critérios: {lista.criterios.length} definido(s)
                </span>
              </div>
            )}
          </div>
        );

        if (enableClickableNames && onNameClick) {
          return (
            <button 
              onClick={() => onNameClick(lista)}
              className="text-left w-full"
            >
              {content}
            </button>
          );
        }

        return content;
      },
    },
    {
      accessorKey: 'marcasAssociadas',
      id: 'marcasAssociadas',
      header: () => (
        <div className="flex items-center gap-1">
          <Building2 className="h-4 w-4" />
          MARCAS
        </div>
      ),
      cell: ({ row }) => {
        const lista = row.original;
        const marcas = lista.marcasAssociadas || [];
        
        if (marcas.length === 0) {
          return (
            <div className="text-muted-foreground text-sm">
              Nenhuma marca
            </div>
          );
        }

        if (marcas.length === 1) {
          const marca = marcas[0];
          return (
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={marca.logo} alt={marca.name} />
                <AvatarFallback className="text-xs bg-[#ff0074] text-white">
                  {marca.name.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium truncate max-w-[120px]" title={marca.name}>
                {marca.name}
              </span>
            </div>
          );
        }

        return (
          <div className="flex items-center gap-1">
            <div className="flex -space-x-1">
              {marcas.slice(0, 3).map((marca, index) => (
                <Avatar key={marca.id} className="h-6 w-6 border-2 border-background" title={marca.name}>
                  <AvatarImage src={marca.logo} alt={marca.name} />
                  <AvatarFallback className="text-xs bg-[#ff0074] text-white">
                    {marca.name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
            </div>
            {marcas.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{marcas.length - 3}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'tipoLista',
      id: 'tipoLista',
      header: () => (
        <div className="flex items-center gap-1">
          TIPO DE LISTA
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <div className="max-w-xs">
                  <p className="font-medium mb-1">Como a lista funciona:</p>
                  <ul className="text-xs space-y-1">
                    <li>• <strong>Estática:</strong> Você adiciona/remove itens manualmente</li>
                    <li>• <strong>Dinâmica:</strong> Lista atualiza automaticamente por critérios</li>
                  </ul>
                  <p className="text-xs mt-2 text-muted-foreground">Ex: "Influencers +50k seguidores" seria dinâmica</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      cell: ({ row }) => {
        const lista = row.original;
        return <StatusBadge status={lista.tipoLista} type="list" />;
      },
    },
    {
      accessorKey: 'tipoObjeto',
      id: 'tipoObjeto',
      header: () => (
        <div className="flex items-center gap-1">
          TIPO DE OBJETO
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <div className="max-w-xs">
                  <p className="font-medium mb-1">O que vai ficar dentro da lista:</p>
                  <ul className="text-xs space-y-1">
                    <li>• <strong>Influenciadores:</strong> Perfis de creators</li>
                    <li>• <strong>Marcas:</strong> Empresas parceiras</li>
                    <li>• <strong>Campanhas:</strong> Projetos/trabalhos</li>
                    <li>• <strong>Conteúdo:</strong> Fotos, vídeos, posts</li>
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ),
      cell: ({ row }) => {
        const lista = row.original;
        return (
          <div className="flex items-center gap-2">
            {typeIcons[lista.tipoObjeto as keyof typeof typeIcons]}
            <span className="capitalize">{lista.tipoObjeto}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'tamanho',
      id: 'tamanho',
      header: 'TAMANHO',
      cell: ({ row }) => (
        <span className="font-medium">
          {formatters.number(row.original.tamanho)}
        </span>
      ),
    },
    {
      accessorKey: 'criadoPorNome',
      id: 'criadoPor',
      header: 'CRIADO POR',
      cell: ({ row }) => row.original.criadoPorNome,
    },
    {
      accessorKey: 'createdAt',
      id: 'createdAt',
      header: 'DATA DE CRIAÇÃO',
      cell: ({ row }) => formatters.date(row.original.createdAt),
    },
    {
      accessorKey: 'updatedAt',
      id: 'updatedAt',
      header: 'ÚLTIMA ATUALIZAÇÃO',
      cell: ({ row }) => formatters.date(row.original.updatedAt),
    },
  ];

  if (showActions) {
    columns.push({
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        const lista = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(lista)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(lista)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
              )}
              {onShare && (
                <DropdownMenuItem onClick={() => onShare(lista)}>
                  <Share className="h-4 w-4 mr-2" />
                  Compartilhar
                </DropdownMenuItem>
              )}
              {onExport && (
                <DropdownMenuItem onClick={() => onExport(lista)}>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem 
                  className="text-destructive"
                  onClick={() => onDelete(lista)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Deletar
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 48,
    });
  }

  return columns;
} 



