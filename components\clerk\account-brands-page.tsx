'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Plus, Building2, Edit, Trash2, Globe, Mail, Phone, X, ImageIcon, Upload } from 'lucide-react';
import Image from 'next/image';

interface Brand {
  id: string;
  name: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  industry?: string;
  logo?: string;
  status: 'active' | 'inactive';
}

export function AccountBrandsPage() {
  const { user } = useUser();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [newBrand, setNewBrand] = useState({
    name: '',
    description: '',
    website: '',
    email: '',
    phone: '',
    industry: '',
    logo: ''
  });

  // Carregar marcas
  useEffect(() => {
    if (user?.id) {
      fetchBrands();
    }
  }, [user?.id]);

  const fetchBrands = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/brands/user/${user.id}`);
      if (response.ok) {
        const data = await response.json();
        setBrands(data);
      }
    } catch (error) {
      console.error('Erro ao carregar marcas:', error);
      toast.error('Erro ao carregar marcas');
    } finally {
      setIsLoading(false);
    }
  };

  const uploadLogo = async (file: File): Promise<string | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'brand-logo');

      const response = await fetch('/api/uploads/brands/logos', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro no upload');
      }

      const result = await response.json();
      return result.success ? result.url : null;
    } catch (error) {
      console.error('Erro no upload do logo:', error);
      toast.error('Erro ao fazer upload do logo');
      return null;
    }
  };

  const createBrand = async () => {
    if (!newBrand.name.trim() || !user?.id) return;

    setIsCreating(true);
    try {
      let logoUrl = '';

      // Se há um arquivo de logo selecionado, fazer upload primeiro
      if (logoFile) {
        const uploadedLogoUrl = await uploadLogo(logoFile);
        if (uploadedLogoUrl) {
          logoUrl = uploadedLogoUrl;
        }
      }

      const response = await fetch(`/api/brands/user/${user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newBrand.name.trim(),
          description: newBrand.description.trim(),
          website: newBrand.website.trim(),
          email: newBrand.email.trim(),
          phone: newBrand.phone.trim(),
          industry: newBrand.industry.trim(),
          logo: logoUrl
        }),
      });

      if (response.ok) {
        const brand = await response.json();
        setBrands(prev => [...prev, brand]);
        setNewBrand({
          name: '',
          description: '',
          website: '',
          email: '',
          phone: '',
          industry: '',
          logo: ''
        });
        setLogoFile(null);
        setIsModalOpen(false);
        toast.success('Marca criada com sucesso!');
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Erro ao criar marca');
      }
    } catch (error) {
      console.error('Erro ao criar marca:', error);
      toast.error('Erro ao criar marca');
    } finally {
      setIsCreating(false);
    }
  };

  const deleteBrand = async (brandId: string) => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/brands/user/${user.id}/${brandId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setBrands(prev => prev.filter(brand => brand.id !== brandId));
        toast.success('Marca removida com sucesso!');
      } else {
        toast.error('Erro ao remover marca');
      }
    } catch (error) {
      console.error('Erro ao remover marca:', error);
      toast.error('Erro ao remover marca');
    }
  };

  const resetForm = () => {
    setNewBrand({
      name: '',
      description: '',
      website: '',
      email: '',
      phone: '',
      industry: '',
      logo: ''
    });
    setLogoFile(null);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    resetForm();
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-muted-foreground">Carregando marcas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Building2 className="h-6 w-6 text-[#ff0074]" />
          <h2 className="text-xl font-semibold">Gerenciamento de Marcas</h2>
        </div>
        
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => setIsModalOpen(true)}
              className="bg-[#ff0074] hover:bg-[#e6006a]"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Marca
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto z-[99999]" onPointerDownOutside={(e) => e.preventDefault()}>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-[#ff0074]" />
                Nova Marca
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-6 py-4" onClick={(e) => e.stopPropagation()}>
              {/* Upload do Logo - Simplificado */}
              <div className="space-y-2">
                <Label>Logo da Marca</Label>
                <div className="flex items-center gap-4">
                  {/* Preview do logo */}
                  {logoFile ? (
                    <div className="w-16 h-16 rounded-lg overflow-hidden border bg-muted relative">
                      <Image
                        src={URL.createObjectURL(logoFile)}
                        alt="Preview do logo"
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setLogoFile(null);
                        }}
                        className="absolute -top-2 -right-2  bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    <div className="w-16 h-16 rounded-lg border bg-muted flex items-center justify-center">
                      <ImageIcon className="h-6 w-6 text-muted-foreground" />
                    </div>
                  )}
                  
                  {/* Botão de upload */}
                  <div className="flex-1">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        e.stopPropagation();
                        const file = e.target.files?.[0];
                        if (file) {
                          if (file.size > 5 * 1024 * 1024) {
                            toast.error('Arquivo muito grande. Máximo 5MB');
                            return;
                          }
                          if (!file.type.startsWith('image/')) {
                            toast.error('Apenas imagens são permitidas');
                            return;
                          }
                          setLogoFile(file);
                        }
                      }}
                      className="hidden"
                      id="logo-upload"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        document.getElementById('logo-upload')?.click();
                      }}
                      className="w-full"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {logoFile ? 'Trocar logo' : 'Escolher logo'}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="brand-name">Nome da Marca *</Label>
                  <Input
                    id="brand-name"
                    value={newBrand.name}
                    onChange={(e) => setNewBrand(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Nome da sua marca"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="brand-industry">Segmento</Label>
                  <Input
                    id="brand-industry"
                    value={newBrand.industry}
                    onChange={(e) => setNewBrand(prev => ({ ...prev, industry: e.target.value }))}
                    placeholder="Ex: Moda, Tecnologia, Beleza..."
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="brand-description">Descrição</Label>
                <Textarea
                  id="brand-description"
                  value={newBrand.description}
                  onChange={(e) => setNewBrand(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Descreva sua marca, missão e valores..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="brand-website">Website</Label>
                  <Input
                    id="brand-website"
                    value={newBrand.website}
                    onChange={(e) => setNewBrand(prev => ({ ...prev, website: e.target.value }))}
                    placeholder="https://..."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="brand-email">Email de Contato</Label>
                  <Input
                    id="brand-email"
                    type="email"
                    value={newBrand.email}
                    onChange={(e) => setNewBrand(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="brand-phone">Telefone</Label>
                  <Input
                    id="brand-phone"
                    value={newBrand.phone}
                    onChange={(e) => setNewBrand(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="(11) 99999-9999"
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={handleModalClose}
                  disabled={isCreating}
                  type="button"
                >
                  Cancelar
                </Button>
                
                <Button
                  onClick={createBrand}
                  disabled={!newBrand.name.trim() || isCreating}
                  className="bg-[#ff0074] hover:bg-[#e6006a]"
                  type="button"
                >
                  {isCreating ? (
                    <>Criando...</>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Criar Marca
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
      
      {/* Lista de Marcas */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Suas Marcas ({brands.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {brands.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground mb-4">
                Você ainda não criou nenhuma marca.
              </p>
              <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => setIsModalOpen(true)}
                    className="bg-[#ff0074] hover:bg-[#e6006a]"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Criar primeira marca
                  </Button>
                </DialogTrigger>
              </Dialog>
            </div>
          ) : (
            <div className="space-y-4">
              {brands.map((brand) => (
                <div
                  key={brand.id}
                  className="p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex gap-4 flex-1">
                      {/* Logo da marca */}
                      <div className="flex-shrink-0">
                        {brand.logo ? (
                          <div className="w-12 h-12 rounded-lg overflow-hidden border bg-muted">
                            <Image
                              src={brand.logo}
                              alt={`Logo da ${brand.name}`}
                              width={48}
                              height={48}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-12 h-12 rounded-lg border bg-muted flex items-center justify-center">
                            <ImageIcon className="h-5 w-5 text-muted-foreground" />
                          </div>
                        )}
                      </div>

                      {/* Informações da marca */}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{brand.name}</h3>
                          {brand.industry && (
                            <Badge variant="outline" className="text-xs">
                              {brand.industry}
                            </Badge>
                          )}
                          <Badge 
                            variant={brand.status === 'active' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {brand.status === 'active' ? 'Ativa' : 'Inativa'}
                          </Badge>
                        </div>
                        
                        {brand.description && (
                          <p className="text-sm text-muted-foreground mb-3">
                            {brand.description}
                          </p>
                        )}
                        
                        <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                          {brand.website && (
                            <div className="flex items-center gap-1">
                              <Globe className="h-3 w-3" />
                              <span>{brand.website}</span>
                            </div>
                          )}
                          {brand.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              <span>{brand.email}</span>
                            </div>
                          )}
                          {brand.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <span>{brand.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteBrand(brand.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 

