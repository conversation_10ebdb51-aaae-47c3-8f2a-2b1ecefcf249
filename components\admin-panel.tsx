"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CategoryManager } from "@/components/category-manager";
import { BrandManager } from "@/components/brand-manager";

export default function AdminPanel() {
  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
      <Tabs defaultValue="categories" className="w-full">
        <div className="border-b border-gray-200 px-4">
          <TabsList className="bg-transparent">
            <TabsTrigger 
              value="categories" 
              className="text-sm py-3 data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
            >
              Categorias
            </TabsTrigger>
            <TabsTrigger 
              value="brands" 
              className="text-sm py-3 data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
            >
              <PERSON><PERSON>
            </TabsTrigger>

            <TabsTrigger 
              value="settings" 
              className="text-sm py-3 data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
            >
              Configurações
            </TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="categories" className="p-4">
          <CategoryManager />
        </TabsContent>
        
        <TabsContent value="brands" className="p-4">
          <BrandManager />
        </TabsContent>
        

        
        <TabsContent value="settings" className="p-4">
          <div className="text-center py-8 text-gray-600">
            <p>Configurações do sistema serão implementadas em breve.</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}


