import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

interface RouteParams {
  params: Promise<{
    token: string;
  }>;
}

/**
 * GET /api/share-tokens/[token]/validate - Validar token de compartilhamento
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const { token } = resolvedParams;

    console.log('[API_VALIDATE_TOKEN] Validando token:', token);

    // Buscar token no banco
    const tokenDoc = await db.collection('share_tokens').doc(token).get();

    if (!tokenDoc.exists) {
      console.log('[API_VALIDATE_TOKEN] Token não encontrado');
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token não encontrado',
          code: 'TOKEN_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    const tokenData = tokenDoc.data()!;

    // Verificar se o token está ativo
    if (!tokenData.isActive) {
      console.log('[API_VALIDATE_TOKEN] Token inativo');
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token foi revogado',
          code: 'TOKEN_REVOKED'
        },
        { status: 403 }
      );
    }

    // Verificar se o token não expirou
    const now = new Date();
    const expiresAt = tokenData.expiresAt.toDate();
    
    if (now > expiresAt) {
      console.log('[API_VALIDATE_TOKEN] Token expirado');
      
      // Marcar token como inativo
      await db.collection('share_tokens').doc(token).update({
        isActive: false,
        expiredAt: now
      });

      return NextResponse.json(
        { 
          valid: false, 
          error: 'Token expirado',
          code: 'TOKEN_EXPIRED',
          expiresAt: expiresAt.toISOString()
        },
        { status: 410 }
      );
    }

    // Token válido - incrementar contador de acesso
    const newAccessCount = (tokenData.accessCount || 0) + 1;
    await db.collection('share_tokens').doc(token).update({
      accessCount: newAccessCount,
      lastAccessed: now
    });

    console.log('[API_VALIDATE_TOKEN] Token válido:', {
      listId: tokenData.listId,
      itemCount: tokenData.listData?.length || 0,
      accessCount: newAccessCount
    });

    // Retornar dados do token
    return NextResponse.json({
      valid: true,
      data: {
        listId: tokenData.listId,
        listName: tokenData.listName,
        listType: tokenData.listType,
        influencerIds: tokenData.listData || [],
        itemCount: tokenData.listData?.length || 0,
        createdAt: tokenData.createdAt.toDate().toISOString(),
        expiresAt: expiresAt.toISOString(),
        accessCount: newAccessCount,
        // Não retornar dados sensíveis como createdBy
      }
    });

  } catch (error: any) {
    console.error('[API_VALIDATE_TOKEN] Erro:', {
      token: resolvedParams?.token,
      error: error.message,
      stack: error.stack
    });

    return NextResponse.json(
      { 
        valid: false, 
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/share-tokens/[token]/validate - Registrar interação com token
 */
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const { token } = resolvedParams;
    const { action, metadata } = await req.json();

    console.log('[API_TOKEN_INTERACTION] Registrando interação:', {
      token,
      action,
      metadata
    });

    // Verificar se o token existe e está válido
    const tokenDoc = await db.collection('share_tokens').doc(token).get();

    if (!tokenDoc.exists || !tokenDoc.data()?.isActive) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Token inválido' 
        },
        { status: 404 }
      );
    }

    const tokenData = tokenDoc.data()!;

    // Verificar expiração
    const now = new Date();
    const expiresAt = tokenData.expiresAt.toDate();
    
    if (now > expiresAt) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Token expirado' 
        },
        { status: 410 }
      );
    }

    // Registrar interação (opcional - para analytics)
    const interactionData = {
      token,
      action,
      timestamp: now,
      metadata: metadata || {},
      ip: req.headers.get('x-forwarded-for') || 
          req.headers.get('x-real-ip') || 
          'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown'
    };

    // Salvar interação em subcoleção (opcional)
    await db.collection('share_tokens')
      .doc(token)
      .collection('interactions')
      .add(interactionData);

    // Atualizar última interação no documento principal
    await db.collection('share_tokens').doc(token).update({
      lastAccessed: now,
      [`interactions.${action}`]: (tokenData.interactions?.[action] || 0) + 1
    });

    return NextResponse.json({
      success: true,
      message: 'Interação registrada'
    });

  } catch (error: any) {
    console.error('[API_TOKEN_INTERACTION] Erro:', {
      token: resolvedParams?.token,
      error: error.message
    });

    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro interno do servidor' 
      },
      { status: 500 }
    );
  }
}
