import { auth } from '@clerk/nextjs/server';
import { NextRequest } from 'next/server';

// Substituto para withAuth do WorkOS
export async function getAuthenticatedUser() {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Usuario não autenticado');
  }
  
  return {
    userId,
    user: {
      id: userId
    }
  };
}

// Helper para verificar autenticação em rotas de API
export async function requireAuth() {
  try {
    return await getAuthenticatedUser();
  } catch (error) {
    return null;
  }
}

// Middleware para rotas protegidas
export function withAuthHandler(handler: (req: NextRequest, context: any) => Promise<Response>) {
  return async (req: NextRequest, context: any) => {
    const authResult = await requireAuth();
    
    if (!authResult) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Adicionar informações do usuário ao contexto
    const newContext = {
      ...context,
      user: authResult.user,
      userId: authResult.userId
    };
    
    return handler(req, newContext);
  };
} 

