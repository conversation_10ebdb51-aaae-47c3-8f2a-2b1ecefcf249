// SCRIPT DE MIGRAÇÃO PARA DENORMALIZAÇÃO DE DADOS FINANCEIROS
// Atualiza todos os influenciadores existentes com dados financeiros denormalizados

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, where, updateDoc, doc } from 'firebase/firestore';
import { FinancialDenormalizationService } from '../lib/financial-denormalization-service';
import { FinancialCacheService } from '../lib/firebase-financial-cache';

// Configuração do Firebase (substitua pelos seus dados)
const firebaseConfig = {
  // Adicione sua configuração do Firebase aqui
  projectId: process.env.FIREBASE_PROJECT_ID || 'deumatch-demo'
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Interface para progresso da migração
interface MigrationProgress {
  total: number;
  processed: number;
  successful: number;
  failed: number;
  errors: Array<{ influencerId: string; error: string }>;
}

/**
 * Migra dados financeiros para denormalização em lotes
 */
async function migrateFinancialDenormalization(): Promise<MigrationProgress> {
  console.log('🚀 Iniciando migração de denormalização de dados financeiros...');
  
  const progress: MigrationProgress = {
    total: 0,
    processed: 0,
    successful: 0,
    failed: 0,
    errors: []
  };

  try {
    // 1. Buscar todos os influenciadores
    console.log('📊 Buscando todos os influenciadores...');
    const influencersSnapshot = await getDocs(collection(db, 'influencers'));
    const influencers = influencersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    progress.total = influencers.length;
    console.log(`✅ Encontrados ${progress.total} influenciadores para processar`);

    // 2. Buscar todos os dados financeiros
    console.log('💰 Buscando todos os dados financeiros...');
    const financialsSnapshot = await getDocs(collection(db, 'influencer_financials'));
    const financialsMap = new Map();
    
    financialsSnapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.influencerId) {
        financialsMap.set(data.influencerId, {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate()
        });
      }
    });

    console.log(`✅ Encontrados ${financialsMap.size} registros financeiros`);

    // 3. Processar em lotes
    const BATCH_SIZE = 10;
    const batches = [];
    
    for (let i = 0; i < influencers.length; i += BATCH_SIZE) {
      batches.push(influencers.slice(i, i + BATCH_SIZE));
    }

    console.log(`🔄 Processando ${batches.length} lotes de ${BATCH_SIZE} influenciadores...`);

    // 4. Processar cada lote
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      
      console.log(`\n📦 Processando lote ${batchIndex + 1}/${batches.length}...`);

      // Processar influenciadores do lote em paralelo
      const batchPromises = batch.map(async (influencer) => {
        try {
          progress.processed++;
          
          // Verificar se existe dados financeiros para este influenciador
          const financialData = financialsMap.get(influencer.id);
          
          if (!financialData) {
            // Se não tem dados financeiros, remover pricing (se existir)
            await updateDoc(doc(db, 'influencers', influencer.id), {
              pricing: null
            });
            
            console.log(`❌ Influenciador ${influencer.id} - sem dados financeiros (pricing removido)`);
            progress.successful++;
            return;
          }

          // Calcular dados denormalizados
          const avgPrice = calculateAveragePrice(financialData);
          const priceRange = calculatePriceRange(avgPrice);
          const mainService = getMainService(financialData);

          const denormalizedPricing = {
            hasFinancialData: true,
            priceRange,
            avgPrice,
            mainService,
            lastPriceUpdate: financialData.updatedAt || new Date(),
            isNegotiable: avgPrice > 0
          };

          // Atualizar documento do influenciador
          await updateDoc(doc(db, 'influencers', influencer.id), {
            pricing: denormalizedPricing
          });

          console.log(`✅ Influenciador ${influencer.id} - pricing atualizado (${priceRange}, R$ ${avgPrice.toFixed(2)})`);
          progress.successful++;

        } catch (error) {
          progress.failed++;
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
          progress.errors.push({
            influencerId: influencer.id,
            error: errorMessage
          });
          
          console.error(`❌ Erro ao processar influenciador ${influencer.id}:`, errorMessage);
        }
      });

      // Aguardar conclusão do lote
      await Promise.all(batchPromises);

      // Pequeno delay entre lotes para não sobrecarregar o Firestore
      if (batchIndex < batches.length - 1) {
        console.log('⏳ Aguardando 2 segundos antes do próximo lote...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Mostrar progresso
      const progressPercent = Math.round((progress.processed / progress.total) * 100);
      console.log(`📊 Progresso: ${progress.processed}/${progress.total} (${progressPercent}%) - Sucessos: ${progress.successful}, Falhas: ${progress.failed}`);
    }

    // 5. Relatório final
    console.log('\n🎉 Migração concluída!');
    console.log(`📊 Resumo:`);
    console.log(`   Total: ${progress.total}`);
    console.log(`   Processados: ${progress.processed}`);
    console.log(`   Sucessos: ${progress.successful}`);
    console.log(`   Falhas: ${progress.failed}`);
    console.log(`   Taxa de sucesso: ${Math.round((progress.successful / progress.total) * 100)}%`);

    if (progress.errors.length > 0) {
      console.log('\n❌ Erros encontrados:');
      progress.errors.forEach(error => {
        console.log(`   ${error.influencerId}: ${error.error}`);
      });
    }

    return progress;

  } catch (error) {
    console.error('💥 Erro fatal na migração:', error);
    throw error;
  }
}

/**
 * Funções auxiliares para cálculos (replicadas do serviço)
 */

// Configuração de faixas de preço
const PRICE_RANGES = {
  low: { min: 0, max: 500 },
  medium: { min: 501, max: 2000 },
  high: { min: 2001, max: 5000 },
  premium: { min: 5001, max: Infinity }
};

function calculatePriceRange(avgPrice: number): 'low' | 'medium' | 'high' | 'premium' {
  if (avgPrice <= PRICE_RANGES.low.max) return 'low';
  if (avgPrice <= PRICE_RANGES.medium.max) return 'medium';
  if (avgPrice <= PRICE_RANGES.high.max) return 'high';
  return 'premium';
}

function calculateAveragePrice(financialData: any): number {
  const prices = [];
  
  if (financialData.prices?.instagramStory?.price > 0) {
    prices.push(financialData.prices.instagramStory.price);
  }
  if (financialData.prices?.instagramReel?.price > 0) {
    prices.push(financialData.prices.instagramReel.price);
  }
  if (financialData.prices?.tiktokVideo?.price > 0) {
    prices.push(financialData.prices.tiktokVideo.price);
  }
  if (financialData.prices?.youtubeDedicated?.price > 0) {
    prices.push(financialData.prices.youtubeDedicated.price);
  }
  if (financialData.prices?.youtubeShorts?.price > 0) {
    prices.push(financialData.prices.youtubeShorts.price);
  }
  if (financialData.prices?.youtubeInsertion?.price > 0) {
    prices.push(financialData.prices.youtubeInsertion.price);
  }

  return prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0;
}

function getMainService(financialData: any): {
  platform: 'instagram' | 'tiktok' | 'youtube';
  serviceType: string;
  price: number;
} {
  const services = [
    {
      platform: 'instagram' as const,
      serviceType: 'story',
      price: financialData.prices?.instagramStory?.price || 0
    },
    {
      platform: 'instagram' as const,
      serviceType: 'reel',
      price: financialData.prices?.instagramReel?.price || 0
    },
    {
      platform: 'tiktok' as const,
      serviceType: 'video',
      price: financialData.prices?.tiktokVideo?.price || 0
    },
    {
      platform: 'youtube' as const,
      serviceType: 'dedicated',
      price: financialData.prices?.youtubeDedicated?.price || 0
    },
    {
      platform: 'youtube' as const,
      serviceType: 'shorts',
      price: financialData.prices?.youtubeShorts?.price || 0
    },
    {
      platform: 'youtube' as const,
      serviceType: 'insertion',
      price: financialData.prices?.youtubeInsertion?.price || 0
    }
  ];

  // Ordenar por preço decrescente e pegar o primeiro
  services.sort((a, b) => b.price - a.price);
  
  return services[0].price > 0 ? services[0] : {
    platform: 'instagram',
    serviceType: 'story',
    price: 0
  };
}

/**
 * Script para validar a migração
 */
async function validateMigration(): Promise<void> {
  console.log('🔍 Validando migração...');

  try {
    // Contar influenciadores com pricing
    const influencersWithPricing = await getDocs(
      query(collection(db, 'influencers'), where('pricing.hasFinancialData', '==', true))
    );

    // Contar total de dados financeiros
    const totalFinancials = await getDocs(collection(db, 'influencer_financials'));

    console.log(`✅ Validação concluída:`);
    console.log(`   Influenciadores com pricing: ${influencersWithPricing.size}`);
    console.log(`   Total de dados financeiros: ${totalFinancials.size}`);
    console.log(`   Taxa de conversão: ${Math.round((influencersWithPricing.size / totalFinancials.size) * 100)}%`);

    // Verificar alguns exemplos
    console.log('\n📋 Exemplos de pricing denormalizado:');
    influencersWithPricing.docs.slice(0, 3).forEach(doc => {
      const data = doc.data();
      console.log(`   ${doc.id}: ${data.pricing?.priceRange} - R$ ${data.pricing?.avgPrice?.toFixed(2)}`);
    });

  } catch (error) {
    console.error('❌ Erro na validação:', error);
  }
}

/**
 * Função principal
 */
async function main() {
  try {
    console.log('🎯 Iniciando processo de migração de denormalização...\n');

    // Executar migração
    const result = await migrateFinancialDenormalization();

    // Validar resultados
    await validateMigration();

    console.log('\n✅ Processo concluído com sucesso!');
    process.exit(0);

  } catch (error) {
    console.error('\n💥 Erro fatal:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

export {
  migrateFinancialDenormalization,
  validateMigration
}; 

