"use client"

import React, { useState } from 'react'
import { FormProvider } from 'react-hook-form'
import { X, User, Smartphone, Tag, BarChart3, Save } from 'lucide-react'
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  She<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { useInfluencerForm } from '@/hooks/use-influencer-form'
import { PersonalInfoSection } from './sections/personal-info-section'
import { SocialPlatformsSection } from './sections/social-platforms-section'
import { CategoriesBusinessSection } from './sections/categories-business-section'

import { MetricsPreviewSection } from './sections/metrics-preview-section'
import type { InfluencerFormData } from '@/types/influencer-form'

interface InfluencerPanelSheetProps {
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  initialData?: Partial<InfluencerFormData>
  onSubmit?: (data: InfluencerFormData) => Promise<void>
  mode?: 'create' | 'edit'
}

export function InfluencerPanelSheet({
  trigger,
  open,
  onOpenChange,
  initialData,
  onSubmit,
  mode = 'create'
}: InfluencerPanelSheetProps) {
  const [activeTab, setActiveTab] = useState('personal')
  
  const {
    form,
    handleSubmit,
    resetForm,
    activePlatforms,
    addPlatform,
    removePlatform,
    availablePlatforms,
    isSubmitting,
    errors,
    isValid
  } = useInfluencerForm({
    initialData,
    onSubmit: async (data) => {
      await onSubmit?.(data)
      onOpenChange?.(false)
      if (mode === 'create') {
        resetForm()
        setActiveTab('personal')
      }
    }
  })

  // 📊 Calcular progresso do formulário
  const calculateProgress = () => {
    const values = form.watch()
    let completed = 0
    let total = 4 // Total de seções (ajustado após remoção das abas de localização e preços)

    // Verificar cada seção
    if (values.personalInfo?.name && values.personalInfo?.age && values.location?.city && values.location?.state && values.contact?.email) completed++
    if (activePlatforms.length > 0) completed++
    if (values.business?.categories && values.business.categories.length > 0) completed++
    


    return Math.round((completed / total) * 100)
  }

  const progress = calculateProgress()

  // 📋 Configuração das abas
  const tabs = [
    {
      id: 'personal',
      label: 'Pessoal',
      icon: User,
      component: PersonalInfoSection,
      description: 'Informações pessoais, localização e contato'
    },
    {
      id: 'platforms',
      label: 'Redes Sociais',
      icon: Smartphone,
      component: SocialPlatformsSection,
      description: 'Plataformas e métricas'
    },
    {
      id: 'categories',
      label: 'Categorias',
      icon: Tag,
      component: CategoriesBusinessSection,
      description: 'Categorias e informações de negócio'
    },

    {
      id: 'metrics',
      label: 'Resumo',
      icon: BarChart3,
      component: MetricsPreviewSection,
      description: 'Métricas e visualização final'
    }
  ]

  const currentTab = tabs.find(tab => tab.id === activeTab)

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      {trigger && <SheetTrigger asChild>{trigger}</SheetTrigger>}
      
      <SheetContent 
        side="right" 
        className="w-full sm:w-[600px] lg:w-[800px] p-0 bg-white dark:bg-gray-950"
      >
        <FormProvider {...form}>
          <div className="flex flex-col h-full">
            {/* 🎯 Header */}
            <SheetHeader className="p-6 pb-4 border-b bg-gray-50 dark:bg-[#080210]/50">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <SheetTitle className="text-xl font-bold">
                    {mode === 'create' ? 'Novo Influenciador' : 'Editar Influenciador'}
                  </SheetTitle>
                  <SheetDescription className="mt-1">
                    {currentTab?.description}
                  </SheetDescription>
                </div>
                
                {/* Progress */}
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="text-sm font-medium">{progress}% completo</div>
                    <Progress value={progress} className="w-24 h-2" />
                  </div>
                </div>
              </div>

              {/* Validation Errors Summary */}
              {Object.keys(errors).length > 0 && (
                <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      {Object.keys(errors).length} campo(s) com erro
                    </span>
                  </div>
                </div>
              )}
            </SheetHeader>

            <div className="flex flex-1 overflow-hidden">
              {/* 🧭 Navigation Sidebar */}
              <div className="w-48 border-r bg-gray-50 dark:bg-[#080210]/30 p-4">
                <nav className="space-y-2">
                  {tabs.map((tab, index) => {
                    const Icon = tab.icon
                    const isActive = activeTab === tab.id
                    const hasError = tab.id !== 'metrics' && Object.keys(errors).some(key => key.startsWith(tab.id))
                    
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`
                          w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all
                          ${isActive 
                            ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700' 
                            : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                          }
                          ${hasError ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20' : ''}
                        `}
                      >
                        <div className="relative">
                          <Icon className="h-4 w-4" />
                          {hasError && (
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                          )}
                        </div>
                        <div>
                          <div className="text-sm font-medium">{tab.label}</div>
                          <div className="text-xs opacity-70">{index + 1}/6</div>
                        </div>
                      </button>
                    )
                  })}
                </nav>

                {/* Platform Pills */}
                {activePlatforms.length > 0 && (
                  <div className="mt-6">
                    <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                      Plataformas Ativas
                    </div>
                    <div className="space-y-1">
                      {activePlatforms.map(platform => (
                        <Badge 
                          key={platform} 
                          variant="secondary" 
                          className="w-full justify-start text-xs"
                        >
                          {(platform as string).charAt(0).toUpperCase() + (platform as string).slice(1)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* 📝 Content Area */}
              <div className="flex-1 flex flex-col">
                <ScrollArea className="flex-1 p-6">
                  <div className="max-w-2xl">
                    {currentTab && (() => {
                      const Component = currentTab.component;
                      
                      // Passar props específicas baseado no componente
                      if (currentTab.id === 'platforms') {
                        return (
                          <Component
                            activePlatforms={activePlatforms}
                            addPlatform={addPlatform}
                            removePlatform={removePlatform}
                            availablePlatforms={availablePlatforms}
                          />
                        );
                      }
                      
                      // Para outros componentes, apenas renderizar sem props específicas
                      return <Component />;
                    })()}
                  </div>
                </ScrollArea>

                {/* 💾 Footer Actions */}
                <div className="border-t bg-gray-50 dark:bg-[#080210]/30 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {/* Navigation */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const currentIndex = tabs.findIndex(tab => tab.id === activeTab)
                          if (currentIndex > 0) {
                            setActiveTab(tabs[currentIndex - 1].id)
                          }
                        }}
                        disabled={tabs.findIndex(tab => tab.id === activeTab) === 0}
                      >
                        Anterior
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const currentIndex = tabs.findIndex(tab => tab.id === activeTab)
                          if (currentIndex < tabs.length - 1) {
                            setActiveTab(tabs[currentIndex + 1].id)
                          }
                        }}
                        disabled={tabs.findIndex(tab => tab.id === activeTab) === tabs.length - 1}
                      >
                        Próximo
                      </Button>
                    </div>

                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        onClick={() => onOpenChange?.(false)}
                        disabled={isSubmitting}
                      >
                        Cancelar
                      </Button>
                      
                      <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting || !isValid}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            Salvando...
                          </div>
                        ) : (
                          <div className="flex items-center text-white gap-2">
                            <Save className="h-4 w-4" />
                            {mode === 'create' ? 'Criar Influenciador' : 'Salvar Alterações'}
                          </div>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </FormProvider>
      </SheetContent>
    </Sheet>
  )
} 

