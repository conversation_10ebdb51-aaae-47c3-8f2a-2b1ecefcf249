import { NextRequest } from 'next/server';
import { withBrandSecurity } from '@/lib/api-middleware';
import { NextResponse } from 'next/server';

// Simulação de dados de negociações (em produção, viria do banco de dados)
const mockNegociacoes = [
  {
    id: 'camp-1',
    brandId: 'brand-1',
    name: 'Stranger Things S5 - Lançamento',
    description: 'Campanha de divulgação da quinta temporada de Stranger Things',
    status: 'active',
    budget: 50000,
    startDate: '2024-01-15',
    endDate: '2024-03-15',
    targetAudience: 'Jovens 16-25 anos',
    platforms: ['Instagram', 'TikTok', 'YouTube'],
    objectives: ['Awareness', 'Engagement'],
    kpis: {
      reach: 1000000,
      engagement: 50000,
      conversions: 5000
    },
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  },
  {
    id: 'camp-2',
    brandId: 'brand-2',
    name: 'Coca-Cola Verão 2024',
    description: 'Campanha de verão focada em momentos de diversão',
    status: 'active',
    budget: 80000,
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    targetAudience: 'Jovens 18-30 anos',
    platforms: ['Instagram', 'TikTok'],
    objectives: ['Brand Awareness', 'Sales'],
    kpis: {
      reach: 2000000,
      engagement: 100000,
      conversions: 10000
    },
    createdAt: '2023-12-15T09:00:00Z',
    updatedAt: '2024-01-10T16:45:00Z'
  },
  {
    id: 'camp-3',
    brandId: 'brand-1',
    name: 'Netflix Documentários',
    description: 'Divulgação de novos documentários originais',
    status: 'completed',
    budget: 30000,
    startDate: '2023-11-01',
    endDate: '2023-12-31',
    targetAudience: 'Adultos 25-45 anos',
    platforms: ['Instagram', 'YouTube'],
    objectives: ['Awareness', 'Subscriptions'],
    kpis: {
      reach: 800000,
      engagement: 40000,
      conversions: 3000
    },
    createdAt: '2023-10-15T11:30:00Z',
    updatedAt: '2024-01-05T10:15:00Z'
  }
];

// GET - Listar negociações da marca atual
export const GET = withBrandSecurity('read', async (req: NextRequest, userId: string) => {
  try {
    // Aplicar filtros se fornecidos
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const platform = searchParams.get('platform');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    let filteredNegociacoes = mockNegociacoes;
    
    if (status) {
      filteredNegociacoes = filteredNegociacoes.filter(c => c.status === status);
    }
    
    if (platform) {
      filteredNegociacoes = filteredNegociacoes.filter(c => 
        c.platforms.includes(platform)
      );
    }
    
    if (startDate) {
      filteredNegociacoes = filteredNegociacoes.filter(c => 
        new Date(c.startDate) >= new Date(startDate)
      );
    }
    
    if (endDate) {
      filteredNegociacoes = filteredNegociacoes.filter(c => 
        new Date(c.endDate) <= new Date(endDate)
      );
    }
    
    // Estatísticas da marca
    const stats = {
      total: brandNegociacoes.length,
      active: brandNegociacoes.filter(c => c.status === 'active').length,
      completed: brandNegociacoes.filter(c => c.status === 'completed').length,
      draft: brandNegociacoes.filter(c => c.status === 'draft').length,
      totalBudget: brandNegociacoes.reduce((sum, c) => sum + c.budget, 0),
      activeBudget: brandNegociacoes
        .filter(c => c.status === 'active')
        .reduce((sum, c) => sum + c.budget, 0)
    };
    
    return NextResponse.json({
      negociacoes: filteredNegociacoes,
      stats,
      brandId,
      userId
    });
  } catch (error) {
    console.error('Erro ao buscar negociações:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

// POST - Criar nova negociação
export const POST = withBrandSecurity('write', async (req: NextRequest, userId: string) => {
  try {
    const body = await req.json();
    
    // Validar dados obrigatórios
    const requiredFields = ['name', 'description', 'budget', 'startDate', 'endDate', 'targetAudience', 'platforms', 'objectives'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Campos obrigatórios faltando: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }
    
    // Validar datas
    const startDate = new Date(body.startDate);
    const endDate = new Date(body.endDate);
    
    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Data de início deve ser anterior à data de fim' },
        { status: 400 }
      );
    }
    
    // Criar nova negociação
    const novaNegociacao = {
      id: `neg-${Date.now()}`,
      name: body.name,
      description: body.description,
      status: body.status || 'draft',
      budget: Number(body.budget),
      startDate: body.startDate,
      endDate: body.endDate,
      targetAudience: body.targetAudience,
      platforms: Array.isArray(body.platforms) ? body.platforms : [body.platforms],
      objectives: Array.isArray(body.objectives) ? body.objectives : [body.objectives],
      kpis: body.kpis || {
        reach: 0,
        engagement: 0,
        conversions: 0
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: userId
    };
    
    // Em produção, salvar no banco de dados
    mockNegociacoes.push(novaNegociacao);
    
    return NextResponse.json({
      message: 'Negociação criada com sucesso',
      negociacao: novaNegociacao
    }, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar negociação:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

