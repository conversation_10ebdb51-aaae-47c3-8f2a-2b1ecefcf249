# 📅 FASE 2: Estrutura de Dados (2-3 dias)

## 🎯 Objetivo
Atualizar todas as interfaces TypeScript e estruturas de dados para suportar isolamento por usuário.

## 📝 Tarefas Detalhadas

### 2.1 Atualização de Interfaces TypeScript

#### 2.1.1 Interface Base para Isolamento
```typescript
// types/base.ts
export interface BaseDocument {
  id: string;
  userId: string;          // 🆕 Campo obrigatório
  createdAt: Date;
  updatedAt: Date;
}

export interface UserOwnedDocument extends BaseDocument {
  createdBy: string;       // ID do usuário que criou
  updatedBy: string;       // ID do usuário que atualizou
}
```

#### 2.1.2 Atualização de Brand
```typescript
// types/brand.ts
export interface Brand extends BaseDocument {
  name: string;
  logo?: string;
  logoBackgroundColor?: string;
  industry?: string;
  website?: string;
  contactEmail?: string;
  contactPhone?: string;
  contactName?: string;
  budget?: number;
  notes?: string;
  isActive: boolean;
}

export interface CreateBrandData {
  // userId será adicionado automaticamente pela API
  name: string;
  logo?: string;
  logoBackgroundColor?: string;
  industry?: string;
  website?: string;
  contactEmail?: string;
  contactPhone?: string;
  contactName?: string;
  budget?: number;
  notes?: string;
}
```

#### 2.1.3 Atualização de Campaign
```typescript
// types/campaign.ts
export interface Campaign extends BaseDocument {
  name: string;
  description?: string;
  brandId: string;         // Deve pertencer ao mesmo userId
  brandName?: string;
  startDate: Date;
  endDate: Date;
  budget: number;
  objectives?: string;
  targetAudience?: string;
  deliverables?: string;
  notes?: string;
  status: CampaignStatus;
  influencers: CampaignInfluencer[];
  priority: 'low' | 'medium' | 'high';
  tags: string[];
  progress: number;
}

// Validação: brandId deve pertencer ao mesmo userId
export interface CreateCampaignData {
  name: string;
  description?: string;
  brandId: string;
  startDate: Date;
  endDate: Date;
  budget: number;
  objectives?: string;
  targetAudience?: string;
  deliverables?: string;
  notes?: string;
  status?: CampaignStatus;
  influencers: CampaignInfluencer[];
  priority?: 'low' | 'medium' | 'high';
  tags?: string[];
}
```

#### 2.1.4 Atualização de Influencer
```typescript
// types/influencer.ts
export interface Influencer extends BaseDocument {
  name: string;
  email: string;
  phone?: string;
  location?: string;
  bio?: string;
  avatar?: string;
  category: string;
  platforms: {
    instagram?: SocialPlatform;
    tiktok?: SocialPlatform;
    youtube?: SocialPlatform;
    twitter?: SocialPlatform;
  };
  rating: number;
  isVerified: boolean;
  isAvailable: boolean;
  notes?: string;
  tags: string[];
}

export interface CreateInfluencerData {
  // userId será adicionado automaticamente pela API
  name: string;
  email: string;
  phone?: string;
  location?: string;
  bio?: string;
  avatar?: string;
  category: string;
  platforms: {
    instagram?: Omit<SocialPlatform, 'id'>;
    tiktok?: Omit<SocialPlatform, 'id'>;
    youtube?: Omit<SocialPlatform, 'id'>;
    twitter?: Omit<SocialPlatform, 'id'>;
  };
  rating?: number;
  isVerified?: boolean;
  isAvailable?: boolean;
  notes?: string;
  tags?: string[];
}
```

### 2.2 Validação com Zod

#### 2.2.1 Schemas Base
```typescript
// lib/validation/base.ts
import { z } from 'zod';

export const BaseDocumentSchema = z.object({
  id: z.string(),
  userId: z.string().min(1, 'userId é obrigatório'),
  createdAt: z.date(),
  updatedAt: z.date()
});

export const UserOwnedDocumentSchema = BaseDocumentSchema.extend({
  createdBy: z.string().min(1, 'createdBy é obrigatório'),
  updatedBy: z.string().min(1, 'updatedBy é obrigatório')
});

// Utilitário para validar ownership
export function validateOwnership(document: any, userId: string) {
  if (document.userId !== userId) {
    throw new Error('Usuário não tem permissão para acessar este recurso');
  }
  return true;
}
```

#### 2.2.2 Schema para Brand
```typescript
// lib/validation/brand.ts
import { z } from 'zod';
import { BaseDocumentSchema } from './base';

export const BrandSchema = BaseDocumentSchema.extend({
  name: z.string().min(1, 'Nome da marca é obrigatório'),
  logo: z.string().url().optional(),
  logoBackgroundColor: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().url().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),
  contactName: z.string().optional(),
  budget: z.number().min(0).optional(),
  notes: z.string().optional(),
  isActive: z.boolean()
});

export const CreateBrandSchema = z.object({
  name: z.string().min(1, 'Nome da marca é obrigatório'),
  logo: z.string().url().optional(),
  logoBackgroundColor: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().url().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),
  contactName: z.string().optional(),
  budget: z.number().min(0).optional(),
  notes: z.string().optional()
});

export type CreateBrandInput = z.infer<typeof CreateBrandSchema>;
```

#### 2.2.3 Schema para Campaign
```typescript
// lib/validation/campaign.ts
import { z } from 'zod';
import { BaseDocumentSchema } from './base';

export const CampaignSchema = BaseDocumentSchema.extend({
  name: z.string().min(1, 'Nome da campanha é obrigatório'),
  description: z.string().optional(),
  brandId: z.string().min(1, 'Brand ID é obrigatório'),
  brandName: z.string().optional(),
  startDate: z.date(),
  endDate: z.date(),
  budget: z.number().min(0, 'Orçamento deve ser positivo'),
  objectives: z.string().optional(),
  targetAudience: z.string().optional(),
  deliverables: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']),
  priority: z.enum(['low', 'medium', 'high']),
  tags: z.array(z.string()),
  progress: z.number().min(0).max(100)
});

export const CreateCampaignSchema = z.object({
  name: z.string().min(1, 'Nome da campanha é obrigatório'),
  description: z.string().optional(),
  brandId: z.string().min(1, 'Brand ID é obrigatório'),
  startDate: z.date(),
  endDate: z.date(),
  budget: z.number().min(0, 'Orçamento deve ser positivo'),
  objectives: z.string().optional(),
  targetAudience: z.string().optional(),
  deliverables: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']).optional(),
  priority: z.enum(['low', 'medium', 'high']).optional(),
  tags: z.array(z.string()).optional()
}).refine(data => data.endDate > data.startDate, {
  message: 'Data de fim deve ser posterior à data de início',
  path: ['endDate']
});
```

### 2.3 Utilitários de Isolamento

#### 2.3.1 Utilitários de Validação
```typescript
// lib/utils/isolation.ts
import { BaseDocument } from '@/types/base';

export class IsolationUtils {
  // Validar se documento pertence ao usuário
  static validateOwnership(document: BaseDocument, userId: string): boolean {
    return document.userId === userId;
  }

  // Filtrar array de documentos por usuário
  static filterByUser<T extends BaseDocument>(documents: T[], userId: string): T[] {
    return documents.filter(doc => doc.userId === userId);
  }

  // Adicionar userId automaticamente nos dados de criação
  static addUserId<T>(data: T, userId: string): T & { userId: string } {
    return {
      ...data,
      userId,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  // Validar relacionamento entre entidades do mesmo usuário
  static validateRelationship(
    parentDocument: BaseDocument,
    childDocument: BaseDocument
  ): boolean {
    return parentDocument.userId === childDocument.userId;
  }

  // Preparar dados para criação com userId
  static prepareCreateData<T>(
    data: T,
    userId: string,
    createdBy?: string
  ): T & BaseDocument {
    const now = new Date();
    return {
      ...data,
      id: '', // Será gerado pelo Firebase
      userId,
      createdBy: createdBy || userId,
      updatedBy: createdBy || userId,
      createdAt: now,
      updatedAt: now
    } as T & BaseDocument;
  }

  // Preparar dados para atualização
  static prepareUpdateData<T>(
    data: T,
    updatedBy: string
  ): T & { updatedAt: Date; updatedBy: string } {
    return {
      ...data,
      updatedAt: new Date(),
      updatedBy
    };
  }
}
```

#### 2.3.2 Middleware de Validação
```typescript
// lib/middleware/validation.ts
import { NextRequest } from 'next/server';
import { z } from 'zod';

export function validateRequestBody<T>(schema: z.ZodSchema<T>) {
  return async (req: NextRequest): Promise<T> => {
    try {
      const body = await req.json();
      return schema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Dados inválidos: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  };
}

export function validateOwnershipMiddleware(
  getDocument: (id: string) => Promise<BaseDocument | null>
) {
  return async (documentId: string, userId: string): Promise<void> => {
    const document = await getDocument(documentId);
    
    if (!document) {
      throw new Error('Documento não encontrado');
    }

    if (!IsolationUtils.validateOwnership(document, userId)) {
      throw new Error('Usuário não tem permissão para acessar este recurso');
    }
  };
}
```

## 📊 Deliverables da Fase 2

### 1. Interfaces TypeScript Atualizadas
- [x] `types/base.ts` - Interface base para isolamento
- [x] `types/brand.ts` - Brand com userId
- [x] `types/campaign.ts` - Campaign com userId e validação de brandId
- [x] `types/influencer.ts` - Influencer com userId
- [ ] `types/group.ts` - Group com userId
- [ ] `types/note.ts` - Note com userId

### 2. Schemas Zod Implementados
- [x] `lib/validation/base.ts` - Schemas base
- [x] `lib/validation/brand.ts` - Validação de brands
- [x] `lib/validation/campaign.ts` - Validação de campaigns
- [ ] `lib/validation/influencer.ts` - Validação de influencers
- [ ] `lib/validation/group.ts` - Validação de groups
- [ ] `lib/validation/note.ts` - Validação de notes

### 3. Utilitários de Isolamento
- [x] `lib/utils/isolation.ts` - Utilitários principais
- [x] `lib/middleware/validation.ts` - Middleware de validação

### 4. Documentação de Estruturas
- [x] Mapeamento de relacionamentos atualizados
- [x] Guia de uso dos novos utilitários
- [x] Exemplos de implementação

## ✅ Critérios de Aceitação

- [ ] Todas as interfaces têm campo `userId` obrigatório
- [ ] Schemas Zod validam ownership e relacionamentos
- [ ] Utilitários de isolamento implementados e testados
- [ ] Validação de relacionamentos entre entidades funcionando
- [ ] Documentação atualizada
- [ ] Testes unitários dos utilitários criados

## 🚀 Próxima Fase

Após completar a estrutura de dados, seguir para [Fase 3: Firebase Security Rules](./fase-3-security-rules.md) 