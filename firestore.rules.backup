rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // ========================================
    // 🔒 FASE 3.1: FUNÇÕES UTILITÁRIAS BASE
    // ========================================
    
    // Verificar se usuário está autenticado
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Verificar se usuário é proprietário do documento
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Verificar se userId está correto nos dados da requisição
    function hasCorrectUserId() {
      return isAuthenticated() && 
             request.resource.data.userId == request.auth.uid;
    }
    
    // Verificar se pode acessar documento baseado no userId
    function canAccessDocument() {
      return isAuthenticated() && 
             resource.data.userId == request.auth.uid;
    }
    
    // Verificar se userId não foi alterado em update
    function userIdNotChanged() {
      return request.resource.data.userId == resource.data.userId;
    }
    
    // Verificar se é admin (implementar conforme necessário)
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.admin == true;
    }
    
    // Verificar se dados obrigatórios estão presentes
    function hasRequiredFields(fields) {
      return request.resource.data.keys().hasAll(fields);
    }
    
    // Verificar se documento existe
    function documentExists(path) {
      return exists(/databases/$(database)/documents/$(path));
    }
    
    // Obter documento
    function getDocument(path) {
      return get(/databases/$(database)/documents/$(path));
    }
    
    // Verificar se brand pertence ao usuário atual
    function brandBelongsToUser(brandId) {
      return documentExists('brands/' + brandId) &&
             getDocument('brands/' + brandId).data.userId == request.auth.uid;
    }
    
    // Verificar se influencer pertence ao usuário atual  
    function influencerBelongsToUser(influencerId) {
      return documentExists('influencers/' + influencerId) &&
             getDocument('influencers/' + influencerId).data.userId == request.auth.uid;
    }
    
    // Verificar se campaign pertence ao usuário atual
    function campaignBelongsToUser(campaignId) {
      return documentExists('campaigns/' + campaignId) &&
             getDocument('campaigns/' + campaignId).data.userId == request.auth.uid;
    }
    
    // Verificar se dados de criação são válidos
    function isValidCreate() {
      return hasCorrectUserId() &&
             hasRequiredFields(['userId', 'createdAt', 'updatedAt']);
    }
    
    // Verificar se dados de update são válidos
    function isValidUpdate() {
      return canAccessDocument() &&
             userIdNotChanged() &&
             request.resource.data.updatedAt is timestamp;
    }
    
    // Verificar se operação de delete é válida
    function isValidDelete() {
      return canAccessDocument();
    }
    
    // Verificar tamanho de string
    function isValidStringLength(field, minLength, maxLength) {
      return request.resource.data[field] is string &&
             request.resource.data[field].size() >= minLength &&
             request.resource.data[field].size() <= maxLength;
    }
    
    // Verificar se email é válido (formato básico)
    function isValidEmail(email) {
      return email is string && email.matches('.*@.*\\..*');
    }
    
    // Verificar se URL é válida (formato básico)
    function isValidUrl(url) {
      return url is string && 
             (url.matches('https://.*') || url.matches('http://.*'));
    }

    // ========================================
    // 🔒 FASE 3.2: REGRAS POR COLEÇÃO
    // ========================================
    
    // USUÁRIOS - Acesso total ao próprio documento
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin();
    }
    
    // BRANDS - Isolamento completo por usuário
    match /brands/{brandId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('name', 2, 100);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // INFLUENCERS - Isolamento completo por usuário
    match /influencers/{influencerId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('name', 2, 100) &&
                    isValidStringLength('country', 2, 50) &&
                    isValidStringLength('state', 2, 50) &&
                    isValidStringLength('city', 2, 50);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // CAMPAIGNS - Isolamento + validação de brand ownership
    match /campaigns/{campaignId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('name', 2, 100) &&
                    'brandId' in request.resource.data &&
                    brandBelongsToUser(request.resource.data.brandId) &&
                    request.resource.data.startDate < request.resource.data.endDate;
      allow update: if isValidUpdate() &&
                    ('brandId' in request.resource.data ? 
                     brandBelongsToUser(request.resource.data.brandId) : true);
      allow delete: if isValidDelete();
    }
    
    // INFLUENCER FINANCIALS - Isolamento por usuário
    match /influencer_financials/{financialId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    'influencerId' in request.resource.data &&
                    influencerBelongsToUser(request.resource.data.influencerId);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // NOTES - Isolamento por usuário  
    match /notes/{noteId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('title', 1, 200) &&
                    'influencerId' in request.resource.data &&
                    influencerBelongsToUser(request.resource.data.influencerId);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // TAGS - Isolamento por usuário
    match /tags/{tagId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('name', 1, 50);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // GROUPS - Isolamento por usuário com validação de influencers
    match /groups/{groupId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('name', 2, 100);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // PROPOSALS - Isolamento por usuário com validação de brand
    match /proposals/{proposalId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    'brandId' in request.resource.data &&
                    brandBelongsToUser(request.resource.data.brandId);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // FILTERS - Isolamento por usuário
    match /filters/{filterId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    isValidStringLength('name', 2, 100);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // CATEGORIES - Dados globais (apenas admin pode modificar)
    match /categories/{categoryId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // BRAND_INFLUENCERS - Relacionamento com validação dupla
    match /brand_influencers/{relationId} {
      allow read: if canAccessDocument();
      allow create: if isValidCreate() &&
                    'brandId' in request.resource.data &&
                    'influencerId' in request.resource.data &&
                    brandBelongsToUser(request.resource.data.brandId) &&
                    influencerBelongsToUser(request.resource.data.influencerId);
      allow update: if isValidUpdate();
      allow delete: if isValidDelete();
    }
    
    // ========================================
    // 🚫 REGRA PADRÃO - NEGAR TUDO
    // ========================================
    
    // Qualquer documento não coberto pelas regras acima é negado
    match /{document=**} {
      allow read, write: if false;
    }
  }
}