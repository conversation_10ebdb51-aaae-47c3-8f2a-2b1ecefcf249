import { NextRequest, NextResponse } from 'next/server';

// Configuração do Firebase
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Função para inicializar Firebase com importação dinâmica
async function initializeFirebase() {
  const { initializeApp, getApps } = await import('firebase/app');
  if (!getApps().length) {
    initializeApp(firebaseConfig);
  }
}

// Função para obter storage com importação dinâmica
async function getFirebaseStorage() {
  const { getStorage } = await import('firebase/storage');
  return getStorage();
}

// Função para criar referência com importação dinâmica
async function createStorageRef(storage: any, path: string) {
  const { ref } = await import('firebase/storage');
  return ref(storage, path);
}

// Função para obter URL de download com importação dinâmica
async function getStorageDownloadURL(storageRef: any) {
  const { getDownloadURL } = await import('firebase/storage');
  return getDownloadURL(storageRef);
}

/**
 * API Route para servir imagens de influenciadores do Firebase Storage
 * Redireciona URLs legacy /uploads/influencers/[filename] para Firebase Storage
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    // Inicializar Firebase
    await initializeFirebase();
    
    // Reconstituir o caminho do arquivo
    const filePath = params.path.join('/');
    
    console.log(`🔍 Buscando imagem: influencers/${filePath}`);
    
    // Obter storage e criar referência para o arquivo no Firebase Storage
    const storage = await getFirebaseStorage();
    const fileRef = await createStorageRef(storage, `influencers/${filePath}`);
    
    try {
      // Obter URL de download do Firebase Storage
      const downloadURL = await getStorageDownloadURL(fileRef);
      
      console.log(`✅ Imagem encontrada, redirecionando para: ${downloadURL}`);
      
      // Redirecionar para a URL do Firebase Storage
      return NextResponse.redirect(downloadURL, 302);
      
    } catch (storageError: any) {
      console.log(`❌ Imagem não encontrada no Firebase Storage: influencers/${filePath}`);
      
      // Se o arquivo não existir no Firebase, retornar imagem placeholder
      const placeholderUrl = new URL('/placeholder-user.jpg', request.url).toString();
      return NextResponse.redirect(placeholderUrl, 302);
    }
    
  } catch (error) {
    console.error('❌ Erro ao buscar imagem do Firebase Storage:', error);
    
    // Em caso de erro, retornar imagem placeholder
    const placeholderUrl = new URL('/placeholder-user.jpg', request.url).toString();
    return NextResponse.redirect(placeholderUrl, 302);
  }
}

/**
 * Configuração para permitir cache das imagens
 */
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';