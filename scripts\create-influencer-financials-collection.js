// Script para criar a coleção influencer_financials no Firestore
const admin = require('firebase-admin');

// Configuração do Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json')),
    projectId: 'deumatch-demo'
  });
}

const db = admin.firestore();

// Estrutura de exemplo para documentos financeiros
const exemplosFinanceiros = [
  {
    influencerId: "exemplo_influencer_1",
    responsibleName: "<PERSON>",
    agencyName: "Agência Digital Creative",
    email: "<EMAIL>",
    whatsapp: "+55 11 99999-9999",
    instagramStoriesViews: 150000,
    prices: {
      instagramStory: {
        name: "Stories",
        price: 2500
      },
      instagramReel: {
        name: "<PERSON><PERSON>",
        price: 4000
      },
      tiktokVideo: {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        price: 3500
      },
      youtubeInsertion: {
        name: "Inser<PERSON>",
        price: 5000
      },
      youtubeDedicated: {
        name: "Dedic<PERSON>",
        price: 12000
      },
      youtubeShorts: {
        name: "Shorts",
        price: 2000
      }
    },
    brandHistory: {
      instagram: ["Nike", "Adidas", "Coca-Cola", "Samsung"],
      tiktok: ["TikTok Shop", "Shein", "AliExpress"],
      youtube: ["NordVPN", "Skillshare", "Squarespace"]
    },
    additionalData: {
      contentType: ["lifestyle", "fitness", "tecnologia"],
      promotesTraders: false,
      responsibleRecruiter: "Maria Santos",
      socialMediaScreenshots: [],
      notes: "Influenciador com ótimo engajamento e histórico de campanhas bem-sucedidas",
      documents: []
    },
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    influencerId: "exemplo_influencer_2",
    responsibleName: "Ana Paula Costa",
    agencyName: "Influencer Hub",
    email: "<EMAIL>",
    whatsapp: "+55 21 88888-8888",
    instagramStoriesViews: 85000,
    prices: {
      instagramStory: {
        name: "Stories",
        price: 1800
      },
      instagramReel: {
        name: "Reels",
        price: 3200
      },
      tiktokVideo: {
        name: "Vídeo",
        price: 2800
      },
      youtubeInsertion: {
        name: "Inserção",
        price: 4500
      },
      youtubeDedicated: {
        name: "Dedicado",
        price: 8500
      },
      youtubeShorts: {
        name: "Shorts",
        price: 1500
      }
    },
    brandHistory: {
      instagram: ["Sephora", "MAC", "Natura", "Boticário"],
      tiktok: ["Romwe", "Zaful", "PatPat"],
      youtube: ["Audible", "MasterClass", "Coursera"]
    },
    additionalData: {
      contentType: ["beleza", "moda", "lifestyle"],
      promotesTraders: false,
      responsibleRecruiter: "Carlos Roberto",
      socialMediaScreenshots: [],
      notes: "Especialista em conteúdo de beleza e moda, público majoritariamente feminino",
      documents: []
    },
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    influencerId: "exemplo_influencer_3",
    responsibleName: "Pedro Oliveira",
    agencyName: "Gaming Talents",
    email: "<EMAIL>",
    whatsapp: "+55 11 77777-7777",
    instagramStoriesViews: 200000,
    prices: {
      instagramStory: {
        name: "Stories",
        price: 3000
      },
      instagramReel: {
        name: "Reels",
        price: 5000
      },
      tiktokVideo: {
        name: "Vídeo",
        price: 4500
      },
      youtubeInsertion: {
        name: "Inserção",
        price: 8000
      },
      youtubeDedicated: {
        name: "Dedicado",
        price: 15000
      },
      youtubeShorts: {
        name: "Shorts",
        price: 2500
      }
    },
    brandHistory: {
      instagram: ["PlayStation", "Xbox", "Razer", "HyperX"],
      tiktok: ["Mobile Legends", "Free Fire", "PUBG"],
      youtube: ["Steam", "Epic Games", "Twitch"]
    },
    additionalData: {
      contentType: ["gaming", "tecnologia", "reviews"],
      promotesTraders: true,
      responsibleRecruiter: "Luiza Fernandes",
      socialMediaScreenshots: [],
      notes: "Gamer profissional com grande alcance no público masculino jovem",
      documents: []
    },
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  }
];

async function criarColecaoInfluencerFinancials() {
  try {
    console.log('🚀 Iniciando criação da coleção influencer_financials...');
    
    const colecaoRef = db.collection('influencer_financials');
    
    // Verificar se a coleção já existe
    const snapshot = await colecaoRef.limit(1).get();
    
    if (!snapshot.empty) {
      console.log('⚠️  A coleção influencer_financials já existe!');
      console.log(`📊 Documentos existentes: ${snapshot.size}`);
      
      // Listar os documentos existentes
      const todosDocumentos = await colecaoRef.get();
      console.log(`📋 Total de documentos na coleção: ${todosDocumentos.size}`);
      
      todosDocumentos.forEach(doc => {
        const data = doc.data();
        console.log(`  - ID: ${doc.id} | Influencer: ${data.influencerId} | Responsável: ${data.responsibleName}`);
      });
      
      return;
    }
    
    console.log('📝 Criando documentos de exemplo...');
    
    // Adicionar documentos de exemplo
    const promises = exemplosFinanceiros.map(async (dadosFinanceiros, index) => {
      const docRef = await colecaoRef.add(dadosFinanceiros);
      console.log(`✅ Documento ${index + 1} criado com ID: ${docRef.id}`);
      return docRef.id;
    });
    
    const idsDocumentos = await Promise.all(promises);
    
    console.log('\n🎉 Coleção influencer_financials criada com sucesso!');
    console.log(`📊 Total de documentos criados: ${idsDocumentos.length}`);
    console.log('📋 IDs dos documentos criados:');
    idsDocumentos.forEach((id, index) => {
      console.log(`  ${index + 1}. ${id}`);
    });
    
    // Verificar a estrutura da coleção
    console.log('\n🔍 Verificando estrutura da coleção...');
    const novosDocumentos = await colecaoRef.get();
    
    console.log(`📈 Documentos na coleção: ${novosDocumentos.size}`);
    
    // Mostrar um exemplo de documento
    if (!novosDocumentos.empty) {
      const primeiroDoc = novosDocumentos.docs[0];
      console.log('\n📄 Exemplo de documento:');
      console.log(`ID: ${primeiroDoc.id}`);
      console.log('Dados:', JSON.stringify(primeiroDoc.data(), null, 2));
    }
    
    console.log('\n✨ Processo concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao criar a coleção:', error);
    throw error;
  }
}

// Executar o script
if (require.main === module) {
  criarColecaoInfluencerFinancials()
    .then(() => {
      console.log('🏁 Script finalizado com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Erro na execução do script:', error);
      process.exit(1);
    });
}

module.exports = { criarColecaoInfluencerFinancials }; 
