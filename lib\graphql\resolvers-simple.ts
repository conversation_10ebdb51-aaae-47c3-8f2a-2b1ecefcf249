// RESOLVERS GRAPHQL SIMPLIFICADOS
// Implementação inicial dos resolvers principais

import { 
  getAllInfluencers, 
  getInfluencerById, 
  addInfluencer, 
  updateInfluencer, 
  deleteInfluencer 
} from '../firebase';
import { FinancialCacheService } from '../firebase-financial-cache';
import { FinancialDenormalizationService } from '../financial-denormalization-service';

// Interface básica para context
interface GraphQLContext {
  user: { id: string } | null;
  loaders: {
    influencer: any;
    financial: any;
  };
}

// ===== RESOLVERS PRINCIPAIS =====

export const resolvers = {
  // ===== QUERIES =====
  Query: {
    // Buscar influenciador por ID
    async influencer(parent: any, { id }: { id: string }, context: GraphQLContext) {
      console.log(`🔍 Query: Buscando influenciador ${id}`);
      
      try {
        return await getInfluencerById(id);
      } catch (error) {
        console.error(`Erro ao buscar influenciador ${id}:`, error);
        throw new Error('Erro ao buscar influenciador');
      }
    },

    // Buscar lista de influenciadores com filtros básicos
    async influencers(parent: any, args: any, context: GraphQLContext) {
      const { filters = {}, userId } = args;
      
      console.log(`🔍 Query: Buscando influenciadores para usuário ${userId}`, filters);
      
      try {
        const allInfluencers = await getAllInfluencers();
        
        // Filtrar por userId
        let filteredInfluencers = allInfluencers.filter(inf => inf.userId === userId);
        
        // Aplicar filtros básicos
        if (filters.search) {
          const search = filters.search.toLowerCase();
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.name.toLowerCase().includes(search) ||
            (inf.bio && inf.bio.toLowerCase().includes(search))
          );
        }
        
        if (filters.category) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.category === filters.category
          );
        }
        
        if (filters.isAvailable !== undefined) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.isAvailable === filters.isAvailable
          );
        }
        
        if (filters.followersMin) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.totalFollowers >= filters.followersMin
          );
        }
        
        if (filters.followersMax) {
          filteredInfluencers = filteredInfluencers.filter(inf => 
            inf.totalFollowers <= filters.followersMax
          );
        }
        
        // Ordenação simples
        filteredInfluencers.sort((a, b) => {
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        });
        
        // Paginação básica
        const limit = args.pagination?.limit || 20;
        const offset = args.pagination?.offset || 0;
        
        const totalCount = filteredInfluencers.length;
        const nodes = filteredInfluencers.slice(offset, offset + limit);
        
        return {
          nodes,
          totalCount,
          hasNextPage: offset + limit < totalCount,
          hasPreviousPage: offset > 0
        };
        
      } catch (error) {
        console.error('Erro ao buscar influenciadores:', error);
        throw new Error('Erro ao buscar influenciadores');
      }
    },

    // Buscar por faixa de preço
    async influencersByPriceRange(parent: any, args: any, context: GraphQLContext) {
      const { priceRange, userId, limit = 20 } = args;
      
      console.log(`💰 Query: Buscando influenciadores por faixa de preço ${priceRange}`);
      
      try {
        return await FinancialDenormalizationService.getInfluencersByPriceRange(
          priceRange.toLowerCase(),
          userId,
          limit
        );
      } catch (error) {
        console.error('Erro ao buscar por faixa de preço:', error);
        throw new Error('Erro ao buscar influenciadores por preço');
      }
    },

    // Dados financeiros de um influenciador
    async influencerFinancial(parent: any, { influencerId }: { influencerId: string }, context: GraphQLContext) {
      console.log(`💰 Query: Buscando dados financeiros para ${influencerId}`);
      
      try {
        return await FinancialCacheService.getFinancialData(influencerId);
      } catch (error) {
        console.error(`Erro ao buscar dados financeiros:`, error);
        throw new Error('Erro ao buscar dados financeiros');
      }
    },

    // Estatísticas financeiras
    async financialStats(parent: any, { userId }: { userId: string }, context: GraphQLContext) {
      console.log(`📊 Query: Buscando estatísticas financeiras para usuário ${userId}`);
      
      try {
        return await FinancialDenormalizationService.getPricingStats(userId);
      } catch (error) {
        console.error('Erro ao buscar estatísticas:', error);
        throw new Error('Erro ao buscar estatísticas financeiras');
      }
    },

    // ✅ NOVO: Resolver para estatísticas gerais (que estava faltando)
    async stats(parent: any, { userId }: { userId: string }, context: GraphQLContext) {
      console.log(`📊 Query: Buscando estatísticas gerais para usuário ${userId}`);
      
      try {
        // Buscar todos os influenciadores do usuário
        const allInfluencers = await getAllInfluencers();
        const userInfluencers = allInfluencers.filter(inf => inf.userId === userId);
        
        // Calcular totais agregados
        let totalViews = 0;
        let totalFollowers = 0;
        
        userInfluencers.forEach(influencer => {
          // Agregar seguidores de todas as plataformas
          const instagramFollowers = parseInt(String(influencer.instagramFollowers || 0).replace(/[^\d]/g, '')) || 0;
          const youtubeFollowers = parseInt(String(influencer.youtubeFollowers || 0).replace(/[^\d]/g, '')) || 0;
          const tiktokFollowers = parseInt(String(influencer.tiktokFollowers || 0).replace(/[^\d]/g, '')) || 0;
          const facebookFollowers = parseInt(String(influencer.facebookFollowers || 0).replace(/[^\d]/g, '')) || 0;
          const twitchFollowers = parseInt(String(influencer.twitchFollowers || 0).replace(/[^\d]/g, '')) || 0;
          const kwaiFollowers = parseInt(String(influencer.kwaiFollowers || 0).replace(/[^\d]/g, '')) || 0;
          
          totalFollowers += instagramFollowers + youtubeFollowers + tiktokFollowers + 
                           facebookFollowers + twitchFollowers + kwaiFollowers;
          
          // Agregar views de todas as plataformas
          const instagramViews = parseInt(String(influencer.instagramAvgViews || 0).replace(/[^\d]/g, '')) || 0;
          const youtubeViews = parseInt(String(influencer.youtubeAvgViews || 0).replace(/[^\d]/g, '')) || 0;
          const tiktokViews = parseInt(String(influencer.tiktokAvgViews || 0).replace(/[^\d]/g, '')) || 0;
          const facebookViews = parseInt(String(influencer.facebookViews || 0).replace(/[^\d]/g, '')) || 0;
          const twitchViews = parseInt(String(influencer.twitchViews || 0).replace(/[^\d]/g, '')) || 0;
          const kwaiViews = parseInt(String(influencer.kwaiViews || 0).replace(/[^\d]/g, '')) || 0;
          
          totalViews += instagramViews + youtubeViews + tiktokViews + 
                       facebookViews + twitchViews + kwaiViews;
        });
        
        // Buscar contadores adicionais
        // const brandsSnapshot = await db.collection('brands').where('userId', '==', userId).get();
        // const campaignsSnapshot = await db.collection('campaigns').where('userId', '==', userId).get();
        
        const stats = {
          totalInfluencers: userInfluencers.length,
          totalViews: totalViews,
          totalFollowers: totalFollowers,
          totalBrands: 0, // Placeholder, precisa de uma fonte de dados
          totalCampaigns: 0 // Placeholder, precisa de uma fonte de dados
        };
        
        console.log(`📊 Estatísticas calculadas:`, stats);
        return stats;
      } catch (error) {
        console.error('Erro ao buscar estatísticas gerais:', error);
        return {
          totalInfluencers: 0,
          totalViews: 0,
          totalFollowers: 0,
          totalBrands: 0,
          totalCampaigns: 0
        };
      }
    },

    // Estatísticas do cache
    async cacheStats() {
      console.log(`📊 Query: Buscando estatísticas do cache`);
      
      try {
        return FinancialCacheService.getCacheStats();
      } catch (error) {
        console.error('Erro ao buscar stats do cache:', error);
        throw new Error('Erro ao buscar estatísticas do cache');
      }
    },

    // Dashboard data
    async dashboardData(parent: any, { userId }: { userId: string }, context: GraphQLContext) {
      console.log(`📊 Query: Carregando dados do dashboard para usuário ${userId}`);
      
      try {
        const [
          allInfluencers,
          financialStats
        ] = await Promise.all([
          getAllInfluencers(),
          FinancialDenormalizationService.getPricingStats(userId)
        ]);

        const userInfluencers = allInfluencers.filter(inf => inf.userId === userId);
        const totalInfluencers = userInfluencers.length;
        const totalWithFinancialData = userInfluencers.filter(inf => inf.pricing?.hasFinancialData).length;

        // Recent influencers (últimos 10)
        const recentInfluencers = userInfluencers
          .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
          .slice(0, 10);

        // Top performers por engajamento
        const topPerformers = userInfluencers
          .filter(inf => inf.engagementRate > 0)
          .sort((a, b) => b.engagementRate - a.engagementRate)
          .slice(0, 10);

        return {
          totalInfluencers,
          totalWithFinancialData,
          totalCampaigns: 0, // Placeholder
          totalBrands: 0, // Placeholder
          financialStats,
          recentInfluencers,
          topPerformers
        };
      } catch (error) {
        console.error('Erro ao carregar dashboard:', error);
        throw new Error('Erro ao carregar dados do dashboard');
      }
    }
  },

  // ===== NESTED RESOLVERS =====
  Influencer: {
    // Resolver para dados financeiros
    async financial(parent: any) {
      if (!parent.id) return null;
      
      console.log(`💰 Nested: Carregando dados financeiros para influenciador ${parent.id}`);
      
      try {
        return await FinancialCacheService.getFinancialData(parent.id);
      } catch (error) {
        console.error('Erro ao carregar dados financeiros nested:', error);
        return null;
      }
    },

    // Resolver para campanhas - placeholder
    async campaigns(parent: any) {
      // Implementar quando tivermos a funcionalidade de campanhas
      return [];
    },

    // Resolver para marcas - placeholder
    async brands(parent: any) {
      // Implementar quando tivermos a funcionalidade de marcas
      return [];
    },

    // Resolver para tags - placeholder
    async tags(parent: any) {
      return [];
    },

    // Resolver para notes - placeholder
    async notes(parent: any) {
      return [];
    },

    // Resolver para categorias principais - placeholder
    async mainCategoriesData(parent: any) {
      return [];
    }
  },

  // ===== MUTATIONS =====
  Mutation: {
    // Criar influenciador
    async createInfluencer(parent: any, { input }: any, context: GraphQLContext) {
      console.log('🆕 Mutation: Criando novo influenciador', input);
      
      try {
        const influencer = await addInfluencer(input);
        return influencer;
      } catch (error) {
        console.error('Erro ao criar influenciador:', error);
        throw new Error('Erro ao criar influenciador');
      }
    },

    // Atualizar influenciador
    async updateInfluencer(parent: any, { id, input }: any, context: GraphQLContext) {
      console.log(`✏️ Mutation: Atualizando influenciador ${id}`, input);
      
      try {
        const influencer = await updateInfluencer(id, input);
        return influencer;
      } catch (error) {
        console.error('Erro ao atualizar influenciador:', error);
        throw new Error('Erro ao atualizar influenciador');
      }
    },

    // Deletar influenciador
    async deleteInfluencer(parent: any, { id }: { id: string }, context: GraphQLContext) {
      console.log(`🗑️ Mutation: Deletando influenciador ${id}`);
      
      try {
        await deleteInfluencer(id);
        
        // Limpar cache
        FinancialCacheService.invalidateInfluencer(id);
        
        return true;
      } catch (error) {
        console.error('Erro ao deletar influenciador:', error);
        throw new Error('Erro ao deletar influenciador');
      }
    },

    // Sincronizar dados financeiros denormalizados
    async syncInfluencerFinancialData(parent: any, { influencerId }: { influencerId: string }, context: GraphQLContext) {
      console.log(`🔄 Mutation: Sincronizando dados financeiros para ${influencerId}`);
      
      try {
        const success = await FinancialDenormalizationService.updateInfluencerPricing(influencerId);
        return success;
      } catch (error) {
        console.error('Erro ao sincronizar dados financeiros:', error);
        throw new Error('Erro ao sincronizar dados financeiros');
      }
    },

    // Limpar cache
    async clearCache() {
      console.log('🧹 Mutation: Limpando cache');
      
      try {
        FinancialCacheService.clearCache();
        return true;
      } catch (error) {
        console.error('Erro ao limpar cache:', error);
        throw new Error('Erro ao limpar cache');
      }
    },

    // Pré-carregar dados
    async preloadInfluencerData(parent: any, { influencerIds }: { influencerIds: string[] }, context: GraphQLContext) {
      console.log(`🚀 Mutation: Pré-carregando dados para ${influencerIds.length} influenciadores`);
      
      try {
        await FinancialCacheService.preloadFinancialData(influencerIds);
        return true;
      } catch (error) {
        console.error('Erro ao pré-carregar dados:', error);
        throw new Error('Erro ao pré-carregar dados');
      }
    }
  }
};

// Função para criar context simplificado
export function createGraphQLContext(req: any): GraphQLContext {
  return {
    user: getUserFromRequest(req),
    loaders: {
      influencer: null, // Placeholder para DataLoaders futuros
      financial: null   // Placeholder para DataLoaders futuros
    }
  };
}

// Placeholder para autenticação
function getUserFromRequest(req: any): { id: string } | null {
  // Implementar extração de usuário do token JWT
  // Por agora, retorna um placeholder
  return { id: 'user_id_placeholder' };
}

export default resolvers; 

