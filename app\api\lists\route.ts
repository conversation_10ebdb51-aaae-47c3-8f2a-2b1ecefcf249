import { NextRequest, NextResponse } from 'next/server';
import { ListService } from '@/services/list-service';
import { 
  withCreateListValidation, 
  withListFiltersValidation,
  withBatchListOperationValidation 
} from '@/lib/middleware/lists-middleware';
import { CriarListaData, FiltrosLista } from '@/types/list';

/**
 * 📝 API ROUTES PARA LISTAS
 * Endpoints principais para operações com listas
 */

/**
 * GET /api/lists - Buscar listas do usuário
 */
export const GET = withListFiltersValidation(
  async (req: NextRequest, userId: string, context: any, filtros: FiltrosLista) => {
    try {
      const resultado = await ListService.buscarListasUsuario(
        userId,
        filtros
      );

      return NextResponse.json(resultado);

    } catch (error: any) {
      console.error('[API_LISTS_GET]', { userId, filtros, error });
      return NextResponse.json(
        { error: 'Erro interno ao buscar listas' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/lists - Criar nova lista
 */
export const POST = withCreateListValidation(
  async (req: NextRequest, userId: string, context: any, data: CriarListaData) => {
    try {
      // TODO: Buscar nome do usuário do contexto/banco
      const userDisplayName = context.userEmail || 'Usuário';
      
      const lista = await ListService.criarLista(data, userId, userDisplayName);

      return NextResponse.json(lista, { status: 201 });

    } catch (error: any) {
      console.error('[API_LISTS_POST]', { userId, data, error });
      
      if (error?.message?.includes('já existe')) {
        return NextResponse.json(
          { error: 'Já existe uma lista com este nome' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: 'Erro interno ao criar lista' },
        { status: 500 }
      );
    }
  }
);

/**
 * PATCH /api/lists - Operações em lote
 */
export const PATCH = withBatchListOperationValidation(
  async (req: NextRequest, userId: string, context: any, data: any) => {
    try {
      const resultado = await ListService.operacaoLote(data, userId);

      return NextResponse.json(resultado);

    } catch (error: any) {
      console.error('[API_LISTS_BATCH]', { userId, data, error });
      return NextResponse.json(
        { error: 'Erro interno na operação em lote' },
        { status: 500 }
      );
    }
  }
); 

