import { NextResponse } from 'next/server';
import { getFinancialById, updateFinancial, deleteFinancial } from '@/lib/firebase-financials';
import { InfluencerFinancial } from '@/types/influencer-financial';

// Interface para entrada de dados na atualização
interface FinancialUpdateInput {
  responsibleName?: string;
  agencyName?: string;
  email?: string;
  whatsapp?: string;
  instagramStoriesViews?: number;
  prices?: {
    instagramStory?: {
      name?: string;
      price?: number;
    };
    instagramReel?: {
      name?: string;
      price?: number;
    };
    tiktokVideo?: {
      name?: string;
      price?: number;
    };
    youtubeInsertion?: {
      name?: string;
      price?: number;
    };
    youtubeDedicated?: {
      name?: string;
      price?: number;
    };
    youtubeShorts?: {
      name?: string;
      price?: number;
    };
  };
  brandHistory?: {
    instagram?: string[];
    tiktok?: string[];
    youtube?: string[];
  };
  additionalData?: {
    contentType?: string[];
    promotesTraders?: boolean;
    responsibleRecruiter?: string;
    socialMediaScreenshots?: string[];
    notes?: string;
    documents?: {
      name: string;
      url: string;
      type: string;
      uploadedAt: Date;
    }[];
  };
}

// Buscar um registro financeiro pelo ID
export async function GET(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    // No Next.js 14, precisamos usar await para acessar context.params
    const { id } = await context.params;
    const financial = await getFinancialById(id);
    
    if (!financial) {
      return NextResponse.json(
        { error: 'Registro financeiro não encontrado' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(financial);
  } catch (error) {
    console.error('Erro ao buscar registro financeiro:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar registro financeiro' },
      { status: 500 }
    );
  }
}

// Atualizar um registro financeiro
export async function PUT(
  request: Request,
  context: { params: { id: string } }
) {
  // No Next.js 14, precisamos usar await para acessar context.params
  const { id } = await context.params;
  
  try {
    // Verificar se o registro existe
    const existingFinancial = await getFinancialById(id);
    
    if (!existingFinancial) {
      return NextResponse.json(
        { error: 'Registro financeiro não encontrado' },
        { status: 404 }
      );
    }
    
    // Dados do corpo da requisição
    const data = await request.json();
    
    // Preparar os dados para atualização (apenas os campos fornecidos)
    const updateData: FinancialUpdateInput = {};
    
    // Verificar dados enviados e atualizar apenas os necessários
    if (data.responsibleName) updateData.responsibleName = data.responsibleName;
    if (data.agencyName) updateData.agencyName = data.agencyName;
    if (data.email) updateData.email = data.email;
    if (data.whatsapp) updateData.whatsapp = data.whatsapp;
    if (data.instagramStoriesViews) updateData.instagramStoriesViews = data.instagramStoriesViews;

    // Preços (se existirem no corpo da requisição)
    if (data.prices) {
      updateData.prices = {};
      
      if (data.prices.instagramStory !== undefined) {
        updateData.prices.instagramStory = {
          name: data.prices.instagramStory.name || "Stories",
          price: parseFloat(data.prices.instagramStory.price || data.prices.instagramStory) || 0
        };
      }
      
      if (data.prices.instagramReel !== undefined) {
        updateData.prices.instagramReel = {
          name: data.prices.instagramReel.name || "Reels",
          price: parseFloat(data.prices.instagramReel.price || data.prices.instagramReel) || 0
        };
      }
      
      if (data.prices.tiktokVideo !== undefined) {
        updateData.prices.tiktokVideo = {
          name: data.prices.tiktokVideo.name || "Vídeo",
          price: parseFloat(data.prices.tiktokVideo.price || data.prices.tiktokVideo) || 0
        };
      }
      
      if (data.prices.youtubeInsertion !== undefined) {
        updateData.prices.youtubeInsertion = {
          name: data.prices.youtubeInsertion.name || "Inserção",
          price: parseFloat(data.prices.youtubeInsertion.price || data.prices.youtubeInsertion) || 0
        };
      }
      
      if (data.prices.youtubeDedicated !== undefined) {
        updateData.prices.youtubeDedicated = {
          name: data.prices.youtubeDedicated.name || "Dedicado",
          price: parseFloat(data.prices.youtubeDedicated.price || data.prices.youtubeDedicated) || 0
        };
      }
      
      if (data.prices.youtubeShorts !== undefined) {
        updateData.prices.youtubeShorts = {
          name: data.prices.youtubeShorts.name || "Shorts",
          price: parseFloat(data.prices.youtubeShorts.price || data.prices.youtubeShorts) || 0
        };
      }
    }

    // Histórico de marcas (se existirem no corpo da requisição)
    if (data.brandHistory) {
      updateData.brandHistory = {};
      if (data.brandHistory.instagram !== undefined) updateData.brandHistory.instagram = data.brandHistory.instagram;
      if (data.brandHistory.tiktok !== undefined) updateData.brandHistory.tiktok = data.brandHistory.tiktok;
      if (data.brandHistory.youtube !== undefined) updateData.brandHistory.youtube = data.brandHistory.youtube;
    }

    // Dados adicionais (se existirem no corpo da requisição)
    if (data.additionalData) {
      updateData.additionalData = {};
      if (data.additionalData.contentType !== undefined) updateData.additionalData.contentType = data.additionalData.contentType;
      if (data.additionalData.promotesTraders !== undefined) updateData.additionalData.promotesTraders = data.additionalData.promotesTraders;
      if (data.additionalData.responsibleRecruiter !== undefined) updateData.additionalData.responsibleRecruiter = data.additionalData.responsibleRecruiter;
      if (data.additionalData.socialMediaScreenshots !== undefined) updateData.additionalData.socialMediaScreenshots = data.additionalData.socialMediaScreenshots;
      if (data.additionalData.notes !== undefined) updateData.additionalData.notes = data.additionalData.notes;
      if (data.additionalData.documents !== undefined) updateData.additionalData.documents = data.additionalData.documents;
    }
    
    // Atualizar dados financeiros
    await updateFinancial(id, updateData);
    
    return NextResponse.json({
      id,
      message: 'Dados financeiros atualizados com sucesso',
      success: true
    });
    
  } catch (error) {
    console.error('Erro ao atualizar dados financeiros:', error);
    
    // Fornecer detalhes do erro para facilitar debug
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    return NextResponse.json(
      { 
        error: 'Erro ao atualizar dados financeiros', 
        details: errorMessage,
        success: false
      },
      { status: 500 }
    );
  }
}

// Excluir um registro financeiro
export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    // No Next.js 14, precisamos usar await para acessar context.params
    const { id } = await context.params;
    
    // Verificar se o registro existe
    const existingFinancial = await getFinancialById(id);
    if (!existingFinancial) {
      return NextResponse.json(
        { error: 'Registro financeiro não encontrado' },
        { status: 404 }
      );
    }
    
    // Excluir registro financeiro
    await deleteFinancial(id);
    
    return NextResponse.json({
      success: true,
      message: 'Registro financeiro excluído com sucesso'
    });
  } catch (error) {
    console.error('Erro ao excluir registro financeiro:', error);
    
    // Fornecer detalhes do erro para facilitar debug
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    return NextResponse.json(
      { 
        error: 'Erro ao excluir registro financeiro', 
        details: errorMessage,
        success: false
      },
      { status: 500 }
    );
  }
}
