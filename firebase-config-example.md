# 🔥 Configuração do Firebase para o Projeto Deu Match

## 🚀 Guia Rápido de Configuração

Este documento fornece todas as informações necessárias para configurar o Firebase no projeto **Deu Match**.

## 📋 Pré-requisitos

1. **Node.js** instalado (versão 18 ou superior)
2. **Conta Firebase** com acesso ao projeto `deumatch-demo`
3. **Arquivo de credenciais** do Firebase Admin SDK (já incluído: `deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json`)

## 🔧 Configuração das Variáveis de Ambiente

### 1. Criar arquivo `.env.local`

Crie um arquivo `.env.local` na raiz do projeto com o seguinte conteúdo:

```bash
# 🔥 CONFIGURAÇÕES DO FIREBASE - PROJETO DEUMATCH-DEMO
# Copie este conteúdo para um arquivo .env.local na raiz do projeto

# Firebase Project Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDhSkFQHdDLke6drWlVJzE3Zys6CJx_arQ
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=deumatch-demo.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=deumatch-demo
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=deumatch-demo.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:e6e275cde8b9b9668e0ff4
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-44CF3HTDFG

# Firebase Admin SDK (Server-side only)
FIREBASE_SERVICE_ACCOUNT_KEY='./deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json'

# Firebase Emulators (apenas para desenvolvimento)
NEXT_PUBLIC_USE_FIREBASE_EMULATORS=false

# JWT Secret para autenticação (gere uma string aleatória segura)
JWT_SECRET=gere_uma_string_aleatoria_muito_segura_aqui_use_pelo_menos_32_caracteres

# Environment
NODE_ENV=development

# Configurações adicionais
NEXT_PUBLIC_APP_NAME=Deu Match
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 2. Gerar JWT Secret Seguro

Para gerar um JWT_SECRET seguro, execute um dos comandos abaixo:

**PowerShell:**
```powershell
-join ((1..32) | ForEach {(65..90) + (97..122) + (48..57) | Get-Random | % {[char]$_}})
```

**Node.js:**
```javascript
require('crypto').randomBytes(32).toString('hex')
```

### 3. Verificar Arquivo de Credenciais

O arquivo `deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json` já está presente no projeto e contém as credenciais do Firebase Admin SDK.

⚠️ **IMPORTANTE**: Este arquivo contém credenciais sensíveis. Certifique-se de que:
- Está incluído no `.gitignore`
- Não é commitado no repositório
- É mantido seguro em produção

## 🔒 Configurações de Segurança

### Content Security Policy (CSP)

As configurações de CSP já estão configuradas nos arquivos:
- `vercel.json` - Para produção no Vercel
- `middleware.ts` - Para aplicação Next.js

Estas configurações permitem conexões seguras com:
- Firebase Auth
- Firestore
- Firebase Storage
- Google Fonts
- Firebase Analytics

### Firebase Security Rules

As regras de segurança do Firestore estão em `firestore.rules` e devem ser configuradas adequadamente para proteger os dados.

## 🚀 Iniciando o Projeto

1. **Instalar dependências:**
   ```bash
   npm install
   ```

2. **Verificar configuração:**
   ```bash
   npm run dev
   ```

3. **Acessar aplicação:**
   Abra `http://localhost:3000` no navegador

## 🐛 Solução de Problemas

### Erro: "Firebase configuration missing"

**Causa**: Variáveis de ambiente não carregadas  
**Solução**: 
1. Verifique se o arquivo `.env.local` existe
2. Reinicie o servidor de desenvolvimento
3. Limpe o cache: `rm -rf .next && npm run dev`

### Erro: "API_KEY_SERVICE_BLOCKED"

**Causa**: API Key com restrições inadequadas  
**Solução**: 
1. Acesse o [Console do Google Cloud](https://console.cloud.google.com)
2. Vá para "APIs & Services" > "Credentials"
3. Verifique as restrições da API Key
4. Certifique-se de que as seguintes APIs estão permitidas:
   - Firebase Management API
   - Identity Toolkit API
   - Firebase Installations API
   - Cloud Firestore API
   - Cloud Storage for Firebase API

### Erro: "Failed to fetch Firebase app's measurement ID"

**Causa**: Firebase Management API não está habilitada  
**Solução**: 
1. Habilite a Firebase Management API no console
2. Adicione-a à lista de APIs permitidas na API Key

## 📚 Recursos Úteis

- [Documentação do Firebase](https://firebase.google.com/docs)
- [Console do Firebase](https://console.firebase.google.com/project/deumatch-demo)
- [Console do Google Cloud](https://console.cloud.google.com/home/<USER>
- [Guia de API Keys do Firebase](https://firebase.google.com/docs/projects/api-keys)

## 🔐 Checklist de Segurança

- [ ] Arquivo `.env.local` criado e não versionado
- [ ] JWT_SECRET gerado com pelo menos 32 caracteres
- [ ] Arquivo de credenciais do Admin SDK protegido
- [ ] Firebase Security Rules configuradas
- [ ] CSP headers configurados corretamente
- [ ] API Keys com restrições apropriadas
- [ ] Isolamento de dados por usuário implementado

## 📞 Suporte

Em caso de dúvidas ou problemas:
1. Verifique este documento
2. Consulte a documentação oficial do Firebase
3. Abra uma issue no repositório do projeto

---

**Última atualização**: Dezembro 2024  
**Projeto**: Deu Match - Firebase Demo  
**ID do Projeto**: `deumatch-demo` 