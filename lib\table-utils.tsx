import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users, Tag, Calendar, List } from 'lucide-react';

// Utilitários para formatação
export const formatters = {
  number: (value: number | undefined | null) => {
    if (value === undefined || value === null || isNaN(value)) return '0';
    return value.toLocaleString('pt-BR');
  },
  currency: (value: number | undefined | null) => {
    if (value === undefined || value === null || isNaN(value)) return 'R$ 0,00';
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  },
  date: (date: Date | string | null | undefined) => {
    if (!date) return '-';
    
    let dateObj: Date;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return '-';
    }
    
    // Verificar se a data é válida
    if (isNaN(dateObj.getTime())) {
      return '-';
    }
    
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(dateObj);
  },
  percentage: (value: number | undefined | null) => {
    if (value === undefined || value === null || isNaN(value)) return '0%';
    return `${value.toFixed(1)}%`;
  },
};

// Utilitários para badges de status
export const statusBadges = {
  influencer: (status: string) => {
    const variants = {
      ativo: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      inativo: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      pendente: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    };
    return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  },
  
  campaign: (status: string) => {
    const variants = {
      'ativa': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'pausada': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'finalizada': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'cancelada': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    };
    return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  },

  list: (tipo: string) => {
    const variants = {
      'estática': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'dinâmica': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    };
    return variants[tipo as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  }
};

// Utilitários para ícones
export const typeIcons = {
  influenciadores: <Users className="h-4 w-4" />,
  marcas: <Tag className="h-4 w-4" />,
  campanhas: <Calendar className="h-4 w-4" />,
  conteúdo: <List className="h-4 w-4" />,
};

// Componente para Avatar personalizado
export function TableAvatar({ 
  src, 
  name, 
  size = "md" 
}: { 
  src?: string; 
  name: string; 
  size?: "sm" | "md" | "lg";
}) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10", 
    lg: "h-12 w-12"
  };

  return (
    <Avatar className={sizeClasses[size]}>
      <AvatarImage src={src} alt={name} />
      <AvatarFallback className="bg-gradient-to-r from-[#ff0074] to-[#9810fa] text-white">
        {name.slice(0, 2).toUpperCase()}
      </AvatarFallback>
    </Avatar>
  );
}

// Componente para Status Badge
export function StatusBadge({ 
  status, 
  type 
}: { 
  status: string; 
  type: 'influencer' | 'campaign' | 'list';
}) {
  return (
    <Badge className={statusBadges[type](status)}>
      {status}
    </Badge>
  );
}

// Utilitários para ações comuns
export const commonActions = {
  confirmDelete: (itemName: string, onConfirm: () => void) => {
    if (confirm(`Tem certeza que deseja deletar "${itemName}"? Esta ação não pode ser desfeita.`)) {
      onConfirm();
    }
  },
  
  handleBulkDelete: (selectedItems: any[], itemName: string, onConfirm: (items: any[]) => void) => {
    if (confirm(`Deletar ${selectedItems.length} ${itemName}(s) selecionado(s)?`)) {
      onConfirm(selectedItems);
    }
  },
}; 



