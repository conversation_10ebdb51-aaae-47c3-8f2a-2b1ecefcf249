import { NextRequest, NextResponse } from 'next/server';
import { getFinancialByInfluencerId } from '@/lib/firebase-financials';

export async function GET(
  req: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // No Next.js 14, precisamos usar await para acessar context.params
    const { id } = await context.params;
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID do influenciador não fornecido' },
        { status: 400 }
      );
    }

    const financialData = await getFinancialByInfluencerId(id);
    
    if (!financialData) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Dados financeiros não encontrados para este influenciador',
          data: null
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        success: true, 
        message: 'Dados financeiros encontrados com sucesso',
        data: financialData
      },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Erro ao buscar dados financeiros do influenciador:`, error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro ao buscar dados financeiros',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
