import { NextResponse } from 'next/server';
import { InfluencerService } from '@/services/influencer-service';

// Rota GET para buscar todos os influenciadores para o painel admin
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  try {
    if (id) {
      // Buscar um influenciador específico pelo ID
      const influencer = await InfluencerService.getById(id);
      
      if (!influencer) {
        return NextResponse.json(
          { error: 'Influenciador não encontrado' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(influencer);
    } else {
      // Buscar todos os influenciadores
      const influencers = await InfluencerService.getAll();
      return NextResponse.json(influencers);
    }
  } catch (error: any) {
    console.error('Erro ao buscar influenciadores:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar dados de influenciadores' },
      { status: 500 }
    );
  }
}

// Rota POST para adicionar um novo influenciador
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Validação básica dos dados
    if (!data.name || !data.location) {
      return NextResponse.json(
        { error: 'Dados inválidos. Nome e localização são obrigatórios.' },
        { status: 400 }
      );
    }
    
    const newInfluencer = await InfluencerService.create(data);
    return NextResponse.json(newInfluencer, { status: 201 });
  } catch (error: any) {
    console.error('Erro ao adicionar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao adicionar influenciador' },
      { status: 500 }
    );
  }
}

// Rota PUT para atualizar um influenciador existente
export async function PUT(request: Request) {
  try {
    const data = await request.json();
    const { id } = data;
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }
    
    // Verificar se o influenciador existe
    const existingInfluencer = await InfluencerService.getById(id);
    
    if (!existingInfluencer) {
      return NextResponse.json(
        { error: 'Influenciador não encontrado' },
        { status: 404 }
      );
    }
    
    // Atualizar o influenciador
    const updatedInfluencer = await InfluencerService.update(id, data);
    return NextResponse.json(updatedInfluencer);
  } catch (error: any) {
    console.error('Erro ao atualizar influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar influenciador' },
      { status: 500 }
    );
  }
}

// Rota DELETE para remover um influenciador
export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  
  if (!id) {
    return NextResponse.json(
      { error: 'ID do influenciador é obrigatório' },
      { status: 400 }
    );
  }
  
  try {
    // Verificar se o influenciador existe
    const existingInfluencer = await InfluencerService.getById(id);
    
    if (!existingInfluencer) {
      return NextResponse.json(
        { error: 'Influenciador não encontrado' },
        { status: 404 }
      );
    }
    
    // Excluir o influenciador
    await InfluencerService.delete(id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Erro ao excluir influenciador:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir influenciador' },
      { status: 500 }
    );
  }
}


