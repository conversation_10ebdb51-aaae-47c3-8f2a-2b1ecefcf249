// ⚠️ ARQUIVO COMPLETAMENTE DESABILITADO
// Firebase Auth removido em favor do <PERSON>

import { ReactNode } from 'react';

// Provider dummy para compatibilidade
export function FirebaseAuthProvider({ children }: { children: ReactNode }) {
  console.warn('FirebaseAuthProvider está desabilitado - use Clerk Auth');
  return <>{children}</>;
}

// Hook dummy para compatibilidade
export function useFirebaseAuth() {
  console.warn('useFirebaseAuth está desabilitado - use Clerk Auth');
  return {
    currentUser: null,
    currentBrand: null,
    isAuthenticated: false,
    isLoading: false,
    login: () => Promise.reject('Firebase Auth desabilitado'),
    logout: () => Promise.reject('Firebase Auth desabilitado'),
    signUp: () => Promise.reject('Firebase Auth desabilitado'),
    updateProfile: () => Promise.reject('Firebase Auth desabilitado'),
  };
}

// Todas as outras funções desabilitadas
export const createUserProfile = () => Promise.reject('Firebase Auth desabilitado');
export const getUserProfile = () => Promise.reject('Firebase Auth desabilitado');
export const updateUserProfile = () => Promise.reject('Firebase Auth desabilitado');
export const getCurrentBrand = () => Promise.reject('Firebase Auth desabilitado');
export const createBrand = () => Promise.reject('Firebase Auth desabilitado');
export const updateBrand = () => Promise.reject('Firebase Auth desabilitado');

/* 
ARQUIVO ORIGINAL DESABILITADO:

// Todo o conteúdo original foi removido em favor do Clerk Auth
// Se necessário restaurar, consulte o backup ou git history

*/

