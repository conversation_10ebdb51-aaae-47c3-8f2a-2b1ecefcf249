import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { getFinancialByInfluencerId } from '@/lib/firebase-financials';
import { db } from '@/lib/firebase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const influencerId = searchParams.get('influencerId');
    const proposalId = searchParams.get('proposalId');
    
    if (!influencerId) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400 }
      );
    }

    console.log(`📄 [GET_DOCS] Buscando documentos: influencer=${influencerId}, proposal=${proposalId || 'independente'}`);
    
    // 🆕 ESTRUTURA HIERÁRQUICA: Se há proposalId, buscar na subcoleção
    if (proposalId) {
      console.log('📄 [HIERARCHICAL] Buscando documentos na subcoleção hierárquica');

      // Verificar se a proposta existe
      const proposalDoc = await db.collection('proposals').doc(proposalId).get();
      if (!proposalDoc.exists) {
        return NextResponse.json(
          { error: 'Proposta não encontrada' },
          { status: 404 }
        );
      }

      // Buscar documentos na estrutura: proposals/{proposalId}/influencers/{influencerId}/documents
      const documentsSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('documents')
        .orderBy('uploadedAt', 'desc')
        .get();

      const documents = documentsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          name: data.name,
          url: data.url,
          type: data.type,
          size: data.size,
          uploadedAt: data.uploadedAt?.toDate?.() || data.uploadedAt,
          uploadedBy: data.uploadedBy,
          proposalId: proposalId,
          influencerId: influencerId
        };
      });

      console.log(`✅ [HIERARCHICAL] ${documents.length} documentos encontrados na subcoleção`);

      return NextResponse.json({
        success: true,
        documents: documents,
        structure: 'hierarchical',
        source: `proposals/${proposalId}/influencers/${influencerId}/documents`,
        total: documents.length
      });
    }
    
    // 🔄 ESTRUTURA INDEPENDENTE: Buscar na coleção influencer_financials (comportamento atual)
    else {
      console.log('📄 [INDEPENDENT] Buscando documentos na estrutura independente (influencer_financials)');
      
      // Buscar registro financeiro do influenciador
      const financialRecord = await getFinancialByInfluencerId(influencerId);
      
      if (!financialRecord) {
        console.log('📄 [INDEPENDENT] Nenhum registro financeiro encontrado');
        return NextResponse.json({
          success: true,
          documents: [],
          structure: 'independent',
          source: 'influencer_financials',
          total: 0
        });
      }
      
      // Extrair documentos do registro financeiro
      const documents = financialRecord.additionalData?.documents || [];
      
      // Formatar documentos para retorno consistente
      const formattedDocuments = documents.map((doc: any, index: number) => ({
        id: doc.id || `doc_${index}`,
        name: doc.name,
        url: doc.url,
        type: doc.type,
        size: doc.size || 0,
        uploadedAt: doc.uploadedAt,
        uploadedBy: doc.uploadedBy || 'unknown',
        proposalId: null,
        influencerId: influencerId
      }));

      console.log(`✅ [INDEPENDENT] ${formattedDocuments.length} documentos encontrados no registro financeiro`);
      
      return NextResponse.json({
        success: true,
        documents: formattedDocuments,
        structure: 'independent',
        source: 'influencer_financials',
        total: formattedDocuments.length
      });
    }
    
  } catch (error) {
    console.error('❌ [GET_DOCS] Erro ao buscar documentos:', error);
    return NextResponse.json(
      { 
        error: 'Falha ao buscar documentos',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
} 