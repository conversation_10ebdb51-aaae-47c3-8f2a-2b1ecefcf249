'use client';

import { useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';

export function ClerkInviteHandler() {
  const { isSignedIn, isLoaded } = useAuth();

  useEffect(() => {
    if (!isLoaded) return;

    const currentUrl = new URL(window.location.href);
    
    // Detectar JWT problemático do Clerk
    const clerkDbJwt = currentUrl.searchParams.get('__clerk_db_jwt');
    
    if (clerkDbJwt) {
      console.log('🔧 JWT problemático detectado e removido:', clerkDbJwt);
      
      // Limpar a URL removendo o JWT problemático
      currentUrl.searchParams.delete('__clerk_db_jwt');
      
      // Se o usuário já está logado, redirecionar para auth-redirect
      if (isSignedIn) {
        console.log('✅ Usuário já logado, redirecionando para auth-redirect');
        window.location.href = '/auth-redirect';
        return;
      }
      
      // Se não está logado, limpar a URL e permanecer na página de signup
      console.log('🔄 Limpando URL problemática');
      window.history.replaceState(null, '', currentUrl.toString());
      return;
    }

    // Detectar outros parâmetros problemáticos do Clerk
    const clerkError = currentUrl.searchParams.get('__clerk_error');
    const clerkRedirect = currentUrl.searchParams.get('__clerk_redirect');
    
    if (clerkError || clerkRedirect) {
      console.log('🔧 Parâmetros problemáticos do Clerk detectados e removidos');
      
      // Limpar parâmetros problemáticos
      currentUrl.searchParams.delete('__clerk_error');
      currentUrl.searchParams.delete('__clerk_redirect');
      
      if (isSignedIn) {
        window.location.href = '/auth-redirect';
        return;
      }
      
      window.history.replaceState(null, '', currentUrl.toString());
    }

    // Tratar convites pendentes
    const inviteToken = currentUrl.searchParams.get('invite_token') || currentUrl.searchParams.get('token');
    
    if (inviteToken && isSignedIn) {
      console.log('📨 Convite detectado após login, processando...');
      // Armazenar token do convite para processar após completar signup
      sessionStorage.setItem('pending_invite_token', inviteToken);
      window.location.href = '/auth-redirect';
    }

  }, [isLoaded, isSignedIn]);

  return null;
} 