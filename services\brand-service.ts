import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  limit,
  startAfter
} from 'firebase/firestore';
import { clientDb as db } from '@/lib/firebase-client';
import { Brand, CreateBrandData, UpdateBrandData } from '@/types/brand';
import { IsolationUtils } from '@/lib/utils/isolation';

export class BrandService {
  private static readonly COLLECTION_NAME = 'brands';

  /**
   * Buscar todas as marcas
   */
  static async getAllBrands(): Promise<Brand[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        orderBy('name', 'asc')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as Brand;
      });
    } catch (error) {
      console.error('Erro ao buscar marcas:', error);
      throw new Error('Falha ao buscar marcas');
    }
  }

  /**
   * Buscar marcas por segmento
   */
  static async getBrandsByIndustry(industry: string): Promise<Brand[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('industry', '==', industry),
        orderBy('name', 'asc')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as Brand;
      });
    } catch (error) {
      console.error(`Erro ao buscar marcas do segmento ${industry}:`, error);
      throw new Error('Falha ao buscar marcas por segmento');
    }
  }



  /**
   * Buscar marcas por usuário
   */
  static async getBrandsByUserId(userId: string): Promise<Brand[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('userId', '==', userId),
        orderBy('name', 'asc')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as Brand;
      });
    } catch (error) {
      console.error(`Erro ao buscar marcas do usuário ${userId}:`, error);
      throw new Error('Falha ao buscar marcas do usuário');
    }
  }

  /**
   * 📝 FASE 4.3: Criar nova marca com isolamento automático
   * ✅ CORREÇÃO: Retorna marca completa para atualização imediata da UI
   */
  static async createBrand(brandData: CreateBrandData, userId: string): Promise<Brand> {
    try {
      // Validar userId
      if (!userId) {
        throw new Error('UserId é obrigatório para criar marca');
      }

      // Verificar se já existe marca com mesmo nome para o usuário
      const existingBrand = await this.findBrandByNameAndUser(brandData.name, userId);
      if (existingBrand) {
        throw new Error(`Marca com nome "${brandData.name}" já existe para este usuário`);
      }

      // Preparar dados com isolamento
      const dataToAdd = IsolationUtils.prepareCreateData(brandData, userId);
      
      // Criar marca no Firestore
      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), dataToAdd);
      
      // ✅ CORREÇÃO: Buscar e retornar a marca completa criada
      const createdBrand = await this.getBrandById(docRef.id, userId);
      
      if (!createdBrand) {
        throw new Error('Erro ao recuperar marca criada');
      }
      
      // Log de auditoria
      IsolationUtils.logIsolationEvent(
        'create',
        this.COLLECTION_NAME,
        docRef.id,
        userId,
        { brandName: brandData.name }
      );
      
      console.log('[BRAND_SERVICE] Marca criada com sucesso:', {
        id: createdBrand.id,
        name: createdBrand.name,
        userId: createdBrand.userId
      });
      
      return createdBrand;
    } catch (error) {
      console.error('[BRAND_SERVICE_CREATE]', error);
      throw error;
    }
  }

  /**
   * 📝 FASE 4.3: Método auxiliar para buscar marca por nome e usuário
   */
  private static async findBrandByNameAndUser(name: string, userId: string): Promise<Brand | null> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('name', '==', name),
        where('userId', '==', userId),
        limit(1)
      );
      
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        return null;
      }
      
      const doc = snapshot.docs[0];
      const data = doc.data();
      
      return {
        ...data,
        id: doc.id,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Brand;
    } catch (error) {
      console.error('[BRAND_SERVICE_FIND_BY_NAME]', error);
      return null;
    }
  }

  /**
   * 📝 FASE 4.3: Buscar marcas do usuário com paginação e filtros
   */
  static async getBrandsByUser(
    userId: string, 
    options: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ brands: Brand[]; total: number }> {
    try {
      const {
        page = 1,
        limit: limitCount = 10,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      // Construir query base
      let q = query(
        collection(db, this.COLLECTION_NAME),
        where('userId', '==', userId)
      );

      // Aplicar ordenação
      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        q = query(q, orderBy(sortBy, sortOrder));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }

      const snapshot = await getDocs(q);
      let brands = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as Brand;
      });

      // Aplicar filtro de busca no cliente (Firestore não suporta busca full-text)
      if (search) {
        brands = brands.filter(brand =>
          brand.name.toLowerCase().includes(search.toLowerCase()) ||
          brand.industry?.toLowerCase().includes(search.toLowerCase())
        );
      }

      const total = brands.length;
      const startIndex = (page - 1) * limitCount;
      const paginatedBrands = brands.slice(startIndex, startIndex + limitCount);

      return {
        brands: paginatedBrands,
        total
      };
    } catch (error) {
      console.error('[BRAND_SERVICE_GET_BY_USER]', error);
      throw new Error('Falha ao buscar marcas do usuário');
    }
  }

  /**
   * 📝 FASE 4.3: Buscar marca por ID com validação de ownership
   */
  static async getBrandById(id: string, userId?: string): Promise<Brand | null> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return null;
      }
      
      const data = docSnap.data();
      const brand = {
        ...data,
        id: docSnap.id,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Brand;

      // Se userId for fornecido, validar ownership
      if (userId && !IsolationUtils.validateOwnership(brand, userId)) {
        return null; // Não retornar marca se não pertencer ao usuário
      }
      
      return brand;
    } catch (error) {
      console.error(`[BRAND_SERVICE_GET_BY_ID] ID: ${id}`, error);
      throw new Error('Falha ao buscar marca');
    }
  }

  /**
   * 📝 FASE 4.3: Atualizar marca com validação de ownership
   */
  static async updateBrand(id: string, brandData: UpdateBrandData, userId: string): Promise<void> {
    try {
      // Validar se marca existe e pertence ao usuário
      const existingBrand = await this.getBrandById(id);
      
      if (!existingBrand) {
        throw new Error('Marca não encontrada');
      }

      if (!IsolationUtils.validateOwnership(existingBrand, userId)) {
        throw new Error('Usuário não tem permissão para editar esta marca');
      }

      // Se o nome está sendo alterado, verificar duplicatas
      if (brandData.name && brandData.name !== existingBrand.name) {
        const duplicateBrand = await this.findBrandByNameAndUser(brandData.name, userId);
        if (duplicateBrand && duplicateBrand.id !== id) {
          throw new Error(`Marca com nome "${brandData.name}" já existe para este usuário`);
        }
      }

      // Preparar dados de atualização
      const updateData = IsolationUtils.prepareUpdateData(brandData, userId);
      
      // Atualizar marca
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await updateDoc(docRef, updateData);

      // Log de auditoria
      IsolationUtils.logIsolationEvent(
        'update',
        this.COLLECTION_NAME,
        id,
        userId,
        { updatedFields: Object.keys(brandData) }
      );
    } catch (error) {
      console.error(`[BRAND_SERVICE_UPDATE] ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 📝 FASE 4.3: Deletar marca com validação de ownership
   */
  static async deleteBrand(id: string, userId: string): Promise<boolean> {
    try {
      // Validar se marca existe e pertence ao usuário
      const existingBrand = await this.getBrandById(id);
      
      if (!existingBrand) {
        throw new Error('Marca não encontrada');
      }

      if (!IsolationUtils.validateOwnership(existingBrand, userId)) {
        throw new Error('Usuário não tem permissão para deletar esta marca');
      }

      // TODO: Verificar se marca tem campanhas associadas
      // const campaigns = await CampaignService.getCampaignsByBrand(id);
      // if (campaigns.length > 0) {
      //   throw new Error('Não é possível deletar marca que possui campanhas associadas');
      // }

      // Deletar marca
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await deleteDoc(docRef);

      // Log de auditoria
      IsolationUtils.logIsolationEvent(
        'delete',
        this.COLLECTION_NAME,
        id,
        userId,
        { brandName: existingBrand.name }
      );
      
      return true;
    } catch (error) {
      console.error(`[BRAND_SERVICE_DELETE] ID: ${id}`, error);
      throw error;
    }
  }



  /**
   * Verificar se uma marca existe
   */
  static async brandExists(id: string): Promise<boolean> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      return docSnap.exists();
    } catch (error) {
      console.error(`Erro ao verificar existência da marca ${id}:`, error);
      return false;
    }
  }

  /**
   * Buscar marcas por nome (busca parcial)
   */
  static async searchBrandsByName(searchTerm: string): Promise<Brand[]> {
    try {
      // Firestore não suporta busca de texto completo nativamente
      // Esta é uma implementação básica que busca marcas e filtra no cliente
      const q = query(
        collection(db, this.COLLECTION_NAME),
        orderBy('name', 'asc')
      );
      const snapshot = await getDocs(q);
      
      const brands = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date()
        } as Brand;
      });
      
      // Filtrar no cliente por nome (case-insensitive)
      return brands.filter(brand => 
        brand.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error(`Erro ao buscar marcas por nome "${searchTerm}":`, error);
      throw new Error('Falha ao buscar marcas por nome');
    }
  }
}

