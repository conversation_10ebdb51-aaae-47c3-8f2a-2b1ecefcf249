'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useFirebaseAuth, useRouteProtection } from '@/contexts/firebase-auth-context';
import { Loader } from '@/components/ui/loader';

interface RouteGuardProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredPermission?: string;
  fallbackRoute?: string;
  allowedRoles?: string[];
}

export function RouteGuard({
  children,
  requiredRole,
  requiredPermission,
  fallbackRoute = '/dashboard',
  allowedRoles
}: RouteGuardProps) {
  const { currentUser, isLoading, hasPermission } = useFirebaseAuth();
  const { requireAuth, requireRole, requirePermission } = useRouteProtection();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (isLoading) return;

    // Verificar autenticação
    if (!requireAuth()) return;

    // Verificar papel específico
    if (requiredRole && !requireRole(requiredRole)) {
      return;
    }

    // Verificar permissão específica
    if (requiredPermission && !requirePermission(requiredPermission)) {
      return;
    }

    // Verificar papéis permitidos
    if (allowedRoles && !allowedRoles.some(role => currentUser?.role === role)) {
      router.push(fallbackRoute);
      return;
    }

    setIsChecking(false);
  }, [currentUser, isLoading, requiredRole, requiredPermission, allowedRoles, hasPermission, router, fallbackRoute]);

  // Mostrar loading enquanto verifica autenticação
  if (isLoading || isChecking) {
    return <Loader isLoading={true} message="" showLogo={true} />;
  }

  // Se chegou até aqui, o usuário tem permissão
  return <>{children}</>;
}

// Componentes específicos para diferentes tipos de proteção
export function AdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard allowedRoles={['super_admin', 'admin']}>
      {children}
    </RouteGuard>
  );
}

export function BrandRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard allowedRoles={['admin', 'manager', 'viewer']}>
      {children}
    </RouteGuard>
  );
}

export function ManagerRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard allowedRoles={['super_admin', 'admin', 'manager']}>
      {children}
    </RouteGuard>
  );
}

export function PermissionRoute({ 
  children, 
  permission 
}: { 
  children: React.ReactNode;
  permission: string;
}) {
  return (
    <RouteGuard requiredPermission={permission}>
      {children}
    </RouteGuard>
  );
}

