// FUNÇÕES PARA DADOS FINANCEIROS

// Importações necessárias
import { db } from './firebase';
import { InfluencerFinancial } from '../types/influencer-financial';

// Referência à coleção de dados financeiros
export const financialsCollection = db.collection('influencer_financials');

// Interface para entrada de dados financeiros (para uso nas funções)
interface FinancialInput {
  influencerId: string;
  responsibleName?: string;
  agencyName?: string;
  email?: string;
  whatsapp?: string;
  instagramStoriesViews?: number;
  prices?: {
        instagramStory?: {
          name?: string;
          price?: number;
        };
        instagramReel?: {
          name?: string;
          price?: number;
        };
        tiktokVideo?: {
          name?: string;
          price?: number;
        };
        youtubeInsertion?: {
          name?: string;
          price?: number;
        };
        youtubeDedicated?: {
          name?: string;
          price?: number;
        };
        youtubeShorts?: {
          name?: string;
          price?: number;
        };
      };
  brandHistory?: {
    instagram?: string[];
    tiktok?: string[];
    youtube?: string[];
  };
  additionalData?: {
    contentType?: string[];
    promotesTraders?: boolean;
    responsibleRecruiter?: string;
    socialMediaScreenshots?: string[];
    notes?: string;
    documents?: {
      name: string;
      url: string;
      type: string;
      uploadedAt: Date;
    }[];
  };
}

// Buscar todos os registros financeiros
export async function getAllFinancials(): Promise<InfluencerFinancial[]> {
  try {
    const snapshot = await financialsCollection.get();
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      };
    }) as InfluencerFinancial[];
  } catch (error) {
    console.error('Erro ao buscar todos os registros financeiros:', error);
    throw error;
  }
}

// Buscar um registro financeiro específico por ID
export async function getFinancialById(id: string): Promise<InfluencerFinancial | null> {
  try {
    const doc = await financialsCollection.doc(id).get();
    if (!doc.exists) {
      return null;
    }
    const data = doc.data();
    if (!data) {
      return null;
    }
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date()
    } as InfluencerFinancial;
  } catch (error) {
    console.error(`Erro ao buscar registro financeiro com ID ${id}:`, error);
    throw error;
  }
}

// Buscar registro financeiro pelo ID do influenciador
export async function getFinancialByInfluencerId(influencerId: string): Promise<InfluencerFinancial | null> {
  try {
    const snapshot = await financialsCollection
      .where('influencerId', '==', influencerId)
      .limit(1)
      .get();
    
    if (snapshot.empty) {
      return null;
    }
    
    const doc = snapshot.docs[0];
    const data = doc.data();
    if (!data) {
      return null;
    }
    
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date()
    } as InfluencerFinancial;
  } catch (error) {
    console.error(`Erro ao buscar registro financeiro para influenciador ${influencerId}:`, error);
    throw error;
  }
}

// Adicionar um novo registro financeiro
export async function addFinancial(data: FinancialInput): Promise<string> {
  try {
    console.log('🔍 Firebase addFinancial - Dados recebidos:', JSON.stringify(data, null, 2));
    
    // Garantir a estrutura correta dos dados
    const financialData = {
      influencerId: data.influencerId,
      responsibleName: data.responsibleName || '',
      agencyName: data.agencyName || '',
      email: data.email || '',
      whatsapp: data.whatsapp || '',
      instagramStoriesViews: data.instagramStoriesViews || 0,
      
      // Preços
      prices: {
        instagramStory: {
          name: data.prices?.instagramStory?.name || "Stories",
          price: data.prices?.instagramStory?.price || 0
        },
        instagramReel: {
          name: data.prices?.instagramReel?.name || "Reels",
          price: data.prices?.instagramReel?.price || 0
        },
        tiktokVideo: {
          name: data.prices?.tiktokVideo?.name || "Vídeo",
          price: data.prices?.tiktokVideo?.price || 0
        },
        youtubeInsertion: {
          name: data.prices?.youtubeInsertion?.name || "Inserção",
          price: data.prices?.youtubeInsertion?.price || 0
        },
        youtubeDedicated: {
          name: data.prices?.youtubeDedicated?.name || "Dedicado",
          price: data.prices?.youtubeDedicated?.price || 0
        },
        youtubeShorts: {
          name: data.prices?.youtubeShorts?.name || "Shorts",
          price: data.prices?.youtubeShorts?.price || 0
        }
      },
      
      // Histórico de marcas
      brandHistory: {
        instagram: data.brandHistory?.instagram || [],
        tiktok: data.brandHistory?.tiktok || [],
        youtube: data.brandHistory?.youtube || []
      },
      
      // Dados adicionais
      additionalData: {
        contentType: data.additionalData?.contentType || [],
        promotesTraders: data.additionalData?.promotesTraders || false,
        responsibleRecruiter: data.additionalData?.responsibleRecruiter || '',
        socialMediaScreenshots: data.additionalData?.socialMediaScreenshots || [],
        notes: data.additionalData?.notes || '',
        documents: data.additionalData?.documents || []
      },
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    console.log('💾 Dados formatados para o Firebase:', JSON.stringify(financialData, null, 2));
    
    const docRef = await financialsCollection.add(financialData);
    
    console.log('✅ Documento criado com ID:', docRef.id);
    
    return docRef.id;
  } catch (error) {
    console.error('💥 Erro ao adicionar registro financeiro:', error);
    throw error;
  }
}

// Atualizar um registro financeiro
export async function updateFinancial(id: string, data: Partial<FinancialInput>): Promise<boolean> {
  try {
    // Buscar registro atual
    const existingDoc = await financialsCollection.doc(id).get();
    if (!existingDoc.exists) {
      throw new Error(`Registro financeiro com ID ${id} não encontrado`);
    }
    
    // Atualizar apenas os campos fornecidos
    const updateData: Record<string, any> = {
      updatedAt: new Date()
    };
    
    // Campos básicos
    if (data.responsibleName !== undefined) updateData.responsibleName = data.responsibleName;
    if (data.agencyName !== undefined) updateData.agencyName = data.agencyName;
    if (data.email !== undefined) updateData.email = data.email;
    if (data.whatsapp !== undefined) updateData.whatsapp = data.whatsapp;
    if (data.instagramStoriesViews !== undefined) updateData.instagramStoriesViews = data.instagramStoriesViews;
    
    // Preços (atualizações parciais)
    if (data.prices) {
      const existing = existingDoc.data()?.prices || {};
      updateData['prices'] = {
        ...existing,
        ...Object.entries(data.prices).reduce((acc, [key, value]) => {
          if (value !== undefined) acc[key] = value;
          return acc;
        }, {} as Record<string, number>)
      };
    }
    
    // Histórico de marcas (atualizações parciais)
    if (data.brandHistory) {
      const existing = existingDoc.data()?.brandHistory || {};
      updateData['brandHistory'] = {
        ...existing,
        ...Object.entries(data.brandHistory).reduce((acc, [key, value]) => {
          if (value !== undefined) acc[key] = value;
          return acc;
        }, {} as Record<string, string[]>)
      };
    }
    
    // Dados adicionais (atualizações parciais)
    if (data.additionalData) {
      const existing = existingDoc.data()?.additionalData || {};
      updateData['additionalData'] = {
        ...existing,
        ...Object.entries(data.additionalData).reduce((acc, [key, value]) => {
          if (value !== undefined) acc[key] = value;
          return acc;
        }, {} as Record<string, any>)
      };
    }
    
    await financialsCollection.doc(id).update(updateData);
    return true;
  } catch (error) {
    console.error(`Erro ao atualizar registro financeiro com ID ${id}:`, error);
    throw error;
  }
}

// Excluir um registro financeiro
export async function deleteFinancial(id: string): Promise<boolean> {
  try {
    const docRef = financialsCollection.doc(id);
    const doc = await docRef.get();
    
    if (!doc.exists) {
      throw new Error(`Registro financeiro com ID ${id} não encontrado`);
    }
    
    await docRef.delete();
    return true;
  } catch (error) {
    console.error(`Erro ao excluir registro financeiro com ID ${id}:`, error);
    throw error;
  }
}


