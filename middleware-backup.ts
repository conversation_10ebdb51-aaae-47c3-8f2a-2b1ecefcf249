import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || '';
const COOKIE_NAME = 'brand-session';

// Cache simples para rate limiting (em produção, usar Redis)
const rateLimitCache = new Map<string, { count: number; resetTime: number }>();

// Função para verificar JWT com validação de assinatura
async function verifyJWT(token: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Token JWT inválido');
    }
    
    const [header, payload, signature] = parts;
    
    // Verificar assinatura usando Web Crypto API
    const encoder = new TextEncoder();
    const data = encoder.encode(`${header}.${payload}`);
    const secretKey = encoder.encode(JWT_SECRET);
    
    // Importar chave secreta
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      secretKey,
      { name: 'HM<PERSON>', hash: 'SHA-256' },
      false,
      ['verify']
    );
    
    // Decodificar assinatura
    const signatureBytes = Uint8Array.from(
      atob(signature.replace(/-/g, '+').replace(/_/g, '/')), 
      c => c.charCodeAt(0)
    );
    
    // Verificar assinatura
    const isValid = await crypto.subtle.verify(
      'HMAC',
      cryptoKey,
      signatureBytes,
      data
    );
    
    if (!isValid) {
      throw new Error('Assinatura JWT inválida');
    }
    
    // Decodificar payload
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
    return decoded;
  } catch (error) {
    throw new Error('Token JWT inválido');
  }
}

// Rate limiting por IP
function checkRateLimit(ip: string): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutos
  const maxRequests = 500; // Aumentado de 100 para 500 requests por 15 min
  
  const current = rateLimitCache.get(ip);
  
  if (!current || now > current.resetTime) {
    const resetTime = now + windowMs;
    rateLimitCache.set(ip, { count: 1, resetTime });
    return { allowed: true, remaining: maxRequests - 1, resetTime };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  return { allowed: true, remaining: maxRequests - current.count, resetTime: current.resetTime };
}

// Rotas públicas que não precisam de autenticação
const PUBLIC_ROUTES = ['/login', '/api/auth/me', '/api/auth/login', '/api/auth/logout', '/api/auth/register', '/api/auth/forgot-password', 
  '/api/auth/reset-password', '/api/auth/verify-email', '/api/auth/verify-email-token', '/api/auth/refresh-token', '/api/graphql'
 ];

// Rotas de compartilhamento público que usam token de acesso próprio
const SHARED_ROUTES = ['/api/shared/', '/shared/'];

// Rotas que só admins podem acessar (incluindo sub-rotas dinâmicas)
const ADMIN_ONLY_ROUTES = [
  '/admin',           // /admin, /admin/*, /admin/users, etc.
  '/creators',        // /creators - proibida para role user
];

// Rotas que usuários podem acessar
const USER_ALLOWED_ROUTES = [
  '/influencers',    // Rotas de influencers para role USER
  '/api/proposals',  // APIs de propostas (para propostas compartilhadas)
  '/api/budgets',    // APIs de orçamentos relacionados
  '/api/notes',      // APIs de notas relacionadas
  '/api/snapshots',  // APIs de snapshots para colaboradores
];

// Rotas que managers e viewers podem acessar (além das rotas de user)
const MANAGER_VIEWER_ALLOWED_ROUTES = [
    // Rotas de campanhas em inglês
  '/proposals',      // Propostas
  '/propostas',      // Propostas em português
       // Analytics
  '/settings',       // Configurações
  '/profile'         // Perfil
];

// APIs que requerem autenticação admin (operações completas)
const ADMIN_API_ROUTES = [
  '/api/admin'
];

// APIs que requerem autenticação admin apenas para escrita (POST, PUT, DELETE)
const ADMIN_WRITE_ONLY_ROUTES = [
  '/api/influencers'
];

interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  brandId?: string;
  iat: number;
  exp: number;
}

// Função para determinar a rota padrão baseada no papel do usuário
function getDefaultRouteForRole(role: string): string {
  switch (role) {
    case 'super_admin':
    case 'admin':
      return '/admin';
    case 'user':
      return '/influencers';  // Usuários com role USER vão para /influencers
    case 'manager':
    case 'viewer':
      return '/campanhas/dashboard';
    default:
      return '/login';
  }
}

// Função para tentar recuperar token de forma mais robusta
async function getTokenFromRequest(request: NextRequest): Promise<string | null> {
  try {
    // Primeira tentativa: usar cookies()
    const cookieStore = await cookies();
    const tokenFromCookies = cookieStore.get(COOKIE_NAME)?.value;
    
    if (tokenFromCookies) {
      return tokenFromCookies;
    }

    // Segunda tentativa: header Authorization
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Terceira tentativa: cookie diretamente do header
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').map(c => c.trim());
      const sessionCookie = cookies.find(c => c.startsWith(`${COOKIE_NAME}=`));
      if (sessionCookie) {
        return sessionCookie.split('=')[1];
      }
    }

    return null;
  } catch (error) {
    console.error('❌ Erro ao recuperar token:', error);
    return null;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
             request.headers.get('x-real-ip') || 
             'unknown';
  
  // Rate limiting
  const rateLimitResult = checkRateLimit(ip);
  if (!rateLimitResult.allowed) {
    return new NextResponse('Rate limit exceeded', { status: 429 });
  }
  
  // Permitir acesso a rotas públicas
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Permitir acesso a rotas de compartilhamento público (usam token próprio)
  if (SHARED_ROUTES.some(route => pathname.startsWith(route))) {
    console.log(`✅ Acesso liberado para rota compartilhada: ${pathname}`);
    return addSecurityHeaders(NextResponse.next(), { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });
  }

  // Permitir acesso a assets estáticos (mas não APIs)
  if (
    pathname.startsWith('/_next/static') ||
    pathname.startsWith('/_next/image') ||
    pathname.startsWith('/favicon.ico') ||
    (pathname.includes('.') && !pathname.startsWith('/api'))
  ) {
    return NextResponse.next();
  }

  try {
    // Usar função mais robusta para recuperar token
    const token = await getTokenFromRequest(request);

    console.log(`🔍 Middleware - Rota: ${pathname}, Token existe: ${!!token}`);

    // Se não há token, redirecionar para login
    if (!token) {
      console.log(`❌ Token não encontrado para ${pathname}`);
      if (pathname.startsWith('/api')) {
        return new NextResponse('Unauthorized', { status: 401 });
      }
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Verificar JWT com validação de assinatura
    let decoded: TokenPayload;
    try {
      decoded = await verifyJWT(token) as TokenPayload;
      console.log(`✅ Token válido - User: ${decoded.email}, Role: ${decoded.role}`);
    } catch (error) {
      console.log(`❌ Token inválido: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      // Token inválido, limpar cookie e redirecionar/retornar erro
      const response = pathname.startsWith('/api') 
        ? new NextResponse('Invalid token', { status: 401 })
        : NextResponse.redirect(new URL('/login', request.url));
        
      response.cookies.set(COOKIE_NAME, '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
        path: '/'
      });
      return response;
    }

    // Verificar se o token não expirou
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp < now) {
      console.log(`❌ Token expirado para ${decoded.email}`);
      const response = pathname.startsWith('/api') 
        ? new NextResponse('Token expired', { status: 401 })
        : NextResponse.redirect(new URL('/login', request.url));
        
      response.cookies.set(COOKIE_NAME, '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0,
        path: '/'
      });
      return response;
    }

    // Validação JWT já foi feita acima com verificação de assinatura e expiração
    // Token válido - prosseguir com verificações de permissão

    // Verificar permissões baseadas no papel do usuário
    const userRole = decoded.role;

    // Verificar acesso a APIs que requerem admin completo
    if (ADMIN_API_ROUTES.some(route => pathname.startsWith(route))) {
      if (userRole !== 'super_admin' && userRole !== 'admin') {
        console.error(`❌ Acesso negado - Role ${userRole} não tem permissão para ${pathname}`);
        return new NextResponse('Forbidden - Admin access required', { status: 403 });
      }
    }

    // Verificar acesso a APIs que requerem admin apenas para escrita
    if (ADMIN_WRITE_ONLY_ROUTES.some(route => pathname.startsWith(route))) {
      const method = request.method;
      
      // Operações de escrita (POST, PUT, DELETE) exigem permissão de admin
      if (['POST', 'PUT', 'DELETE'].includes(method)) {
        if (userRole !== 'super_admin' && userRole !== 'admin') {
          console.error(`❌ Operação de escrita negada - Role ${userRole} não tem permissão para ${method} em ${pathname}`);
          return new NextResponse('Forbidden - Admin access required for write operations', { status: 403 });
        }
      }
      // Operações de leitura (GET) são permitidas para usuários autenticados
    }

    // Super Admin e Admin podem acessar tudo
    if (userRole === 'super_admin' || userRole === 'admin') {
      console.log(`✅ Acesso liberado: ${userRole} acessando: ${pathname}`);
      const response = NextResponse.next();
      
      // Adicionar header com rota padrão do usuário para detecção de discrepâncias no frontend
      response.headers.set('X-User-Default-Route', getDefaultRouteForRole(userRole));
      response.headers.set('X-User-Role', userRole);
      
      return addSecurityHeaders(response, { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });
    }

    // Verificar se é uma rota exclusiva para admins
    const isAdminRoute = ADMIN_ONLY_ROUTES.some(route => pathname.startsWith(route));
    if (isAdminRoute) {
      console.log(`🚫 Acesso negado: ${userRole} tentou acessar rota admin: ${pathname}`);
      
      if (pathname.startsWith('/api')) {
        return new NextResponse('Forbidden - Admin access required', { status: 403 });
      }
      
      // Evitar loop de redirecionamento
      const defaultRoute = getDefaultRouteForRole(userRole);
      if (pathname !== defaultRoute && !pathname.startsWith(defaultRoute + '/')) {
        console.log(`🔄 Redirecionando ${userRole} de ${pathname} para ${defaultRoute}`);
        return NextResponse.redirect(new URL(defaultRoute, request.url));
      }
      
      // Se já está na rota correta, permitir acesso
      return NextResponse.next();
    }

    // Para role USER, permitir acesso APENAS às rotas em USER_ALLOWED_ROUTES
    if (userRole === 'user') {
      const isAllowed = USER_ALLOWED_ROUTES.some(route => 
        pathname === route || pathname.startsWith(route + '/')
      );
      
      if (!isAllowed) {
        console.log(`🚫 Acesso negado: role ${userRole} tentou acessar rota não permitida: ${pathname}`);
        
        if (pathname.startsWith('/api')) {
          return new NextResponse('Forbidden', { status: 403 });
        }
        
        // Redirecionar para rota padrão do usuário
        const defaultRoute = getDefaultRouteForRole(userRole);
        if (pathname !== defaultRoute && !pathname.startsWith(defaultRoute + '/')) {
          console.log(`🔄 Redirecionando ${userRole} de ${pathname} para ${defaultRoute}`);
          return NextResponse.redirect(new URL(defaultRoute, request.url));
        }
        
        // Se já está na rota correta, permitir acesso
        return NextResponse.next();
      }
    }

    // Para roles MANAGER e VIEWER, permitir acesso às rotas de user + rotas específicas
    if (userRole === 'manager' || userRole === 'viewer') {
      const allowedRoutes = [...USER_ALLOWED_ROUTES, ...MANAGER_VIEWER_ALLOWED_ROUTES];
      const isAllowed = allowedRoutes.some(route => 
        pathname === route || pathname.startsWith(route + '/')
      );
      
      if (!isAllowed) {
        console.log(`🚫 Acesso negado: role ${userRole} tentou acessar rota não permitida: ${pathname}`);
        
        if (pathname.startsWith('/api')) {
          return new NextResponse('Forbidden', { status: 403 });
        }
        
        // Redirecionar para rota padrão do usuário
      const defaultRoute = getDefaultRouteForRole(userRole);
      if (pathname !== defaultRoute && !pathname.startsWith(defaultRoute + '/')) {
        console.log(`🔄 Redirecionando ${userRole} de ${pathname} para ${defaultRoute}`);
        return NextResponse.redirect(new URL(defaultRoute, request.url));
      }
      
      // Se já está na rota correta, permitir acesso
      return NextResponse.next();
      }
    }

    // Adicionar headers para todos os usuários autenticados
    const response = NextResponse.next();
    response.headers.set('X-User-Default-Route', getDefaultRouteForRole(userRole));
    response.headers.set('X-User-Role', userRole);
    
    return addSecurityHeaders(response, { remaining: rateLimitResult.remaining, resetTime: rateLimitResult.resetTime });

  } catch (error) {
    // Em caso de erro crítico, negar acesso
    if (pathname.startsWith('/api')) {
      return new NextResponse('Internal server error', { status: 500 });
    }
    
    const response = NextResponse.redirect(new URL('/login', request.url));
    response.cookies.set(COOKIE_NAME, '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    });
    return response;
  }
}

// Adicionar headers de segurança e rate limit
function addSecurityHeaders(response: NextResponse, rateLimitInfo?: { remaining: number; resetTime: number }) {
  // Content Security Policy - ✅ CORREÇÃO: Incluir todas as URLs necessárias do Firebase
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://firebase.googleapis.com https://*.firebase.googleapis.com https://*.firebaseio.com wss://*.firebaseio.com https://firebaseinstallations.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.google-analytics.com https://analytics.google.com https://firestore.googleapis.com https://*.firestore.googleapis.com wss://firestore.googleapis.com wss://*.firestore.googleapis.com https://viacep.com.br; frame-src 'none';"
  );
  
  // Rate limit headers se fornecidos
  if (rateLimitInfo) {
    response.headers.set('X-RateLimit-Limit', '500');
    response.headers.set('X-RateLimit-Remaining', rateLimitInfo.remaining.toString());
    response.headers.set('X-RateLimit-Reset', Math.ceil(rateLimitInfo.resetTime / 1000).toString());
  }
  
  // Outros headers de segurança
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};

// Função para testar proteção de rotas (desenvolvimento)
export function testRouteProtection() {
  const testCases = [
    '/influencers',
    '/influencers/user123',
    '/api/proposals',
    '/api/proposals/123',
    '/api/budgets',
    //'/api/budgets/counter-proposal',
    '/api/notes',
    '/admin',
    '/admin/users',
    '/campanhas/dashboard',
    '/proposals',
    '/propostas'
  ];

  console.log('🧪 Testando proteção de rotas (Atualizado para Propostas Compartilhadas):');
  console.log('📋 Configuração atual:');
  console.log('- ADMIN_ONLY_ROUTES:', ADMIN_ONLY_ROUTES);
  console.log('- USER_ALLOWED_ROUTES (role USER - inclui APIs para propostas compartilhadas):', USER_ALLOWED_ROUTES);
  console.log('- MANAGER_VIEWER_ALLOWED_ROUTES (roles MANAGER/VIEWER):', MANAGER_VIEWER_ALLOWED_ROUTES);
  console.log('- SHARED_ROUTES (token próprio, sem auth):', SHARED_ROUTES);
  console.log('');
  
  testCases.forEach(route => {
    const isAdminRoute = ADMIN_ONLY_ROUTES.some(adminRoute => route.startsWith(adminRoute));
    const isUserRoute = USER_ALLOWED_ROUTES.some(userRoute => route.startsWith(userRoute));
    const isManagerViewerRoute = MANAGER_VIEWER_ALLOWED_ROUTES.some(managerRoute => route.startsWith(managerRoute));
    const isSharedRoute = SHARED_ROUTES.some(sharedRoute => route.startsWith(sharedRoute));
    
    let access = '';
    if (isSharedRoute) access = '🌐 COMPARTILHAMENTO PÚBLICO (sem auth)';
    else if (isAdminRoute) access = '🚫 ADMIN ONLY';
    else if (isUserRoute) access = '✅ USER + MANAGER + VIEWER (inclui propostas compartilhadas)';
    else if (isManagerViewerRoute) access = '⚠️ MANAGER + VIEWER ONLY';
    else access = '❓ UNKNOWN';
    
    console.log(`${route}: ${access}`);
  });
  
  console.log('');
  console.log('🎯 Resumo das permissões para role USER:');
  console.log('- ✅ Pode acessar /influencers e sub-rotas');
  console.log('- ✅ Pode acessar /api/proposals para propostas compartilhadas');
  console.log('- ✅ Pode acessar /api/budgets para orçamentos');
  console.log('- ✅ Pode acessar /api/notes para anotações');
  console.log('- ❌ NÃO pode acessar APIs admin (/api/admin/*)');
  console.log('- ❌ NÃO pode acessar outras áreas (campanhas, settings, etc.)');
}

