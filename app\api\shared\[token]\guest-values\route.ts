import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';
import { CreateGuestProposalRequest, GuestProposal } from '@/types/proposal-sharing';
import { v4 as uuidv4 } from 'uuid';
import { Proposal } from '@/types/proposal';

// Interface extendida para proposta com orçamentos
interface ProposalWithBudgets extends Proposal {
  budgets?: {
    [profileId: string]: {
      [service: string]: {
        budgetedPrice: number;
        originalPrice: number;
        updatedAt: Date;
        updatedBy: string;
        guestProposals?: GuestProposal[];
      };
    };
  };
}

// POST - Criar nova contraproposta de convidado
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params;
    const proposalRequest: CreateGuestProposalRequest = await request.json();

    // Buscar dados do compartilhamento
    const sharing = await ProposalService.getProposalSharingByToken(token);
    
    if (!sharing) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se está ativo
    if (!sharing.isActive) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento foi desativado' },
        { status: 403 }
      );
    }

    // Verificar se não expirou
    const now = new Date();
    if (sharing.expiresAt && sharing.expiresAt < now) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento expirou' },
        { status: 403 }
      );
    }

    // Verificar se tem permissão de edição
    if (!proposalRequest.guestInfo.permissionLevel || 
        !['edit', 'admin'].includes(proposalRequest.guestInfo.permissionLevel)) {
      return NextResponse.json(
        { success: false, error: 'Permissão insuficiente para criar contrapropostas' },
        { status: 403 }
      );
    }

    // Buscar a proposta
    const proposal = await ProposalService.getProposalById(sharing.proposalId);
    
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    // BUSCAR ORÇAMENTOS NA SUBCOLEÇÃO DE INFLUENCIADORES
    let serviceBudget = null;
    let budgetKey = '';
    let influencerData = null;
    const currentBudgets = (proposal as any).budgets || {};

    // Extrair influencerId do profileId
    const influencerId = proposalRequest.profileId.includes('-') ? 
      (proposalRequest.profileId.split('-').pop() || proposalRequest.profileId) : 
      proposalRequest.profileId;

    console.log('🔍 Tentando buscar na subcoleção:', {
      proposalId: sharing.proposalId,
      profileId: proposalRequest.profileId,
      extractedInfluencerId: influencerId,
      service: proposalRequest.service
    });

    try {
      // Buscar o influenciador na subcoleção
      const influencersRef = ProposalService.getProposalInfluencers(sharing.proposalId);
      const proposalInfluencers = await influencersRef;
      
      console.log('📋 Influenciadores na subcoleção:', {
        count: proposalInfluencers.length,
        influencers: proposalInfluencers.map((inf: any) => ({
          id: inf.id,
          influencerId: inf.influencerId,
          hasBudgets: !!inf.budgets,
          budgetKeys: inf.budgets ? Object.keys(inf.budgets) : []
        }))
      });

      // Encontrar o influenciador específico
      influencerData = proposalInfluencers.find((inf: any) => inf.influencerId === influencerId);
      
      if (influencerData?.budgets?.[proposalRequest.service]) {
        serviceBudget = influencerData.budgets[proposalRequest.service];
        budgetKey = influencerId;
        console.log('✅ Orçamento encontrado na subcoleção!', serviceBudget);
      }
    } catch (error) {
      console.error('❌ Erro ao buscar na subcoleção:', error);
    }

    // Fallback: tentar buscar no documento principal (estrutura antiga)
    if (!serviceBudget) {
      console.log('🔄 Fallback: buscando no documento principal:', {
        budgetsExist: !!currentBudgets,
        budgetKeys: Object.keys(currentBudgets)
      });

      if (currentBudgets[proposalRequest.profileId]?.[proposalRequest.service]) {
        serviceBudget = currentBudgets[proposalRequest.profileId][proposalRequest.service];
        budgetKey = proposalRequest.profileId;
      } else if (currentBudgets[influencerId]?.[proposalRequest.service]) {
        serviceBudget = currentBudgets[influencerId][proposalRequest.service];
        budgetKey = influencerId;
      }
    }

    if (!serviceBudget || !serviceBudget.budgetedPrice) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Serviço não encontrado ou sem orçamento definido',
          debug: {
            profileId: proposalRequest.profileId,
            service: proposalRequest.service,
            availableProfiles: Object.keys(currentBudgets),
            budgetStructure: Object.fromEntries(
              Object.entries(currentBudgets).map(([key, value]) => [
                key, 
                typeof value === 'object' && value ? Object.keys(value) : 'invalid'
              ])
            ),
            proposal: {
              id: proposal.id,
              hasSubcollection: (proposal as any)._usingSubcollection,
              subcollectionCount: (proposal as any).subcollectionInfluencers?.length || 0,
              influencerIds: (proposal as any).influencerIds?.length || 0
            }
          }
        },
        { status: 400 }
      );
    }

    // Validar valor proposto
    if (proposalRequest.value <= 0) {
      return NextResponse.json(
        { success: false, error: 'Valor deve ser maior que zero' },
        { status: 400 }
      );
    }

    if (proposalRequest.value > serviceBudget.budgetedPrice) {
      return NextResponse.json(
        { success: false, error: `Valor deve ser menor ou igual ao orçado (R$ ${serviceBudget.budgetedPrice})` },
        { status: 400 }
      );
    }

    // Verificar limite de propostas por convidado (máximo 3 por serviço)
    const existingProposals = serviceBudget.guestProposals || [];
    const guestProposals = existingProposals.filter(
      (gp: GuestProposal) => gp.proposedBy === proposalRequest.guestInfo.email
    );

    if (guestProposals.length >= 3) {
      return NextResponse.json(
        { success: false, error: 'Limite máximo de 3 contrapropostas por serviço atingido' },
        { status: 400 }
      );
    }

    // Criar nova contraproposta
    const newGuestProposal: GuestProposal = {
      id: uuidv4(),
      value: proposalRequest.value,
      proposedBy: proposalRequest.guestInfo.email,
      proposedAt: new Date(),
      status: 'pending',
      comments: proposalRequest.comments,
      guestInfo: proposalRequest.guestInfo
    };

    // Atualizar orçamento com nova contraproposta
    if (influencerData) {
      // Atualizar na subcoleção (nova estrutura)
      console.log('💾 Atualizando orçamento na subcoleção');
      
      const updatedBudgets = {
        ...influencerData.budgets,
        [proposalRequest.service]: {
          ...serviceBudget,
          guestProposals: [...existingProposals, newGuestProposal]
        }
      };

      await ProposalService.updateInfluencerBudgets(
        sharing.proposalId,
        influencerId,
        updatedBudgets,
        'guest'
      );
    } else {
      // Atualizar no documento principal (estrutura antiga)
      console.log('💾 Atualizando orçamento no documento principal');
      
      const updatedBudgets = {
        ...currentBudgets,
        [budgetKey]: {
          ...currentBudgets[budgetKey],
          [proposalRequest.service]: {
            ...serviceBudget,
            guestProposals: [...existingProposals, newGuestProposal]
          }
        }
      };

      await ProposalService.updateProposalBudgets(sharing.proposalId, updatedBudgets, 'guest');
    }

    console.log('✅ Contraproposta criada:', newGuestProposal.id);

    return NextResponse.json({
      success: true,
      message: 'Contraproposta criada com sucesso',
      data: newGuestProposal
    });

  } catch (error) {
    console.error('❌ Erro ao criar contraproposta:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// GET - Listar contrapropostas de um token
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params;

    // Buscar dados do compartilhamento
    const sharing = await ProposalService.getProposalSharingByToken(token);
    
    if (!sharing) {
      return NextResponse.json(
        { success: false, error: 'Link de compartilhamento não encontrado' },
        { status: 404 }
      );
    }

    // Buscar a proposta
    const proposal = await ProposalService.getProposalById(sharing.proposalId);
    
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    // Extrair todas as contrapropostas
    const allGuestProposals: (GuestProposal & { profileId: string; service: string })[] = [];
    const budgets = (proposal as any).budgets || {};

    Object.entries(budgets).forEach(([profileId, profileBudgets]) => {
      if (profileBudgets && typeof profileBudgets === 'object') {
        Object.entries(profileBudgets).forEach(([service, budget]) => {
          if (budget && typeof budget === 'object' && 'guestProposals' in budget && budget.guestProposals) {
            budget.guestProposals.forEach((gp: GuestProposal) => {
              allGuestProposals.push({
                ...gp,
                profileId,
                service
              });
            });
          }
        });
      }
    });

    return NextResponse.json({
      success: true,
      data: allGuestProposals
    });

  } catch (error) {
    console.error('❌ Erro ao listar contrapropostas:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 