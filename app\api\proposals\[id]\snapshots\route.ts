import { NextRequest, NextResponse } from 'next/server';
// Firebase Auth removido em favor do Clerk
// import { adminAuth } from '@/lib/firebase-admin';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { clientDb as db } from '@/lib/firebase-client';

// GET - Buscar snapshots de uma proposta
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    const { searchParams } = new URL(request.url);
    const influencerId = searchParams.get('influencerId');
    
    console.log('📋 [SNAPSHOTS API] Buscando snapshots:', { proposalId, influencerId });
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    
    // Verificar se a proposta existe
    const proposalDoc = await getDoc(doc(db, 'propostas', proposalId));
    if (!proposalDoc.exists()) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }
    
    const proposalData = proposalDoc.data();
    
    // Verificar se o usuário tem acesso à proposta
    const hasAccess = proposalData?.criadoPor === userId || 
                     proposalData?.brandId === userId ||
                     proposalData?.sharedWith?.includes(userId);
    
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Sem permissão para acessar esta proposta' },
        { status: 403 }
      );
    }
    
    // Verificar se a proposta tem snapshots
    if (!proposalData?.hasSnapshots) {
      return NextResponse.json({
        success: true,
        data: {
          hasSnapshots: false,
          snapshots: [],
          message: 'Esta proposta não possui snapshots capturados'
        }
      });
    }
    
    try {
      if (influencerId) {
        // Buscar snapshot específico de um influenciador
        console.log(`🔍 [SNAPSHOTS API] Buscando snapshot específico: ${influencerId}`);
        
        const snapshotDoc = await getDoc(doc(db, 'propostas', proposalId, 'snapshots', influencerId));
        
        if (!snapshotDoc.exists()) {
          return NextResponse.json(
            { success: false, error: 'Snapshot do influenciador não encontrado' },
            { status: 404 }
          );
        }
        
        const snapshotData = snapshotDoc.data();
        
        return NextResponse.json({
          success: true,
          data: {
            snapshot: {
              id: snapshotDoc.id,
              ...snapshotData,
              capturedAt: snapshotData?.capturedAt?.toDate?.() || snapshotData?.capturedAt
            }
          }
        });
        
      } else {
        // Buscar todos os snapshots da proposta
        console.log('📋 [SNAPSHOTS API] Buscando todos os snapshots da proposta');
        
        const snapshotsRef = collection(db, 'propostas', proposalId, 'snapshots');
        const snapshotsSnapshot = await getDocs(snapshotsRef);
        
        const snapshots = snapshotsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            capturedAt: data?.capturedAt?.toDate?.() || data?.capturedAt
          };
        });
        
        console.log(`✅ [SNAPSHOTS API] Encontrados ${snapshots.length} snapshots`);
        
        return NextResponse.json({
          success: true,
          data: {
            hasSnapshots: true,
            snapshots,
            count: snapshots.length,
            proposalInfo: {
              id: proposalId,
              name: proposalData?.nome,
              snapshotCreatedAt: proposalData?.snapshotCreatedAt?.toDate?.() || proposalData?.snapshotCreatedAt
            }
          }
        });
      }
      
    } catch (firestoreError) {
      console.error('❌ [SNAPSHOTS API] Erro ao buscar snapshots do Firestore:', firestoreError);
      return NextResponse.json(
        { success: false, error: 'Erro ao acessar dados dos snapshots' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ [SNAPSHOTS API] Erro geral:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Verificar status dos snapshots
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    const body = await request.json();
    const { action } = body;
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;
    
    if (action === 'check-status') {
      // Verificar status dos snapshots de uma proposta
      const proposalDoc = await getDoc(doc(db, 'propostas', proposalId));
      
      if (!proposalDoc.exists()) {
        return NextResponse.json(
          { success: false, error: 'Proposta não encontrada' },
          { status: 404 }
        );
      }
      
      const proposalData = proposalDoc.data();
      const hasSnapshots = proposalData?.hasSnapshots === true;
      
      let snapshotsCount = 0;
      let snapshotsInfo: Array<{
        influencerId: string;
        name: string;
        capturedAt: Date | any;
        version: string;
        source: string;
      }> = [];
      
      if (hasSnapshots) {
        try {
          const snapshotsRef = collection(db, 'propostas', proposalId, 'snapshots');
          const snapshotsSnapshot = await getDocs(snapshotsRef);
          
          snapshotsCount = snapshotsSnapshot.size;
          snapshotsInfo = snapshotsSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              influencerId: doc.id,
              name: data?.name,
              capturedAt: data?.capturedAt?.toDate?.() || data?.capturedAt,
              version: data?.version,
              source: data?.source
            };
          });
          
        } catch (error) {
          console.error('Erro ao verificar snapshots:', error);
        }
      }
      
      return NextResponse.json({
        success: true,
        data: {
          proposalId,
          hasSnapshots,
          snapshotsCount,
          snapshotsCreatedAt: proposalData?.snapshotCreatedAt?.toDate?.() || proposalData?.snapshotCreatedAt,
          snapshots: snapshotsInfo
        }
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Ação não reconhecida' },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ [SNAPSHOTS API] Erro em POST:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 