import React from 'react';
import Flag from 'react-world-flags';

interface CountryFlagProps {
  countryCode: string;
  className?: string;
  style?: React.CSSProperties;
}

export const CountryFlag: React.FC<CountryFlagProps> = ({ 
  countryCode, 
  className = "", 
  style = {} 
}) => {
  // Garantir que temos um código válido
  if (!countryCode || countryCode.length !== 2) {
    return (
      <div 
        className={`inline-flex items-center justify-center w-4 h-4 bg-gray-200 text-gray-500 text-xs rounded ${className}`}
        style={style}
      >
        ?
      </div>
    );
  }

  return (
    <Flag
      code={countryCode.toUpperCase()}
      className={`inline-block w-4 h-4 rounded-sm object-cover ${className}`}
      style={{
        width: '16px',
        height: '12px',
        borderRadius: '2px',
        objectFit: 'cover',
        ...style
      }}
      fallback={
        <div 
          className={`inline-flex items-center justify-center w-4 h-4 bg-gray-200 text-gray-500 text-xs rounded ${className}`}
          style={style}
        >
          {countryCode.toUpperCase()}
        </div>
      }
    />
  );
}; 

