// Script de debug para verificar dados de influenciadores no Firestore
// Execute com: node debug-influencer-data.js

const { initializeApp } = require('firebase/app');
const { getFirestore, doc, getDoc, collection, getDocs, query, where } = require('firebase/firestore');

// Configuração do Firebase (use as mesmas configurações do projeto)
const firebaseConfig = {
  // Adicione sua configuração aqui
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function debugInfluencerData(influencerId) {
  console.log(`🔍 Debugando dados do influenciador: ${influencerId}`);
  
  try {
    // 1. Buscar dados principais do influenciador
    const influencerRef = doc(db, 'influencers', influencerId);
    const influencerDoc = await getDoc(influencerRef);
    
    if (!influencerDoc.exists()) {
      console.log('❌ Influenciador não encontrado');
      return;
    }
    
    const influencerData = influencerDoc.data();
    console.log('📋 Dados principais do influenciador:');
    console.log('- Nome:', influencerData.name);
    console.log('- Email:', influencerData.email);
    console.log('- Campos disponíveis:', Object.keys(influencerData));
    
    // 2. Verificar subcoleção de demographics
    console.log('\n📊 Verificando demographics...');
    const demographicsRef = collection(db, 'influencers', influencerId, 'demographics');
    const demographicsSnapshot = await getDocs(demographicsRef);
    
    console.log(`- Documentos de demographics encontrados: ${demographicsSnapshot.docs.length}`);
    demographicsSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`  ${index + 1}. ID: ${doc.id}, Platform: ${data.platform}, Active: ${data.isActive}`);
    });
    
    // 3. Verificar subcoleção de budgets
    console.log('\n💰 Verificando budgets...');
    const budgetsRef = collection(db, 'influencers', influencerId, 'budgets');
    const budgetsSnapshot = await getDocs(budgetsRef);
    
    console.log(`- Documentos de budgets encontrados: ${budgetsSnapshot.docs.length}`);
    budgetsSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`  ${index + 1}. Platform: ${doc.id}, Data:`, data);
    });
    
    // 4. Verificar se há dados de pricing
    console.log('\n💵 Verificando pricing...');
    const pricingRef = collection(db, 'influencers', influencerId, 'pricing');
    const pricingSnapshot = await getDocs(pricingRef);
    
    console.log(`- Documentos de pricing encontrados: ${pricingSnapshot.docs.length}`);
    pricingSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`  ${index + 1}. ID: ${doc.id}, Active: ${data.isActive}`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao debugar dados:', error);
  }
}

// IDs de exemplo para testar (substitua pelos IDs reais do seu projeto)
const testInfluencerIds = [
  'lxrMDbxTQGCpYvRrkCPZ',
  '7PnAxZLXq0ih5IcLz3nF'
];

async function runDebug() {
  for (const id of testInfluencerIds) {
    await debugInfluencerData(id);
    console.log('\n' + '='.repeat(50) + '\n');
  }
}

// Executar debug
runDebug().then(() => {
  console.log('✅ Debug concluído');
  process.exit(0);
}).catch(error => {
  console.error('❌ Erro no debug:', error);
  process.exit(1);
});
