import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';
import { SnapshotFilterService } from '@/lib/snapshot-filter-service';
import { cookies } from 'next/headers';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Função para extrair dados do usuário do token JWT
async function getUserFromToken(token: string): Promise<{ userId: string; role: string } | null> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
    return {
      userId: payload.userId,
      role: payload.role
    };
  } catch {
    return null;
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Proposal ID is required'
        },
        { status: 400 }
      );
    }

    // Verificar autenticação e role
    const cookieStore = await cookies();
    const token = cookieStore.get('brand-session')?.value;
    
    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid token'
        },
        { status: 401 }
      );
    }

    // Para usuários com role 'user', verificar se tem acesso à proposta
    if (user.role === 'user') {
      const hasAccess = await ProposalService.canUserAccessProposal(id, user.userId);
      
      if (!hasAccess) {
        return NextResponse.json(
          {
            success: false,
            error: 'Access denied - Proposal not shared with you'
          },
          { status: 403 }
        );
      }
    }

    const proposal = await ProposalService.getProposalById(id);
    
    if (!proposal) {
      return NextResponse.json(
        {
          success: false,
          error: 'Proposal not found'
        },
        { status: 404 }
      );
    }
    
    // 🔒 FILTRAR DADOS BASEADO NO ROLE DO USUÁRIO
    if (user.role === 'user') {
      console.log('🔒 Filtrando dados para usuário com role USER');
      
      // 🎯 NOVA LÓGICA: Verificar se é colaborador da proposta
      let collaboratorRole: 'owner' | 'editor' | 'viewer' | 'none' = 'none';
      let isCollaborator = false;
      
      // Verificar se é proprietário
      if (proposal.criadoPor === user.userId || proposal.brandId === user.userId) {
        collaboratorRole = 'owner';
        isCollaborator = true;
        console.log('👑 [FILTER] Usuário é proprietário da proposta');
      } 
      // Verificar se é colaborador
      else if ('collaborators' in proposal && Array.isArray((proposal as any).collaborators)) {
        const collaborator = (proposal as any).collaborators.find(
          (collab: any) => collab.userId === user.userId && collab.status === 'active'
        );
        if (collaborator) {
          collaboratorRole = collaborator.role;
          isCollaborator = true;
          console.log(`🤝 [FILTER] Usuário é colaborador ${collaboratorRole}`);
        }
      }
      
      // Aplicar filtro baseado no papel como colaborador
      const filteredProposal = SnapshotFilterService.filterSnapshotByRole(
        proposal,
        user.role,
        {
          collaboratorRole,
          isCollaborator
        }
      );
      
      console.log('✅ Dados filtrados para role USER:', {
        userRole: user.role,
        collaboratorRole,
        isCollaborator,
        filteredFields: SnapshotFilterService.getFilteredFieldsForRole(user.role)
      });
      
      return NextResponse.json({
        success: true,
        data: filteredProposal,
        metadata: {
          accessLevel: collaboratorRole === 'owner' ? 'complete' : 'basic',
          userRole: user.role,
          collaboratorRole,
          isCollaborator,
          filteredFields: SnapshotFilterService.getFilteredFieldsForRole(user.role)
        }
      });
    }
    
    // Para admins/managers, retornar dados completos
    return NextResponse.json({
      success: true,
      data: proposal,
      metadata: {
        accessLevel: 'complete',
        userRole: user.role,
        filteredFields: []
      }
    });
  } catch (error) {
    console.error('Error fetching proposal:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch proposal'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Proposal ID is required'
        },
        { status: 400 }
      );
    }

    // Verificar autenticação e role
    const cookieStore = await cookies();
    const token = cookieStore.get('brand-session')?.value;
    
    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid token'
        },
        { status: 401 }
      );
    }

    // Para usuários com role 'user', negar acesso a modificações
    if (user.role === 'user') {
      return NextResponse.json(
        {
          success: false,
          error: 'Access denied - Users cannot modify proposals'
        },
        { status: 403 }
      );
    }

    await ProposalService.updateProposal(id, body);
    
    return NextResponse.json({
      success: true,
      message: 'Proposal updated successfully'
    });
  } catch (error) {
    console.error('Error updating proposal:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update proposal'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Proposal ID is required'
        },
        { status: 400 }
      );
    }

    // Verificar autenticação e role
    const cookieStore = await cookies();
    const token = cookieStore.get('brand-session')?.value;
    
    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid token'
        },
        { status: 401 }
      );
    }

    // Para usuários com role 'user', negar acesso a exclusões
    if (user.role === 'user') {
      return NextResponse.json(
        {
          success: false,
          error: 'Access denied - Users cannot delete proposals'
        },
        { status: 403 }
      );
    }

    await ProposalService.deleteProposal(id);
    
    return NextResponse.json({
      success: true,
      message: 'Proposal deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting proposal:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete proposal'
      },
      { status: 500 }
    );
  }
}