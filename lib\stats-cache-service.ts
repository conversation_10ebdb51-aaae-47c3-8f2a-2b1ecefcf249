// Importações necessárias
import { db, FieldValue } from '@/lib/firebase-admin';

interface UserStats {
  totalInfluencers: number;
  totalViews: number;
  totalFollowers: number;
  totalBrands: number;
  totalCampaigns: number;
  lastUpdated: Date;
}

interface StatsCacheMap {
  [userId: string]: UserStats;
}

class StatsCache {
  private cache: StatsCacheMap = {};
  private isCalculating: { [userId: string]: boolean } = {}; // 🔥 OTIMIZAÇÃO: Controle de cálculos simultâneos
  private readonly CACHE_TTL = 10 * 60 * 1000; // 🔥 OTIMIZAÇÃO: Aumentar TTL para 10 minutos
  
  /**
   * Busca estatísticas do cache ou calcula se necessário
   */
  async getStats(userId: string): Promise<UserStats> {
    // 🔥 VALIDAÇÃO: Verificar se userId é válido
    if (!userId || userId === 'undefined' || userId === 'null') {
      console.warn('⚠️ [StatsCache] UserId inválido:', userId);
      return this.getDefaultStats();
    }

    const cached = this.cache[userId];
    
    // Se tem cache válido, retorna
    if (cached && this.isCacheValid(cached)) {
      console.log('✅ [StatsCache] Usando cache válido para:', userId);
      return cached;
    }
    
    // 🔥 OTIMIZAÇÃO: Evitar cálculos simultâneos para o mesmo usuário
    if (this.isCalculating[userId]) {
      console.log('⏳ [StatsCache] Aguardando cálculo em andamento para:', userId);
      return this.getDefaultStats();
    }
    
    // Senão, calcula e cacheia
    console.log('🔄 [StatsCache] Calculando estatísticas para:', userId);
    this.isCalculating[userId] = true;
    
    try {
    const stats = await this.calculateStats(userId);
    this.cache[userId] = stats;
    return stats;
    } finally {
      this.isCalculating[userId] = false;
    }
  }
  
  /**
   * Calcula estatísticas otimizadas usando contadores denormalizados
   */
  private async calculateStats(userId: string): Promise<UserStats> {
    try {
      console.log('📊 [StatsCache] Iniciando cálculo de estatísticas para:', userId);
      
      // Busca contador denormalizado primeiro (mais rápido)
      const userStatsDoc = await db.collection('user_stats').doc(userId).get();
      
      if (userStatsDoc.exists) {
        const data = userStatsDoc.data();
        console.log('✅ [StatsCache] Contador denormalizado encontrado:', data);
        
        // 🔥 VALIDAÇÃO: Verificar se os dados são válidos
        const stats = {
          totalInfluencers: this.validateNumber(data?.totalInfluencers),
          totalViews: this.validateNumber(data?.totalViews),
          totalFollowers: this.validateNumber(data?.totalFollowers),
          totalBrands: this.validateNumber(data?.totalBrands),
          totalCampaigns: this.validateNumber(data?.totalCampaigns),
          lastUpdated: new Date()
        };

        // Se os dados parecem válidos, retornar
        if (stats.totalInfluencers > 0 || stats.totalFollowers > 0) {
          return stats;
        }
        
        console.log('⚠️ [StatsCache] Contador denormalizado com dados zerados, recalculando...');
      }
      
      // Fallback: calcula diretamente (primeira vez ou dados inválidos)
      console.log('🔄 [StatsCache] Calculando a partir das coleções...');
      return await this.calculateStatsFromCollections(userId);
      
    } catch (error) {
      console.error('❌ [StatsCache] Erro ao calcular estatísticas:', error);
      return this.getDefaultStats();
    }
  }
  
  /**
   * Valida e corrige números vindos do banco
   */
  private validateNumber(value: any): number {
    if (typeof value === 'number' && !isNaN(value) && value >= 0) {
      return value;
    }
    if (typeof value === 'string' && !isNaN(Number(value))) {
      const num = Number(value);
      return num >= 0 ? num : 0;
    }
    return 0;
  }
  
  /**
   * Calcula estatísticas consultando diretamente as coleções do Firestore
   */
  private async calculateStatsFromCollections(userId: string): Promise<UserStats> {
    try {
      console.log(`📊 [StatsCache] Calculando estatísticas para usuário: ${userId}`);
      
      const [influencersQuery, brandsQuery] = await Promise.all([
        db.collection('influencers')
          .where('userId', '==', userId)
          .get(),
        db.collection('brands')
          .where('userId', '==', userId)
          .get()
      ]);

      let totalFollowers = 0;
      let totalViews = 0;

      console.log(`📊 [StatsCache] Encontrados ${influencersQuery.size} influencers e ${brandsQuery.size} marcas`);

      // 🔥 CORREÇÃO CRÍTICA: Usar diretamente totalFollowers e totalViews dos documentos
      // Em vez de recalcular por plataforma, que causa inconsistências
      influencersQuery.docs.forEach((doc, index) => {
        const data = doc.data();
        
        // ✅ CORRETO: Usar diretamente o totalFollowers que já está calculado corretamente
        const influencerFollowers = this.validateNumber(data.totalFollowers);
        totalFollowers += influencerFollowers;
        
        // ✅ CORRETO: Usar diretamente o totalViews que já está calculado corretamente  
        const influencerViews = this.validateNumber(data.totalViews);
        totalViews += influencerViews;
        
        // Debug para primeiros 3 influencers
        if (index < 3) {
          console.log(`📊 [StatsCache] Influencer ${index + 1}:`, {
            name: data.name || 'Sem nome',
            followers: influencerFollowers,
            views: influencerViews,
            // Debug dos campos originais para verificação
            instagramFollowers: data.instagramFollowers || 0,
            tiktokFollowers: data.tiktokFollowers || 0,
            youtubeFollowers: data.youtubeFollowers || 0
          });
        }
      });
      
      const stats: UserStats = {
        totalInfluencers: influencersQuery.size,
        totalViews: totalViews,
        totalFollowers: totalFollowers,
        totalBrands: brandsQuery.size,
        totalCampaigns: 0, // TODO: implementar campanhas
        lastUpdated: new Date()
      };

      console.log(`📊 [StatsCache] Estatísticas calculadas:`, {
        totalInfluencers: stats.totalInfluencers,
        totalFollowers: stats.totalFollowers,
        totalViews: stats.totalViews,
        totalBrands: stats.totalBrands
      });

      // Salvar no contador para cache futuro
      await this.saveStatsCounter(userId, stats);
      
      return stats;
      
    } catch (error) {
      console.error('❌ [StatsCache] Erro ao calcular estatísticas:', error);
      
      // Retornar estatísticas vazias em caso de erro
      return this.getDefaultStats();
    }
  }

  /**
   * Parse inteligente de contadores de seguidores (lida com strings como "1.2M", "500K", etc.)
   */
  private parseFollowersCount(value: any): number {
    if (!value) return 0;
    
    if (typeof value === 'number') {
      return value;
    }
    
    if (typeof value === 'string') {
      // Remove caracteres não numéricos exceto ponto, vírgula, K, M, B
      const cleaned = value.toString().toUpperCase().replace(/[^\d.,KMB]/g, '');
      
      if (cleaned.includes('B')) {
        return Math.round(parseFloat(cleaned.replace('B', '')) * 1000000000);
      }
      if (cleaned.includes('M')) {
        return Math.round(parseFloat(cleaned.replace('M', '')) * 1000000);
      }
      if (cleaned.includes('K')) {
        return Math.round(parseFloat(cleaned.replace('K', '')) * 1000);
      }
      
      // Tentar converter diretamente
      const num = parseFloat(cleaned.replace(',', '.'));
      return isNaN(num) ? 0 : Math.round(num);
    }
    
    return 0;
  }
  
  /**
   * Salva contador denormalizado no Firestore
   */
  private async saveStatsCounter(userId: string, stats: UserStats): Promise<void> {
    try {
      console.log('💾 [StatsCache] Salvando contador denormalizado...');
      await db.collection('user_stats').doc(userId).set({
        totalInfluencers: stats.totalInfluencers,
        totalViews: stats.totalViews,
        totalFollowers: stats.totalFollowers,
        totalBrands: stats.totalBrands,
        totalCampaigns: stats.totalCampaigns,
        lastUpdated: new Date()
      });
      console.log('✅ [StatsCache] Contador salvo com sucesso');
    } catch (error) {
      console.error('❌ [StatsCache] Erro ao salvar contador de estatísticas:', error);
    }
  }
  
  /**
   * Invalida cache para um usuário específico
   */
  invalidateCache(userId: string): void {
    delete this.cache[userId];
    console.log('🗑️ [StatsCache] Cache invalidado para:', userId);
  }

  /**
   * Atualiza contadores quando há mudanças (para manter denormalização atualizada)
   */
  async updateStatsCounter(userId: string, operation: 'add' | 'remove', type: 'influencer' | 'brand', data?: any): Promise<void> {
    try {
      console.log(`🔄 [StatsCache] Atualizando contador: ${operation} ${type} para ${userId}`);
      
      // Invalidar cache primeiro
      this.invalidateCache(userId);
      
      // Buscar contador atual
      const userStatsRef = db.collection('user_stats').doc(userId);
      const userStatsDoc = await userStatsRef.get();
      
      let currentStats: any = {};
      if (userStatsDoc.exists) {
        currentStats = userStatsDoc.data() || {};
      }
      
      // Preparar atualização baseada no tipo
      const updateData: any = {};
      
      if (type === 'influencer') {
        const increment = operation === 'add' ? 1 : -1;
        updateData.totalInfluencers = (currentStats.totalInfluencers || 0) + increment;
        
        // Se temos dados do influencer, atualizar followers/views também
        if (data && operation === 'add') {
          const addFollowers = this.validateNumber(data.totalFollowers);
          const addViews = this.validateNumber(data.totalViews);
          
          updateData.totalFollowers = (currentStats.totalFollowers || 0) + addFollowers;
          updateData.totalViews = (currentStats.totalViews || 0) + addViews;
        }
      } else if (type === 'brand') {
        const increment = operation === 'add' ? 1 : -1;
        updateData.totalBrands = (currentStats.totalBrands || 0) + increment;
      }
      
      updateData.lastUpdated = new Date();
      
      // Salvar no Firestore
      await userStatsRef.set(updateData, { merge: true });
      
      console.log('✅ [StatsCache] Contador atualizado:', updateData);
      
    } catch (error) {
      console.error('❌ [StatsCache] Erro ao atualizar contador:', error);
      
      // Em caso de erro, forçar recálculo completo
      console.log('🔄 [StatsCache] Forçando recálculo completo devido ao erro...');
      await this.forceRecalculate(userId);
    }
  }

  /**
   * Verifica se o cache ainda é válido
   */
  private isCacheValid(stats: UserStats): boolean {
    const now = Date.now();
    const cacheTime = stats.lastUpdated.getTime();
    const isValid = (now - cacheTime) < this.CACHE_TTL;
    
    return isValid;
  }

  /**
   * Retorna estatísticas padrão zeradas
   */
  private getDefaultStats(): UserStats {
    return {
      totalInfluencers: 0,
      totalViews: 0,
      totalFollowers: 0,
      totalBrands: 0,
      totalCampaigns: 0,
      lastUpdated: new Date()
    };
  }

  /**
   * Força recálculo completo das estatísticas (ignora cache)
   */
  async forceRecalculate(userId: string): Promise<UserStats> {
    try {
      console.log('🔄 [StatsCache] Forçando recálculo completo para:', userId);
      
      // Limpar cache
      this.invalidateCache(userId);
      
      // Calcular diretamente das coleções
      const stats = await this.calculateStatsFromCollections(userId);
      
      // Cachear resultado
      this.cache[userId] = stats;
      
      console.log('✅ [StatsCache] Recálculo forçado concluído:', stats);
      return stats;
      
    } catch (error) {
      console.error('❌ [StatsCache] Erro no recálculo forçado:', error);
      return this.getDefaultStats();
    }
  }

  /**
   * Remove contador denormalizado e força recálculo
   */
  async resetUserStats(userId: string): Promise<UserStats> {
    try {
      console.log('🔄 [StatsCache] Resetando estatísticas do usuário:', userId);
      
      // Remover contador denormalizado
      await db.collection('user_stats').doc(userId).delete();
      
      // Limpar cache
      this.invalidateCache(userId);
      
      // Recalcular
      return await this.forceRecalculate(userId);
      
    } catch (error) {
      console.error('❌ [StatsCache] Erro ao resetar estatísticas:', error);
      return this.getDefaultStats();
    }
  }
}

// Singleton instance
export const statsCache = new StatsCache();

// Função utilitária para uso direto em APIs
export async function getUserStats(userId: string): Promise<UserStats> {
  return statsCache.getStats(userId);
}

// Função para invalidar cache via API
export function invalidateUserStatsCache(userId: string): void {
  statsCache.invalidateCache(userId);
}

// Função para forçar recálculo via API
export async function forceRecalculateUserStats(userId: string): Promise<UserStats> {
  return statsCache.forceRecalculate(userId);
} 

