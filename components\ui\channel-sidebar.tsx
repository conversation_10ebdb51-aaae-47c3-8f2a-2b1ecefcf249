"use client";

import * as React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ChevronDown,
  Hash,
  Plus,
  Search,
  User,
  Users,
  Settings,
  Bell,
  Briefcase,
  Filter,
  Star,
  Zap
} from "lucide-react";

interface ChannelProps {
  name: string;
  href: string;
  isActive?: boolean;
  notifications?: number;
}

function Channel({ name, href, isActive, notifications }: ChannelProps) {
  return (
    <Link
      href={href}
      className={cn(
        "flex items-center px-2 py-1.5 rounded-md group relative",
        isActive ? "bg-white/10 text-white" : "text-gray-400 hover:text-white hover:bg-white/5"
      )}
    >
      <Hash className="h-4 w-4 mr-2 shrink-0" />
      <span className="flex-1 truncate">{name}</span>
      {notifications && (
        <Badge variant="secondary" className="ml-auto bg-blue-600 text-white">
          {notifications}
        </Badge>
      )}
    </Link>
  );
}

interface DirectMessageProps {
  name: string;
  href: string;
  avatar: string;
  status?: "online" | "away" | "offline";
  isActive?: boolean;
  notifications?: number;
}

function DirectMessage({ name, href, avatar, status = "offline", isActive, notifications }: DirectMessageProps) {
  return (
    <Link
      href={href}
      className={cn(
        "flex items-center px-2 py-1.5 rounded-md group relative",
        isActive ? "bg-white/10 text-white" : "text-gray-400 hover:text-white hover:bg-white/5"
      )}
    >
      <div className="relative mr-2">
        <div className="h-7 w-7 rounded-full bg-gray-800 flex items-center justify-center overflow-hidden">
          {avatar ? (
            <img src={avatar} alt={name} className="h-full w-full object-cover" />
          ) : (
            <span className="font-medium text-sm">{name[0]}</span>
          )}
        </div>
        <span
          className={cn(
            "absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-gray-900",
            status === "online" ? "bg-green-500" : status === "away" ? "bg-yellow-500" : "bg-gray-500"
          )}
        />
      </div>
      <span className="flex-1 truncate">{name}</span>
      {notifications && (
        <Badge variant="secondary" className="ml-auto bg-blue-600 text-white">
          {notifications}
        </Badge>
      )}
    </Link>
  );
}

interface CategoryProps {
  name: string;
  children: React.ReactNode;
}

function Category({ name, children }: CategoryProps) {
  const [isExpanded, setIsExpanded] = React.useState(true);

  return (
    <div className="mb-3">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full px-1 py-1 text-xs font-medium text-gray-400 hover:text-white"
      >
        <div className="flex items-center">
          <ChevronDown
            className={cn("h-3.5 w-3.5 mr-1 transition-transform", !isExpanded && "-rotate-90")}
          />
          <span className="uppercase tracking-wider">{name}</span>
        </div>
        <Plus className="h-3.5 w-3.5 opacity-0 group-hover:opacity-100" />
      </button>
      <div className={cn("mt-1 space-y-0.5", !isExpanded && "hidden")}>
        {children}
      </div>
    </div>
  );
}

export function ChannelSidebar() {
  const pathname = React.useState("");
  
  if (typeof window !== "undefined") {
    React.useEffect(() => {
      pathname[1](window.location.pathname);
    }, []);
  }

  return (
          <div className="fixed inset-y-0 left-16 z-10 w-60 bg-[#080210] border-r border-white/10 hidden md:flex flex-col">
      <div className="p-3 border-b border-white/10">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-white">Agência Deu Match</h2>
          <Button variant="ghost" size="icon" className="h-7 w-7 rounded-md hover:bg-white/10">
            <Bell className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="p-2">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Pesquisar"
            className="w-full bg-black/60 pl-9 h-9 border-0 focus-visible:ring-1 focus-visible:ring-white/20"
          />
        </div>
      </div>

      <ScrollArea className="flex-1 px-2">
        <div className="py-2">
          <Category name="Favoritos">
            <Channel name="Pelican room" href="#" isActive={true} />
          </Category>

          <Category name="Mensagens Diretas">
            <DirectMessage
              name="Jeremy Firow"
              href="#"
              avatar=""
              status="online"
            />
            <DirectMessage
              name="Mariuz Jaders"
              href="#"
              avatar=""
              status="online"
              notifications={1}
            />
            <DirectMessage
              name="Emil Anders"
              href="#"
              avatar=""
              status="away"
            />
            <DirectMessage
              name="Markus Gavrilov"
              href="#"
              avatar=""
              status="offline"
            />
          </Category>

          <Category name="Categorias">
            <Channel name="Crypto" href="#" />
            <Channel name="Futures" href="#" />
            <Channel name="Finance" href="#" notifications={8} />
            <Channel name="Stocktalk Germany" href="#" />
          </Category>

          <Category name="Plataformas">
            <Channel name="Instagram" href="#" />
            <Channel name="YouTube" href="#" />
            <Channel name="TikTok" href="#" />
            <Channel name="Twitter" href="#" />
          </Category>

                  <Category name="Influenciadores">
          <Channel name="Cadastro" href="/influencers/cadastro" isActive={pathname[0] === "/influencers/cadastro"} />
          <Channel name="Gerenciamento" href="/influencers/lista" isActive={pathname[0] === "/influencers/lista"} />
          <Channel name="Top Influencers" href="/influencers/top" isActive={pathname[0] === "/influencers/top"} />
          </Category>
          
          <Category name="Marcas">
                    <Channel name="Cadastro" href="/campanhas/cadastro" isActive={pathname[0] === "/campanhas/cadastro"} />
        <Channel name="Gerenciamento" href="/campanhas/lista" isActive={pathname[0] === "/campanhas/lista"} />
        <Channel name="Parcerias" href="/campanhas/parcerias" isActive={pathname[0] === "/campanhas/parcerias"} />
          </Category>
        </div>
      </ScrollArea>

      <div className="p-3 border-t border-white/10 bg-black/20">
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-full bg-white/10 flex items-center justify-center mr-2">
            <User className="h-4 w-4" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-white">Admin</p>
            <p className="text-xs text-gray-400">Online</p>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-md hover:bg-white/10">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}


