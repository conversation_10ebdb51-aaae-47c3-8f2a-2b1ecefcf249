'use client'

import React, { useState, useRef, useCallback } from 'react'
import Cropper from 'react-easy-crop'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { Loader2, Crop as CropIcon, RotateCw, Download, X } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { compressImage } from '@/lib/image-compression'

interface AvatarCropModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCropComplete: (croppedFile: File) => void
  aspectRatio?: number
  circularCrop?: boolean
  title?: string
}

interface Area {
  x: number
  y: number
  width: number
  height: number
}

interface CropArea extends Area {}

// Função para criar canvas com a imagem recortada
const createCroppedImage = async (
  imageSrc: string,
  pixelCrop: Area,
  rotation = 0,
  fileName = 'avatar.jpg'
): Promise<File> => {
  const image = new Image()
  image.crossOrigin = 'anonymous'
  
  return new Promise((resolve, reject) => {
    image.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        reject(new Error('Canvas context não disponível'))
        return
      }

      const maxSize = Math.max(image.width, image.height)
      const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2))

      // Define tamanho do canvas
      canvas.width = safeArea
      canvas.height = safeArea

      // Translada canvas para o centro
      ctx.translate(safeArea / 2, safeArea / 2)
      ctx.rotate((rotation * Math.PI) / 180)
      ctx.translate(-safeArea / 2, -safeArea / 2)

      // Desenha imagem rotacionada
      ctx.drawImage(
        image,
        safeArea / 2 - image.width * 0.5,
        safeArea / 2 - image.height * 0.5
      )

      const data = ctx.getImageData(0, 0, safeArea, safeArea)

      // Define tamanho final do canvas baseado no crop
      canvas.width = pixelCrop.width
      canvas.height = pixelCrop.height

      // Limpa e desenha área recortada
      ctx.putImageData(
        data,
        Math.round(0 - safeArea / 2 + image.width * 0.5 - pixelCrop.x),
        Math.round(0 - safeArea / 2 + image.height * 0.5 - pixelCrop.y)
      )

      // Converte canvas para blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Falha ao gerar imagem recortada'))
            return
          }

          const file = new File([blob], fileName, {
            type: 'image/jpeg',
            lastModified: Date.now()
          })

          resolve(file)
        },
        'image/jpeg',
        0.92 // Qualidade
      )
    }

    image.onerror = () => reject(new Error('Falha ao carregar imagem'))
    image.src = imageSrc
  })
}

export function AvatarCropModal({
  open,
  onOpenChange,
  onCropComplete,
  aspectRatio = 1,
  circularCrop = true,
  title = 'Recortar Avatar'
}: AvatarCropModalProps) {
  const [imageSrc, setImageSrc] = useState<string>('')
  const [crop, setCrop] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null)
  const [processing, setProcessing] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Resetar estado ao abrir/fechar modal
  const handleOpenChange = useCallback((isOpen: boolean) => {
    if (!isOpen) {
      setImageSrc('')
      setCrop({ x: 0, y: 0 })
      setZoom(1)
      setRotation(0)
      setCroppedAreaPixels(null)
      setSelectedFile(null)
      setProcessing(false)
    }
    onOpenChange(isOpen)
  }, [onOpenChange])

  // Abrir seletor de arquivo automaticamente quando modal abre
  React.useEffect(() => {
    if (open && !imageSrc) {
      // Pequeno delay para garantir que o modal está totalmente renderizado
      const timer = setTimeout(() => {
        fileInputRef.current?.click()
      }, 100)
      
      return () => clearTimeout(timer)
    }
  }, [open, imageSrc])

  // Selecionar arquivo
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) {
      // Se não selecionou arquivo, fechar modal
      handleOpenChange(false)
      return
    }

    // Validações
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Erro",
        description: "Selecione apenas arquivos de imagem",
        variant: "destructive",
      })
      handleOpenChange(false)
      return
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB
      toast({
        title: "Erro", 
        description: "Arquivo muito grande. Máximo: 10MB",
        variant: "destructive",
      })
      handleOpenChange(false)
      return
    }

    setSelectedFile(file)

    // Criar URL para o cropper
    const reader = new FileReader()
    reader.onload = (event) => {
      if (event.target?.result) {
        setImageSrc(event.target.result as string)
      }
    }
    reader.readAsDataURL(file)
  }, [handleOpenChange])

  // Callback quando área de recorte é definida
  const onCropChange = useCallback((crop: { x: number; y: number }) => {
    setCrop(crop)
  }, [])

  const onCropAreaChange = useCallback((croppedArea: CropArea, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels)
  }, [])

  // Processar recorte
  const handleCropConfirm = useCallback(async () => {
    if (!croppedAreaPixels || !selectedFile || !imageSrc) {
      toast({
        title: "Erro",
        description: "Configure o recorte antes de confirmar",
        variant: "destructive",
      })
      return
    }

    try {
      setProcessing(true)

      // Criar arquivo recortado
      const croppedFile = await createCroppedImage(
        imageSrc,
        croppedAreaPixels,
        rotation,
        `avatar_${Date.now()}.jpg`
      )

      console.log('🎯 Arquivo recortado criado:', {
        originalSize: selectedFile.size,
        croppedSize: croppedFile.size,
        dimensions: `${croppedAreaPixels.width}x${croppedAreaPixels.height}`
      })

      // Comprimir se necessário (manter qualidade para avatares)
      let finalFile = croppedFile
      if (croppedFile.size > 2 * 1024 * 1024) { // Se > 2MB, comprimir
        finalFile = await compressImage(croppedFile, {
          maxWidth: 512,
          maxHeight: 512,
          quality: 0.9,
          format: 'jpeg'
        })
        console.log('🗜️ Arquivo comprimido:', finalFile.size)
      }

      // Chamar callback com arquivo final
      onCropComplete(finalFile)
      
      // Fechar modal
      handleOpenChange(false)

      toast({
        title: "Sucesso!",
        description: "Avatar recortado com sucesso",
      })

    } catch (error) {
      console.error('❌ Erro ao recortar imagem:', error)
      toast({
        title: "Erro",
        description: "Falha ao processar imagem. Tente novamente.",
        variant: "destructive",
      })
    } finally {
      setProcessing(false)
    }
  }, [croppedAreaPixels, selectedFile, imageSrc, rotation, onCropComplete, handleOpenChange])

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CropIcon className="h-5 w-5 text-[#ff0074]" />
            {title}
          </DialogTitle>
        </DialogHeader>

        {/* Input file escondido */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="space-y-6">
          {imageSrc ? (
            /* Interface de recorte */
            <div className="space-y-4">
              {/* Área de recorte principal */}
              <Card className="relative overflow-hidden" style={{ height: '400px' }}>
                <Cropper
                  image={imageSrc}
                  crop={crop}
                  zoom={zoom}
                  rotation={rotation}
                  aspect={aspectRatio}
                  onCropChange={onCropChange}
                  onCropComplete={onCropAreaChange}
                  onZoomChange={setZoom}
                  onRotationChange={setRotation}
                  showGrid={false}
                  cropShape={circularCrop ? 'round' : 'rect'}
                  style={{
                    containerStyle: {
                      width: '100%',
                      height: '100%',
                      backgroundColor: '#000'
                    },
                    cropAreaStyle: {
                      border: '2px solid #ff0074',
                      boxShadow: '0 0 0 9999em rgba(0, 0, 0, 0.5)'
                    }
                  }}
                />
              </Card>

              {/* Controles */}
              <Card className="p-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Controle de Zoom */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">Zoom</label>
                        <span className="text-sm text-muted-foreground">{Math.round(zoom * 100)}%</span>
                      </div>
                      <Slider
                        value={[zoom]}
                        onValueChange={(value) => setZoom(value[0])}
                        min={1}
                        max={3}
                        step={0.1}
                        className="w-full"
                      />
                    </div>

                    {/* Controle de Rotação */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">Rotação</label>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{rotation}°</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setRotation((prev) => prev + 90)}
                            className="h-8 w-8 p-0"
                          >
                            <RotateCw className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <Slider
                        value={[rotation]}
                        onValueChange={(value) => setRotation(value[0])}
                        min={-180}
                        max={180}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </Card>

              {/* Preview e Botões */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-4">
                  {/* Preview miniatura */}
                  <div className="text-center">
                    <p className="text-sm font-medium mb-2">Preview</p>
                    <div className={`w-16 h-16 border-2 border-[#ff0074]/20 overflow-hidden bg-gradient-to-br from-[#ff0074] to-[#9810fa] ${circularCrop ? 'rounded-full' : 'rounded-lg'}`}>
                      {/* Preview será atualizado via CSS */}
                    </div>
                    {croppedAreaPixels && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {Math.round(croppedAreaPixels.width)}x{Math.round(croppedAreaPixels.height)}px
                      </p>
                    )}
                  </div>
                </div>

                {/* Botões de ação */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleOpenChange(false)}
                    disabled={processing}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancelar
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={processing}
                  >
                    Trocar Imagem
                  </Button>
                  
                  <Button
                    onClick={handleCropConfirm}
                    disabled={!croppedAreaPixels || processing}
                    className="bg-[#ff0074] hover:bg-[#ff0074]/90"
                  >
                    {processing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      <>
                        <CropIcon className="h-4 w-4 mr-2" />
                        Confirmar Recorte
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            /* Loading state while file is being processed */
            <div className="flex items-center justify-center p-8">
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 mx-auto text-[#ff0074] animate-spin" />
                <p className="text-sm text-muted-foreground">Carregando imagem...</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
} 


