// Implementação básica de conexão com banco de dados (mock)
// Este arquivo é usado por rotas de API que exigem acesso ao banco de dados

import { getApps, initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';

// Configuração do Firebase
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'placeholder-api-key',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'placeholder-auth-domain',
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'placeholder-project-id',
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'placeholder-storage-bucket',
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || 'placeholder-messaging-sender-id',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || 'placeholder-app-id',
};

// Inicializa o Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
const db = getFirestore(app);

// Funções de utilidade para o banco de dados
export const getFilterOptions = async () => {
  // Mock para as opções de filtro
  return {
    categories: ['Instagram', 'TikTok', 'YouTube', 'Twitch'],
    industries: ['Moda', 'Beleza', 'Tecnologia', 'Comida', 'Viagem', 'Jogos'],
    followersRanges: ['1k-10k', '10k-50k', '50k-100k', '100k-500k', '500k+'],
    locations: ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Curitiba', 'Porto Alegre']
  };
};

export default db;

