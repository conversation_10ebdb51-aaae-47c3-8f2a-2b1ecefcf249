import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';

export type ProposalRole = 'a' | 'e' | 'v'; // admin, editor, viewer
export type ProposalAccess = Record<string, ProposalRole>;
export type UserMetadata = {
  p?: ProposalAccess; // proposals access
};

export interface ProposalAccessInfo {
  proposalId: string;
  role: ProposalRole;
  permissions: {
    canView: boolean;
    canEdit: boolean;
    canManageAccess: boolean;
    canDelete: boolean;
  };
}

export const useProposalAccess = () => {
  const { user, isLoaded } = useUser();
  const [accessibleProposals, setAccessibleProposals] = useState<ProposalAccessInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Função para obter permissões baseadas no role
  const getPermissions = (role: ProposalRole) => ({
    canView: true, // Todos os roles podem visualizar
    canEdit: role === 'a' || role === 'e', // Admin e Editor podem editar
    canManageAccess: role === 'a', // Apenas Admin pode gerenciar acesso
    canDelete: role === 'a' // Apenas Admin pode deletar
  });

  // Função para verificar se usuário tem acesso a uma proposta específica
  const hasAccess = (proposalId: string): ProposalAccessInfo | null => {
    if (!user?.unsafeMetadata) return null;
    
    const metadata = user.unsafeMetadata as UserMetadata;
    const role = metadata.p?.[proposalId];
    
    if (!role) return null;
    
    return {
      proposalId,
      role,
      permissions: getPermissions(role)
    };
  };

  // Função para adicionar acesso a uma proposta
  const addProposalAccess = async (proposalId: string, role: ProposalRole) => {
    if (!user) return false;
    
    try {
      const currentMetadata = (user.unsafeMetadata as UserMetadata) || {};
      const newMetadata: UserMetadata = {
        ...currentMetadata,
        p: {
          ...currentMetadata.p,
          [proposalId]: role
        }
      };
      
      await user.update({
        unsafeMetadata: newMetadata
      });
      
      // Atualizar estado local
      const newAccess: ProposalAccessInfo = {
        proposalId,
        role,
        permissions: getPermissions(role)
      };
      
      setAccessibleProposals(prev => {
        const filtered = prev.filter(item => item.proposalId !== proposalId);
        return [...filtered, newAccess];
      });
      
      return true;
    } catch (error) {
      return false;
    }
  };

  // Função para remover acesso a uma proposta
  const removeProposalAccess = async (proposalId: string) => {
    if (!user) return false;
    
    try {
      const currentMetadata = (user.unsafeMetadata as UserMetadata) || {};
      const newProposals = { ...currentMetadata.p };
      delete newProposals[proposalId];
      
      const newMetadata: UserMetadata = {
        ...currentMetadata,
        p: newProposals
      };
      
      await user.update({
        unsafeMetadata: newMetadata
      });
      
      // Atualizar estado local
      setAccessibleProposals(prev => 
        prev.filter(item => item.proposalId !== proposalId)
      );
      
      return true;
    } catch (error) {
      return false;
    }
  };

  // Função para atualizar role de uma proposta
  const updateProposalRole = async (proposalId: string, newRole: ProposalRole) => {
    if (!user) return false;
    
    try {
      const currentMetadata = (user.unsafeMetadata as UserMetadata) || {};
      const newMetadata: UserMetadata = {
        ...currentMetadata,
        p: {
          ...currentMetadata.p,
          [proposalId]: newRole
        }
      };
      
      await user.update({
        unsafeMetadata: newMetadata
      });
      
      // Atualizar estado local
      setAccessibleProposals(prev => 
        prev.map(item => 
          item.proposalId === proposalId 
            ? { ...item, role: newRole, permissions: getPermissions(newRole) }
            : item
        )
      );
      
      return true;
    } catch (error) {
      return false;
    }
  };

  // Função para obter todas as propostas acessíveis
  const getAccessibleProposalIds = (): string[] => {
    return accessibleProposals.map(item => item.proposalId);
  };

  // Carregar propostas do metadata quando o usuário for carregado
  useEffect(() => {
    if (!isLoaded) return;
    
    if (user?.unsafeMetadata) {
      const metadata = user.unsafeMetadata as UserMetadata;
      const proposals = metadata.p || {};
      
      const accessInfo: ProposalAccessInfo[] = Object.entries(proposals).map(([proposalId, role]) => ({
        proposalId,
        role,
        permissions: getPermissions(role)
      }));
      
      setAccessibleProposals(accessInfo);
    } else {
      setAccessibleProposals([]);
    }
    
    setIsLoading(false);
  }, [user, isLoaded]);

  return {
    // Estados
    accessibleProposals,
    isLoading,
    
    // Funções de verificação
    hasAccess,
    getAccessibleProposalIds,
    
    // Funções de modificação
    addProposalAccess,
    removeProposalAccess,
    updateProposalRole,
    
    // Dados do usuário
    user,
    isLoaded
  };
}; 