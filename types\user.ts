export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'influencer' | 'user' | 'brand_owner' | 'brand_manager'; // ✅ Expandir tipos de role
  password?: string; // hash da senha - não deve ser exposto no frontend
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  lastLoginAt?: Date;
  profileImage?: string;
}

export interface UserSession {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  createdAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface UserPermission {
  id: string;
  userId: string; // FK para User
  permission: string; // 'view_campaigns', 'edit_proposals', etc.
  resourceType?: string; // 'campaign', 'proposal', 'influencer'
  resourceId?: string; // ID específico do recurso (opcional)
  grantedBy: string; // ID do usuário que concedeu
  createdAt: Date;
  expiresAt?: Date; // Permissão temporária (opcional)
}

// Enum para permissões do sistema
export const PERMISSIONS = {
  // Permissões gerais
  VIEW_DASHBOARD: 'view_dashboard',
  VIEW_ANALYTICS: 'view_analytics',
  
  // Permissões de campanhas
  VIEW_CAMPAIGNS: 'view_campaigns',
  CREATE_CAMPAIGNS: 'create_campaigns',
  EDIT_CAMPAIGNS: 'edit_campaigns',
  DELETE_CAMPAIGNS: 'delete_campaigns',
  
  // Permissões de propostas
  VIEW_PROPOSALS: 'view_proposals',
  CREATE_PROPOSALS: 'create_proposals',
  EDIT_PROPOSALS: 'edit_proposals',
  DELETE_PROPOSALS: 'delete_proposals',
  APPROVE_PROPOSALS: 'approve_proposals',
  
  // Permissões de influenciadores
  VIEW_INFLUENCERS: 'view_influencers',
  CREATE_INFLUENCERS: 'create_influencers',
  EDIT_INFLUENCERS: 'edit_influencers',
  DELETE_INFLUENCERS: 'delete_influencers',
  
  // Permissões de marcas
  VIEW_BRANDS: 'view_brands',
  CREATE_BRANDS: 'create_brands',
  EDIT_BRANDS: 'edit_brands',
  DELETE_BRANDS: 'delete_brands',
  
  // Permissões administrativas
  MANAGE_USERS: 'manage_users',
  MANAGE_PERMISSIONS: 'manage_permissions',
  SYSTEM_ADMIN: 'system_admin',
  
  // Permissões financeiras
  VIEW_FINANCIALS: 'view_financials',
  EDIT_FINANCIALS: 'edit_financials',
  APPROVE_PAYMENTS: 'approve_payments'
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];

// Interface para criação de novo usuário
export interface CreateUserData {
  email: string;
  name: string;
  role: 'admin' | 'influencer' | 'user' | 'brand_owner' | 'brand_manager';
  password: string;
  profileImage?: string;
}

// Interface para atualização de usuário
export interface UpdateUserData {
  email?: string;
  name?: string;
  role?: 'admin' | 'influencer' | 'user' | 'brand_owner' | 'brand_manager';
  password?: string;
  profileImage?: string;
  isActive?: boolean;
}

// Roles predefinidos com suas permissões
export const ROLE_PERMISSIONS = {
  admin: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.VIEW_CAMPAIGNS,
    PERMISSIONS.CREATE_CAMPAIGNS,
    PERMISSIONS.EDIT_CAMPAIGNS,
    PERMISSIONS.DELETE_CAMPAIGNS,
    PERMISSIONS.VIEW_PROPOSALS,
    PERMISSIONS.CREATE_PROPOSALS,
    PERMISSIONS.EDIT_PROPOSALS,
    PERMISSIONS.DELETE_PROPOSALS,
    PERMISSIONS.APPROVE_PROPOSALS,
    PERMISSIONS.VIEW_INFLUENCERS,
    PERMISSIONS.CREATE_INFLUENCERS,
    PERMISSIONS.EDIT_INFLUENCERS,
    PERMISSIONS.DELETE_INFLUENCERS,
    PERMISSIONS.VIEW_BRANDS,
    PERMISSIONS.CREATE_BRANDS,
    PERMISSIONS.EDIT_BRANDS,
    PERMISSIONS.DELETE_BRANDS,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_PERMISSIONS,
    PERMISSIONS.SYSTEM_ADMIN,
    PERMISSIONS.VIEW_FINANCIALS,
    PERMISSIONS.EDIT_FINANCIALS,
    PERMISSIONS.APPROVE_PAYMENTS
  ],

  influencer: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PROPOSALS,
    PERMISSIONS.VIEW_CAMPAIGNS
  ]
} as const;

