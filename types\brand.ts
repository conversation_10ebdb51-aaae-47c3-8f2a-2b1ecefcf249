// Modelo de dados para marcas

import { User } from './user';
import { BaseDocument, CreateData, UpdateData, DocumentStatus } from './base';
import type { DocumentStatusValidation } from '@/lib/validation/base';

export interface Brand extends BaseDocument {
  name: string;           // Nome da marca
  logo?: string;          // URL do logo
  logoBackgroundColor?: string; // Cor de fundo da logo (hex, rgb, etc.)
  industry?: string;      // Segmento (Alimentação, Tecnologia, etc.)
  website?: string;       // Site da marca
  contactEmail?: string;  // Email de contato
  contactPhone?: string;  // Telefone de contato
  contactName?: string;   // Nome do contato
  budget?: number;        // Orçamento típico para campanhas
  notes?: string;         // Observações
  status: DocumentStatus; // Status da marca (ACTIVE, INACTIVE, etc.)
  
  // Metadados adicionais
  description?: string;   // Descrição da marca
  socialMedia?: {         // Redes sociais da marca
    instagram?: string;
    facebook?: string;
    twitter?: string;
    linkedin?: string;
  };
  
  // Dados relacionados (populados via join)
  user?: User;            // Dados do usuário proprietário
}

// Interface para criação de nova marca (userId será adicionado automaticamente)
export interface CreateBrandData {
  name: string;
  logo?: string;
  logoBackgroundColor?: string;
  industry?: string;
  website?: string;
  contactEmail?: string;
  contactPhone?: string;
  contactName?: string;
  budget?: number;
  notes?: string;
  description?: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    linkedin?: string;
  };
  status?: DocumentStatusValidation;
}

// Interface para atualização de marca usando tipo utilitário
export type UpdateBrandData = UpdateData<Brand>;

// Interface para brand com validação de ownership
export interface BrandWithOwnership extends Brand {
  canEdit: boolean;
  canDelete: boolean;
  canShare: boolean;
}

// Interface para dados completos da marca com usuário
export interface BrandWithUser extends Brand {
  user: User;
}


