{"data": {"influencers": {"totalCount": 2, "hasNextPage": false, "hasPreviousPage": false, "nodes": [{"id": "lxrMDbxTQGCpYvRrkCPZ", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+55", "whatsapp": "+55", "country": "Brasil", "state": "CE", "city": "<PERSON><PERSON><PERSON>", "location": "Barbalha/CE", "age": 24, "gender": "male", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752352966267_1752352965497.jpg", "backgroundImage": null, "gradient": null, "category": "Lifestyle", "categories": [], "totalFollowers": 132000, "totalViews": 5334444, "engagementRate": 34, "rating": 4, "isVerified": false, "isAvailable": true, "status": "active", "instagramUsername": "aquiehparmera", "instagramFollowers": 132000, "instagramEngagementRate": 34, "instagramAvgViews": 5334444, "instagramStoriesViews": 56666, "instagramReelsViews": 87777, "tiktokUsername": "", "tiktokFollowers": 0, "tiktokEngagementRate": 0, "tiktokAvgViews": 0, "tiktokVideoViews": 0, "youtubeUsername": "", "youtubeFollowers": 0, "youtubeSubscribers": 0, "youtubeEngagementRate": 0, "youtubeAvgViews": 0, "youtubeShortsViews": 0, "youtubeLongFormViews": 0, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchAvgViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiAvgViews": 0, "promotesTraders": false, "responsibleName": "", "agencyName": "", "mainNetwork": "instagram", "mainPlatform": null, "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-15T00:04:43.065Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "qhgZgR8C6g28dlvFGXQE", "services": {"instagram": {"story": {"price": 655.55, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 755.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": null, "youtube": null, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-14T23:53:33.333Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "GZ1bVxlJHRwTaUyi9CiY", "platform": "instagram", "audienceGender": {"male": 85.5, "female": 14.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 17.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo", "percentage": 2, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.6, "__typename": "AudienceCity"}, {"city": "<PERSON>", "percentage": 1.4, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1.3, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-12T20:42:46.499Z", "updatedAt": "2025-07-14T23:53:32.926Z", "__typename": "Influencer"}, {"id": "7PnAxZLXq0ih5IcLz3nF", "userId": "user_2zs61BrZj1XaS2nDCq1PiPDygC9", "name": "Gigante Glorioso", "email": "<EMAIL>", "phone": "+55888888888", "whatsapp": "+55888888888", "country": "Brasil", "state": "CE", "city": "Jardim", "location": "Jardim/CE", "age": 42, "gender": "male", "bio": "", "avatar": "https://storage.googleapis.com/deumatch-demo.firebasestorage.app/influencers/avatars/temp_1752171603008_1752171604310.png", "backgroundImage": null, "gradient": null, "category": "Lifestyle", "categories": [], "totalFollowers": 2068888, "totalViews": 3443334, "engagementRate": 48.67, "rating": 4, "isVerified": true, "isAvailable": true, "status": "active", "instagramUsername": "marina<PERSON><PERSON>va_", "instagramFollowers": 34333, "instagramEngagementRate": 45, "instagramAvgViews": 3, "instagramStoriesViews": 3454765, "instagramReelsViews": 765432, "tiktokUsername": "@marinasilva", "tiktokFollowers": 2000000, "tiktokEngagementRate": 45, "tiktokAvgViews": 0, "tiktokVideoViews": 567777, "youtubeUsername": "fgggg", "youtubeFollowers": 34555, "youtubeSubscribers": 34555, "youtubeEngagementRate": 56, "youtubeAvgViews": 5, "youtubeShortsViews": 3434, "youtubeLongFormViews": 3434, "facebookUsername": "", "facebookFollowers": 0, "facebookEngagementRate": 0, "facebookAvgViews": 0, "twitchUsername": "", "twitchFollowers": 0, "twitchEngagementRate": 0, "twitchAvgViews": 0, "kwaiUsername": "", "kwaiFollowers": 0, "kwaiEngagementRate": 0, "kwaiAvgViews": 0, "promotesTraders": false, "responsibleName": "", "agencyName": "", "mainNetwork": "youtube", "mainPlatform": "youtube", "pricing": {"hasFinancialData": false, "priceRange": "", "avgPrice": 0, "lastPriceUpdate": "2025-07-15T00:04:43.066Z", "isNegotiable": false, "__typename": "DenormalizedPricing"}, "currentPricing": {"id": "I0JCDsydZAhLYSo0Tz7G", "services": {"instagram": {"story": {"price": 54.44, "currency": "BRL", "__typename": "PlatformPrice"}, "reel": {"price": 755.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "InstagramPricing"}, "tiktok": {"video": {"price": 4555.55, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "TikTokPricing"}, "youtube": {"insertion": {"price": 678.77, "currency": "BRL", "__typename": "PlatformPrice"}, "dedicated": {"price": 87.66, "currency": "BRL", "__typename": "PlatformPrice"}, "shorts": {"price": 65.43, "currency": "BRL", "__typename": "PlatformPrice"}, "__typename": "YouTubePricing"}, "__typename": "PricingServices"}, "isActive": true, "validFrom": "2025-07-14T23:17:21.753Z", "validUntil": null, "__typename": "InfluencerPricing"}, "currentDemographics": [{"id": "aWhqmdqKx0M1EKCAtLpt", "platform": "youtube", "audienceGender": {"male": 88.5, "female": 11.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 13.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "13-17", "percentage": 0.8, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 5.6, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 14.4, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 22.8, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 15.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 20.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 20.8, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "MKZ3vFFra9AjAv4ayc1i", "platform": "tiktok", "audienceGender": {"male": 85.5, "female": 14.5, "other": 0, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}, {"id": "sUe7c9vTbvqZQzSG2N9l", "platform": "instagram", "audienceGender": {"male": 85.5, "female": 14.5, "other": 2, "__typename": "AudienceGender"}, "audienceLocations": [{"country": "Brasil", "percentage": 93.1, "__typename": "AudienceLocation"}, {"country": "Estados Unidos", "percentage": 0.4, "__typename": "AudienceLocation"}, {"country": "Portugal", "percentage": 0.3, "__typename": "AudienceLocation"}, {"country": "Paraguai", "percentage": 0.2, "__typename": "AudienceLocation"}], "audienceCities": [{"city": "São Paulo", "percentage": 17.7, "__typename": "AudienceCity"}, {"city": "São Bernardo do Campo", "percentage": 2, "__typename": "AudienceCity"}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentage": 1.6, "__typename": "AudienceCity"}, {"city": "<PERSON>", "percentage": 1.4, "__typename": "AudienceCity"}, {"city": "Fortaleza", "percentage": 1.3, "__typename": "AudienceCity"}], "audienceAgeRange": [{"range": "13-17", "percentage": 2.3, "__typename": "AudienceAgeRange"}, {"range": "18-24", "percentage": 12.4, "__typename": "AudienceAgeRange"}, {"range": "25-34", "percentage": 33, "__typename": "AudienceAgeRange"}, {"range": "35-44", "percentage": 36, "__typename": "AudienceAgeRange"}, {"range": "45-54", "percentage": 10.3, "__typename": "AudienceAgeRange"}, {"range": "55-64", "percentage": 4.4, "__typename": "AudienceAgeRange"}, {"range": "65+", "percentage": 1.7, "__typename": "AudienceAgeRange"}], "isActive": true, "source": "form", "__typename": "AudienceDemographic"}], "budgets": {"instagram": [], "tiktok": [], "youtube": [], "facebook": [], "twitch": [], "kwai": [], "personalizado": [], "__typename": "InfluencerBudgets"}, "createdAt": "2025-07-10T18:20:07.798Z", "updatedAt": "2025-07-14T23:17:21.309Z", "__typename": "Influencer"}], "__typename": "InfluencerConnection"}}}