'use client';

import { useState, useEffect, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Protect } from '@clerk/nextjs';
import { useAuth } from '@/hooks/use-auth-v2';
import { useBrandsGraphQL, useBrandMutations } from '@/hooks/use-graphql-influencers';
import { CreateBrandData, Brand } from '@/types/brand';
import { hasPermission, isAdmin } from '@/lib/security-config';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Building2, Plus, AlertTriangle, Search, Filter, MoreHorizontal, Edit, Trash2, ExternalLink, X, Shield, Check } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader } from '@/components/ui/loader';
import { BrandLogoUpload } from '@/components/ui/brand-logo-upload';

// Componente protegido por role admin
function BrandsSettingsContent() {
  const params = useParams();
  const router = useRouter();
  const { currentUser, firebaseUser, isLoading, isInitialized, getToken } = useAuth();
  
  // GraphQL hooks para marcas
  const { brands, loading: brandsLoading, error: brandsError, refetch: refetchBrands } = useBrandsGraphQL(currentUser?.id || '');
  const { createBrand, updateBrand, deleteBrand } = useBrandMutations();
  
  // Estados do formulário
  const [isCreating, setIsCreating] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null);
  const [selectedLogoFile, setSelectedLogoFile] = useState<File | null>(null);
  const [formData, setFormData] = useState<CreateBrandData>({
    name: '',
    description: '',
    website: '',
    industry: '',
    logo: ''
  });

  // Estados de filtros e pesquisa
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [industryFilter, setIndustryFilter] = useState<string>('all');
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);

  const targetUserId = params?.userId as string;
  const isOwnProfile = currentUser?.id === targetUserId;
  const canAccess = isOwnProfile || (currentUser && isAdmin(currentUser.role));

  // Verificação de permissões
  useEffect(() => {
    if (!isInitialized || isLoading) {
      return;
    }

    if (!canAccess) {
      toast.error('Acesso negado. Você só pode gerenciar suas próprias marcas.');
      router.push('/dashboard');
      return;
    }

    // Debug do currentUser e role
    console.log('[BRANDS] Debug currentUser:', {
      currentUser,
      role: currentUser?.role,
      isInitialized,
      isLoading,
      canAccess
    });

    if (!currentUser || !currentUser.role) {
      console.log('[BRANDS] currentUser ou role está undefined');
      return; // Aguardar até que o role seja carregado
    }

    const hasViewPermission = hasPermission(currentUser.role, 'view_brands');
    console.log('[BRANDS] Verificação de permissão:', {
      userRole: currentUser.role,
      hasViewPermission,
      permission: 'view_brands'
    });

    if (!hasViewPermission) {
      console.log('[BRANDS] Sem permissão view_brands - redirecionando para dashboard');
      toast.error('Você não tem permissão para visualizar marcas.');
      router.push('/dashboard');
      return;
    }
  }, [currentUser, isInitialized, isLoading, targetUserId, canAccess, router]);

  // Filtrar marcas do usuário
  const userBrands = brands.filter(brand => brand.userId === targetUserId);

  // Aplicar filtros e pesquisa
  const filteredBrands = useMemo(() => {
    // ✅ CORREÇÃO: Remover duplicatas antes de aplicar filtros
    const uniqueUserBrands = userBrands.filter((brand: Brand, index: number, self: Brand[]) => 
      index === self.findIndex((b: Brand) => b.id === brand.id)
    );
    
    return uniqueUserBrands.filter((brand: Brand) => {
      const matchesSearch = brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           brand.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           brand.industry?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesIndustry = industryFilter === 'all' || brand.industry === industryFilter;
      
      return matchesSearch && matchesIndustry;
    });
  }, [userBrands, searchTerm, statusFilter, industryFilter]);

  // Obter indústrias únicas para o filtro
  const uniqueIndustries = useMemo(() => {
    const industries = userBrands
      .map((brand: Brand) => brand.industry)
      .filter((industry): industry is string => Boolean(industry));
    return [...new Set(industries)] as string[];
  }, [userBrands]);

  const handleInputChange = (field: keyof CreateBrandData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Função para limpar dados antes de enviar para API
  const sanitizeFormData = (data: typeof formData): CreateBrandData => {
    const sanitized: any = {
      name: data.name.trim(),
      userId: currentUser?.id || '',
    };

    // Adicionar campos opcionais apenas se não estiverem vazios
    if (data.description && data.description.trim()) {
      sanitized.description = data.description.trim();
    }

    if (data.website && data.website.trim()) {
      // Garantir que o website tenha protocolo
      let website = data.website.trim();
      if (!website.startsWith('http://') && !website.startsWith('https://')) {
        website = `https://${website}`;
      }
      sanitized.website = website;
    }

    if (data.industry && data.industry.trim()) {
      sanitized.industry = data.industry.trim();
    }

    if (data.logo && data.logo.trim()) {
      // Garantir que o logo tenha protocolo
      let logo = data.logo.trim();
      if (!logo.startsWith('http://') && !logo.startsWith('https://')) {
        logo = `https://${logo}`;
      }
      sanitized.logo = logo;
    }

    return sanitized as CreateBrandData;
  };

  const handleCreateBrand = async () => {
    if (!formData.name.trim()) {
      toast.error('Nome da marca é obrigatório');
      return;
    }

    if (!currentUser || !hasPermission(currentUser.role, 'create_brands')) {
      toast.error('Você não tem permissão para criar marcas');
      return;
    }

    try {
      setIsCreating(true);
      
      let logoUrl = '';
      
      // 1. Fazer upload da logo se um arquivo foi selecionado
      if (selectedLogoFile) {
        toast.info('Enviando logo da marca...');
        
        try {
          // Usar token do Clerk ao invés do Firebase
          const token = await getToken();
          const formDataUpload = new FormData();
          formDataUpload.append('file', selectedLogoFile);
          formDataUpload.append('type', 'brand-logo');
          
          const uploadResponse = await fetch('/api/uploads/brands/logos', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`
            },
            body: formDataUpload
          });

          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json().catch(() => ({}));
            throw new Error(errorData.error || 'Erro no upload da logo');
          }

          const uploadResult = await uploadResponse.json();
          
          if (!uploadResult.success) {
            throw new Error(uploadResult.error || 'Falha no upload da logo');
          }

          logoUrl = uploadResult.url;
          console.log('[CRIAR_MARCA] Logo enviada com sucesso:', logoUrl);
          
        } catch (uploadError) {
          console.error('Erro no upload da logo:', uploadError);
          throw new Error('Erro ao enviar logo: ' + (uploadError instanceof Error ? uploadError.message : 'Erro desconhecido'));
        }
      }

      // 2. Limpar e validar dados antes de enviar
      const sanitizedData = sanitizeFormData({
        ...formData,
        logo: logoUrl || formData.logo // Usar logo enviada ou URL manual
      });
      
      console.log('[CRIAR_MARCA] Dados enviados:', sanitizedData);
      
      // 3. Criar a marca
      await createBrand(sanitizedData);

      // 4. Limpar formulário e fechar painel
      setFormData({
        name: '',
        description: '',
        website: '',
        industry: '',
        logo: ''
      });
      setSelectedLogoFile(null);
      setIsPanelOpen(false);
      setEditingBrand(null);

      toast.success('Marca criada com sucesso!');
    } catch (error) {
      console.error('Erro ao criar marca:', error);
      
      // Tratamento melhorado de erros
      let errorMessage = 'Erro ao criar marca. Tente novamente.';
      
      if (error instanceof Error) {
        if (error.message.includes('Erro ao enviar logo')) {
          errorMessage = error.message;
        } else if (error.message.includes('URL inválida')) {
          errorMessage = 'Verifique se o website e logo são URLs válidas';
        } else if (error.message.includes('já existe')) {
          errorMessage = 'Uma marca com este nome já existe';
        } else if (error.message.includes('Dados inválidos')) {
          errorMessage = 'Verifique os dados inseridos - website e logo devem ser URLs válidas';
        } else {
          errorMessage = error.message;
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsCreating(false);
    }
  };

  const handleEditBrand = (brand: Brand) => {
    setEditingBrand(brand);
    setFormData({
      name: brand.name,
      description: brand.description || '',
      website: brand.website || '',
      industry: brand.industry || '',
      logo: brand.logo || ''
    });
    setIsPanelOpen(true);
  };

  const handleUpdateBrand = async () => {
    if (!editingBrand || !formData.name.trim()) {
      toast.error('Nome da marca é obrigatório');
      return;
    }

    if (!currentUser || !hasPermission(currentUser.role, 'edit_brands')) {
      toast.error('Você não tem permissão para editar marcas');
      return;
    }

    try {
      setIsCreating(true);
      
      console.log('[UPDATE_BRAND] Iniciando atualização da marca:', {
        brandId: editingBrand.id,
        currentUser: currentUser?.id,
        formData
      });
      
      const updateData = {
        name: formData.name.trim(),
        description: formData.description?.trim() || '',
        website: formData.website?.trim() || '',
        industry: formData.industry?.trim() || '',
        logo: formData.logo?.trim() || '',
        updatedAt: new Date()
      };
      
      console.log('[UPDATE_BRAND] Dados de atualização:', updateData);
      
      await updateBrand(editingBrand.id, updateData);

      // Limpar estados
      setEditingBrand(null);
      setFormData({
        name: '',
        description: '',
        website: '',
        industry: '',
        logo: ''
      });
      setIsPanelOpen(false);

      console.log('[UPDATE_BRAND] Marca atualizada com sucesso');
      toast.success('Marca atualizada com sucesso!');
    } catch (error) {
      console.error('[UPDATE_BRAND] Erro ao atualizar marca:', error);
      
      // Tratamento melhorado de erros GraphQL
      let errorMessage = 'Erro ao atualizar marca. Tente novamente.';
      
      if (error instanceof Error) {
        if (error.message.includes('não autenticado')) {
          errorMessage = 'Sessão expirada. Faça login novamente.';
          // Redirecionar para login se necessário
          router.push('/sign-in');
        } else if (error.message.includes('não tem permissão')) {
          errorMessage = 'Você não tem permissão para editar esta marca.';
        } else if (error.message.includes('já existe')) {
          errorMessage = 'Uma marca com este nome já existe.';
        } else {
          errorMessage = error.message;
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteBrand = async (brand: Brand) => {
    if (!currentUser || !hasPermission(currentUser.role, 'delete_brands')) {
      toast.error('Você não tem permissão para deletar marcas');
      return;
    }

    if (!confirm(`Tem certeza que deseja deletar a marca "${brand.name}"? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      console.log('[DELETE_BRAND] Iniciando exclusão da marca:', {
        brandId: brand.id,
        brandName: brand.name,
        currentUser: currentUser?.id
      });
      
      await deleteBrand(brand.id);
      
      console.log('[DELETE_BRAND] Marca deletada com sucesso');
      toast.success('Marca deletada com sucesso!');
    } catch (error) {
      console.error('[DELETE_BRAND] Erro ao deletar marca:', error);
      
      // Tratamento melhorado de erros GraphQL
      let errorMessage = 'Erro ao deletar marca. Tente novamente.';
      
      if (error instanceof Error) {
        if (error.message.includes('não autenticado')) {
          errorMessage = 'Sessão expirada. Faça login novamente.';
          // Redirecionar para login se necessário
          router.push('/sign-in');
        } else if (error.message.includes('não tem permissão')) {
          errorMessage = 'Você não tem permissão para deletar esta marca.';
        } else if (error.message.includes('associações com influenciadores')) {
          errorMessage = 'Não é possível deletar uma marca que possui associações com influenciadores.';
        } else {
          errorMessage = error.message;
        }
      }
      
      toast.error(errorMessage);
    }
  };

  const handleSelectBrand = (brandId: string, checked: boolean) => {
    if (checked) {
      setSelectedBrands(prev => [...prev, brandId]);
    } else {
      setSelectedBrands(prev => prev.filter(id => id !== brandId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedBrands(filteredBrands.map(brand => brand.id));
    } else {
      setSelectedBrands([]);
    }
  };

  const resetPanel = () => {
    setEditingBrand(null);
    setSelectedLogoFile(null);
    setFormData({
      name: '',
      description: '',
      website: '',
      industry: '',
      logo: ''
    });
    setIsPanelOpen(false);
  };

  const openCreatePanel = () => {
    resetPanel();
    setIsPanelOpen(true);
  };

  // Loading state
  if (isLoading || brandsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader message="Carregando configurações..." />
      </div>
    );
  }

  // Access denied
  if (!canAccess || !currentUser) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            Acesso negado. Você só pode gerenciar suas próprias marcas.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Conteúdo Principal */}
      <div className={cn("flex-1 overflow-auto transition-all duration-300", isPanelOpen ? "mr-96" : "mr-0")}>
        <div className="p-6">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
               
              </div>
              
         
            </div>

            {/* Barra de Filtros */}
            <div className="flex items-center gap-4 mb-6">
              {/* Pesquisa */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Pesquisar marcas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filtro por Setor */}
              <Select value={industryFilter} onValueChange={setIndustryFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filtrar por setor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os setores</SelectItem>
                  {uniqueIndustries.map((industry) => (
                    <SelectItem key={industry} value={industry}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Badge de filtros aplicados */}
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {filteredBrands.length === userBrands.length 
                    ? '0 filtros aplicados' 
                    : `${userBrands.length - filteredBrands.length} filtro(s) aplicado(s)`
                  }
                </Badge>
              </div>
 {/* Botão Nova Marca */}
 <Button 
                className="bg-[#ff0074] hover:bg-[#e6006a] text-white"
                onClick={openCreatePanel}
              >
                <Plus className="h-4 w-4 mr-2" />
                Crie uma nova marca
              </Button>
              {/* Ações */}
              {selectedBrands.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      Ações ({selectedBrands.length})
                      <MoreHorizontal className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setSelectedBrands([])}>
                      Desmarcar todas
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      className="text-destructive"
                      onClick={() => {
                        if (confirm(`Deletar ${selectedBrands.length} marca(s) selecionada(s)?`)) {
                          selectedBrands.forEach(brandId => {
                            const brand = userBrands.find((b: Brand) => b.id === brandId);
                            if (brand) handleDeleteBrand(brand);
                          });
                          setSelectedBrands([]);
                        }
                      }}
                    >
                      Deletar selecionadas
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
                  
            </div>
          </div>

          {/* Tabela de Marcas */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedBrands.length === filteredBrands.length && filteredBrands.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>NOME DA MARCA</TableHead>
                    <TableHead>SÍTIO WEB DA MARCA</TableHead>
                    <TableHead>CRIADO POR</TableHead>
                    <TableHead>EM UTILIZAÇÃO</TableHead>
                    <TableHead>DATA DE CRIAÇÃO</TableHead>
                    <TableHead className="w-12"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBrands.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-12">
                        <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <p className="text-muted-foreground mb-2">
                          {searchTerm || industryFilter !== 'all' 
                            ? 'Nenhuma marca encontrada com os filtros aplicados.'
                            : 'Nenhuma marca criada ainda.'
                          }
                        </p>
                        {!searchTerm && industryFilter === 'all' && (
                          <p className="text-sm text-muted-foreground">
                            Clique em "Crie uma nova marca" para começar.
                          </p>
                        )}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBrands.map((brand) => (
                      <TableRow key={brand.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedBrands.includes(brand.id)}
                            onCheckedChange={(checked) => handleSelectBrand(brand.id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            {brand.logo ? (
                              <img
                                src={brand.logo}
                                alt={`Logo ${brand.name}`}
                                className="w-8 h-8 rounded object-cover"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                            ) : (
                              <div className="w-8 h-8 rounded bg-[#ff0074]/10 flex items-center justify-center">
                                <Building2 className="h-4 w-4 text-[#ff0074]" />
                              </div>
                            )}
                            <div>
                              <p className="font-medium">{brand.name}</p>
                              {brand.industry && (
                                <p className="text-xs text-muted-foreground">{brand.industry}</p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {brand.website ? (
                            <a
                              href={brand.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[#ff0074] hover:underline flex items-center gap-1"
                            >
                              {brand.website.replace(/^https?:\/\//, '')}
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{currentUser?.name ?? 'Usuário'}</span>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">0</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {new Date(brand.createdAt).toLocaleDateString('pt-BR')}
                          </span>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditBrand(brand)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleDeleteBrand(brand)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Deletar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Painel Lateral Direito */}
      <div className={cn(
        "fixed top-0 right-0 h-full w-96 bg-background border-l border-border transform transition-transform duration-300 ease-in-out z-50 shadow-xl",
        isPanelOpen ? "translate-x-0" : "translate-x-full"
      )}>
        {/* Header do Painel */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-lg font-semibold">
              {editingBrand ? 'Editar Marca' : 'Nova Marca'}
            </h2>
            <p className="text-sm text-muted-foreground">
              {editingBrand 
                ? 'Atualize as informações da marca.' 
                : 'Preencha as informações para criar uma nova marca.'
              }
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={resetPanel}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Formulário */}
        <div className="p-6 space-y-6 overflow-y-auto h-full pb-24">
          {/* Nome da Marca */}
          <div className="space-y-2">
            <Label htmlFor="name">Nome da Marca *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Ex: Netflix, Coca-Cola, Nike..."
            />
          </div>

          {/* Descrição */}
          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Breve descrição sobre a marca..."
              rows={3}
            />
          </div>

          {/* Website */}
          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              type="url"
              value={formData.website || ''}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://www.exemplo.com"
            />
          </div>

          {/* Setor */}
          <div className="space-y-2">
            <Label htmlFor="industry">Setor</Label>
            <Input
              id="industry"
              value={formData.industry || ''}
              onChange={(e) => handleInputChange('industry', e.target.value)}
              placeholder="Ex: Tecnologia, Moda, Alimentício..."
            />
          </div>

          {/* Upload do Logo */}
          <div className="space-y-2">
            <Label htmlFor="logo">Logo da Marca</Label>
            <BrandLogoUpload
              value={formData.logo || undefined}
              file={selectedLogoFile}
              mode="local"
              onChange={(logoUrl: string | null) => handleInputChange('logo', logoUrl || '')}
              onFileChange={(file: File | null) => setSelectedLogoFile(file)}
              disabled={isCreating}
            />
          </div>
        </div>

        {/* Botões de Ação - Fixos no Final */}
        <div className="absolute bottom-0 left-0 right-0 p-6 bg-background border-t border-border">
          <div className="flex gap-3">
            <Button
              onClick={editingBrand ? handleUpdateBrand : handleCreateBrand}
              disabled={isCreating || !formData.name.trim()}
              className="flex-1 bg-[#ff0074] hover:bg-[#e6006a]"
            >
              {isCreating 
                ? (editingBrand ? 'Atualizando...' : 'Criando...') 
                : (editingBrand ? 'Atualizar' : 'Criar Marca')
              }
            </Button>
            
            <Button variant="outline" onClick={resetPanel}>
              Cancelar
            </Button>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isPanelOpen && (
        <div 
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={resetPanel}
        />
      )}
    </div>
  );
}

// Componente principal com proteção de role admin
export default function UserBrandsSettingsPage() {
  return (
    <Protect 
      role="org:admin"
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">Acesso Restrito</h2>
            <p className="text-muted-foreground">
              Apenas administradores podem acessar as configurações de marcas.
            </p>
          </div>
        </div>
      }
    >
      <BrandsSettingsContent />
    </Protect>
  );
} 