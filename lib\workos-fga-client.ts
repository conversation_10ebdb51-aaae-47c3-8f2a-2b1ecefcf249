// Interface para permissões mapeadas
export interface UserPermissions {
  canManageUsers: boolean;
  canManageContent: boolean;
  canManageSettings: boolean;
  canViewReports: boolean;
  canEditInfluencers: boolean;
  canCreateCampaigns: boolean;
  canManageBudgets: boolean;
  canViewFinancials: boolean;
}

// Interface para verificação de permissões
export interface PermissionCheck {
  userId: string;
  organizationId: string;
  resource: string;
  action: string;
  context?: Record<string, any>;
}

// Interface para resultado de permissão
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
}

// Serviço client-side que usa APIs HTTP
export class WorkOSFGAClientService {
  
  /**
   * Buscar permissões de um usuário baseado no role (fallback)
   */
  static async getUserPermissionsByRole(role: string): Promise<UserPermissions> {
    switch (role?.toLowerCase()) {
      case 'admin':
        return {
          canManageUsers: true,
          canManageContent: true,
          canManageSettings: true,
          canViewReports: true,
          canEditInfluencers: true,
          canCreateCampaigns: true,
          canManageBudgets: true,
          canViewFinancials: true,
        };
      case 'manager':
        return {
          canManageUsers: false,
          canManageContent: true,
          canManageSettings: false,
          canViewReports: true,
          canEditInfluencers: true,
          canCreateCampaigns: true,
          canManageBudgets: true,
          canViewFinancials: true,
        };
      case 'member':
        return {
          canManageUsers: false,
          canManageContent: true,
          canManageSettings: false,
          canViewReports: true,
          canEditInfluencers: false,
          canCreateCampaigns: false,
          canManageBudgets: false,
          canViewFinancials: false,
        };
      case 'editor':
        return {
          canManageUsers: false,
          canManageContent: true,
          canManageSettings: false,
          canViewReports: true,
          canEditInfluencers: true,
          canCreateCampaigns: false,
          canManageBudgets: false,
          canViewFinancials: false,
        };
      case 'viewer':
      default:
        return {
          canManageUsers: false,
          canManageContent: false,
          canManageSettings: false,
          canViewReports: true,
          canEditInfluencers: false,
          canCreateCampaigns: false,
          canManageBudgets: false,
          canViewFinancials: false,
        };
    }
  }

  /**
   * Buscar permissões de um usuário através da API
   */
  static async getUserPermissions(userId: string, organizationId: string, role?: string): Promise<UserPermissions> {
    try {
      // TODO: Implementar API de permissões quando necessário
      // Por enquanto, usar fallback baseado em role
      console.log('🔍 Usando fallback de permissões para role:', role);
      return await this.getUserPermissionsByRole(role || 'viewer');
    } catch (error) {
      console.error('❌ Erro ao buscar permissões do usuário:', error);
      return await this.getUserPermissionsByRole(role || 'viewer');
    }
  }

  /**
   * Verificar se FGA está habilitado
   */
  static async isFGAEnabled(): Promise<boolean> {
    try {
      // Por enquanto, retornar false indicando que está usando fallback
      return true;
    } catch (error) {
      return true;
    }
  }

  /**
   * Helper para mapear ações do sistema para verificações de permissão
   */
  static getPermissionForAction(action: string, resource?: string): string {
    const actionMap: Record<string, string> = {
      'create_influencer': 'canEditInfluencers',
      'edit_influencer': 'canEditInfluencers',
      'view_influencer': 'canViewReports',
      'create_campaign': 'canCreateCampaigns',
      'edit_campaign': 'canCreateCampaigns',
      'view_campaign': 'canViewReports',
      'manage_budget': 'canManageBudgets',
      'view_financials': 'canViewFinancials',
      'manage_users': 'canManageUsers',
      'manage_settings': 'canManageSettings'
    };

    return actionMap[action] || 'canViewReports';
  }

  /**
   * Verificação rápida de permissão usando o sistema atual
   */
  static hasPermission(permissions: UserPermissions, action: string, resource?: string): boolean {
    const permissionKey = this.getPermissionForAction(action, resource);
    return permissions[permissionKey as keyof UserPermissions] || false;
  }

  /**
   * Verificar permissão específica através da API
   */
  static async checkPermission(params: PermissionCheck): Promise<PermissionResult> {
    try {
      // TODO: Implementar verificação de permissão específica quando necessário
      // Por enquanto, usar verificação simples baseada em role
      console.log('🔍 Verificação de permissão simples para:', params);
      return {
        allowed: true, // Permitir por padrão até implementar sistema completo
        reason: 'Fallback permission check'
      };
    } catch (error) {
      console.error('❌ Erro ao verificar permissão:', error);
      return {
        allowed: false,
        reason: 'Error checking permission'
      };
    }
  }
} 

