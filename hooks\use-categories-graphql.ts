'use client'

import { useQuery, useMutation, useApolloClient } from '@apollo/client'
import { useFirebaseAuth } from '@/hooks/use-clerk-auth'
import { 
  GET_CATEGORIES_QUERY,
  GET_USER_CATEGORIES_QUERY, 
  CREATE_CATEGORY_MUTATION, 
  UPDATE_CATEGORY_MUTATION, 
  DELETE_CATEGORY_MUTATION 
} from '@/lib/graphql/mutations'

export interface Category {
  id: string
  name: string
  slug?: string
  description?: string
  color?: string
  count?: number | null
  userId: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateCategoryInput {
  name: string
  userId: string
  isActive?: boolean
}

export interface UpdateCategoryInput {
  name?: string
  isActive?: boolean
}

export function useCategoriesGraphQL(userSpecific: boolean = false) {
  const { currentUser } = useFirebaseAuth()
  const client = useApolloClient()

  // 🔥 OTIMIZAÇÃO: Query com cache inteligente
  const { 
    data: categoriesData, 
    loading: categoriesLoading, 
    error: categoriesError,
    refetch: refetchCategories 
  } = useQuery(userSpecific ? GET_USER_CATEGORIES_QUERY : GET_CATEGORIES_QUERY, {
    variables: userSpecific 
      ? { userId: currentUser?.id || '' } 
      : { userId: currentUser?.id }, // 🔄 CORREÇÃO: Sempre passar userId para incluir categorias do usuário
    skip: userSpecific && !currentUser?.id,
    errorPolicy: 'all',
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache (era cache-and-network)
    notifyOnNetworkStatusChange: false // 🔥 OTIMIZAÇÃO: Reduzir re-renders
  })

  // Debug removido para produção

  // Mutation para criar category
  const [createCategoryMutation, { loading: createLoading }] = useMutation(CREATE_CATEGORY_MUTATION, {
    update(cache, { data }) {
      if (data?.createCategory) {
        // Atualizar cache adicionando nova category
        const query = userSpecific ? GET_USER_CATEGORIES_QUERY : GET_CATEGORIES_QUERY
        const variables = userSpecific ? { userId: currentUser?.id || '' } : undefined
        
        const existingCategories = cache.readQuery<{ categories?: Category[], userCategories?: Category[] }>({
          query,
          variables
        })

        const categoriesField = userSpecific ? 'userCategories' : 'categories'
        const existingList = userSpecific ? existingCategories?.userCategories : existingCategories?.categories

        if (existingList) {
          cache.writeQuery({
            query,
            variables,
            data: {
              [categoriesField]: [data.createCategory, ...existingList]
            }
          })
        }
      }
    },
    onError: (error) => {
      // Erro silencioso - tratado pelo UI
    }
  })

  // Mutation para atualizar category
  const [updateCategoryMutation, { loading: updateLoading }] = useMutation(UPDATE_CATEGORY_MUTATION, {
    onError: (error) => {
      // Erro silencioso - tratado pelo UI
    }
  })

  // Mutation para deletar category
  const [deleteCategoryMutation, { loading: deleteLoading }] = useMutation(DELETE_CATEGORY_MUTATION, {
    update(cache, { data }, { variables }) {
      if (data?.deleteCategory && variables?.id) {
        // Remover category do cache
        const query = userSpecific ? GET_USER_CATEGORIES_QUERY : GET_CATEGORIES_QUERY
        const queryVariables = userSpecific ? { userId: currentUser?.id || '' } : undefined
        
        const existingCategories = cache.readQuery<{ categories?: Category[], userCategories?: Category[] }>({
          query,
          variables: queryVariables
        })

        const categoriesField = userSpecific ? 'userCategories' : 'categories'
        const existingList = userSpecific ? existingCategories?.userCategories : existingCategories?.categories

        if (existingList) {
          cache.writeQuery({
            query,
            variables: queryVariables,
            data: {
              [categoriesField]: existingList.filter((category: Category) => category.id !== variables.id)
            }
          })
        }
      }
    },
    onError: (error) => {
      // Erro silencioso - tratado pelo UI
    }
  })

  // Funções auxiliares
  const createCategory = async (input: CreateCategoryInput) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await createCategoryMutation({
        variables: {
          input: {
            ...input,
            userId: currentUser.id
          }
        }
      })
      return result.data?.createCategory
    } catch (error) {
      throw error
    }
  }

  const updateCategory = async (id: string, input: UpdateCategoryInput) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await updateCategoryMutation({
        variables: { id, input }
      })
      return result.data?.updateCategory
    } catch (error) {
      throw error
    }
  }

  const deleteCategory = async (id: string) => {
    if (!currentUser?.id) {
      throw new Error('Usuário não autenticado')
    }

    try {
      const result = await deleteCategoryMutation({
        variables: { 
          id, 
          userId: currentUser.id 
        }
      })
      return result.data?.deleteCategory
    } catch (error) {
      throw error
    }
  }

  const clearCategoriesCache = () => {
    client.cache.evict({
      fieldName: 'categories'
    })
    client.cache.evict({
      fieldName: 'userCategories'
    })
    client.cache.gc()
  }

  // Determinar qual lista usar baseado no tipo de query
  const categories = userSpecific 
    ? categoriesData?.userCategories || []
    : categoriesData?.categories || []

  return {
    // Dados
    categories,
    
    // Estados de loading
    categoriesLoading,
    createLoading,
    updateLoading,
    deleteLoading,
    
    // Erros
    categoriesError,
    
    // Funções
    createCategory,
    updateCategory,
    deleteCategory,
    refetchCategories,
    clearCategoriesCache,
    
    // Estados computados
    isLoading: categoriesLoading || createLoading || updateLoading || deleteLoading,
    hasError: !!categoriesError
  }
} 

