# Script cuidadoso para remover console.log linha por linha
param(
    [string]$FilePath = "hooks/use-influencer-form.ts"
)

Write-Host "🧹 Limpeza cuidadosa de console.log em $FilePath..." -ForegroundColor Yellow

if (-not (Test-Path $FilePath)) {
    Write-Host "❌ Arquivo não encontrado: $FilePath" -ForegroundColor Red
    exit 1
}

# Backup
$backupPath = "$FilePath.backup-careful-" + (Get-Date -Format "yyyyMMdd-HHmmss")
Copy-Item $FilePath $backupPath
Write-Host "📁 Backup criado: $backupPath" -ForegroundColor Green

# Ler todas as linhas
$lines = Get-Content $FilePath

# Processar linha por linha
$cleanedLines = @()
$removedCount = 0

foreach ($line in $lines) {
    # Se a linha contém APENAS um console.log (com possível indentação), pular
    if ($line -match '^\s*console\.(log|error|warn|info|debug)\([^;]*\);\s*$') {
        $removedCount++
        # Pular esta linha (não adicionar ao array)
    } else {
        # Manter a linha
        $cleanedLines += $line
    }
}

# Escrever arquivo limpo
$cleanedLines | Set-Content $FilePath -Encoding UTF8

Write-Host "✅ Limpeza concluída!" -ForegroundColor Green
Write-Host "📊 Console.log removidos: $removedCount" -ForegroundColor Cyan
Write-Host "📊 Linhas restantes: $($cleanedLines.Count)" -ForegroundColor Cyan

# Verificar resultado
$remaining = Select-String -Path $FilePath -Pattern "console\.(log|error|warn|info|debug)" -AllMatches
if ($remaining) {
    Write-Host "⚠️ Ainda restam $($remaining.Count) console.log no arquivo" -ForegroundColor Yellow
    Write-Host "   (Provavelmente console.log multilinhas que precisam revisão manual)" -ForegroundColor Yellow
} else {
    Write-Host "🎉 Nenhum console.log encontrado! Arquivo completamente limpo." -ForegroundColor Green
} 