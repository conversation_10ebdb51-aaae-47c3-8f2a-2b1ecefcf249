import { 
  collection, 
  doc, 
  addDoc, 
  setDoc,
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit as firestoreLimit, 
  Timestamp,
  Query,
  DocumentData,
  writeBatch,
  increment
} from 'firebase/firestore';
import { clientDb as db } from '@/lib/firebase-client';

// Tipos simplificados baseados no painel real
export interface ProposalService {
  serviceId: string;
  quantity: number;
  customPrice?: number;
  notes?: string;
}

export interface InfluencerInProposal {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  tier?: string;
  followers?: number;
  engagement?: number;
}

export interface CreateProposalData {
  nome: string;
  descricao?: string;
  criadoPor: string;
  influencers: InfluencerInProposal[];
  brandId: string;
  services: ProposalService[];
  totalAmount: number;
  dataEnvio: string;
  grupo?: string;
  priority: 'low' | 'medium' | 'high';
  icon?: string; // ✅ NOVO: Campo para ícone da proposta
}

export interface Proposal {
  id: string;
  nome: string;
  descricao?: string;
  criadoPor: string;
  influencers: InfluencerInProposal[];
  brandId: string;
  services: ProposalService[];
  totalAmount: number;
  dataEnvio: string;
  grupo?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'draft' | 'pending' | 'sent' | 'viewed' | 'under_review' | 'negotiating' | 'approved' | 'accepted' | 'rejected' | 'expired' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  icon?: string; // ✅ NOVO: Campo para ícone da proposta
  budgets?: {
    [profileId: string]: {
      [service: string]: {
        originalPrice: number;
        budgetedPrice: number;
        updatedAt: Date;
        updatedBy: string;
      };
    };
  };
}

export interface ProposalFilters {
  brandId?: string;
  status?: string[];
  priority?: string[];
  grupo?: string;
}

export class ProposalService {
  private static readonly COLLECTION_NAME = 'proposals';

  /**
   * Criar nova proposta
   */
  static async createProposal(data: CreateProposalData): Promise<string> {
    try {
      const now = new Date();
      
      const proposalData = {
        ...data,
        status: 'negotiating', // ✅ Alterado de 'draft' para 'negotiating' (em andamento)
        createdAt: Timestamp.fromDate(now),
        updatedAt: Timestamp.fromDate(now)
      };

      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), proposalData);
      return docRef.id;
    } catch (error) {
      throw new Error('Falha ao criar proposta');
    }
  }

  /**
   * Contar propostas com filtros
   */
  static async countProposals(
    filters: ProposalFilters = {},
    userId?: string
  ): Promise<number> {
    try {
      let q = query(collection(db, this.COLLECTION_NAME));

      // Aplicar filtros - Fase 1
      if (filters.brandId) {
        q = query(q, where('brandId', '==', filters.brandId));
      }

      if (filters.status && filters.status.length > 0 && filters.status.length <= 10) {
        q = query(q, where('status', 'in', filters.status));
      }

      if (filters.priority && filters.priority.length > 0 && filters.priority.length <= 10) {
        q = query(q, where('priority', 'in', filters.priority));
      }

      if (filters.grupo) {
        q = query(q, where('grupo', '==', filters.grupo));
      }

      // Se não há brandId específico nos filtros, usar o userId como brandId
      if (!filters.brandId && userId) {
        q = query(q, where('brandId', '==', userId));
      }

      const snapshot = await getDocs(q);
      let count = snapshot.size;
      
      // Se há filtros que precisam ser aplicados no lado do cliente
      if ((filters.status && filters.status.length > 10) || 
          (filters.priority && filters.priority.length > 10)) {
        // Buscar todas as propostas e aplicar filtros
        const proposals = await this.getProposals(filters, 1000, 0, userId);
        count = proposals.length;
      }
      
      return count;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Buscar propostas com filtros
   */
  static async getProposals(
    filters: ProposalFilters = {}, 
    limit: number = 50,
    offset: number = 0,
    userId?: string
  ): Promise<Proposal[]> {
    try {
let queryRef = collection(db, this.COLLECTION_NAME) as Query;

      // Aplicar filtros - Implementação Fase 1 com filtros avançados por marca
      if (filters.brandId) {
queryRef = query(queryRef, where('brandId', '==', filters.brandId));
      } else if (userId) {
// Para carregar todas as propostas, buscar onde criadoPor = userId OU onde brandId = userId
        // Como Firestore não suporta OR diretamente, vamos buscar por brandId do usuário
        // assumindo que brandId representa as marcas do usuário
        queryRef = query(queryRef, where('brandId', '==', userId));
      } else {
return [];
      }

      if (filters.status && filters.status.length > 0) {
// Para múltiplos status, usar 'in' se <= 10 itens, senão filtrar depois
        if (filters.status.length <= 10) {
          queryRef = query(queryRef, where('status', 'in', filters.status));
        }
      }

      if (filters.priority && filters.priority.length > 0) {
// Para múltiplas prioridades, usar 'in' se <= 10 itens
        if (filters.priority.length <= 10) {
          queryRef = query(queryRef, where('priority', 'in', filters.priority));
        }
      }

      if (filters.grupo) {
queryRef = query(queryRef, where('grupo', '==', filters.grupo));
      }

      // Ordenação
queryRef = query(queryRef, orderBy('createdAt', 'desc'));

      // Executar consulta
const snapshot = await getDocs(queryRef);
const proposals: Proposal[] = [];

      // ✅ CORREÇÃO: Buscar influenciadores da subcoleção para cada proposta
      for (const doc of snapshot.docs) {
        try {
          const data = doc.data() as DocumentData;
          
          // Buscar influenciadores da subcoleção
          const influencersFromSubcollection = await this.getProposalInfluencers(doc.id);
          
          // Criar array de influenciadores no formato correto
          const influencers: InfluencerInProposal[] = influencersFromSubcollection.map((inf: any) => ({
            id: inf.influencerId,
            name: inf.influencerId, // Vai ser resolvido depois com os dados reais
            email: '',
            avatar: '',
            tier: '',
            followers: 0,
            engagement: 0
          }));

proposals.push({
            id: doc.id,
            nome: data?.nome || '',
            descricao: data?.descricao,
            criadoPor: data?.criadoPor || '',
            influencers: influencers, // ✅ CORREÇÃO: Usar influenciadores da subcoleção
            brandId: data?.brandId || '',
            services: data?.services || [],
            totalAmount: data?.totalAmount || 0,
            dataEnvio: data?.dataEnvio || '',
            grupo: data?.grupo,
            priority: data?.priority || 'medium',
            status: data?.status || 'draft',
            createdAt: data?.createdAt?.toDate() || new Date(),
            updatedAt: data?.updatedAt?.toDate() || new Date(),
            sentAt: data?.sentAt?.toDate(),
            icon: data?.icon // ✅ CORRIGIDO: Incluir campo icon
          } as Proposal);
        } catch (docError) {
          // Documento inválido - ignorar
          console.warn('Erro ao processar proposta:', docError);
        }
      }
      
// Aplicar filtros que não podem ser feitos no Firestore - Fase 1
      let filteredProposals = proposals;
      
      // Filtro por status (quando há mais de 10 status)
      if (filters.status && filters.status.length > 10) {
        filteredProposals = filteredProposals.filter(proposal => 
          filters.status!.includes(proposal.status)
        );
      }
      
      // Filtro por priority (quando há mais de 10 prioridades)
      if (filters.priority && filters.priority.length > 10) {
        filteredProposals = filteredProposals.filter(proposal => 
          filters.priority!.includes(proposal.priority)
        );
      }

      // Aplicar paginação manualmente
      const result = filteredProposals.slice(offset, offset + limit);
      
      return result;
    } catch (error) {
      // Se for erro de índice, dar uma mensagem mais específica
      if (error instanceof Error && error.message.includes('index')) {
        throw new Error('Índices do banco de dados ainda sendo construídos. Tente novamente em alguns minutos.');
      }
      
      throw new Error(`Falha ao buscar propostas: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Buscar proposta por ID
   */
  static async getProposalById(id: string): Promise<Proposal | null> {
    try {
      console.log(`🔍 [ProposalService] Buscando proposta por ID: ${id}`);
      
      if (!id || id.trim() === '') {
        console.warn(`⚠️ [ProposalService] ID inválido fornecido: "${id}"`);
        return null;
      }

      const docRef = doc(db, this.COLLECTION_NAME, id.trim());
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        console.warn(`❌ [ProposalService] Proposta não encontrada: ${id}`);
        return null;
      }

      const data = docSnap.data();
      console.log(`✅ [ProposalService] Proposta encontrada: ${id} (${data?.nome || 'sem nome'})`);
      
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        sentAt: data.sentAt?.toDate(),
        icon: data.icon // ✅ CORRIGIDO: Garantir que icon está incluído
      } as Proposal;
    } catch (error) {
      console.error(`❌ [ProposalService] Erro ao buscar proposta ${id}:`, {
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        stack: error instanceof Error ? error.stack : undefined,
        id
      });
      throw new Error(`Falha ao buscar proposta ${id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Atualizar proposta
   */
  static async updateProposal(id: string, updates: Partial<Proposal>): Promise<void> {
    try {
const docRef = doc(db, this.COLLECTION_NAME, id);
      const updateData = {
        ...updates,
        updatedAt: Timestamp.fromDate(new Date())
      };      await updateDoc(docRef, updateData);
} catch (error) {
      throw new Error(`Falha ao atualizar proposta: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Deletar proposta
   */
  static async deleteProposal(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await deleteDoc(docRef);
    } catch (error) {
      throw new Error('Falha ao deletar proposta');
    }
  }

  /**
   * Enviar proposta
   */
  static async sendProposal(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await updateDoc(docRef, {
        status: 'sent',
        sentAt: Timestamp.fromDate(new Date()),
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      throw new Error('Falha ao enviar proposta');
    }
  }

  /**
   * Aceitar proposta
   */
  static async acceptProposal(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await updateDoc(docRef, {
        status: 'accepted',
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      throw new Error('Falha ao aceitar proposta');
    }
  }

  /**
   * Rejeitar proposta
   */
  static async rejectProposal(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await updateDoc(docRef, {
        status: 'rejected',
        updatedAt: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      throw new Error('Falha ao rejeitar proposta');
    }
  }

  /**
   * Buscar propostas por marca
   */
  static async getProposalsByBrand(brandId: string): Promise<Proposal[]> {
    return this.getProposals({ brandId });
  }

  /**
   * Buscar todas as propostas da coleção (versão simplificada para evitar índices)
   */
  static async getAllUserProposalsSimple(
    userId: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<Proposal[]> {
    try {
      if (!userId) {
        return [];
      }

      // 🔥 MÉTODO SIMPLIFICADO: Buscar apenas por brandId primeiro
      // Se não encontrar nada, buscar por criadoPor
      // Isso evita completamente a necessidade de índices compostos
      
      let queryRef = collection(db, this.COLLECTION_NAME) as Query;

      // Primeira tentativa: buscar por brandId (caso mais comum)
const brandIdQuery = query(
        queryRef,
        where('brandId', '==', userId),
        firestoreLimit(limit + offset)
      );

      const brandIdSnapshot = await getDocs(brandIdQuery);
let allProposals: Proposal[] = [];
      
      // ✅ CORREÇÃO: Processar propostas da marca buscando influenciadores da subcoleção
      for (const doc of brandIdSnapshot.docs) {
        try {
          const data = doc.data() as DocumentData;
          
          // Buscar influenciadores da subcoleção
          const influencersFromSubcollection = await this.getProposalInfluencers(doc.id);
          
          // Criar array de influenciadores no formato correto
          const influencers: InfluencerInProposal[] = influencersFromSubcollection.map((inf: any) => ({
            id: inf.influencerId,
            name: inf.influencerId, // Vai ser resolvido depois com os dados reais
            email: '',
            avatar: '',
            tier: '',
            followers: 0,
            engagement: 0
          }));

          allProposals.push({
            id: doc.id,
            nome: data?.nome || '',
            descricao: data?.descricao,
            criadoPor: data?.criadoPor || '',
            influencers: influencers, // ✅ CORREÇÃO: Usar influenciadores da subcoleção
            brandId: data?.brandId || '',
            services: data?.services || [],
            totalAmount: data?.totalAmount || 0,
            dataEnvio: data?.dataEnvio || '',
            grupo: data?.grupo,
            priority: data?.priority || 'medium',
            status: data?.status || 'draft',
            createdAt: data?.createdAt?.toDate() || new Date(),
            updatedAt: data?.updatedAt?.toDate() || new Date(),
            sentAt: data?.sentAt?.toDate(),
            icon: data?.icon // ✅ CORRIGIDO: Incluir campo icon
          } as Proposal);
        } catch (docError) {
          // Documento inválido - ignorar
          console.warn('Erro ao processar proposta da marca:', docError);
        }
      }

      // Se não encontrou muitas propostas por brandId, buscar também por criadoPor
      if (allProposals.length < 5) {
const createdByQuery = query(
          queryRef,
          where('criadoPor', '==', userId),
          firestoreLimit(limit)
        );

        const createdBySnapshot = await getDocs(createdByQuery);
const addedIds = new Set(allProposals.map(p => p.id));
        
        // ✅ CORREÇÃO: Adicionar propostas criadas pelo usuário buscando influenciadores da subcoleção
        for (const doc of createdBySnapshot.docs) {
          if (!addedIds.has(doc.id)) {
            try {
              const data = doc.data() as DocumentData;
              
              // Buscar influenciadores da subcoleção
              const influencersFromSubcollection = await this.getProposalInfluencers(doc.id);
              
              // Criar array de influenciadores no formato correto
              const influencers: InfluencerInProposal[] = influencersFromSubcollection.map((inf: any) => ({
                id: inf.influencerId,
                name: inf.influencerId, // Vai ser resolvido depois com os dados reais
                email: '',
                avatar: '',
                tier: '',
                followers: 0,
                engagement: 0
              }));

              allProposals.push({
                id: doc.id,
                nome: data?.nome || '',
                descricao: data?.descricao,
                criadoPor: data?.criadoPor || '',
                influencers: influencers, // ✅ CORREÇÃO: Usar influenciadores da subcoleção
                brandId: data?.brandId || '',
                services: data?.services || [],
                totalAmount: data?.totalAmount || 0,
                dataEnvio: data?.dataEnvio || '',
                grupo: data?.grupo,
                priority: data?.priority || 'medium',
                status: data?.status || 'draft',
                createdAt: data?.createdAt?.toDate() || new Date(),
                updatedAt: data?.updatedAt?.toDate() || new Date(),
                sentAt: data?.sentAt?.toDate(),
                icon: data?.icon // ✅ CORRIGIDO: Incluir campo icon
              } as Proposal);
            } catch (docError) {
              // Documento inválido - ignorar
              console.warn('Erro ao processar proposta criada pelo usuário:', docError);
            }
          }
        }
      }

      // Ordenar no código (sem necessidade de índices)
      allProposals.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      // Aplicar paginação
      const result = allProposals.slice(offset, offset + limit);
      
      return result;
    } catch (error) {
      throw new Error(`Falha ao buscar todas as propostas: ${error instanceof Error ? error.message : String(error)}`);
    }
  }




  /**
   * Verifica se usuário tem acesso à proposta (apenas ownership - para compatibilidade)
   * NOTA: Para membros convidados, verificação deve ser feita nas APIs server-side
   */
  static async canUserAccessProposal(proposalId: string, userId: string): Promise<boolean> {
    try {
      // Buscar proposta
      const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
      const proposalDoc = await getDoc(proposalRef);
      
      if (!proposalDoc.exists()) {
return false;
      }

      const proposalData = proposalDoc.data();
      
      // Verificar se é proprietário (criador ou brandId)
      const isOwner = proposalData.criadoPor === userId || proposalData.brandId === userId;

      if (isOwner) {
return true;
      }
      return false;
      
    } catch (error) {
      return false;
    }
  }

  /**
   * Buscar propostas por influenciador - Função desabilitada temporariamente
   */
  // static async getProposalsByInfluencer(influencerId: string): Promise<Proposal[]> {
  //   return this.getProposals({ influencerId });
  // }

  /**
   * Buscar propostas por grupo
   */
  static async getProposalsByGroup(grupo: string, brandId?: string): Promise<Proposal[]> {
    const filters: ProposalFilters = { grupo };
    if (brandId) {
      filters.brandId = brandId;
    }
    return this.getProposals(filters);
  }

  /**
   * Calcular estatísticas básicas
   */
  static async getBasicStats(brandId?: string): Promise<{
    total: number;
    sent: number;
    accepted: number;
    rejected: number;
    totalValue: number;
  }> {
    try {
      const filters: ProposalFilters = {};
      if (brandId) {
        filters.brandId = brandId;
      }

      const proposals = await this.getProposals(filters, 1000);
      
      const stats = {
        total: proposals.length,
        sent: proposals.filter(p => p.status === 'sent').length,
        accepted: proposals.filter(p => p.status === 'accepted').length,
        rejected: proposals.filter(p => p.status === 'rejected').length,
        totalValue: proposals.reduce((sum, p) => sum + p.totalAmount, 0)
      };

      return stats;
    } catch (error) {
      throw new Error('Falha ao calcular estatísticas');
    }
  }

  /**
   * Obter analytics avançados - Fase 1
   */
  static async getAnalytics(
    filters: ProposalFilters = {},
    userId?: string
  ): Promise<any> {
    try {
      const proposals = await this.getProposals(filters, 1000, 0, userId);
      
      // Calcular métricas por mês
      const monthlyData: { [key: string]: any } = {};
      
      proposals.forEach(proposal => {
        const month = proposal.createdAt.toISOString().substring(0, 7); // YYYY-MM
        
        if (!monthlyData[month]) {
          monthlyData[month] = {
            month,
            total: 0,
            sent: 0,
            accepted: 0,
            rejected: 0,
            totalValue: 0,
            averageValue: 0
          };
        }
        
        monthlyData[month].total++;
        monthlyData[month].totalValue += proposal.totalAmount;
        
        if (proposal.status === 'sent') monthlyData[month].sent++;
        if (proposal.status === 'accepted') monthlyData[month].accepted++;
        if (proposal.status === 'rejected') monthlyData[month].rejected++;
      });
      
      // Calcular médias
      Object.values(monthlyData).forEach((data: any) => {
        data.averageValue = data.total > 0 ? Math.round(data.totalValue / data.total) : 0;
      });
      
      // Performance por marca (top 5)
      const brandPerformance: { [key: string]: any } = {};
      
      proposals.forEach(proposal => {
        if (!brandPerformance[proposal.brandId]) {
          brandPerformance[proposal.brandId] = {
            brandId: proposal.brandId,
            brandName: `Marca ${proposal.brandId.substring(0, 8)}`,
            total: 0,
            accepted: 0,
            totalValue: 0,
            acceptanceRate: 0
          };
        }
        
        brandPerformance[proposal.brandId].total++;
        brandPerformance[proposal.brandId].totalValue += proposal.totalAmount;
        
        if (proposal.status === 'accepted') {
          brandPerformance[proposal.brandId].accepted++;
        }
      });
      
      // Calcular taxa de aceitação por marca
      Object.values(brandPerformance).forEach((brand: any) => {
        brand.acceptanceRate = brand.total > 0 ? Math.round((brand.accepted / brand.total) * 100) : 0;
      });
      
      // Top 5 marcas por receita
      const topBrands = Object.values(brandPerformance)
        .sort((a: any, b: any) => b.totalValue - a.totalValue)
        .slice(0, 5);
      
      // Performance por influenciador (top 5)
      const influencerPerformance: { [key: string]: any } = {};
      
      proposals.forEach(proposal => {
        // Iterar sobre todos os influencers da proposta
        proposal.influencers?.forEach(influencer => {
          if (!influencerPerformance[influencer.id]) {
            influencerPerformance[influencer.id] = {
              influencerId: influencer.id,
              influencerName: influencer.name || `Influencer ${influencer.id.substring(0, 8)}`,
              total: 0,
              accepted: 0,
              totalValue: 0,
              acceptanceRate: 0
            };
          }
          
          influencerPerformance[influencer.id].total++;
          // Dividir valor da proposta pelo número de influencers
          const sharePerInfluencer = proposal.totalAmount / (proposal.influencers?.length || 1);
          influencerPerformance[influencer.id].totalValue += sharePerInfluencer;
          
          if (proposal.status === 'accepted') {
            influencerPerformance[influencer.id].accepted++;
          }
        });
      });
      
      // Calcular taxa de aceitação por influenciador
      Object.values(influencerPerformance).forEach((influencer: any) => {
        influencer.acceptanceRate = influencer.total > 0 ? Math.round((influencer.accepted / influencer.total) * 100) : 0;
      });
      
      // Top 5 influenciadores por receita
      const topInfluencers = Object.values(influencerPerformance)
        .sort((a: any, b: any) => b.totalValue - a.totalValue)
        .slice(0, 5);
      
      return {
        monthlyData: Object.values(monthlyData).sort((a: any, b: any) => a.month.localeCompare(b.month)),
        brandPerformance: topBrands,
        influencerPerformance: topInfluencers,
        totalProposals: proposals.length,
        totalValue: proposals.reduce((sum, p) => sum + p.totalAmount, 0),
        averageValue: proposals.length > 0 ? Math.round(proposals.reduce((sum, p) => sum + p.totalAmount, 0) / proposals.length) : 0,
        acceptanceRate: proposals.length > 0 ? Math.round((proposals.filter(p => p.status === 'accepted').length / proposals.length) * 100) : 0
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Criar compartilhamento público de proposta
   */
  static async createProposalSharing(token: string, sharingData: any): Promise<void> {
    try {
      const docRef = doc(db, 'proposal_sharings', token);
      await updateDoc(docRef, {
        ...sharingData,
        createdAt: Timestamp.fromDate(sharingData.createdAt),
        expiresAt: Timestamp.fromDate(sharingData.expiresAt)
      });
    } catch (error) {
      // Se documento não existe, usar setDoc para criar um novo com ID específico
      const docRef = doc(db, 'proposal_sharings', token);
      await setDoc(docRef, {
        ...sharingData,
        createdAt: Timestamp.fromDate(sharingData.createdAt),
        expiresAt: Timestamp.fromDate(sharingData.expiresAt)
      });
    }
  }

  /**
   * Buscar compartilhamentos de uma proposta
   */
  static async getProposalSharings(proposalId: string, userId: string): Promise<any[]> {
    try {
      const q = query(
        collection(db, 'proposal_sharings'), 
        where('proposalId', '==', proposalId),
        where('createdBy', '==', userId),
        where('isActive', '==', true)
      );
      
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
        createdAt: doc.data().createdAt?.toDate(),
        expiresAt: doc.data().expiresAt?.toDate(),
        lastAccessed: doc.data().lastAccessed?.toDate()
      }));
    } catch (error) {
      return [];
    }
  }

  /**
   * Desativar compartilhamento
   */
  static async deactivateProposalSharing(token: string, userId: string): Promise<void> {
    try {
      const docRef = doc(db, 'proposal_sharings', token);
      await updateDoc(docRef, {
        isActive: false,
        deactivatedAt: Timestamp.fromDate(new Date()),
        deactivatedBy: userId
      });
    } catch (error) {
      throw new Error('Falha ao desativar compartilhamento');
    }
  }

  /**
   * Buscar compartilhamento por token
   */
  static async getProposalSharingByToken(token: string): Promise<any | null> {
    try {
      const docRef = doc(db, 'proposal_sharings', token);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      const data = docSnap.data();
      return {
        ...data,
        id: docSnap.id,
        createdAt: data.createdAt?.toDate(),
        expiresAt: data.expiresAt?.toDate(),
        lastAccessed: data.lastAccessed?.toDate()
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Atualizar contador de acesso do compartilhamento
   */
  static async updateSharingAccess(token: string): Promise<void> {
    try {
      const docRef = doc(db, 'proposal_sharings', token);
      await updateDoc(docRef, {
        accessCount: (await getDoc(docRef)).data()?.accessCount + 1 || 1,
        lastAccessed: Timestamp.fromDate(new Date())
      });
    } catch (error) {
      // Erro silencioso - não é crítico
    }
  }

  /**
   * Adiciona influenciadores à subcoleção da proposta
   */
  static async addInfluencersToProposal(
    proposalId: string, 
    influencerIds: string[], 
    addedBy: string
  ): Promise<void> {
// ✅ Validações robustas de entrada
    if (!proposalId || typeof proposalId !== 'string') {
      throw new Error(`ProposalId inválido: ${proposalId}`);
    }

    if (!influencerIds || !Array.isArray(influencerIds)) {
      throw new Error(`InfluencerIds deve ser um array: ${influencerIds}`);
    }

    if (!addedBy || typeof addedBy !== 'string') {
      throw new Error(`AddedBy inválido: ${addedBy}`);
    }

    // ✅ Filtrar IDs válidos (remover undefined, null, vazios)
    const validInfluencerIds = influencerIds.filter(id => 
      id && typeof id === 'string' && id.trim().length > 0
    );

    if (validInfluencerIds.length === 0) {
      throw new Error('Nenhum ID de influenciador válido encontrado');
    }
try {
      const batch = writeBatch(db);
      const timestamp = new Date();

      // ✅ Para cada influenciador válido, criar um documento na subcoleção
      validInfluencerIds.forEach((influencerId, index) => {
try {
          const influencerRef = doc(
            db, 
            this.COLLECTION_NAME, 
            proposalId, 
            'influencers', 
            influencerId
          );
batch.set(influencerRef, {
            influencerId,
            status: 'pendente',
            comments: '',
            budgets: {},
            addedAt: timestamp,
            addedBy,
            updatedAt: timestamp
          });
} catch (docError) {
          throw new Error(`Erro ao criar referência para influenciador ${influencerId}: ${docError}`);
        }
      });

      // ✅ Atualizar contador na proposta principal
try {
        const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
batch.update(proposalRef, {
          influencerCount: increment(validInfluencerIds.length),
          updatedAt: timestamp
        });
} catch (proposalRefError) {
        throw new Error(`Erro ao criar referência da proposta principal: ${proposalRefError}`);
      }

      // ✅ Commit do batch
      await batch.commit();} catch (error) {
      throw new Error(`Erro ao adicionar influenciadores à proposta: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Remove influenciador da subcoleção da proposta
   */
  static async removeInfluencerFromProposal(
    proposalId: string, 
    influencerId: string
  ): Promise<void> {
    const batch = writeBatch(db);
    const timestamp = new Date();

    // Remover documento da subcoleção
    const influencerRef = doc(
      db, 
      this.COLLECTION_NAME, 
      proposalId, 
      'influencers', 
      influencerId
    );
    batch.delete(influencerRef);

    // Atualizar contador na proposta principal
    const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
    batch.update(proposalRef, {
      influencerCount: increment(-1),
      updatedAt: timestamp
    });

    await batch.commit();
  }

  /**
   * Lista todos os influenciadores de uma proposta
   */
  static async getProposalInfluencers(proposalId: string): Promise<any[]> {
    const influencersRef = collection(db, this.COLLECTION_NAME, proposalId, 'influencers');
    const snapshot = await getDocs(influencersRef);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  /**
   * Atualiza status de um influenciador na proposta
   */
  static async updateInfluencerStatus(
    proposalId: string,
    influencerId: string,
    status: 'pendente' | 'aceito' | 'rejeitado' | 'descartado',
    updatedBy: string
  ): Promise<void> {
    const influencerRef = doc(
      db, 
      this.COLLECTION_NAME, 
      proposalId, 
      'influencers', 
      influencerId
    );

    await updateDoc(influencerRef, {
      status,
      updatedAt: new Date(),
      updatedBy
    });

    // Também atualizar a proposta principal
    const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
    await updateDoc(proposalRef, {
      updatedAt: new Date()
    });
  }

  /**
   * Atualiza comentários de um influenciador na proposta
   */
  static async updateInfluencerComments(
    proposalId: string,
    influencerId: string,
    comments: string,
    updatedBy: string
  ): Promise<void> {
    const influencerRef = doc(
      db, 
      this.COLLECTION_NAME, 
      proposalId, 
      'influencers', 
      influencerId
    );

    await updateDoc(influencerRef, {
      comments,
      updatedAt: new Date(),
      updatedBy
    });
  }

  /**
   * Atualiza orçamentos de um influenciador na proposta
   */
  static async updateInfluencerBudgets(
    proposalId: string,
    influencerId: string,
    budgets: { [service: string]: { originalPrice: number; budgetedPrice: number } },
    updatedBy: string
  ): Promise<void> {
    const influencerRef = doc(
      db, 
      this.COLLECTION_NAME, 
      proposalId, 
      'influencers', 
      influencerId
    );

    await updateDoc(influencerRef, {
      budgets,
      updatedAt: new Date(),
      updatedBy
    });

  
  }

  /**
   * Recalcula o valor total da proposta baseado nos orçamentos
   */
  static async recalculateProposalTotal(proposalId: string): Promise<void> {
try {
      const influencersRef = collection(db, this.COLLECTION_NAME, proposalId, 'influencers');
      const influencersSnapshot = await getDocs(influencersRef);
let totalAmount = 0;
      let budgetCount = 0;
      const budgetDetails: any[] = [];
      
      // Para cada influenciador, buscar budgets na subcoleção
      for (const influencerDoc of influencersSnapshot.docs) {
        const influencerId = influencerDoc.id;
// 🔥 CORREÇÃO: Buscar budgets ORDENADOS por data de atualização (mais recente primeiro)
        const budgetsRef = collection(db, this.COLLECTION_NAME, proposalId, 'influencers', influencerId, 'budgets');
        const q = query(budgetsRef, orderBy('updatedAt', 'desc'));
        const budgetsSnapshot = await getDocs(q);
// 🔥 DEDUPLICAÇÃO: Manter apenas o budget mais recente por serviceType
        const uniqueBudgets = new Map<string, any>();
        
        budgetsSnapshot.docs.forEach(budgetDoc => {
          const budgetData = budgetDoc.data();
          const serviceType = budgetData.serviceType || 'personalizado';
          
          // Verificar se já temos esse serviço, se não ou se este é mais recente, mantém
          if (!uniqueBudgets.has(serviceType)) {
            uniqueBudgets.set(serviceType, {
              id: budgetDoc.id,
              data: budgetData,
              serviceType
            });
          } else {
            // Budget já existe e é mais antigo - ignorar
          }
        });
        
        // Somar valores dos budgets únicos
        uniqueBudgets.forEach(budget => {
          const budgetData = budget.data;
          
          // Usar 'amount' (campo principal) ou 'budgetedPrice' (se houver negociação)
          const amount = budgetData.budgetedPrice || budgetData.amount || 0;
          
          if (amount > 0) {
            totalAmount += amount;
            budgetCount++;
            
            const budgetDetail = {
              influencerId,
              budgetId: budget.id,
              serviceType: budget.serviceType,
              amount,
              updatedAt: budgetData.updatedAt?.toDate?.()?.toISOString() || budgetData.updatedAt
            };
            
            budgetDetails.push(budgetDetail);
          } else {
            // Valor zero ou inválido - ignorar
          }
        });
      }

      // 🔥 VERIFICAR SE HOUVE MUDANÇA: Buscar valor atual antes de atualizar
      const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
      const proposalSnap = await getDoc(proposalRef);
      const currentTotal = proposalSnap.data()?.totalAmount || 0;
      
      if (currentTotal !== totalAmount) {
        // Atualizar total na proposta principal
        await updateDoc(proposalRef, {
          totalAmount,
          lastCalculatedAt: new Date(),
          budgetCount,
          updatedAt: new Date()
        });
      }
      
    } catch (error) {
      throw error;
    }
  }

  /**
   * Obtém dados de um influenciador específico na proposta
   */
  static async getProposalInfluencer(
    proposalId: string, 
    influencerId: string
  ): Promise<any | null> {
    const influencerRef = doc(
      db, 
      this.COLLECTION_NAME, 
      proposalId, 
      'influencers', 
      influencerId
    );
    
    const snapshot = await getDoc(influencerRef);
    
    if (snapshot.exists()) {
      return {
        id: snapshot.id,
        ...snapshot.data()
      };
    }
    
    return null;
  }

  /**
   * Migra proposta existente do formato antigo (array) para subcoleção
   */
  static async migrateProposalToSubcollection(
    proposalId: string,
    currentUserId: string
  ): Promise<void> {
    // Buscar proposta atual
    const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
    const proposalSnap = await getDoc(proposalRef);
    
    if (!proposalSnap.exists()) {
      throw new Error('Proposta não encontrada');
    }

    const proposalData = proposalSnap.data();
    const influencerIds = proposalData.influencerIds || [];
    const perfisStatus = proposalData.perfisStatus || {};
    const perfisComments = proposalData.perfisComments || {};
    const budgets = proposalData.budgets || {};

    if (influencerIds.length === 0) return;

    const batch = writeBatch(db);
    const timestamp = new Date();

    // Criar documentos na subcoleção para cada influenciador
    influencerIds.forEach((influencerId: string) => {
      const influencerRef = doc(
        db, 
        this.COLLECTION_NAME, 
        proposalId, 
        'influencers', 
        influencerId
      );

      batch.set(influencerRef, {
        influencerId,
        status: perfisStatus[influencerId] || 'pendente',
        comments: perfisComments[influencerId] || '',
      
        addedAt: proposalData.createdAt || timestamp,
        addedBy: proposalData.criadoPor || currentUserId,
        updatedAt: timestamp,
        // Migração flag
        migratedFrom: 'array',
        migratedAt: timestamp
      });
    });

    // Atualizar documento principal (remover arrays antigos)
    batch.update(proposalRef, {
      influencerCount: influencerIds.length,
      updatedAt: timestamp,
      // Manter dados antigos por segurança, mas marcar como migrados
      _migrated: true,
      _migratedAt: timestamp,
      // Opcional: remover arrays antigos depois de confirmar que tudo funciona
      // influencerIds: deleteField(),
      // perfisStatus: deleteField(),
      // perfisComments: deleteField()
    });

    await batch.commit();
  }

  /**
   * Atualiza orçamentos completos de uma proposta (para contrapropostas de convidados)
   */
  static async updateProposalBudgets(
    proposalId: string,
    budgets: any,
    updatedBy: string
  ): Promise<void> {
    try {
      const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
      
      await updateDoc(proposalRef, {
        budgets,
        updatedAt: new Date(),
        updatedBy
      });
// 🔥 Recalcular total após atualizar orçamentos
      await this.recalculateProposalTotal(proposalId);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 🔥 NOVA FUNÇÃO: Força recálculo manual do total de uma proposta
   * Útil para corrigir inconsistências ou após operações manuais
   */
  static async forceRecalculateProposalTotal(proposalId: string): Promise<{
    success: boolean;
    oldTotal: number;
    newTotal: number;
    budgetCount: number;
    message: string;
  }> {
    try {
// Buscar total atual
      const proposalRef = doc(db, this.COLLECTION_NAME, proposalId);
      const proposalSnap = await getDoc(proposalRef);
      
      if (!proposalSnap.exists()) {
        throw new Error('Proposta não encontrada');
      }
      
      const oldTotal = proposalSnap.data()?.totalAmount || 0;
// Executar recálculo
      await this.recalculateProposalTotal(proposalId);
      
      // Buscar total atualizado
      const updatedProposalSnap = await getDoc(proposalRef);
      const newTotal = updatedProposalSnap.data()?.totalAmount || 0;
      const budgetCount = updatedProposalSnap.data()?.budgetCount || 0;
      
      const result = {
        success: true,
        oldTotal,
        newTotal,
        budgetCount,
        message: oldTotal !== newTotal 
          ? `Total atualizado de R$ ${oldTotal.toLocaleString('pt-BR')} para R$ ${newTotal.toLocaleString('pt-BR')}`
          : `Total já estava correto: R$ ${newTotal.toLocaleString('pt-BR')}`
      };
      return result;
      
    } catch (error) {
      return {
        success: false,
        oldTotal: 0,
        newTotal: 0,
        budgetCount: 0,
        message: `Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }

  /**
   * 🔥 NOVA FUNÇÃO: Recalcula totais de TODAS as propostas de um usuário
   * Útil para corrigir inconsistências em massa
   */
  static async recalculateAllUserProposalTotals(userId: string): Promise<{
    success: boolean;
    processedCount: number;
    updatedCount: number;
    results: Array<{
      proposalId: string;
      proposalName: string;
      oldTotal: number;
      newTotal: number;
      changed: boolean;
    }>;
  }> {
    try {
// Buscar todas as propostas do usuário
      const proposals = await this.getAllUserProposalsSimple(userId, 1000);
const results = [];
      let updatedCount = 0;
      
      for (const proposal of proposals) {
        try {
          const recalcResult = await this.forceRecalculateProposalTotal(proposal.id);
          const changed = recalcResult.oldTotal !== recalcResult.newTotal;
          
          if (changed) updatedCount++;
          
          results.push({
            proposalId: proposal.id,
            proposalName: proposal.nome,
            oldTotal: recalcResult.oldTotal,
            newTotal: recalcResult.newTotal,
            changed
          });
// Pequeno delay para não sobrecarregar o Firebase
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (proposalError) {
          results.push({
            proposalId: proposal.id,
            proposalName: proposal.nome,
            oldTotal: 0,
            newTotal: 0,
            changed: false
          });
        }
      }
return {
        success: true,
        processedCount: proposals.length,
        updatedCount,
        results
      };
      
    } catch (error) {
      return {
        success: false,
        processedCount: 0,
        updatedCount: 0,
        results: []
      };
    }
  }
}

