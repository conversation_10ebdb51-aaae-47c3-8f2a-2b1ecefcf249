import { NextRequest, NextResponse } from 'next/server';
import { logCSPViolation } from '@/lib/csp-config';

/**
 * 🚨 API para receber relatórios de violação de Content Security Policy
 * 
 * Esta API é chamada automaticamente pelo navegador quando uma violação de CSP ocorre.
 * Útil para monitorar e debuggar problemas de segurança em produção.
 */
export async function POST(request: NextRequest) {
  try {
    const contentType = request.headers.get('content-type');
    
    // CSP reports são enviados como JSON
    if (!contentType?.includes('application/json')) {
      return NextResponse.json({ error: 'Invalid content type' }, { status: 400 });
    }
    
    const body = await request.json();
    const cspReport = body['csp-report'] || body;
    
    // Extrair informações relevantes do relatório
    const violationInfo = {
      timestamp: new Date().toISOString(),
      documentUri: cspReport['document-uri'] || 'unknown',
      violatedDirective: cspReport['violated-directive'] || 'unknown',
      blockedUri: cspReport['blocked-uri'] || 'unknown',
      effectiveDirective: cspReport['effective-directive'] || 'unknown',
      originalPolicy: cspReport['original-policy'] || 'unknown',
      sourceFile: cspReport['source-file'] || 'unknown',
      lineNumber: cspReport['line-number'] || 'unknown',
      columnNumber: cspReport['column-number'] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      ip: request.headers.get('x-forwarded-for')?.split(',')[0] || 
          request.headers.get('x-real-ip') || 'unknown'
    };
    
    // Log da violação
    logCSPViolation('CSP Violation Detected', violationInfo);
    
    // Em produção, você pode querer salvar isso em um banco de dados
    // ou enviar para um serviço de monitoramento como Sentry
    if (process.env.NODE_ENV === 'production') {
      console.error('🚨 CSP VIOLATION:', JSON.stringify(violationInfo, null, 2));
      
      // Opcional: Integração com serviços de monitoramento
      // await sendToMonitoringService(violationInfo);
    }
    
    return NextResponse.json({ status: 'received' }, { status: 200 });
  } catch (error) {
    console.error('❌ Erro ao processar relatório de CSP:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Também aceitar GET para debugging
export async function GET() {
  return NextResponse.json({
    message: 'CSP Violation Reporting Endpoint',
    status: 'active',
    environment: process.env.NODE_ENV
  });
} 

