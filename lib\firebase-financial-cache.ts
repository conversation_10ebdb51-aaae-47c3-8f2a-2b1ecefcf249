// SISTEMA DE CACHE ESTRATÉGICO PARA DADOS FINANCEIROS
// Implementação de cache com TTL, invalidação e estratégias de otimização

import { InfluencerFinancial } from '../types/influencer-financial';
import { getFinancialByInfluencerId } from './firebase-financials';

// Configuração do cache
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos
const MAX_CACHE_SIZE = 1000; // Máximo 1000 itens no cache
const BATCH_SIZE = 10; // Tamanho do lote para pré-cache

// Interfaces para o sistema de cache
interface CacheEntry {
  data: InfluencerFinancial | null;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  size: number;
}

// Cache global para dados financeiros
class FinancialCache {
  private cache: Map<string, CacheEntry> = new Map();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    size: 0
  };

  /**
   * Busca dados do cache ou do Firebase
   */
  async get(influencerId: string): Promise<InfluencerFinancial | null> {
    const cacheKey = `financial_${influencerId}`;
    const now = Date.now();

    // Verificar se existe no cache e se não expirou
    const cached = this.cache.get(cacheKey);
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      // Atualizar estatísticas de acesso
      cached.accessCount++;
      cached.lastAccessed = now;
      this.stats.hits++;
      
      console.log(`🎯 Cache HIT para influenciador ${influencerId}`);
      return cached.data;
    }

    // Cache miss - buscar no Firebase
    this.stats.misses++;
    console.log(`🔍 Cache MISS para influenciador ${influencerId} - buscando no Firebase`);

    try {
      const data = await getFinancialByInfluencerId(influencerId);
      
      // Salvar no cache
      this.set(cacheKey, data);
      
      return data;
    } catch (error) {
      console.error(`Erro ao buscar dados financeiros para ${influencerId}:`, error);
      return null;
    }
  }

  /**
   * Adiciona ou atualiza item no cache
   */
  set(key: string, data: InfluencerFinancial | null): void {
    const now = Date.now();

    // Verificar se precisa limpar cache
    if (this.cache.size >= MAX_CACHE_SIZE) {
      this.evictLeastUsed();
    }

    // Adicionar ao cache
    this.cache.set(key, {
      data,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now
    });

    this.stats.size = this.cache.size;
    console.log(`💾 Cache SET para ${key} - Tamanho atual: ${this.cache.size}`);
  }

  /**
   * Remove item específico do cache
   */
  invalidate(influencerId: string): void {
    const cacheKey = `financial_${influencerId}`;
    const deleted = this.cache.delete(cacheKey);
    
    if (deleted) {
      this.stats.size = this.cache.size;
      console.log(`🗑️ Cache INVALIDATED para ${cacheKey}`);
    }
  }

  /**
   * Pré-carrega dados financeiros para uma lista de influenciadores
   */
  async preload(influencerIds: string[]): Promise<void> {
    console.log(`🚀 Pré-carregando dados financeiros para ${influencerIds.length} influenciadores`);
    
    // Processar em lotes para não sobrecarregar o Firebase
    for (let i = 0; i < influencerIds.length; i += BATCH_SIZE) {
      const batch = influencerIds.slice(i, i + BATCH_SIZE);
      
      // Processar lote em paralelo
      const promises = batch.map(async (influencerId) => {
        const cacheKey = `financial_${influencerId}`;
        
        // Só pré-carregar se não estiver no cache
        if (!this.cache.has(cacheKey)) {
          try {
            const data = await getFinancialByInfluencerId(influencerId);
            this.set(cacheKey, data);
          } catch (error) {
            console.error(`Erro ao pré-carregar dados financeiros para ${influencerId}:`, error);
          }
        }
      });

      await Promise.all(promises);
      
      // Pequeno delay entre lotes para não sobrecarregar
      if (i + BATCH_SIZE < influencerIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log(`✅ Pré-carregamento concluído. Cache atual: ${this.cache.size} itens`);
  }

  /**
   * Remove itens menos utilizados do cache
   */
  private evictLeastUsed(): void {
    const entries = Array.from(this.cache.entries());
    
    // Ordenar por accessCount (menos usado primeiro)
    entries.sort((a, b) => {
      // Considerar tanto frequência quanto recência
      const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].lastAccessed) * 0.3;
      const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].lastAccessed) * 0.3;
      return scoreB - scoreA; // Maior score = mais usado
    });

    // Remover 25% dos itens menos utilizados
    const toRemove = Math.floor(entries.length * 0.25);
    for (let i = entries.length - toRemove; i < entries.length; i++) {
      this.cache.delete(entries[i][0]);
      this.stats.evictions++;
    }

    this.stats.size = this.cache.size;
    console.log(`🧹 Cache cleanup: removidos ${toRemove} itens. Tamanho atual: ${this.cache.size}`);
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      size: 0
    };
    console.log('🧹 Cache completamente limpo');
  }

  /**
   * Retorna estatísticas do cache
   */
  getStats(): CacheStats & { hitRate: number } {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
    
    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * Retorna informações detalhadas sobre o cache
   */
  getInfo(): {
    stats: CacheStats & { hitRate: number };
    cacheSize: number;
    oldestEntry: string | null;
    newestEntry: string | null;
  } {
    const stats = this.getStats();
    let oldestEntry: string | null = null;
    let newestEntry: string | null = null;
    let oldestTime = Date.now();
    let newestTime = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestEntry = key;
      }
      if (entry.timestamp > newestTime) {
        newestTime = entry.timestamp;
        newestEntry = key;
      }
    }

    return {
      stats,
      cacheSize: this.cache.size,
      oldestEntry,
      newestEntry
    };
  }

  /**
   * Remove itens expirados do cache
   */
  cleanExpired(): number {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    
    let expired = 0;
    for (const [key, entry] of entries) {
      if (now - entry.timestamp > CACHE_DURATION) {
        this.cache.delete(key);
        expired++;
      }
    }
    
    if (expired > 0) {
      this.stats.size = this.cache.size;
      console.log(`🧹 Limpeza automática: removidos ${expired} itens expirados do cache`);
    }
    
    return expired;
  }
}

// Instância global do cache
const financialCache = new FinancialCache();

// Funções de cache para usar na aplicação
export const FinancialCacheService = {
  /**
   * Busca dados financeiros com cache
   */
  async getFinancialData(influencerId: string): Promise<InfluencerFinancial | null> {
    return await financialCache.get(influencerId);
  },

  /**
   * Busca dados financeiros para múltiplos influenciadores
   */
  async getMultipleFinancialData(influencerIds: string[]): Promise<Map<string, InfluencerFinancial | null>> {
    const results = new Map<string, InfluencerFinancial | null>();
    
    // Processar em paralelo
    const promises = influencerIds.map(async (id) => {
      const data = await financialCache.get(id);
      results.set(id, data);
    });

    await Promise.all(promises);
    return results;
  },

  /**
   * Invalida cache para um influenciador específico
   */
  invalidateInfluencer(influencerId: string): void {
    financialCache.invalidate(influencerId);
  },

  /**
   * Pré-carrega dados para uma lista de influenciadores
   */
  async preloadFinancialData(influencerIds: string[]): Promise<void> {
    await financialCache.preload(influencerIds);
  },

  /**
   * Limpa todo o cache
   */
  clearCache(): void {
    financialCache.clear();
  },

  /**
   * Retorna estatísticas do cache
   */
  getCacheStats() {
    return financialCache.getStats();
  },

  /**
   * Retorna informações detalhadas do cache
   */
  getCacheInfo() {
    return financialCache.getInfo();
  }
};

// Limpar cache expirado automaticamente a cada 10 minutos
setInterval(() => {
  financialCache.cleanExpired();
}, 10 * 60 * 1000); // 10 minutos

export default FinancialCacheService; 

