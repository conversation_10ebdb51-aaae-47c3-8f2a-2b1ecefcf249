# Script final para limpar console.log dos arquivos principais que estão gerando saída no navegador
param(
    [string[]]$TargetFiles = @(
        "lib/firebase-client.ts",
        "contexts/firebase-auth-context.tsx", 
        "components/add-influencer-form.tsx",
        "components/influencer-grid/index.tsx",
        "app/[userId]/influencers/page.tsx"
    )
)

Write-Host "🧹 Limpeza FINAL de console.log - Removendo saída do navegador..." -ForegroundColor Yellow

$totalRemoved = 0

foreach ($file in $TargetFiles) {
    if (Test-Path $file) {
        Write-Host "📁 Processando: $file" -ForegroundColor Cyan
        
        # Backup
        $backupPath = "$file.backup-final-" + (Get-Date -Format "yyyyMMdd-HHmmss")
        Copy-Item $file $backupPath
        
        # Ler conteúdo
        $content = Get-Content $file -Raw
        $originalSize = $content.Length
        
        # Padrões para remoção completa - mais agressivos
        $patterns = @(
            # Console.log com qualquer conteúdo
            'console\.log\([^)]*\);?\s*',
            'console\.error\([^)]*\);?\s*',
            'console\.warn\([^)]*\);?\s*',
            'console\.info\([^)]*\);?\s*',
            'console\.debug\([^)]*\);?\s*',
            
            # Console multilinha - padrões mais agressivos
            'console\.(log|error|warn|info|debug)\s*\(\s*[\s\S]*?\)\s*;?\s*',
            
            # Linhas inteiras que começam com console
            '^\s*console\.(log|error|warn|info|debug).*$\r?\n?',
            
            # Blocos de console multilinha
            'console\.(log|error|warn|info|debug)\s*\(\s*`[\s\S]*?`\s*\)\s*;?\s*',
            'console\.(log|error|warn|info|debug)\s*\(\s*"[\s\S]*?"\s*\)\s*;?\s*',
            "console\.(log|error|warn|info|debug)\s*\(\s*'[\s\S]*?'\s*\)\s*;?\s*"
        )
        
        $removedInFile = 0
        
        foreach ($pattern in $patterns) {
            $matches = [regex]::Matches($content, $pattern, [System.Text.RegularExpressions.RegexOptions]::Multiline)
            if ($matches.Count -gt 0) {
                $content = [regex]::Replace($content, $pattern, '', [System.Text.RegularExpressions.RegexOptions]::Multiline)
                $removedInFile += $matches.Count
            }
        }
        
        # Limpar linhas vazias excessivas
        $content = [regex]::Replace($content, '\n\s*\n\s*\n', "`n`n", [System.Text.RegularExpressions.RegexOptions]::Multiline)
        
        # Salvar o arquivo limpo
        Set-Content $file -Value $content -NoNewline
        
        $newSize = $content.Length
        $reduction = $originalSize - $newSize
        
        Write-Host "  ✅ $removedInFile logs removidos" -ForegroundColor Green
        Write-Host "  📊 Redução: $reduction bytes" -ForegroundColor Blue
        Write-Host "  💾 Backup: $backupPath" -ForegroundColor DarkGray
        
        $totalRemoved += $removedInFile
    } else {
        Write-Host "  ❌ Arquivo não encontrado: $file" -ForegroundColor Red
    }
}

Write-Host "`n🎉 LIMPEZA CONCLUÍDA!" -ForegroundColor Green
Write-Host "📊 Total de console.log removidos: $totalRemoved" -ForegroundColor Yellow
Write-Host "🔇 O console do navegador deve estar muito mais limpo agora!" -ForegroundColor Cyan 