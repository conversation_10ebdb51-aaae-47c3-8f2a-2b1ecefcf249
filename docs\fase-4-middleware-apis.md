# 📅 FASE 4: Middleware e APIs (2-3 dias)

## 🎯 Objetivo
Garantir que todas as APIs façam isolamento automático por usuário através de middleware atualizado.

## 📝 Tarefas Principais

### 4.1 Middleware de Isolamento Atualizado

#### Middleware Base
```typescript
// lib/middleware/user-isolation.ts
export function withUserIsolation(
  handler: (req: NextRequest, userId: string) => Promise<Response>
) {
  return async (req: NextRequest) => {
    try {
      const authData = await verifyAuth(req);
      
      if (!authData) {
        return NextResponse.json(
          { error: 'Não autenticado' },
          { status: 401 }
        );
      }

      return await handler(req, authData.user.id);
    } catch (error) {
      console.error('Erro no middleware de isolamento:', error);
      return NextResponse.json(
        { error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  };
}
```

### 4.2 Atualização de APIs

#### API de Brands
```typescript
// app/api/brands/route.ts
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const brands = await BrandService.getBrandsByUser(userId);
    return NextResponse.json({ success: true, data: brands });
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao buscar marcas' }, { status: 500 });
  }
});

export const POST = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const data = await validateRequestBody(CreateBrandSchema)(req);
    const brandId = await BrandService.createBrand(data, userId);
    return NextResponse.json({ success: true, data: { id: brandId } });
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao criar marca' }, { status: 500 });
  }
});
```

#### API de Campaigns
```typescript
// app/api/campaigns/route.ts
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(req.url);
    const brandId = searchParams.get('brandId');
    
    // Validar se brandId pertence ao usuário
    if (brandId) {
      await validateBrandOwnership(brandId, userId);
    }
    
    const campaigns = await CampaignService.getCampaignsByUser(userId, brandId);
    return NextResponse.json({ success: true, data: campaigns });
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 400 });
  }
});
```

### 4.3 Serviços Atualizados

#### BrandService
```typescript
// services/brand-service.ts
export class BrandService {
  static async getBrandsByUser(userId: string): Promise<Brand[]> {
    const snapshot = await db
      .collection('brands')
      .where('userId', '==', userId)
      .orderBy('createdAt', 'desc')
      .get();
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Brand[];
  }
  
  static async createBrand(data: CreateBrandData, userId: string): Promise<string> {
    const brandData = IsolationUtils.prepareCreateData(data, userId);
    const docRef = await db.collection('brands').add(brandData);
    return docRef.id;
  }
  
  static async updateBrand(id: string, data: UpdateBrandData, userId: string): Promise<void> {
    const brandRef = db.collection('brands').doc(id);
    const brand = await brandRef.get();
    
    if (!brand.exists) {
      throw new Error('Marca não encontrada');
    }
    
    if (!IsolationUtils.validateOwnership(brand.data(), userId)) {
      throw new Error('Usuário não tem permissão para editar esta marca');
    }
    
    const updateData = IsolationUtils.prepareUpdateData(data, userId);
    await brandRef.update(updateData);
  }
}
```

### 4.4 Validações de Ownership

#### Utilitários de Validação
```typescript
// lib/utils/ownership-validation.ts
export async function validateBrandOwnership(brandId: string, userId: string): Promise<void> {
  const brand = await db.collection('brands').doc(brandId).get();
  
  if (!brand.exists) {
    throw new Error('Marca não encontrada');
  }
  
  if (brand.data()?.userId !== userId) {
    throw new Error('Usuário não tem permissão para acessar esta marca');
  }
}

export async function validateCampaignOwnership(campaignId: string, userId: string): Promise<void> {
  const campaign = await db.collection('campaigns').doc(campaignId).get();
  
  if (!campaign.exists) {
    throw new Error('Campanha não encontrada');
  }
  
  if (campaign.data()?.userId !== userId) {
    throw new Error('Usuário não tem permissão para acessar esta campanha');
  }
}
```

## 📊 Deliverables

### 1. Middleware Atualizado
- [x] `withUserIsolation` implementado
- [x] Validação automática de autenticação
- [x] Injeção automática de userId

### 2. APIs Atualizadas
- [x] `/api/brands` - Com filtro por userId
- [x] `/api/campaigns` - Com filtro por userId  
- [x] `/api/influencers` - Com filtro por userId
- [ ] `/api/groups` - Com filtro por userId
- [ ] `/api/notes` - Com filtro por userId

### 3. Serviços com Isolamento
- [x] BrandService com métodos isolados
- [x] CampaignService com métodos isolados
- [ ] InfluencerService com métodos isolados
- [ ] GroupService com métodos isolados

### 4. Validações de Ownership
- [x] Utilitários para validar posse de recursos
- [x] Middleware para verificação automática
- [x] Tratamento de erros padronizado

## ✅ Critérios de Aceitação

- [ ] Todas as APIs usam middleware de isolamento
- [ ] Filtros automáticos por userId funcionando
- [ ] Validação de ownership implementada
- [ ] Tratamento de erros padronizado
- [ ] Logs de auditoria funcionando
- [ ] Performance adequada

## 🚀 Próxima Fase

Seguir para [Fase 5: Migração de Dados](./fase-5-migracao-dados.md) 