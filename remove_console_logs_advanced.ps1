# Script avançado para remover console.log statements
$filePath = "app\[userId]\influencers\page.tsx"

# Ler conteúdo completo do arquivo
$content = Get-Content $filePath | Out-String

# Remover console statements em múltiplas linhas usando regex mais complexo
$content = $content -replace "(?s)console\.(log|error|warn|info|debug)\s*\([^)]*\)[^;]*;", ""

# Remover linhas vazias em excesso
$content = $content -replace "(?m)^\s*\r?\n", "`n"

# Salvar arquivo
$content | Set-Content $filePath -NoNewline

Write-Host "Console logs avançados removidos de $filePath" 