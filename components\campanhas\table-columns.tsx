import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { motion, AnimatePresence } from "framer-motion";
import { Protect } from "@clerk/nextjs";
import { 
  MapPin, 
  BarChart3, 
  UserCheck, 
  Briefcase, 
  Phone, 
  Mail, 
  FileText,
  Instagram,
  Youtube,
  ChevronDown,
  ChevronRight,
  Edit3,
  Save,
  X,
  DollarSign,
  User
} from "lucide-react";

// Interfaces locais
interface Influencer {
  id: string;
  nome: string;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp?: string; // ✅ Campo opcional (pode não estar no snapshot)
  email?: string;    // ✅ Campo opcional (pode não estar no snapshot)
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
  stories_views?: number | string;
  instagram_reels_views?: number | string;
  youtube_insertion_views?: number | string;
  youtube_long_video_views?: number | string;
  youtube_shorts_views?: number | string;
  tiktok_views?: number | string;
  dadosFinanceiros?: {
    precos?: {
      instagramStory?: { price: number; name?: string };
      instagramReel?: { price: number; name?: string };
      youtubeInsertion?: { price: number; name?: string };
      youtubeDedicated?: { price: number; name?: string };
      youtubeShorts?: { price: number; name?: string };
      tiktokVideo?: { price: number; name?: string };
    };
    responsavel?: string;
    agencia?: string;
    whatsappFinanceiro?: string;
    emailFinanceiro?: string;
  };
}

interface BrandInfluencer {
  id: string;
  brandId: string;
  brandName: string;
  influencerId: string;
  influencerName: string;
  influencerData: Influencer;
  status: 'enviado' | 'visualizado' | 'interessado' | 'rejeitado' | 'proposta_enviada';
  sentAt: Date;
  viewedAt?: Date;
  lastInteractionAt?: Date;
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

interface CustomBudget {
  influencerId: string;
  serviceType: 'instagramStory' | 'instagramReel' | 'youtubeInsertion' | 'youtubeDedicated' | 'youtubeShorts' | 'tiktokVideo';
  customPrice: number;
  notes?: string;
  createdAt: Date;
}

interface TableColumnsProps {
  selectedInfluencers: string[];
  selectionMode: boolean;
  activeTab: string;
  filteredInfluencers: Influencer[];
  handleSelectInfluencer: (id: string) => void;
  formatNumber: (num: number) => string;
  formatCurrency: (value: number) => string;
  getInfluencerStatus: (id: string) => BrandInfluencer | null;
  renderStatusBadge: (status: string) => JSX.Element;
  openProposalSheet: (influencer: Influencer) => void;
  expandedRows: string[];
  toggleRowExpansion: (id: string) => void;
  addCustomBudget: (influencerId: string, serviceType: CustomBudget['serviceType'], customPrice: number, notes?: string) => void;
  getCustomBudget: (influencerId: string, serviceType: CustomBudget['serviceType']) => CustomBudget | null;
  removeCustomBudget: (influencerId: string, serviceType: CustomBudget['serviceType']) => void;
}

const TiktokIcon = ({ className }: { className?: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="currentColor" viewBox="0 0 448 512" className={className}>
    <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
  </svg>
);

// Componente para editar orçamento personalizado
const CustomBudgetEditor = ({ 
  influencerId, 
  serviceType, 
  originalPrice, 
  serviceName,
  formatCurrency,
  addCustomBudget,
  getCustomBudget,
  removeCustomBudget 
}: {
  influencerId: string;
  serviceType: CustomBudget['serviceType'];
  originalPrice: number;
  serviceName: string;
  formatCurrency: (value: number) => string;
  addCustomBudget: (influencerId: string, serviceType: CustomBudget['serviceType'], customPrice: number, notes?: string) => void;
  getCustomBudget: (influencerId: string, serviceType: CustomBudget['serviceType']) => CustomBudget | null;
  removeCustomBudget: (influencerId: string, serviceType: CustomBudget['serviceType']) => void;
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [customPrice, setCustomPrice] = React.useState('');
  const [notes, setNotes] = React.useState('');
  
  const existingBudget = getCustomBudget(influencerId, serviceType);
  
  React.useEffect(() => {
    if (existingBudget) {
      setCustomPrice(existingBudget.customPrice.toString());
      setNotes(existingBudget.notes || '');
    } else {
      setCustomPrice('');
      setNotes('');
    }
  }, [existingBudget, isOpen]);
  
  const handleSave = () => {
    const price = parseFloat(customPrice);
    if (isNaN(price) || price <= 0) {
      return;
    }
    
    addCustomBudget(influencerId, serviceType, price, notes.trim() || undefined);
    setIsOpen(false);
  };
  
  const handleRemove = () => {
    removeCustomBudget(influencerId, serviceType);
    setIsOpen(false);
  };
  
  return (
    <div className="flex justify-between items-start">
      <div className="flex flex-col gap-1">
        <span className="text-xs font-medium text-muted-foreground">{serviceName}</span>
        <span className="text-xs text-muted-foreground">
          Orçamento: {existingBudget ? formatCurrency(existingBudget.customPrice) : "-"}
        </span>
      </div>
      <div className="flex flex-col items-end gap-1">
        <div className="flex items-center gap-1">
          <span className="font-medium text-xs">
            {formatCurrency(originalPrice)}
          </span>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-accent"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Orçamento Personalizado</DialogTitle>
                <DialogDescription>
                  Defina um valor personalizado para {serviceName}. Valor original: {formatCurrency(originalPrice)}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="custom-price" className="text-right">
                    Valor
                  </Label>
                  <Input
                    id="custom-price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={customPrice}
                    onChange={(e) => setCustomPrice(e.target.value)}
                    className="col-span-3"
                    placeholder="0.00"
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="notes" className="text-right pt-2">
                    Observações
                  </Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="col-span-3"
                    placeholder="Observações sobre o orçamento..."
                    rows={3}
                  />
                </div>
              </div>
              <DialogFooter>
                {existingBudget && (
                  <Button variant="destructive" onClick={handleRemove}>
                    <X className="h-4 w-4 mr-2" />
                    Remover
                  </Button>
                )}
                <Button onClick={handleSave} disabled={!customPrice || isNaN(parseFloat(customPrice)) || parseFloat(customPrice) <= 0}>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

      </div>
    </div>
  );
};

// Componente memoizado para o botão de expandir
const ExpandButton = React.memo(({ 
  influencerId, 
  expandedRows, 
  toggleRowExpansion 
}: { 
  influencerId: string; 
  expandedRows: string[]; 
  toggleRowExpansion: (id: string) => void;
}) => {
  const isExpanded = expandedRows.includes(influencerId);
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => toggleRowExpansion(influencerId)}
      className="h-8 w-8 p-0"
    >
      <motion.div
        animate={{ rotate: isExpanded ? 90 : 0 }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
      >
        <ChevronRight className="h-4 w-4" />
      </motion.div>
    </Button>
  );
}, (prevProps, nextProps) => {
  // Re-renderiza se o estado de expansão mudou para este influenciador específico
  const prevExpanded = prevProps.expandedRows.includes(prevProps.influencerId);
  const nextExpanded = nextProps.expandedRows.includes(nextProps.influencerId);
  return prevExpanded === nextExpanded && prevProps.influencerId === nextProps.influencerId;
});

// Componente memoizado para o avatar do influenciador
const InfluencerAvatarCell = React.memo(({ influencer, getInfluencerStatus }: { 
  influencer: Influencer; 
  getInfluencerStatus: (id: string) => BrandInfluencer | null;
}) => {
  const brandInfluencer = influencer?.id ? getInfluencerStatus(influencer.id) : null;
  
  return (
    <div className="flex items-center space-x-3 min-w-[200px]">
      <Avatar className="h-10 w-10">
        <AvatarImage src={influencer.avatar} alt={influencer.nome} />
        <AvatarFallback>{influencer.nome.charAt(0)}</AvatarFallback>
      </Avatar>
      <div className="space-y-1">
        <div className="font-medium text-sm">{influencer.nome}</div>
        <div className="flex items-center text-xs text-muted-foreground space-x-1">
          <User className="h-3 w-3" />
          <span>{influencer.idade} anos • {influencer.genero}</span>
        </div>
        <div className="flex items-center text-xs text-muted-foreground space-x-2">
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3" />
            <span>{influencer.cidade}, {influencer.estado}, {influencer.pais}</span>
          </div>
        
          <span className="capitalize">{influencer.categoria}</span>
        </div>
        {brandInfluencer && (
          <div className="flex items-center space-x-1">
            <Badge 
              variant="default" 
              className="text-xs h-5 bg-[#ff0074] text-white"
            >
              ✓ Trader
            </Badge>
          </div>
        )}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Só re-renderiza se os dados do influenciador mudaram
  return (
    prevProps.influencer.id === nextProps.influencer.id &&
    prevProps.influencer.nome === nextProps.influencer.nome &&
    prevProps.influencer.avatar === nextProps.influencer.avatar &&
    prevProps.influencer.idade === nextProps.influencer.idade &&
    prevProps.influencer.genero === nextProps.influencer.genero &&
    prevProps.influencer.cidade === nextProps.influencer.cidade &&
    prevProps.influencer.estado === nextProps.influencer.estado &&
    prevProps.influencer.pais === nextProps.influencer.pais &&
    prevProps.influencer.categoria === nextProps.influencer.categoria
  );
});

export const createTableColumns = ({
  selectedInfluencers,
  selectionMode,
  activeTab,
  filteredInfluencers,
  handleSelectInfluencer,
  formatNumber,
  formatCurrency,
  getInfluencerStatus,
  renderStatusBadge,
  openProposalSheet,
  expandedRows,
  toggleRowExpansion,
  addCustomBudget,
  getCustomBudget,
  removeCustomBudget
}: TableColumnsProps): ColumnDef<Influencer>[] => [
  {
    accessorKey: "nome",
    header: "Influenciador",
    cell: ({ row }) => {
      const influencer = row.original;
      return (
        <InfluencerAvatarCell influencer={influencer} getInfluencerStatus={getInfluencerStatus} />
      );
    },
  },
  {
    accessorKey: "instagram",
    header: "Instagram",
    cell: ({ row }) => {
      const influencer = row.original;
      const { redesSociais } = influencer;
      return (
        <div className="space-y-1 w-auto align-top">
          {redesSociais?.instagram && (
            <div className="flex items-center justify-between text-sm mb-1">
              <div className="flex items-center gap-1">
                <div className="flex items-center justify-center w-3 h-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                    <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                  </svg>
                </div>
                <div className="flex flex-col">
                  <span className="font-medium text-xs">{formatNumber(redesSociais.instagram.seguidores)}</span>
                  <span className="text-muted-foreground text-xs">{redesSociais.instagram.engajamento}%</span>
                </div>
              </div>
              <div className="flex flex-col items-end text-xs text-muted-foreground">
                {influencer.stories_views && parseInt(influencer.stories_views.toString().replace(/\./g, '')) > 0 && (
                  <div className="flex items-center gap-1">
                    <span>Stories:</span>
                    <span>{formatNumber(typeof influencer.stories_views === 'string' ? parseInt(influencer.stories_views.replace(/\./g, '')) : influencer.stories_views)}</span>
                  </div>
                )}
                {influencer.instagram_reels_views && parseInt(influencer.instagram_reels_views.toString().replace(/\./g, '')) > 0 && (
                  <div className="flex items-center gap-1">
                    <span>Reels:</span>
                    <span>{formatNumber(typeof influencer.instagram_reels_views === 'string' ? parseInt(influencer.instagram_reels_views.replace(/\./g, '')) : influencer.instagram_reels_views)}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="grid grid-cols-2 gap-1">
            <div className="bg-muted/20 border p-1 rounded relative">
              <CustomBudgetEditor
                influencerId={influencer.id}
                serviceType="instagramStory"
                originalPrice={influencer.dadosFinanceiros?.precos?.instagramStory?.price || 0}
                serviceName={influencer.dadosFinanceiros?.precos?.instagramStory?.name || "Story"}
                formatCurrency={formatCurrency}
                addCustomBudget={addCustomBudget}
                getCustomBudget={getCustomBudget}
                removeCustomBudget={removeCustomBudget}
              />
            </div>
            <div className="bg-muted/20 border p-1 rounded relative">
              <CustomBudgetEditor
                influencerId={influencer.id}
                serviceType="instagramReel"
                originalPrice={influencer.dadosFinanceiros?.precos?.instagramReel?.price || 0}
                serviceName={influencer.dadosFinanceiros?.precos?.instagramReel?.name || "Reel"}
                formatCurrency={formatCurrency}
                addCustomBudget={addCustomBudget}
                getCustomBudget={getCustomBudget}
                removeCustomBudget={removeCustomBudget}
              />
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "youtube",
    header: "YouTube",
    cell: ({ row }) => {
      const influencer = row.original;
      const { redesSociais } = influencer;
      return (
        <div className="space-y-1 w-auto align-top">
          {redesSociais?.youtube && (
            <div className="flex items-center justify-between text-sm mb-1">
              <div className="flex items-center gap-1.5">
                <div className="flex items-center justify-center w-4 h-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                    <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                  </svg>
                </div>
                <div className="flex flex-col">
                  <span className="font-medium text-xs">{formatNumber(redesSociais.youtube.seguidores)}</span>
                  <span className="text-muted-foreground text-xs">{formatNumber(redesSociais.youtube.visualizacoes)}</span>
                </div>
              </div>
              <div className="flex flex-col items-end text-xs text-muted-foreground">
                {influencer.youtube_insertion_views && parseInt(influencer.youtube_insertion_views.toString().replace(/\./g, '')) > 0 && (
                  <div className="flex items-center gap-1">
                    <span>Inserção:</span>
                    <span>{formatNumber(typeof influencer.youtube_insertion_views === 'string' ? parseInt(influencer.youtube_insertion_views.replace(/\./g, '')) : influencer.youtube_insertion_views)}</span>
                  </div>
                )}
                {influencer.youtube_long_video_views && parseInt(influencer.youtube_long_video_views.toString().replace(/\./g, '')) > 0 && (
                  <div className="flex items-center gap-1">
                    <span>Longo:</span>
                    <span>{formatNumber(typeof influencer.youtube_long_video_views === 'string' ? parseInt(influencer.youtube_long_video_views.replace(/\./g, '')) : influencer.youtube_long_video_views)}</span>
                  </div>
                )}
                {influencer.youtube_shorts_views && parseInt(influencer.youtube_shorts_views.toString().replace(/\./g, '')) > 0 && (
                  <div className="flex items-center gap-1">
                    <span>Shorts:</span>
                    <span>{formatNumber(typeof influencer.youtube_shorts_views === 'string' ? parseInt(influencer.youtube_shorts_views.replace(/\./g, '')) : influencer.youtube_shorts_views)}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="grid grid-cols-3 gap-1">
            <div className="bg-muted/20 border p-1.5 rounded relative">
              <CustomBudgetEditor
                influencerId={influencer.id}
                serviceType="youtubeInsertion"
                originalPrice={influencer.dadosFinanceiros?.precos?.youtubeInsertion?.price || 0}
                serviceName={influencer.dadosFinanceiros?.precos?.youtubeInsertion?.name || "Inserção"}
                formatCurrency={formatCurrency}
                addCustomBudget={addCustomBudget}
                getCustomBudget={getCustomBudget}
                removeCustomBudget={removeCustomBudget}
              />

            </div>
            <div className="bg-muted/20 border p-1.5 rounded relative">
              <CustomBudgetEditor
                influencerId={influencer.id}
                serviceType="youtubeDedicated"
                originalPrice={influencer.dadosFinanceiros?.precos?.youtubeDedicated?.price || 0}
                serviceName={influencer.dadosFinanceiros?.precos?.youtubeDedicated?.name || "Longo"}
                formatCurrency={formatCurrency}
                addCustomBudget={addCustomBudget}
                getCustomBudget={getCustomBudget}
                removeCustomBudget={removeCustomBudget}
              />

            </div>
            <div className="bg-muted/20 border p-1.5 rounded relative">
              <CustomBudgetEditor
                influencerId={influencer.id}
                serviceType="youtubeShorts"
                originalPrice={influencer.dadosFinanceiros?.precos?.youtubeShorts?.price || 0}
                serviceName={influencer.dadosFinanceiros?.precos?.youtubeShorts?.name || "Shorts"}
                formatCurrency={formatCurrency}
                addCustomBudget={addCustomBudget}
                getCustomBudget={getCustomBudget}
                removeCustomBudget={removeCustomBudget}
              />

            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "tiktok",
    header: "TikTok",
    cell: ({ row }) => {
      const influencer = row.original;
      const { redesSociais } = influencer;
      return (
        <div className="space-y-1 w-auto align-top">
          {redesSociais?.tiktok && (
            <div className="flex items-center justify-between text-sm mb-1">
              <div className="flex items-center gap-1">
                <div className="flex items-center justify-center w-3 h-3">
                  <TiktokIcon className="text-black dark:text-white" />
                </div>
                <div className="flex flex-col">
                  <span className="font-medium text-xs">{formatNumber(redesSociais.tiktok.seguidores)}</span>
                  {redesSociais.tiktok.curtidas && (
                    <span className="text-muted-foreground text-xs">{formatNumber(redesSociais.tiktok.curtidas)} likes</span>
                  )}
                </div>
              </div>
              <div className="flex flex-col items-end text-xs text-muted-foreground">
                {influencer.tiktok_views && parseInt(influencer.tiktok_views.toString().replace(/\./g, '')) > 0 && (
                  <div className="flex items-center gap-1">
                    <span>Vídeo:</span>
                    <span>{formatNumber(typeof influencer.tiktok_views === 'string' ? parseInt(influencer.tiktok_views.replace(/\./g, '')) : influencer.tiktok_views)}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="grid grid-cols-1 gap-1">
            <div className="bg-muted/20 border p-1 rounded relative">
              <CustomBudgetEditor
                influencerId={influencer.id}
                serviceType="tiktokVideo"
                originalPrice={influencer.dadosFinanceiros?.precos?.tiktokVideo?.price || 0}
                serviceName={influencer.dadosFinanceiros?.precos?.tiktokVideo?.name || "Vídeo"}
                formatCurrency={formatCurrency}
                addCustomBudget={addCustomBudget}
                getCustomBudget={getCustomBudget}
                removeCustomBudget={removeCustomBudget}
              />

            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "responsavel_agencia",
    header: "Responsável / Agência",
    cell: ({ row }) => {
      const responsavel = row.original.dadosFinanceiros?.responsavel;
      const agencia = row.original.dadosFinanceiros?.agencia;
      
      return (
        <div className="text-sm min-w-[120px] space-y-1">
          {responsavel && (
            <div className="flex items-center gap-2">
              <UserCheck className="h-3 w-3 text-blue-500 flex-shrink-0" />
              <div className="font-medium">{responsavel}</div>
            </div>
          )}
          {agencia && (
            <div className="flex items-center gap-2">
              <Briefcase className="h-3 w-3 text-purple-500 flex-shrink-0" />
              <div className="text-muted-foreground text-xs">{agencia}</div>
            </div>
          )}
          {/* ✅ Se não há dados administrativos, não exibir nada */}
        </div>
      );
    },
  },
  {
    accessorKey: "contato",
    header: "Contato",
    cell: ({ row }) => {
      const influencer = row.original;
      
      // ✅ CAMPOS DINÂMICOS: Verificar múltiplas fontes de dados
              // ✅ Usar APENAS os dados do snapshot (sem fallbacks)
        const whatsapp = influencer.whatsapp;
        const email = influencer.email;
      
      return (
        <div className="w-20 space-y-1">
          {/* WhatsApp */}
          <div>
            <Protect role="org:admin">
              {whatsapp ? (
                <Button variant="outline" size="sm" asChild>
                  <a href={`https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}`} target="_blank" rel="noopener noreferrer">
                    <Phone className="h-4 w-4" />
                  </a>
                </Button>
              ) : null /* ✅ Não exibir se não há WhatsApp */}
            </Protect>
          </div>
          {/* Email */}
          <div>
            {email ? (
              <Button variant="outline" size="sm" asChild>
                <a href={`mailto:${email}`}>
                  <Mail className="h-4 w-4" />
                </a>
              </Button>
            ) : null /* ✅ Não exibir se não há Email */}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const influencer = row.original;
      const brandInfluencer = influencer?.id ? getInfluencerStatus(influencer.id) : null;
      
      if (activeTab === 'sent' && brandInfluencer) {
        return (
          <div className="space-y-2 min-w-[120px]">
            <div className="flex gap-2 items-center">
              {renderStatusBadge(brandInfluencer.status)}
              <Button
                size="sm"
                variant="outline"
                onClick={() => openProposalSheet(influencer)}
                className="flex items-center gap-1 text-xs h-6 px-2"
              >
                <FileText className="h-3 w-3" />
                Proposta
              </Button>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>Enviado: {new Date(brandInfluencer.sentAt).toLocaleDateString('pt-BR')}</div>
              {brandInfluencer.viewedAt && (
                <div>Visualizado: {new Date(brandInfluencer.viewedAt).toLocaleDateString('pt-BR')}</div>
              )}
              {brandInfluencer.lastInteractionAt && (
                <div>Última interação: {new Date(brandInfluencer.lastInteractionAt).toLocaleDateString('pt-BR')}</div>
              )}
            </div>
            {brandInfluencer.notes && (
              <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                <strong>Notas:</strong> {brandInfluencer.notes}
              </div>
            )}
          </div>
        );
      }
      
      return brandInfluencer ? (
        <div className="space-y-1">
          {renderStatusBadge(brandInfluencer.status)}
          <div className="text-xs text-muted-foreground">
            {new Date(brandInfluencer.sentAt).toLocaleDateString('pt-BR')}
          </div>
        </div>
      ) : (
        <Badge variant="outline">Não enviado</Badge>
      );
    },
  },
];


