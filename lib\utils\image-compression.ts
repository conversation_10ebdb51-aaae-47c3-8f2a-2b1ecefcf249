/**
 * Função para comprimir imagens antes do upload
 * Reduz significativamente o tamanho dos arquivos base64 enviados via GraphQL
 */
export function compressImage(file: File, quality: number = 0.7, maxWidth: number = 1920): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    const img = new Image()
    
    img.onload = () => {
      // Calcular proporções mantendo aspect ratio
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
      const width = img.width * ratio
      const height = img.height * ratio
      
      // Configurar canvas
      canvas.width = width
      canvas.height = height
      
      // Desenhar imagem redimensionada
      ctx.drawImage(img, 0, 0, width, height)
      
      // Converter para blob com compressão
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            })
            console.log(`🗜️ [Compression] ${file.name}: ${file.size} -> ${compressedFile.size} bytes (${Math.round((1 - compressedFile.size / file.size) * 100)}% redução)`)
            resolve(compressedFile)
          } else {
            // Se falhar na compressão, retornar arquivo original
            console.warn(`⚠️ [Compression] Falha na compressão de ${file.name}, usando original`)
            resolve(file)
          }
        },
        file.type,
        quality
      )
    }
    
    img.onerror = () => {
      // Se falhar no carregamento, retornar arquivo original
      console.warn(`⚠️ [Compression] Falha no carregamento de ${file.name}, usando original`)
      resolve(file)
    }
    
    img.src = URL.createObjectURL(file)
  })
} 

