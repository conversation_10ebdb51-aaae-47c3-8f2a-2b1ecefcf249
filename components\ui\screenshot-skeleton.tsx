import React from 'react'
import { cn } from '@/lib/utils'

interface ScreenshotSkeletonProps {
  className?: string
  aspectRatio?: 'square' | 'video' | 'auto'
  showFilename?: boolean
  count?: number
}

export function ScreenshotSkeleton({ 
  className, 
  aspectRatio = 'video', 
  showFilename = true,
  count = 1 
}: ScreenshotSkeletonProps) {
  const aspectRatioClass = {
    square: 'aspect-square',
    video: 'aspect-video',
    auto: 'aspect-auto'
  }[aspectRatio]

  const skeletons = Array.from({ length: count }, (_, index) => (
    <div key={index} className="space-y-1">
      {/* Skeleton da imagem */}
      <div className={cn(
        "relative rounded-md overflow-hidden bg-muted animate-pulse",
        aspectRatioClass,
        className
      )}>
        {/* Gradiente animado */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
        
        {/* Ícone de imagem no centro */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 bg-muted-foreground/20 rounded-full flex items-center justify-center">
            <svg 
              className="w-4 h-4 text-muted-foreground/40" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
          </div>
        </div>
      </div>
      
      {/* Skeleton do nome do arquivo */}
      {showFilename && (
        <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
      )}
    </div>
  ))

  return count === 1 ? skeletons[0] : <>{skeletons}</>
}

interface ScreenshotGridSkeletonProps {
  columns?: number
  rows?: number
  className?: string
  aspectRatio?: 'square' | 'video' | 'auto'
  showFilenames?: boolean
}

export function ScreenshotGridSkeleton({
  columns = 4,
  rows = 2,
  className,
  aspectRatio = 'video',
  showFilenames = true
}: ScreenshotGridSkeletonProps) {
  const totalItems = columns * rows

  const gridClass = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6'
  }[columns] || 'grid-cols-4'

  return (
    <div className={cn(`grid ${gridClass} gap-2`, className)}>
      <ScreenshotSkeleton
        aspectRatio={aspectRatio}
        showFilename={showFilenames}
        count={totalItems}
      />
    </div>
  )
}

// Adicionar classes CSS personalizadas para a animação shimmer
// Isso deve ser adicionado ao seu arquivo CSS global ou Tailwind config
const shimmerKeyframes = `
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
`

// Componente para injetar os estilos CSS
export function ScreenshotSkeletonStyles() {
  return (
    <style jsx global>{shimmerKeyframes}</style>
  )
}
