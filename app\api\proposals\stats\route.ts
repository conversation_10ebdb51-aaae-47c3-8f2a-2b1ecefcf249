'use server';

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getFirestore } from 'firebase-admin/firestore';
import '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    // Usar autenticação Clerk
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Usuário não autenticado' }, { status: 401 });
    }

    const { influencerIds, brandId } = await request.json();

    if (!influencerIds || !Array.isArray(influencerIds) || influencerIds.length === 0) {
      return NextResponse.json({ error: 'IDs de influenciadores são obrigatórios' }, { status: 400 });
    }

    console.log('🔍 [PROPOSAL_STATUS_API] Verificando status para:', {
      userId: userId,
      influencerIds: influencerIds.length,
      brandId: brandId || 'Todas as marcas'
    });

    const db = getFirestore();
    const statusMap: Record<string, { 
      status: 'Enviado' | 'Não enviado'; 
      totalProposals: number; 
      lastProposalDate?: string;
      lastProposalName?: string;
    }> = {};

    // Inicializar todos os influenciadores como "Não enviado"
    influencerIds.forEach(id => {
      statusMap[id] = {
        status: 'Não enviado',
        totalProposals: 0
      };
    });

    try {
      // Construir query base para propostas do usuário
      let proposalsQuery = db
        .collection('proposals')
        .where('criadoPor', '==', userId);

      // Se brandId for fornecido, filtrar por marca específica
      if (brandId && brandId !== 'all') {
        proposalsQuery = proposalsQuery.where('brandId', '==', brandId);
        console.log(`🏷️ [PROPOSAL_STATUS_API] Filtrando por marca: ${brandId}`);
      }

      const proposalsSnapshot = await proposalsQuery.get();

      console.log('📊 [PROPOSAL_STATUS_API] Propostas encontradas:', {
        total: proposalsSnapshot.size,
        filteredByBrand: brandId ? `Marca: ${brandId}` : 'Todas as marcas'
      });

      // Para cada proposta, verificar os influenciadores na subcoleção
      const proposalCheckPromises = proposalsSnapshot.docs.map(async (proposalDoc) => {
        const proposalId = proposalDoc.id;
        const proposalData = proposalDoc.data();

        try {
          // Buscar influenciadores na subcoleção desta proposta
          const influencersSnapshot = await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .get();

          console.log(`📋 [PROPOSAL_${proposalId}] Marca: ${proposalData.brandId}, Influenciadores: ${influencersSnapshot.size}`);

          influencersSnapshot.docs.forEach(influencerDoc => {
            const influencerData = influencerDoc.data();
            const influencerId = influencerData.influencerId || influencerDoc.id;

            // Se este influenciador está na lista que estamos verificando
            if (influencerIds.includes(influencerId)) {
              if (statusMap[influencerId]) {
                statusMap[influencerId].status = 'Enviado';
                statusMap[influencerId].totalProposals += 1;
                
                // Atualizar a data e nome da última proposta se for mais recente
                const proposalDate = proposalData.createdAt?.toDate?.() || 
                                   proposalData.dataEnvio ? new Date(proposalData.dataEnvio) : 
                                   new Date();
                
                if (!statusMap[influencerId].lastProposalDate || 
                    new Date(proposalDate) > new Date(statusMap[influencerId].lastProposalDate!)) {
                  statusMap[influencerId].lastProposalDate = proposalDate instanceof Date 
                    ? proposalDate.toISOString() 
                    : new Date(proposalDate).toISOString();
                  statusMap[influencerId].lastProposalName = proposalData.nome || `Proposta ${proposalId.substring(0, 8)}`;
                }
              }
            }
          });

        } catch (error) {
          console.warn(`⚠️ [PROPOSAL_${proposalId}] Erro ao buscar influenciadores:`, error);
          // Continuar com as outras propostas mesmo se uma falhar
        }
      });

      await Promise.all(proposalCheckPromises);

      const statsResult = {
        total: Object.keys(statusMap).length,
        enviados: Object.values(statusMap).filter(s => s.status === 'Enviado').length,
        naoEnviados: Object.values(statusMap).filter(s => s.status === 'Não enviado').length,
        brandId: brandId || 'all'
      };

      console.log('✅ [PROPOSAL_STATUS_API] Status final calculado:', statsResult);

      return NextResponse.json({
        success: true,
        influencersStatus: statusMap,
        stats: statsResult
      });

    } catch (error) {
      console.error('❌ [PROPOSAL_STATUS_API] Erro ao verificar propostas:', error);
      
      // Em caso de erro, retornar status padrão
      return NextResponse.json({
        success: true,
        influencersStatus: statusMap // Já inicializado com "Não enviado"
      });
    }

  } catch (error) {
    console.error('❌ [PROPOSAL_STATUS_API] Erro geral:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

