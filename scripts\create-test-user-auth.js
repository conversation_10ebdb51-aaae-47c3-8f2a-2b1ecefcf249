const admin = require('firebase-admin');

// Inicializar Firebase Admin (se ainda não foi inicializado)
try {
  admin.app();
} catch (e) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'deumatch-demo'
  });
}

const auth = admin.auth();
const db = admin.firestore();

// Dados do usuário de teste
const testUser = {
  uid: 'dev-user-123',
  email: '<EMAIL>',
  password: '*********', // Senha simples para testes
  displayName: 'Usuário de Teste',
  disabled: false
};

async function createTestUserAuth() {
  try {
    console.log('🚀 Criando usuário de teste no Firebase Authentication...');

    // Verificar se usuário já existe
    try {
      const existingUser = await auth.getUser(testUser.uid);
      console.log('⚠️ Usuário já existe no Firebase Auth:', existingUser.email);
      
      // Atualizar senha se necessário
      await auth.updateUser(testUser.uid, {
        password: testUser.password,
        disabled: false
      });
      console.log('✅ Senha atualizada para:', testUser.password);
      
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        // Criar novo usuário
        const userRecord = await auth.createUser({
          uid: testUser.uid,
          email: testUser.email,
          password: testUser.password,
          displayName: testUser.displayName,
          disabled: false
        });
        
        console.log('✅ Usuário criado no Firebase Auth:', userRecord.email);
      } else {
        throw error;
      }
    }

    // Verificar/criar documento no Firestore
    const userDoc = await db.collection('users').doc(testUser.uid).get();
    
    if (!userDoc.exists) {
      await db.collection('users').doc(testUser.uid).set({
        email: testUser.email,
        name: testUser.displayName,
        role: 'admin',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: new Date()
      });
      console.log('✅ Documento de usuário criado no Firestore');
    } else {
      console.log('✅ Documento já existe no Firestore');
    }

    // Definir custom claims para admin
    await auth.setCustomUserClaims(testUser.uid, {
      admin: true,
      role: 'admin'
    });
    console.log('✅ Custom claims definidas (admin: true)');

    console.log('\n🎉 Usuário de teste criado com sucesso!');
    console.log('\n📋 Credenciais de Login:');
    console.log(`📧 Email: ${testUser.email}`);
    console.log(`🔑 Senha: ${testUser.password}`);
    console.log(`🆔 UID: ${testUser.uid}`);
    console.log(`👤 Role: admin`);
    
    console.log('\n🧪 URLs de Teste:');
    console.log('• http://localhost:3000/login');
    console.log('• http://localhost:3000/test-fase6');
    console.log('• http://localhost:3000/test-isolamento');
    console.log('• http://localhost:3000/admin/brands');

  } catch (error) {
    console.error('❌ Erro ao criar usuário de teste:', error);
  } finally {
    process.exit(0);
  }
}

// Executar criação do usuário
createTestUserAuth(); 
