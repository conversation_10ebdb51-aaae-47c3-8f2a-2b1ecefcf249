import { NextResponse } from 'next/server';
import { 
  getAllTags, 
  getTagById, 
  addTag, 
  updateTag, 
  deleteTag,
  addTagToInfluencer,
  removeTagFromInfluencer
} from '@/lib/firebase-tags';

// Rota GET para buscar todas as etiquetas ou uma específica por ID
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  const influencerId = searchParams.get('influencerId');

  try {
    if (id) {
      // Buscar uma etiqueta específica pelo ID
      const tag = await getTagById(id);
      
      if (!tag) {
        return NextResponse.json(
          { error: 'Etiqueta não encontrada' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(tag);
    } else if (influencerId) {
      // Buscar todas as etiquetas de um influenciador específico
      // Esta funcionalidade seria implementada no futuro
      // Por enquanto, retornamos todas as etiquetas
      const tags = await getAllTags();
      return NextResponse.json(tags);
    } else {
      // Buscar todas as etiquetas
      const tags = await getAllTags();
      return NextResponse.json(tags);
    }
  } catch (error) {
    console.error('Erro ao buscar etiquetas:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar etiquetas' },
      { status: 500 }
    );
  }
}

// Rota POST para adicionar uma nova etiqueta
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Validar dados
    if (!data.name || !data.color) {
      return NextResponse.json(
        { error: 'Nome e cor são obrigatórios' },
        { status: 400 }
      );
    }
    
    // Adicionar etiqueta
    const tagId = await addTag({
      name: data.name,
      color: data.color,
      createdAt: new Date()
    });
    
    // Se for para associar a um influenciador
    if (data.influencerId) {
      await addTagToInfluencer(data.influencerId, tagId);
    }
    
    return NextResponse.json({ 
      success: true, 
      id: tagId,
      message: 'Etiqueta criada com sucesso'
    }, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar etiqueta:', error);
    return NextResponse.json(
      { error: 'Erro ao criar etiqueta' },
      { status: 500 }
    );
  }
}

// Rota PUT para atualizar uma etiqueta existente
export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID da etiqueta é obrigatório' },
        { status: 400 }
      );
    }
    
    const data = await request.json();
    
    // Verificar se a etiqueta existe
    const tag = await getTagById(id);
    if (!tag) {
      return NextResponse.json(
        { error: 'Etiqueta não encontrada' },
        { status: 404 }
      );
    }
    
    // Atualizar etiqueta
    await updateTag(id, {
      name: data.name,
      color: data.color
    });
    
    return NextResponse.json({ 
      success: true,
      message: 'Etiqueta atualizada com sucesso'
    });
  } catch (error) {
    console.error('Erro ao atualizar etiqueta:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar etiqueta' },
      { status: 500 }
    );
  }
}

// Rota DELETE para excluir uma etiqueta
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const influencerId = searchParams.get('influencerId');
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID da etiqueta é obrigatório' },
        { status: 400 }
      );
    }
    
    // Se for para remover de um influenciador específico
    if (influencerId) {
      await removeTagFromInfluencer(influencerId, id);
      return NextResponse.json({ 
        success: true,
        message: 'Etiqueta removida do influenciador com sucesso'
      });
    }
    
    // Excluir etiqueta completamente
    await deleteTag(id);
    
    return NextResponse.json({ 
      success: true,
      message: 'Etiqueta excluída com sucesso'
    });
  } catch (error) {
    console.error('Erro ao excluir etiqueta:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir etiqueta' },
      { status: 500 }
    );
  }
}


