import { NextRequest, NextResponse } from 'next/server';
import { withCategoryAccess, validateCategoryInput, logCategoryOperation } from '@/lib/middleware/category-access';
import { CategoryService } from '@/services/category-service-unified';

// 🔒 ROTA GET PROTEGIDA - Buscar categorias com isolamento de usuário
export const GET = withCategoryAccess(async (req: NextRequest, userId: string, context) => {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  try {
    if (id) {
      // Buscar categoria específica
      const category = await CategoryService.getCategoryById(id, userId);

      if (!category) {
        logCategoryOperation('GET_CATEGORY_BY_ID', id, userId, false, { reason: 'not_found' });
        return NextResponse.json(
          { error: 'Categoria não encontrada' },
          { status: 404 }
        );
      }

      logCategoryOperation('GET_CATEGORY_BY_ID', id, userId, true);
      return NextResponse.json(category);
    } else {
      // Buscar todas as categorias
      const categories = await CategoryService.getCategories(
        userId,
        { includePublic: true, isActive: true },
        { orderBy: 'name', orderDirection: 'asc' }
      );

      logCategoryOperation('GET_CATEGORIES', null, userId, true, { count: categories.length });
      return NextResponse.json(categories);
    }
  } catch (error: any) {
    logCategoryOperation(id ? 'GET_CATEGORY_BY_ID' : 'GET_CATEGORIES', id, userId, false, { error: error.message });
    return NextResponse.json(
      { error: 'Erro ao buscar dados de categorias' },
      { status: 500 }
    );
  }
});

// 🔒 ROTA POST PROTEGIDA - Criar categoria com isolamento de usuário
export const POST = withCategoryAccess(async (req: NextRequest, userId: string, context) => {
  try {
    const data = await req.json();

    // Validar e sanitizar dados de entrada
    const validatedInput = validateCategoryInput(data, 'create');

    // Criar categoria usando o serviço unificado
    const result = await CategoryService.createCategory(userId, validatedInput);

    logCategoryOperation('CREATE_CATEGORY', result.id, userId, true, { name: result.name });
    return NextResponse.json(result, { status: 201 });
  } catch (error: any) {
    logCategoryOperation('CREATE_CATEGORY', null, userId, false, { error: error.message });
    return NextResponse.json(
      { error: error.message || 'Erro ao criar categoria' },
      { status: 400 }
    );
  }
});

// 🔒 ROTA PUT PROTEGIDA - Atualizar categoria com isolamento de usuário
export const PUT = withCategoryAccess(async (req: NextRequest, userId: string, context) => {
  try {
    const data = await req.json();

    // Validar ID obrigatório
    if (!data.id) {
      return NextResponse.json(
        { error: 'ID da categoria é obrigatório' },
        { status: 400 }
      );
    }

    // Validar e sanitizar dados de entrada
    const validatedInput = validateCategoryInput(data, 'update');

    // Atualizar categoria usando o serviço unificado
    const result = await CategoryService.updateCategory(data.id, userId, validatedInput);

    logCategoryOperation('UPDATE_CATEGORY', data.id, userId, true, { name: result.name });
    return NextResponse.json(result);
  } catch (error: any) {
    logCategoryOperation('UPDATE_CATEGORY', data?.id || null, userId, false, { error: error.message });
    return NextResponse.json(
      { error: error.message || 'Erro ao atualizar categoria' },
      { status: 400 }
    );
  }
});

// 🔒 ROTA DELETE PROTEGIDA - Excluir categoria com isolamento de usuário
export const DELETE = withCategoryAccess(async (req: NextRequest, userId: string, context) => {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json(
      { error: 'ID da categoria não fornecido' },
      { status: 400 }
    );
  }

  try {
    // Deletar categoria usando o serviço unificado
    const success = await CategoryService.deleteCategory(id, userId);

    if (success) {
      logCategoryOperation('DELETE_CATEGORY', id, userId, true);
      return NextResponse.json({ success: true });
    } else {
      logCategoryOperation('DELETE_CATEGORY', id, userId, false, { reason: 'delete_failed' });
      return NextResponse.json(
        { error: 'Erro ao excluir categoria' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    logCategoryOperation('DELETE_CATEGORY', id, userId, false, { error: error.message });
    return NextResponse.json(
      { error: error.message || 'Erro ao excluir categoria' },
      { status: 400 }
    );
  }
});


