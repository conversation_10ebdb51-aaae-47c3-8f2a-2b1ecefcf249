import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { getAllCategories, getCategoryById, addCategory, updateCategory, deleteCategory } from '@/lib/firebase';

// 🔒 ROTA GET PROTEGIDA - Buscar categorias com isolamento de usuário
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  try {
    console.log(`🔒 [SECURITY] Buscando categorias para userId: ${userId}`);

    if (id) {
      // Buscar uma categoria específica pelo ID
      const category = await getCategoryById(id);

      if (!category) {
        return NextResponse.json(
          { error: 'Categoria não encontrada' },
          { status: 404 }
        );
      }

      // 🔒 VALIDAÇÃO: Verificar se a categoria é pública ou pertence ao usuário
      const isPublic = !category.userId || category.userId === '' || category.userId === 'system';
      const isUserCategory = category.userId === userId;

      if (!isPublic && !isUserCategory) {
        console.warn(`⚠️ [SECURITY] Tentativa de acesso a categoria de outro usuário: ${id}`);
        return NextResponse.json(
          { error: 'Categoria não encontrada' },
          { status: 404 }
        );
      }

      return NextResponse.json(category);
    } else {
      // 🔒 SEGURANÇA: Buscar categorias públicas + categorias do usuário
      const allCategories = await getAllCategories();
      const filteredCategories = allCategories.filter((category: any) => {
        const isPublic = !category.userId || category.userId === '' || category.userId === 'system';
        const isUserCategory = category.userId === userId;
        return isPublic || isUserCategory;
      });

      console.log(`🔒 [SECURITY] Retornando ${filteredCategories.length} categorias filtradas para usuário: ${userId}`);
      return NextResponse.json(filteredCategories);
    }
  } catch (error: any) {
    console.error('❌ [SECURITY] Erro ao buscar categorias:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar dados de categorias' },
      { status: 500 }
    );
  }
});

// 🔒 ROTA POST PROTEGIDA - Criar categoria com isolamento de usuário
export const POST = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const data = await req.json();

    console.log(`🔒 [SECURITY] Criando categoria para userId: ${userId}`);

    // Validação básica dos dados
    if (!data.name || !data.slug) {
      return NextResponse.json(
        { error: 'Dados inválidos. Nome e slug são obrigatórios.' },
        { status: 400 }
      );
    }

    // 🔒 SEGURANÇA: Garantir que a categoria seja criada com o userId correto
    const categoryData = {
      ...data,
      userId: userId // Forçar o userId do usuário autenticado
    };

    const newCategory = await addCategory(categoryData);
    console.log(`✅ [SECURITY] Categoria criada com sucesso: ${newCategory.id} para usuário: ${userId}`);

    return NextResponse.json(newCategory, { status: 201 });
  } catch (error: any) {
    console.error('❌ [SECURITY] Erro ao adicionar categoria:', error);
    return NextResponse.json(
      { error: 'Erro ao adicionar categoria' },
      { status: 500 }
    );
  }
});

// 🔒 ROTA PUT PROTEGIDA - Atualizar categoria com isolamento de usuário
export const PUT = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const data = await req.json();

    console.log(`🔒 [SECURITY] Atualizando categoria ${data.id} para userId: ${userId}`);

    // Validação básica dos dados
    if (!data.id || !data.name || !data.slug) {
      return NextResponse.json(
        { error: 'Dados inválidos. ID, nome e slug são obrigatórios.' },
        { status: 400 }
      );
    }

    // 🔒 VALIDAÇÃO: Verificar se a categoria pertence ao usuário
    const existingCategory = await getCategoryById(data.id);
    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Categoria não encontrada' },
        { status: 404 }
      );
    }

    if (existingCategory.userId !== userId) {
      console.warn(`⚠️ [SECURITY] Tentativa de atualizar categoria de outro usuário: ${data.id}`);
      return NextResponse.json(
        { error: 'Categoria não encontrada' },
        { status: 404 }
      );
    }

    const updatedCategory = await updateCategory(data.id, data);
    console.log(`✅ [SECURITY] Categoria atualizada com sucesso: ${data.id} para usuário: ${userId}`);

    return NextResponse.json(updatedCategory);
  } catch (error: any) {
    console.error('❌ [SECURITY] Erro ao atualizar categoria:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar categoria' },
      { status: 500 }
    );
  }
});

// 🔒 ROTA DELETE PROTEGIDA - Excluir categoria com isolamento de usuário
export const DELETE = withUserIsolation(async (req: NextRequest, userId: string) => {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json(
      { error: 'ID da categoria não fornecido' },
      { status: 400 }
    );
  }

  try {
    console.log(`🔒 [SECURITY] Excluindo categoria ${id} para userId: ${userId}`);

    // 🔒 VALIDAÇÃO: Verificar se a categoria pertence ao usuário
    const existingCategory = await getCategoryById(id);
    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Categoria não encontrada' },
        { status: 404 }
      );
    }

    if (existingCategory.userId !== userId) {
      console.warn(`⚠️ [SECURITY] Tentativa de excluir categoria de outro usuário: ${id}`);
      return NextResponse.json(
        { error: 'Categoria não encontrada' },
        { status: 404 }
      );
    }

    await deleteCategory(id);
    console.log(`✅ [SECURITY] Categoria excluída com sucesso: ${id} para usuário: ${userId}`);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('❌ [SECURITY] Erro ao excluir categoria:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir categoria' },
      { status: 500 }
    );
  }
});


