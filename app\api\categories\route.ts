import { NextResponse } from 'next/server';
import { getAllCategories, getCategoryById, addCategory, updateCategory, deleteCategory } from '@/lib/firebase';

// Rota GET para buscar todas as categorias
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  try {
    if (id) {
      // Buscar uma categoria específica pelo ID
      const category = await getCategoryById(id);
      
      if (!category) {
        return NextResponse.json(
          { error: 'Categoria não encontrada' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(category);
    } else {
      // 🔒 SEGURANÇA: Buscar apenas categorias públicas (sem userId específico)
      const allCategories = await getAllCategories();
      const publicCategories = allCategories.filter((category: any) => 
        !(category as any).userId || (category as any).userId === '' || (category as any).userId === 'system'
      );
      return NextResponse.json(publicCategories);
    }
  } catch (error: any) {
    console.error('Erro ao buscar categorias:', error);
    return NextResponse.json(
      { error: 'Erro ao buscar dados de categorias' },
      { status: 500 }
    );
  }
}

// Rota POST para adicionar uma nova categoria
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Validação básica dos dados
    if (!data.name || !data.slug) {
      return NextResponse.json(
        { error: 'Dados inválidos. Nome e slug são obrigatórios.' },
        { status: 400 }
      );
    }
    
    const newCategory = await addCategory(data);
    return NextResponse.json(newCategory, { status: 201 });
  } catch (error: any) {
    console.error('Erro ao adicionar categoria:', error);
    return NextResponse.json(
      { error: 'Erro ao adicionar categoria' },
      { status: 500 }
    );
  }
}

// Rota PUT para atualizar uma categoria existente
export async function PUT(request: Request) {
  try {
    const data = await request.json();
    
    // Validação básica dos dados
    if (!data.id || !data.name || !data.slug) {
      return NextResponse.json(
        { error: 'Dados inválidos. ID, nome e slug são obrigatórios.' },
        { status: 400 }
      );
    }
    
    const updatedCategory = await updateCategory(data.id, data);
    return NextResponse.json(updatedCategory);
  } catch (error: any) {
    console.error('Erro ao atualizar categoria:', error);
    return NextResponse.json(
      { error: 'Erro ao atualizar categoria' },
      { status: 500 }
    );
  }
}

// Rota DELETE para excluir uma categoria
export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');
  
  if (!id) {
    return NextResponse.json(
      { error: 'ID da categoria não fornecido' },
      { status: 400 }
    );
  }
  
  try {
    await deleteCategory(id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Erro ao excluir categoria:', error);
    return NextResponse.json(
      { error: 'Erro ao excluir categoria' },
      { status: 500 }
    );
  }
}


