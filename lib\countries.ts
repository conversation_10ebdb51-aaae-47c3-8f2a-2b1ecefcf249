import countries from 'world-countries';

// Interface para país simplificado
export interface Country {
  code: string;        // Código ISO (ex: 'BR')
  name: string;        // Nome em português
  nameEn: string;      // Nome em inglês
  region: string;      // Região geográfica
  subregion: string;   // Sub-região
  popular?: boolean;   // Se é um país popular para influenciadores
}

// Mapeamento de nomes para português
const countryNamesInPortuguese: Record<string, string> = {
  'Brazil': 'Brasil',
  'United States': 'Estados Unidos',
  'Portugal': 'Portugal',
  'Spain': 'Espanha',
  'France': 'França',
  'United Kingdom': 'Reino Unido',
  'Germany': 'Alemanha',
  'Italy': 'Itália',
  'Argentina': 'Argentina',
  'Mexico': 'México',
  'Colombia': 'Colômbia',
  'Chile': 'Chile',
  'Peru': 'Peru',
  'Uruguay': 'Uruguai',
  'Paraguay': 'Paraguai',
  'Bolivia': 'Bolívia',
  'Venezuela': 'Venezuela',
  'Ecuador': 'Equador',
  'Canada': 'Canadá',
  'Australia': 'Austrália',
  'Japan': 'Japão',
  'South Korea': 'Coreia do Sul',
  'China': 'China',
  'India': 'Índia',
  'Russia': 'Rússia',
  'Netherlands': 'Holanda',
  'Belgium': 'Bélgica',
  'Switzerland': 'Suíça',
  'Austria': 'Áustria',
  'Sweden': 'Suécia',
  'Norway': 'Noruega',
  'Denmark': 'Dinamarca',
  'Finland': 'Finlândia',
  'Poland': 'Polônia',
  'Czech Republic': 'República Tcheca',
  'Hungary': 'Hungria',
  'Greece': 'Grécia',
  'Turkey': 'Turquia',
  'Israel': 'Israel',
  'United Arab Emirates': 'Emirados Árabes Unidos',
  'Saudi Arabia': 'Arábia Saudita',
  'South Africa': 'África do Sul',
  'Egypt': 'Egito',
  'Morocco': 'Marrocos',
  'Nigeria': 'Nigéria',
  'Kenya': 'Quênia',
  'Thailand': 'Tailândia',
  'Singapore': 'Singapura',
  'Malaysia': 'Malásia',
  'Indonesia': 'Indonésia',
  'Philippines': 'Filipinas',
  'Vietnam': 'Vietnã',
  'New Zealand': 'Nova Zelândia',
  'Ireland': 'Irlanda',
  'Iceland': 'Islândia',
  'Luxembourg': 'Luxemburgo',
  'Monaco': 'Mônaco',
  'Croatia': 'Croácia',
  'Slovenia': 'Eslovênia',
  'Slovakia': 'Eslováquia',
  'Lithuania': 'Lituânia',
  'Latvia': 'Letônia',
  'Estonia': 'Estônia',
  'Romania': 'Romênia',
  'Bulgaria': 'Bulgária',
  'Serbia': 'Sérvia',
  'Bosnia and Herzegovina': 'Bósnia e Herzegovina',
  'North Macedonia': 'Macedônia do Norte',
  'Montenegro': 'Montenegro',
  'Albania': 'Albânia',
  'Malta': 'Malta',
  'Cyprus': 'Chipre',
  'Costa Rica': 'Costa Rica',
  'Panama': 'Panamá',
  'Guatemala': 'Guatemala',
  'Honduras': 'Honduras',
  'El Salvador': 'El Salvador',
  'Nicaragua': 'Nicarágua',
  'Cuba': 'Cuba',
  'Dominican Republic': 'República Dominicana',
  'Jamaica': 'Jamaica',
  'Trinidad and Tobago': 'Trinidad e Tobago',
  'Barbados': 'Barbados',
  'Bahamas': 'Bahamas',
  'Puerto Rico': 'Porto Rico'
};

// Países mais populares para influenciadores (em ordem de popularidade)
const popularCountryCodes = [
  'BR',  // Brasil
  'US',  // Estados Unidos
  'PT',  // Portugal
  'ES',  // Espanha
  'AR',  // Argentina
  'MX',  // México
  'CO',  // Colômbia
  'CL',  // Chile
  'FR',  // França
  'GB',  // Reino Unido
  'DE',  // Alemanha
  'IT',  // Itália
  'CA',  // Canadá
  'AU',  // Austrália
  'JP',  // Japão
  'KR',  // Coreia do Sul
  'PE',  // Peru
  'UY',  // Uruguai
  'PY',  // Paraguai
  'BO',  // Bolívia
  'VE',  // Venezuela
  'EC',  // Equador
];



// Converter dados da biblioteca para nossa interface
export const getAllCountries = (): Country[] => {
  return countries.map(country => {
    const nameInPortuguese = countryNamesInPortuguese[country.name.common] || country.name.common;
    const isPopular = popularCountryCodes.includes(country.cca2);
    
    return {
      code: country.cca2,
      name: nameInPortuguese,
      nameEn: country.name.common,
      region: country.region,
      subregion: country.subregion || '',
      popular: isPopular
    };
  }).sort((a, b) => {
    // Ordenar: países populares primeiro, depois alfabeticamente
    if (a.popular && !b.popular) return -1;
    if (!a.popular && b.popular) return 1;
    return a.name.localeCompare(b.name, 'pt-BR');
  });
};

// Obter apenas países populares
export const getPopularCountries = (): Country[] => {
  return getAllCountries().filter(country => country.popular);
};

// Obter países por região
export const getCountriesByRegion = (region: string): Country[] => {
  return getAllCountries().filter(country => country.region === region);
};

// Obter países da América do Sul
export const getSouthAmericanCountries = (): Country[] => {
  return getCountriesByRegion('Americas').filter(country => 
    country.subregion === 'South America'
  );
};

// Obter países da América do Norte
export const getNorthAmericanCountries = (): Country[] => {
  return getCountriesByRegion('Americas').filter(country => 
    country.subregion === 'Northern America'
  );
};

// Obter países europeus
export const getEuropeanCountries = (): Country[] => {
  return getCountriesByRegion('Europe');
};

// Buscar país por código
export const getCountryByCode = (code: string): Country | undefined => {
  return getAllCountries().find(country => country.code === code);
};

// Buscar país por nome
export const getCountryByName = (name: string): Country | undefined => {
  return getAllCountries().find(country => 
    country.name.toLowerCase() === name.toLowerCase() ||
    country.nameEn.toLowerCase() === name.toLowerCase()
  );
};

// Para compatibilidade com código existente - retorna apenas os nomes
export const getCountryNames = (): string[] => {
  return getAllCountries().map(country => country.name);
};

// Para compatibilidade - retorna apenas países populares como nomes
export const getPopularCountryNames = (): string[] => {
  return getPopularCountries().map(country => country.name);
};

// Constante para uso direto (compatibilidade)
export const COUNTRIES = getCountryNames();
export const POPULAR_COUNTRIES = getPopularCountryNames(); 

