import { NextRequest, NextResponse } from 'next/server';
import { ProposalService } from '@/services/proposal-service';
// Firebase Auth removido em favor do Clerk
// import { adminAuth } from '@/lib/firebase-admin';

// GET - Listar colaboradores de uma proposta
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;

    // Verificar se o usuário tem acesso à proposta
    const hasAccess = await ProposalService.canUserAccessProposal(proposalId, userId);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Sem permissão para acessar esta proposta' },
        { status: 403 }
      );
    }

    // Buscar colaboradores da proposta
    const proposal = await ProposalService.getProposalById(proposalId);
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    // Extrair colaboradores do documento da proposta
    const collaborators = (proposal as any).collaborators || [];

    return NextResponse.json({
      success: true,
      data: {
        collaborators,
        totalCount: collaborators.length
      }
    });

  } catch (error) {
    console.error('Erro ao buscar colaboradores:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Adicionar colaborador a uma proposta
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: proposalId } = await params;
    
    // Verificar autenticação
    const authHeader = request.headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação requerido' },
        { status: 401 }
      );
    }

    const idToken = authHeader.split('Bearer ')[1];
    let decodedToken;
    
    try {
      decodedToken = await adminAuth.verifyIdToken(idToken);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    const userId = decodedToken.uid;

    // Verificar se o usuário é proprietário da proposta
    const proposal = await ProposalService.getProposalById(proposalId);
    if (!proposal) {
      return NextResponse.json(
        { success: false, error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    if (proposal.criadoPor !== userId && proposal.brandId !== userId) {
      return NextResponse.json(
        { success: false, error: 'Apenas o proprietário pode adicionar colaboradores' },
        { status: 403 }
      );
    }

    // Obter dados do colaborador
    const body = await request.json();
    const { userEmail, userName, role = 'viewer' } = body;

    if (!userEmail || !userName) {
      return NextResponse.json(
        { success: false, error: 'Email e nome do usuário são obrigatórios' },
        { status: 400 }
      );
    }

    if (!['editor', 'viewer'].includes(role)) {
      return NextResponse.json(
        { success: false, error: 'Role deve ser "editor" ou "viewer"' },
        { status: 400 }
      );
    }

    // TODO: Verificar se o userEmail existe no sistema
    // Por enquanto, vamos assumir que existe e usar o email como userId temporário
    const collaboratorUserId = userEmail; // Em produção, buscar userId real

    // Adicionar colaborador
    await ProposalService.addCollaboratorToProposal(proposalId, {
      userId: collaboratorUserId,
      userEmail,
      userName,
      role: role as 'editor' | 'viewer',
      addedBy: userId
    });

    return NextResponse.json({
      success: true,
      data: {
        message: 'Colaborador adicionado com sucesso',
        collaborator: {
          userEmail,
          userName,
          role,
          addedAt: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Erro ao adicionar colaborador:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 