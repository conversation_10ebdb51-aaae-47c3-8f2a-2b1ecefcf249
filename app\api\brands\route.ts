import { NextRequest, NextResponse } from 'next/server';
import { withUserIsolation, withCreateIsolation } from '@/lib/middleware/user-isolation';
import { BrandService } from '@/services/brand-service';
import { CreateBrandSchema, UpdateBrandSchema } from '@/lib/validation/brand';
import { CreateBrandData } from '@/types/brand';

export const dynamic = 'force-dynamic';

/**
 * 🔍 GET /api/brands - Buscar marcas do usuário autenticado
 * FASE 4.2: API atualizada com isolamento automático por userId
 */
export const GET = withUserIsolation(async (req: NextRequest, userId: string) => {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc';

    // Buscar marcas do usuário
    let brands = await BrandService.getBrandsByUserId(userId);
    
    // Aplicar filtro de busca se fornecido
    if (search) {
      brands = brands.filter(brand => 
        brand.name.toLowerCase().includes(search.toLowerCase()) ||
        brand.industry?.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Aplicar ordenação
    brands.sort((a, b) => {
      const aValue = (a as any)[sortBy] || '';
      const bValue = (b as any)[sortBy] || '';
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      }
      return aValue < bValue ? 1 : -1;
    });
    
    // Aplicar paginação
    const total = brands.length;
    const startIndex = (page - 1) * limit;
    const paginatedBrands = brands.slice(startIndex, startIndex + limit);
    
    const result = {
      brands: paginatedBrands,
      total
    };
    
    return NextResponse.json({
      success: true, 
      data: result.brands,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit)
      }
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error('[BRANDS_API_GET]', { userId, error: errorMessage });
    
    return NextResponse.json({ 
      error: 'Erro ao buscar marcas',
      details: errorMessage 
    }, { status: 500 });
  }
});

/**
 * 📝 POST /api/brands - Criar nova marca para o usuário autenticado
 * FASE 4.2: API com validação automática e isolamento por userId
 */
export const POST = withCreateIsolation(async (req: NextRequest, userId: string, context, data) => {
  try {
    // Validar dados com Zod
    const validatedData = CreateBrandSchema.parse(data);
    
    // Criar marca com isolamento automático (passar userId como parâmetro separado)
    const createdBrand = await BrandService.createBrand(validatedData, userId);
    
    console.log('[BRANDS_API] Marca criada com sucesso para usuário:', userId, {
      brandId: createdBrand.id,
      brandName: createdBrand.name
    });
    
    return NextResponse.json({
      success: true,
      data: createdBrand,
      message: 'Marca criada com sucesso'
    }, { status: 201 });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    console.error('[BRANDS_API_POST]', { userId, error: errorMessage });
    
    // Tratar erros de validação do Zod
    if ((error as any).name === 'ZodError') {
      const zodErrors = (error as any).errors;
      let specificError = 'Dados inválidos';
      
      // Verificar erros específicos para dar feedback melhor
      const urlErrors = zodErrors.filter((err: any) => 
        err.message.includes('URL inválida') || 
        (err.path && ['website', 'logo'].includes(err.path[0]))
      );
      
      if (urlErrors.length > 0) {
        specificError = 'URL inválida: verifique se website e logo são URLs válidas (ex: https://exemplo.com)';
      } else if (zodErrors.some((err: any) => err.path && err.path[0] === 'name')) {
        specificError = 'Nome da marca é obrigatório e deve ter pelo menos 2 caracteres';
      } else {
        // Usar a primeira mensagem de erro específica
        specificError = zodErrors[0]?.message || 'Dados inválidos';
      }
      
      return NextResponse.json({
        error: specificError,
        details: zodErrors
      }, { status: 400 });
    }
    
    // Tratar erro de nome duplicado
    if (errorMessage.includes('já existe')) {
      return NextResponse.json({
        error: 'Uma marca com este nome já existe',
        details: errorMessage
      }, { status: 409 });
    }
    
    return NextResponse.json({ 
      error: 'Erro ao criar marca',
      details: errorMessage 
    }, { status: 500 });
  }
});


