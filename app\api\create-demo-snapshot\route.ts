import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shareToken = searchParams.get('shareToken') || 'demo-' + Date.now();
    const influencerId = searchParams.get('influencerId') || 'JMTS1denFFLWVGJ7UARJ';
    
    console.log(`🧪 [DEMO SNAPSHOT] Criando snapshot demonstrativo para: ${influencerId}`);

    // 1. Buscar dados reais do influenciador
    const influencerDoc = await db.collection('influencers').doc(influencerId).get();
    
    if (!influencerDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'Influenciador não encontrado'
      }, { status: 404 });
    }

    const influencerData = influencerDoc.data()!;

    // 2. Buscar pricing real das subcoleções
    const pricingCollection = db.collection('influencers').doc(influencerId).collection('pricing');
    const pricingSnapshot = await pricingCollection.where('isActive', '==', true).get();
    
    let currentPricing: any = null;
    if (!pricingSnapshot.empty) {
      const pricingDoc = pricingSnapshot.docs[0];
      currentPricing = {
        id: pricingDoc.id,
        ...pricingDoc.data()
      };
    }

    // 3. Criar estrutura de snapshot demonstrativa
    const batch = db.batch();
    const userId = 'demo-user-' + Date.now();

    // Criar documento de compartilhamento
    const sharingData = {
      token: shareToken,
      proposalId: 'demo-proposal-' + Date.now(),
      createdBy: userId,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      isPublic: true,
      isActive: true,
      accessCount: 0
    };

    const sharingRef = db.collection('proposal_sharings').doc(shareToken);
    batch.set(sharingRef, sharingData);

    // 4. Criar documento principal do snapshot
    const mainSnapshotData = {
      name: influencerData.name,
      email: influencerData.email,
      avatar: influencerData.avatar,
      bio: influencerData.bio,
      totalFollowers: influencerData.totalFollowers,
      engagementRate: influencerData.engagementRate,
      isVerified: influencerData.isVerified,
      
      // Metadados do snapshot
      originalInfluencerId: influencerId,
      capturedAt: new Date(),
      version: '2.0', // Nova versão com subcoleções
      source: 'demo',
      createdBy: userId,
      proposalId: sharingData.proposalId,
      shareToken: shareToken
    };

    const mainSnapshotRef = sharingRef.collection('snapshots').doc(influencerId);
    batch.set(mainSnapshotRef, mainSnapshotData);

    // 5. Criar subcoleção pricing no snapshot (se existir)
    if (currentPricing) {
      const pricingSnapshotRef = mainSnapshotRef.collection('pricing').doc('current');
      const pricingData = {
        userId: currentPricing.userId,
        services: currentPricing.services,
        isActive: currentPricing.isActive,
        originalPricingId: currentPricing.id,
        capturedAt: new Date(),
        snapshotCreatedBy: userId,
        shareToken: shareToken
      };
      
      batch.set(pricingSnapshotRef, pricingData);
    }

    // 6. Executar todas as operações
    await batch.commit();

    const result = {
      success: true,
      shareToken,
      influencerId,
      timestamp: new Date().toISOString(),
      
      created: {
        sharing: true,
        mainSnapshot: true,
        pricingSubcollection: !!currentPricing
      },
      
      testUrls: {
        verifyStructure: `http://localhost:3000/api/test-snapshot-structure?shareToken=${shareToken}`,
        verifySpecific: `http://localhost:3000/api/test-snapshot-structure?shareToken=${shareToken}&influencerId=${influencerId}`
      }
    };

    console.log(`🎉 [DEMO SNAPSHOT] Snapshot demonstrativo criado!`);
    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ [DEMO SNAPSHOT] Erro:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno'
    }, { status: 500 });
  }
} 

