// Script para debug dos screenshots no Firestore
// Executa: node scripts/debug-screenshots.js [influencerId]

const admin = require('firebase-admin');

// Inicializar Firebase Admin se ainda não foi inicializado
if (!admin.apps.length) {
  const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: 'deumatch-demo.firebasestorage.app'
  });
}

const db = admin.firestore();

async function debugScreenshots(influencerId) {
  try {
    console.log(`\n🔍 [DEBUG] Verificando screenshots para influencer: ${influencerId}`);
    console.log('=' + '='.repeat(60));

    // Buscar a subcoleção de screenshots
    const screenshotsCollection = db
      .collection('influencers')
      .doc(influencerId)
      .collection('screenshots');

    const snapshot = await screenshotsCollection.get();

    if (snapshot.empty) {
      console.log('❌ Nenhum documento de screenshot encontrado');
      return;
    }

    console.log(`✅ Encontrados ${snapshot.docs.length} documentos de plataformas`);
    console.log('');

    let totalScreenshots = 0;
    
    snapshot.docs.forEach((doc, docIndex) => {
      const data = doc.data();
      const platformName = doc.id;
      const urls = data?.urls || [];
      const lastUpdated = data?.lastUpdated;
      
      console.log(`📱 Plataforma ${docIndex + 1}: ${platformName}`);
      console.log(`   📸 Screenshots: ${urls.length}`);
      console.log(`   📅 Última atualização: ${lastUpdated ? lastUpdated.toDate() : 'N/A'}`);
      
      if (urls.length > 0) {
        console.log(`   🔗 URLs:`);
        urls.forEach((url, urlIndex) => {
          console.log(`      ${urlIndex + 1}. ${url}`);
        });
      }
      
      console.log('   📋 Estrutura completa do documento:');
      console.log('   ', JSON.stringify(data, null, 2));
      console.log('');
      
      totalScreenshots += urls.length;
    });

    console.log(`📊 RESUMO:`);
    console.log(`   Total de plataformas: ${snapshot.docs.length}`);
    console.log(`   Total de screenshots: ${totalScreenshots}`);
    console.log('=' + '='.repeat(60));

  } catch (error) {
    console.error('❌ Erro ao verificar screenshots:', error);
  }
}

async function listAllInfluencersWithScreenshots() {
  try {
    console.log('\n🔍 [DEBUG] Listando todos os influencers com screenshots...');
    console.log('=' + '='.repeat(60));

    const influencersSnapshot = await db.collection('influencers').get();
    
    if (influencersSnapshot.empty) {
      console.log('❌ Nenhum influencer encontrado');
      return;
    }

    console.log(`📋 Verificando ${influencersSnapshot.docs.length} influencers...`);
    console.log('');

    const influencersWithScreenshots = [];

    for (const influencerDoc of influencersSnapshot.docs) {
      const influencerId = influencerDoc.id;
      const influencerData = influencerDoc.data();
      
      // Verificar se tem screenshots
      const screenshotsSnapshot = await db
        .collection('influencers')
        .doc(influencerId)
        .collection('screenshots')
        .get();

      if (!screenshotsSnapshot.empty) {
        let totalScreenshots = 0;
        const platforms = [];

        screenshotsSnapshot.docs.forEach(doc => {
          const data = doc.data();
          const urls = data?.urls || [];
          platforms.push({
            name: doc.id,
            count: urls.length
          });
          totalScreenshots += urls.length;
        });

        influencersWithScreenshots.push({
          id: influencerId,
          name: influencerData.name || 'Nome não encontrado',
          totalScreenshots,
          platforms
        });
      }
    }

    console.log(`✅ Encontrados ${influencersWithScreenshots.length} influencers com screenshots:`);
    console.log('');

    influencersWithScreenshots.forEach((influencer, index) => {
      console.log(`${index + 1}. ${influencer.name} (ID: ${influencer.id})`);
      console.log(`   📸 Total de screenshots: ${influencer.totalScreenshots}`);
      console.log(`   📱 Plataformas: ${influencer.platforms.map(p => `${p.name} (${p.count})`).join(', ')}`);
      console.log('');
    });

    console.log('=' + '='.repeat(60));

    return influencersWithScreenshots;

  } catch (error) {
    console.error('❌ Erro ao listar influencers:', error);
  }
}

// Função principal
async function main() {
  const influencerId = process.argv[2];

  if (influencerId) {
    // Debug de um influencer específico
    await debugScreenshots(influencerId);
  } else {
    // Listar todos os influencers com screenshots
    const influencersWithScreenshots = await listAllInfluencersWithScreenshots();
    
    if (influencersWithScreenshots && influencersWithScreenshots.length > 0) {
      console.log('\n💡 Para debug detalhado de um influencer específico, execute:');
      console.log(`node scripts/debug-screenshots.js ${influencersWithScreenshots[0].id}`);
    }
  }

  process.exit(0);
}

// Executar script
main().catch(console.error); 