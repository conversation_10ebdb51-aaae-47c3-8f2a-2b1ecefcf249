import { useState } from 'react';
import { Brand } from '@/types/brand';
import { BrandService } from '@/services/brand-service';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'react-hot-toast';

interface BrandEditFormProps {
  brand: Brand;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function BrandEditForm({ brand, onSuccess, onCancel }: BrandEditFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: brand.name,
    logo: brand.logo
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await BrandService.update(brand.id!, formData);
      toast.success('Marca atualizada com sucesso!');
      onSuccess?.();
    } catch (error) {
      console.error('Erro ao atualizar marca:', error);
      toast.error('Erro ao atualizar marca. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-200">
          Nome da Marca
        </label>
        <Input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          required
          className="mt-1"
          placeholder="Digite o nome da marca"
        />
      </div>

      <div>
        <label htmlFor="logo" className="block text-sm font-medium text-gray-200">
          URL do Logo
        </label>
        <Input
          id="logo"
          type="url"
          value={formData.logo}
          onChange={(e) => setFormData(prev => ({ ...prev, logo: e.target.value }))}
          required
          className="mt-1"
          placeholder="Digite a URL do logo"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Salvando...' : 'Salvar'}
        </Button>
      </div>
    </form>
  );
} 

