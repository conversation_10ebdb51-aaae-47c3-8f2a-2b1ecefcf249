import { backupCollection, backupFullSystem } from './backup-firestore';
import { migrateBrands, validateBrandsMigration } from './migrate-brands';
import { migrateCampaigns, validateCampaignBrandRelationships } from './migrate-campaigns';

/**
 * 🚀 SCRIPT PRINCIPAL DE MIGRAÇÃO
 * FASE 5.3: Orquestrador completo da migração de dados
 */

// Interface para resultado geral da migração
interface FullMigrationResult {
  startTime: Date;
  endTime: Date;
  duration: number;
  backupPath?: string;
  phases: {
    backup: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
    brands: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
    campaigns: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
    influencers: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
    groups: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
    notes: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
    validation: { status: 'success' | 'failed' | 'skipped'; error?: string; details?: any };
  };
  summary: {
    totalCollections: number;
    totalDocuments: number;
    totalMigrated: number;
    totalErrors: number;
    successRate: number;
  };
}

// Configurações globais de migração
const MIGRATION_CONFIG = {
  enableBackup: true,
  continueOnError: false,
  dryRun: false,
  batchSize: 100,
  enableValidation: true,
  collections: [
    { name: 'brands', enabled: true, priority: 1 },
    { name: 'campaigns', enabled: true, priority: 2 },
    { name: 'influencers', enabled: true, priority: 3 },
    { name: 'groups', enabled: true, priority: 4 },
    { name: 'notes', enabled: true, priority: 5 }
  ]
};

/**
 * Executar migração completa do sistema
 */
export async function runFullMigration(options: {
  dryRun?: boolean;
  enableBackup?: boolean;
  continueOnError?: boolean;
  collections?: string[];
  batchSize?: number;
} = {}): Promise<FullMigrationResult> {
  const config = { ...MIGRATION_CONFIG, ...options };
  const startTime = new Date();
  
  console.log('🚀 INICIANDO MIGRAÇÃO COMPLETA DO SISTEMA');
  console.log('==========================================');
  console.log(`⏰ Início: ${startTime.toLocaleString('pt-BR')}`);
  console.log(`🔧 Modo: ${config.dryRun ? 'DRY RUN' : 'EXECUÇÃO REAL'}`);
  console.log(`📦 Backup: ${config.enableBackup ? 'HABILITADO' : 'DESABILITADO'}`);
  console.log(`🔄 Continuar em erro: ${config.continueOnError ? 'SIM' : 'NÃO'}`);
  console.log('==========================================\n');
  
  const result: FullMigrationResult = {
    startTime,
    endTime: new Date(),
    duration: 0,
    phases: {
      backup: { status: 'skipped' },
      brands: { status: 'skipped' },
      campaigns: { status: 'skipped' },
      influencers: { status: 'skipped' },
      groups: { status: 'skipped' },
      notes: { status: 'skipped' },
      validation: { status: 'skipped' }
    },
    summary: {
      totalCollections: 0,
      totalDocuments: 0,
      totalMigrated: 0,
      totalErrors: 0,
      successRate: 0
    }
  };
  
  try {
    // 1. FASE DE BACKUP
    if (config.enableBackup) {
      console.log('📦 FASE 1: BACKUP COMPLETO');
      console.log('============================');
      
      try {
        const backupPath = await backupFullSystem();
        result.backupPath = backupPath;
        result.phases.backup = { status: 'success', details: { path: backupPath } };
        console.log(`✅ Backup concluído em: ${backupPath}\n`);
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido no backup';
        result.phases.backup = { status: 'failed', error: errorMsg };
        console.error('❌ Erro no backup:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Backup falhou: ${errorMsg}`);
        }
        console.log('⚠️ Continuando sem backup...\n');
      }
    }
    
    // 2. FASE DE MIGRAÇÃO DE BRANDS
    if (shouldMigrateCollection('brands', config)) {
      console.log('🏢 FASE 2: MIGRAÇÃO DE BRANDS');
      console.log('===============================');
      
      try {
        const brandsResult = await migrateBrands({
          dryRun: config.dryRun,
          backupFirst: false, // Já fizemos backup completo
          batchSize: config.batchSize
        });
        
        result.phases.brands = { status: 'success', details: brandsResult };
        result.summary.totalMigrated += brandsResult.migratedCount;
        result.summary.totalErrors += brandsResult.errorCount;
        console.log('✅ Migração de brands concluída\n');
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido na migração de brands';
        result.phases.brands = { status: 'failed', error: errorMsg };
        console.error('❌ Erro na migração de brands:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Migração de brands falhou: ${errorMsg}`);
        }
        console.log('⚠️ Continuando para próxima fase...\n');
      }
    }
    
    // 3. FASE DE MIGRAÇÃO DE CAMPAIGNS
    if (shouldMigrateCollection('campaigns', config)) {
      console.log('📈 FASE 3: MIGRAÇÃO DE CAMPAIGNS');
      console.log('==================================');
      
      try {
        const campaignsResult = await migrateCampaigns({
          dryRun: config.dryRun,
          backupFirst: false,
          batchSize: config.batchSize,
          validateBrandRelationships: true
        });
        
        result.phases.campaigns = { status: 'success', details: campaignsResult };
        result.summary.totalMigrated += campaignsResult.migratedCount;
        result.summary.totalErrors += campaignsResult.errorCount;
        console.log('✅ Migração de campaigns concluída\n');
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido na migração de campaigns';
        result.phases.campaigns = { status: 'failed', error: errorMsg };
        console.error('❌ Erro na migração de campaigns:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Migração de campaigns falhou: ${errorMsg}`);
        }
        console.log('⚠️ Continuando para próxima fase...\n');
      }
    }
    
    // 4. FASE DE MIGRAÇÃO DE INFLUENCERS
    if (shouldMigrateCollection('influencers', config)) {
      console.log('👥 FASE 4: MIGRAÇÃO DE INFLUENCERS');
      console.log('===================================');
      
      try {
        const influencersResult = await migrateInfluencers({
          dryRun: config.dryRun,
          batchSize: config.batchSize
        });
        
        result.phases.influencers = { status: 'success', details: influencersResult };
        result.summary.totalMigrated += influencersResult.migratedCount;
        result.summary.totalErrors += influencersResult.errorCount;
        console.log('✅ Migração de influencers concluída\n');
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido na migração de influencers';
        result.phases.influencers = { status: 'failed', error: errorMsg };
        console.error('❌ Erro na migração de influencers:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Migração de influencers falhou: ${errorMsg}`);
        }
        console.log('⚠️ Continuando para próxima fase...\n');
      }
    }
    
    // 5. FASE DE MIGRAÇÃO DE GROUPS
    if (shouldMigrateCollection('groups', config)) {
      console.log('👥 FASE 5: MIGRAÇÃO DE GROUPS');
      console.log('===============================');
      
      try {
        const groupsResult = await migrateGroups({
          dryRun: config.dryRun,
          batchSize: config.batchSize
        });
        
        result.phases.groups = { status: 'success', details: groupsResult };
        result.summary.totalMigrated += groupsResult.migratedCount;
        result.summary.totalErrors += groupsResult.errorCount;
        console.log('✅ Migração de groups concluída\n');
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido na migração de groups';
        result.phases.groups = { status: 'failed', error: errorMsg };
        console.error('❌ Erro na migração de groups:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Migração de groups falhou: ${errorMsg}`);
        }
        console.log('⚠️ Continuando para próxima fase...\n');
      }
    }
    
    // 6. FASE DE MIGRAÇÃO DE NOTES
    if (shouldMigrateCollection('notes', config)) {
      console.log('📝 FASE 6: MIGRAÇÃO DE NOTES');
      console.log('==============================');
      
      try {
        const notesResult = await migrateNotes({
          dryRun: config.dryRun,
          batchSize: config.batchSize
        });
        
        result.phases.notes = { status: 'success', details: notesResult };
        result.summary.totalMigrated += notesResult.migratedCount;
        result.summary.totalErrors += notesResult.errorCount;
        console.log('✅ Migração de notes concluída\n');
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido na migração de notes';
        result.phases.notes = { status: 'failed', error: errorMsg };
        console.error('❌ Erro na migração de notes:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Migração de notes falhou: ${errorMsg}`);
        }
        console.log('⚠️ Continuando para validação...\n');
      }
    }
    
    // 7. FASE DE VALIDAÇÃO FINAL
    if (config.enableValidation && !config.dryRun) {
      console.log('🔍 FASE 7: VALIDAÇÃO FINAL');
      console.log('============================');
      
      try {
        const validationResult = await validateMigration();
        result.phases.validation = { status: 'success', details: validationResult };
        console.log('✅ Validação concluída\n');
        
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido na validação';
        result.phases.validation = { status: 'failed', error: errorMsg };
        console.error('❌ Erro na validação:', error);
        
        if (!config.continueOnError) {
          throw new Error(`Validação falhou: ${errorMsg}`);
        }
      }
    }
    
    // Finalizar resultado
    result.endTime = new Date();
    result.duration = result.endTime.getTime() - result.startTime.getTime();
    
    // Calcular estatísticas finais
    const successfulPhases = Object.values(result.phases).filter(phase => phase.status === 'success').length;
    const totalPhases = Object.values(result.phases).filter(phase => phase.status !== 'skipped').length;
    result.summary.successRate = totalPhases > 0 ? (successfulPhases / totalPhases) * 100 : 0;
    
    // Relatório final
    printFinalReport(result, config);
    
    return result;
    
  } catch (error) {
    result.endTime = new Date();
    result.duration = result.endTime.getTime() - result.startTime.getTime();
    
    console.error('\n❌ MIGRAÇÃO INTERROMPIDA POR ERRO FATAL');
    console.error('=========================================');
    console.error('Erro:', error);
    
    if (result.backupPath) {
      console.log(`\n📦 Backup disponível em: ${result.backupPath}`);
      console.log('🔄 Use o script de rollback se necessário');
    }
    
    throw error;
  }
}

/**
 * Validar migração completa
 */
export async function validateMigration(): Promise<{
  collections: Array<{
    name: string;
    total: number;
    withUserId: number;
    withoutUserId: number;
    migrationComplete: boolean;
    issues: any[];
  }>;
  summary: {
    totalCollections: number;
    totalDocuments: number;
    fullyMigrated: number;
    partiallyMigrated: number;
    notMigrated: number;
    overallSuccess: boolean;
  };
}> {
  console.log('🔍 Validando migração completa...');
  
  const collections = ['brands', 'campaigns', 'influencers', 'groups', 'notes'];
  const results = [];
  
  for (const collectionName of collections) {
    console.log(`📊 Validando ${collectionName}...`);
    
    try {
      const snapshot = await db.collection(collectionName).get();
      const total = snapshot.size;
      const withUserId = snapshot.docs.filter(doc => doc.data().userId).length;
      const withoutUserId = total - withUserId;
      const issues: any[] = [];
      
      // Verificar qualidade dos userIds
      for (const doc of snapshot.docs) {
        const data = doc.data();
        if (data.userId) {
          // Verificar se userId é válido (existe na coleção users)
          const userDoc = await db.collection('users').doc(data.userId).get();
          if (!userDoc.exists) {
            issues.push({
              id: doc.id,
              issue: `UserId inválido: ${data.userId}`,
              severity: 'high'
            });
          }
        }
      }
      
      results.push({
        name: collectionName,
        total,
        withUserId,
        withoutUserId,
        migrationComplete: withoutUserId === 0 && issues.length === 0,
        issues
      });
      
      console.log(`  ✅ ${withUserId}/${total} documentos com userId`);
      if (issues.length > 0) {
        console.log(`  ⚠️ ${issues.length} problemas encontrados`);
      }
      
    } catch (error) {
      console.error(`  ❌ Erro ao validar ${collectionName}:`, error);
      results.push({
        name: collectionName,
        total: 0,
        withUserId: 0,
        withoutUserId: 0,
        migrationComplete: false,
        issues: [{ error: error.message, severity: 'critical' }]
      });
    }
  }
  
  // Resumo geral
  const totalDocuments = results.reduce((sum, r) => sum + r.total, 0);
  const fullyMigrated = results.filter(r => r.migrationComplete).length;
  const partiallyMigrated = results.filter(r => r.withUserId > 0 && !r.migrationComplete).length;
  const notMigrated = results.filter(r => r.withUserId === 0).length;
  const overallSuccess = fullyMigrated === results.length;
  
  const summary = {
    totalCollections: results.length,
    totalDocuments,
    fullyMigrated,
    partiallyMigrated,
    notMigrated,
    overallSuccess
  };
  
  console.log('\n📊 RESUMO DA VALIDAÇÃO:');
  console.log(`📁 Coleções totalmente migradas: ${fullyMigrated}/${results.length}`);
  console.log(`📄 Total de documentos: ${totalDocuments}`);
  console.log(`✅ Status geral: ${overallSuccess ? 'SUCESSO' : 'PROBLEMAS ENCONTRADOS'}`);
  
  return { collections: results, summary };
}

// Implementações placeholder para outras migrações
async function migrateInfluencers(options: any): Promise<any> {
  console.log('⚠️ Migração de influencers ainda não implementada');
  return { migratedCount: 0, errorCount: 0 };
}

async function migrateGroups(options: any): Promise<any> {
  console.log('⚠️ Migração de groups ainda não implementada');
  return { migratedCount: 0, errorCount: 0 };
}

async function migrateNotes(options: any): Promise<any> {
  console.log('⚠️ Migração de notes ainda não implementada');
  return { migratedCount: 0, errorCount: 0 };
}

// Funções auxiliares
function shouldMigrateCollection(collectionName: string, config: any): boolean {
  if (config.collections && Array.isArray(config.collections)) {
    return config.collections.includes(collectionName);
  }
  
  const collectionConfig = MIGRATION_CONFIG.collections.find(c => c.name === collectionName);
  return collectionConfig?.enabled ?? false;
}

function printFinalReport(result: FullMigrationResult, config: any): void {
  console.log('\n🎉 MIGRAÇÃO COMPLETA FINALIZADA');
  console.log('================================');
  console.log(`⏰ Início: ${result.startTime.toLocaleString('pt-BR')}`);
  console.log(`⏰ Fim: ${result.endTime.toLocaleString('pt-BR')}`);
  console.log(`⏱️ Duração: ${formatDuration(result.duration)}`);
  console.log(`🔧 Modo: ${config.dryRun ? 'DRY RUN' : 'EXECUÇÃO REAL'}`);
  
  if (result.backupPath) {
    console.log(`📦 Backup: ${result.backupPath}`);
  }
  
  console.log('\n📊 RESUMO POR FASE:');
  Object.entries(result.phases).forEach(([phase, details]) => {
    const icon = details.status === 'success' ? '✅' : details.status === 'failed' ? '❌' : '⏭️';
    console.log(`${icon} ${phase.toUpperCase()}: ${details.status.toUpperCase()}`);
    if (details.error) {
      console.log(`   Erro: ${details.error}`);
    }
  });
  
  console.log('\n📈 ESTATÍSTICAS GERAIS:');
  console.log(`📄 Total migrado: ${result.summary.totalMigrated} documentos`);
  console.log(`❌ Total de erros: ${result.summary.totalErrors}`);
  console.log(`📊 Taxa de sucesso: ${result.summary.successRate.toFixed(1)}%`);
  
  if (config.dryRun) {
    console.log('\n🔍 ESTA FOI UMA EXECUÇÃO EM MODO DRY RUN');
    console.log('Nenhuma alteração foi feita no banco de dados.');
    console.log('Execute novamente sem --dry-run para aplicar as mudanças.');
  } else if (result.summary.successRate === 100) {
    console.log('\n🎊 MIGRAÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('Todos os dados foram migrados corretamente.');
  } else {
    console.log('\n⚠️ MIGRAÇÃO CONCLUÍDA COM PROBLEMAS');
    console.log('Verifique os logs e considere executar correções manuais.');
  }
}

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

// Função principal para execução via linha de comando
export async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const noBackup = args.includes('--no-backup');
  const continueOnError = args.includes('--continue-on-error');
  const collectionsArg = args.find(arg => arg.startsWith('--collections='));
  const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
  
  const collections = collectionsArg ? collectionsArg.split('=')[1].split(',') : undefined;
  const batchSize = batchSizeArg ? parseInt(batchSizeArg.split('=')[1]) : undefined;
  
  try {
    console.log('🚀 Iniciando migração completa do sistema...\n');
    
    const result = await runFullMigration({
      dryRun,
      enableBackup: !noBackup,
      continueOnError,
      collections,
      batchSize
    });
    
    // Determinar código de saída baseado no resultado
    const hasErrors = result.summary.totalErrors > 0;
    const hasFailedPhases = Object.values(result.phases).some(phase => phase.status === 'failed');
    
    if (hasErrors || hasFailedPhases) {
      console.log('\n⚠️ Migração concluída com problemas. Código de saída: 1');
      process.exit(1);
    } else {
      console.log('\n✅ Migração concluída com sucesso. Código de saída: 0');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('\n❌ Erro fatal na migração:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
} 

