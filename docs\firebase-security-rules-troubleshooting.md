# 🔧 Firebase Security Rules - Troubleshooting

## 🎯 Guia Completo de Solução de Problemas

Este documento fornece soluções para problemas comuns com Firebase Security Rules após a implementação do isolamento multi-tenancy.

## 🚨 Problemas Mais Comuns

### 1. **Erro: "Permission denied"**

#### **Sintomas:**
- Usuários não conseguem acessar seus próprios dados
- APIs retornam erro 403 (Forbidden)
- Console mostra "Missing or insufficient permissions"

#### **Causas Possíveis:**
```javascript
// ❌ PROBLEMA: userId não está sendo definido
{
  name: "<PERSON><PERSON>",
  // userId: "user123" <- FALTANDO
}

// ❌ PROBLEMA: Regra muito restritiva
allow read: if isAuthenticated() && false; // Sempre nega

// ❌ PROBLEMA: Função com erro
function isOwner(userId) {
  return request.auth.uid = userId; // Usando = ao invés de ==
}
```

#### **Soluções:**

1. **Verificar se userId está sendo definido:**
```javascript
// ✅ SOLUÇÃO: Garantir userId em todos os documentos
const data = {
  name: "Marca Teste",
  userId: request.auth.uid, // Sempre incluir
  createdAt: new Date(),
  updatedAt: new Date()
};
```

2. **Validar autenticação:**
```javascript
// ✅ SOLUÇÃO: Verificar auth no frontend
const { user } = useAuth();
if (!user) {
  // Redirecionar para login
  router.push('/login');
  return;
}
```

3. **Debug das regras:**
```javascript
// ✅ SOLUÇÃO: Adicionar logs temporários
function canAccessDocument() {
  // Temporário para debug
  return isAuthenticated() && 
         resource.data.userId == request.auth.uid;
}
```

### 2. **Erro: "Document doesn't exist"**

#### **Sintomas:**
- Funções `get()` ou `exists()` falham
- Regras que dependem de outros documentos não funcionam

#### **Causas Possíveis:**
```javascript
// ❌ PROBLEMA: Path incorreto
function brandBelongsToUser(brandId) {
  return exists(/databases/$(database)/documents/brand/$(brandId)); // Singular 'brand'
}

// ❌ PROBLEMA: Document não existe realmente
function validateRelationship() {
  return brandBelongsToUser('brand_inexistente');
}
```

#### **Soluções:**

1. **Corrigir paths:**
```javascript
// ✅ SOLUÇÃO: Path correto
function brandBelongsToUser(brandId) {
  return exists(/databases/$(database)/documents/brands/$(brandId)); // Plural 'brands'
}
```

2. **Verificar existência antes de usar:**
```javascript
// ✅ SOLUÇÃO: Verificar existência
function safeBrandBelongsToUser(brandId) {
  return brandId != null && 
         brandId != '' &&
         exists(/databases/$(database)/documents/brands/$(brandId)) &&
         get(/databases/$(database)/documents/brands/$(brandId)).data.userId == request.auth.uid;
}
```

### 3. **Erro: "Rules evaluation timed out"**

#### **Sintomas:**
- Operações demoram muito para completar
- Timeout em regras complexas

#### **Causas Possíveis:**
```javascript
// ❌ PROBLEMA: Muitas operações get()
function validateAllInfluencers(influencerIds) {
  // Faz get() para cada influencer - muito lento
  return influencerIds.hasAny() && 
         influencerIds.hasAll([get(...), get(...), get(...)]);
}

// ❌ PROBLEMA: Loops implícitos
function hasValidCategories(categories) {
  // Verifica cada categoria individualmente
  return categories.hasAll([
    exists(/databases/$(database)/documents/categories/$(categories[0])),
    exists(/databases/$(database)/documents/categories/$(categories[1])),
    // ... muitas verificações
  ]);
}
```

#### **Soluções:**

1. **Limitar operações get():**
```javascript
// ✅ SOLUÇÃO: Validação básica sem gets excessivos
function validateInfluencersList(influencerIds) {
  return influencerIds is list && 
         influencerIds.size() <= 10; // Limite razoável
}
```

2. **Usar validação no backend:**
```javascript
// ✅ SOLUÇÃO: Mover validação complexa para API
// Nas regras, fazer apenas validações básicas
allow create: if hasCorrectUserId() && 
              isValidStringLength('name', 2, 100);

// No backend (API), fazer validações complexas
async function validateCampaignInfluencers(influencerIds, userId) {
  for (const id of influencerIds) {
    const influencer = await getInfluencer(id);
    if (influencer.userId !== userId) {
      throw new Error('Influencer não pertence ao usuário');
    }
  }
}
```

### 4. **Erro: "Invalid function call"**

#### **Sintomas:**
- Funções não são reconhecidas
- Erro de sintaxe nas regras

#### **Causas Possíveis:**
```javascript
// ❌ PROBLEMA: Função não definida
allow read: if canAccessDocument(); // Função não existe

// ❌ PROBLEMA: Sintaxe incorreta
function isValidEmail(email) {
  return email.match('.*@.*'); // match() não existe, usar matches()
}

// ❌ PROBLEMA: Escopo incorreto
match /brands/{brandId} {
  function localFunction() { return true; } // Função local
  allow read: if isAuthenticated(); // Função global não acessível
}
```

#### **Soluções:**

1. **Definir funções no escopo correto:**
```javascript
// ✅ SOLUÇÃO: Funções globais no início
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Funções globais aqui
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Regras depois
    match /brands/{brandId} {
      allow read: if isAuthenticated();
    }
  }
}
```

2. **Usar sintaxe correta:**
```javascript
// ✅ SOLUÇÃO: Sintaxe correta do Firestore Rules
function isValidEmail(email) {
  return email is string && email.matches('.*@.*\\..*');
}
```

## 🛠️ Ferramentas de Debug

### 1. **Firebase Console - Rules Playground**

```javascript
// Testar regras no console:
// 1. Ir para Firestore > Regras
// 2. Clicar em "Rules Playground"
// 3. Configurar teste:

{
  "auth": {
    "uid": "user123"
  },
  "method": "get",
  "path": "/brands/brand456",
  "data": {
    "userId": "user123",
    "name": "Marca Teste"
  }
}
```

### 2. **Logs do Firebase**

```javascript
// Monitorar logs em tempo real:
firebase emulators:start --import=./data --export-on-exit

// Ou no console:
// https://console.firebase.google.com/project/[PROJECT]/firestore/usage
```

### 3. **Scripts de Teste Local**

```bash
# Executar testes de segurança
node scripts/test-security-rules.js

# Validar antes do deploy
node scripts/deploy-security-rules.js
```

## 🔍 Checklist de Debug

### **Antes de Investigar:**
- [ ] Verificar se usuário está autenticado (`request.auth != null`)
- [ ] Verificar se `userId` está presente nos dados
- [ ] Verificar se paths estão corretos
- [ ] Verificar sintaxe das funções

### **Durante Investigação:**
- [ ] Usar Rules Playground para testar cenários específicos
- [ ] Adicionar logs temporários (comentários descritivos)
- [ ] Testar com dados mínimos primeiro
- [ ] Verificar permissões em desenvolvimento vs produção

### **Após Correção:**
- [ ] Executar suite completa de testes
- [ ] Verificar performance (tempo de resposta)
- [ ] Testar cenários edge cases
- [ ] Documentar a solução

## 🚨 Problemas de Performance

### **Sintomas:**
- Queries lentas
- Timeout em operações
- Alto uso de recursos

### **Soluções:**

1. **Otimizar Índices:**
```javascript
// Criar índices compostos para queries filtradas
// firebase.json
{
  "firestore": {
    "indexes": [
      {
        "collectionGroup": "brands",
        "queryScope": "COLLECTION",
        "fields": [
          { "fieldPath": "userId", "order": "ASCENDING" },
          { "fieldPath": "createdAt", "order": "DESCENDING" }
        ]
      }
    ]
  }
}
```

2. **Limitar Resultados:**
```javascript
// ✅ Usar limit() nas queries
const brands = await db.collection('brands')
  .where('userId', '==', userId)
  .limit(50) // Limitar resultados
  .get();
```

3. **Cache Estratégico:**
```javascript
// ✅ Implementar cache no frontend
const useBrands = () => {
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    // Cache por 5 minutos
    const cached = localStorage.getItem('brands');
    const cacheTime = localStorage.getItem('brands_time');
    
    if (cached && cacheTime && Date.now() - cacheTime < 300000) {
      setBrands(JSON.parse(cached));
      return;
    }
    
    fetchBrands();
  }, []);
};
```

## 📞 Quando Pedir Ajuda

### **Situações que Requerem Suporte:**
- Regras funcionam no emulator mas não em produção
- Performance degradada após deploy
- Problemas de billing relacionados a reads excessivos
- Inconsistências entre diferentes regiões

### **Informações para Incluir:**
1. **Projeto Firebase ID**
2. **Arquivo completo de regras**
3. **Exemplo específico que falha**
4. **Logs de erro completos**
5. **Dados de exemplo (sem informações sensíveis)**

### **Recursos Úteis:**
- [Firebase Documentation](https://firebase.google.com/docs/firestore/security)
- [Stack Overflow - Firebase Tag](https://stackoverflow.com/questions/tagged/firebase)
- [Firebase Community Discord](https://discord.gg/firebase)

---

## 🔄 Processo de Rollback

Se algo der errado após o deploy:

```bash
# 1. Verificar backup mais recente
ls -la backups/firestore-rules/

# 2. Restaurar backup
firebase firestore:rules:release backups/firestore-rules/firestore-rules-[TIMESTAMP].rules --project [PROJECT_ID]

# 3. Verificar se funcionou
node scripts/test-security-rules.js
``` 