import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/firebase-admin'
import { deleteScreenshot } from '@/lib/firebase-storage'

// Função para extrair usuário da requisição
async function getUserFromRequest(req: NextRequest): Promise<{ uid: string; email?: string } | null> {
  try {
    // Implementar verificação de token JWT/Firebase Auth
    // Por enquanto, retornamos um usuário mock para desenvolvimento
    return { uid: 'mock-user-id', email: '<EMAIL>' }
  } catch (error) {
    console.error('Erro ao verificar autenticação:', error)
    return null
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verificar autenticação
    const user = await getUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    // Extrair dados do body
    const { id, influencerId, platform } = await request.json()

    if (!id || !influencerId || !platform) {
      return NextResponse.json({ 
        error: 'Parâmetros obrigatórios: id, influencerId, platform' 
      }, { status: 400 })
    }

    // Verificar se o usuário tem permissão para editar este influenciador
    const influencerDoc = await db.collection('influencers').doc(influencerId).get()
    if (!influencerDoc.exists || influencerDoc.data()?.userId !== user.uid) {
      return NextResponse.json({ 
        error: 'Você não tem permissão para editar este influenciador' 
      }, { status: 403 })
    }

    // Buscar o documento do screenshot
    const screenshotDoc = await db
      .collection('influencers')
      .doc(influencerId)
      .collection('screenshots')
      .doc(id)
      .get()

    if (!screenshotDoc.exists) {
      return NextResponse.json({ 
        error: 'Screenshot não encontrado' 
      }, { status: 404 })
    }

    const screenshotData = screenshotDoc.data()
    
    // Deletar arquivo do Storage
    if (screenshotData?.url) {
      await deleteScreenshot(screenshotData.url)
    }

    // Deletar documento do Firestore
    await screenshotDoc.ref.delete()

    return NextResponse.json({
      success: true,
      message: 'Screenshot deletado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao deletar screenshot:', error)
    return NextResponse.json({ 
      error: 'Erro interno do servidor' 
    }, { status: 500 })
  }
} 

