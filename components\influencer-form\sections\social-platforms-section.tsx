import React, { useState, useC<PERSON>back, useEffect } from 'react'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import { AlertCircle, TrendingUp, Users, Eye, Smartphone, Plus } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { SocialPlatformCard } from '../social-platform-card'
import { PLATFORM_CONFIG, type SocialPlatform } from '@/types/influencer-form'
import type { InfluencerFormData } from '@/types/influencer-form'

// Tipo para arquivos pendentes organizados por plataforma
interface PendingFile {
  file: File
  preview: string
  id: string
  platform: string
}

type PendingFilesByPlatform = Record<string, PendingFile[]>
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useInfluencerForm } from '@/hooks/use-influencer-form'

interface SocialPlatformsSectionProps {
  // Removemos props de activePlatforms e onAdd/Remove
}

// Função auxiliar para formatar números grandes
const formatNumber = (num: number | undefined | null): string => {
  if (!num || isNaN(num)) return '0'
  
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// Funções auxiliares para cálculos
const calculateTotalFollowers = (platforms: any) => {
  if (!platforms) return 0
  return Object.values(platforms).reduce((total: number, platform: any) => {
    return total + (platform?.followers || 0)
  }, 0)
}

const calculateAverageEngagement = (platforms: any) => {
  if (!platforms) return 0
  const platformsArray = Object.values(platforms).filter((p: any) => p?.engagementRate)
  if (platformsArray.length === 0) return 0
  
  const total = platformsArray.reduce((sum: number, platform: any) => {
    return sum + (platform?.engagementRate || 0)
  }, 0)
  
  return Math.round((total / platformsArray.length) * 100) / 100
}

const countActivePlatforms = (platforms: any) => {
  if (!platforms) return 0
  return Object.values(platforms).filter((p: any) => p?.followers > 0).length
}

const getAvailablePlatformsForMain = (platforms: any) => {
  if (!platforms) return []
  return Object.keys(platforms).filter(platform => {
    const data = platforms[platform]
    // Plataforma disponível para ser principal se tem username OU followers > 0
    return data && (data.username?.trim() || data.followers > 0)
  })
}

export function SocialPlatformsSection({}: SocialPlatformsSectionProps) {
  const { control, watch, setValue, formState: { errors } } = useFormContext<InfluencerFormData>()
  const { addPlatform, availablePlatforms, activePlatforms } = useInfluencerForm()

  // Watch plataformas com segurança
  const platforms = watch('platforms') || {}

  // Estado para gerenciar arquivos pendentes de todas as plataformas
  const [pendingFilesByPlatform, setPendingFilesByPlatform] = useState<PendingFilesByPlatform>({})

  // Função para criar URL de preview de forma segura
  const createPreviewUrl = useCallback((file: File): string => {
    return URL.createObjectURL(file)
  }, [])

  // Função para revogar URL de preview de forma segura
  const revokePreviewUrl = useCallback((url: string) => {
    try {
      URL.revokeObjectURL(url)
    } catch (error) {
      console.warn('Erro ao revogar URL de preview:', error)
    }
  }, [])

  // 🔄 RESTAURAR screenshots pendentes do formulário quando o componente for montado
  useEffect(() => {
    const savedPendingScreenshots = watch('pendingScreenshots') || []
    console.log('📸 [SOCIAL-PLATFORMS] Restaurando screenshots pendentes:', savedPendingScreenshots.length)
    
    if (savedPendingScreenshots.length > 0) {
      // Agrupar por plataforma e recriar URLs de preview
      const filesByPlatform: PendingFilesByPlatform = {}
      
      savedPendingScreenshots.forEach((savedFile: any) => {
        if (!filesByPlatform[savedFile.platform]) {
          filesByPlatform[savedFile.platform] = []
        }
        
        // ✅ RECRIAR URL de preview se o arquivo ainda existe
        const restoredFile = {
          ...savedFile,
          preview: savedFile.file ? URL.createObjectURL(savedFile.file) : savedFile.preview
        }
        
        filesByPlatform[savedFile.platform].push(restoredFile)
      })
      
      setPendingFilesByPlatform(filesByPlatform)
      
      // ✅ ATUALIZAR formulário com as novas URLs de preview
      const allRestoredFiles = Object.values(filesByPlatform).flat()
      setValue('pendingScreenshots' as any, allRestoredFiles)
      
      console.log('✅ [SOCIAL-PLATFORMS] Screenshots restaurados com preview recreado:', Object.keys(filesByPlatform))
    }
  }, []) // Executar apenas na montagem - eslint-disable-line react-hooks/exhaustive-deps

  // Função para atualizar arquivos pendentes de uma plataforma específica
  const updatePendingFiles = useCallback((platform: string, files: PendingFile[]) => {
    setPendingFilesByPlatform(prev => {
      // Revogar URLs antigas que não estão mais sendo usadas
      const oldFiles = prev[platform] || []
      const newFileIds = new Set(files.map(f => f.id))

      oldFiles.forEach(oldFile => {
        if (!newFileIds.has(oldFile.id)) {
          revokePreviewUrl(oldFile.preview)
        }
      })

      return {
        ...prev,
        [platform]: files
      }
    })

    // Sincronizar com o formulário principal - combinar todos os arquivos pendentes
    const allPendingFiles = Object.values({
      ...pendingFilesByPlatform,
      [platform]: files
    }).flat()

    setValue('pendingScreenshots' as any, allPendingFiles)

    console.log(`📸 [${platform}] Arquivos pendentes atualizados:`, files.length)
    console.log(`📸 [TOTAL] Total de arquivos pendentes:`, allPendingFiles.length)
  }, [pendingFilesByPlatform, setValue, revokePreviewUrl])

  // Função para limpar todos os arquivos pendentes de uma plataforma
  const clearPlatformFiles = useCallback((platform: string) => {
    const files = pendingFilesByPlatform[platform] || []
    files.forEach(file => revokePreviewUrl(file.preview))
    updatePendingFiles(platform, [])
  }, [pendingFilesByPlatform, revokePreviewUrl, updatePendingFiles])

  // Função para limpar todos os arquivos pendentes
  const clearAllPendingFiles = useCallback(() => {
    Object.entries(pendingFilesByPlatform).forEach(([platform, files]) => {
      files.forEach(file => revokePreviewUrl(file.preview))
    })
    setPendingFilesByPlatform({})
    setValue('pendingScreenshots' as any, [])
  }, [pendingFilesByPlatform, revokePreviewUrl, setValue])

  // 🧹 Cleanup das URLs de preview quando o componente for desmontado
  useEffect(() => {
    return () => {
      // Revogar apenas as URLs do estado local, pois as do formulário persistem
      Object.values(pendingFilesByPlatform).flat().forEach(file => {
        try {
          revokePreviewUrl(file.preview)
        } catch (error) {
          // Ignorar erros de revogação
        }
      })
    }
  }, [pendingFilesByPlatform, revokePreviewUrl])

  // Função para adicionar novos arquivos com preview
  const addFilesWithPreview = useCallback((platform: string, newFiles: File[]) => {
    const currentFiles = pendingFilesByPlatform[platform] || []

    const newPendingFiles: PendingFile[] = newFiles.map(file => ({
      file,
      preview: createPreviewUrl(file),
      id: `${platform}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      platform
    }))

    const updatedFiles = [...currentFiles, ...newPendingFiles]
    updatePendingFiles(platform, updatedFiles)

    return newPendingFiles
  }, [pendingFilesByPlatform, createPreviewUrl, updatePendingFiles])
  const mainPlatform = watch('mainPlatform')
  
  // Calcular métricas com segurança
  const totalFollowers = React.useMemo(() => calculateTotalFollowers(platforms), [platforms])
  const avgEngagement = React.useMemo(() => calculateAverageEngagement(platforms), [platforms])
  const activePlatformsCount = React.useMemo(() => countActivePlatforms(platforms), [platforms])
  const availableForMain = React.useMemo(() => {
    const available = getAvailablePlatformsForMain(platforms)
    // Logs removidos para limpar console
    return available
  }, [platforms])
  
  // Usar activePlatforms para mostrar abas, e activePlatformsCount para estatísticas
  const showPlatformTabs = activePlatforms.length > 0
  
  // Todas as plataformas disponíveis (do config) - filtrar apenas as ativas
  const activePlatformNames = activePlatforms
  
  // Função para definir plataforma principal
  const handleSetMainPlatform = (platform: string) => {
    setValue('mainPlatform', platform as any)
  }

  // Auto-definir primeira plataforma como principal se não houver uma selecionada
  React.useEffect(() => {
    if (!mainPlatform && availableForMain.length > 0) {
      handleSetMainPlatform(availableForMain[0])
    }
  }, [availableForMain, mainPlatform, setValue])

  return (
    <div className="space-y-6">
     

      

      {/* Seleção de plataforma principal */}
      {availableForMain.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Plataforma Principal</label>
          <Controller
            name="mainPlatform"
            control={control}
            render={({ field }) => (
              <div className="flex gap-2 flex-wrap">
                {availableForMain.map((platform) => {
                  const config = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]
                  const isSelected = field.value === platform
                  
                  return (
                    <button
                      key={platform}
                      type="button"
                      onClick={() => {
                        field.onChange(platform)
                        handleSetMainPlatform(platform)
                      }}
                      className={`
                        flex items-center gap-2 px-3 py-2 rounded-lg border text-sm
                        ${isSelected ? 'border-[#ff0074] bg-[#ff0074]/10' : 'border-border hover:bg-muted/50'}
                      `}
                    >
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: config?.color }}
                      />
                      {config?.name || platform}
                    </button>
                  )
                })}
              </div>
            )}
          />
        </div>
      )}

      

      {/* Abas das Redes Sociais */}
      <Card>
       
        <CardContent>
          <Tabs defaultValue="instagram" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              {Object.keys(PLATFORM_CONFIG).map((platform) => {
                const config = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]
                const platformData = platforms?.[platform as keyof typeof platforms] as any
                const hasData = platformData && platformData.followers > 0
                
                return (
                  <TabsTrigger
                    key={platform}
                    value={platform}
                    className="flex items-center gap-1 text-xs"
                  >
                    <div 
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: config?.color }}
                    />
                    {config?.name}
                    {hasData && (
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full ml-1" />
                    )}
                  </TabsTrigger>
                )
              })}
            </TabsList>
            
            {Object.keys(PLATFORM_CONFIG).map((platform) => (
              <TabsContent key={platform} value={platform} className="mt-4">
                <SocialPlatformCard
                  platform={platform}
                  control={control}
                  showAsTab={true}
                  pendingFiles={pendingFilesByPlatform[platform] || []}
                  onPendingFilesChange={(files) => updatePendingFiles(platform, files)}
                  onAddFilesWithPreview={(files) => addFilesWithPreview(platform, files)}
                  onClearPlatformFiles={() => clearPlatformFiles(platform)}
                />
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>

      {/* Mensagem de erro global */}
      {errors.platforms && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {errors.platforms.message || 'Erro ao validar dados das redes sociais'}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
} 

