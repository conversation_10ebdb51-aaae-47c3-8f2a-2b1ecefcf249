import { NextRequest, NextResponse } from 'next/server';
import { withBrandSecurity } from '@/lib/api-middleware';
import { ProposalService } from '@/services/proposal-service';
import { ProposalFilters, ProposalStats, ProposalStatus, ProposalPriority, InfluencerTier } from '@/types/proposal';

// GET - Listar propostas com filtros avançados (Fase 1)
export const GET = withBrandSecurity('read', async (request: NextRequest, userId: string) => {
  try {
    // Obter parâmetros de query
    const { searchParams } = new URL(request.url);
    
    // Filtros básicos
    const brandId = searchParams.get('brandId');
    const brandName = searchParams.get('brandName');
    const search = searchParams.get('search');
    const assignedTo = searchParams.get('assignedTo');
    const campaignType = searchParams.get('campaignType');
    const priority = searchParams.get('priority') as ProposalPriority;
    const influencerTier = searchParams.get('influencerTier') as InfluencerTier;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Filtros de status (múltiplos)
    const statusParam = searchParams.get('status');
    const statusArray = statusParam ? statusParam.split(',') as ProposalStatus[] : undefined;
    
    // Filtros de data
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    // Filtros de tags
    const tagsParam = searchParams.get('tags');
    const tagsArray = tagsParam ? tagsParam.split(',') : undefined;
    
    // Configurar filtros (apenas campos que existem na interface ProposalFilters)
    const filters: ProposalFilters = {
      userId: userId || undefined,
      status: statusArray,
      priority: priority ? [priority] : undefined, // ✅ CORREÇÃO: Converter string única para array
      dateRange: startDate && endDate ? {
        start: new Date(startDate),
        end: new Date(endDate)
      } : undefined
    };
    
    // Buscar propostas usando o ProposalService
    const proposals = await ProposalService.getProposals(filters, limit, offset, userId);
    
    // Buscar total de propostas para paginação
    const totalProposals = await ProposalService.countProposals(filters, userId);

    // Calcular estatísticas completas - Fase 1
    const stats: ProposalStats = {
      total: totalProposals,
      byStatus: {
        draft: proposals.filter(p => p.status === 'draft').length,
        pending: proposals.filter(p => p.status === 'pending').length,
        sent: proposals.filter(p => p.status === 'sent').length,
        viewed: proposals.filter(p => p.status === 'viewed').length,
        under_review: proposals.filter(p => p.status === 'under_review').length,
        negotiating: proposals.filter(p => p.status === 'negotiating').length,
        approved: proposals.filter(p => p.status === 'approved').length,
        accepted: proposals.filter(p => p.status === 'accepted').length,
        rejected: proposals.filter(p => p.status === 'rejected').length,
        expired: proposals.filter(p => p.status === 'expired').length,
        cancelled: proposals.filter(p => p.status === 'cancelled').length
      },
      averageResponseTime: calculateAverageResponseTime(proposals),
      averageAmount: calculateAverageValue(proposals), // ✅ CORREÇÃO: Usar averageAmount em vez de acceptanceRate
      totalValue: calculateTotalValue(proposals)
    };
    
    // Buscar dados para analytics se solicitado
    const includeAnalytics = searchParams.get('includeAnalytics') === 'true';
    let analytics = null;
    
    if (includeAnalytics) {
      analytics = await ProposalService.getAnalytics(filters, userId);
    }

    return NextResponse.json({
      success: true,
      propostas: proposals,
      stats,
      analytics,
      pagination: {
        total: totalProposals,
        limit,
        offset,
        hasMore: offset + proposals.length < totalProposals
      }
    });

// Funções auxiliares para cálculo de estatísticas - Fase 1
function calculateAverageResponseTime(proposals: any[]): number {
  const proposalsWithResponse = proposals.filter(p => p.responseTime && p.responseTime > 0);
  if (proposalsWithResponse.length === 0) return 0;
  
  const totalTime = proposalsWithResponse.reduce((sum, p) => sum + p.responseTime, 0);
  return Math.round(totalTime / proposalsWithResponse.length);
}

function calculateTotalValue(proposals: any[]): number {
  return proposals
    .filter(p => ['approved', 'accepted'].includes(p.status))
    .reduce((sum, p) => sum + (p.totalAmount || 0), 0);
}

function calculateAverageValue(proposals: any[]): number {
  if (proposals.length === 0) return 0;
  
  const totalValue = proposals.reduce((sum, p) => sum + (p.totalAmount || 0), 0);
  return Math.round(totalValue / proposals.length);
}

  } catch (error) {
    console.error('Erro ao buscar propostas:', error);
    
    // Se a coleção não existe, retornar dados vazios em vez de erro
    if (error instanceof Error && error.message.includes('collection')) {
      console.log('Coleção proposals não encontrada, retornando dados vazios');
      return NextResponse.json({
        success: true,
        propostas: [],
        stats: {
          total: 0,
          byStatus: {
            draft: 0,
            pending: 0,
            sent: 0,
            viewed: 0,
            under_review: 0,
            negotiating: 0,
            approved: 0,
            accepted: 0,
            rejected: 0,
            expired: 0,
            cancelled: 0
          },
          averageResponseTime: 0,
          acceptanceRate: 0,
          averageNegotiationRounds: 0,
          totalValue: 0,
          averageValue: 0
        },
        analytics: null,
        pagination: {
          total: 0,
          limit: 20,
          offset: 0,
          hasMore: false
        }
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

// POST - Criar nova proposta
export const POST = withBrandSecurity('write', async (request: NextRequest, userId: string) => {
  try {
    const body = await request.json();
    console.log('📋 Dados recebidos no POST:', JSON.stringify(body, null, 2));
    
    // Validar dados obrigatórios - incluindo brandId
    const requiredFields = ['brandId'];
    for (const field of requiredFields) {
      if (!body[field]) {
        console.log(`❌ Campo obrigatório ausente: ${field}`);
        return NextResponse.json(
          { success: false, error: `Campo obrigatório: ${field}` },
          { status: 400 }
        );
      }
    }
    
    // Validar se brandId é válido
    if (!body.brandId || typeof body.brandId !== 'string' || body.brandId.trim() === '') {
      console.log('❌ brandId inválido:', body.brandId);
      return NextResponse.json(
        { success: false, error: 'brandId é obrigatório e deve ser uma string válida' },
        { status: 400 }
      );
    }
    
    console.log('✅ Campos obrigatórios validados, brandId:', body.brandId);

    // Mapear deliverables para services se necessário
    const services = body.services || body.deliverables || [];
    console.log('📦 Services mapeados:', services);

    // Validar se services é um array não vazio
    if (!Array.isArray(services) || services.length === 0) {
      console.log('❌ Services inválido:', services);
      return NextResponse.json(
        { success: false, error: 'Pelo menos um serviço é obrigatório' },
        { status: 400 }
      );
    }
    console.log('✅ Services validado');

    // Calcular valor total dos services
    const totalValue = body.totalAmount || services.reduce((sum, service) => sum + (service.amount || 0), 0);
    console.log('💰 Valor total calculado:', totalValue);

    // Estrutura simplificada - sem processamento de termos complexos

    // Criar dados da proposta com todos os campos obrigatórios
    const proposalData: any = {
      nome: body.nome || `Proposta ${new Date().toLocaleDateString('pt-BR')}`,
      descricao: body.descricao || '',
      criadoPor: body.criadoPor || userId || 'Sistema',
      influencers: [], // ✅ CORREÇÃO: Sempre array vazio - influenciadores devem ser adicionados via subcoleção
      brandId: body.brandId, // brandId é obrigatório e já foi validado
      services: services, // Usar services mapeados
      totalAmount: totalValue,
      dataEnvio: body.dataEnvio || new Date().toISOString().split('T')[0],
      grupo: body.grupo || '',
      priority: body.priority || 'medium'
    };
    console.log('📦 Dados da proposta preparados:', JSON.stringify(proposalData, null, 2));

    // ✅ IMPORTANTE: Se influenciadores foram enviados no body, avisar que devem usar subcoleção
    if (body.influencers && Array.isArray(body.influencers) && body.influencers.length > 0) {
      console.warn('⚠️ [API] Influenciadores enviados no array serão ignorados. Use ProposalService.addInfluencersToProposal() após criar a proposta.');
    }

    // Criar proposta usando o ProposalService
    console.log('🚀 Chamando ProposalService.createProposal...');
    const proposalId = await ProposalService.createProposal(proposalData);
    console.log('✅ Proposta criada com ID:', proposalId);

    // ✅ OPCIONAL: Se influencerIds foi enviado, adicionar à subcoleção
    if (body.influencerIds && Array.isArray(body.influencerIds) && body.influencerIds.length > 0) {
      console.log('📝 Adicionando influenciadores à subcoleção...');
      try {
        await ProposalService.addInfluencersToProposal(
          proposalId,
          body.influencerIds,
          userId || 'Sistema'
        );
        console.log(`✅ ${body.influencerIds.length} influenciadores adicionados à subcoleção`);
      } catch (influencerError) {
        console.error('❌ Erro ao adicionar influenciadores à subcoleção:', influencerError);
        // Não falhar a criação da proposta por este erro
      }
    }

    // Buscar a proposta criada para retornar
    console.log('🔍 Buscando proposta criada...');
    const createdProposal = await ProposalService.getProposalById(proposalId);
    console.log('📋 Proposta encontrada:', createdProposal ? 'Sim' : 'Não');

    return NextResponse.json({
      success: true,
      message: 'Proposta criada com sucesso!',
      proposta: createdProposal
    }, { status: 201 });

  } catch (error) {
    console.error('Erro ao criar proposta:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
});

