"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { Textarea } from './textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription 
} from './dialog';
import { 
  Check, 
  X, 
  Clock, 
  DollarSign, 
  MessageSquare, 
  User,
  Calendar
} from 'lucide-react';

interface CollaboratorCounterProposal {
  id: string;
  budgetId: string;
  proposalId: string;
  influencerId: string;
  originalAmount: number;
  proposedAmount: number;
  currency: string;
  notes?: string;
  serviceType: string;
  proposedBy: {
    userId: string;
    userName: string;
    userEmail: string;
    collaboratorRole: 'editor' | 'viewer';
  };
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: Date;
  updatedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  reviewNote?: string;
}

interface CollaboratorCounterProposalManagerProps {
  proposalId: string;
  budgetId: string;
  influencerId: string;
  userRole: 'owner' | 'editor' | 'viewer' | 'none';
  onCounterProposalProcessed?: () => void;
}

export function CollaboratorCounterProposalManager({
  proposalId,
  budgetId,
  influencerId,
  userRole,
  onCounterProposalProcessed
}: CollaboratorCounterProposalManagerProps) {
  const [counterProposals, setCounterProposals] = useState<CollaboratorCounterProposal[]>([]);
  const [loading, setLoading] = useState(false);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [selectedProposal, setSelectedProposal] = useState<CollaboratorCounterProposal | null>(null);
  const [reviewNote, setReviewNote] = useState('');
  const [processing, setProcessing] = useState(false);

  // Buscar contrapropostas de colaboradores
  const fetchCounterProposals = async () => {
    if (!proposalId || !budgetId || !influencerId) return;

    setLoading(true);
    try {
      const response = await fetch(
        `/api/proposals/${proposalId}/budgets/${budgetId}/collaborator-counter-proposals?influencerId=${influencerId}`
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setCounterProposals(result.data);
        }
      }
    } catch (error) {
      console.error('Erro ao buscar contrapropostas de colaboradores:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCounterProposals();
  }, [proposalId, budgetId, influencerId]);

  // Formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Processar contraproposta (aceitar/rejeitar)
  const processCounterProposal = async (
    counterProposal: CollaboratorCounterProposal,
    action: 'accepted' | 'rejected'
  ) => {
    setProcessing(true);
    try {
      const response = await fetch(
        `/api/proposals/${proposalId}/budgets/${budgetId}/collaborator-counter-proposals`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            counterProposalId: counterProposal.id,
            status: action,
            reviewNote: reviewNote.trim(),
            influencerId,
            reviewedBy: 'current_user' // TODO: Pegar do contexto de auth
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Contraproposta ${action}:`, result);

        // 🔥 CORREÇÃO: Atualizar estado local imediatamente para feedback visual
        setCounterProposals(prev =>
          prev.map(cp =>
            cp.id === counterProposal.id
              ? {
                  ...cp,
                  status: action as 'accepted' | 'rejected',
                  reviewedAt: new Date(),
                  reviewedBy: 'current_user',
                  reviewNote: reviewNote.trim()
                }
              : cp
          )
        );

        // Atualizar lista local com dados do servidor
        await fetchCounterProposals();

        // Callback para atualizar dados externos (orçamentos, etc.)
        onCounterProposalProcessed?.();

        // Fechar dialog
        setShowReviewDialog(false);
        setSelectedProposal(null);
        setReviewNote('');

        // ✅ Valor já é aplicado automaticamente pela API
        if (action === 'accepted') {
          console.log('💰 Valor da contraproposta aplicado ao orçamento:', counterProposal.proposedAmount);
        }

      } else {
        const error = await response.json();
        console.error('❌ Erro ao processar contraproposta:', error);
        alert(`Erro ao ${action === 'accepted' ? 'aceitar' : 'rejeitar'} contraproposta: ${error.error || 'Erro desconhecido'}`);
      }
    } catch (error) {
      console.error('❌ Erro ao processar contraproposta:', error);
      alert(`Erro ao ${action === 'accepted' ? 'aceitar' : 'rejeitar'} contraproposta. Tente novamente.`);
    } finally {
      setProcessing(false);
    }
  };

  // Abrir dialog de revisão
  const openReviewDialog = (counterProposal: CollaboratorCounterProposal) => {
    setSelectedProposal(counterProposal);
    setReviewNote('');
    setShowReviewDialog(true);
  };

  // Calcular diferença percentual
  const calculatePercentageDifference = (original: number, proposed: number) => {
    const diff = ((proposed - original) / original) * 100;
    return diff.toFixed(1);
  };

  // Se não for proprietário, não mostrar o componente
  if (userRole !== 'owner') {
    return null;
  }

  // Filtrar apenas contrapropostas pendentes
  const pendingProposals = counterProposals.filter(cp => cp.status === 'pending');

  if (pendingProposals.length === 0) {
    return null;
  }

  return (
    <>
      <Card className="mt-4 border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-950/20">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-amber-800 dark:text-amber-200 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Contrapropostas de Colaboradores Pendentes
            <Badge variant="secondary" className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
              {pendingProposals.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {pendingProposals.map((counterProposal) => {
            const percentDiff = calculatePercentageDifference(
              counterProposal.originalAmount,
              counterProposal.proposedAmount
            );
            const isIncrease = counterProposal.proposedAmount > counterProposal.originalAmount;

            return (
              <div 
                key={counterProposal.id}
                className="p-4 bg-white dark:bg-[#080210] rounded-lg border border-gray-200 dark:border-gray-700"
              >
                {/* Cabeçalho com dados do colaborador */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="font-medium text-sm">
                      {counterProposal.proposedBy.userName}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {counterProposal.proposedBy.collaboratorRole}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Calendar className="h-3 w-3" />
                    {new Date(counterProposal.createdAt).toLocaleDateString('pt-BR')}
                  </div>
                </div>

                {/* Comparação de valores */}
                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-xs text-gray-500 mb-1">Valor Original</div>
                    <div className="font-medium text-sm">
                      {formatCurrency(counterProposal.originalAmount)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500 mb-1">Proposta</div>
                    <div className={`font-medium text-sm ${
                      isIncrease ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'
                    }`}>
                      {formatCurrency(counterProposal.proposedAmount)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500 mb-1">Diferença</div>
                    <div className={`font-medium text-sm ${
                      isIncrease ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'
                    }`}>
                      {isIncrease ? '+' : ''}{percentDiff}%
                    </div>
                  </div>
                </div>

                {/* Observações */}
                {counterProposal.notes && (
                  <div className="mb-3">
                    <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
                      <MessageSquare className="h-3 w-3" />
                      Observações
                    </div>
                    <div className="text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                      {counterProposal.notes}
                    </div>
                  </div>
                )}

                {/* Ações */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white flex-1"
                    onClick={() => {
                      setSelectedProposal(counterProposal);
                      processCounterProposal(counterProposal, 'accepted');
                    }}
                    disabled={processing}
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Aceitar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 flex-1"
                    onClick={() => openReviewDialog(counterProposal)}
                    disabled={processing}
                  >
                    <X className="h-3 w-3 mr-1" />
                    Rejeitar
                  </Button>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Dialog de Revisão/Rejeição */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Rejeitar Contraproposta</DialogTitle>
            <DialogDescription>
              Adicione uma observação sobre o motivo da rejeição (opcional).
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {selectedProposal && (
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-sm font-medium mb-1">
                  Proposta de {selectedProposal.proposedBy.userName}
                </div>
                <div className="text-xs text-gray-500">
                  {formatCurrency(selectedProposal.originalAmount)} → {formatCurrency(selectedProposal.proposedAmount)}
                </div>
              </div>
            )}
            
            <div>
              <label className="text-sm font-medium mb-2 block">
                Observações (opcional)
              </label>
              <Textarea
                value={reviewNote}
                onChange={(e) => setReviewNote(e.target.value)}
                placeholder="Explique o motivo da rejeição..."
                rows={3}
              />
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowReviewDialog(false)}
              disabled={processing}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={() => selectedProposal && processCounterProposal(selectedProposal, 'rejected')}
              disabled={processing}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
            >
              {processing ? 'Rejeitando...' : 'Rejeitar'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
} 

