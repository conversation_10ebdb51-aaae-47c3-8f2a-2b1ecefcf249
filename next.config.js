/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configurações para build
  output: 'standalone',
  
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // Configurações para TypeScript
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configurações para ESLint
  eslint: {
    ignoreDuringBuilds: true,
  },
}

module.exports = nextConfig 