'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Plus, Calendar, DollarSign, User, Clock, AlertCircle } from 'lucide-react';
import { CampaignProposal, ProposalStatus } from '@/types/campaign';
import { cn } from '@/lib/utils';

interface KanbanColumnProps {
  title: string;
  status: ProposalStatus;
  proposals: CampaignProposal[];
  onUpdateProposal: (id: string, status: ProposalStatus, observations?: string) => void;
  onDeleteProposal: (id: string) => void;
  color: string;
}

interface NewProposalForm {
  campaignName: string;
  clientName: string;
  budget: number;
  startDate: string;
  endDate: string;
  observations: string;
  priority: 'low' | 'medium' | 'high';
}

const statusConfig = {
  analyzing: {
    title: 'Em analise',
    color: 'bg-yellow-50 border-yellow-200',
    badgeColor: 'bg-yellow-100 text-yellow-800',
    icon: Clock
  },
  approved: {
    title: 'Aprovado',
    color: 'bg-green-50 border-green-200',
    badgeColor: 'bg-green-100 text-green-800',
    icon: User
  },
  rejected: {
    title: 'Reprovado',
    color: 'bg-red-50 border-red-200',
    badgeColor: 'bg-red-100 text-red-800',
    icon: AlertCircle
  },
  proposal_rejected: {
    title: 'Proposta Recusada',
    color: 'bg-gray-50 border-gray-200',
    badgeColor: 'bg-gray-100 text-gray-800',
    icon: AlertCircle
  }
};

const priorityColors = {
  low: 'bg-blue-100 text-blue-800',
  medium: 'bg-orange-100 text-orange-800',
  high: 'bg-red-100 text-red-800'
};

function ProposalCard({ proposal, onUpdateStatus, onDelete }: {
  proposal: CampaignProposal;
  onUpdateStatus: (status: ProposalStatus, observations?: string) => void;
  onDelete: () => void;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [observations, setObservations] = useState(proposal.observations);
  const [selectedStatus, setSelectedStatus] = useState<ProposalStatus>(proposal.status);

  const handleSave = () => {
    onUpdateStatus(selectedStatus, observations);
    setIsEditing(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR').format(new Date(date));
  };

  return (
    <Card className="mb-4 hover:shadow-md transition-shadow duration-200 cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {proposal.campaignName}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{proposal.clientName}</p>
          </div>
          {proposal.priority && (
            <Badge className={cn('text-xs', priorityColors[proposal.priority])}>
              {proposal.priority === 'low' ? 'Baixa' : proposal.priority === 'medium' ? 'Média' : 'Alta'}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <DollarSign className="h-4 w-4" />
            <span className="font-medium">{formatCurrency(proposal.budget)}</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(proposal.startDate)} - {formatDate(proposal.endDate)}</span>
          </div>

          {proposal.observations && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-700">
                <strong>Observações:</strong> {proposal.observations}
              </p>
            </div>
          )}

          <div className="flex gap-2 pt-2">
            <Dialog open={isEditing} onOpenChange={setIsEditing}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="flex-1">
                  Editar
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Editar Proposta</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={selectedStatus} onValueChange={(value: ProposalStatus) => setSelectedStatus(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="analyzing">Em analise</SelectItem>
                        <SelectItem value="approved">Aprovado</SelectItem>
                        <SelectItem value="rejected">Reprovado</SelectItem>
                        <SelectItem value="proposal_rejected">Proposta Recusada</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="observations">Observações</Label>
                    <Textarea
                      id="observations"
                      value={observations}
                      onChange={(e) => setObservations(e.target.value)}
                      placeholder="Adicione suas observações..."
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handleSave} className="flex-1">
                      Salvar
                    </Button>
                    <Button variant="outline" onClick={() => setIsEditing(false)} className="flex-1">
                      Cancelar
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            
            <Button variant="destructive" size="sm" onClick={onDelete}>
              Excluir
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function KanbanColumn({ title, status, proposals, onUpdateProposal, onDeleteProposal, color }: KanbanColumnProps) {
  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <div className={cn('rounded-xl p-4 min-h-[600px]', config.color)}>
      <div className="flex items-center gap-2 mb-4">
        <Icon className="h-5 w-5 text-gray-600" />
        <h3 className="font-semibold text-gray-900">{title}</h3>
        <Badge className={cn('ml-auto', config.badgeColor)}>
          {proposals.length}
        </Badge>
      </div>
      
      <div className="space-y-3">
        {proposals.map((proposal) => (
          <ProposalCard
            key={proposal.id}
            proposal={proposal}
            onUpdateStatus={(newStatus, observations) => 
              onUpdateProposal(proposal.id, newStatus, observations)
            }
            onDelete={() => onDeleteProposal(proposal.id)}
          />
        ))}
        
        {proposals.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p className="text-sm">Nenhuma proposta neste status</p>
          </div>
        )}
      </div>
    </div>
  );
}

export function CampaignsKanban() {
  const [proposals, setProposals] = useState<CampaignProposal[]>([]);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newProposal, setNewProposal] = useState<NewProposalForm>({
    campaignName: '',
    clientName: '',
    budget: 0,
    startDate: '',
    endDate: '',
    observations: '',
    priority: 'medium'
  });

  // Dados de exemplo para demonstração
  useEffect(() => {
    const mockProposals: CampaignProposal[] = [
      {
        id: '1',
        campaignName: 'Campanha Verão 2024',
        clientName: 'Marca Fashion',
        budget: 50000,
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-02-15'),
        status: 'analyzing',
        observations: 'Aguardando aprovação do orçamento pelo cliente',
        createdAt: new Date(),
        updatedAt: new Date(),
        priority: 'high'
      },
      {
        id: '2',
        campaignName: 'Lançamento Produto X',
        clientName: 'Tech Corp',
        budget: 75000,
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-03-01'),
        status: 'approved',
        observations: 'Campanha aprovada, iniciando seleção de influenciadores',
        createdAt: new Date(),
        updatedAt: new Date(),
        priority: 'medium'
      },
      {
        id: '3',
        campaignName: 'Black Friday 2024',
        clientName: 'E-commerce Plus',
        budget: 30000,
        startDate: new Date('2024-11-20'),
        endDate: new Date('2024-11-30'),
        status: 'rejected',
        observations: 'Orçamento muito baixo para o alcance desejado',
        createdAt: new Date(),
        updatedAt: new Date(),
        priority: 'low'
      }
    ];
    setProposals(mockProposals);
  }, []);

  const handleUpdateProposal = (id: string, status: ProposalStatus, observations?: string) => {
    setProposals(prev => prev.map(proposal => 
      proposal.id === id 
        ? { 
            ...proposal, 
            status, 
            observations: observations || proposal.observations,
            updatedAt: new Date() 
          }
        : proposal
    ));
  };

  const handleDeleteProposal = (id: string) => {
    if (confirm('Tem certeza que deseja excluir esta proposta?')) {
      setProposals(prev => prev.filter(proposal => proposal.id !== id));
    }
  };

  const handleAddProposal = () => {
    if (!newProposal.campaignName || !newProposal.clientName) {
      alert('Nome da campanha e cliente são obrigatórios');
      return;
    }

    const proposal: CampaignProposal = {
      id: Date.now().toString(),
      campaignName: newProposal.campaignName,
      clientName: newProposal.clientName,
      budget: newProposal.budget,
      startDate: new Date(newProposal.startDate),
      endDate: new Date(newProposal.endDate),
      status: 'analyzing',
      observations: newProposal.observations,
      createdAt: new Date(),
      updatedAt: new Date(),
      priority: newProposal.priority
    };

    setProposals(prev => [...prev, proposal]);
    setNewProposal({
      campaignName: '',
      clientName: '',
      budget: 0,
      startDate: '',
      endDate: '',
      observations: '',
      priority: 'medium'
    });
    setIsAddingNew(false);
  };

  const getProposalsByStatus = (status: ProposalStatus) => {
    return proposals.filter(proposal => proposal.status === status);
  };

  return (
    <div className="p-6 bg-white min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Campanhas</h1>
          <p className="text-gray-600 mt-1">Gerencie propostas e campanhas em andamento</p>
        </div>
        
        <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Nova Proposta
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Nova Proposta de Campanha</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="campaignName">Nome da Campanha</Label>
                  <Input
                    id="campaignName"
                    value={newProposal.campaignName}
                    onChange={(e) => setNewProposal(prev => ({ ...prev, campaignName: e.target.value }))}
                    placeholder="Ex: Campanha Verão 2024"
                  />
                </div>
                <div>
                  <Label htmlFor="clientName">Cliente</Label>
                  <Input
                    id="clientName"
                    value={newProposal.clientName}
                    onChange={(e) => setNewProposal(prev => ({ ...prev, clientName: e.target.value }))}
                    placeholder="Nome do cliente"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="budget">Orçamento (R$)</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={newProposal.budget}
                    onChange={(e) => setNewProposal(prev => ({ ...prev, budget: Number(e.target.value) }))}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="priority">Prioridade</Label>
                  <Select value={newProposal.priority} onValueChange={(value: 'low' | 'medium' | 'high') => setNewProposal(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Baixa</SelectItem>
                      <SelectItem value="medium">Média</SelectItem>
                      <SelectItem value="high">Alta</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Data de Início</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={newProposal.startDate}
                    onChange={(e) => setNewProposal(prev => ({ ...prev, startDate: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">Data de Fim</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={newProposal.endDate}
                    onChange={(e) => setNewProposal(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="observations">Observações</Label>
                <Textarea
                  id="observations"
                  value={newProposal.observations}
                  onChange={(e) => setNewProposal(prev => ({ ...prev, observations: e.target.value }))}
                  placeholder="Adicione observações sobre a proposta..."
                  rows={3}
                />
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handleAddProposal} className="flex-1">
                  Criar Proposta
                </Button>
                <Button variant="outline" onClick={() => setIsAddingNew(false)} className="flex-1">
                  Cancelar
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KanbanColumn
          title="Em analise"
          status="analyzing"
          proposals={getProposalsByStatus('analyzing')}
          onUpdateProposal={handleUpdateProposal}
          onDeleteProposal={handleDeleteProposal}
          color="bg-yellow-50"
        />
        
        <KanbanColumn
          title="Aprovado"
          status="approved"
          proposals={getProposalsByStatus('approved')}
          onUpdateProposal={handleUpdateProposal}
          onDeleteProposal={handleDeleteProposal}
          color="bg-green-50"
        />
        
        <KanbanColumn
          title="Reprovado"
          status="rejected"
          proposals={getProposalsByStatus('rejected')}
          onUpdateProposal={handleUpdateProposal}
          onDeleteProposal={handleDeleteProposal}
          color="bg-red-50"
        />
        
        <KanbanColumn
          title="Proposta Recusada"
          status="proposal_rejected"
          proposals={getProposalsByStatus('proposal_rejected')}
          onUpdateProposal={handleUpdateProposal}
          onDeleteProposal={handleDeleteProposal}
          color="bg-gray-50"
        />
      </div>
    </div>
  );
}

