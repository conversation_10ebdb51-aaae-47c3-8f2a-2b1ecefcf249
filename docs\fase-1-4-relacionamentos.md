# 🔗 FASE 1.4: Análise de Relacionamentos Entre Entidades

## 🎯 Objetivo
Mapear todos os relacionamentos entre entidades e identificar dependências críticas para o isolamento multi-tenancy.

## 📊 Mapa Completo de Relacionamentos

### 🏗️ Estrutura Hierárquica Identificada

```
📋 users (ROOT) ✅ Isolado
├── 🏢 brands ✅ Isolado (userId FK)
│   ├── 📢 campaigns ❌ Precisa userId
│   ├── 📝 proposals ⚠️ Parcial (createdBy)
│   └── 🤝 brand_influencers ❌ Precisa userId
├── 👥 influencers ❌ Precisa userId
│   ├── 💰 influencer_financials ❌ Precisa userId
│   ├── 📋 notes ❌ Precisa userId
│   └── 🏷️ tags ❌ Precisa userId
├── 📁 groups ⚠️ Parcial (createdBy)
├── 🔍 filters ✅ Isolado (userId FK)
└── 📂 categories ❓ Global (avaliação necessária)
```

## 🔄 Análise de Dependências

### 1. Relacionamentos Críticos (Alto Impacto)

#### **brands → campaigns**
```typescript
// ATUAL: campaigns sem userId
interface Campaign {
  brandId: string;  // ✅ Existe
  // userId: string; // ❌ Faltando
}

// NECESSÁRIO: Adicionar userId
interface Campaign {
  id: string;
  userId: string;     // 🆕 FK para users
  brandId: string;    // ✅ FK para brands
  // ... outros campos
}
```

**Impacto da Migração:**
- **Alto**: Campaigns é central no sistema
- **Complexidade**: Média (precisa garantir consistência com brands)
- **Riscos**: Campanhas órfãs se brand não tiver userId válido

#### **brands → brand_influencers**
```typescript
// ATUAL: Relacionamento complexo sem isolamento
interface BrandInfluencer {
  brandId: string;      // ✅ Existe
  influencerId: string; // ✅ Existe  
  // userId: string;    // ❌ Faltando
}

// NECESSÁRIO: Isolamento completo
interface BrandInfluencer {
  id: string;
  userId: string;       // 🆕 FK para users
  brandId: string;      // ✅ FK para brands
  influencerId: string; // ✅ FK para influencers
  // ... outros campos
}
```

**Impacto da Migração:**
- **Crítico**: Relacionamento Many-to-Many complexo
- **Complexidade**: Alta (3 entidades interdependentes)
- **Riscos**: Quebra de relacionamentos existentes

### 2. Relacionamentos Secundários (Médio Impacto)

#### **influencers → influencer_financials**
```typescript
// Relacionamento 1:1 - Migração sincronizada necessária
interface InfluencerFinancial {
  influencerId: string; // ✅ Existe
  userId: string;       // 🆕 Deve ser igual ao userId do influencer
}
```

#### **influencers → notes**
```typescript
// Relacionamento 1:N - Migração em lote
interface Note {
  influencerId: string; // ✅ Existe
  userId: string;       // 🆕 Herda do influencer
}
```

#### **influencers → tags**
```typescript
// Relacionamento N:N - Complexidade média
interface Tag {
  influencerIds: string[]; // ✅ Existe
  userId: string;          // 🆕 Proprietário da tag
}
```

### 3. Relacionamentos Especiais

#### **groups → influencers**
```typescript
// ATUAL: Grupo pode ter influencers de qualquer usuário
interface Group {
  createdBy: string;        // ⚠️ Existe mas não é padrão
  influencerIds: string[];  // ❌ Sem validação de ownership
}

// NECESSÁRIO: Validação de propriedade
interface Group {
  id: string;
  userId: string;           // 🆕 Padronização (renomear createdBy)
  influencerIds: string[];  // ✅ Validar se influencers pertencem ao mesmo userId
  brandId?: string;         // ✅ Opcional para grupos específicos de marca
}
```

## 🚨 Cenários de Conflito Identificados

### 1. **Órfãos Potenciais**
```sql
-- Campanhas sem brand válido
SELECT c.* FROM campaigns c 
LEFT JOIN brands b ON c.brandId = b.id 
WHERE b.id IS NULL;

-- Brand_influencers com referências inválidas  
SELECT bi.* FROM brand_influencers bi
LEFT JOIN brands b ON bi.brandId = b.id
LEFT JOIN influencers i ON bi.influencerId = i.id
WHERE b.id IS NULL OR i.id IS NULL;
```

### 2. **Inconsistências de Propriedade**
```sql
-- Grupos com influencers de outros usuários
SELECT g.id, g.userId, i.userId as influencer_userId
FROM groups g
JOIN influencers i ON i.id = ANY(g.influencerIds)
WHERE g.userId != i.userId;
```

### 3. **Dados Compartilhados Problemáticos**
```sql
-- Tags que referenciam influencers de múltiplos usuários
SELECT t.id, t.userId, COUNT(DISTINCT i.userId) as distinct_users
FROM tags t
JOIN influencers i ON i.id = ANY(t.influencerIds)
GROUP BY t.id, t.userId
HAVING COUNT(DISTINCT i.userId) > 1;
```

## 🛠️ Estratégias de Migração por Relacionamento

### **Estratégia 1: Migração em Cascata (brands → campaigns)**
```typescript
// Fase 1: Migrar brands (já feito ✅)
// Fase 2: Migrar campaigns usando brandId como base
async function migrateCampaigns() {
  const campaigns = await getAllCampaigns();
  
  for (const campaign of campaigns) {
    const brand = await getBrandById(campaign.brandId);
    if (brand?.userId) {
      await updateCampaign(campaign.id, {
        userId: brand.userId,
        updatedAt: new Date()
      });
    } else {
      console.warn(`Campaign ${campaign.id} tem brandId inválido`);
      // Lógica para campanhas órfãs
    }
  }
}
```

### **Estratégia 2: Migração Sincronizada (influencers → financials)**
```typescript
// Migrar influencers e financials em sincronia
async function migrateInfluencersWithFinancials() {
  const influencers = await getAllInfluencers();
  
  for (const influencer of influencers) {
    // Determinar userId padrão (admin ou primeiro brand criado)
    const defaultUserId = await getDefaultUserForMigration();
    
    // Migrar influencer
    await updateInfluencer(influencer.id, {
      userId: defaultUserId,
      updatedAt: new Date()
    });
    
    // Migrar dados financiais relacionados
    const financials = await getFinancialByInfluencerId(influencer.id);
    if (financials) {
      await updateFinancial(financials.id, {
        userId: defaultUserId,
        updatedAt: new Date()
      });
    }
  }
}
```

### **Estratégia 3: Migração com Validação (groups)**
```typescript
async function migrateGroups() {
  const groups = await getAllGroups();
  
  for (const group of groups) {
    // Renomear createdBy para userId
    const userId = group.createdBy;
    
    // Validar se todos os influencers pertencem ao mesmo usuário
    const influencerUserIds = await Promise.all(
      group.influencerIds.map(id => getInfluencerUserId(id))
    );
    
    const uniqueUserIds = [...new Set(influencerUserIds.filter(Boolean))];
    
    if (uniqueUserIds.length === 1 && uniqueUserIds[0] === userId) {
      // Migração segura
      await updateGroup(group.id, {
        userId: userId,
        updatedAt: new Date()
      });
    } else {
      // Grupo com inconsistências - requer atenção manual
      console.warn(`Grupo ${group.id} tem influencers de múltiplos usuários`);
      await markGroupForReview(group.id);
    }
  }
}
```

## 📋 Scripts de Validação Pré-Migração

### 1. **Auditoria de Integridade**
```typescript
// scripts/audit-data-integrity.ts
export async function auditDataIntegrity() {
  const report = {
    orphanedCampaigns: [],
    orphanedBrandInfluencers: [],
    inconsistentGroups: [],
    summary: {}
  };
  
  // Verificar campanhas órfãs
  const campaigns = await getAllCampaigns();
  for (const campaign of campaigns) {
    const brand = await getBrandById(campaign.brandId);
    if (!brand) {
      report.orphanedCampaigns.push(campaign.id);
    }
  }
  
  // Verificar brand_influencers órfãos
  const brandInfluencers = await getAllBrandInfluencers();
  for (const bi of brandInfluencers) {
    const brand = await getBrandById(bi.brandId);
    const influencer = await getInfluencerById(bi.influencerId);
    if (!brand || !influencer) {
      report.orphanedBrandInfluencers.push(bi.id);
    }
  }
  
  // Gerar relatório
  report.summary = {
    totalCampaigns: campaigns.length,
    orphanedCampaigns: report.orphanedCampaigns.length,
    totalBrandInfluencers: brandInfluencers.length,
    orphanedBrandInfluencers: report.orphanedBrandInfluencers.length
  };
  
  return report;
}
```

### 2. **Simulação de Migração**
```typescript
// scripts/simulate-migration.ts
export async function simulateMigration() {
  const simulation = {
    wouldMigrate: 0,
    wouldFail: 0,
    failures: []
  };
  
  // Simular migração de campanhas
  const campaigns = await getAllCampaigns();
  for (const campaign of campaigns) {
    const brand = await getBrandById(campaign.brandId);
    if (brand?.userId) {
      simulation.wouldMigrate++;
    } else {
      simulation.wouldFail++;
      simulation.failures.push({
        type: 'campaign',
        id: campaign.id,
        reason: 'Brand not found or no userId'
      });
    }
  }
  
  return simulation;
}
```

## 📊 Ordem de Implementação Recomendada

### **Fase A: Entidades Base** ✅
1. `users` (já implementado)
2. `brands` (já implementado) 
3. `filters` (já implementado)

### **Fase B: Entidades Independentes** 
1. `influencers` (não depende de outras migrações)
2. `influencer_financials` (depende apenas de influencers)
3. `categories` (avaliar se deve ser global)

### **Fase C: Entidades com Relacionamentos Simples**
1. `campaigns` (depende de brands)
2. `notes` (depende de influencers)
3. `tags` (depende de influencers)

### **Fase D: Entidades Complexas**
1. `groups` (depende de influencers, validação especial)
2. `brand_influencers` (depende de brands e influencers)
3. `proposals` (padronizar campos existentes)

## ⚠️ Riscos e Mitigações

### **Risco 1: Dados Órfãos**
- **Mitigação**: Scripts de auditoria antes da migração
- **Plano B**: Criar usuário "legacy" para dados órfãos

### **Risco 2: Relacionamentos Quebrados**
- **Mitigação**: Migração em transação quando possível
- **Plano B**: Rollback automático em caso de falha

### **Risco 3: Performance Durante Migração**
- **Mitigação**: Migração em lotes pequenos (100-500 registros)
- **Plano B**: Migração fora do horário de pico

### **Risco 4: Inconsistências de Estado**
- **Mitigação**: Validação após cada lote migrado
- **Plano B**: Sistema de flags para marcar registros problemáticos

## ✅ Critérios de Validação Pós-Migração

### **Integridade Referencial**
- [ ] Todos os FKs apontam para registros válidos
- [ ] Nenhum registro órfão identificado
- [ ] Contadores de relacionamentos consistentes

### **Isolamento de Dados**
- [ ] Nenhuma query retorna dados de múltiplos usuários
- [ ] APIs respeitam filtros por userId
- [ ] Testes de isolamento passando

### **Performance**
- [ ] Queries com mesmo tempo de resposta ou melhor
- [ ] Índices otimizados para filtros por userId
- [ ] Cache invalidado adequadamente

---

✅ **Análise da Fase 1.4 Concluída** 