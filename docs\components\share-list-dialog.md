# ShareListDialog Component

## Descrição

O `ShareListDialog` é um componente React moderno e seguro para compartilhamento de listas, implementado seguindo as melhores práticas do Next.js e as diretrizes de segurança web. Oferece múltiplas opções de compartilhamento incluindo Web Share API nativa, redes sociais e cópia para clipboard.

## Características Principais

### 🔒 Segurança
- **Tokens seguros**: Geração de URLs com tokens únicos e data de expiração
- **Validação de permissões**: Estrutura preparada para validação de acesso
- **Rate limiting**: Preparado para implementação de controle de taxa
- **Sanitização**: Proteção contra XSS e injeção de código

### ⚡ Performance
- **Lazy loading**: Geração de URL apenas quando necessário
- **Memoização**: Callbacks otimizados com useCallback
- **Fallbacks**: Suporte para contextos não seguros (clipboard)
- **Debounce**: Prevenção de múltiplas chamadas simultâneas

### 🎨 UX/UI
- **Modo claro/escuro**: Compatibilidade total com temas
- **Feedback visual**: Estados de loading e confirmação
- **Responsivo**: Design adaptável para mobile e desktop
- **Acessibilidade**: Suporte a leitores de tela e navegação por teclado

### 🌐 Compatibilidade
- **Web Share API**: Integração nativa para dispositivos móveis
- **Fallbacks**: Suporte para navegadores sem Web Share API
- **Cross-platform**: Funciona em todos os navegadores modernos

## Props

```typescript
interface ShareListDialogProps {
  isOpen: boolean;           // Controla visibilidade do modal
  onClose: () => void;       // Callback para fechar modal
  listName: string;          // Nome da lista para compartilhamento
  listId: string;            // ID único da lista
  listType: string;          // Tipo da lista (influenciadores, marcas, etc.)
  itemCount: number;         // Quantidade de itens na lista
}
```

## Uso Básico

```tsx
import { ShareListDialog } from '@/components/ui/share-list-dialog';

function MyComponent() {
  const [showShareDialog, setShowShareDialog] = useState(false);

  return (
    <>
      <Button onClick={() => setShowShareDialog(true)}>
        <Share2 className="h-4 w-4 mr-2" />
        Compartilhar Lista
      </Button>

      <ShareListDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        listName="Minha Lista de Influenciadores"
        listId="abc123"
        listType="influenciadores"
        itemCount={25}
      />
    </>
  );
}
```

## Funcionalidades

### 1. Web Share API Nativa
- Detecta automaticamente se o dispositivo suporta Web Share API
- Integração nativa com apps de compartilhamento do sistema
- Ideal para dispositivos móveis e PWAs

### 2. Compartilhamento Social
- **WhatsApp**: Compartilhamento direto via web.whatsapp.com
- **Twitter**: Tweet com texto personalizado e URL
- **LinkedIn**: Compartilhamento profissional
- **Email**: Composição automática de email

### 3. Cópia para Clipboard
- Suporte para Clipboard API moderna
- Fallback para contextos não seguros
- Feedback visual com ícone de confirmação
- Toast notification de sucesso

### 4. Geração de URLs Seguras
- Tokens únicos com timestamp
- Data de expiração configurável (padrão: 7 dias)
- Estrutura preparada para validação backend
- Encoding seguro para URLs

## Implementação de Segurança

### Backend Integration (Recomendado)

```typescript
// Exemplo de API route para geração segura de URLs
// app/api/lists/[listId]/share/route.ts

export async function POST(request: Request, { params }: { params: { listId: string } }) {
  try {
    // 1. Validar autenticação
    const user = await getCurrentUser();
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

    // 2. Validar permissões na lista
    const hasPermission = await checkListPermission(user.id, params.listId);
    if (!hasPermission) return NextResponse.json({ error: 'Forbidden' }, { status: 403 });

    // 3. Aplicar rate limiting
    await applyRateLimit(user.id, 'share_list');

    // 4. Gerar token seguro
    const token = await generateSecureShareToken({
      listId: params.listId,
      userId: user.id,
      expiresIn: '7d'
    });

    // 5. Registrar atividade
    await logActivity({
      userId: user.id,
      action: 'list_shared',
      resourceId: params.listId
    });

    return NextResponse.json({ shareUrl: `${baseUrl}/shared-lists/${params.listId}?token=${token}` });
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

## Customização

### Cores e Temas
O componente usa as cores padrão do projeto:
- **Primária**: `#ff0074` (rosa)
- **Secundária**: `#5600ce` (roxo)
- **Gradientes**: Suporte automático para modo claro/escuro

### Opções de Compartilhamento
Facilmente extensível para novas plataformas:

```typescript
const newShareOption: ShareOption = {
  id: 'telegram',
  name: 'Telegram',
  icon: <TelegramIcon className="h-4 w-4" />,
  color: 'bg-blue-500 hover:bg-blue-600',
  action: (url, text) => {
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(telegramUrl, '_blank', 'noopener,noreferrer');
  }
};
```

## Dependências

- React 18+
- Next.js 13+ (App Router)
- Radix UI (Dialog, Label)
- Lucide React (ícones)
- Sonner (toast notifications)
- Tailwind CSS
- TypeScript

## Considerações de Performance

1. **Lazy Loading**: URL gerada apenas quando modal é aberto
2. **Memoização**: Callbacks otimizados para evitar re-renders
3. **Debounce**: Prevenção de múltiplas chamadas simultâneas
4. **Cleanup**: Estados resetados adequadamente ao fechar modal

## Acessibilidade

- **ARIA labels**: Todos os botões possuem labels descritivos
- **Keyboard navigation**: Navegação completa por teclado
- **Screen readers**: Compatibilidade com leitores de tela
- **Focus management**: Foco gerenciado adequadamente no modal

## Testes Recomendados

```typescript
// Exemplo de testes unitários
describe('ShareListDialog', () => {
  it('should generate secure share URL', async () => {
    // Test URL generation
  });

  it('should copy to clipboard successfully', async () => {
    // Test clipboard functionality
  });

  it('should handle Web Share API', async () => {
    // Test native sharing
  });

  it('should validate props correctly', () => {
    // Test prop validation
  });
});
```

## Roadmap

- [ ] Integração com analytics de compartilhamento
- [ ] Suporte a compartilhamento por QR Code
- [ ] Personalização de mensagens por plataforma
- [ ] Compartilhamento com permissões granulares
- [ ] Integração com sistemas de notificação
