'use client';

import { useState, useEffect } from 'react';
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  Users, 
  FileText,
  ArrowRight,
  Search,
  Calendar,
  DollarSign,
  Loader2,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface InfluencerSimple {
  id: string;
  nome: string;
  foto?: string;
  seguidores: number;
  nicho: string;
}

interface Proposta {
  id: string;
  nome: string;
  descricao?: string;
  brandId: string;
  status: 'draft' | 'sent' | 'accepted' | 'rejected';
  priority: 'low' | 'medium' | 'high';
  totalAmount: number;
  influencersCount: number;
  createdAt: Date;
  updatedAt: Date;
}

interface SendToProposalModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedInfluencers: InfluencerSimple[];
  onSuccess?: () => void;
}

export function SendToProposalModal({
  isOpen,
  onClose,
  selectedInfluencers,
  onSuccess
}: SendToProposalModalProps) {
  const { firebaseUser } = useFirebaseAuth();
  
  // Estados
  const [propostas, setPropostas] = useState<Proposta[]>([]);
  const [filteredPropostas, setFilteredPropostas] = useState<Proposta[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingPropostas, setLoadingPropostas] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProposta, setSelectedProposta] = useState<string>('');

  // Carregar propostas quando modal abrir
  useEffect(() => {
    if (isOpen && firebaseUser) {
      loadPropostas();
    }
  }, [isOpen, firebaseUser]);

  // Filtrar propostas por busca
  useEffect(() => {
    if (!searchTerm) {
      setFilteredPropostas(propostas);
    } else {
      const filtered = propostas.filter(proposta =>
        proposta.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proposta.descricao?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredPropostas(filtered);
    }
  }, [propostas, searchTerm]);

  const loadPropostas = async () => {
    if (!firebaseUser) return;

    try {
      setLoadingPropostas(true);
      
      // Para carregar todas as propostas do usuário, vamos usar a mesma API da página propostas
      const response = await fetch('/api/proposals', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      
      // Processar dados das propostas
      const proposalsFormatted = data.map((proposal: any) => ({
        id: proposal.id,
        nome: proposal.nome || `Proposta ${proposal.id.substring(0, 8)}`,
        descricao: proposal.descricao,
        brandId: proposal.brandId,
        status: proposal.status,
        priority: proposal.priority,
        totalAmount: proposal.totalAmount || 0,
        influencersCount: proposal.influencers?.length || 0,
        createdAt: proposal.createdAt || new Date(),
        updatedAt: proposal.updatedAt || new Date()
      }));

      setPropostas(proposalsFormatted);
      
    } catch (error) {
      console.error('Erro ao carregar propostas:', error);
      toast.error('Erro ao carregar propostas');
    } finally {
      setLoadingPropostas(false);
    }
  };

  const handleSendToProposal = async () => {
    if (!selectedProposta) {
      toast.error('Selecione uma proposta');
      return;
    }

    if (!firebaseUser) {
      toast.error('Usuário não autenticado');
      return;
    }

    try {
      setLoading(true);

      // Preparar dados dos influenciadores para envio
      const influencerIds = selectedInfluencers.map(inf => inf.id);
      
      const response = await fetch(`/api/proposals/${selectedProposta}/influencers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          influencerIds,
          addedBy: firebaseUser.uid
        }),
      });

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const result = await response.json();
      
      toast.success(`${selectedInfluencers.length} influenciador(es) adicionado(s) à proposta!`);
      onSuccess?.();
      handleClose();
      
    } catch (error) {
      console.error('Erro ao enviar para proposta:', error);
      toast.error('Erro ao adicionar influenciadores à proposta');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedProposta('');
    setSearchTerm('');
    onClose();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
              case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300';
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'accepted': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300';
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft': return 'Rascunho';
      case 'sent': return 'Enviada';
      case 'accepted': return 'Aceita';
      case 'rejected': return 'Rejeitada';
      default: return status;
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'low': return 'Baixa';
      case 'medium': return 'Média';
      case 'high': return 'Alta';
      default: return priority;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowRight className="h-5 w-5 text-[#ff0074]" />
            Enviar para Proposta
          </DialogTitle>
          <DialogDescription>
            Selecione uma proposta para adicionar {selectedInfluencers.length} influenciador(es)
          </DialogDescription>
        </DialogHeader>

        {/* Resumo dos influenciadores */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-[#ff0074]" />
                <span className="font-medium">Influenciadores Selecionados</span>
              </div>
              <Badge variant="secondary">
                {selectedInfluencers.length} selecionados
              </Badge>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {selectedInfluencers.slice(0, 5).map((influencer) => (
                <div key={influencer.id} className="flex items-center gap-2 bg-muted rounded-lg px-3 py-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={influencer.foto} alt={influencer.nome} />
                    <AvatarFallback className="text-xs bg-[#ff0074] text-white">
                      {influencer.nome.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium">{influencer.nome}</span>
                </div>
              ))}
              {selectedInfluencers.length > 5 && (
                <div className="flex items-center justify-center bg-muted rounded-lg px-3 py-2">
                  <span className="text-sm text-muted-foreground">
                    +{selectedInfluencers.length - 5} mais
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Campo de busca */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar propostas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Lista de propostas */}
        <div className="max-h-80 overflow-y-auto">
          {loadingPropostas ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-[#ff0074]" />
              <span className="ml-2 text-muted-foreground">Carregando propostas...</span>
            </div>
          ) : filteredPropostas.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="font-medium mb-2">Nenhuma proposta encontrada</h3>
              <p className="text-sm text-muted-foreground">
                {searchTerm ? 'Tente ajustar o termo de busca.' : 'Você ainda não possui propostas criadas.'}
              </p>
            </div>
          ) : (
            <RadioGroup value={selectedProposta} onValueChange={setSelectedProposta}>
              <div className="space-y-3">
                {filteredPropostas.map((proposta) => (
                  <div key={proposta.id}>
                    <label
                      htmlFor={proposta.id}
                      className={cn(
                        "flex items-start space-x-3 p-4 border rounded-lg cursor-pointer transition-colors",
                        selectedProposta === proposta.id
                          ? "border-[#ff0074] bg-[#ff0074]/5"
                          : "border-border hover:bg-muted/50"
                      )}
                    >
                      <RadioGroupItem value={proposta.id} id={proposta.id} className="mt-1" />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm truncate">{proposta.nome}</h4>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusBadgeColor(proposta.status)}>
                              {getStatusLabel(proposta.status)}
                            </Badge>
                            <Badge className={getPriorityBadgeColor(proposta.priority)}>
                              {getPriorityLabel(proposta.priority)}
                            </Badge>
                          </div>
                        </div>
                        
                        {proposta.descricao && (
                          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                            {proposta.descricao}
                          </p>
                        )}
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {proposta.influencersCount} influencer(es)
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            R$ {proposta.totalAmount.toLocaleString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(proposta.updatedAt).toLocaleDateString('pt-BR')}
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancelar
          </Button>
          <Button
            onClick={handleSendToProposal}
            disabled={loading || !selectedProposta}
            className="bg-[#ff0074] hover:bg-[#d10037] text-white"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <ArrowRight className="h-4 w-4 mr-2" />
            )}
            Adicionar à Proposta
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 


